<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-194" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="1354 -1429 1997 1200">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="67" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <polyline arcFlag="1" points="44,21 45,21 46,21 46,21 47,22 47,22 48,23 48,23 49,24 49,24 49,25 49,26 50,27 50,28 50,28 49,29 49,30 49,31 49,31 48,32 48,32 47,33 47,33 46,34 46,34 45,34 44,34 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,34 45,34 46,34 46,34 47,35 47,35 48,36 48,36 49,37 49,37 49,38 49,39 50,40 50,41 50,41 49,42 49,43 49,44 49,44 48,45 48,45 47,46 47,46 46,47 46,47 45,47 44,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,47 45,47 46,47 46,47 47,48 47,48 48,49 48,49 49,50 49,50 49,51 49,52 50,53 50,54 50,54 49,55 49,56 49,57 49,57 48,58 48,58 47,59 47,59 46,60 46,60 45,60 44,60 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="13" y2="13"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="32"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="21" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="21" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="29" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="88" y2="88"/>
    <polyline points="25,101 27,101 29,100 30,100 32,99 33,98 35,97 36,95 37,93 37,92 38,90 38,88 38,86 37,84 37,83 36,81 35,80 33,78 32,77 30,76 29,76 27,75 25,75 23,75 21,76 20,76 18,77 17,78 15,80 14,81 13,83 13,84 12,86 12,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="67" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="68" y2="68"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape113">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="9" y1="5" y2="36"/>
    <rect height="31" stroke-width="1" width="15" x="1" y="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.13333"/>
    <polyline points="58,100 64,100 " stroke-width="1.13333"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.13333"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape83_0">
    <circle cx="65" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="84,100 90,100 27,37 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="90,100 90,93 " stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="65" x2="7" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="74" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="27" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="21" y2="21"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="53" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="53" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="65" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="65" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="65" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="65" y1="74" y2="85"/>
   </symbol>
   <symbol id="transformer2:shape83_1">
    <circle cx="65" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline DF8003:Layer="PUBLIC" points="65,19 58,34 73,34 65,19 65,19 65,19 "/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="voltageTransformer:shape2">
    <circle cx="7" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape133">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="18" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="41" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="38" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="39" y1="46" y2="46"/>
    <circle cx="7" cy="37" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="25" r="7.5" stroke-width="1"/>
    <rect height="14" stroke-width="1" width="8" x="32" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="35" y2="44"/>
    <polyline points="42,23 30,32 30,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="21" y1="29" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="21" y1="35" y2="33"/>
    <circle cx="17" cy="31" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="35" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="35" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="37" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="36" y1="11" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23cb450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23cbc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23cc560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23cd7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ceaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23cf740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d02e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23d0ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_20df9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_20df9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d4090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d4090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d5910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d5910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_23d6920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23d85b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23d9200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23da0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23da9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23dc180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23dc980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23dd070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23dda90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23dec70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23df630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23e00f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23e0ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23e1f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23e2af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23e3b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23e4760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23f2f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23e6050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23e70b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23e8600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1210" width="2007" x="1349" y="-1434"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1929" x2="1929" y1="-340" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.322998" x1="1932" x2="1926" y1="-530" y2="-530"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.228882" x1="1930" x2="1928" y1="-533" y2="-533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1935" x2="1923" y1="-526" y2="-526"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1991" x2="1929" y1="-340" y2="-340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1929" x2="1929" y1="-507" y2="-526"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="1820" y="-1408"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-138931">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2335.000000 -971.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24981" ObjectName="SW-YR_ZH.YR_ZH_3011SW"/>
     <cge:Meas_Ref ObjectId="138931"/>
    <cge:TPSR_Ref TObjectID="24981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138859">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2233.000000 -1222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24973" ObjectName="SW-YR_ZH.YR_ZH_3926SW"/>
     <cge:Meas_Ref ObjectId="138859"/>
    <cge:TPSR_Ref TObjectID="24973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139225">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1980.000000 -599.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25002" ObjectName="SW-YR_ZH.YR_ZH_0971SW"/>
     <cge:Meas_Ref ObjectId="139225"/>
    <cge:TPSR_Ref TObjectID="25002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139226">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1981.000000 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25003" ObjectName="SW-YR_ZH.YR_ZH_0976SW"/>
     <cge:Meas_Ref ObjectId="139226"/>
    <cge:TPSR_Ref TObjectID="25003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138858">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2232.500000 -1047.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24972" ObjectName="SW-YR_ZH.YR_ZH_3921SW"/>
     <cge:Meas_Ref ObjectId="138858"/>
    <cge:TPSR_Ref TObjectID="24972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138940">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2333.000000 -669.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24984" ObjectName="SW-YR_ZH.YR_ZH_0011SW"/>
     <cge:Meas_Ref ObjectId="138940"/>
    <cge:TPSR_Ref TObjectID="24984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138860">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2291.000000 -1088.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24974" ObjectName="SW-YR_ZH.YR_ZH_39217SW"/>
     <cge:Meas_Ref ObjectId="138860"/>
    <cge:TPSR_Ref TObjectID="24974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2291.000000 -1171.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24975" ObjectName="SW-YR_ZH.YR_ZH_39260SW"/>
     <cge:Meas_Ref ObjectId="138861"/>
    <cge:TPSR_Ref TObjectID="24975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2291.000000 -1263.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24976" ObjectName="SW-YR_ZH.YR_ZH_39267SW"/>
     <cge:Meas_Ref ObjectId="138862"/>
    <cge:TPSR_Ref TObjectID="24976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138805">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2872.000000 -1221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24967" ObjectName="SW-YR_ZH.YR_ZH_3916SW"/>
     <cge:Meas_Ref ObjectId="138805"/>
    <cge:TPSR_Ref TObjectID="24967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138804">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2871.500000 -1046.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24966" ObjectName="SW-YR_ZH.YR_ZH_3911SW"/>
     <cge:Meas_Ref ObjectId="138804"/>
    <cge:TPSR_Ref TObjectID="24966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138806">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2930.000000 -1087.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24968" ObjectName="SW-YR_ZH.YR_ZH_39117SW"/>
     <cge:Meas_Ref ObjectId="138806"/>
    <cge:TPSR_Ref TObjectID="24968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138807">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2930.000000 -1170.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24969" ObjectName="SW-YR_ZH.YR_ZH_39160SW"/>
     <cge:Meas_Ref ObjectId="138807"/>
    <cge:TPSR_Ref TObjectID="24969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138808">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2930.000000 -1262.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24970" ObjectName="SW-YR_ZH.YR_ZH_39167SW"/>
     <cge:Meas_Ref ObjectId="138808"/>
    <cge:TPSR_Ref TObjectID="24970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138910">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2535.000000 -1077.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24977" ObjectName="SW-YR_ZH.YR_ZH_3901SW"/>
     <cge:Meas_Ref ObjectId="138910"/>
    <cge:TPSR_Ref TObjectID="24977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138911">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2594.000000 -1028.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24978" ObjectName="SW-YR_ZH.YR_ZH_39010SW"/>
     <cge:Meas_Ref ObjectId="138911"/>
    <cge:TPSR_Ref TObjectID="24978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138912">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2594.000000 -1120.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24979" ObjectName="SW-YR_ZH.YR_ZH_39017SW"/>
     <cge:Meas_Ref ObjectId="138912"/>
    <cge:TPSR_Ref TObjectID="24979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138989">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3095.500000 -693.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24985" ObjectName="SW-YR_ZH.YR_ZH_0901SW"/>
     <cge:Meas_Ref ObjectId="138989"/>
    <cge:TPSR_Ref TObjectID="24985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2442.000000 -584.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24996" ObjectName="SW-YR_ZH.YR_ZH_0941SW"/>
     <cge:Meas_Ref ObjectId="139133"/>
    <cge:TPSR_Ref TObjectID="24996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139087">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2673.000000 -583.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24993" ObjectName="SW-YR_ZH.YR_ZH_0931SW"/>
     <cge:Meas_Ref ObjectId="139087"/>
    <cge:TPSR_Ref TObjectID="24993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139041">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2908.000000 -584.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24990" ObjectName="SW-YR_ZH.YR_ZH_0921SW"/>
     <cge:Meas_Ref ObjectId="139041"/>
    <cge:TPSR_Ref TObjectID="24990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139042">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2908.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24991" ObjectName="SW-YR_ZH.YR_ZH_0926SW"/>
     <cge:Meas_Ref ObjectId="139042"/>
    <cge:TPSR_Ref TObjectID="24991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138995">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3135.000000 -582.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24987" ObjectName="SW-YR_ZH.YR_ZH_0911SW"/>
     <cge:Meas_Ref ObjectId="138995"/>
    <cge:TPSR_Ref TObjectID="24987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3135.000000 -459.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24988" ObjectName="SW-YR_ZH.YR_ZH_0916SW"/>
     <cge:Meas_Ref ObjectId="138996"/>
    <cge:TPSR_Ref TObjectID="24988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2672.000000 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24994" ObjectName="SW-YR_ZH.YR_ZH_0936SW"/>
     <cge:Meas_Ref ObjectId="139088"/>
    <cge:TPSR_Ref TObjectID="24994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139136">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2442.000000 -475.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24997" ObjectName="SW-YR_ZH.YR_ZH_0946SW"/>
     <cge:Meas_Ref ObjectId="139136"/>
    <cge:TPSR_Ref TObjectID="24997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139179">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2211.000000 -582.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24999" ObjectName="SW-YR_ZH.YR_ZH_0951SW"/>
     <cge:Meas_Ref ObjectId="139179"/>
    <cge:TPSR_Ref TObjectID="24999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139180">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2211.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25000" ObjectName="SW-YR_ZH.YR_ZH_0956SW"/>
     <cge:Meas_Ref ObjectId="139180"/>
    <cge:TPSR_Ref TObjectID="25000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138932">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2300.000000 -898.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24982" ObjectName="SW-YR_ZH.YR_ZH_30117SW"/>
     <cge:Meas_Ref ObjectId="138932"/>
    <cge:TPSR_Ref TObjectID="24982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139232">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1920.000000 -466.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25004" ObjectName="SW-YR_ZH.YR_ZH_09767SW"/>
     <cge:Meas_Ref ObjectId="139232"/>
    <cge:TPSR_Ref TObjectID="25004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-241656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2845.000000 -973.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40673" ObjectName="SW-YR_ZH.YR_ZH_3021SW"/>
     <cge:Meas_Ref ObjectId="241656"/>
    <cge:TPSR_Ref TObjectID="40673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-241659">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2843.000000 -680.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40676" ObjectName="SW-YR_ZH.YR_ZH_0021SW"/>
     <cge:Meas_Ref ObjectId="241659"/>
    <cge:TPSR_Ref TObjectID="40676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-241657">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2809.000000 -905.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40674" ObjectName="SW-YR_ZH.YR_ZH_30217SW"/>
     <cge:Meas_Ref ObjectId="241657"/>
    <cge:TPSR_Ref TObjectID="40674"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YR_ZH.YR_ZH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1833,-660 3326,-660 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24963" ObjectName="BS-YR_ZH.YR_ZH_9IM"/>
    <cge:TPSR_Ref TObjectID="24963"/></metadata>
   <polyline fill="none" opacity="0" points="1833,-660 3326,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_ZH.YR_ZH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1970,-1028 3134,-1028 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24962" ObjectName="BS-YR_ZH.YR_ZH_3IM"/>
    <cge:TPSR_Ref TObjectID="24962"/></metadata>
   <polyline fill="none" opacity="0" points="1970,-1028 3134,-1028 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YR_ZH.YR_ZH_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1965.000000 -340.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41828" ObjectName="CB-YR_ZH.YR_ZH_Cb1"/>
    <cge:TPSR_Ref TObjectID="41828"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YR_ZH.YR_ZH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35235"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.939394 -0.000000 0.000000 -0.882353 2305.000000 -803.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.939394 -0.000000 0.000000 -0.882353 2305.000000 -803.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25005" ObjectName="TF-YR_ZH.YR_ZH_1T"/>
    <cge:TPSR_Ref TObjectID="25005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2633.000000 -841.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2633.000000 -841.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YR_ZH.YR_ZH_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="14521"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2787.000000 -801.000000)" xlink:href="#transformer2:shape83_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2787.000000 -801.000000)" xlink:href="#transformer2:shape83_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="40671" ObjectName="TF-YR_ZH.YR_ZH_2T"/>
    <cge:TPSR_Ref TObjectID="40671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 -723.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 -723.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1bcbc60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2174.000000 -1241.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbd8d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2813.000000 -1240.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b37110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2168.000000 -379.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa4740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3172.000000 -390.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa6560">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2945.000000 -394.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa7e90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2709.000000 -391.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa9dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2479.000000 -410.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aabab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2248.000000 -389.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1afb380">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2507.000000 -1166.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1afc130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2535.000000 -1163.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1affbb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2639.000000 -965.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b04b90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1961.000000 -675.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b09060">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3095.000000 -798.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b09b40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3132.000000 -783.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1453.000000 -1339.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255532" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1495.000000 -1217.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255532" ObjectName="YR_ZH:YR_ZH_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255532" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1495.000000 -1176.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255532" ObjectName="YR_ZH:YR_ZH_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255532" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1493.000000 -1261.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255532" ObjectName="YR_ZH:YR_ZH_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255533" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1497.000000 -1136.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255533" ObjectName="YR_ZH:YR_ZH_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2416.000000 -1182.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24971"/>
     <cge:Term_Ref ObjectID="35165"/>
    <cge:TPSR_Ref TObjectID="24971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2416.000000 -1182.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24971"/>
     <cge:Term_Ref ObjectID="35165"/>
    <cge:TPSR_Ref TObjectID="24971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138698" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2416.000000 -1182.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138698" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24971"/>
     <cge:Term_Ref ObjectID="35165"/>
    <cge:TPSR_Ref TObjectID="24971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138694" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3057.000000 -1181.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138694" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24965"/>
     <cge:Term_Ref ObjectID="35117"/>
    <cge:TPSR_Ref TObjectID="24965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138695" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3057.000000 -1181.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24965"/>
     <cge:Term_Ref ObjectID="35117"/>
    <cge:TPSR_Ref TObjectID="24965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138691" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3057.000000 -1181.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24965"/>
     <cge:Term_Ref ObjectID="35117"/>
    <cge:TPSR_Ref TObjectID="24965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2498.000000 -952.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24980"/>
     <cge:Term_Ref ObjectID="35183"/>
    <cge:TPSR_Ref TObjectID="24980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2498.000000 -952.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24980"/>
     <cge:Term_Ref ObjectID="35183"/>
    <cge:TPSR_Ref TObjectID="24980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138705" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2498.000000 -952.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24980"/>
     <cge:Term_Ref ObjectID="35183"/>
    <cge:TPSR_Ref TObjectID="24980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138714" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2498.000000 -784.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24983"/>
     <cge:Term_Ref ObjectID="35189"/>
    <cge:TPSR_Ref TObjectID="24983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138715" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2498.000000 -784.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138715" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24983"/>
     <cge:Term_Ref ObjectID="35189"/>
    <cge:TPSR_Ref TObjectID="24983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2498.000000 -784.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24983"/>
     <cge:Term_Ref ObjectID="35189"/>
    <cge:TPSR_Ref TObjectID="24983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -305.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25001"/>
     <cge:Term_Ref ObjectID="35225"/>
    <cge:TPSR_Ref TObjectID="25001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -305.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25001"/>
     <cge:Term_Ref ObjectID="35225"/>
    <cge:TPSR_Ref TObjectID="25001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -305.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25001"/>
     <cge:Term_Ref ObjectID="35225"/>
    <cge:TPSR_Ref TObjectID="25001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138762" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2238.000000 -307.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24998"/>
     <cge:Term_Ref ObjectID="35219"/>
    <cge:TPSR_Ref TObjectID="24998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2238.000000 -307.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24998"/>
     <cge:Term_Ref ObjectID="35219"/>
    <cge:TPSR_Ref TObjectID="24998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138759" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2238.000000 -307.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138759" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24998"/>
     <cge:Term_Ref ObjectID="35219"/>
    <cge:TPSR_Ref TObjectID="24998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138756" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2469.000000 -307.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24995"/>
     <cge:Term_Ref ObjectID="35213"/>
    <cge:TPSR_Ref TObjectID="24995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2469.000000 -307.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24995"/>
     <cge:Term_Ref ObjectID="35213"/>
    <cge:TPSR_Ref TObjectID="24995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2469.000000 -307.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24995"/>
     <cge:Term_Ref ObjectID="35213"/>
    <cge:TPSR_Ref TObjectID="24995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2699.000000 -305.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24992"/>
     <cge:Term_Ref ObjectID="35207"/>
    <cge:TPSR_Ref TObjectID="24992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138751" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2699.000000 -305.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138751" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24992"/>
     <cge:Term_Ref ObjectID="35207"/>
    <cge:TPSR_Ref TObjectID="24992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138747" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2699.000000 -305.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138747" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24992"/>
     <cge:Term_Ref ObjectID="35207"/>
    <cge:TPSR_Ref TObjectID="24992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -305.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24989"/>
     <cge:Term_Ref ObjectID="35201"/>
    <cge:TPSR_Ref TObjectID="24989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -305.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24989"/>
     <cge:Term_Ref ObjectID="35201"/>
    <cge:TPSR_Ref TObjectID="24989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -305.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24989"/>
     <cge:Term_Ref ObjectID="35201"/>
    <cge:TPSR_Ref TObjectID="24989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-138738" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3164.000000 -305.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138738" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24986"/>
     <cge:Term_Ref ObjectID="35195"/>
    <cge:TPSR_Ref TObjectID="24986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-138739" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3164.000000 -305.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24986"/>
     <cge:Term_Ref ObjectID="35195"/>
    <cge:TPSR_Ref TObjectID="24986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-138735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3164.000000 -305.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24986"/>
     <cge:Term_Ref ObjectID="35195"/>
    <cge:TPSR_Ref TObjectID="24986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-138727" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -741.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24963"/>
     <cge:Term_Ref ObjectID="35114"/>
    <cge:TPSR_Ref TObjectID="24963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-138728" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -741.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138728" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24963"/>
     <cge:Term_Ref ObjectID="35114"/>
    <cge:TPSR_Ref TObjectID="24963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-138729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -741.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24963"/>
     <cge:Term_Ref ObjectID="35114"/>
    <cge:TPSR_Ref TObjectID="24963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-138730" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -741.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138730" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24963"/>
     <cge:Term_Ref ObjectID="35114"/>
    <cge:TPSR_Ref TObjectID="24963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-138734" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -741.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24963"/>
     <cge:Term_Ref ObjectID="35114"/>
    <cge:TPSR_Ref TObjectID="24963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-138719" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -1129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24962"/>
     <cge:Term_Ref ObjectID="35113"/>
    <cge:TPSR_Ref TObjectID="24962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-138720" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -1129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138720" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24962"/>
     <cge:Term_Ref ObjectID="35113"/>
    <cge:TPSR_Ref TObjectID="24962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-138721" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -1129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24962"/>
     <cge:Term_Ref ObjectID="35113"/>
    <cge:TPSR_Ref TObjectID="24962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-138722" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -1129.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24962"/>
     <cge:Term_Ref ObjectID="35113"/>
    <cge:TPSR_Ref TObjectID="24962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-138726" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -1129.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24962"/>
     <cge:Term_Ref ObjectID="35113"/>
    <cge:TPSR_Ref TObjectID="24962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-138718" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2499.000000 -863.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25005"/>
     <cge:Term_Ref ObjectID="35236"/>
    <cge:TPSR_Ref TObjectID="25005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-138717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2499.000000 -863.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="138717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25005"/>
     <cge:Term_Ref ObjectID="35236"/>
    <cge:TPSR_Ref TObjectID="25005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-241738" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3014.000000 -974.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241738" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40672"/>
     <cge:Term_Ref ObjectID="15080"/>
    <cge:TPSR_Ref TObjectID="40672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-241739" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3014.000000 -974.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40672"/>
     <cge:Term_Ref ObjectID="15080"/>
    <cge:TPSR_Ref TObjectID="40672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-241734" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3014.000000 -974.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40672"/>
     <cge:Term_Ref ObjectID="15080"/>
    <cge:TPSR_Ref TObjectID="40672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-241745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2999.000000 -759.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40675"/>
     <cge:Term_Ref ObjectID="61503"/>
    <cge:TPSR_Ref TObjectID="40675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-241746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2999.000000 -759.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40675"/>
     <cge:Term_Ref ObjectID="61503"/>
    <cge:TPSR_Ref TObjectID="40675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-241741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2999.000000 -759.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40675"/>
     <cge:Term_Ref ObjectID="61503"/>
    <cge:TPSR_Ref TObjectID="40675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-241749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3029.000000 -877.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40671"/>
     <cge:Term_Ref ObjectID="14519"/>
    <cge:TPSR_Ref TObjectID="40671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-241748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3029.000000 -877.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="241748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40671"/>
     <cge:Term_Ref ObjectID="14519"/>
    <cge:TPSR_Ref TObjectID="40671"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="1486" y="-1412"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="1486" y="-1412"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1437" y="-1429"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1437" y="-1429"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2251" y="-1162"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2251" y="-1162"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2890" y="-1161"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2890" y="-1161"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1998" y="-577"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1998" y="-577"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2229" y="-556"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2229" y="-556"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2460" y="-558"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2460" y="-558"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2690" y="-559"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2690" y="-559"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2926" y="-560"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2926" y="-560"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3153" y="-558"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3153" y="-558"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="16" qtmmishow="hidden" width="59" x="1389" y="-1045"/>
    </a>
   <metadata/><rect fill="white" height="16" opacity="0" stroke="white" transform="" width="59" x="1389" y="-1045"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1684" y="-1420"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1684" y="-1420"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1684" y="-1386"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1684" y="-1386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2239" y="-879"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2239" y="-879"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="2892" y="-865"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="2892" y="-865"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="1820" y="-1409"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="1820" y="-1409"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="1486" y="-1412"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1437" y="-1429"/></g>
   <g href="35kV中和变35kV中他线392间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2251" y="-1162"/></g>
   <g href="35kV中和变35kV万中线391间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2890" y="-1161"/></g>
   <g href="35kV中和变10kV1号电容器组097间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1998" y="-577"/></g>
   <g href="35kV中和变10kV中厂线095间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2229" y="-556"/></g>
   <g href="35kV中和变10kV直苴线094间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2460" y="-558"/></g>
   <g href="35kV中和变10kV小直么线093间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2690" y="-559"/></g>
   <g href="35kV中和变10kV乡镇府线092间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2926" y="-560"/></g>
   <g href="35kV中和变10kV直苴Ⅱ回线091间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3153" y="-558"/></g>
   <g href="35kV中和变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="16" qtmmishow="hidden" width="59" x="1389" y="-1045"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1684" y="-1420"/></g>
   <g href="cx_配调_配网接线图35_永仁.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1684" y="-1386"/></g>
   <g href="35kV中和变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2239" y="-879"/></g>
   <g href="35kV中和变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="2892" y="-865"/></g>
   <g href="AVC中和站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="1820" y="-1409"/></g>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2332,-757 2341,-769 2350,-757 2332,-757 " stroke="rgb(0,255,0)" stroke-width="2"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,-796 2341,-784 2332,-796 2350,-796 " stroke="rgb(0,255,0)" stroke-width="2"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2442,-417 2451,-429 2460,-417 2442,-417 " stroke="rgb(0,255,0)" stroke-width="2"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2460,-456 2451,-444 2442,-456 2460,-456 " stroke="rgb(0,255,0)" stroke-width="2"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1999,-550 1990,-538 1981,-550 1999,-550 " stroke="rgb(0,255,0)" stroke-width="2"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1981,-515 1990,-527 1999,-515 1981,-515 " stroke="rgb(0,255,0)" stroke-width="2"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c47b70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2175.000000 -1208.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc5460">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2814.000000 -1207.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b36c00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2169.000000 -351.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af8f30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2536.000000 -1205.000000)" xlink:href="#voltageTransformer:shape133"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b06730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3084.000000 -843.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YR_ZH.092Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2908.000000 -383.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37992" ObjectName="EC-YR_ZH.092Ld"/>
    <cge:TPSR_Ref TObjectID="37992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_ZH.091Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3135.000000 -381.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37993" ObjectName="EC-YR_ZH.091Ld"/>
    <cge:TPSR_Ref TObjectID="37993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_ZH.093Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2672.000000 -382.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37991" ObjectName="EC-YR_ZH.093Ld"/>
    <cge:TPSR_Ref TObjectID="37991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_ZH.094Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2442.000000 -381.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37990" ObjectName="EC-YR_ZH.094Ld"/>
    <cge:TPSR_Ref TObjectID="37990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_ZH.095Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2211.000000 -379.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37979" ObjectName="EC-YR_ZH.095Ld"/>
    <cge:TPSR_Ref TObjectID="37979"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1b6f220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2342,-911 2342,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24980@0" ObjectIDZND0="25005@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138924_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2342,-911 2342,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c01840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2341,-719 2341,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24983@0" ObjectIDZND0="24984@1" Pin0InfoVect0LinkObjId="SW-138940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138937_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2341,-719 2341,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c01a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1990,-559 1990,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25001@0" ObjectIDZND0="25003@1" Pin0InfoVect0LinkObjId="SW-139226_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139222_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1990,-559 1990,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c01ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2241,-1052 2241,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24972@0" ObjectIDZND0="24962@0" Pin0InfoVect0LinkObjId="g_1bc5240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138858_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2241,-1052 2241,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c11ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2336,-1114 2318,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c120d0@0" ObjectIDZND0="24974@1" Pin0InfoVect0LinkObjId="SW-138860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c120d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2336,-1114 2318,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c365b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2282,-1114 2241,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24974@0" ObjectIDZND0="24972@x" ObjectIDZND1="24971@x" Pin0InfoVect0LinkObjId="SW-138858_0" Pin0InfoVect1LinkObjId="SW-138856_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2282,-1114 2241,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb1b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2241,-1088 2241,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24972@1" ObjectIDZND0="24974@x" ObjectIDZND1="24971@x" Pin0InfoVect0LinkObjId="SW-138860_0" Pin0InfoVect1LinkObjId="SW-138856_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138858_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2241,-1088 2241,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bb1d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2241,-1114 2241,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24974@x" ObjectIDND1="24972@x" ObjectIDZND0="24971@0" Pin0InfoVect0LinkObjId="SW-138856_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138860_0" Pin1InfoVect1LinkObjId="SW-138858_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2241,-1114 2241,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c44c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2336,-1197 2318,-1197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c44e00@0" ObjectIDZND0="24975@1" Pin0InfoVect0LinkObjId="SW-138861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c44e00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2336,-1197 2318,-1197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c455b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2282,-1197 2241,-1197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24975@0" ObjectIDZND0="24971@x" ObjectIDZND1="24973@x" Pin0InfoVect0LinkObjId="SW-138856_0" Pin0InfoVect1LinkObjId="SW-138859_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2282,-1197 2241,-1197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c45e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2241,-1168 2241,-1197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24971@1" ObjectIDZND0="24975@x" ObjectIDZND1="24973@x" Pin0InfoVect0LinkObjId="SW-138861_0" Pin0InfoVect1LinkObjId="SW-138859_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2241,-1168 2241,-1197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c46080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2241,-1197 2241,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="24975@x" ObjectIDND1="24971@x" ObjectIDZND0="24973@0" Pin0InfoVect0LinkObjId="SW-138859_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138861_0" Pin1InfoVect1LinkObjId="SW-138856_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2241,-1197 2241,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc9f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2336,-1289 2318,-1289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bca120@0" ObjectIDZND0="24976@1" Pin0InfoVect0LinkObjId="SW-138862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bca120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2336,-1289 2318,-1289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcb190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2282,-1289 2241,-1289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24976@0" ObjectIDZND0="24973@x" ObjectIDZND1="g_1bcbc60@0" ObjectIDZND2="33969@1" Pin0InfoVect0LinkObjId="SW-138859_0" Pin0InfoVect1LinkObjId="g_1bcbc60_0" Pin0InfoVect2LinkObjId="g_1b628c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2282,-1289 2241,-1289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2241,-1263 2241,-1289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24973@1" ObjectIDZND0="24976@x" ObjectIDZND1="g_1bcbc60@0" ObjectIDZND2="33969@1" Pin0InfoVect0LinkObjId="SW-138862_0" Pin0InfoVect1LinkObjId="g_1bcbc60_0" Pin0InfoVect2LinkObjId="g_1b628c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138859_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2241,-1263 2241,-1289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcc2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2182,-1228 2182,-1245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1c47b70@0" ObjectIDZND0="g_1bcbc60@0" Pin0InfoVect0LinkObjId="g_1bcbc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c47b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2182,-1228 2182,-1245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcc4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2182,-1276 2182,-1315 2241,-1315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_1bcbc60@1" ObjectIDZND0="24976@x" ObjectIDZND1="24973@x" ObjectIDZND2="33969@1" Pin0InfoVect0LinkObjId="SW-138862_0" Pin0InfoVect1LinkObjId="SW-138859_0" Pin0InfoVect2LinkObjId="g_1b628c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bcbc60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2182,-1276 2182,-1315 2241,-1315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b626d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2241,-1289 2241,-1316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="24976@x" ObjectIDND1="24973@x" ObjectIDZND0="g_1bcbc60@0" ObjectIDZND1="33969@1" Pin0InfoVect0LinkObjId="g_1bcbc60_0" Pin0InfoVect1LinkObjId="g_1b628c0_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138862_0" Pin1InfoVect1LinkObjId="SW-138859_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2241,-1289 2241,-1316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b628c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2241,-1315 2241,-1345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1bcbc60@0" ObjectIDND1="24976@x" ObjectIDND2="24973@x" ObjectIDZND0="33969@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bcbc60_0" Pin1InfoVect1LinkObjId="SW-138862_0" Pin1InfoVect2LinkObjId="SW-138859_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2241,-1315 2241,-1345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc5240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2880,-1051 2880,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24966@0" ObjectIDZND0="24962@0" Pin0InfoVect0LinkObjId="g_1c01ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2880,-1051 2880,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd3c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2975,-1113 2957,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bd3e80@0" ObjectIDZND0="24968@1" Pin0InfoVect0LinkObjId="SW-138806_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd3e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2975,-1113 2957,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be4ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2921,-1113 2880,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24968@0" ObjectIDZND0="24966@x" ObjectIDZND1="24965@x" Pin0InfoVect0LinkObjId="SW-138804_0" Pin0InfoVect1LinkObjId="SW-138802_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2921,-1113 2880,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2880,-1087 2880,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24966@1" ObjectIDZND0="24968@x" ObjectIDZND1="24965@x" Pin0InfoVect0LinkObjId="SW-138806_0" Pin0InfoVect1LinkObjId="SW-138802_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2880,-1087 2880,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be50e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2880,-1113 2880,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24966@x" ObjectIDND1="24968@x" ObjectIDZND0="24965@0" Pin0InfoVect0LinkObjId="SW-138802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138804_0" Pin1InfoVect1LinkObjId="SW-138806_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2880,-1113 2880,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be1ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2975,-1196 2957,-1196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1be1d00@0" ObjectIDZND0="24969@1" Pin0InfoVect0LinkObjId="SW-138807_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1be1d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2975,-1196 2957,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be2630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2921,-1196 2880,-1196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24969@0" ObjectIDZND0="24965@x" ObjectIDZND1="24967@x" Pin0InfoVect0LinkObjId="SW-138802_0" Pin0InfoVect1LinkObjId="SW-138805_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2921,-1196 2880,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be2850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2880,-1167 2880,-1196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24965@1" ObjectIDZND0="24969@x" ObjectIDZND1="24967@x" Pin0InfoVect0LinkObjId="SW-138807_0" Pin0InfoVect1LinkObjId="SW-138805_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2880,-1167 2880,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be2a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2880,-1196 2880,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24965@x" ObjectIDND1="24969@x" ObjectIDZND0="24967@0" Pin0InfoVect0LinkObjId="SW-138805_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138802_0" Pin1InfoVect1LinkObjId="SW-138807_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2880,-1196 2880,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbc940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2975,-1288 2957,-1288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bbcb60@0" ObjectIDZND0="24970@1" Pin0InfoVect0LinkObjId="SW-138808_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bbcb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2975,-1288 2957,-1288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbd490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2921,-1288 2880,-1288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24970@0" ObjectIDZND0="24967@x" ObjectIDZND1="g_1bbd8d0@0" ObjectIDZND2="37447@1" Pin0InfoVect0LinkObjId="SW-138805_0" Pin0InfoVect1LinkObjId="g_1bbd8d0_0" Pin0InfoVect2LinkObjId="g_1bc10d0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138808_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2921,-1288 2880,-1288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbd6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2880,-1262 2880,-1288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24967@1" ObjectIDZND0="24970@x" ObjectIDZND1="g_1bbd8d0@0" ObjectIDZND2="37447@1" Pin0InfoVect0LinkObjId="SW-138808_0" Pin0InfoVect1LinkObjId="g_1bbd8d0_0" Pin0InfoVect2LinkObjId="g_1bc10d0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138805_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2880,-1262 2880,-1288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc0a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2821,-1227 2821,-1244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1bc5460@0" ObjectIDZND0="g_1bbd8d0@0" Pin0InfoVect0LinkObjId="g_1bbd8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc5460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2821,-1227 2821,-1244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc0c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2821,-1275 2821,-1314 2880,-1314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_1bbd8d0@1" ObjectIDZND0="24967@x" ObjectIDZND1="24970@x" ObjectIDZND2="37447@1" Pin0InfoVect0LinkObjId="SW-138805_0" Pin0InfoVect1LinkObjId="SW-138808_0" Pin0InfoVect2LinkObjId="g_1bc10d0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bbd8d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2821,-1275 2821,-1314 2880,-1314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc0eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2880,-1288 2880,-1315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="24967@x" ObjectIDND1="24970@x" ObjectIDZND0="g_1bbd8d0@0" ObjectIDZND1="37447@1" Pin0InfoVect0LinkObjId="g_1bbd8d0_0" Pin0InfoVect1LinkObjId="g_1bc10d0_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138805_0" Pin1InfoVect1LinkObjId="SW-138808_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2880,-1288 2880,-1315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc10d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2880,-1314 2880,-1346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="24967@x" ObjectIDND1="24970@x" ObjectIDND2="g_1bbd8d0@0" ObjectIDZND0="37447@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138805_0" Pin1InfoVect1LinkObjId="SW-138808_0" Pin1InfoVect2LinkObjId="g_1bbd8d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2880,-1314 2880,-1346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a9f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2639,-1054 2621,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a9f600@0" ObjectIDZND0="24978@1" Pin0InfoVect0LinkObjId="SW-138911_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a9f600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2639,-1054 2621,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bacc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2585,-1054 2544,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="24978@0" ObjectIDZND0="24977@x" ObjectIDZND1="24962@0" Pin0InfoVect0LinkObjId="SW-138910_0" Pin0InfoVect1LinkObjId="g_1c01ed0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138911_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2585,-1054 2544,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bad680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-1082 2544,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="24977@0" ObjectIDZND0="24978@x" ObjectIDZND1="24962@0" Pin0InfoVect0LinkObjId="SW-138911_0" Pin0InfoVect1LinkObjId="g_1c01ed0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-1082 2544,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bad8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-1054 2544,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="24978@x" ObjectIDND1="24977@x" ObjectIDZND0="24962@0" Pin0InfoVect0LinkObjId="g_1c01ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138911_0" Pin1InfoVect1LinkObjId="SW-138910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-1054 2544,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a92ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2639,-1146 2621,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a92ce0@0" ObjectIDZND0="24979@1" Pin0InfoVect0LinkObjId="SW-138912_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a92ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2639,-1146 2621,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a93610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2585,-1146 2544,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24979@0" ObjectIDZND0="24977@x" ObjectIDZND1="g_1afb380@0" ObjectIDZND2="g_1afc130@0" Pin0InfoVect0LinkObjId="SW-138910_0" Pin0InfoVect1LinkObjId="g_1afb380_0" Pin0InfoVect2LinkObjId="g_1afc130_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138912_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2585,-1146 2544,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a94010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-1146 2544,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24979@x" ObjectIDND1="g_1afb380@0" ObjectIDND2="g_1afc130@0" ObjectIDZND0="24977@1" Pin0InfoVect0LinkObjId="SW-138910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138912_0" Pin1InfoVect1LinkObjId="g_1afb380_0" Pin1InfoVect2LinkObjId="g_1afc130_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-1146 2544,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a94230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2341,-807 2341,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25005@0" ObjectIDZND0="24983@1" Pin0InfoVect0LinkObjId="SW-138937_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b6f220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2341,-807 2341,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a94dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2343,-1012 2343,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24981@1" ObjectIDZND0="24962@0" Pin0InfoVect0LinkObjId="g_1c01ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138931_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-1012 2343,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a94fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2341,-674 2341,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24984@0" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1b4bc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2341,-674 2341,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4bc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3104,-698 3104,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24985@0" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1a94fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3104,-698 3104,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4c390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1989,-604 1989,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25002@0" ObjectIDZND0="25001@1" Pin0InfoVect0LinkObjId="SW-139222_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139225_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1989,-604 1989,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4c5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1989,-640 1989,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25002@1" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1a94fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139225_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1989,-640 1989,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b91f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2917,-539 2917,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24989@0" ObjectIDZND0="24991@1" Pin0InfoVect0LinkObjId="SW-139042_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139038_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2917,-539 2917,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b92800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2917,-587 2917,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24990@0" ObjectIDZND0="24989@1" Pin0InfoVect0LinkObjId="SW-139038_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139041_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2917,-587 2917,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b92a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2917,-625 2917,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24990@1" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1a94fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139041_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2917,-625 2917,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b73360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3144,-539 3144,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24986@0" ObjectIDZND0="24988@1" Pin0InfoVect0LinkObjId="SW-138996_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138992_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3144,-539 3144,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b73c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3144,-585 3144,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24987@0" ObjectIDZND0="24986@1" Pin0InfoVect0LinkObjId="SW-138992_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3144,-585 3144,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b73ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3144,-623 3144,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24987@1" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1a94fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138995_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3144,-623 3144,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2681,-538 2681,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24992@0" ObjectIDZND0="24994@1" Pin0InfoVect0LinkObjId="SW-139088_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139084_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2681,-538 2681,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8d890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2681,-565 2681,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24992@1" ObjectIDZND0="24993@0" Pin0InfoVect0LinkObjId="SW-139087_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139084_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2681,-565 2681,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4e940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2451,-537 2451,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24995@0" ObjectIDZND0="24997@1" Pin0InfoVect0LinkObjId="SW-139136_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2451,-537 2451,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4f240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2451,-564 2451,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24995@1" ObjectIDZND0="24996@0" Pin0InfoVect0LinkObjId="SW-139133_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2451,-564 2451,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4f4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2451,-625 2451,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24996@1" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1a94fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139133_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2451,-625 2451,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4fc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2682,-624 2682,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24993@1" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1a94fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139087_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2682,-624 2682,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b503a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1990,-461 1953,-461 1953,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="25003@x" ObjectIDND1="41828@x" ObjectIDZND0="25004@0" Pin0InfoVect0LinkObjId="SW-139232_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-139226_0" Pin1InfoVect1LinkObjId="CB-YR_ZH.YR_ZH_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1990,-461 1953,-461 1953,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9b1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1990,-471 1990,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="25003@0" ObjectIDZND0="25004@x" ObjectIDZND1="41828@x" Pin0InfoVect0LinkObjId="SW-139232_0" Pin0InfoVect1LinkObjId="CB-YR_ZH.YR_ZH_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139226_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1990,-471 1990,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9b410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1990,-461 1990,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="25004@x" ObjectIDND1="25003@x" ObjectIDZND0="41828@0" Pin0InfoVect0LinkObjId="CB-YR_ZH.YR_ZH_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-139232_0" Pin1InfoVect1LinkObjId="SW-139226_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1990,-461 1990,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a4bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-535 2220,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24998@0" ObjectIDZND0="25000@1" Pin0InfoVect0LinkObjId="SW-139180_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-535 2220,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a4c870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-562 2220,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24998@1" ObjectIDZND0="24999@0" Pin0InfoVect0LinkObjId="SW-139179_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139176_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-562 2220,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a4cad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-623 2220,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24999@1" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1a94fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139179_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-623 2220,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b37950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2176,-371 2176,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1b36c00@0" ObjectIDZND0="g_1b37110@0" Pin0InfoVect0LinkObjId="g_1b37110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b36c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2176,-371 2176,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b380a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2176,-414 2176,-429 2220,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1b37110@1" ObjectIDZND0="37979@x" ObjectIDZND1="g_1aabab0@0" ObjectIDZND2="25000@x" Pin0InfoVect0LinkObjId="EC-YR_ZH.095Ld_0" Pin0InfoVect1LinkObjId="g_1aabab0_0" Pin0InfoVect2LinkObjId="SW-139180_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b37110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2176,-414 2176,-429 2220,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b38a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-429 2220,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1b37110@0" ObjectIDND1="g_1aabab0@0" ObjectIDND2="25000@x" ObjectIDZND0="37979@0" Pin0InfoVect0LinkObjId="EC-YR_ZH.095Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b37110_0" Pin1InfoVect1LinkObjId="g_1aabab0_0" Pin1InfoVect2LinkObjId="SW-139180_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-429 2220,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be9800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2309,-949 2309,-962 2343,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24982@0" ObjectIDZND0="24981@x" ObjectIDZND1="24980@x" Pin0InfoVect0LinkObjId="SW-138931_0" Pin0InfoVect1LinkObjId="SW-138924_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2309,-949 2309,-962 2343,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bea170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2343,-976 2343,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24981@0" ObjectIDZND0="24982@x" ObjectIDZND1="24980@x" Pin0InfoVect0LinkObjId="SW-138932_0" Pin0InfoVect1LinkObjId="SW-138924_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-976 2343,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bea360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2343,-962 2343,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24982@x" ObjectIDND1="24981@x" ObjectIDZND0="24980@1" Pin0InfoVect0LinkObjId="SW-138924_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138932_0" Pin1InfoVect1LinkObjId="SW-138931_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-962 2343,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa5220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3179,-444 3179,-451 3144,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1aa4740@0" ObjectIDZND0="24988@x" ObjectIDZND1="37993@x" Pin0InfoVect0LinkObjId="SW-138996_0" Pin0InfoVect1LinkObjId="EC-YR_ZH.091Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa4740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3179,-444 3179,-451 3144,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa5e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3144,-464 3144,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24988@0" ObjectIDZND0="g_1aa4740@0" ObjectIDZND1="37993@x" Pin0InfoVect0LinkObjId="g_1aa4740_0" Pin0InfoVect1LinkObjId="EC-YR_ZH.091Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3144,-464 3144,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa60a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3144,-451 3144,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1aa4740@0" ObjectIDND1="24988@x" ObjectIDZND0="37993@0" Pin0InfoVect0LinkObjId="EC-YR_ZH.091Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aa4740_0" Pin1InfoVect1LinkObjId="SW-138996_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3144,-451 3144,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa6300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-448 2952,-455 2917,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1aa6560@0" ObjectIDZND0="24991@x" ObjectIDZND1="37992@x" Pin0InfoVect0LinkObjId="SW-139042_0" Pin0InfoVect1LinkObjId="EC-YR_ZH.092Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa6560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-448 2952,-455 2917,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa79d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2917,-462 2917,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24991@0" ObjectIDZND0="g_1aa6560@0" ObjectIDZND1="37992@x" Pin0InfoVect0LinkObjId="g_1aa6560_0" Pin0InfoVect1LinkObjId="EC-YR_ZH.092Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139042_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2917,-462 2917,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa7c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2917,-455 2917,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1aa6560@0" ObjectIDND1="24991@x" ObjectIDZND0="37992@0" Pin0InfoVect0LinkObjId="EC-YR_ZH.092Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aa6560_0" Pin1InfoVect1LinkObjId="SW-139042_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2917,-455 2917,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa8bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2716,-445 2716,-452 2681,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1aa7e90@0" ObjectIDZND0="24994@x" ObjectIDZND1="37991@x" Pin0InfoVect0LinkObjId="SW-139088_0" Pin0InfoVect1LinkObjId="EC-YR_ZH.093Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa7e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2716,-445 2716,-452 2681,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa96b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2681,-461 2681,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24994@0" ObjectIDZND0="g_1aa7e90@0" ObjectIDZND1="37991@x" Pin0InfoVect0LinkObjId="g_1aa7e90_0" Pin0InfoVect1LinkObjId="EC-YR_ZH.093Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2681,-461 2681,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa9910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2681,-452 2681,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1aa7e90@0" ObjectIDND1="24994@x" ObjectIDZND0="37991@0" Pin0InfoVect0LinkObjId="EC-YR_ZH.093Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aa7e90_0" Pin1InfoVect1LinkObjId="SW-139088_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2681,-452 2681,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa9b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2486,-464 2486,-471 2451,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1aa9dd0@0" ObjectIDZND0="24997@x" ObjectIDZND1="37990@x" Pin0InfoVect0LinkObjId="SW-139136_0" Pin0InfoVect1LinkObjId="EC-YR_ZH.094Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa9dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2486,-464 2486,-471 2451,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aab390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2451,-480 2451,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24997@0" ObjectIDZND0="g_1aa9dd0@0" ObjectIDZND1="37990@x" Pin0InfoVect0LinkObjId="g_1aa9dd0_0" Pin0InfoVect1LinkObjId="EC-YR_ZH.094Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139136_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2451,-480 2451,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aab5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2451,-471 2451,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1aa9dd0@0" ObjectIDND1="24997@x" ObjectIDZND0="37990@0" Pin0InfoVect0LinkObjId="EC-YR_ZH.094Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aa9dd0_0" Pin1InfoVect1LinkObjId="SW-139136_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2451,-471 2451,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aab850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2255,-443 2255,-450 2220,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_1aabab0@0" ObjectIDZND0="25000@x" ObjectIDZND1="g_1b37110@0" ObjectIDZND2="37979@x" Pin0InfoVect0LinkObjId="SW-139180_0" Pin0InfoVect1LinkObjId="g_1b37110_0" Pin0InfoVect2LinkObjId="EC-YR_ZH.095Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aabab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2255,-443 2255,-450 2220,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a97450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-458 2220,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="25000@0" ObjectIDZND0="g_1aabab0@0" ObjectIDZND1="g_1b37110@0" ObjectIDZND2="37979@x" Pin0InfoVect0LinkObjId="g_1aabab0_0" Pin0InfoVect1LinkObjId="g_1b37110_0" Pin0InfoVect2LinkObjId="EC-YR_ZH.095Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-458 2220,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a976b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2220,-450 2220,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_1aabab0@0" ObjectIDND1="25000@x" ObjectIDZND0="g_1b37110@0" ObjectIDZND1="37979@x" Pin0InfoVect0LinkObjId="g_1b37110_0" Pin0InfoVect1LinkObjId="EC-YR_ZH.095Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aabab0_0" Pin1InfoVect1LinkObjId="SW-139180_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2220,-450 2220,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a9b130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1953,-517 1953,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a9a6a0@0" ObjectIDZND0="25004@1" Pin0InfoVect0LinkObjId="SW-139232_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a9a6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1953,-517 1953,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a51ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2852,-922 2852,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="40672@0" ObjectIDZND0="40671@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-241655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2852,-922 2852,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a56950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2851,-730 2851,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40675@0" ObjectIDZND0="40676@1" Pin0InfoVect0LinkObjId="SW-241659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-241658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2851,-730 2851,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a56bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2851,-807 2851,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="40671@1" ObjectIDZND0="40675@1" Pin0InfoVect0LinkObjId="SW-241658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a51ea0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2851,-807 2851,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1af26e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2818,-956 2818,-966 2853,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="40674@0" ObjectIDZND0="40673@x" ObjectIDZND1="40672@x" Pin0InfoVect0LinkObjId="SW-241656_0" Pin0InfoVect1LinkObjId="SW-241655_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-241657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2818,-956 2818,-966 2853,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1af31d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2853,-978 2853,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="40673@0" ObjectIDZND0="40674@x" ObjectIDZND1="40672@x" Pin0InfoVect0LinkObjId="SW-241657_0" Pin0InfoVect1LinkObjId="SW-241655_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-241656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2853,-978 2853,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1af3430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2853,-966 2853,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="40674@x" ObjectIDND1="40673@x" ObjectIDZND0="40672@1" Pin0InfoVect0LinkObjId="SW-241655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-241657_0" Pin1InfoVect1LinkObjId="SW-241656_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2853,-966 2853,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1af70b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2853,-1014 2853,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40673@1" ObjectIDZND0="24962@0" Pin0InfoVect0LinkObjId="g_1c01ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-241656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2853,-1014 2853,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af7790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2851,-685 2851,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40676@0" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1a94fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-241659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2851,-685 2851,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1afc9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-1210 2544,-1199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1af8f30@0" ObjectIDZND0="g_1afc130@0" Pin0InfoVect0LinkObjId="g_1afc130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1af8f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-1210 2544,-1199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1afcc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2514,-1170 2514,-1155 2544,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1afb380@0" ObjectIDZND0="g_1afc130@0" ObjectIDZND1="24979@x" ObjectIDZND2="24977@x" Pin0InfoVect0LinkObjId="g_1afc130_0" Pin0InfoVect1LinkObjId="SW-138912_0" Pin0InfoVect2LinkObjId="SW-138910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1afb380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2514,-1170 2514,-1155 2544,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1afd700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-1168 2544,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1afc130@1" ObjectIDZND0="g_1afb380@0" ObjectIDZND1="24979@x" ObjectIDZND2="24977@x" Pin0InfoVect0LinkObjId="g_1afb380_0" Pin0InfoVect1LinkObjId="SW-138912_0" Pin0InfoVect2LinkObjId="SW-138910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1afc130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-1168 2544,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1afd960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-1155 2544,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1afb380@0" ObjectIDND1="g_1afc130@0" ObjectIDZND0="24979@x" ObjectIDZND1="24977@x" Pin0InfoVect0LinkObjId="SW-138912_0" Pin0InfoVect1LinkObjId="SW-138910_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1afb380_0" Pin1InfoVect1LinkObjId="g_1afc130_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-1155 2544,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b001b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2648,-934 2648,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1affbb0@1" Pin0InfoVect0LinkObjId="g_1affbb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2648,-934 2648,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b00410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2648,-1001 2648,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_1affbb0@0" ObjectIDZND0="24962@0" Pin0InfoVect0LinkObjId="g_1c01ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1affbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2648,-1001 2648,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b05410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1970,-729 1970,-711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1b04b90@0" Pin0InfoVect0LinkObjId="g_1b04b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1970,-729 1970,-711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b05670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1970,-680 1970,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_1b04b90@1" ObjectIDZND0="24963@0" Pin0InfoVect0LinkObjId="g_1a94fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b04b90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1970,-680 1970,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b098e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3104,-847 3104,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1b06730@0" ObjectIDZND0="g_1b09060@0" Pin0InfoVect0LinkObjId="g_1b09060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b06730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3104,-847 3104,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b0a8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3139,-787 3139,-775 3104,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1b09b40@0" ObjectIDZND0="g_1b09060@0" ObjectIDZND1="24985@x" Pin0InfoVect0LinkObjId="g_1b09060_0" Pin0InfoVect1LinkObjId="SW-138989_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b09b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3139,-787 3139,-775 3104,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b0b3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3104,-803 3104,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1b09060@1" ObjectIDZND0="g_1b09b40@0" ObjectIDZND1="24985@x" Pin0InfoVect0LinkObjId="g_1b09b40_0" Pin0InfoVect1LinkObjId="SW-138989_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b09060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3104,-803 3104,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b0b640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3104,-775 3104,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b09b40@0" ObjectIDND1="g_1b09060@0" ObjectIDZND0="24985@1" Pin0InfoVect0LinkObjId="SW-138989_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b09b40_0" Pin1InfoVect1LinkObjId="g_1b09060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3104,-775 3104,-734 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130474" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.500000 -1314.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23914" ObjectName="DYN-YR_ZH"/>
     <cge:Meas_Ref ObjectId="130474"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1223.500000 33.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c94780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3056.000000 1116.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c95e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3072.000000 1100.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcd7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3064.000000 1131.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcdc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3064.000000 1146.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcdec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3064.000000 1160.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1300.500000 419.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bce1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3056.000000 1116.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bce4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3072.000000 1100.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bce6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3064.000000 1131.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bce920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3064.000000 1146.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bceb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3064.000000 1160.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcef80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2361.000000 1179.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2350.000000 1164.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd03e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2375.000000 1149.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd0e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3002.000000 1179.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd1140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2991.000000 1164.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd1380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3016.000000 1149.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd17a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2441.000000 951.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd1a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2430.000000 936.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c136b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2455.000000 921.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c13aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2441.000000 782.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c13d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2430.000000 767.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c13fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2455.000000 752.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.500000 -263.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c143c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 45.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c146c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 30.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c14900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c14c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2423.000000 847.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c157c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2423.000000 862.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2163.500000 -265.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5c000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 45.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5c650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 30.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5c890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2396.500000 -263.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5ccb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 45.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5cfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 30.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5d1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2628.500000 -262.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5d610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 45.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5d910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 30.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5db50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2864.500000 -261.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5df70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 45.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5e270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 30.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5e4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3089.500000 -262.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5e8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 45.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5ebd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 30.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b5ee10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af4ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2957.000000 973.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af4e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2946.000000 958.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af5050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2971.000000 943.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af5380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2960.000000 863.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af55e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2960.000000 878.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af5a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2940.000000 761.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af5cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2929.000000 746.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af5f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2954.000000 731.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YR_TPL" endPointId="0" endStationName="YR_ZH" flowDrawDirect="1" flowShape="0" id="AC-35kV.zhongta_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2241,-1344 2241,-1398 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="33969" ObjectName="AC-35kV.zhongta_line"/>
    <cge:TPSR_Ref TObjectID="33969_SS-194"/></metadata>
   <polyline fill="none" opacity="0" points="2241,-1344 2241,-1398 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WM" endPointId="0" endStationName="YR_ZH" flowDrawDirect="1" flowShape="0" id="AC-35kV.wanzhong_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2880,-1345 2880,-1399 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37447" ObjectName="AC-35kV.wanzhong_line"/>
    <cge:TPSR_Ref TObjectID="37447_SS-194"/></metadata>
   <polyline fill="none" opacity="0" points="2880,-1345 2880,-1399 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24962" cx="2241" cy="-1028" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24962" cx="2880" cy="-1028" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24962" cx="2544" cy="-1028" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="3104" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="1989" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="2917" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="3144" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="2451" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="2682" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="2220" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24962" cx="2343" cy="-1028" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="2341" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24962" cx="2853" cy="-1028" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="2851" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24963" cx="1970" cy="-660" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24962" cx="2648" cy="-1028" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-138924">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2334.200000 -903.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24980" ObjectName="SW-YR_ZH.YR_ZH_301BK"/>
     <cge:Meas_Ref ObjectId="138924"/>
    <cge:TPSR_Ref TObjectID="24980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2232.333333 -1133.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24971" ObjectName="SW-YR_ZH.YR_ZH_392BK"/>
     <cge:Meas_Ref ObjectId="138856"/>
    <cge:TPSR_Ref TObjectID="24971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139222">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1980.000000 -548.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25001" ObjectName="SW-YR_ZH.YR_ZH_097BK"/>
     <cge:Meas_Ref ObjectId="139222"/>
    <cge:TPSR_Ref TObjectID="25001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138937">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2332.200000 -711.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24983" ObjectName="SW-YR_ZH.YR_ZH_001BK"/>
     <cge:Meas_Ref ObjectId="138937"/>
    <cge:TPSR_Ref TObjectID="24983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138802">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2871.333333 -1132.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24965" ObjectName="SW-YR_ZH.YR_ZH_391BK"/>
     <cge:Meas_Ref ObjectId="138802"/>
    <cge:TPSR_Ref TObjectID="24965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139038">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2908.000000 -531.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24989" ObjectName="SW-YR_ZH.YR_ZH_092BK"/>
     <cge:Meas_Ref ObjectId="139038"/>
    <cge:TPSR_Ref TObjectID="24989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-138992">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3135.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24986" ObjectName="SW-YR_ZH.YR_ZH_091BK"/>
     <cge:Meas_Ref ObjectId="138992"/>
    <cge:TPSR_Ref TObjectID="24986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139084">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2672.000000 -530.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24992" ObjectName="SW-YR_ZH.YR_ZH_093BK"/>
     <cge:Meas_Ref ObjectId="139084"/>
    <cge:TPSR_Ref TObjectID="24992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139130">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2442.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24995" ObjectName="SW-YR_ZH.YR_ZH_094BK"/>
     <cge:Meas_Ref ObjectId="139130"/>
    <cge:TPSR_Ref TObjectID="24995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139176">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2211.000000 -527.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24998" ObjectName="SW-YR_ZH.YR_ZH_095BK"/>
     <cge:Meas_Ref ObjectId="139176"/>
    <cge:TPSR_Ref TObjectID="24998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-241655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2844.200000 -914.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40672" ObjectName="SW-YR_ZH.YR_ZH_302BK"/>
     <cge:Meas_Ref ObjectId="241655"/>
    <cge:TPSR_Ref TObjectID="40672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-241658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2842.200000 -722.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40675" ObjectName="SW-YR_ZH.YR_ZH_002BK"/>
     <cge:Meas_Ref ObjectId="241658"/>
    <cge:TPSR_Ref TObjectID="40675"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b59b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1942.000000 -334.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b1b910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3279.000000 -680.000000) translate(0,15)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c46ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3073.000000 -1049.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c01c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -908.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c35f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -822.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b33080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1260.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b33080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1260.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b33080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1260.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b33080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1260.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b33080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1260.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b33080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1260.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b33080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1260.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b33080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1260.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b33080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1260.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1b76d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1513.000000 -1401.500000) translate(0,16)">中和变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c47f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2214.500000 -1402.000000) translate(0,15)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c47f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2214.500000 -1402.000000) translate(0,33)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c47f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2214.500000 -1402.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b62ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2155.000000 -1208.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bc5900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2852.500000 -1401.000000) translate(0,15)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bc5900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2852.500000 -1401.000000) translate(0,33)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bc5900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2852.500000 -1401.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bc12f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2794.000000 -1207.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a94450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2598.000000 -840.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a94a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1923.500000 -855.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c31730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2881.000000 -379.000000) translate(0,15)">中和乡政府线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b931e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3116.000000 -377.000000) translate(0,15)">直苴Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b64ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2645.000000 -378.000000) translate(0,15)">小直么线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b8daf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2423.000000 -377.000000) translate(0,15)">直苴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b9dbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2192.000000 -375.000000) translate(0,15)">中厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ae6ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2496.000000 -1282.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae6f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2251.000000 -1162.000000) translate(0,12)">392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae7240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2248.000000 -1252.000000) translate(0,12)">3926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae7550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2279.000000 -1315.000000) translate(0,12)">39267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae7790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2279.000000 -1223.000000) translate(0,12)">39260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae7b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2280.000000 -1140.000000) translate(0,12)">39217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae7f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2248.000000 -1077.000000) translate(0,12)">3921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae81b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2551.000000 -1107.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae83f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2583.000000 -1172.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae8630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2583.000000 -1080.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae8870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2890.000000 -1161.000000) translate(0,12)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae8ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2887.000000 -1251.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae8cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2887.000000 -1076.000000) translate(0,12)">3911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae8f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2919.000000 -1139.000000) translate(0,12)">39117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae9170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2919.000000 -1222.000000) translate(0,12)">39160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae93b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2919.000000 -1314.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae95f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2353.000000 -932.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae9830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2348.000000 -1002.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae9a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2351.000000 -740.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae9cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2348.000000 -698.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae9ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 -723.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aea130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1998.000000 -577.000000) translate(0,12)">097</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aea370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1996.000000 -629.000000) translate(0,12)">0971</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aea5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1997.000000 -496.000000) translate(0,12)">0976</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aea7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2229.000000 -556.000000) translate(0,12)">095</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeaa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2227.000000 -612.000000) translate(0,12)">0951</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c92760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2227.000000 -483.000000) translate(0,12)">0956</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c929a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2460.000000 -558.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c92be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -505.000000) translate(0,12)">0946</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c93010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -614.000000) translate(0,12)">0941</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c93250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2690.000000 -559.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c93490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -486.000000) translate(0,12)">0936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c936d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2689.000000 -613.000000) translate(0,12)">0931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c93910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2926.000000 -560.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c93b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2924.000000 -487.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c93d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2924.000000 -614.000000) translate(0,12)">0921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c93fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3153.000000 -558.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c94210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -489.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c94450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -612.000000) translate(0,12)">0911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b37bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2149.000000 -349.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be9280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2258.000000 -940.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1beb4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1390.000000 -1044.000000) translate(0,12)">公用间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1bec9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.000000 -1412.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1aa0a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.000000 -1380.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa2600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2198.000000 -851.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa2600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2198.000000 -851.000000) translate(0,27)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b84190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1869.000000 -493.000000) translate(0,12)">09767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b85af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.500000 -463.000000) translate(0,17)">6841237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b5f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 -418.000000) translate(0,17)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b60650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.000000 -428.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b60650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.000000 -428.500000) translate(0,38)">13987885824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4d3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2239.000000 -879.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af3690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2863.000000 -943.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af3c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2860.000000 -1003.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af3e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2765.000000 -942.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af40a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2866.000000 -752.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af42e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2863.000000 -710.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af4520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2890.000000 -835.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af4520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2890.000000 -835.000000) translate(0,27)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1af81f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1835.500000 -1396.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1afdbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2669.000000 -998.000000) translate(0,15)">3931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b02670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 -865.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b058d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1996.500000 -705.000000) translate(0,15)">0981</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c120d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2332.000000 -1108.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c44e00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2332.000000 -1191.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bca120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2332.000000 -1283.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd3e80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2971.000000 -1107.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be1d00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2971.000000 -1190.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbcb60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2971.000000 -1282.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9f600" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2635.000000 -1048.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a92ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2635.000000 -1140.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9a6a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1947.000000 -512.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YR_ZH"/>
</svg>