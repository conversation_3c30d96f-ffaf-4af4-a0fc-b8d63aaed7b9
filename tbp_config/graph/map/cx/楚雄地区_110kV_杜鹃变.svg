<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-244" aopId="2884222" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1252 2257 1357">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape25">
    <polyline arcFlag="1" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <polyline arcFlag="1" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <polyline arcFlag="1" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="34" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="48" y2="31"/>
    <circle cx="30" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="64" x2="64" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.1301" x1="6" x2="6" y1="17" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.1301" x1="0" x2="13" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.1301" x1="4" x2="9" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.06525" x1="5" x2="8" y1="2" y2="2"/>
    <polyline arcFlag="1" points="7,29 7,29 7,29 7,29 8,29 8,30 8,30 8,30 9,30 9,31 9,31 9,31 9,32 9,32 9,32 9,33 9,33 9,34 9,34 8,34 8,34 8,35 8,35 7,35 7,35 7,35 " stroke-width="0.323101"/>
    <polyline arcFlag="1" points="6,17 7,17 7,17 7,17 7,17 8,17 8,17 8,18 8,18 8,18 8,19 9,19 9,19 9,20 9,20 9,20 8,21 8,21 8,21 8,22 8,22 8,22 7,22 7,23 7,23 7,23 " stroke-width="0.323101"/>
    <polyline arcFlag="1" points="7,23 7,23 7,23 7,23 8,23 8,23 8,24 8,24 9,24 9,24 9,25 9,25 9,25 9,26 9,26 9,27 9,27 9,27 9,28 8,28 8,28 8,28 8,29 7,29 7,29 7,29 " stroke-width="0.323101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.35042" x1="7" x2="7" y1="46" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape201">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.485753" x1="3" x2="15" y1="27" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="15" x2="9" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="90" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.532624" x1="5" x2="8" y1="100" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="3" x2="10" y1="98" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="0" x2="13" y1="96" y2="96"/>
    <polyline arcFlag="1" points="6,79 7,79 7,79 8,79 9,79 9,78 10,78 11,77 11,77 11,76 12,75 12,75 12,74 12,73 12,72 12,72 11,71 11,70 11,70 10,69 9,69 9,68 8,68 7,68 7,68 6,68 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,68 7,68 7,68 8,68 9,68 9,67 10,67 11,66 11,66 11,65 12,64 12,64 12,63 12,62 12,61 12,61 11,60 11,59 11,59 10,58 9,58 9,57 8,57 7,57 7,57 6,57 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,57 7,57 7,57 8,57 9,57 9,56 10,56 11,55 11,55 11,54 12,53 12,53 12,52 12,51 12,50 12,50 11,49 11,48 11,48 10,47 9,47 9,46 8,46 7,46 7,46 6,46 " stroke-width="0.815047"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="4" y2="13"/>
    <polyline arcFlag="1" points="6,46 7,46 7,46 8,46 9,46 9,45 10,45 11,44 11,44 11,43 12,42 12,42 12,41 12,40 12,39 12,39 11,38 11,37 11,37 10,36 9,36 9,35 8,35 7,35 7,35 6,35 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,35 7,35 7,35 8,35 9,35 9,34 10,34 11,33 11,33 11,32 12,31 12,31 12,30 12,29 12,28 12,28 11,27 11,26 11,26 10,25 9,25 9,24 8,24 7,24 7,24 6,24 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,90 7,90 7,90 8,90 9,90 9,89 10,89 11,88 11,88 11,87 12,86 12,86 12,85 12,84 12,83 12,83 11,82 11,81 11,81 10,80 9,80 9,79 8,79 7,79 7,79 6,79 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,24 7,24 7,24 8,24 9,24 9,23 10,23 11,22 11,22 11,21 12,20 12,20 12,19 12,18 12,17 12,17 11,16 11,15 11,15 10,14 9,14 9,13 8,13 7,13 7,13 6,13 " stroke-width="0.815047"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.71892" x1="15" x2="15" y1="8" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape152">
    <ellipse cx="13" cy="27" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="53" x2="60" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="47" x2="65" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="54" x2="58" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="56" y1="28" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="56" y1="28" y2="28"/>
    <circle cx="13" cy="46" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="23" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="27" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="31" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="42" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="46" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="50" y2="46"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape77_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 6,61 6,32 " stroke-width="1"/>
    <circle cx="31" cy="64" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="19" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="31"/>
    <polyline DF8003:Layer="PUBLIC" points="31,18 25,31 37,31 31,18 31,19 31,18 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape77_1">
    <circle cx="31" cy="86" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="26" y1="90" y2="90"/>
   </symbol>
   <symbol id="voltageTransformer:shape117">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="9" y2="24"/>
    <circle cx="59" cy="16" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="22" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="61" x2="62" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="58" x2="57" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="57" x2="62" y1="14" y2="14"/>
    <circle cx="48" cy="9" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="22" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="23" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="23" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="52" x2="48" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="48" x2="44" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="48" x2="48" y1="10" y2="5"/>
    <circle cx="35" cy="9" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="24" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape0">
    <circle cx="21" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="voltageTransformer:shape49">
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="18" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="11" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="11" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="32" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="8" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2569530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25906e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2555000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ccb870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2de6a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e07b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cdabc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2eacf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d35330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d35330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e2b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e2b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d709f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d709f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2e076b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26ce910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e3f2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dcdca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dd3f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d18690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2daaa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b7a030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e188a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ec70f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e77050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b7a8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e52720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ea7120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e60d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d18140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cc3790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ea30a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ca9d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2545540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2e5a930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1367" width="2267" x="3110" y="-1257"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5308,14 5308,14 5307,14 5307,14 5306,13 5306,13 5306,13 5305,12 5305,12 5305,12 5305,11 5305,11 5304,10 5304,10 5305,9 5305,9 5305,8 5305,8 5305,7 5306,7 5306,7 5306,6 5307,6 5307,6 5308,6 5308,6 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5308,22 5308,22 5307,22 5307,21 5306,21 5306,21 5306,21 5305,20 5305,20 5305,19 5305,19 5305,18 5304,18 5304,17 5305,17 5305,16 5305,16 5305,15 5305,15 5306,15 5306,14 5306,14 5307,14 5307,14 5308,14 5308,14 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5308,37 5308,37 5307,37 5307,37 5306,37 5306,36 5306,36 5305,36 5305,35 5305,35 5305,34 5305,34 5304,33 5304,33 5305,32 5305,32 5305,31 5305,31 5305,30 5306,30 5306,30 5306,29 5307,29 5307,29 5308,29 5308,29 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5308,45 5308,45 5307,45 5307,44 5306,44 5306,44 5306,44 5305,43 5305,43 5305,42 5305,42 5305,41 5304,41 5304,40 5305,40 5305,39 5305,39 5305,38 5305,38 5306,38 5306,37 5306,37 5307,37 5307,37 5308,37 5308,37 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5366,0 5366,0 5367,0 5367,0 5367,0 5368,-1 5368,-1 5369,-1 5369,-2 5369,-2 5369,-3 5369,-3 5369,-4 5369,-4 5369,-5 5369,-5 5369,-6 5369,-6 5369,-7 5368,-7 5368,-7 5367,-8 5367,-8 5367,-8 5366,-8 5366,-8 " stroke="rgb(50,205,50)" stroke-width="0.0277671"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(170,85,127)" stroke-dasharray="10 5 " stroke-width="6" x1="4310" x2="4463" y1="-886" y2="-886"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(255,255,0)" stroke-dasharray="10 5 " stroke-width="1" x1="4273" x2="4351" y1="-506" y2="-506"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(255,255,0)" stroke-dasharray="10 5 " stroke-width="1" x1="4866" x2="4866" y1="-544" y2="-494"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.284211" x1="4987" x2="4983" y1="-977" y2="-974"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.284211" x1="4992" x2="4996" y1="-980" y2="-977"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.284211" x1="4996" x2="4992" y1="-977" y2="-974"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.284211" x1="4983" x2="4987" y1="-980" y2="-977"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4617" x2="4614" y1="-896" y2="-896"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4619" x2="4617" y1="-899" y2="-896"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4619" x2="4617" y1="-893" y2="-896"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3840" x2="3871" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3995" x2="4026" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5339" x2="5347" y1="-748" y2="-748"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="5299" x2="5299" y1="5" y2="21"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="5299" x2="5299" y1="29" y2="45"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5308" x2="5332" y1="44" y2="44"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5308" x2="5328" y1="29" y2="29"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5308" x2="5314" y1="21" y2="21"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5308" x2="5314" y1="6" y2="6"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5332" x2="5332" y1="50" y2="45"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5332" x2="5350" y1="50" y2="50"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5337" x2="5350" y1="29" y2="29"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5366" x2="5366" y1="-68" y2="-3"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.637931" x1="5371" x2="5366" y1="0" y2="12"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5366" x2="5366" y1="12" y2="19"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="5362" x2="5370" y1="34" y2="34"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="5363" x2="5368" y1="35" y2="35"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.339777" x1="5365" x2="5367" y1="37" y2="37"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.197315" x1="5366" x2="5366" y1="30" y2="34"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5350" x2="5350" y1="37" y2="29"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5353" x2="5350" y1="41" y2="41"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5353" x2="5350" y1="41" y2="37"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5350" x2="5350" y1="41" y2="50"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5288" x2="5366" y1="-69" y2="-69"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="5305" x2="5313" y1="-41" y2="-41"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="5305" x2="5313" y1="-23" y2="-23"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5271" x2="5231" y1="-127" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5231" x2="5231" y1="-117" y2="-75"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3395" y="-1180"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.416609" width="26" x="4925" y="-983"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.416609" width="26" x="4663" y="-909"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="8" stroke="rgb(0,255,0)" stroke-width="0.277778" width="17" x="3715" y="-193"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(0,255,0)" stroke-width="0.416609" width="13" x="4127" y="-231"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="64" stroke="rgb(0,255,0)" stroke-width="1" width="37" x="5280" y="-6"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="32" stroke="rgb(0,255,0)" stroke-width="1" width="34" x="5322" y="22"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(0,255,0)" stroke-width="1" width="10" x="5328" y="26"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="10" stroke="rgb(0,255,0)" stroke-width="1" width="5" x="5363" y="19"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-176871">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.000000 -907.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27404" ObjectName="SW-CX_DuJ.CX_DuJ_1311SW"/>
     <cge:Meas_Ref ObjectId="176871"/>
    <cge:TPSR_Ref TObjectID="27404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176873">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.000000 -1030.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27406" ObjectName="SW-CX_DuJ.CX_DuJ_1316SW"/>
     <cge:Meas_Ref ObjectId="176873"/>
    <cge:TPSR_Ref TObjectID="27406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176872">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 -956.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27405" ObjectName="SW-CX_DuJ.CX_DuJ_13117SW"/>
     <cge:Meas_Ref ObjectId="176872"/>
    <cge:TPSR_Ref TObjectID="27405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176874">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 -1014.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27407" ObjectName="SW-CX_DuJ.CX_DuJ_13160SW"/>
     <cge:Meas_Ref ObjectId="176874"/>
    <cge:TPSR_Ref TObjectID="27407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176875">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 -1083.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27408" ObjectName="SW-CX_DuJ.CX_DuJ_13167SW"/>
     <cge:Meas_Ref ObjectId="176875"/>
    <cge:TPSR_Ref TObjectID="27408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176935">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.844492 -832.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27412" ObjectName="SW-CX_DuJ.CX_DuJ_1121SW"/>
     <cge:Meas_Ref ObjectId="176935"/>
    <cge:TPSR_Ref TObjectID="27412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176936">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.844492 -772.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27413" ObjectName="SW-CX_DuJ.CX_DuJ_11217SW"/>
     <cge:Meas_Ref ObjectId="176936"/>
    <cge:TPSR_Ref TObjectID="27413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176937">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.844492 -830.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27414" ObjectName="SW-CX_DuJ.CX_DuJ_1122SW"/>
     <cge:Meas_Ref ObjectId="176937"/>
    <cge:TPSR_Ref TObjectID="27414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176938">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.844492 -772.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27415" ObjectName="SW-CX_DuJ.CX_DuJ_11227SW"/>
     <cge:Meas_Ref ObjectId="176938"/>
    <cge:TPSR_Ref TObjectID="27415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176954">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -786.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27416" ObjectName="SW-CX_DuJ.CX_DuJ_1901SW"/>
     <cge:Meas_Ref ObjectId="176954"/>
    <cge:TPSR_Ref TObjectID="27416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176956">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 -847.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27418" ObjectName="SW-CX_DuJ.CX_DuJ_11017SW"/>
     <cge:Meas_Ref ObjectId="176956"/>
    <cge:TPSR_Ref TObjectID="27418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176955">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 -773.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27417" ObjectName="SW-CX_DuJ.CX_DuJ_19017SW"/>
     <cge:Meas_Ref ObjectId="176955"/>
    <cge:TPSR_Ref TObjectID="27417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176976">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -658.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27424" ObjectName="SW-CX_DuJ.CX_DuJ_10167SW"/>
     <cge:Meas_Ref ObjectId="176976"/>
    <cge:TPSR_Ref TObjectID="27424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176975">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -734.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27423" ObjectName="SW-CX_DuJ.CX_DuJ_10160SW"/>
     <cge:Meas_Ref ObjectId="176975"/>
    <cge:TPSR_Ref TObjectID="27423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176973">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -804.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27421" ObjectName="SW-CX_DuJ.CX_DuJ_10117SW"/>
     <cge:Meas_Ref ObjectId="176973"/>
    <cge:TPSR_Ref TObjectID="27421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177015">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -501.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27431" ObjectName="SW-CX_DuJ.CX_DuJ_3010SW"/>
     <cge:Meas_Ref ObjectId="177015"/>
    <cge:TPSR_Ref TObjectID="27431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176932">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 -907.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27409" ObjectName="SW-CX_DuJ.CX_DuJ_1321SW"/>
     <cge:Meas_Ref ObjectId="176932"/>
    <cge:TPSR_Ref TObjectID="27409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 -1030.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176933">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -956.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27410" ObjectName="SW-CX_DuJ.CX_DuJ_13217SW"/>
     <cge:Meas_Ref ObjectId="176933"/>
    <cge:TPSR_Ref TObjectID="27410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -1013.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -1083.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177021">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -451.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27433" ObjectName="SW-CX_DuJ.CX_DuJ_001XC"/>
     <cge:Meas_Ref ObjectId="177021"/>
    <cge:TPSR_Ref TObjectID="27433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177021">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -390.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27434" ObjectName="SW-CX_DuJ.CX_DuJ_001XC1"/>
     <cge:Meas_Ref ObjectId="177021"/>
    <cge:TPSR_Ref TObjectID="27434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177001">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4652.000000 -571.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27426" ObjectName="SW-CX_DuJ.CX_DuJ_301XC"/>
     <cge:Meas_Ref ObjectId="177001"/>
    <cge:TPSR_Ref TObjectID="27426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177001">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -571.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27427" ObjectName="SW-CX_DuJ.CX_DuJ_301XC1"/>
     <cge:Meas_Ref ObjectId="177001"/>
    <cge:TPSR_Ref TObjectID="27427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177170">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -941.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27437" ObjectName="SW-CX_DuJ.CX_DuJ_331XC"/>
     <cge:Meas_Ref ObjectId="177170"/>
    <cge:TPSR_Ref TObjectID="27437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177170">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -941.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27438" ObjectName="SW-CX_DuJ.CX_DuJ_331XC1"/>
     <cge:Meas_Ref ObjectId="177170"/>
    <cge:TPSR_Ref TObjectID="27438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177171">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -901.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27439" ObjectName="SW-CX_DuJ.CX_DuJ_33167SW"/>
     <cge:Meas_Ref ObjectId="177171"/>
    <cge:TPSR_Ref TObjectID="27439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177159">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 -945.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27435" ObjectName="SW-CX_DuJ.CX_DuJ_3316SW"/>
     <cge:Meas_Ref ObjectId="177159"/>
    <cge:TPSR_Ref TObjectID="27435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.000000 -887.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177193">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -822.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27442" ObjectName="SW-CX_DuJ.CX_DuJ_332XC"/>
     <cge:Meas_Ref ObjectId="177193"/>
    <cge:TPSR_Ref TObjectID="27442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177193">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -822.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27443" ObjectName="SW-CX_DuJ.CX_DuJ_332XC1"/>
     <cge:Meas_Ref ObjectId="177193"/>
    <cge:TPSR_Ref TObjectID="27443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177194">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -782.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27444" ObjectName="SW-CX_DuJ.CX_DuJ_33267SW"/>
     <cge:Meas_Ref ObjectId="177194"/>
    <cge:TPSR_Ref TObjectID="27444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177182">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 -826.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27440" ObjectName="SW-CX_DuJ.CX_DuJ_3326SW"/>
     <cge:Meas_Ref ObjectId="177182"/>
    <cge:TPSR_Ref TObjectID="27440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177214">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -717.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27447" ObjectName="SW-CX_DuJ.CX_DuJ_333XC"/>
     <cge:Meas_Ref ObjectId="177214"/>
    <cge:TPSR_Ref TObjectID="27447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177214">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -717.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27448" ObjectName="SW-CX_DuJ.CX_DuJ_333XC1"/>
     <cge:Meas_Ref ObjectId="177214"/>
    <cge:TPSR_Ref TObjectID="27448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177215">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -677.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27449" ObjectName="SW-CX_DuJ.CX_DuJ_33367SW"/>
     <cge:Meas_Ref ObjectId="177215"/>
    <cge:TPSR_Ref TObjectID="27449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177203">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 -721.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27445" ObjectName="SW-CX_DuJ.CX_DuJ_3336SW"/>
     <cge:Meas_Ref ObjectId="177203"/>
    <cge:TPSR_Ref TObjectID="27445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177235">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -616.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27452" ObjectName="SW-CX_DuJ.CX_DuJ_334XC"/>
     <cge:Meas_Ref ObjectId="177235"/>
    <cge:TPSR_Ref TObjectID="27452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177235">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -616.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27453" ObjectName="SW-CX_DuJ.CX_DuJ_334XC1"/>
     <cge:Meas_Ref ObjectId="177235"/>
    <cge:TPSR_Ref TObjectID="27453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177236">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -576.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27454" ObjectName="SW-CX_DuJ.CX_DuJ_33467SW"/>
     <cge:Meas_Ref ObjectId="177236"/>
    <cge:TPSR_Ref TObjectID="27454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177224">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 -620.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27450" ObjectName="SW-CX_DuJ.CX_DuJ_3346SW"/>
     <cge:Meas_Ref ObjectId="177224"/>
    <cge:TPSR_Ref TObjectID="27450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177270">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27460" ObjectName="SW-CX_DuJ.CX_DuJ_038XC"/>
     <cge:Meas_Ref ObjectId="177270"/>
    <cge:TPSR_Ref TObjectID="27460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177270">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.000000 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27461" ObjectName="SW-CX_DuJ.CX_DuJ_038XC1"/>
     <cge:Meas_Ref ObjectId="177270"/>
    <cge:TPSR_Ref TObjectID="27461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177271">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3826.000000 -219.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27462" ObjectName="SW-CX_DuJ.CX_DuJ_03860SW"/>
     <cge:Meas_Ref ObjectId="177271"/>
    <cge:TPSR_Ref TObjectID="27462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177275">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -86.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27463" ObjectName="SW-CX_DuJ.CX_DuJ_0386SW"/>
     <cge:Meas_Ref ObjectId="177275"/>
    <cge:TPSR_Ref TObjectID="27463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177276">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3811.000000 -85.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27464" ObjectName="SW-CX_DuJ.CX_DuJ_03867SW"/>
     <cge:Meas_Ref ObjectId="177276"/>
    <cge:TPSR_Ref TObjectID="27464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177294">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4019.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27466" ObjectName="SW-CX_DuJ.CX_DuJ_037XC"/>
     <cge:Meas_Ref ObjectId="177294"/>
    <cge:TPSR_Ref TObjectID="27466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177294">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4019.000000 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27467" ObjectName="SW-CX_DuJ.CX_DuJ_037XC1"/>
     <cge:Meas_Ref ObjectId="177294"/>
    <cge:TPSR_Ref TObjectID="27467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177295">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3981.000000 -219.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27468" ObjectName="SW-CX_DuJ.CX_DuJ_03760SW"/>
     <cge:Meas_Ref ObjectId="177295"/>
    <cge:TPSR_Ref TObjectID="27468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 -86.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27469" ObjectName="SW-CX_DuJ.CX_DuJ_0376SW"/>
     <cge:Meas_Ref ObjectId="177300"/>
    <cge:TPSR_Ref TObjectID="27469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -85.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27470" ObjectName="SW-CX_DuJ.CX_DuJ_03767SW"/>
     <cge:Meas_Ref ObjectId="177301"/>
    <cge:TPSR_Ref TObjectID="27470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177321">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27472" ObjectName="SW-CX_DuJ.CX_DuJ_035XC"/>
     <cge:Meas_Ref ObjectId="177321"/>
    <cge:TPSR_Ref TObjectID="27472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177321">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27473" ObjectName="SW-CX_DuJ.CX_DuJ_035XC1"/>
     <cge:Meas_Ref ObjectId="177321"/>
    <cge:TPSR_Ref TObjectID="27473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.000000 -219.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27474" ObjectName="SW-CX_DuJ.CX_DuJ_03567SW"/>
     <cge:Meas_Ref ObjectId="177322"/>
    <cge:TPSR_Ref TObjectID="27474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177344">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27476" ObjectName="SW-CX_DuJ.CX_DuJ_034XC"/>
     <cge:Meas_Ref ObjectId="177344"/>
    <cge:TPSR_Ref TObjectID="27476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177344">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27477" ObjectName="SW-CX_DuJ.CX_DuJ_034XC1"/>
     <cge:Meas_Ref ObjectId="177344"/>
    <cge:TPSR_Ref TObjectID="27477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4314.000000 -219.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27478" ObjectName="SW-CX_DuJ.CX_DuJ_03467SW"/>
     <cge:Meas_Ref ObjectId="177345"/>
    <cge:TPSR_Ref TObjectID="27478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177368">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27481" ObjectName="SW-CX_DuJ.CX_DuJ_033XC"/>
     <cge:Meas_Ref ObjectId="177368"/>
    <cge:TPSR_Ref TObjectID="27481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177368">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27482" ObjectName="SW-CX_DuJ.CX_DuJ_033XC1"/>
     <cge:Meas_Ref ObjectId="177368"/>
    <cge:TPSR_Ref TObjectID="27482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177369">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 -219.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27483" ObjectName="SW-CX_DuJ.CX_DuJ_03367SW"/>
     <cge:Meas_Ref ObjectId="177369"/>
    <cge:TPSR_Ref TObjectID="27483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4602.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27485" ObjectName="SW-CX_DuJ.CX_DuJ_032XC"/>
     <cge:Meas_Ref ObjectId="177391"/>
    <cge:TPSR_Ref TObjectID="27485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4602.000000 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27486" ObjectName="SW-CX_DuJ.CX_DuJ_032XC1"/>
     <cge:Meas_Ref ObjectId="177391"/>
    <cge:TPSR_Ref TObjectID="27486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177392">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 -219.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27487" ObjectName="SW-CX_DuJ.CX_DuJ_03267SW"/>
     <cge:Meas_Ref ObjectId="177392"/>
    <cge:TPSR_Ref TObjectID="27487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27489" ObjectName="SW-CX_DuJ.CX_DuJ_031XC"/>
     <cge:Meas_Ref ObjectId="177414"/>
    <cge:TPSR_Ref TObjectID="27489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 -238.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27490" ObjectName="SW-CX_DuJ.CX_DuJ_031XC1"/>
     <cge:Meas_Ref ObjectId="177414"/>
    <cge:TPSR_Ref TObjectID="27490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 -219.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27491" ObjectName="SW-CX_DuJ.CX_DuJ_03167SW"/>
     <cge:Meas_Ref ObjectId="177415"/>
    <cge:TPSR_Ref TObjectID="27491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176972">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -824.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27420" ObjectName="SW-CX_DuJ.CX_DuJ_1011SW"/>
     <cge:Meas_Ref ObjectId="176972"/>
    <cge:TPSR_Ref TObjectID="27420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176974">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -677.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27422" ObjectName="SW-CX_DuJ.CX_DuJ_1016SW"/>
     <cge:Meas_Ref ObjectId="176974"/>
    <cge:TPSR_Ref TObjectID="27422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177425">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 -307.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27492" ObjectName="SW-CX_DuJ.CX_DuJ_0901XC"/>
     <cge:Meas_Ref ObjectId="177425"/>
    <cge:TPSR_Ref TObjectID="27492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177425">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 -275.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27493" ObjectName="SW-CX_DuJ.CX_DuJ_0901XC1"/>
     <cge:Meas_Ref ObjectId="177425"/>
    <cge:TPSR_Ref TObjectID="27493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -298.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27496" ObjectName="SW-CX_DuJ.CX_DuJ_0361XC"/>
     <cge:Meas_Ref ObjectId="177431"/>
    <cge:TPSR_Ref TObjectID="27496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -235.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27497" ObjectName="SW-CX_DuJ.CX_DuJ_0361XC1"/>
     <cge:Meas_Ref ObjectId="177431"/>
    <cge:TPSR_Ref TObjectID="27497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4815.000000 -305.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27494" ObjectName="SW-CX_DuJ.CX_DuJ_0121XC"/>
     <cge:Meas_Ref ObjectId="177429"/>
    <cge:TPSR_Ref TObjectID="27494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4815.000000 -253.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27495" ObjectName="SW-CX_DuJ.CX_DuJ_0121XC1"/>
     <cge:Meas_Ref ObjectId="177429"/>
    <cge:TPSR_Ref TObjectID="27495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.000000 -523.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27429" ObjectName="SW-CX_DuJ.CX_DuJ_30167SW"/>
     <cge:Meas_Ref ObjectId="177004"/>
    <cge:TPSR_Ref TObjectID="27429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177252">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -535.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27457" ObjectName="SW-CX_DuJ.CX_DuJ_3121XC"/>
     <cge:Meas_Ref ObjectId="177252"/>
    <cge:TPSR_Ref TObjectID="27457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177252">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4838.000000 -535.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27458" ObjectName="SW-CX_DuJ.CX_DuJ_3121XC1"/>
     <cge:Meas_Ref ObjectId="177252"/>
    <cge:TPSR_Ref TObjectID="27458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 -893.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27455" ObjectName="SW-CX_DuJ.CX_DuJ_3901XC"/>
     <cge:Meas_Ref ObjectId="177239"/>
    <cge:TPSR_Ref TObjectID="27455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.000000 -893.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27456" ObjectName="SW-CX_DuJ.CX_DuJ_3901XC1"/>
     <cge:Meas_Ref ObjectId="177239"/>
    <cge:TPSR_Ref TObjectID="27456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 -575.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27428" ObjectName="SW-CX_DuJ.CX_DuJ_3016SW"/>
     <cge:Meas_Ref ObjectId="177003"/>
    <cge:TPSR_Ref TObjectID="27428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177005">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -523.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27430" ObjectName="SW-CX_DuJ.CX_DuJ_30160SW"/>
     <cge:Meas_Ref ObjectId="177005"/>
    <cge:TPSR_Ref TObjectID="27430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5044.000000 -793.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.788991 -234.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40977" ObjectName="SW-CX_DuJ.CX_DuJ_012XC"/>
     <cge:Meas_Ref ObjectId="243831"/>
    <cge:TPSR_Ref TObjectID="40977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4975.788991 -234.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40978" ObjectName="SW-CX_DuJ.CX_DuJ_012XC1"/>
     <cge:Meas_Ref ObjectId="243831"/>
    <cge:TPSR_Ref TObjectID="40978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243884">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5094.596330 -291.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40986" ObjectName="SW-CX_DuJ.CX_DuJ_051XC"/>
     <cge:Meas_Ref ObjectId="243884"/>
    <cge:TPSR_Ref TObjectID="40986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243884">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5094.596330 -232.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40987" ObjectName="SW-CX_DuJ.CX_DuJ_051XC1"/>
     <cge:Meas_Ref ObjectId="243884"/>
    <cge:TPSR_Ref TObjectID="40987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243885">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.000000 -202.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40988" ObjectName="SW-CX_DuJ.CX_DuJ_05167SW"/>
     <cge:Meas_Ref ObjectId="243885"/>
    <cge:TPSR_Ref TObjectID="40988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177009">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -553.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27502" ObjectName="SW-CX_DuJ.CX_DuJ_1010SW"/>
     <cge:Meas_Ref ObjectId="177009"/>
    <cge:TPSR_Ref TObjectID="27502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-303821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5315.500000 -118.994737)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47057" ObjectName="SW-CX_DuJ.CX_DuJ_0520SW"/>
     <cge:Meas_Ref ObjectId="303821"/>
    <cge:TPSR_Ref TObjectID="47057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-303798">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5220.000000 -291.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47054" ObjectName="SW-CX_DuJ.CX_DuJ_002XC"/>
     <cge:Meas_Ref ObjectId="303798"/>
    <cge:TPSR_Ref TObjectID="47054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-303798">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5220.000000 -189.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47055" ObjectName="SW-CX_DuJ.CX_DuJ_002XC1"/>
     <cge:Meas_Ref ObjectId="303798"/>
    <cge:TPSR_Ref TObjectID="47055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-303799">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5170.000000 -173.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47056" ObjectName="SW-CX_DuJ.CX_DuJ_05267SW"/>
     <cge:Meas_Ref ObjectId="303799"/>
    <cge:TPSR_Ref TObjectID="47056"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3b1e940">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -733.000000)" xlink:href="#voltageTransformer:shape117"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b85ee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4973.000000 -971.000000)" xlink:href="#voltageTransformer:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3be0e00">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4654.000000 -883.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c714f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.000000 -165.000000)" xlink:href="#voltageTransformer:shape117"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d1f6a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5115.000000 -789.000000)" xlink:href="#voltageTransformer:shape49"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_DuJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.xihudujTduj_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3852,-1176 3852,-1211 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28369" ObjectName="AC-110kV.xihudujTduj_line"/>
    <cge:TPSR_Ref TObjectID="28369_SS-244"/></metadata>
   <polyline fill="none" opacity="0" points="3852,-1176 3852,-1211 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4142,-1175 4142,-1211 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4142,-1175 4142,-1211 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DuJ" endPointId="0" endStationName="CX_DGK" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_duda" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5119,-831 5177,-831 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48297" ObjectName="AC-35kV.LN_duda"/>
    <cge:TPSR_Ref TObjectID="48297_SS-244"/></metadata>
   <polyline fill="none" opacity="0" points="5119,-831 5177,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XJ" endPointId="0" endStationName="CX_DuJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.duxizhong_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5094,-950 5146,-950 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38057" ObjectName="AC-35kV.duxizhong_line"/>
    <cge:TPSR_Ref TObjectID="38057_SS-244"/></metadata>
   <polyline fill="none" opacity="0" points="5094,-950 5146,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DuJ" endPointId="0" endStationName="CX_BJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_duba" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5137,-726 5178,-726 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38062" ObjectName="AC-35kV.LN_duba"/>
    <cge:TPSR_Ref TObjectID="38062_SS-244"/></metadata>
   <polyline fill="none" opacity="0" points="5137,-726 5178,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DuJ" endPointId="0" endStationName="CX_SJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dusan" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5137,-625 5177,-625 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38063" ObjectName="AC-35kV.LN_dusan"/>
    <cge:TPSR_Ref TObjectID="38063_SS-244"/></metadata>
   <polyline fill="none" opacity="0" points="5137,-625 5177,-625 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_DuJ.035Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4231.000000 23.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34459" ObjectName="EC-CX_DuJ.035Ld"/>
    <cge:TPSR_Ref TObjectID="34459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DuJ.034Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 25.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34460" ObjectName="EC-CX_DuJ.034Ld"/>
    <cge:TPSR_Ref TObjectID="34460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DuJ.033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 24.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34461" ObjectName="EC-CX_DuJ.033Ld"/>
    <cge:TPSR_Ref TObjectID="34461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DuJ.032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 26.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34462" ObjectName="EC-CX_DuJ.032Ld"/>
    <cge:TPSR_Ref TObjectID="34462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DuJ.031Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4728.000000 25.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34463" ObjectName="EC-CX_DuJ.031Ld"/>
    <cge:TPSR_Ref TObjectID="34463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DuJ.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 23.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42250" ObjectName="EC-CX_DuJ.051Ld"/>
    <cge:TPSR_Ref TObjectID="42250"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3b77be0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -803.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c6f6b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -733.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c8dcc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -657.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ccd3c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3765.000000 -846.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bef000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3765.000000 -772.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a49740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.844492 -745.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c772a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.844492 -745.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bd1060" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3774.000000 -1082.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c2cd10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3774.000000 -1013.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cc1a80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3774.000000 -955.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ba2e40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -495.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c04830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -1082.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a29740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -1012.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c8ec00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -955.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bc3b10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -880.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c3e270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -761.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b42b60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -656.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b9ef40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -555.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b729c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -218.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ac28d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3838.000000 -130.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ac3320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -121.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cae900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -218.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c514d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 -130.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c51f60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 -121.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b8cfe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -218.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ef7140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 -218.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39b31a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4421.000000 -218.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e23800" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4543.000000 -218.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e31560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.000000 -218.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d15130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4625.000000 -495.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3de86e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5026.000000 -201.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e12270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5149.000000 -172.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_3c9fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-961 3852,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27405@1" ObjectIDZND0="27403@x" ObjectIDZND1="27404@x" Pin0InfoVect0LinkObjId="SW-176870_0" Pin0InfoVect1LinkObjId="SW-176871_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176872_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-961 3852,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c2c9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-948 3852,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27404@1" ObjectIDZND0="27403@x" ObjectIDZND1="27405@x" Pin0InfoVect0LinkObjId="SW-176870_0" Pin0InfoVect1LinkObjId="SW-176872_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176871_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3852,-948 3852,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c8e9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-961 3852,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27405@x" ObjectIDND1="27404@x" ObjectIDZND0="27403@0" Pin0InfoVect0LinkObjId="SW-176870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176872_0" Pin1InfoVect1LinkObjId="SW-176871_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3852,-961 3852,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c75b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-1019 3852,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27407@1" ObjectIDZND0="27403@x" ObjectIDZND1="27406@x" Pin0InfoVect0LinkObjId="SW-176870_0" Pin0InfoVect1LinkObjId="SW-176873_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176874_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-1019 3852,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c850f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-1002 3852,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27403@1" ObjectIDZND0="27406@x" ObjectIDZND1="27407@x" Pin0InfoVect0LinkObjId="SW-176873_0" Pin0InfoVect1LinkObjId="SW-176874_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3852,-1002 3852,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c45cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-1019 3852,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27403@x" ObjectIDND1="27407@x" ObjectIDZND0="27406@0" Pin0InfoVect0LinkObjId="SW-176873_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176870_0" Pin1InfoVect1LinkObjId="SW-176874_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3852,-1019 3852,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ac5e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4202,-777 4202,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27413@0" ObjectIDZND0="g_3a49740@0" Pin0InfoVect0LinkObjId="g_3a49740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176936_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4202,-777 4202,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3190950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4202,-873 4202,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27412@1" ObjectIDZND0="27399@0" Pin0InfoVect0LinkObjId="g_3bcb600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176935_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4202,-873 4202,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c9f330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-777 4337,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27415@0" ObjectIDZND0="g_3c772a0@0" Pin0InfoVect0LinkObjId="g_3c772a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176938_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-777 4337,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c06eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-871 4337,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="27414@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176937_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-871 4337,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b145b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4256,-826 4202,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27411@1" ObjectIDZND0="27412@x" ObjectIDZND1="27413@x" Pin0InfoVect0LinkObjId="SW-176935_0" Pin0InfoVect1LinkObjId="SW-176936_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176934_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4256,-826 4202,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3bb4630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4283,-826 4337,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27411@0" ObjectIDZND0="27414@x" ObjectIDZND1="27415@x" Pin0InfoVect0LinkObjId="SW-176937_0" Pin0InfoVect1LinkObjId="SW-176938_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176934_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4283,-826 4337,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b73960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3783,-852 3791,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3ccd3c0@0" ObjectIDZND0="27418@0" Pin0InfoVect0LinkObjId="SW-176956_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ccd3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3783,-852 3791,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3bf5450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3783,-778 3791,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3bef000@0" ObjectIDZND0="27417@0" Pin0InfoVect0LinkObjId="SW-176955_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bef000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3783,-778 3791,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3a49fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-778 3842,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="27417@1" ObjectIDZND0="27416@x" ObjectIDZND1="g_3b1e940@0" ObjectIDZND2="g_3b5a730@0" Pin0InfoVect0LinkObjId="SW-176954_0" Pin0InfoVect1LinkObjId="g_3b1e940_0" Pin0InfoVect2LinkObjId="g_3b5a730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176955_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-778 3842,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c4e640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-791 3842,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="27416@0" ObjectIDZND0="27417@x" ObjectIDZND1="g_3b1e940@0" ObjectIDZND2="g_3b5a730@0" Pin0InfoVect0LinkObjId="SW-176955_0" Pin0InfoVect1LinkObjId="g_3b1e940_0" Pin0InfoVect2LinkObjId="g_3b5a730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176954_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-791 3842,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c29a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-778 3842,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="27417@x" ObjectIDND1="27416@x" ObjectIDND2="g_3b5a730@0" ObjectIDZND0="g_3b1e940@0" Pin0InfoVect0LinkObjId="g_3b1e940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-176955_0" Pin1InfoVect1LinkObjId="SW-176954_0" Pin1InfoVect2LinkObjId="g_3b5a730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-778 3842,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b8a420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-1160 3852,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3dbf080@0" ObjectIDND1="27408@x" ObjectIDND2="27406@x" ObjectIDZND0="28369@1" Pin0InfoVect0LinkObjId="g_3dbfa60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3dbf080_0" Pin1InfoVect1LinkObjId="SW-176875_0" Pin1InfoVect2LinkObjId="SW-176873_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3852,-1160 3852,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b6c6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-852 3842,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="27418@1" ObjectIDZND0="27416@x" ObjectIDZND1="27399@0" Pin0InfoVect0LinkObjId="SW-176954_0" Pin0InfoVect1LinkObjId="g_3190950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-852 3842,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b7fa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-739 3995,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27419@x" ObjectIDND1="27422@x" ObjectIDZND0="27423@1" Pin0InfoVect0LinkObjId="SW-176975_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176971_0" Pin1InfoVect1LinkObjId="SW-176974_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-739 3995,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bc6db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4011,-518 4027,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27499@x" ObjectIDND1="27433@x" ObjectIDZND0="g_3b12940@0" Pin0InfoVect0LinkObjId="g_3b12940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c2a040_0" Pin1InfoVect1LinkObjId="SW-177021_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4011,-518 4027,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3bf2e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-663 3996,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3c45210@0" ObjectIDND1="27499@x" ObjectIDND2="27422@x" ObjectIDZND0="27424@1" Pin0InfoVect0LinkObjId="SW-176976_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c45210_0" Pin1InfoVect1LinkObjId="g_3c2a040_0" Pin1InfoVect2LinkObjId="SW-176974_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-663 3996,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3a3f610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-682 4012,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="27422@0" ObjectIDZND0="g_3c45210@0" ObjectIDZND1="27499@x" ObjectIDZND2="27424@x" Pin0InfoVect0LinkObjId="g_3c45210_0" Pin0InfoVect1LinkObjId="g_3c2a040_0" Pin0InfoVect2LinkObjId="SW-176976_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176974_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-682 4012,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3be1e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-809 3996,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27419@x" ObjectIDND1="27420@x" ObjectIDZND0="27421@1" Pin0InfoVect0LinkObjId="SW-176973_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176971_0" Pin1InfoVect1LinkObjId="SW-176972_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-809 3996,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ca2750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-886 4012,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27399@0" ObjectIDZND0="27420@1" Pin0InfoVect0LinkObjId="SW-176972_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3190950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-886 4012,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3be6150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4011,-538 4011,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="27499@2" ObjectIDZND0="g_3b12940@0" ObjectIDZND1="27433@x" Pin0InfoVect0LinkObjId="g_3b12940_0" Pin0InfoVect1LinkObjId="SW-177021_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c2a040_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4011,-538 4011,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c85b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-739 4012,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27419@x" ObjectIDND1="27423@x" ObjectIDZND0="27422@1" Pin0InfoVect0LinkObjId="SW-176974_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176971_0" Pin1InfoVect1LinkObjId="SW-176975_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-739 4012,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b77f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-761 4012,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27419@0" ObjectIDZND0="27423@x" ObjectIDZND1="27422@x" Pin0InfoVect0LinkObjId="SW-176975_0" Pin0InfoVect1LinkObjId="SW-176974_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176971_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-761 4012,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3a48f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-809 4012,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27421@x" ObjectIDND1="27420@x" ObjectIDZND0="27419@1" Pin0InfoVect0LinkObjId="SW-176971_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176973_0" Pin1InfoVect1LinkObjId="SW-176972_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-809 4012,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3a4caa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-829 4012,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27420@0" ObjectIDZND0="27419@x" ObjectIDZND1="27421@x" Pin0InfoVect0LinkObjId="SW-176971_0" Pin0InfoVect1LinkObjId="SW-176973_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176972_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-829 4012,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3bdcad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-653 4028,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="27424@x" ObjectIDND1="27422@x" ObjectIDND2="27499@x" ObjectIDZND0="g_3c45210@0" Pin0InfoVect0LinkObjId="g_3c45210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-176976_0" Pin1InfoVect1LinkObjId="SW-176974_0" Pin1InfoVect2LinkObjId="g_3c2a040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-653 4028,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c042d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-663 4012,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="27424@x" ObjectIDND1="27422@x" ObjectIDZND0="g_3c45210@0" ObjectIDZND1="27499@x" Pin0InfoVect0LinkObjId="g_3c45210_0" Pin0InfoVect1LinkObjId="g_3c2a040_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176976_0" Pin1InfoVect1LinkObjId="SW-176974_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-663 4012,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c2a040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-652 4012,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="transformer" ObjectIDND0="g_3c45210@0" ObjectIDND1="27424@x" ObjectIDND2="27422@x" ObjectIDZND0="27499@1" Pin0InfoVect0LinkObjId="g_3b31940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c45210_0" Pin1InfoVect1LinkObjId="SW-176976_0" Pin1InfoVect2LinkObjId="SW-176974_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-652 4012,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b21d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-663 3947,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27424@0" ObjectIDZND0="g_3c8dcc0@0" Pin0InfoVect0LinkObjId="g_3c8dcc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176976_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-663 3947,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c2f690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-1088 3792,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27408@0" ObjectIDZND0="g_3bd1060@0" Pin0InfoVect0LinkObjId="g_3bd1060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-1088 3792,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b388f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-1019 3792,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27407@0" ObjectIDZND0="g_3c2cd10@0" Pin0InfoVect0LinkObjId="g_3c2cd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176874_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-1019 3792,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3bb4990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-961 3792,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27405@0" ObjectIDZND0="g_3cc1a80@0" Pin0InfoVect0LinkObjId="g_3cc1a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-961 3792,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3bcb600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-912 3852,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27404@0" ObjectIDZND0="27399@0" Pin0InfoVect0LinkObjId="g_3190950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176871_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3852,-912 3852,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b3de90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-809 3959,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3b77be0@0" ObjectIDZND0="27421@0" Pin0InfoVect0LinkObjId="SW-176973_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b77be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-809 3959,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b48590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-739 3958,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3c6f6b0@0" ObjectIDZND0="27423@0" Pin0InfoVect0LinkObjId="SW-176975_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c6f6b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-739 3958,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c9b520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-778 3890,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="27417@x" ObjectIDND1="27416@x" ObjectIDND2="g_3b1e940@0" ObjectIDZND0="g_3b5a730@0" Pin0InfoVect0LinkObjId="g_3b5a730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-176955_0" Pin1InfoVect1LinkObjId="SW-176954_0" Pin1InfoVect2LinkObjId="g_3b1e940_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-778 3890,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c87aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4531,-528 4531,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27429@0" ObjectIDZND0="g_3ba2e40@0" Pin0InfoVect0LinkObjId="g_3ba2e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4531,-528 4531,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c47160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4202,-837 4202,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27412@0" ObjectIDZND0="27413@x" ObjectIDZND1="27411@x" Pin0InfoVect0LinkObjId="SW-176936_0" Pin0InfoVect1LinkObjId="SW-176934_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176935_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4202,-837 4202,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3bae330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4202,-826 4202,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="27412@x" ObjectIDND1="27411@x" ObjectIDZND0="27413@1" Pin0InfoVect0LinkObjId="SW-176936_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176935_0" Pin1InfoVect1LinkObjId="SW-176934_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4202,-826 4202,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b36690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-835 4337,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27414@0" ObjectIDZND0="27415@x" ObjectIDZND1="27411@x" Pin0InfoVect0LinkObjId="SW-176938_0" Pin0InfoVect1LinkObjId="SW-176934_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176937_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-835 4337,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ac0230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-826 4337,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="27414@x" ObjectIDND1="27411@x" ObjectIDZND0="27415@1" Pin0InfoVect0LinkObjId="SW-176938_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176937_0" Pin1InfoVect1LinkObjId="SW-176934_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-826 4337,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3bdda20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-961 4142,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27410@1" ObjectIDZND0="27409@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-176932_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176933_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-961 4142,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c66370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-948 4142,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27409@1" ObjectIDZND0="27410@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-176933_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-948 4142,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_31e17d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-961 4142,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27409@x" ObjectIDND1="27410@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176932_0" Pin1InfoVect1LinkObjId="SW-176933_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-961 4142,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c49740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-1018 4142,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-1018 4142,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c719f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-1088 4142,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3c5f990@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3c5f990_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-1088 4142,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c89f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-1002 4142,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-1002 4142,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bcabb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-1018 4142,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-1018 4142,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bf1050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-1071 4142,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3c5f990@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3c5f990_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-1071 4142,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c578e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-1088 4082,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3c04830@0" Pin0InfoVect0LinkObjId="g_3c04830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-1088 4082,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c57ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-1018 4082,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3a29740@0" Pin0InfoVect0LinkObjId="g_3a29740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-1018 4082,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3c04600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-961 4082,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27410@0" ObjectIDZND0="g_3c8ec00@0" Pin0InfoVect0LinkObjId="g_3c8ec00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176933_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-961 4082,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b2e590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-912 4142,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27409@0" ObjectIDZND0="27399@0" Pin0InfoVect0LinkObjId="g_3190950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-912 4142,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3be3aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-1164 4142,-1175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3b75fe0@0" ObjectIDND1="g_3c5f990@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b75fe0_0" Pin1InfoVect1LinkObjId="g_3c5f990_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-1164 4142,-1175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a32eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1164 4142,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3b75fe0@0" ObjectIDZND0="0@1" ObjectIDZND1="g_3c5f990@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="g_3c5f990_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b75fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1164 4142,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bdeae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-1141 4142,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3c5f990@0" ObjectIDZND0="0@1" ObjectIDZND1="g_3b75fe0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="g_3b75fe0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c5f990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-1141 4142,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c59440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-1164 4142,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDND1="g_3b75fe0@0" ObjectIDZND0="g_3c5f990@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3c5f990_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="g_3b75fe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-1164 4142,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c59680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-1141 4142,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3c5f990@0" ObjectIDND1="0@1" ObjectIDND2="g_3b75fe0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c5f990_0" Pin1InfoVect1LinkObjId="SW-0_1" Pin1InfoVect2LinkObjId="g_3b75fe0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-1141 4142,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_31e2f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-886 3842,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27399@0" ObjectIDZND0="27418@x" ObjectIDZND1="27416@x" Pin0InfoVect0LinkObjId="SW-176956_0" Pin0InfoVect1LinkObjId="SW-176954_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3190950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-886 3842,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_31e31f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-852 3842,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="27418@x" ObjectIDND1="27399@0" ObjectIDZND0="27416@1" Pin0InfoVect0LinkObjId="SW-176954_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176956_0" Pin1InfoVect1LinkObjId="g_3190950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-852 3842,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3bf18e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-604 3927,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27499@x" ObjectIDZND0="g_3bc49a0@0" ObjectIDZND1="g_2cfc950@0" Pin0InfoVect0LinkObjId="g_3bc49a0_0" Pin0InfoVect1LinkObjId="g_2cfc950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c2a040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-604 3927,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b59050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-604 3927,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="g_2cfc950@0" ObjectIDND1="27499@x" ObjectIDZND0="g_3bc49a0@0" Pin0InfoVect0LinkObjId="g_3bc49a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cfc950_0" Pin1InfoVect1LinkObjId="g_3c2a040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-604 3927,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3b592b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-604 3904,-604 3904,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="g_3bc49a0@0" ObjectIDND1="27499@x" ObjectIDZND0="g_2cfc950@0" Pin0InfoVect0LinkObjId="g_2cfc950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3bc49a0_0" Pin1InfoVect1LinkObjId="g_3c2a040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-604 3904,-604 3904,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b31940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4201,-506 4123,-506 4050,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="27431@0" ObjectIDZND0="27499@x" Pin0InfoVect0LinkObjId="g_3c2a040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177015_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4201,-506 4123,-506 4050,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aee5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4011,-423 4011,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27432@0" ObjectIDZND0="27434@1" Pin0InfoVect0LinkObjId="SW-177021_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177019_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4011,-423 4011,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b12e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4011,-397 4011,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27434@0" ObjectIDZND0="27401@0" Pin0InfoVect0LinkObjId="g_3db83a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177021_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4011,-397 4011,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b13070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4011,-517 4011,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_3b12940@0" ObjectIDND1="27499@x" ObjectIDZND0="27433@0" Pin0InfoVect0LinkObjId="SW-177021_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b12940_0" Pin1InfoVect1LinkObjId="g_3c2a040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4011,-517 4011,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b37e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4011,-458 4011,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27433@1" ObjectIDZND0="27432@1" Pin0InfoVect0LinkObjId="SW-177019_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177021_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4011,-458 4011,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6fe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-488 4289,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3c73270@0" ObjectIDND1="27431@x" ObjectIDZND0="g_3b745d0@0" Pin0InfoVect0LinkObjId="g_3b745d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c73270_0" Pin1InfoVect1LinkObjId="SW-177015_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-488 4289,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c21e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-480 4273,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3c73270@0" ObjectIDZND0="g_3b745d0@0" ObjectIDZND1="27431@x" Pin0InfoVect0LinkObjId="g_3b745d0_0" Pin0InfoVect1LinkObjId="SW-177015_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c73270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-480 4273,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1a9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-488 4273,-506 4237,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3b745d0@0" ObjectIDND1="g_3c73270@0" ObjectIDZND0="27431@1" Pin0InfoVect0LinkObjId="SW-177015_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b745d0_0" Pin1InfoVect1LinkObjId="g_3c73270_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-488 4273,-506 4237,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1ac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-580 4717,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27425@0" ObjectIDZND0="27427@0" Pin0InfoVect0LinkObjId="SW-177001_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176999_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-580 4717,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf4f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-580 4771,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27427@1" ObjectIDZND0="27400@0" Pin0InfoVect0LinkObjId="g_3ac5840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177001_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-580 4771,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf5160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-580 4682,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27426@1" ObjectIDZND0="27425@1" Pin0InfoVect0LinkObjId="SW-176999_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177001_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-580 4682,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ac5840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-950 4771,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27437@0" ObjectIDZND0="27400@0" Pin0InfoVect0LinkObjId="g_3bf4f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-950 4771,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ac5aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-950 4806,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27436@1" ObjectIDZND0="27437@1" Pin0InfoVect0LinkObjId="SW-177170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177169_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-950 4806,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b70740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-950 4843,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27438@0" ObjectIDZND0="27436@0" Pin0InfoVect0LinkObjId="SW-177169_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-950 4843,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b37200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-950 4868,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="27439@x" ObjectIDND1="g_3b85ee0@0" ObjectIDND2="27435@x" ObjectIDZND0="27438@1" Pin0InfoVect0LinkObjId="SW-177170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177171_0" Pin1InfoVect1LinkObjId="g_3b85ee0_0" Pin1InfoVect2LinkObjId="SW-177159_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-950 4868,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b37460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-898 4893,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3bc3b10@0" ObjectIDZND0="27439@0" Pin0InfoVect0LinkObjId="SW-177171_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bc3b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-898 4893,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca05d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-942 4893,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="27439@1" ObjectIDZND0="27438@x" ObjectIDZND1="g_3b85ee0@0" ObjectIDZND2="27435@x" Pin0InfoVect0LinkObjId="SW-177170_0" Pin0InfoVect1LinkObjId="g_3b85ee0_0" Pin0InfoVect2LinkObjId="SW-177159_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177171_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-942 4893,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca0970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4978,-977 4893,-977 4893,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b85ee0@0" ObjectIDZND0="27438@x" ObjectIDZND1="27439@x" ObjectIDZND2="27435@x" Pin0InfoVect0LinkObjId="SW-177170_0" Pin0InfoVect1LinkObjId="SW-177171_0" Pin0InfoVect2LinkObjId="SW-177159_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b85ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4978,-977 4893,-977 4893,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c20840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4970,-950 4893,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="27435@0" ObjectIDZND0="27438@x" ObjectIDZND1="27439@x" ObjectIDZND2="g_3b85ee0@0" Pin0InfoVect0LinkObjId="SW-177170_0" Pin0InfoVect1LinkObjId="SW-177171_0" Pin0InfoVect2LinkObjId="g_3b85ee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177159_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4970,-950 4893,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c6bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5024,-968 5024,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_3c2d9f0@0" ObjectIDZND0="27435@x" ObjectIDZND1="0@x" ObjectIDZND2="38057@1" Pin0InfoVect0LinkObjId="SW-177159_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c2d9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5024,-968 5024,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bfa990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5094,-950 5024,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38057@1" ObjectIDZND0="g_3c2d9f0@0" ObjectIDZND1="27435@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3c2d9f0_0" Pin0InfoVect1LinkObjId="SW-177159_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5094,-950 5024,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bfabd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5024,-950 5006,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3c2d9f0@0" ObjectIDND1="0@x" ObjectIDND2="38057@1" ObjectIDZND0="27435@1" Pin0InfoVect0LinkObjId="SW-177159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c2d9f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5024,-950 5006,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c6e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-892 5093,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-892 5093,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c6e710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5024,-951 5024,-892 5034,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3c2d9f0@0" ObjectIDND1="27435@x" ObjectIDND2="38057@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c2d9f0_0" Pin1InfoVect1LinkObjId="SW-177159_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5024,-951 5024,-892 5034,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3baf110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-831 4771,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27442@0" ObjectIDZND0="27400@0" Pin0InfoVect0LinkObjId="g_3bf4f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-831 4771,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3f130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-831 4806,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27441@1" ObjectIDZND0="27442@1" Pin0InfoVect0LinkObjId="SW-177193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177192_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-831 4806,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3f390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-831 4843,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27443@0" ObjectIDZND0="27441@0" Pin0InfoVect0LinkObjId="SW-177192_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-831 4843,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c06900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-831 4868,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27444@x" ObjectIDND1="27440@x" ObjectIDZND0="27443@1" Pin0InfoVect0LinkObjId="SW-177193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177194_0" Pin1InfoVect1LinkObjId="SW-177182_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-831 4868,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bdc050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-779 4893,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3c3e270@0" ObjectIDZND0="27444@0" Pin0InfoVect0LinkObjId="SW-177194_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3e270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-779 4893,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bdc2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-823 4893,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27444@1" ObjectIDZND0="27443@x" ObjectIDZND1="27440@x" Pin0InfoVect0LinkObjId="SW-177193_0" Pin0InfoVect1LinkObjId="SW-177182_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177194_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-823 4893,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3be4f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4970,-831 4893,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27440@0" ObjectIDZND0="27444@x" ObjectIDZND1="27443@x" Pin0InfoVect0LinkObjId="SW-177194_0" Pin0InfoVect1LinkObjId="SW-177193_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4970,-831 4893,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf3360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-831 5024,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="27440@1" ObjectIDZND0="g_3be51a0@0" ObjectIDZND1="0@x" ObjectIDZND2="48297@1" Pin0InfoVect0LinkObjId="g_3be51a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177182_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-831 5024,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c49ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5024,-831 5024,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="27440@x" ObjectIDND1="0@x" ObjectIDND2="48297@1" ObjectIDZND0="g_3be51a0@0" Pin0InfoVect0LinkObjId="g_3be51a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177182_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5024,-831 5024,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ba6630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-726 4771,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27447@0" ObjectIDZND0="27400@0" Pin0InfoVect0LinkObjId="g_3bf4f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-726 4771,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3be46c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-726 4806,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27446@1" ObjectIDZND0="27447@1" Pin0InfoVect0LinkObjId="SW-177214_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177213_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-726 4806,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3be4920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-726 4843,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27448@0" ObjectIDZND0="27446@0" Pin0InfoVect0LinkObjId="SW-177213_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-726 4843,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c76090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-726 4868,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27449@x" ObjectIDND1="27445@x" ObjectIDZND0="27448@1" Pin0InfoVect0LinkObjId="SW-177214_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177215_0" Pin1InfoVect1LinkObjId="SW-177203_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-726 4868,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c762f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-674 4893,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3b42b60@0" ObjectIDZND0="27449@0" Pin0InfoVect0LinkObjId="SW-177215_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b42b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-674 4893,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c7bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-718 4893,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27449@1" ObjectIDZND0="27448@x" ObjectIDZND1="27445@x" Pin0InfoVect0LinkObjId="SW-177214_0" Pin0InfoVect1LinkObjId="SW-177203_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177215_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-718 4893,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b565a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4970,-726 4893,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27445@0" ObjectIDZND0="27449@x" ObjectIDZND1="27448@x" Pin0InfoVect0LinkObjId="SW-177215_0" Pin0InfoVect1LinkObjId="SW-177214_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4970,-726 4893,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b425b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-726 5024,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="27445@1" ObjectIDZND0="g_3b56800@0" ObjectIDZND1="38062@1" Pin0InfoVect0LinkObjId="g_3b56800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177203_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-726 5024,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b433d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5137,-726 5024,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="38062@1" ObjectIDZND0="27445@x" ObjectIDZND1="g_3b56800@0" Pin0InfoVect0LinkObjId="SW-177203_0" Pin0InfoVect1LinkObjId="g_3b56800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5137,-726 5024,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b43630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5024,-726 5024,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="27445@x" ObjectIDND1="38062@1" ObjectIDZND0="g_3b56800@0" Pin0InfoVect0LinkObjId="g_3b56800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5024,-726 5024,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca9780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-625 4771,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27452@0" ObjectIDZND0="27400@0" Pin0InfoVect0LinkObjId="g_3bf4f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-625 4771,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca99e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-625 4806,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27451@1" ObjectIDZND0="27452@1" Pin0InfoVect0LinkObjId="SW-177235_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-625 4806,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b4b570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-625 4843,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27453@0" ObjectIDZND0="27451@0" Pin0InfoVect0LinkObjId="SW-177234_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-625 4843,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7e7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-625 4868,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27454@x" ObjectIDND1="27450@x" ObjectIDZND0="27453@1" Pin0InfoVect0LinkObjId="SW-177235_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177236_0" Pin1InfoVect1LinkObjId="SW-177224_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-625 4868,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-573 4893,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3b9ef40@0" ObjectIDZND0="27454@0" Pin0InfoVect0LinkObjId="SW-177236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b9ef40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-573 4893,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7ec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-617 4893,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27454@1" ObjectIDZND0="27453@x" ObjectIDZND1="27450@x" Pin0InfoVect0LinkObjId="SW-177235_0" Pin0InfoVect1LinkObjId="SW-177224_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-617 4893,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf7970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4970,-625 4893,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27450@0" ObjectIDZND0="27454@x" ObjectIDZND1="27453@x" Pin0InfoVect0LinkObjId="SW-177236_0" Pin0InfoVect1LinkObjId="SW-177235_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177224_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4970,-625 4893,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5dfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-625 5024,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="27450@1" ObjectIDZND0="g_3bf7bd0@0" ObjectIDZND1="38063@1" Pin0InfoVect0LinkObjId="g_3bf7bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177224_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-625 5024,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf0560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5137,-625 5024,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="38063@1" ObjectIDZND0="27450@x" ObjectIDZND1="g_3bf7bd0@0" Pin0InfoVect0LinkObjId="SW-177224_0" Pin0InfoVect1LinkObjId="g_3bf7bd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5137,-625 5024,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf07a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5024,-625 5024,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="27450@x" ObjectIDND1="38063@1" ObjectIDZND0="g_3bf7bd0@0" Pin0InfoVect0LinkObjId="g_3bf7bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177224_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5024,-625 5024,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ba1090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-195 3742,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3c714f0@0" ObjectIDZND0="g_31e63c0@0" Pin0InfoVect0LinkObjId="g_31e63c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c714f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-195 3742,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9a8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4762,-902 4771,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27456@1" ObjectIDZND0="27400@0" Pin0InfoVect0LinkObjId="g_3bf4f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4762,-902 4771,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b71120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-871 4702,-871 4702,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_3c9b220@0" ObjectIDZND0="g_3be0e00@0" ObjectIDZND1="27455@x" Pin0InfoVect0LinkObjId="g_3be0e00_0" Pin0InfoVect1LinkObjId="SW-177239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c9b220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-871 4702,-871 4702,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c71030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-902 4702,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3be0e00@0" ObjectIDZND0="g_3c9b220@0" ObjectIDZND1="27455@x" Pin0InfoVect0LinkObjId="g_3c9b220_0" Pin0InfoVect1LinkObjId="SW-177239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3be0e00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-902 4702,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c71290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-902 4717,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_3c9b220@0" ObjectIDND1="g_3be0e00@0" ObjectIDZND0="27455@0" Pin0InfoVect0LinkObjId="SW-177239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c9b220_0" Pin1InfoVect1LinkObjId="g_3be0e00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-902 4717,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c1de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-271 3874,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27459@0" ObjectIDZND0="27461@1" Pin0InfoVect0LinkObjId="SW-177270_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-271 3874,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b72530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-348 3874,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27401@0" ObjectIDZND0="27460@0" Pin0InfoVect0LinkObjId="SW-177270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b12e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-348 3874,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b72760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-306 3874,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27460@1" ObjectIDZND0="27459@1" Pin0InfoVect0LinkObjId="SW-177269_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-306 3874,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c4df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-224 3831,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3b729c0@0" ObjectIDZND0="27462@0" Pin0InfoVect0LinkObjId="SW-177271_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b729c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-224 3831,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b6be90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-224 3874,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27462@1" ObjectIDZND0="27461@x" ObjectIDZND1="g_3aab1b0@0" ObjectIDZND2="27463@x" Pin0InfoVect0LinkObjId="SW-177270_0" Pin0InfoVect1LinkObjId="g_3aab1b0_0" Pin0InfoVect2LinkObjId="SW-177275_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-224 3874,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b6c0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-224 3874,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="27462@x" ObjectIDND1="g_3aab1b0@0" ObjectIDND2="27463@x" ObjectIDZND0="27461@0" Pin0InfoVect0LinkObjId="SW-177270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177271_0" Pin1InfoVect1LinkObjId="g_3aab1b0_0" Pin1InfoVect2LinkObjId="SW-177275_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-224 3874,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aabee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-215 3874,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3aab1b0@0" ObjectIDZND0="27462@x" ObjectIDZND1="27461@x" ObjectIDZND2="27463@x" Pin0InfoVect0LinkObjId="SW-177271_0" Pin0InfoVect1LinkObjId="SW-177270_0" Pin0InfoVect2LinkObjId="SW-177275_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3aab1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-215 3874,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfbfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-224 3874,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="27462@x" ObjectIDND1="27461@x" ObjectIDZND0="g_3aab1b0@0" ObjectIDZND1="27463@x" Pin0InfoVect0LinkObjId="g_3aab1b0_0" Pin0InfoVect1LinkObjId="SW-177275_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177271_0" Pin1InfoVect1LinkObjId="SW-177270_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-224 3874,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b525e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-215 3874,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3aab1b0@0" ObjectIDND1="27462@x" ObjectIDND2="27461@x" ObjectIDZND0="27463@1" Pin0InfoVect0LinkObjId="SW-177275_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3aab1b0_0" Pin1InfoVect1LinkObjId="SW-177271_0" Pin1InfoVect2LinkObjId="SW-177270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-215 3874,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b50c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-90 3820,50 3874,50 3874,40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" ObjectIDZND0="28362@0" Pin0InfoVect0LinkObjId="CB-CX_DuJ.CX_DuJ_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-90 3820,50 3874,50 3874,40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b50eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-80 3844,-80 3844,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="27463@x" ObjectIDND1="28362@x" ObjectIDZND0="27464@0" Pin0InfoVect0LinkObjId="SW-177276_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177275_0" Pin1InfoVect1LinkObjId="CB-CX_DuJ.CX_DuJ_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-80 3844,-80 3844,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac2410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-91 3874,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="27463@0" ObjectIDZND0="28362@x" ObjectIDZND1="27464@x" Pin0InfoVect0LinkObjId="CB-CX_DuJ.CX_DuJ_Cb1_0" Pin0InfoVect1LinkObjId="SW-177276_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177275_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-91 3874,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac2670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-80 3874,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="27463@x" ObjectIDND1="27464@x" ObjectIDZND0="28362@1" Pin0InfoVect0LinkObjId="CB-CX_DuJ.CX_DuJ_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177275_0" Pin1InfoVect1LinkObjId="SW-177276_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-80 3874,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac3db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-135 3844,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3ac28d0@0" ObjectIDZND0="27464@1" Pin0InfoVect0LinkObjId="SW-177276_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ac28d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-135 3844,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cae1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-271 4029,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27465@0" ObjectIDZND0="27467@1" Pin0InfoVect0LinkObjId="SW-177294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-271 4029,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cae440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-348 4029,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27401@0" ObjectIDZND0="27466@0" Pin0InfoVect0LinkObjId="SW-177294_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b12e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-348 4029,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cae6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-306 4029,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27466@1" ObjectIDZND0="27465@1" Pin0InfoVect0LinkObjId="SW-177293_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177294_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-306 4029,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b29b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3978,-224 3986,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3cae900@0" ObjectIDZND0="27468@0" Pin0InfoVect0LinkObjId="SW-177295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cae900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3978,-224 3986,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2a020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4022,-224 4029,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27468@1" ObjectIDZND0="27467@x" ObjectIDZND1="g_3b2a4e0@0" ObjectIDZND2="27469@x" Pin0InfoVect0LinkObjId="SW-177294_0" Pin0InfoVect1LinkObjId="g_3b2a4e0_0" Pin0InfoVect2LinkObjId="SW-177300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4022,-224 4029,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2a280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-224 4029,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="27468@x" ObjectIDND1="g_3b2a4e0@0" ObjectIDND2="27469@x" ObjectIDZND0="27467@0" Pin0InfoVect0LinkObjId="SW-177294_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177295_0" Pin1InfoVect1LinkObjId="g_3b2a4e0_0" Pin1InfoVect2LinkObjId="SW-177300_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-224 4029,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2b210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,-215 4029,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b2a4e0@0" ObjectIDZND0="27467@x" ObjectIDZND1="27468@x" ObjectIDZND2="27469@x" Pin0InfoVect0LinkObjId="SW-177294_0" Pin0InfoVect1LinkObjId="SW-177295_0" Pin0InfoVect2LinkObjId="SW-177300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b2a4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4059,-215 4029,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2b470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-224 4029,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="27467@x" ObjectIDND1="27468@x" ObjectIDZND0="g_3b2a4e0@0" ObjectIDZND1="27469@x" Pin0InfoVect0LinkObjId="g_3b2a4e0_0" Pin0InfoVect1LinkObjId="SW-177300_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177294_0" Pin1InfoVect1LinkObjId="SW-177295_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-224 4029,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c950c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-215 4029,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27467@x" ObjectIDND1="27468@x" ObjectIDND2="g_3b2a4e0@0" ObjectIDZND0="27469@1" Pin0InfoVect0LinkObjId="SW-177300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177294_0" Pin1InfoVect1LinkObjId="SW-177295_0" Pin1InfoVect2LinkObjId="g_3b2a4e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-215 4029,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c50b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-90 3975,50 4029,50 4029,40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" ObjectIDZND0="28363@0" Pin0InfoVect0LinkObjId="CB-CX_DuJ.CX_DuJ_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-90 3975,50 4029,50 4029,40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c50db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-80 3999,-80 3999,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="27469@x" ObjectIDND1="28363@x" ObjectIDZND0="27470@0" Pin0InfoVect0LinkObjId="SW-177301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177300_0" Pin1InfoVect1LinkObjId="CB-CX_DuJ.CX_DuJ_Cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-80 3999,-80 3999,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c51010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-91 4029,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="27469@0" ObjectIDZND0="27470@x" ObjectIDZND1="28363@x" Pin0InfoVect0LinkObjId="SW-177301_0" Pin0InfoVect1LinkObjId="CB-CX_DuJ.CX_DuJ_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-91 4029,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c51270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-80 4029,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="27469@x" ObjectIDND1="27470@x" ObjectIDZND0="28363@1" Pin0InfoVect0LinkObjId="CB-CX_DuJ.CX_DuJ_Cb2_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177300_0" Pin1InfoVect1LinkObjId="SW-177301_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-80 4029,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c529f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-135 3999,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3c514d0@0" ObjectIDZND0="27470@1" Pin0InfoVect0LinkObjId="SW-177301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c514d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-135 3999,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c52de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-349 4134,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27401@0" ObjectIDZND0="27496@0" Pin0InfoVect0LinkObjId="SW-177431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b12e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-349 4134,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b8c8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-271 4240,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27471@0" ObjectIDZND0="27473@1" Pin0InfoVect0LinkObjId="SW-177321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177317_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-271 4240,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b8cb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-348 4240,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27401@0" ObjectIDZND0="27472@0" Pin0InfoVect0LinkObjId="SW-177321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b12e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-348 4240,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b8cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-306 4240,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27472@1" ObjectIDZND0="27471@1" Pin0InfoVect0LinkObjId="SW-177317_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177321_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-306 4240,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b8ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4189,-224 4197,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3b8cfe0@0" ObjectIDZND0="27474@0" Pin0InfoVect0LinkObjId="SW-177322_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b8cfe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4189,-224 4197,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b90520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4233,-224 4240,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="27474@1" ObjectIDZND0="27473@x" ObjectIDZND1="g_3b909e0@0" ObjectIDZND2="34459@x" Pin0InfoVect0LinkObjId="SW-177321_0" Pin0InfoVect1LinkObjId="g_3b909e0_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.035Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177322_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4233,-224 4240,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b90780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-224 4240,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="27474@x" ObjectIDND1="g_3b909e0@0" ObjectIDND2="34459@x" ObjectIDZND0="27473@0" Pin0InfoVect0LinkObjId="SW-177321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177322_0" Pin1InfoVect1LinkObjId="g_3b909e0_0" Pin1InfoVect2LinkObjId="EC-CX_DuJ.035Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-224 4240,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a56ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4270,-215 4240,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_3b909e0@0" ObjectIDZND0="27474@x" ObjectIDZND1="27473@x" ObjectIDZND2="34459@x" Pin0InfoVect0LinkObjId="SW-177322_0" Pin0InfoVect1LinkObjId="SW-177321_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.035Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b909e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4270,-215 4240,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a57120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-224 4240,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27474@x" ObjectIDND1="27473@x" ObjectIDZND0="g_3b909e0@0" ObjectIDZND1="34459@x" Pin0InfoVect0LinkObjId="g_3b909e0_0" Pin0InfoVect1LinkObjId="EC-CX_DuJ.035Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177322_0" Pin1InfoVect1LinkObjId="SW-177321_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-224 4240,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a57380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-215 4240,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_3b909e0@0" ObjectIDND1="27474@x" ObjectIDND2="27473@x" ObjectIDZND0="34459@0" Pin0InfoVect0LinkObjId="EC-CX_DuJ.035Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b909e0_0" Pin1InfoVect1LinkObjId="SW-177322_0" Pin1InfoVect2LinkObjId="SW-177321_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-215 4240,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ef6a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-271 4362,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27475@0" ObjectIDZND0="27477@1" Pin0InfoVect0LinkObjId="SW-177344_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-271 4362,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ef6c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-348 4362,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27401@0" ObjectIDZND0="27476@0" Pin0InfoVect0LinkObjId="SW-177344_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b12e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-348 4362,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ef6ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-306 4362,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27476@1" ObjectIDZND0="27475@1" Pin0InfoVect0LinkObjId="SW-177340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-306 4362,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3efa100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4311,-224 4319,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3ef7140@0" ObjectIDZND0="27478@0" Pin0InfoVect0LinkObjId="SW-177345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ef7140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4311,-224 4319,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3efa680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4355,-224 4362,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="27478@1" ObjectIDZND0="27477@x" ObjectIDZND1="g_3efab40@0" ObjectIDZND2="34460@x" Pin0InfoVect0LinkObjId="SW-177344_0" Pin0InfoVect1LinkObjId="g_3efab40_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.034Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177345_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4355,-224 4362,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3efa8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-224 4362,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="27478@x" ObjectIDND1="g_3efab40@0" ObjectIDND2="34460@x" ObjectIDZND0="27477@0" Pin0InfoVect0LinkObjId="SW-177344_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177345_0" Pin1InfoVect1LinkObjId="g_3efab40_0" Pin1InfoVect2LinkObjId="EC-CX_DuJ.034Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-224 4362,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3efb870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-215 4362,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_3efab40@0" ObjectIDZND0="27477@x" ObjectIDZND1="27478@x" ObjectIDZND2="34460@x" Pin0InfoVect0LinkObjId="SW-177344_0" Pin0InfoVect1LinkObjId="SW-177345_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.034Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3efab40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-215 4362,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3efbad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-224 4362,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27477@x" ObjectIDND1="27478@x" ObjectIDZND0="g_3efab40@0" ObjectIDZND1="34460@x" Pin0InfoVect0LinkObjId="g_3efab40_0" Pin0InfoVect1LinkObjId="EC-CX_DuJ.034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177344_0" Pin1InfoVect1LinkObjId="SW-177345_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-224 4362,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b2a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-271 4490,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27480@0" ObjectIDZND0="27482@1" Pin0InfoVect0LinkObjId="SW-177368_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177364_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-271 4490,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b2ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-348 4490,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27401@0" ObjectIDZND0="27481@0" Pin0InfoVect0LinkObjId="SW-177368_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b12e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-348 4490,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b2f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-306 4490,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27481@1" ObjectIDZND0="27480@1" Pin0InfoVect0LinkObjId="SW-177364_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177368_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-306 4490,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e1bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4439,-224 4447,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_39b31a0@0" ObjectIDZND0="27483@0" Pin0InfoVect0LinkObjId="SW-177369_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39b31a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4439,-224 4447,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e1c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-224 4490,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="27483@1" ObjectIDZND0="27482@x" ObjectIDZND1="g_3e1c5d0@0" ObjectIDZND2="34461@x" Pin0InfoVect0LinkObjId="SW-177368_0" Pin0InfoVect1LinkObjId="g_3e1c5d0_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.033Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177369_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4483,-224 4490,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e1c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-224 4490,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="27483@x" ObjectIDND1="g_3e1c5d0@0" ObjectIDND2="34461@x" ObjectIDZND0="27482@0" Pin0InfoVect0LinkObjId="SW-177368_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177369_0" Pin1InfoVect1LinkObjId="g_3e1c5d0_0" Pin1InfoVect2LinkObjId="EC-CX_DuJ.033Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-224 4490,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e1d240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-215 4490,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_3e1c5d0@0" ObjectIDZND0="27482@x" ObjectIDZND1="27483@x" ObjectIDZND2="34461@x" Pin0InfoVect0LinkObjId="SW-177368_0" Pin0InfoVect1LinkObjId="SW-177369_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.033Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e1c5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-215 4490,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e1d4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-224 4490,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27482@x" ObjectIDND1="27483@x" ObjectIDZND0="g_3e1c5d0@0" ObjectIDZND1="34461@x" Pin0InfoVect0LinkObjId="g_3e1c5d0_0" Pin0InfoVect1LinkObjId="EC-CX_DuJ.033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177368_0" Pin1InfoVect1LinkObjId="SW-177369_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-224 4490,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e1d700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-215 4490,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="27482@x" ObjectIDND1="27483@x" ObjectIDND2="g_3e1c5d0@0" ObjectIDZND0="34461@0" Pin0InfoVect0LinkObjId="EC-CX_DuJ.033Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177368_0" Pin1InfoVect1LinkObjId="SW-177369_0" Pin1InfoVect2LinkObjId="g_3e1c5d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-215 4490,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e230e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-271 4612,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27484@0" ObjectIDZND0="27486@1" Pin0InfoVect0LinkObjId="SW-177391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-271 4612,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e23340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-348 4612,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27401@0" ObjectIDZND0="27485@0" Pin0InfoVect0LinkObjId="SW-177391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b12e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-348 4612,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e235a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-306 4612,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27485@1" ObjectIDZND0="27484@1" Pin0InfoVect0LinkObjId="SW-177387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-306 4612,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e26540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-224 4569,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e23800@0" ObjectIDZND0="27487@0" Pin0InfoVect0LinkObjId="SW-177392_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e23800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-224 4569,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e26a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-224 4612,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="27487@1" ObjectIDZND0="27486@x" ObjectIDZND1="g_3e26f00@0" ObjectIDZND2="34462@x" Pin0InfoVect0LinkObjId="SW-177391_0" Pin0InfoVect1LinkObjId="g_3e26f00_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.032Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-224 4612,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e26ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-224 4612,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="27487@x" ObjectIDND1="g_3e26f00@0" ObjectIDND2="34462@x" ObjectIDZND0="27486@0" Pin0InfoVect0LinkObjId="SW-177391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177392_0" Pin1InfoVect1LinkObjId="g_3e26f00_0" Pin1InfoVect2LinkObjId="EC-CX_DuJ.032Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-224 4612,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e27bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-215 4612,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_3e26f00@0" ObjectIDZND0="27486@x" ObjectIDZND1="27487@x" ObjectIDZND2="34462@x" Pin0InfoVect0LinkObjId="SW-177391_0" Pin0InfoVect1LinkObjId="SW-177392_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.032Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e26f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-215 4612,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e27e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-224 4612,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27486@x" ObjectIDND1="27487@x" ObjectIDZND0="g_3e26f00@0" ObjectIDZND1="34462@x" Pin0InfoVect0LinkObjId="g_3e26f00_0" Pin0InfoVect1LinkObjId="EC-CX_DuJ.032Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177391_0" Pin1InfoVect1LinkObjId="SW-177392_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-224 4612,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e28070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-215 4612,-1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="27486@x" ObjectIDND1="27487@x" ObjectIDND2="g_3e26f00@0" ObjectIDZND0="34462@0" Pin0InfoVect0LinkObjId="EC-CX_DuJ.032Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177391_0" Pin1InfoVect1LinkObjId="SW-177392_0" Pin1InfoVect2LinkObjId="g_3e26f00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-215 4612,-1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e30e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-271 4737,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27488@0" ObjectIDZND0="27490@1" Pin0InfoVect0LinkObjId="SW-177414_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-271 4737,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e310a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-348 4737,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27401@0" ObjectIDZND0="27489@0" Pin0InfoVect0LinkObjId="SW-177414_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b12e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-348 4737,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e31300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-306 4737,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27489@1" ObjectIDZND0="27488@1" Pin0InfoVect0LinkObjId="SW-177410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-306 4737,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d9a340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4686,-224 4694,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e31560@0" ObjectIDZND0="27491@0" Pin0InfoVect0LinkObjId="SW-177415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e31560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4686,-224 4694,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d9a8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4730,-224 4737,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="27491@1" ObjectIDZND0="27490@x" ObjectIDZND1="g_3d9ad80@0" ObjectIDZND2="34463@x" Pin0InfoVect0LinkObjId="SW-177414_0" Pin0InfoVect1LinkObjId="g_3d9ad80_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.031Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4730,-224 4737,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d9ab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-224 4737,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="27491@x" ObjectIDND1="g_3d9ad80@0" ObjectIDND2="34463@x" ObjectIDZND0="27490@0" Pin0InfoVect0LinkObjId="SW-177414_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177415_0" Pin1InfoVect1LinkObjId="g_3d9ad80_0" Pin1InfoVect2LinkObjId="EC-CX_DuJ.031Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-224 4737,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d9bab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4767,-215 4737,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_3d9ad80@0" ObjectIDZND0="27490@x" ObjectIDZND1="27491@x" ObjectIDZND2="34463@x" Pin0InfoVect0LinkObjId="SW-177414_0" Pin0InfoVect1LinkObjId="SW-177415_0" Pin0InfoVect2LinkObjId="EC-CX_DuJ.031Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d9ad80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4767,-215 4737,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d9bd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-224 4737,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27490@x" ObjectIDND1="27491@x" ObjectIDZND0="g_3d9ad80@0" ObjectIDZND1="34463@x" Pin0InfoVect0LinkObjId="g_3d9ad80_0" Pin0InfoVect1LinkObjId="EC-CX_DuJ.031Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177414_0" Pin1InfoVect1LinkObjId="SW-177415_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-224 4737,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d9bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-215 4737,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="27490@x" ObjectIDND1="27491@x" ObjectIDND2="g_3d9ad80@0" ObjectIDZND0="34463@0" Pin0InfoVect0LinkObjId="EC-CX_DuJ.031Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177414_0" Pin1InfoVect1LinkObjId="SW-177415_0" Pin1InfoVect2LinkObjId="g_3d9ad80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-215 4737,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d9d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-215 4362,-1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="27477@x" ObjectIDND1="27478@x" ObjectIDND2="g_3efab40@0" ObjectIDZND0="34460@0" Pin0InfoVect0LinkObjId="EC-CX_DuJ.034Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177344_0" Pin1InfoVect1LinkObjId="SW-177345_0" Pin1InfoVect2LinkObjId="g_3efab40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-215 4362,-1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da2620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4825,-348 4825,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27401@0" ObjectIDZND0="27494@0" Pin0InfoVect0LinkObjId="SW-177429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b12e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4825,-348 4825,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db1cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-247 3742,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_31e63c0@1" ObjectIDZND0="g_31e69d0@0" ObjectIDZND1="27493@x" Pin0InfoVect0LinkObjId="g_31e69d0_0" Pin0InfoVect1LinkObjId="SW-177425_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31e63c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-247 3742,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db1ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-266 3774,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_31e63c0@0" ObjectIDND1="27493@x" ObjectIDZND0="g_31e69d0@0" Pin0InfoVect0LinkObjId="g_31e69d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31e63c0_0" Pin1InfoVect1LinkObjId="SW-177425_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-266 3774,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db83a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-331 3742,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27492@0" ObjectIDZND0="27401@0" Pin0InfoVect0LinkObjId="g_3b12e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-331 3742,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db8c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-264 3742,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_31e63c0@0" ObjectIDND1="g_31e69d0@0" ObjectIDZND0="27493@0" Pin0InfoVect0LinkObjId="SW-177425_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31e63c0_0" Pin1InfoVect1LinkObjId="g_31e69d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-264 3742,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db8e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-299 3742,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27493@1" ObjectIDZND0="27492@1" Pin0InfoVect0LinkObjId="SW-177425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177425_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-299 3742,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39bef80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-115 4134,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="27497@0" Pin0InfoVect0LinkObjId="SW-177431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-115 4134,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39bf7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-259 4134,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27497@1" ObjectIDZND0="27496@1" Pin0InfoVect0LinkObjId="SW-177431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-259 4134,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d1970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4825,-277 4825,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27495@1" ObjectIDZND0="27494@1" Pin0InfoVect0LinkObjId="SW-177429_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4825,-277 4825,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf5e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4771,-544 4784,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27400@0" ObjectIDZND0="27457@0" Pin0InfoVect0LinkObjId="SW-177252_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bf4f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4771,-544 4784,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf6090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4801,-544 4842,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27457@1" ObjectIDZND0="27458@0" Pin0InfoVect0LinkObjId="SW-177252_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177252_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4801,-544 4842,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf62f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-544 4866,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="27458@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177252_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-544 4866,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cfc970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-902 4745,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27455@1" ObjectIDZND0="27456@0" Pin0InfoVect0LinkObjId="SW-177239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-902 4745,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d14a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-580 4492,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="27499@x" ObjectIDND1="27429@x" ObjectIDND2="27428@x" ObjectIDZND0="g_3bc44d0@0" Pin0InfoVect0LinkObjId="g_3bc44d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c2a040_0" Pin1InfoVect1LinkObjId="SW-177004_0" Pin1InfoVect2LinkObjId="SW-177003_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-580 4492,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d14c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4531,-564 4531,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="27429@1" ObjectIDZND0="g_3bc44d0@0" ObjectIDZND1="27499@x" ObjectIDZND2="27428@x" Pin0InfoVect0LinkObjId="g_3bc44d0_0" Pin0InfoVect1LinkObjId="g_3c2a040_0" Pin0InfoVect2LinkObjId="SW-177003_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4531,-564 4531,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d14ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-528 4631,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27430@0" ObjectIDZND0="g_3d15130@0" Pin0InfoVect0LinkObjId="g_3d15130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-528 4631,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d188b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-564 4631,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27430@1" ObjectIDZND0="27428@x" ObjectIDZND1="27426@x" Pin0InfoVect0LinkObjId="SW-177003_0" Pin0InfoVect1LinkObjId="SW-177001_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-564 4631,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d194d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-580 4492,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="27499@0" ObjectIDZND0="g_3bc44d0@0" ObjectIDZND1="27429@x" ObjectIDZND2="27428@x" Pin0InfoVect0LinkObjId="g_3bc44d0_0" Pin0InfoVect1LinkObjId="SW-177004_0" Pin0InfoVect2LinkObjId="SW-177003_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c2a040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-580 4492,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d19ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-580 4531,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3bc44d0@0" ObjectIDND1="27499@x" ObjectIDZND0="27429@x" ObjectIDZND1="27428@x" Pin0InfoVect0LinkObjId="SW-177004_0" Pin0InfoVect1LinkObjId="SW-177003_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3bc44d0_0" Pin1InfoVect1LinkObjId="g_3c2a040_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-580 4531,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1a0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4531,-580 4576,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="27429@x" ObjectIDND1="g_3bc44d0@0" ObjectIDND2="27499@x" ObjectIDZND0="27428@0" Pin0InfoVect0LinkObjId="SW-177003_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-177004_0" Pin1InfoVect1LinkObjId="g_3bc44d0_0" Pin1InfoVect2LinkObjId="g_3c2a040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4531,-580 4576,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1abb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-580 4631,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27428@1" ObjectIDZND0="27430@x" ObjectIDZND1="27426@x" Pin0InfoVect0LinkObjId="SW-177005_0" Pin0InfoVect1LinkObjId="SW-177001_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-177003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-580 4631,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1ae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-580 4656,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27430@x" ObjectIDND1="27428@x" ObjectIDZND0="27426@0" Pin0InfoVect0LinkObjId="SW-177001_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-177005_0" Pin1InfoVect1LinkObjId="SW-177003_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-580 4656,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbd600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5049,-798 5037,-798 5037,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@1" ObjectIDZND0="27440@x" ObjectIDZND1="g_3be51a0@0" ObjectIDZND2="48297@1" Pin0InfoVect0LinkObjId="SW-177182_0" Pin0InfoVect1LinkObjId="g_3be51a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5049,-798 5037,-798 5037,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbe0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5119,-831 5037,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48297@1" ObjectIDZND0="0@x" ObjectIDZND1="27440@x" ObjectIDZND2="g_3be51a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-177182_0" Pin0InfoVect2LinkObjId="g_3be51a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5119,-831 5037,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbe350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5037,-831 5024,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="48297@1" ObjectIDZND0="27440@x" ObjectIDZND1="g_3be51a0@0" Pin0InfoVect0LinkObjId="SW-177182_0" Pin0InfoVect1LinkObjId="g_3be51a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5037,-831 5024,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dbe5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5101,-798 5120,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3d1f6a0@0" Pin0InfoVect0LinkObjId="g_3d1f6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5101,-798 5120,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dbfa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-1160 3852,-1160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3dbf080@0" ObjectIDZND0="28369@1" ObjectIDZND1="27408@x" ObjectIDZND2="27406@x" Pin0InfoVect0LinkObjId="g_3b8a420_1" Pin0InfoVect1LinkObjId="SW-176875_0" Pin0InfoVect2LinkObjId="SW-176873_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dbf080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-1160 3852,-1160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dbfcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-1088 3852,-1144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="27408@x" ObjectIDND1="27406@x" ObjectIDZND0="g_2eb6710@0" ObjectIDZND1="28369@1" ObjectIDZND2="g_3dbf080@0" Pin0InfoVect0LinkObjId="g_2eb6710_0" Pin0InfoVect1LinkObjId="g_3b8a420_1" Pin0InfoVect2LinkObjId="g_3dbf080_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-176875_0" Pin1InfoVect1LinkObjId="SW-176873_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3852,-1088 3852,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc07b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-1088 3852,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="27408@1" ObjectIDZND0="g_2eb6710@0" ObjectIDZND1="28369@1" ObjectIDZND2="g_3dbf080@0" Pin0InfoVect0LinkObjId="g_2eb6710_0" Pin0InfoVect1LinkObjId="g_3b8a420_1" Pin0InfoVect2LinkObjId="g_3dbf080_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-176875_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-1088 3852,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc0a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-1088 3852,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2eb6710@0" ObjectIDND1="28369@1" ObjectIDND2="g_3dbf080@0" ObjectIDZND0="27406@1" Pin0InfoVect0LinkObjId="SW-176873_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2eb6710_0" Pin1InfoVect1LinkObjId="g_3b8a420_1" Pin1InfoVect2LinkObjId="g_3dbf080_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3852,-1088 3852,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc1500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-1125 3852,-1125 3852,-1144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2eb6710@0" ObjectIDZND0="27408@x" ObjectIDZND1="27406@x" ObjectIDZND2="28369@1" Pin0InfoVect0LinkObjId="SW-176875_0" Pin0InfoVect1LinkObjId="SW-176873_0" Pin0InfoVect2LinkObjId="g_3b8a420_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eb6710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-1125 3852,-1125 3852,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc1760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3852,-1144 3852,-1160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="27408@x" ObjectIDND1="27406@x" ObjectIDND2="g_2eb6710@0" ObjectIDZND0="28369@1" ObjectIDZND1="g_3dbf080@0" Pin0InfoVect0LinkObjId="g_3b8a420_1" Pin0InfoVect1LinkObjId="g_3dbf080_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-176875_0" Pin1InfoVect1LinkObjId="SW-176873_0" Pin1InfoVect2LinkObjId="g_2eb6710_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3852,-1144 3852,-1160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de19a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-243 4927,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40977@1" ObjectIDZND0="40976@1" Pin0InfoVect0LinkObjId="SW-243829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243831_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-243 4927,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de1c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4954,-243 4980,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40976@0" ObjectIDZND0="40978@0" Pin0InfoVect0LinkObjId="SW-243831_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4954,-243 4980,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de20c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5027,-346 5027,-243 4997,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41129@0" ObjectIDZND0="40978@1" Pin0InfoVect0LinkObjId="SW-243831_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e0d0b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5027,-346 5027,-243 4997,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de8220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-263 5105,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40985@0" ObjectIDZND0="40987@1" Pin0InfoVect0LinkObjId="SW-243884_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-263 5105,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3de8480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-298 5105,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40986@1" ObjectIDZND0="40985@1" Pin0InfoVect0LinkObjId="SW-243882_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243884_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-298 5105,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3deb6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5044,-207 5052,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3de86e0@0" ObjectIDZND0="40988@0" Pin0InfoVect0LinkObjId="SW-243885_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3de86e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5044,-207 5052,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3debc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5088,-207 5107,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="40988@1" ObjectIDZND0="g_3debe80@0" ObjectIDZND1="42250@x" ObjectIDZND2="40987@x" Pin0InfoVect0LinkObjId="g_3debe80_0" Pin0InfoVect1LinkObjId="EC-CX_DuJ.051Ld_0" Pin0InfoVect2LinkObjId="SW-243884_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243885_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5088,-207 5107,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3decbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-207 5105,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3debe80@0" ObjectIDZND0="42250@x" ObjectIDZND1="40988@x" ObjectIDZND2="40987@x" Pin0InfoVect0LinkObjId="EC-CX_DuJ.051Ld_0" Pin0InfoVect1LinkObjId="SW-243885_0" Pin0InfoVect2LinkObjId="SW-243884_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3debe80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-207 5105,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dece10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-239 5105,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="40987@0" ObjectIDZND0="g_3debe80@0" ObjectIDZND1="42250@x" ObjectIDZND2="40988@x" Pin0InfoVect0LinkObjId="g_3debe80_0" Pin0InfoVect1LinkObjId="EC-CX_DuJ.051Ld_0" Pin0InfoVect2LinkObjId="SW-243885_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243884_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-239 5105,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ded070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-207 5105,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_3debe80@0" ObjectIDND1="40988@x" ObjectIDND2="40987@x" ObjectIDZND0="42250@0" Pin0InfoVect0LinkObjId="EC-CX_DuJ.051Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3debe80_0" Pin1InfoVect1LinkObjId="SW-243885_0" Pin1InfoVect2LinkObjId="SW-243884_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-207 5105,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3df53a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4885,-243 4825,-243 4825,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40977@0" ObjectIDZND0="27495@0" Pin0InfoVect0LinkObjId="SW-177429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4885,-243 4825,-243 4825,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dfda40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5309,-54 5309,-60 5287,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3df81d0@0" ObjectIDZND0="g_3dfb7e0@0" ObjectIDZND1="g_3dfe160@0" Pin0InfoVect0LinkObjId="g_3dfb7e0_0" Pin0InfoVect1LinkObjId="g_3dfe160_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df81d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5309,-54 5309,-60 5287,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dfdca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5287,-76 5287,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDZND0="g_3df81d0@0" ObjectIDZND1="g_3dfb7e0@0" ObjectIDZND2="g_3dfe160@0" Pin0InfoVect0LinkObjId="g_3df81d0_0" Pin0InfoVect1LinkObjId="g_3dfb7e0_0" Pin0InfoVect2LinkObjId="g_3dfe160_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5287,-76 5287,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dfdf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5287,-60 5287,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3df81d0@0" ObjectIDND1="g_3dfe160@0" ObjectIDZND0="g_3dfb7e0@0" Pin0InfoVect0LinkObjId="g_3dfb7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3df81d0_0" Pin1InfoVect1LinkObjId="g_3dfe160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5287,-60 5287,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dfee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5287,-60 5263,-60 5263,-53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3df81d0@0" ObjectIDND1="g_3dfb7e0@0" ObjectIDZND0="g_3dfe160@0" Pin0InfoVect0LinkObjId="g_3dfe160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3df81d0_0" Pin1InfoVect1LinkObjId="g_3dfb7e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5287,-60 5263,-60 5263,-53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e01940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5230,-147 5310,-147 5310,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="47057@0" Pin0InfoVect0LinkObjId="SW-303821_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5230,-147 5310,-147 5310,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e01ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5310,-78 5310,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="47057@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-303821_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5310,-78 5310,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e0c820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5230,-262 5230,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47053@1" ObjectIDZND0="47054@1" Pin0InfoVect0LinkObjId="SW-303798_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-303797_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5230,-262 5230,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e0d0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-315 5105,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40986@0" ObjectIDZND0="41129@0" Pin0InfoVect0LinkObjId="g_3e142e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-243884_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-315 5105,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e0faf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5230,-213 5230,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47055@1" ObjectIDZND0="47053@0" Pin0InfoVect0LinkObjId="SW-303797_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-303798_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5230,-213 5230,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e0fce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5211,-178 5230,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47056@1" ObjectIDZND0="g_3e02430@0" ObjectIDZND1="47055@x" Pin0InfoVect0LinkObjId="g_3e02430_0" Pin0InfoVect1LinkObjId="SW-303798_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-303799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5211,-178 5230,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e13590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5230,-161 5230,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3e02430@0" ObjectIDZND0="47056@x" ObjectIDZND1="47055@x" Pin0InfoVect0LinkObjId="SW-303799_0" Pin0InfoVect1LinkObjId="SW-303798_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e02430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5230,-161 5230,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e137f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5230,-178 5230,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="47056@x" ObjectIDND1="g_3e02430@0" ObjectIDZND0="47055@0" Pin0InfoVect0LinkObjId="SW-303798_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-303799_0" Pin1InfoVect1LinkObjId="g_3e02430_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5230,-178 5230,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e13a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5175,-178 5167,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="47056@0" ObjectIDZND0="g_3e12270@0" Pin0InfoVect0LinkObjId="g_3e12270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-303799_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5175,-178 5167,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e142e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5230,-315 5230,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47054@0" ObjectIDZND0="41129@0" Pin0InfoVect0LinkObjId="g_3e0d0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-303798_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5230,-315 5230,-347 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27399" cx="4202" cy="-887" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27399" cx="4012" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27399" cx="3852" cy="-887" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27399" cx="4142" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27399" cx="3842" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27400" cx="4771" cy="-580" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27400" cx="4771" cy="-950" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27400" cx="4771" cy="-831" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27400" cx="4771" cy="-726" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27400" cx="4771" cy="-625" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27400" cx="4771" cy="-902" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="4011" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="3874" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="4029" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="4240" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="4362" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="4490" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="4612" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="4737" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="3742" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="4134" cy="-349" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27400" cx="4771" cy="-544" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27401" cx="4825" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41129" cx="5027" cy="-346" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41129" cx="5105" cy="-346" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41129" cx="5230" cy="-347" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-170848" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3416.500000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27218" ObjectName="DYN-CX_DuJ"/>
     <cge:Meas_Ref ObjectId="170848"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a33810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a33810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a33810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,59)">片区有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a33810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a33810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a33810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a33810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a33810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a33810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b95be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c8a720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3719.000000 -371.000000) translate(0,15)">10kVⅠ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3bf2690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 -1167.500000) translate(0,16)">杜鹃变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_31e7c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3411.500000 -1166.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc2f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3793.000000 -731.000000) translate(0,12)">110kV Ⅰ段TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c4bb50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3863.142857 -1226.800000) translate(0,15)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c4bb50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3863.142857 -1226.800000) translate(0,33)">虎</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c4bb50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3863.142857 -1226.800000) translate(0,51)">杜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c4bb50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3863.142857 -1226.800000) translate(0,69)">嘉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c4bb50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3863.142857 -1226.800000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b1e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -701.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b1e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -701.000000) translate(0,33)">SSZ11-40000/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b1e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -701.000000) translate(0,51)">40MVA,100/100/100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b1e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -701.000000) translate(0,69)">YN,yn0,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b1e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -701.000000) translate(0,87)">110±8x1.25%/38.5±2x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b1e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -701.000000) translate(0,105)">Uk1-2%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b1e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -701.000000) translate(0,123)">Uk1-3%=17.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b1e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -701.000000) translate(0,141)">Uk2-3%=6.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3152790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -432.000000) translate(0,12)">35kV 1号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_3b40ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3271.000000 -256.000000) translate(0,16)">4938</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a60420" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4114.142857 -1234.800000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c49f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4966.000000 -1000.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c5f610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5111.000000 -971.000000) translate(0,12)">杜西中线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b575f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -846.000000) translate(0,12)">杜大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b3f090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -741.000000) translate(0,12)">杜八线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b3f530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -641.000000) translate(0,12)">杜三线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c68fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 54.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6a0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.000000 58.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6a730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4087.000000 -12.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d9d550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4219.000000 23.000000) translate(0,12)">中角线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d9dee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 23.000000) translate(0,12)">中洋线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d9e450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 23.000000) translate(0,12)">中山集镇线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d9eff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4585.000000 23.000000) translate(0,12)">中三线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d9f260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4708.000000 23.000000) translate(0,12)">大过口线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da4a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 -996.000000) translate(0,12)">131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da4c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.000000 -954.000000) translate(0,12)">13117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da5010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -937.000000) translate(0,12)">1311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da5460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.500000 -1010.000000) translate(0,12)">13160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da56a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.500000 -1079.000000) translate(0,12)">13167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da58e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3858.500000 -1060.000000) translate(0,12)">1316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da5b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -951.000000) translate(0,12)">13217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da5d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -939.000000) translate(0,12)">1321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da5fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3786.000000 -847.000000) translate(0,12)">11017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da61e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -818.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da6420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.000000 -773.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da6660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.500000 -835.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da68a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -762.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da6ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -692.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da6d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -443.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da8cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -782.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dae070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4019.000000 -854.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dae560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.500000 -708.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dae7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4156.000000 -859.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dae9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.500000 -793.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3daec20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4342.000000 -801.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db09c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -850.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db0eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4348.500000 -860.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db10f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -499.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3691.000000 -876.000000) translate(0,12)">110kVⅠ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3db8600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3756.000000 -314.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b7b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3882.000000 -291.000000) translate(0,12)">038</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b82d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -291.000000) translate(0,12)">037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b8820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3818.000000 -249.000000) translate(0,12)">03860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b8a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.500000 -116.000000) translate(0,12)">0386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b8ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3766.000000 -116.000000) translate(0,12)">03867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b8ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -249.000000) translate(0,12)">03760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b9120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -116.000000) translate(0,12)">03767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b9360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -116.000000) translate(0,12)">0376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39bfa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -291.000000) translate(0,12)">0361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39c0040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4187.000000 -249.000000) translate(0,12)">03567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39c1f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 -291.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39c42b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -291.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39c4a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4300.000000 -249.000000) translate(0,12)">03467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39cace0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -291.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39cb310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -291.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39cb550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -291.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39cb790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4435.000000 -249.000000) translate(0,12)">03367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39cb9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -249.000000) translate(0,12)">03267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39cbc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4682.000000 -249.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39d1bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4833.000000 -301.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39d4750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4538.000000 -553.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39d67a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -604.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39da600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4819.500000 -974.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39daaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4819.500000 -854.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39dc890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4819.000000 -754.000000) translate(0,12)">333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cee2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4819.000000 -648.000000) translate(0,12)">334</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cee910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4970.000000 -940.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ceeb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4900.000000 -931.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ceed90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4900.000000 -809.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ceefd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4970.000000 -853.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cef210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4899.500000 -707.000000) translate(0,12)">33367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cef450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.500000 -752.000000) translate(0,12)">3336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cef690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4899.500000 -602.000000) translate(0,12)">33467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cef8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.500000 -651.000000) translate(0,12)">3346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cf6550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4807.000000 -567.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cfcbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4727.000000 -889.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cfd200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -550.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cfd440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4062.000000 -634.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d183c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -553.000000) translate(0,12)">30160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d18aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -606.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3d1cbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3181.000000 -827.000000) translate(0,17)"> 公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1e1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -159.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1e1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -159.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1f1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -844.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1f1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -844.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1f450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5141.000000 -886.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbe810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5114.000000 -784.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4698.000000 -975.000000) translate(0,12)">35kVⅠ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,16)">1、全站停电检修前应挂“全站停电检修”牌，复电后</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,36)">方可摘除“全站停电检修”牌。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,76)">2、各类间隔工作时，停电完成后应在相应间隔挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,96)">“禁止合闸，有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,136)">3、线路工作时，停电完成后应在相应间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,156)">挂“禁止合闸，线路有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,196)">4、现场工作影响对应间隔四遥信息正确性的，应挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,216)">“禁止刷新”牌，工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,256)">5、现场开展相应间隔四遥信息核对前，应挂“调试一”牌，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3dc19c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -558.000000) translate(0,276)">核对工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3dcf9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3498.000000 -1152.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3dd0970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3498.000000 -1187.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd5c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3645.000000 -1074.000000) translate(0,12)">同期U(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="19" graphid="g_3dd6cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -211.000000) translate(0,16)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="19" graphid="g_3dd6cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -211.000000) translate(0,35)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_3dd9060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -221.500000) translate(0,16)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_3dd9060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -221.500000) translate(0,35)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_3dd9060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -221.500000) translate(0,54)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ded2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 21.000000) translate(0,12)">大过口Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3defc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5123.596330 -284.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df0250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5039.000000 -238.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3df12d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5077.000000 -375.000000) translate(0,15)">10kVⅡ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df1b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -276.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3df69e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3186.000000 -771.000000) translate(0,17)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e01e00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5325.500000 -105.500000) translate(0,12)">0520</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e0ca80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5239.000000 -256.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e0ecc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5286.000000 65.000000) translate(0,12)">1号消弧线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e0ecc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5286.000000 65.000000) translate(0,27)">圈及接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e13cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5177.000000 -204.000000) translate(0,12)">05267</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.048660 -0.000000 0.000000 -1.021739 -1401.024460 -88.880435)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c400c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31c1c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b8b730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da2970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 634.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da3530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 619.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da41b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 604.000000) translate(0,12)">绕组温度(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cfec70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 441.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cff620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 456.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cffb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 470.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cffd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 426.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cfffb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 410.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d001f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 393.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d00f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 968.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d011b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 983.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d013f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 997.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d01630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 953.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d01870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.000000 937.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d01ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3637.000000 920.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d02360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 1058.000000) translate(0,10)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d03b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 1074.000000) translate(0,10)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d04210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4695.000000 1029.000000) translate(0,10)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d04ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.000000 1014.000000) translate(0,10)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d04d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 1000.000000) translate(0,10)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d05920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 1044.000000) translate(0,10)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.950820 -0.000000 0.000000 -0.934783 -764.344262 80.641304)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d06570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d06870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d06ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -429.000000 230.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d074b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d07770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d079b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 -1264.655738 458.880435)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d083b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d08670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d088b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 -1143.655738 923.880435)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0ccf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0cf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 -1004.655738 920.880435)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0d350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0d650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0d890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 -872.655738 922.880435)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0dcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0dfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0e1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 -731.655738 922.880435)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0e610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0e910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0eb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 -603.655738 922.880435)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0f270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0f4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0f7e0" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 3807.229508 -88.836957) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0fa40" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 3781.000000 -74.163043) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0fd70" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 3970.229508 -88.836957) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0ffd0" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 3944.000000 -74.163043) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 121.000000 -65.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d103f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d106f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d10930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 67.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d10d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d11050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d11290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 173.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d116b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d119b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d11bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 131.000000 266.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d12010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d12310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d12550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 -249.655738 921.880435)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df0670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df0970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df0bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.049180 -0.000000 0.000000 -0.978261 -431.655738 666.880435)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df6150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df6420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df6660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3df7c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 1247.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="3869" cy="1240" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-176870">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.638655 -966.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27403" ObjectName="SW-CX_DuJ.CX_DuJ_131BK"/>
     <cge:Meas_Ref ObjectId="176870"/>
    <cge:TPSR_Ref TObjectID="27403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177019">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.613445 -414.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27432" ObjectName="SW-CX_DuJ.CX_DuJ_001BK"/>
     <cge:Meas_Ref ObjectId="177019"/>
    <cge:TPSR_Ref TObjectID="27432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.638655 -966.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176971">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.613445 -753.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27419" ObjectName="SW-CX_DuJ.CX_DuJ_101BK"/>
     <cge:Meas_Ref ObjectId="176971"/>
    <cge:TPSR_Ref TObjectID="27419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176934">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -816.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27411" ObjectName="SW-CX_DuJ.CX_DuJ_112BK"/>
     <cge:Meas_Ref ObjectId="176934"/>
    <cge:TPSR_Ref TObjectID="27411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177293">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27465" ObjectName="SW-CX_DuJ.CX_DuJ_037BK"/>
     <cge:Meas_Ref ObjectId="177293"/>
    <cge:TPSR_Ref TObjectID="27465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27459" ObjectName="SW-CX_DuJ.CX_DuJ_038BK"/>
     <cge:Meas_Ref ObjectId="177269"/>
    <cge:TPSR_Ref TObjectID="27459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177317">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4231.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27471" ObjectName="SW-CX_DuJ.CX_DuJ_035BK"/>
     <cge:Meas_Ref ObjectId="177317"/>
    <cge:TPSR_Ref TObjectID="27471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27475" ObjectName="SW-CX_DuJ.CX_DuJ_034BK"/>
     <cge:Meas_Ref ObjectId="177340"/>
    <cge:TPSR_Ref TObjectID="27475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177364">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27480" ObjectName="SW-CX_DuJ.CX_DuJ_033BK"/>
     <cge:Meas_Ref ObjectId="177364"/>
    <cge:TPSR_Ref TObjectID="27480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27484" ObjectName="SW-CX_DuJ.CX_DuJ_032BK"/>
     <cge:Meas_Ref ObjectId="177387"/>
    <cge:TPSR_Ref TObjectID="27484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177410">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4728.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27488" ObjectName="SW-CX_DuJ.CX_DuJ_031BK"/>
     <cge:Meas_Ref ObjectId="177410"/>
    <cge:TPSR_Ref TObjectID="27488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-176999">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4673.000000 -570.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27425" ObjectName="SW-CX_DuJ.CX_DuJ_301BK"/>
     <cge:Meas_Ref ObjectId="176999"/>
    <cge:TPSR_Ref TObjectID="27425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177169">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -940.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27436" ObjectName="SW-CX_DuJ.CX_DuJ_331BK"/>
     <cge:Meas_Ref ObjectId="177169"/>
    <cge:TPSR_Ref TObjectID="27436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177192">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -821.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27441" ObjectName="SW-CX_DuJ.CX_DuJ_332BK"/>
     <cge:Meas_Ref ObjectId="177192"/>
    <cge:TPSR_Ref TObjectID="27441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177213">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -716.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27446" ObjectName="SW-CX_DuJ.CX_DuJ_333BK"/>
     <cge:Meas_Ref ObjectId="177213"/>
    <cge:TPSR_Ref TObjectID="27446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-177234">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -615.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27451" ObjectName="SW-CX_DuJ.CX_DuJ_334BK"/>
     <cge:Meas_Ref ObjectId="177234"/>
    <cge:TPSR_Ref TObjectID="27451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243829">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.788991 -233.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40976" ObjectName="SW-CX_DuJ.CX_DuJ_012BK"/>
     <cge:Meas_Ref ObjectId="243829"/>
    <cge:TPSR_Ref TObjectID="40976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-243882">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5095.596330 -255.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40985" ObjectName="SW-CX_DuJ.CX_DuJ_051BK"/>
     <cge:Meas_Ref ObjectId="243882"/>
    <cge:TPSR_Ref TObjectID="40985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-303797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5221.000000 -227.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47053" ObjectName="SW-CX_DuJ.CX_DuJ_052BK"/>
     <cge:Meas_Ref ObjectId="303797"/>
    <cge:TPSR_Ref TObjectID="47053"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DuJ.CX_DuJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3712,-349 4858,-349 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27401" ObjectName="BS-CX_DuJ.CX_DuJ_9IM"/>
    <cge:TPSR_Ref TObjectID="27401"/></metadata>
   <polyline fill="none" opacity="0" points="3712,-349 4858,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DuJ.CX_DuJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4772,-978 4772,-519 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27400" ObjectName="BS-CX_DuJ.CX_DuJ_3IM"/>
    <cge:TPSR_Ref TObjectID="27400"/></metadata>
   <polyline fill="none" opacity="0" points="4772,-978 4772,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DuJ.CX_DuJ_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-887 4219,-887 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27399" ObjectName="BS-CX_DuJ.CX_DuJ_1IM"/>
    <cge:TPSR_Ref TObjectID="27399"/></metadata>
   <polyline fill="none" opacity="0" points="3721,-887 4219,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DuJ.CX_DuJ_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4969,-347 5276,-347 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="41129" ObjectName="BS-CX_DuJ.CX_DuJ_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="41129"/></metadata>
   <polyline fill="none" opacity="0" points="4969,-347 5276,-347 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.693878 -0.000000 0.000000 -0.717391 5090.000000 -881.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.693878 -0.000000 0.000000 -0.717391 5090.000000 -881.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -15.000000)" xlink:href="#transformer2:shape77_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -15.000000)" xlink:href="#transformer2:shape77_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3215.000000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-176747" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3283.538462 -943.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176747" ObjectName="CX_DuJ:CX_DuJ_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-176741" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3283.538462 -901.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176741" ObjectName="CX_DuJ:CX_DuJ_131BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-176772" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4240.000000 -635.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176772" ObjectName="CX_DuJ:CX_DuJ_1T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-176768" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4240.000000 -619.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176768" ObjectName="CX_DuJ:CX_DuJ_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-176770" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4240.000000 -603.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176770" ObjectName="CX_DuJ:CX_DuJ_1T_Tmp3"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-176747" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3282.538462 -1026.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176747" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-176747" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3281.538462 -983.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176747" ObjectName="CX_DuJ:CX_DuJ_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-176746" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3732.000000 -1073.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176746" ObjectName="CX_DuJ:CX_DuJ_131BK_U"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3235" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3186" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="43" qtmmishow="hidden" width="73" x="3395" y="-1180"/>
    </a>
   <metadata/><rect fill="white" height="43" opacity="0" stroke="white" transform="" width="73" x="3395" y="-1180"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3863" y="-996"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3863" y="-996"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="24" x="4258" y="-850"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="24" x="4258" y="-850"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4062" y="-634"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4062" y="-634"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3882" y="-291"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3882" y="-291"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4038" y="-291"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4038" y="-291"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4249" y="-291"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4249" y="-291"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4372" y="-291"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4372" y="-291"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4503" y="-291"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4503" y="-291"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4621" y="-291"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4621" y="-291"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4746" y="-292"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4746" y="-292"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4819" y="-974"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4819" y="-974"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4819" y="-854"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4819" y="-854"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4819" y="-754"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4819" y="-754"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4817" y="-649"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4817" y="-649"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="77" x="3193" y="-825"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="77" x="3193" y="-825"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3487" y="-1160"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3487" y="-1160"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3487" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3487" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5123" y="-284"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5123" y="-284"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4926" y="-277"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4926" y="-277"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="23" qtmmishow="hidden" width="84" x="3185" y="-772"/>
    </a>
   <metadata/><rect fill="white" height="23" opacity="0" stroke="white" transform="" width="84" x="3185" y="-772"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5239" y="-256"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5239" y="-256"/></g>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4617" cy="-896" fill="none" r="7.5" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DuJ.CX_DuJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38941"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.613445 -533.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="38943"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.613445 -533.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="38945"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.613445 -533.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27499" ObjectName="TF-CX_DuJ.CX_DuJ_1T"/>
    <cge:TPSR_Ref TObjectID="27499"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3b5a730">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3883.420168 -720.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb6710">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.638655 -1085.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cfc950">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3898.000000 -534.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b12940">
    <use class="BV-10KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 4022.613445 -510.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c45210">
    <use class="BV-110KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 4023.613445 -645.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bc49a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -536.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bc44d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4485.000000 -500.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c5f990">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.638655 -1101.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b75fe0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.638655 -1106.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c73270">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.000000 -432.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b745d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 -480.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c2d9f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5020.000000 -960.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3be51a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5020.000000 -841.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b56800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5020.000000 -736.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bf7bd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5020.000000 -635.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e63c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 -211.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e69d0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3768.000000 -270.400000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c9b220">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.000000 -864.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aab1b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3897.000000 -161.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b2a4e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -161.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b909e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -161.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3efab40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 -161.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e1c5d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 -161.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e26f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.000000 -161.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d9ad80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 -161.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dbf080">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 -1153.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3debe80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5128.000000 -153.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3df81d0">
    <use class="BV-0KV" transform="matrix(-0.750000 0.000000 -0.000000 -0.830189 5319.500000 -14.794737)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dfb7e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 0.578431 5281.500000 3.500000)" xlink:href="#lightningRod:shape201"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dfe160">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5270.500000 1.205263)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e02430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 5217.000000 -102.000000)" xlink:href="#lightningRod:shape152"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/></g>
   <g href="AVC杜鹃站.svg" style="fill-opacity:0"><rect height="43" qtmmishow="hidden" width="73" x="3395" y="-1180"/></g>
   <g href="110kV杜鹃变CX_DuJ_131间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3863" y="-996"/></g>
   <g href="110kV杜鹃变CX_DuJ_112间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="24" x="4258" y="-850"/></g>
   <g href="110kV杜鹃变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4062" y="-634"/></g>
   <g href="110kV杜鹃变CX_DuJ_038间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3882" y="-291"/></g>
   <g href="110kV杜鹃变CX_DuJ_037间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4038" y="-291"/></g>
   <g href="110kV杜鹃变10kV中角线035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4249" y="-291"/></g>
   <g href="110kV杜鹃变10kV中洋线034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4372" y="-291"/></g>
   <g href="110kV杜鹃变10kV中山集镇线033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4503" y="-291"/></g>
   <g href="110kV杜鹃变10kV中三线032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4621" y="-291"/></g>
   <g href="110kV杜鹃变10kV大过口线031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4746" y="-292"/></g>
   <g href="110kV杜鹃变CX_DuJ_331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4819" y="-974"/></g>
   <g href="110kV杜鹃变35kV杜大线332间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4819" y="-854"/></g>
   <g href="110kV杜鹃变35kV杜八线333间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4819" y="-754"/></g>
   <g href="110kV杜鹃变35kV杜三线334间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4817" y="-649"/></g>
   <g href="110kV杜鹃变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="77" x="3193" y="-825"/></g>
   <g href="cx_配调_配网接线图110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3487" y="-1160"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3487" y="-1195"/></g>
   <g href="110kV杜鹃变10kV大过口Ⅱ回线051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5123" y="-284"/></g>
   <g href="110kV杜鹃变CX_DuJ_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4926" y="-277"/></g>
   <g href="110kV杜鹃变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="23" qtmmishow="hidden" width="84" x="3185" y="-772"/></g>
   <g href="110kV杜鹃变CX_DuJ_52间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5239" y="-256"/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-506 4189,-492 4189,-520 4164,-506 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-506 4134,-492 4134,-520 4159,-506 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-950 4957,-940 4957,-960 4939,-950 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-950 4909,-940 4909,-960 4926,-950 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5024,-926 5034,-943 5014,-943 5024,-926 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5024,-922 5034,-905 5014,-905 5024,-922 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-831 4957,-821 4957,-841 4939,-831 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-831 4909,-821 4909,-841 4926,-831 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-726 4957,-716 4957,-736 4939,-726 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-726 4909,-716 4909,-736 4926,-726 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-625 4957,-615 4957,-635 4939,-625 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-625 4909,-615 4909,-635 4926,-625 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-191 3884,-209 3864,-209 3874,-191 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-186 3884,-169 3864,-169 3874,-186 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-191 4039,-209 4019,-209 4029,-191 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-186 4039,-169 4019,-169 4029,-186 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-167 4144,-185 4124,-185 4134,-167 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-162 4144,-145 4124,-145 4134,-162 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-131 4250,-149 4230,-149 4240,-131 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-107 4250,-90 4230,-90 4240,-107 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-131 4372,-149 4352,-149 4362,-131 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-107 4372,-90 4352,-90 4362,-107 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-131 4500,-149 4480,-149 4490,-131 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-107 4500,-90 4480,-90 4490,-107 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-131 4622,-149 4602,-149 4612,-131 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-107 4622,-90 4602,-90 4612,-107 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-131 4747,-149 4727,-149 4737,-131 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-107 4747,-90 4727,-90 4737,-107 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-123 5115,-141 5095,-141 5105,-123 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-99 5115,-82 5095,-82 5105,-99 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5231,-86 5237,-98 5224,-98 5231,-86 5231,-86 5231,-86 " stroke="rgb(0,255,0)"/>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3970.000000 -1013.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27403"/>
     <cge:Term_Ref ObjectID="38746"/>
    <cge:TPSR_Ref TObjectID="27403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3970.000000 -1013.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27403"/>
     <cge:Term_Ref ObjectID="38746"/>
    <cge:TPSR_Ref TObjectID="27403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3970.000000 -1013.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27403"/>
     <cge:Term_Ref ObjectID="38746"/>
    <cge:TPSR_Ref TObjectID="27403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-176777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3688.000000 -995.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27399"/>
     <cge:Term_Ref ObjectID="38715"/>
    <cge:TPSR_Ref TObjectID="27399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-176778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3688.000000 -995.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27399"/>
     <cge:Term_Ref ObjectID="38715"/>
    <cge:TPSR_Ref TObjectID="27399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-176779" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3688.000000 -995.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27399"/>
     <cge:Term_Ref ObjectID="38715"/>
    <cge:TPSR_Ref TObjectID="27399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-176780" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3688.000000 -995.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176780" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27399"/>
     <cge:Term_Ref ObjectID="38715"/>
    <cge:TPSR_Ref TObjectID="27399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-176774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3688.000000 -995.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27399"/>
     <cge:Term_Ref ObjectID="38715"/>
    <cge:TPSR_Ref TObjectID="27399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-176773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3688.000000 -995.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27399"/>
     <cge:Term_Ref ObjectID="38715"/>
    <cge:TPSR_Ref TObjectID="27399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-176793" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -468.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176793" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27401"/>
     <cge:Term_Ref ObjectID="38717"/>
    <cge:TPSR_Ref TObjectID="27401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-176794" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -468.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27401"/>
     <cge:Term_Ref ObjectID="38717"/>
    <cge:TPSR_Ref TObjectID="27401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-176795" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -468.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27401"/>
     <cge:Term_Ref ObjectID="38717"/>
    <cge:TPSR_Ref TObjectID="27401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-176796" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -468.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27401"/>
     <cge:Term_Ref ObjectID="38717"/>
    <cge:TPSR_Ref TObjectID="27401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-176790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -468.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27401"/>
     <cge:Term_Ref ObjectID="38717"/>
    <cge:TPSR_Ref TObjectID="27401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-176789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -468.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27401"/>
     <cge:Term_Ref ObjectID="38717"/>
    <cge:TPSR_Ref TObjectID="27401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-176785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1075.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27400"/>
     <cge:Term_Ref ObjectID="38716"/>
    <cge:TPSR_Ref TObjectID="27400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-176786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1075.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27400"/>
     <cge:Term_Ref ObjectID="38716"/>
    <cge:TPSR_Ref TObjectID="27400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-176787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1075.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27400"/>
     <cge:Term_Ref ObjectID="38716"/>
    <cge:TPSR_Ref TObjectID="27400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-176788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1075.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27400"/>
     <cge:Term_Ref ObjectID="38716"/>
    <cge:TPSR_Ref TObjectID="27400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-176782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1075.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27400"/>
     <cge:Term_Ref ObjectID="38716"/>
    <cge:TPSR_Ref TObjectID="27400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-176781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1075.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27400"/>
     <cge:Term_Ref ObjectID="38716"/>
    <cge:TPSR_Ref TObjectID="27400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176747" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -766.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176747" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27419"/>
     <cge:Term_Ref ObjectID="38778"/>
    <cge:TPSR_Ref TObjectID="27419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -766.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27419"/>
     <cge:Term_Ref ObjectID="38778"/>
    <cge:TPSR_Ref TObjectID="27419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -766.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27419"/>
     <cge:Term_Ref ObjectID="38778"/>
    <cge:TPSR_Ref TObjectID="27419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -671.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27425"/>
     <cge:Term_Ref ObjectID="38790"/>
    <cge:TPSR_Ref TObjectID="27425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176755" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -671.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176755" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27425"/>
     <cge:Term_Ref ObjectID="38790"/>
    <cge:TPSR_Ref TObjectID="27425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -671.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27425"/>
     <cge:Term_Ref ObjectID="38790"/>
    <cge:TPSR_Ref TObjectID="27425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176761" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -424.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176761" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27432"/>
     <cge:Term_Ref ObjectID="38804"/>
    <cge:TPSR_Ref TObjectID="27432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176762" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -424.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27432"/>
     <cge:Term_Ref ObjectID="38804"/>
    <cge:TPSR_Ref TObjectID="27432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -424.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27432"/>
     <cge:Term_Ref ObjectID="38804"/>
    <cge:TPSR_Ref TObjectID="27432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5244.000000 -966.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27436"/>
     <cge:Term_Ref ObjectID="38812"/>
    <cge:TPSR_Ref TObjectID="27436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176803" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5244.000000 -966.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176803" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27436"/>
     <cge:Term_Ref ObjectID="38812"/>
    <cge:TPSR_Ref TObjectID="27436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5244.000000 -966.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27436"/>
     <cge:Term_Ref ObjectID="38812"/>
    <cge:TPSR_Ref TObjectID="27436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176806" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.000000 -834.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176806" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27441"/>
     <cge:Term_Ref ObjectID="38822"/>
    <cge:TPSR_Ref TObjectID="27441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176807" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.000000 -834.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176807" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27441"/>
     <cge:Term_Ref ObjectID="38822"/>
    <cge:TPSR_Ref TObjectID="27441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176805" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.000000 -834.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27441"/>
     <cge:Term_Ref ObjectID="38822"/>
    <cge:TPSR_Ref TObjectID="27441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176810" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.000000 -729.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176810" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27446"/>
     <cge:Term_Ref ObjectID="38832"/>
    <cge:TPSR_Ref TObjectID="27446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.000000 -729.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27446"/>
     <cge:Term_Ref ObjectID="38832"/>
    <cge:TPSR_Ref TObjectID="27446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176809" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.000000 -729.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176809" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27446"/>
     <cge:Term_Ref ObjectID="38832"/>
    <cge:TPSR_Ref TObjectID="27446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.500000 -634.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27451"/>
     <cge:Term_Ref ObjectID="38842"/>
    <cge:TPSR_Ref TObjectID="27451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176815" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.500000 -634.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27451"/>
     <cge:Term_Ref ObjectID="38842"/>
    <cge:TPSR_Ref TObjectID="27451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176813" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5254.500000 -634.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176813" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27451"/>
     <cge:Term_Ref ObjectID="38842"/>
    <cge:TPSR_Ref TObjectID="27451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176818" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3846.000000 75.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176818" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27459"/>
     <cge:Term_Ref ObjectID="38858"/>
    <cge:TPSR_Ref TObjectID="27459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3846.000000 75.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27459"/>
     <cge:Term_Ref ObjectID="38858"/>
    <cge:TPSR_Ref TObjectID="27459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176821" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 75.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27465"/>
     <cge:Term_Ref ObjectID="38870"/>
    <cge:TPSR_Ref TObjectID="27465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176820" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 75.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176820" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27465"/>
     <cge:Term_Ref ObjectID="38870"/>
    <cge:TPSR_Ref TObjectID="27465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176824" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4228.000000 42.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27471"/>
     <cge:Term_Ref ObjectID="38882"/>
    <cge:TPSR_Ref TObjectID="27471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176825" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4228.000000 42.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27471"/>
     <cge:Term_Ref ObjectID="38882"/>
    <cge:TPSR_Ref TObjectID="27471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4228.000000 42.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27471"/>
     <cge:Term_Ref ObjectID="38882"/>
    <cge:TPSR_Ref TObjectID="27471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176828" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4366.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27475"/>
     <cge:Term_Ref ObjectID="38890"/>
    <cge:TPSR_Ref TObjectID="27475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176829" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4366.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176829" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27475"/>
     <cge:Term_Ref ObjectID="38890"/>
    <cge:TPSR_Ref TObjectID="27475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176827" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4366.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27475"/>
     <cge:Term_Ref ObjectID="38890"/>
    <cge:TPSR_Ref TObjectID="27475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176832" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 40.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176832" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27480"/>
     <cge:Term_Ref ObjectID="38900"/>
    <cge:TPSR_Ref TObjectID="27480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176833" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 40.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27480"/>
     <cge:Term_Ref ObjectID="38900"/>
    <cge:TPSR_Ref TObjectID="27480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176831" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 40.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27480"/>
     <cge:Term_Ref ObjectID="38900"/>
    <cge:TPSR_Ref TObjectID="27480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176836" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4641.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176836" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27484"/>
     <cge:Term_Ref ObjectID="38908"/>
    <cge:TPSR_Ref TObjectID="27484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176837" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4641.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176837" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27484"/>
     <cge:Term_Ref ObjectID="38908"/>
    <cge:TPSR_Ref TObjectID="27484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176835" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4641.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176835" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27484"/>
     <cge:Term_Ref ObjectID="38908"/>
    <cge:TPSR_Ref TObjectID="27484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-176840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27488"/>
     <cge:Term_Ref ObjectID="38916"/>
    <cge:TPSR_Ref TObjectID="27488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-176841" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176841" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27488"/>
     <cge:Term_Ref ObjectID="38916"/>
    <cge:TPSR_Ref TObjectID="27488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-176839" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176839" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27488"/>
     <cge:Term_Ref ObjectID="38916"/>
    <cge:TPSR_Ref TObjectID="27488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-243918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5132.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40985"/>
     <cge:Term_Ref ObjectID="62088"/>
    <cge:TPSR_Ref TObjectID="40985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5132.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40985"/>
     <cge:Term_Ref ObjectID="62088"/>
    <cge:TPSR_Ref TObjectID="40985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5132.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40985"/>
     <cge:Term_Ref ObjectID="62088"/>
    <cge:TPSR_Ref TObjectID="40985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-243856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4945.000000 -212.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40976"/>
     <cge:Term_Ref ObjectID="62072"/>
    <cge:TPSR_Ref TObjectID="40976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-243857" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4945.000000 -212.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40976"/>
     <cge:Term_Ref ObjectID="62072"/>
    <cge:TPSR_Ref TObjectID="40976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-243864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4945.000000 -212.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="243864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40976"/>
     <cge:Term_Ref ObjectID="62072"/>
    <cge:TPSR_Ref TObjectID="40976"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_DuJ.CX_DuJ_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 45.000000)" xlink:href="#capacitor:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28362" ObjectName="CB-CX_DuJ.CX_DuJ_Cb1"/>
    <cge:TPSR_Ref TObjectID="28362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_DuJ.CX_DuJ_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 45.000000)" xlink:href="#capacitor:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28363" ObjectName="CB-CX_DuJ.CX_DuJ_Cb2"/>
    <cge:TPSR_Ref TObjectID="28363"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DuJ"/>
</svg>