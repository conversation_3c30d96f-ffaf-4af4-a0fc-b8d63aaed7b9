<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-115" aopId="2885374" id="thSvg" product="E8000V2" version="1.0" viewBox="3116 -1234 2712 1298">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="52" x2="52" y1="45" y2="16"/>
    <polyline arcFlag="1" points="25,101 23,101 21,100 20,100 18,99 17,98 15,97 14,95 13,93 13,92 12,90 12,88 12,86 13,84 13,83 14,81 15,80 17,78 18,77 20,76 21,76 23,75 25,75 27,75 29,76 30,76 32,77 33,78 35,80 36,81 37,83 37,84 38,86 38,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="38" x2="26" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="100" y2="108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="54" y2="47"/>
    <polyline arcFlag="1" points="43,15 44,15 45,15 45,15 46,15 46,16 47,16 47,17 48,17 48,18 48,18 48,19 49,20 49,20 49,21 48,22 48,22 48,23 48,23 47,24 47,24 46,25 46,25 45,25 45,26 44,26 43,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,25 44,25 45,25 45,25 46,26 46,26 47,26 47,27 48,27 48,28 48,29 48,29 49,30 49,31 49,31 48,32 48,33 48,33 48,34 47,34 47,35 46,35 46,35 45,36 45,36 44,36 43,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,36 44,36 45,36 45,37 46,37 46,37 47,38 47,38 48,39 48,39 48,40 48,40 49,41 49,42 49,42 48,43 48,44 48,44 48,45 47,45 47,46 46,46 46,47 45,47 45,47 44,47 43,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="26" x2="26" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="8" y2="8"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="23"/>
    <rect height="23" stroke-width="0.398039" width="12" x="20" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="8" x2="8" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="43" x2="43" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="16" y2="24"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <rect height="29" stroke-width="2" width="15" x="1" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="8" x2="11" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="9" x2="6" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.8" x1="8" x2="8" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="11" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="1" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="51" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape173">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="29" x2="29" y1="15" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99998" x1="46" x2="46" y1="59" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.49157" x1="15" x2="15" y1="14" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99998" x1="46" x2="36" y1="59" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="2" x2="46" y1="15" y2="59"/>
    <polyline points="29,14 23,17 21,18 21,19 21,19 23,21 26,22 30,24 31,25 31,25 31,26 30,27 26,28 23,30 22,30 22,31 22,32 23,33 26,34 30,36 31,36 31,37 31,38 30,38 26,40 23,41 22,42 22,43 22,44 23,44 26,46 30,47 31,48 31,49 31,49 30,50 26,52 23,53 21,55 21,55 21,56 23,57 29,60 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="24" x2="33" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="20" x2="36" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="26" x2="30" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="29" x2="29" y1="59" y2="66"/>
   </symbol>
   <symbol id="lightningRod:shape85">
    <circle cx="8" cy="17" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="20" y2="20"/>
    <rect height="27" stroke-width="0.416667" width="14" x="1" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="73" y2="25"/>
    <circle cx="8" cy="8" fillStyle="0" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape70">
    <circle cx="43" cy="47" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="29" x2="32" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="27" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="31" x2="31" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="37" x2="25" y1="8" y2="8"/>
    <polyline arcFlag="1" points="57,22 58,22 58,22 58,22 59,23 59,23 60,24 60,24 60,25 61,25 61,26 61,27 61,27 61,28 61,28 61,29 61,30 60,30 60,31 60,31 59,32 59,32 58,33 58,33 58,33 57,33 " stroke-width="0.0290179"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="98" y2="113"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="45" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429293" x1="56" x2="56" y1="22" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429293" x1="57" x2="57" y1="48" y2="43"/>
    <polyline arcFlag="1" points="57,32 58,32 58,32 58,32 59,33 59,33 60,34 60,34 60,35 61,35 61,36 61,37 61,37 61,38 61,38 61,39 61,40 60,40 60,41 60,41 59,42 59,42 58,43 58,43 58,43 57,43 " stroke-width="0.0290179"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="98" y2="57"/>
    <rect height="29" stroke-width="0.416667" width="14" x="28" y="62"/>
    <circle cx="43" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="35" y1="98" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="97" y2="54"/>
    <rect height="29" stroke-width="0.416667" width="14" x="0" y="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="55" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="51" x2="57" y1="48" y2="48"/>
    <circle cx="34" cy="47" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="34" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape172">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="40" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="17" x2="33" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="40" x2="40" y1="44" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="33" x2="33" y1="44" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="8" x2="25" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="25" y1="56" y2="56"/>
    <circle cx="16" cy="18" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="16" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="48" y2="28"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape24_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape5_0">
    <circle cx="38" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="60" y1="56" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="60" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="58" y1="90" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="29" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="22" y2="29"/>
   </symbol>
   <symbol id="transformer:shape5_1">
    <circle cx="38" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape5-2">
    <circle cx="68" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="54" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="54"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape16_0">
    <circle cx="29" cy="25" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="23" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="23" x2="15" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="15" x2="23" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape16_1">
    <ellipse cx="60" cy="25" fillStyle="0" rx="24.5" ry="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="74" x2="66" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="66" x2="58" y1="25" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="58" x2="66" y1="17" y2="25"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="voltageTransformer:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07261" x1="14" x2="11" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="41" x2="48" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="35" x2="53" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="42" x2="46" y1="38" y2="38"/>
    <polyline points="20,13 45,13 45,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07261" x1="7" x2="11" y1="15" y2="13"/>
    <circle cx="10" cy="28" r="9.5" stroke-width="1.0673"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07261" x1="14" x2="11" y1="31" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07262" x1="11" x2="11" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07261" x1="7" x2="11" y1="31" y2="28"/>
    <ellipse cx="10" cy="13" rx="9.5" ry="10" stroke-width="1.0673"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07262" x1="11" x2="11" y1="13" y2="9"/>
   </symbol>
   <symbol id="voltageTransformer:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="64" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="57" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="9" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="45" y1="9" y2="9"/>
    <circle cx="31" cy="46" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="40" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="31" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="23" x2="23" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="39" x2="22" y1="10" y2="10"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_276efa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_276fef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27707e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2770f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2771b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27725f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2772d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27737f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d09730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d09730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27761f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27761f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2777060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2777060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2777a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2779730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_277a380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_277b260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_277bb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_277ca90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_277d620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_277dee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_277e6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_277f780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2780100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2780840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2780f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27824c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2782fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2783fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2784c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27930e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2793910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27865d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2787b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1308" width="2722" x="3111" y="-1239"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0eec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 997.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a10160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3982.000000 982.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a10d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 967.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a122c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4477.000000 959.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a12590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4466.000000 944.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a127d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.000000 929.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a12bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 862.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a12eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 847.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a130f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 832.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a13510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 862.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a137d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 847.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a13a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 832.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a13e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5078.000000 723.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a140f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 708.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a14330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 693.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a14750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 280.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a14a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5066.000000 265.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a14c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5091.000000 250.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a15070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5662.000000 374.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a15330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5651.000000 359.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a15570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5676.000000 344.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a15990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5653.000000 680.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a15c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5642.000000 665.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a15e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5667.000000 650.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a162b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5643.000000 842.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a16570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5632.000000 827.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a167b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5657.000000 812.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a16bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5655.000000 964.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a16e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5644.000000 949.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a170d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5669.000000 934.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a174f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 43.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1998f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3581.000000 28.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19991a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 13.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19995c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4416.000000 266.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1999880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4405.000000 251.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1999ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 236.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1999df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -4.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199a050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 11.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199a380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 -49.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199a5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -34.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a8080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 452.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a8340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4252.000000 437.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a8580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 422.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195b500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 961.375000) translate(0,12)">Uc(kV):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195c1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 946.250000) translate(0,12)">Uab(kV):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195c780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 991.625000) translate(0,12)">Ua(kV):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195c980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 976.500000) translate(0,12)">Ub(kV):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195cbc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 931.250000) translate(0,12)">F(Hz):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdde70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 438.375000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cde0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.000000 423.375000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cde330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4930.000000 392.250000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cde570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 453.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cde7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 468.625000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cde9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.000000 408.250000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cded20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 440.375000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdefa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3609.000000 425.375000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdf1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3611.000000 394.250000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdf420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 455.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdf660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 470.625000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdf8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 410.250000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdfbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.000000 16.375000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdfe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 -13.750000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce0090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.000000 46.625000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce02d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.000000 31.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce0510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -29.750000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce0750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 1.375000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce0a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5099.000000 1123.375000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce0d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5091.000000 1093.250000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce0f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5099.000000 1153.625000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce1180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5099.000000 1138.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce13c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5107.000000 1077.250000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce1600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.000000 1108.375000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce32b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5593.000000 240.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce3530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5582.000000 225.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce3730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5607.000000 210.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d22990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 457.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d22fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.000000 442.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 427.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 38.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3693.000000 23.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 8.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 37.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3798.000000 22.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.000000 7.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 36.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 6.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d251a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 36.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d25460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.000000 21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d256a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 6.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d25ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.000000 40.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d25d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 25.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d25fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4783.000000 10.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d263e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4895.000000 40.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d266a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 25.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d268e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4909.000000 10.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d26c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -4.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d26e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4139.000000 11.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2067700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5657.000000 124.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2067d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5646.000000 109.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2067f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5671.000000 94.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5422,-942 5422,-922 5434,-933 5422,-942 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5461,-924 5461,-944 5449,-933 5461,-924 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5414,-363 5414,-343 5426,-354 5414,-363 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5446,-344 5446,-364 5434,-353 5446,-344 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5491,-90 5491,-110 5479,-99 5491,-90 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5459,-110 5459,-90 5471,-101 5459,-110 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-68397">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5340.000000 -943.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14533" ObjectName="SW-CX_SB.CX_SB_361BK"/>
     <cge:Meas_Ref ObjectId="68397"/>
    <cge:TPSR_Ref TObjectID="14533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5339.000000 -817.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14528" ObjectName="SW-CX_SB.CX_SB_363BK"/>
     <cge:Meas_Ref ObjectId="68422"/>
    <cge:TPSR_Ref TObjectID="14528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5342.000000 -666.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14529" ObjectName="SW-CX_SB.CX_SB_365BK"/>
     <cge:Meas_Ref ObjectId="68445"/>
    <cge:TPSR_Ref TObjectID="14529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5342.000000 -364.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14530" ObjectName="SW-CX_SB.CX_SB_362BK"/>
     <cge:Meas_Ref ObjectId="68468"/>
    <cge:TPSR_Ref TObjectID="14530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70977">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.000000 -975.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15959" ObjectName="SW-CX_SB.CX_SB_156BK"/>
     <cge:Meas_Ref ObjectId="70977"/>
    <cge:TPSR_Ref TObjectID="15959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70904">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4576.000000 -943.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15955" ObjectName="SW-CX_SB.CX_SB_154BK"/>
     <cge:Meas_Ref ObjectId="70904"/>
    <cge:TPSR_Ref TObjectID="15955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69725">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4110.000000 -814.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15608" ObjectName="SW-CX_SB.CX_SB_101BK"/>
     <cge:Meas_Ref ObjectId="69725"/>
    <cge:TPSR_Ref TObjectID="15608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4210.442247 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15614" ObjectName="SW-CX_SB.CX_SB_001BK"/>
     <cge:Meas_Ref ObjectId="69765"/>
    <cge:TPSR_Ref TObjectID="15614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70734">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.568116 -270.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15963" ObjectName="SW-CX_SB.CX_SB_012BK"/>
     <cge:Meas_Ref ObjectId="70734"/>
    <cge:TPSR_Ref TObjectID="15963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4765.563986 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11963" ObjectName="SW-CX_SB.CX_SB_002BK"/>
     <cge:Meas_Ref ObjectId="63162"/>
    <cge:TPSR_Ref TObjectID="11963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5115.000000 -727.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15613" ObjectName="SW-CX_SB.CX_SB_301BK"/>
     <cge:Meas_Ref ObjectId="69759"/>
    <cge:TPSR_Ref TObjectID="15613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68491">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5342.000000 -233.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14531" ObjectName="SW-CX_SB.CX_SB_364BK"/>
     <cge:Meas_Ref ObjectId="68491"/>
    <cge:TPSR_Ref TObjectID="14531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63431">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 -815.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11974" ObjectName="SW-CX_SB.CX_SB_102BK"/>
     <cge:Meas_Ref ObjectId="63431"/>
    <cge:TPSR_Ref TObjectID="11974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63455">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5115.000000 -289.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11976" ObjectName="SW-CX_SB.CX_SB_302BK"/>
     <cge:Meas_Ref ObjectId="63455"/>
    <cge:TPSR_Ref TObjectID="11976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3656.000000 -235.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14522" ObjectName="SW-CX_SB.CX_SB_061BK"/>
     <cge:Meas_Ref ObjectId="68269"/>
    <cge:TPSR_Ref TObjectID="14522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68292">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -235.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14523" ObjectName="SW-CX_SB.CX_SB_062BK"/>
     <cge:Meas_Ref ObjectId="68292"/>
    <cge:TPSR_Ref TObjectID="14523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14524" ObjectName="SW-CX_SB.CX_SB_063BK"/>
     <cge:Meas_Ref ObjectId="68315"/>
    <cge:TPSR_Ref TObjectID="14524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68338">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -236.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14525" ObjectName="SW-CX_SB.CX_SB_064BK"/>
     <cge:Meas_Ref ObjectId="68338"/>
    <cge:TPSR_Ref TObjectID="14525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14526" ObjectName="SW-CX_SB.CX_SB_065BK"/>
     <cge:Meas_Ref ObjectId="68359"/>
    <cge:TPSR_Ref TObjectID="14526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63884">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 -236.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12008" ObjectName="SW-CX_SB.CX_SB_071BK"/>
     <cge:Meas_Ref ObjectId="63884"/>
    <cge:TPSR_Ref TObjectID="12008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68377">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -237.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14527" ObjectName="SW-CX_SB.CX_SB_066BK"/>
     <cge:Meas_Ref ObjectId="68377"/>
    <cge:TPSR_Ref TObjectID="14527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63464">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -236.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11985" ObjectName="SW-CX_SB.CX_SB_072BK"/>
     <cge:Meas_Ref ObjectId="63464"/>
    <cge:TPSR_Ref TObjectID="11985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5298.000000 -481.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15948" ObjectName="SW-CX_SB.CX_SB_312BK"/>
     <cge:Meas_Ref ObjectId="70722"/>
    <cge:TPSR_Ref TObjectID="15948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106116">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20692" ObjectName="SW-CX_SB.CX_SB_073BK"/>
     <cge:Meas_Ref ObjectId="106116"/>
    <cge:TPSR_Ref TObjectID="20692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 -256.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20697" ObjectName="SW-CX_SB.CX_SB_074BK"/>
     <cge:Meas_Ref ObjectId="106123"/>
    <cge:TPSR_Ref TObjectID="20697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-305069">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5346.000000 -110.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47265" ObjectName="SW-CX_SB.CX_SB_368BK"/>
     <cge:Meas_Ref ObjectId="305069"/>
    <cge:TPSR_Ref TObjectID="47265"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SB.CX_SB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="7035"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 -674.000000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="7037"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 -674.000000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="7039"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 -674.000000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4843" ObjectName="TF-CX_SB.CX_SB_1T"/>
    <cge:TPSR_Ref TObjectID="4843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SB.CX_SB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16992"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.121739 -673.000000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="16994"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.121739 -673.000000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="16996"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.121739 -673.000000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="12047" ObjectName="TF-CX_SB.CX_SB_2T"/>
    <cge:TPSR_Ref TObjectID="12047"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-1216 5217,-530 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12192" ObjectName="BS-CX_SB.CX_SB_3IM"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   <polyline fill="none" opacity="0" points="5217,-1216 5217,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-474 5217,30 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12193" ObjectName="BS-CX_SB.CX_SB_3IIM"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   <polyline fill="none" opacity="0" points="5217,-474 5217,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-351 3608,-351 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11961" ObjectName="BS-CX_SB.CX_SB_9IM"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   <polyline fill="none" opacity="0" points="4442,-351 3608,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-351 4490,-351 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11962" ObjectName="BS-CX_SB.CX_SB_9IIM"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   <polyline fill="none" opacity="0" points="5015,-351 4490,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SB.CX_SB_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3958,-904 4821,-904 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12191" ObjectName="BS-CX_SB.CX_SB_1IM"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   <polyline fill="none" opacity="0" points="3958,-904 4821,-904 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_SB.CX_SB_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -19.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28611" ObjectName="CB-CX_SB.CX_SB_Cb1"/>
    <cge:TPSR_Ref TObjectID="28611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_SB.CX_SB_Cb3">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -15.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28612" ObjectName="CB-CX_SB.CX_SB_Cb3"/>
    <cge:TPSR_Ref TObjectID="28612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_SB.CX_SB_2C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4563.852174 28.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12112" ObjectName="CB-CX_SB.CX_SB_2C"/>
    <cge:TPSR_Ref TObjectID="12112"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -68.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -68.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.555556 -0.000000 0.000000 -0.540000 5521.000000 -399.000000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.555556 -0.000000 0.000000 -0.540000 5521.000000 -399.000000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_17f1730">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -528.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e8410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4335.000000 -528.000000)" xlink:href="#lightningRod:shape173"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17c59c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4467.000000 -528.000000)" xlink:href="#lightningRod:shape173"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aaf210">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.000000 -528.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1abe640">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5438.000000 -1161.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a523e0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5440.000000 -1109.000000)" xlink:href="#lightningRod:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a83700">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5363.500000 -897.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab2890">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5479.500000 -975.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b1b840">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5363.500000 -787.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a82ca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5359.500000 -636.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab5ae0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5360.500000 -334.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab7a60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5366.500000 -203.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b73030">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.000000 -1101.000000)" xlink:href="#lightningRod:shape172"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b73e40">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4127.000000 -1086.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1affb50">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4521.000000 -1075.000000)" xlink:href="#lightningRod:shape172"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b00c70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 -1060.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a7cc60">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 -655.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a7d690">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4065.500000 -718.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b52910">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4606.000000 -654.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b53200">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4628.500000 -717.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ae9090">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3695.442247 -127.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa1490">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3801.442247 -127.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa39d0">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3910.442247 -126.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aca3a0">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4016.442247 -128.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aca750">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 3872.000000 -192.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acab30">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 3978.000000 -192.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acaf10">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 3763.000000 -191.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acb2f0">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 3657.000000 -191.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a559d0">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 4085.000000 -197.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b13e80">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 4212.000000 -193.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1950d30">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3724.442247 -436.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a3f0c0">
    <use class="BV-10KV" transform="matrix(0.666667 -0.000000 0.000000 -0.642857 3899.000000 -422.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a42150">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.880000 4339.000000 -189.440000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a24470">
    <use class="BV-10KV" transform="matrix(0.666667 -0.000000 0.000000 -0.642857 4341.000000 -247.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19fa060">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4743.166885 -127.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a84060">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 4704.724638 -192.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a94020">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.469388 4579.852174 -192.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a3e760">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4596.900218 -430.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9d000">
    <use class="BV-110KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4721.000000 -766.099782)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9ddb0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4762.000000 -691.099782)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9eb60">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4717.900218 -635.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19b01c0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5439.000000 -31.000000)" xlink:href="#lightningRod:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4b590">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4259.442247 -1082.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_199dd00">
    <use class="BV-10KV" transform="matrix(0.833333 -0.000000 0.000000 -0.809524 4625.000000 -433.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc4520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -144.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc6ad0">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.816327 4826.000000 -159.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc7bb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4851.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cd70b0">
    <use class="BV-10KV" transform="matrix(0.800000 -0.000000 0.000000 -0.816327 4952.000000 -159.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cd8190">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cd8f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -144.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d33f90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5485.500000 -387.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_205fca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5464.500000 -81.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3225.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-69501" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.000000 -697.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69501" ObjectName="CX_SB:CX_SB_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-63855" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4860.000000 -757.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63855" ObjectName="CX_SB:CX_SB_2T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62632" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3262.538462 -956.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62632" ObjectName="CX_SB:CX_SB_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79740" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3262.538462 -914.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79740" ObjectName="CX_SB:CX_SB_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208334" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3261.538462 -1039.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208334" ObjectName="CX_SB:CX_SB_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239629" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3263.538462 -996.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239629" ObjectName="CX_SB:CX_SB_sumP2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-229032" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4057.000000 -1180.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229032" ObjectName="CX_SB:CX_SB_156BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-70698" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4057.000000 -1200.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70698" ObjectName="CX_SB:CX_SB_156BK_Ux"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-229026" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4530.000000 -1152.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229026" ObjectName="CX_SB:CX_SB_154BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-70690" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4530.000000 -1172.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70690" ObjectName="CX_SB:CX_SB_154BK_Ux"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-68551" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5554.000000 -887.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68551" ObjectName="CX_SB:CX_SB_361BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-68558" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5574.000000 -796.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68558" ObjectName="CX_SB:CX_SB_363BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-68565" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5613.000000 -710.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68565" ObjectName="CX_SB:CX_SB_365BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-68572" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5669.000000 -319.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68572" ObjectName="CX_SB:CX_SB_362BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-68579" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5618.000000 -275.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68579" ObjectName="CX_SB:CX_SB_364BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-63872" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4681.000000 10.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63872" ObjectName="CX_SB:CX_SB_072BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(124,252,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5617.000000 -154.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="12191" cx="4585" cy="-904" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12191" cx="4681" cy="-904" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4713" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4588" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4756" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4632" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4526" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3880" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3665" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="4347" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3771" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3986" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="4220" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3760" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="3905" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="4403" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="4200" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4834" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11962" cx="4960" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5217" cy="-1088" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5217" cy="-1153" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5217" cy="-567" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5217" cy="-807" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5217" cy="-656" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11961" cx="4093" cy="-351" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12191" cx="4119" cy="-904" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12191" cx="4110" cy="-904" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12191" cx="4253" cy="-904" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12192" cx="5217" cy="-737" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5217" cy="-10" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5217" cy="-224" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5217" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5217" cy="-439" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5217" cy="-299" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12193" cx="5217" cy="-100" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-62776" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3427.000000 -1087.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11953" ObjectName="DYN-CX_SBEQ"/>
     <cge:Meas_Ref ObjectId="62776"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b57dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1041.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b57dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1041.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b57dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1041.000000) translate(0,59)">片区有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b57dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1041.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b57dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1041.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b57dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1041.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b57dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1041.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b57dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1041.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b57dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -1041.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,17)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,101)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,374)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,395)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,416)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18a6f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -580.000000) translate(0,437)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b619b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5374.000000 -833.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a5d510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5378.000000 -959.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a5ff10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5375.000000 -682.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a5acc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5373.000000 -379.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a5ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5444.000000 -1161.000000) translate(0,15)">I段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a5b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5449.000000 -1088.000000) translate(0,15)">I段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a5b420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5568.000000 -385.000000) translate(0,15)">爱尼山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a5b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5526.000000 -834.000000) translate(0,15)">雨龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a595f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5490.000000 -679.000000) translate(0,15)">双小妥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a597e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5490.000000 -216.000000) translate(0,15)">双妥大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a599d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5555.000000 -963.000000) translate(0,15)">鱼庄河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1a59f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3277.000000 -1167.500000) translate(0,16)">双柏变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a5a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5504.000000 -732.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b72b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5252.000000 -1114.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b72cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5389.000000 -247.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b72e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5573.000000 -345.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_194fe40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3706.500000 -518.000000) translate(0,15)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a63150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3857.500000 -518.000000) translate(0,15)">10kVI段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a3de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4497.500000 -516.000000) translate(0,15)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a9f910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.500000 -1203.000000) translate(0,15)">双鄂大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b4d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5448.000000 -10.000000) translate(0,15)">Ⅱ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b5270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5251.000000 -36.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.500000 -868.000000) translate(0,15)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.500000 -868.000000) translate(0,33)">SFSZ8-25000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.500000 -868.000000) translate(0,51)">110±8×1.25%38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.500000 -868.000000) translate(0,69)">25/25/25MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.500000 -868.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.500000 -868.000000) translate(0,105)">Uk1-2%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.500000 -868.000000) translate(0,123)">Uk1-3%=18.1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.500000 -868.000000) translate(0,141)">Uk2-3%=6.86</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a47ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.500000 -878.000000) translate(0,15)">2号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a47ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.500000 -878.000000) translate(0,33)">SFSZ11-40000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a47ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.500000 -878.000000) translate(0,51)">110±8×1.25%38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a47ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.500000 -878.000000) translate(0,69)">40/40/40MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a47ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.500000 -878.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a47ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.500000 -878.000000) translate(0,105)">Uk1-2%=10.37</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a47ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.500000 -878.000000) translate(0,123)">Uk1-3%=18.51</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a47ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.500000 -878.000000) translate(0,141)">Uk2-3%=6.65</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a4b090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.500000 -1140.000000) translate(0,15)">110kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3607.000000 -372.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a50590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4924.000000 -370.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a50c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -392.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a50e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -249.000000) translate(0,12)">07127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6e4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4595.000000 -313.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4595.000000 -173.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6e990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -312.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6ebd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4732.000000 -249.000000) translate(0,12)">07227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6ee10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4764.000000 -390.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4765.000000 -487.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6f290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4539.000000 -714.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6f4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4632.000000 -798.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6f710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5170.000000 -325.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6f950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5072.000000 -325.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6fb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4533.000000 -323.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a6fdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3778.000000 -172.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a70010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -173.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a70250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.000000 -171.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a70490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -176.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a706d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -137.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a706d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -137.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a706d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -137.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a706d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -137.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a706d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -137.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a706d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -137.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -137.000000) translate(0,15)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -137.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -137.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -137.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -137.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -137.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a718b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -161.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -137.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -137.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -137.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -137.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -137.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a71b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -137.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a71d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 -157.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a71f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4260.000000 -1004.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a721d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4273.000000 -1057.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -976.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -1085.000000) translate(0,12)">15667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4068.000000 -798.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5074.000000 -763.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5172.000000 -763.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a72f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -51.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a736b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3672.000000 -315.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a73930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 -248.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a73b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3778.000000 -314.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a73db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.000000 -248.000000) translate(0,12)">06217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a73ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -314.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a74230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3900.000000 -247.000000) translate(0,12)">06317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a74470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.000000 -313.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a746b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4006.000000 -249.000000) translate(0,12)">06417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a748f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -318.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a74b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -254.000000) translate(0,12)">06517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a74d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -178.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a74fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -314.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a751f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4240.000000 -250.000000) translate(0,12)">06617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4354.000000 -313.000000) translate(0,12)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -322.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a758b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -390.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -487.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4618.000000 -156.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3767.000000 -398.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a761b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -399.000000) translate(0,12)">0801</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a766f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3978.000000 -714.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a77df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 -717.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a78790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -778.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a78bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4408.000000 -626.000000) translate(0,12)">3000</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a78e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -568.000000) translate(0,12)">    35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a78e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -568.000000) translate(0,27)">1号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a79c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4542.000000 -568.000000) translate(0,12)">    35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a79c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4542.000000 -568.000000) translate(0,27)">2号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a7a0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 -697.000000) translate(0,12)">温度1：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a7a860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4811.000000 -758.000000) translate(0,12)">温度1：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0cf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3672.000000 -165.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0d420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4224.000000 -629.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0d660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -630.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0d8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -750.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0dae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4740.000000 -750.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0e690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3892.000000 -928.000000) translate(0,12)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0e8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5223.000000 -1216.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0eaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5226.000000 27.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199a820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -172.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199cb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5253.000000 -1177.000000) translate(0,12)">3903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5252.000000 -833.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199d400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5253.000000 -682.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199d640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5251.000000 -592.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199d880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5248.000000 -465.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_199dac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5252.000000 -377.000000) translate(0,12)">3622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_199e420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.500000 -1234.000000) translate(0,15)">谢烟双线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19aaf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5248.000000 -958.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_19b9130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3422.000000 -1164.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_198d4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4531.000000 -1062.000000) translate(0,12)">15467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_198ebd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5367.000000 -509.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1962410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 -460.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdbbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4841.000000 -144.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdc220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4841.000000 -243.000000) translate(0,12)">0733</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdc460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4841.000000 -326.000000) translate(0,12)">0732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdc6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4849.000000 -224.000000) translate(0,12)">07337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdc8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4967.000000 -144.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdcb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4967.000000 -242.000000) translate(0,12)">0743</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdcd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4976.000000 -224.000000) translate(0,12)">07437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cdcfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4967.000000 -326.000000) translate(0,12)">0742</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1ce1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.500000 -161.000000) translate(0,17)">4937</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce6ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5255.000000 -247.000000) translate(0,12)">3642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cf6f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5506.000000 -261.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cfbec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3148.000000 -684.000000) translate(0,17)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfd310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3674.000000 -264.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfd780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 -264.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfd9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3889.000000 -263.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfdc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3995.000000 -265.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfde40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -270.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfe080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4229.000000 -266.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfe2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -265.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfe500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4722.000000 -265.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfe740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -285.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfe980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.000000 -285.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfebc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5305.000000 -958.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfee00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5304.000000 -832.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cff040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5316.000000 -510.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cff280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5308.000000 -681.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cff4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5307.000000 -379.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cff700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5308.000000 -248.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cff940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5125.000000 -761.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cffb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5125.000000 -323.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cffdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 -438.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d00000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -438.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d00240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4128.000000 -848.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d00480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4690.000000 -844.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d006c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -1004.000000) translate(0,12)">156</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d00900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4594.000000 -972.000000) translate(0,12)">154</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d02360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -304.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d03a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4658.000000 -452.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,16)">1、全站停电检修前应挂“全站停电检修”牌，复电后</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,36)">方可摘除“全站停电检修”牌。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,76)">2、各类间隔工作时，停电完成后应在相应间隔挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,96)">“禁止合闸，有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,136)">3、线路工作时，停电完成后应在相应间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,156)">挂“禁止合闸，线路有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,196)">4、现场工作影响对应间隔四遥信息正确性的，应挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,216)">“禁止刷新”牌，工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,256)">5、现场开展相应间隔四遥信息核对前，应挂“调试一”牌，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d04000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -459.000000) translate(0,276)">核对工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1d131b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -1146.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1d06180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -1181.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d17660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -1201.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d17db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3994.000000 -1181.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d18860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -1173.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d18aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4467.000000 -1153.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5499.000000 -888.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d19bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5519.000000 -797.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5558.000000 -711.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5614.000000 -321.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1af00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5563.000000 -276.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1b570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4649.000000 10.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1bbe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3636.000000 -126.000000) translate(0,15)">瓦</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1bbe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3636.000000 -126.000000) translate(0,33)">波</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1bbe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3636.000000 -126.000000) translate(0,51)">里</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1bbe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3636.000000 -126.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1c750" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -127.000000) translate(0,15)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1c750" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -127.000000) translate(0,33)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1c750" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -127.000000) translate(0,51)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1c750" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -127.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1c750" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -127.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1d600" transform="matrix(1.000000 0.000000 0.000000 1.000000 3748.000000 -119.000000) translate(0,15)">花</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1d600" transform="matrix(1.000000 0.000000 0.000000 1.000000 3748.000000 -119.000000) translate(0,33)">箐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1d600" transform="matrix(1.000000 0.000000 0.000000 1.000000 3748.000000 -119.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1de90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3962.000000 -105.000000) translate(0,15)">朝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1de90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3962.000000 -105.000000) translate(0,33)">阳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1de90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3962.000000 -105.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1e710" transform="matrix(1.000000 0.000000 0.000000 1.000000 4685.000000 -123.000000) translate(0,15)">玉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1e710" transform="matrix(1.000000 0.000000 0.000000 1.000000 4685.000000 -123.000000) translate(0,33)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1e710" transform="matrix(1.000000 0.000000 0.000000 1.000000 4685.000000 -123.000000) translate(0,51)">柏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1e710" transform="matrix(1.000000 0.000000 0.000000 1.000000 4685.000000 -123.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1f2e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -113.000000) translate(0,15)">和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1f2e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -113.000000) translate(0,33)">平</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1f2e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -113.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1fb50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4935.000000 -110.000000) translate(0,15)">环</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1fb50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4935.000000 -110.000000) translate(0,33)">东</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1fb50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4935.000000 -110.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d270b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3116.000000 -120.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d270b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3116.000000 -120.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b1c360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -130.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b1c360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -130.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b1c360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -130.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d34d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5466.000000 -450.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d3ba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3148.000000 -608.000000) translate(0,17)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2062f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5378.000000 -130.000000) translate(0,12)">3686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2063b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5508.000000 -176.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_206a7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5311.000000 -131.000000) translate(0,12)">368</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_206ae20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5562.000000 -155.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_2070860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5259.000000 -131.000000) translate(0,12)">3682</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="15" graphid="g_2070e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5434.000000 -66.000000) translate(0,12)">36867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23d1410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5545.000000 -86.000000) translate(0,15)">35kV双田线</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4588" x2="4604" y1="-127" y2="-127"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4588" x2="4604" y1="-99" y2="-99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5654" x2="5663" y1="-656" y2="-656"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5824" x2="5828" y1="-524" y2="-524"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5658" x2="5667" y1="-100" y2="-100"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-69816">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 4404.000000 -600.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15647" ObjectName="SW-CX_SB.CX_SB_3000SW"/>
     <cge:Meas_Ref ObjectId="69816"/>
    <cge:TPSR_Ref TObjectID="15647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-64539">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 4217.000000 -600.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12190" ObjectName="SW-CX_SB.CX_SB_3010SW"/>
     <cge:Meas_Ref ObjectId="64539"/>
    <cge:TPSR_Ref TObjectID="12190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-64538">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 4559.000000 -600.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12188" ObjectName="SW-CX_SB.CX_SB_3020SW"/>
     <cge:Meas_Ref ObjectId="64538"/>
    <cge:TPSR_Ref TObjectID="12188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70748">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5245.000000 -1083.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15952" ObjectName="SW-CX_SB.CX_SB_3901SW"/>
     <cge:Meas_Ref ObjectId="70748"/>
    <cge:TPSR_Ref TObjectID="15952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68608">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5368.000000 -349.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14560" ObjectName="SW-CX_SB.CX_SB_3626SW"/>
     <cge:Meas_Ref ObjectId="68608"/>
    <cge:TPSR_Ref TObjectID="14560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5368.000000 -651.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14558" ObjectName="SW-CX_SB.CX_SB_3656SW"/>
     <cge:Meas_Ref ObjectId="68606"/>
    <cge:TPSR_Ref TObjectID="14558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68399">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5371.000000 -928.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15966" ObjectName="SW-CX_SB.CX_SB_3616SW"/>
     <cge:Meas_Ref ObjectId="68399"/>
    <cge:TPSR_Ref TObjectID="15966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5368.000000 -802.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14556" ObjectName="SW-CX_SB.CX_SB_3636SW"/>
     <cge:Meas_Ref ObjectId="68604"/>
    <cge:TPSR_Ref TObjectID="14556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70990">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -1019.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15960" ObjectName="SW-CX_SB.CX_SB_156XC"/>
     <cge:Meas_Ref ObjectId="70990"/>
    <cge:TPSR_Ref TObjectID="15960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70917">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -913.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15957" ObjectName="SW-CX_SB.CX_SB_154XC1"/>
     <cge:Meas_Ref ObjectId="70917"/>
    <cge:TPSR_Ref TObjectID="15957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70917">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -990.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15956" ObjectName="SW-CX_SB.CX_SB_154XC"/>
     <cge:Meas_Ref ObjectId="70917"/>
    <cge:TPSR_Ref TObjectID="15956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70918">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 4528.000000 -1033.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15958" ObjectName="SW-CX_SB.CX_SB_15467SW"/>
     <cge:Meas_Ref ObjectId="70918"/>
    <cge:TPSR_Ref TObjectID="15958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69737">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.857143 4066.000000 -770.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15609" ObjectName="SW-CX_SB.CX_SB_10167SW"/>
     <cge:Meas_Ref ObjectId="69737"/>
    <cge:TPSR_Ref TObjectID="15609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63432">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.978261 -0.000000 0.000000 -0.857143 4627.000000 -769.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11975" ObjectName="SW-CX_SB.CX_SB_10267SW"/>
     <cge:Meas_Ref ObjectId="63432"/>
    <cge:TPSR_Ref TObjectID="11975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63512">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12033" ObjectName="SW-CX_SB.CX_SB_0626SW"/>
     <cge:Meas_Ref ObjectId="63512"/>
    <cge:TPSR_Ref TObjectID="12033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -143.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12034" ObjectName="SW-CX_SB.CX_SB_0636SW"/>
     <cge:Meas_Ref ObjectId="63513"/>
    <cge:TPSR_Ref TObjectID="12034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63514">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12035" ObjectName="SW-CX_SB.CX_SB_0646SW"/>
     <cge:Meas_Ref ObjectId="63514"/>
    <cge:TPSR_Ref TObjectID="12035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68589">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3656.000000 -285.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14541" ObjectName="SW-CX_SB.CX_SB_0611SW"/>
     <cge:Meas_Ref ObjectId="68589"/>
    <cge:TPSR_Ref TObjectID="14541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68591">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14543" ObjectName="SW-CX_SB.CX_SB_0621SW"/>
     <cge:Meas_Ref ObjectId="68591"/>
    <cge:TPSR_Ref TObjectID="14543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68593">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14545" ObjectName="SW-CX_SB.CX_SB_0631SW"/>
     <cge:Meas_Ref ObjectId="68593"/>
    <cge:TPSR_Ref TObjectID="14545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68595">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14547" ObjectName="SW-CX_SB.CX_SB_0641SW"/>
     <cge:Meas_Ref ObjectId="68595"/>
    <cge:TPSR_Ref TObjectID="14547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -146.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14551" ObjectName="SW-CX_SB.CX_SB_0656SW"/>
     <cge:Meas_Ref ObjectId="68599"/>
    <cge:TPSR_Ref TObjectID="14551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68597">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14549" ObjectName="SW-CX_SB.CX_SB_0651SW"/>
     <cge:Meas_Ref ObjectId="68597"/>
    <cge:TPSR_Ref TObjectID="14549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68602">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14554" ObjectName="SW-CX_SB.CX_SB_0666SW"/>
     <cge:Meas_Ref ObjectId="68602"/>
    <cge:TPSR_Ref TObjectID="14554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68600">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14552" ObjectName="SW-CX_SB.CX_SB_0661SW"/>
     <cge:Meas_Ref ObjectId="68600"/>
    <cge:TPSR_Ref TObjectID="14552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15946" ObjectName="SW-CX_SB.CX_SB_0901SW"/>
     <cge:Meas_Ref ObjectId="70705"/>
    <cge:TPSR_Ref TObjectID="15946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3896.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15947" ObjectName="SW-CX_SB.CX_SB_0671SW"/>
     <cge:Meas_Ref ObjectId="70711"/>
    <cge:TPSR_Ref TObjectID="15947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69736">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 -784.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15633" ObjectName="SW-CX_SB.CX_SB_101XC1"/>
     <cge:Meas_Ref ObjectId="69736"/>
    <cge:TPSR_Ref TObjectID="15633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69736">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 -855.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15632" ObjectName="SW-CX_SB.CX_SB_101XC"/>
     <cge:Meas_Ref ObjectId="69736"/>
    <cge:TPSR_Ref TObjectID="15632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63584">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.121739 -862.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12071" ObjectName="SW-CX_SB.CX_SB_102XC"/>
     <cge:Meas_Ref ObjectId="63584"/>
    <cge:TPSR_Ref TObjectID="12071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63584">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.121739 -784.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12072" ObjectName="SW-CX_SB.CX_SB_102XC1"/>
     <cge:Meas_Ref ObjectId="63584"/>
    <cge:TPSR_Ref TObjectID="12072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15616" ObjectName="SW-CX_SB.CX_SB_0016SW"/>
     <cge:Meas_Ref ObjectId="69769"/>
    <cge:TPSR_Ref TObjectID="15616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15615" ObjectName="SW-CX_SB.CX_SB_0011SW"/>
     <cge:Meas_Ref ObjectId="69768"/>
    <cge:TPSR_Ref TObjectID="15615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63507">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12028" ObjectName="SW-CX_SB.CX_SB_0726SW"/>
     <cge:Meas_Ref ObjectId="63507"/>
    <cge:TPSR_Ref TObjectID="12028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63465">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -281.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11986" ObjectName="SW-CX_SB.CX_SB_0722SW"/>
     <cge:Meas_Ref ObjectId="63465"/>
    <cge:TPSR_Ref TObjectID="11986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12012" ObjectName="SW-CX_SB.CX_SB_0716SW"/>
     <cge:Meas_Ref ObjectId="63491"/>
    <cge:TPSR_Ref TObjectID="12012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63489">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12010" ObjectName="SW-CX_SB.CX_SB_0712SW"/>
     <cge:Meas_Ref ObjectId="63489"/>
    <cge:TPSR_Ref TObjectID="12010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70736">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15951" ObjectName="SW-CX_SB.CX_SB_0121SW"/>
     <cge:Meas_Ref ObjectId="70736"/>
    <cge:TPSR_Ref TObjectID="15951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63515">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4518.000000 -293.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12036" ObjectName="SW-CX_SB.CX_SB_0122SW"/>
     <cge:Meas_Ref ObjectId="63515"/>
    <cge:TPSR_Ref TObjectID="12036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4747.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11966" ObjectName="SW-CX_SB.CX_SB_0026SW"/>
     <cge:Meas_Ref ObjectId="63165"/>
    <cge:TPSR_Ref TObjectID="11966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63164">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4747.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11965" ObjectName="SW-CX_SB.CX_SB_0022SW"/>
     <cge:Meas_Ref ObjectId="63164"/>
    <cge:TPSR_Ref TObjectID="11965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63506">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4624.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12027" ObjectName="SW-CX_SB.CX_SB_0902SW"/>
     <cge:Meas_Ref ObjectId="63506"/>
    <cge:TPSR_Ref TObjectID="12027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5475.000000 -901.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5460.000000 -698.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5501.000000 -312.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70754">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5244.000000 -5.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15953" ObjectName="SW-CX_SB.CX_SB_3902SW"/>
     <cge:Meas_Ref ObjectId="70754"/>
    <cge:TPSR_Ref TObjectID="15953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69508">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -974.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15605" ObjectName="SW-CX_SB.CX_SB_1901SW"/>
     <cge:Meas_Ref ObjectId="69508"/>
    <cge:TPSR_Ref TObjectID="15605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3656.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12032" ObjectName="SW-CX_SB.CX_SB_0616SW"/>
     <cge:Meas_Ref ObjectId="63511"/>
    <cge:TPSR_Ref TObjectID="12032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.956522 -0.000000 0.000000 -0.928571 5247.000000 -1149.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70990">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -943.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15961" ObjectName="SW-CX_SB.CX_SB_156XC1"/>
     <cge:Meas_Ref ObjectId="70990"/>
    <cge:TPSR_Ref TObjectID="15961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5368.000000 -218.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14562" ObjectName="SW-CX_SB.CX_SB_3646SW"/>
     <cge:Meas_Ref ObjectId="68610"/>
    <cge:TPSR_Ref TObjectID="14562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 -222.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12011" ObjectName="SW-CX_SB.CX_SB_07127SW"/>
     <cge:Meas_Ref ObjectId="63490"/>
    <cge:TPSR_Ref TObjectID="12011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63466">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 -222.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11987" ObjectName="SW-CX_SB.CX_SB_07227SW"/>
     <cge:Meas_Ref ObjectId="63466"/>
    <cge:TPSR_Ref TObjectID="11987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63458">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5066.000000 -294.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11979" ObjectName="SW-CX_SB.CX_SB_3026SW"/>
     <cge:Meas_Ref ObjectId="63458"/>
    <cge:TPSR_Ref TObjectID="11979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63457">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5164.000000 -294.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11978" ObjectName="SW-CX_SB.CX_SB_3022SW"/>
     <cge:Meas_Ref ObjectId="63457"/>
    <cge:TPSR_Ref TObjectID="11978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69758">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 -732.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15612" ObjectName="SW-CX_SB.CX_SB_3016SW"/>
     <cge:Meas_Ref ObjectId="69758"/>
    <cge:TPSR_Ref TObjectID="15612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69757">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5163.000000 -732.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15611" ObjectName="SW-CX_SB.CX_SB_3011SW"/>
     <cge:Meas_Ref ObjectId="69757"/>
    <cge:TPSR_Ref TObjectID="15611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68592">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.000000 -221.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14544" ObjectName="SW-CX_SB.CX_SB_06217SW"/>
     <cge:Meas_Ref ObjectId="68592"/>
    <cge:TPSR_Ref TObjectID="14544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68594">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3897.000000 -220.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14546" ObjectName="SW-CX_SB.CX_SB_06317SW"/>
     <cge:Meas_Ref ObjectId="68594"/>
    <cge:TPSR_Ref TObjectID="14546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68596">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -222.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14548" ObjectName="SW-CX_SB.CX_SB_06417SW"/>
     <cge:Meas_Ref ObjectId="68596"/>
    <cge:TPSR_Ref TObjectID="14548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68598">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4110.000000 -227.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14550" ObjectName="SW-CX_SB.CX_SB_06517SW"/>
     <cge:Meas_Ref ObjectId="68598"/>
    <cge:TPSR_Ref TObjectID="14550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68601">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 -223.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14553" ObjectName="SW-CX_SB.CX_SB_06617SW"/>
     <cge:Meas_Ref ObjectId="68601"/>
    <cge:TPSR_Ref TObjectID="14553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68590">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3682.000000 -221.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14542" ObjectName="SW-CX_SB.CX_SB_06117SW"/>
     <cge:Meas_Ref ObjectId="68590"/>
    <cge:TPSR_Ref TObjectID="14542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4110.000000 -135.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12030" ObjectName="SW-CX_SB.CX_SB_06567SW"/>
     <cge:Meas_Ref ObjectId="63509"/>
    <cge:TPSR_Ref TObjectID="12030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63510">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 -131.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12031" ObjectName="SW-CX_SB.CX_SB_06667SW"/>
     <cge:Meas_Ref ObjectId="63510"/>
    <cge:TPSR_Ref TObjectID="12031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69509">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -1026.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15606" ObjectName="SW-CX_SB.CX_SB_19017SW"/>
     <cge:Meas_Ref ObjectId="69509"/>
    <cge:TPSR_Ref TObjectID="15606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69510">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 -941.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15607" ObjectName="SW-CX_SB.CX_SB_19010SW"/>
     <cge:Meas_Ref ObjectId="69510"/>
    <cge:TPSR_Ref TObjectID="15607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70991">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 -1055.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15962" ObjectName="SW-CX_SB.CX_SB_15667SW"/>
     <cge:Meas_Ref ObjectId="70991"/>
    <cge:TPSR_Ref TObjectID="15962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-64506">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 -91.000000)" xlink:href="#switch2:shape24_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12114" ObjectName="SW-CX_SB.CX_SB_07167SW"/>
     <cge:Meas_Ref ObjectId="64506"/>
    <cge:TPSR_Ref TObjectID="12114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-64576">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.804348 -0.000000 0.000000 -1.000000 4652.000000 -423.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12196" ObjectName="SW-CX_SB.CX_SB_09027SW"/>
     <cge:Meas_Ref ObjectId="64576"/>
    <cge:TPSR_Ref TObjectID="12196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 -429.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18054" ObjectName="SW-CX_SB.CX_SB_09017SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="18054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20696" ObjectName="SW-CX_SB.CX_SB_0736SW"/>
     <cge:Meas_Ref ObjectId="106120"/>
    <cge:TPSR_Ref TObjectID="20696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106117">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20693" ObjectName="SW-CX_SB.CX_SB_0732SW"/>
     <cge:Meas_Ref ObjectId="106117"/>
    <cge:TPSR_Ref TObjectID="20693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -197.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20695" ObjectName="SW-CX_SB.CX_SB_07337SW"/>
     <cge:Meas_Ref ObjectId="106119"/>
    <cge:TPSR_Ref TObjectID="20695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20694" ObjectName="SW-CX_SB.CX_SB_0733SW"/>
     <cge:Meas_Ref ObjectId="106118"/>
    <cge:TPSR_Ref TObjectID="20694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20701" ObjectName="SW-CX_SB.CX_SB_0746SW"/>
     <cge:Meas_Ref ObjectId="106127"/>
    <cge:TPSR_Ref TObjectID="20701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20698" ObjectName="SW-CX_SB.CX_SB_0742SW"/>
     <cge:Meas_Ref ObjectId="106124"/>
    <cge:TPSR_Ref TObjectID="20698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106126">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4969.000000 -197.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20700" ObjectName="SW-CX_SB.CX_SB_07437SW"/>
     <cge:Meas_Ref ObjectId="106126"/>
    <cge:TPSR_Ref TObjectID="20700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106125">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 -212.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20699" ObjectName="SW-CX_SB.CX_SB_0743SW"/>
     <cge:Meas_Ref ObjectId="106125"/>
    <cge:TPSR_Ref TObjectID="20699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68609">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5247.000000 -219.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14561" ObjectName="SW-CX_SB.CX_SB_3642SW"/>
     <cge:Meas_Ref ObjectId="68609"/>
    <cge:TPSR_Ref TObjectID="14561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68607">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5246.000000 -349.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14559" ObjectName="SW-CX_SB.CX_SB_3622SW"/>
     <cge:Meas_Ref ObjectId="68607"/>
    <cge:TPSR_Ref TObjectID="14559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5242.000000 -434.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15949" ObjectName="SW-CX_SB.CX_SB_3122SW"/>
     <cge:Meas_Ref ObjectId="70724"/>
    <cge:TPSR_Ref TObjectID="15949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-70761">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5243.000000 -562.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15954" ObjectName="SW-CX_SB.CX_SB_3121SW"/>
     <cge:Meas_Ref ObjectId="70761"/>
    <cge:TPSR_Ref TObjectID="15954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68605">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5248.000000 -651.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14557" ObjectName="SW-CX_SB.CX_SB_3651SW"/>
     <cge:Meas_Ref ObjectId="68605"/>
    <cge:TPSR_Ref TObjectID="14557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68603">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5244.000000 -802.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14555" ObjectName="SW-CX_SB.CX_SB_3631SW"/>
     <cge:Meas_Ref ObjectId="68603"/>
    <cge:TPSR_Ref TObjectID="14555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-68398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5237.000000 -928.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15965" ObjectName="SW-CX_SB.CX_SB_3611SW"/>
     <cge:Meas_Ref ObjectId="68398"/>
    <cge:TPSR_Ref TObjectID="15965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5467.000000 -266.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-69749">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 -673.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15610" ObjectName="SW-CX_SB.CX_SB_1010SW"/>
     <cge:Meas_Ref ObjectId="69749"/>
    <cge:TPSR_Ref TObjectID="15610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-63168">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4578.000000 -672.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11969" ObjectName="SW-CX_SB.CX_SB_1020SW"/>
     <cge:Meas_Ref ObjectId="63168"/>
    <cge:TPSR_Ref TObjectID="11969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5462.000000 -409.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-305071">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5372.000000 -95.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47268" ObjectName="SW-CX_SB.CX_SB_3686SW"/>
     <cge:Meas_Ref ObjectId="305071"/>
    <cge:TPSR_Ref TObjectID="47268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.887097 -0.000000 0.000000 -0.870968 5464.000000 -142.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-305070">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5252.000000 -95.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47266" ObjectName="SW-CX_SB.CX_SB_3682SW"/>
     <cge:Meas_Ref ObjectId="305070"/>
    <cge:TPSR_Ref TObjectID="47266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-305072">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5417.000000 -33.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47270" ObjectName="SW-CX_SB.CX_SB_36867SW"/>
     <cge:Meas_Ref ObjectId="305072"/>
    <cge:TPSR_Ref TObjectID="47270"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieshuangTbai_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4110,-1189 4110,-1160 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11720" ObjectName="AC-110kV.xieshuangTbai_line"/>
    <cge:TPSR_Ref TObjectID="11720_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="4110,-1189 4110,-1160 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.LNShuangEDaTSB" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4585,-1157 4585,-1130 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11856" ObjectName="AC-110kV.LNShuangEDaTSB"/>
    <cge:TPSR_Ref TObjectID="11856_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="4585,-1157 4585,-1130 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shuangtuodaTsb" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5474,-223 5520,-223 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34566" ObjectName="AC-35kV.LN_shuangtuodaTsb"/>
    <cge:TPSR_Ref TObjectID="34566_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="5474,-223 5520,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="SB_YL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yulong_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5567,-807 5616,-807 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37757" ObjectName="AC-35kV.LN_yulong_line"/>
    <cge:TPSR_Ref TObjectID="37757_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="5567,-807 5616,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="SB_TD" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shuangxiaotuo" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5561,-656 5610,-656 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37758" ObjectName="AC-35kV.LN_shuangxiaotuo"/>
    <cge:TPSR_Ref TObjectID="37758_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="5561,-656 5610,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yuzhuanghe" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5569,-933 5634,-933 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37766" ObjectName="AC-35kV.LN_yuzhuanghe"/>
    <cge:TPSR_Ref TObjectID="37766_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="5569,-933 5634,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="SB_ANS" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_ainishan_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5564,-353 5626,-353 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37763" ObjectName="AC-35kV.LN_ainishan_line"/>
    <cge:TPSR_Ref TObjectID="37763_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="5564,-353 5626,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="PAS_T2" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_ShuangTian_sb" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5565,-100 5624,-100 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48151" ObjectName="AC-35kV.LN_ShuangTian_sb"/>
    <cge:TPSR_Ref TObjectID="48151_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="5565,-100 5624,-100 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_19fd570" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4000.000000 -1051.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3c190" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -1027.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3ea50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 -764.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a7c330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.121739 -763.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ae8760" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4047.000000 -233.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa0b60" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3941.000000 -231.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa3480" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3832.000000 -232.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ac9e20" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3726.000000 -232.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1adda30" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4154.000000 -238.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b04de0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4163.000000 -146.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b10d30" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4281.000000 -234.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af8540" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4290.000000 -142.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1951890" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3824.000000 -440.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f6f10" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4774.724638 -233.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a87340" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4649.852174 -233.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a98fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4702.457971 -434.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4c0b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4323.000000 -1037.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4cb10" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4324.000000 -952.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1974db0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4888.000000 -208.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc8920" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5014.000000 -208.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectPoint_Layer"/><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3237" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3237" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3188" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3188" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="4177" y="-750"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="4177" y="-750"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4740" y="-750"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4740" y="-750"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="39" qtmmishow="hidden" width="93" x="3394" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="93" x="3394" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="88" x="3147" y="-685"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="88" x="3147" y="-685"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4119" y="-1004"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4119" y="-1004"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4594" y="-972"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4594" y="-972"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3674" y="-264"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3674" y="-264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3780" y="-264"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3780" y="-264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3889" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3889" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3995" y="-265"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3995" y="-265"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4102" y="-270"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4102" y="-270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4597" y="-265"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4597" y="-265"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4722" y="-265"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4722" y="-265"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4843" y="-285"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4843" y="-285"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4969" y="-285"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4969" y="-285"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5308" y="-248"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5308" y="-248"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5307" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5307" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="5316" y="-510"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="5316" y="-510"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4455" y="-304"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4455" y="-304"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5308" y="-681"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5308" y="-681"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5304" y="-832"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5304" y="-832"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5305" y="-958"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5305" y="-958"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4229" y="-266"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4229" y="-266"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3519" y="-1154"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3519" y="-1154"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3519" y="-1189"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3519" y="-1189"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="88" x="3147" y="-610"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="88" x="3147" y="-610"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5311" y="-131"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5311" y="-131"/></g>
  </g><g id="IosButton_Layer">
   <g DF8003:Layer="PUBLIC">
    <polygon fill="rgb(255,255,255)" points="3395,-1177 3392,-1180 3392,-1131 3395,-1134 3395,-1177" stroke="rgb(255,255,255)"/>
    <polygon fill="rgb(255,255,255)" points="3395,-1177 3392,-1180 3492,-1180 3489,-1177 3395,-1177" stroke="rgb(255,255,255)"/>
    <polygon fill="rgb(127,127,127)" points="3395,-1134 3392,-1131 3492,-1131 3489,-1134 3395,-1134" stroke="rgb(127,127,127)"/>
    <polygon fill="rgb(127,127,127)" points="3489,-1177 3492,-1180 3492,-1131 3489,-1134 3489,-1177" stroke="rgb(127,127,127)"/>
    <rect fill="rgb(255,255,255)" height="43" stroke="rgb(255,255,255)" width="94" x="3395" y="-1177"/>
   <metadata/><rect fill="white" height="43" opacity="0" stroke="white" transform="" width="94" x="3395" y="-1177"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1768320">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4313.560660 -563.353553)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19522f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 -454.000000)" xlink:href="#voltageTransformer:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a63650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -455.000000)" xlink:href="#voltageTransformer:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a999f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.457971 -471.000000)" xlink:href="#voltageTransformer:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a286a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5531.000000 -897.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a2f040">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5516.000000 -694.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19af750">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5572.000000 -308.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4d5a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -1063.000000)" xlink:href="#voltageTransformer:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cfa240">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5522.000000 -261.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2066a50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5520.000000 -138.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1bbe440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4292,-579 4292,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1aaf210@0" ObjectIDZND0="g_17e8410@0" ObjectIDZND1="15647@x" ObjectIDZND2="g_1768320@0" Pin0InfoVect0LinkObjId="g_17e8410_0" Pin0InfoVect1LinkObjId="SW-69816_0" Pin0InfoVect2LinkObjId="g_1768320_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aaf210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4292,-579 4292,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbe6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-604 4292,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="12190@1" ObjectIDZND0="g_1aaf210@0" ObjectIDZND1="g_17e8410@0" ObjectIDZND2="15647@x" Pin0InfoVect0LinkObjId="g_1aaf210_0" Pin0InfoVect1LinkObjId="g_17e8410_0" Pin0InfoVect2LinkObjId="SW-69816_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64539_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-604 4292,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbe900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4322,-558 4322,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1768320@0" ObjectIDZND0="g_17e8410@0" ObjectIDZND1="15647@x" ObjectIDZND2="g_1aaf210@0" Pin0InfoVect0LinkObjId="g_17e8410_0" Pin0InfoVect1LinkObjId="SW-69816_0" Pin0InfoVect2LinkObjId="g_1aaf210_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1768320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4322,-558 4322,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbeb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-594 4364,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_17e8410@0" ObjectIDZND0="g_1768320@0" ObjectIDZND1="g_1aaf210@0" ObjectIDZND2="12190@x" Pin0InfoVect0LinkObjId="g_1768320_0" Pin0InfoVect1LinkObjId="g_1aaf210_0" Pin0InfoVect2LinkObjId="SW-64539_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17e8410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-594 4364,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbedc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4409,-604 4364,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="15647@0" ObjectIDZND0="g_17e8410@0" ObjectIDZND1="g_1768320@0" ObjectIDZND2="g_1aaf210@0" Pin0InfoVect0LinkObjId="g_17e8410_0" Pin0InfoVect1LinkObjId="g_1768320_0" Pin0InfoVect2LinkObjId="g_1aaf210_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4409,-604 4364,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbf020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-604 4322,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_17e8410@0" ObjectIDND1="15647@x" ObjectIDZND0="g_1768320@0" ObjectIDZND1="g_1aaf210@0" ObjectIDZND2="12190@x" Pin0InfoVect0LinkObjId="g_1768320_0" Pin0InfoVect1LinkObjId="g_1aaf210_0" Pin0InfoVect2LinkObjId="SW-64539_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17e8410_0" Pin1InfoVect1LinkObjId="SW-69816_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-604 4322,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbf280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-594 4496,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_17c59c0@0" ObjectIDZND0="g_17f1730@0" ObjectIDZND1="12188@x" ObjectIDZND2="15647@x" Pin0InfoVect0LinkObjId="g_17f1730_0" Pin0InfoVect1LinkObjId="SW-64538_0" Pin0InfoVect2LinkObjId="SW-69816_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c59c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-594 4496,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbf4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-604 4496,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15647@1" ObjectIDZND0="g_17c59c0@0" ObjectIDZND1="g_17f1730@0" ObjectIDZND2="12188@x" Pin0InfoVect0LinkObjId="g_17c59c0_0" Pin0InfoVect1LinkObjId="g_17f1730_0" Pin0InfoVect2LinkObjId="SW-64538_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-604 4496,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbf740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4533,-579 4533,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_17f1730@0" ObjectIDZND0="g_17c59c0@0" ObjectIDZND1="15647@x" ObjectIDZND2="12188@x" Pin0InfoVect0LinkObjId="g_17c59c0_0" Pin0InfoVect1LinkObjId="SW-69816_0" Pin0InfoVect2LinkObjId="SW-64538_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17f1730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4533,-579 4533,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbf9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-604 4533,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_17c59c0@0" ObjectIDND1="15647@x" ObjectIDZND0="g_17f1730@0" ObjectIDZND1="12188@x" Pin0InfoVect0LinkObjId="g_17f1730_0" Pin0InfoVect1LinkObjId="SW-64538_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17c59c0_0" Pin1InfoVect1LinkObjId="SW-69816_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-604 4533,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbfc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4533,-604 4563,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_17c59c0@0" ObjectIDND1="15647@x" ObjectIDND2="g_17f1730@0" ObjectIDZND0="12188@0" Pin0InfoVect0LinkObjId="SW-64538_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17c59c0_0" Pin1InfoVect1LinkObjId="SW-69816_0" Pin1InfoVect2LinkObjId="g_17f1730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4533,-604 4563,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbfe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4292,-604 4322,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1aaf210@0" ObjectIDND1="12190@x" ObjectIDZND0="g_17e8410@0" ObjectIDZND1="15647@x" ObjectIDZND2="g_1768320@0" Pin0InfoVect0LinkObjId="g_17e8410_0" Pin0InfoVect1LinkObjId="SW-69816_0" Pin0InfoVect2LinkObjId="g_1768320_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aaf210_0" Pin1InfoVect1LinkObjId="SW-64539_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4292,-604 4322,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc00c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5352,-933 5352,-891 5368,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="15966@x" ObjectIDND1="14533@x" ObjectIDZND0="g_1a83700@0" Pin0InfoVect0LinkObjId="g_1a83700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68399_0" Pin1InfoVect1LinkObjId="SW-68397_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5352,-933 5352,-891 5368,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc0320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-656 5348,-630 5364,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="14529@x" ObjectIDND1="14558@x" ObjectIDZND0="g_1a82ca0@0" Pin0InfoVect0LinkObjId="g_1a82ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68445_0" Pin1InfoVect1LinkObjId="SW-68606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-656 5348,-630 5364,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc0580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-656 5333,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1a82ca0@0" ObjectIDND1="14558@x" ObjectIDZND0="14529@0" Pin0InfoVect0LinkObjId="SW-68445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a82ca0_0" Pin1InfoVect1LinkObjId="SW-68606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-656 5333,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc07e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5365,-1153 5284,-1153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1abe640@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1abe640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5365,-1153 5284,-1153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc0a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5252,-1153 5217,-1153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="g_1bc0f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5252,-1153 5217,-1153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc0ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5328,-1088 5286,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a523e0@0" ObjectIDZND0="15952@1" Pin0InfoVect0LinkObjId="SW-70748_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a523e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5328,-1088 5286,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc0f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5250,-1088 5217,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15952@0" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="g_1bc0a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5250,-1088 5217,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc1160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5249,-807 5217,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="14555@0" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="g_1bc0a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5249,-807 5217,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc13c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5373,-656 5348,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="14558@0" ObjectIDZND0="14529@x" ObjectIDZND1="g_1a82ca0@0" Pin0InfoVect0LinkObjId="SW-68445_0" Pin0InfoVect1LinkObjId="g_1a82ca0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5373,-656 5348,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc1620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5376,-933 5352,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="15966@0" ObjectIDZND0="g_1a83700@0" ObjectIDZND1="14533@x" Pin0InfoVect0LinkObjId="g_1a83700_0" Pin0InfoVect1LinkObjId="SW-68397_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68399_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5376,-933 5352,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc1880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5436,-656 5436,-703 5466,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="14558@x" ObjectIDND1="37758@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68606_0" Pin1InfoVect1LinkObjId="g_1bc1d40_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5436,-656 5436,-703 5466,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc1ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5409,-656 5436,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="14558@1" ObjectIDZND0="0@x" ObjectIDZND1="37758@1" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_1bc1d40_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5409,-656 5436,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc1d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5436,-656 5561,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="14558@x" ObjectIDZND0="37758@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="SW-68606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5436,-656 5561,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc1fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5467,-354 5409,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d33f90@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="14560@1" Pin0InfoVect0LinkObjId="SW-68608_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d33f90_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="TF-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5467,-354 5409,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc2200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1026 4110,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15960@1" ObjectIDZND0="15959@1" Pin0InfoVect0LinkObjId="SW-70977_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1026 4110,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc2460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4585,-997 4585,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15956@1" ObjectIDZND0="15955@1" Pin0InfoVect0LinkObjId="SW-70904_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70917_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4585,-997 4585,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc26c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4585,-951 4585,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15955@0" ObjectIDZND0="15957@1" Pin0InfoVect0LinkObjId="SW-70917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70904_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4585,-951 4585,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc2920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4585,-920 4585,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15957@0" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_19d3d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4585,-920 4585,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc2b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-774 4070,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b3ea50@0" ObjectIDZND0="15609@0" Pin0InfoVect0LinkObjId="SW-69737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3ea50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-774 4070,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc2de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-712 4050,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_1a7cc60@0" ObjectIDZND0="15610@x" ObjectIDZND1="4843@x" ObjectIDZND2="g_1a7d690@0" Pin0InfoVect0LinkObjId="SW-69749_0" Pin0InfoVect1LinkObjId="g_1bc32a0_0" Pin0InfoVect2LinkObjId="g_1a7d690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a7cc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-712 4050,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc3040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4025,-724 4025,-740 4050,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="15610@0" ObjectIDZND0="g_1a7cc60@0" ObjectIDZND1="4843@x" ObjectIDZND2="g_1a7d690@0" Pin0InfoVect0LinkObjId="g_1a7cc60_0" Pin0InfoVect1LinkObjId="g_1bc32a0_0" Pin0InfoVect2LinkObjId="g_1a7d690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4025,-724 4025,-740 4050,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc32a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-740 4073,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="15610@x" ObjectIDND1="g_1a7cc60@0" ObjectIDZND0="4843@x" ObjectIDZND1="g_1a7d690@0" Pin0InfoVect0LinkObjId="g_1bc3500_0" Pin0InfoVect1LinkObjId="g_1a7d690_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-69749_0" Pin1InfoVect1LinkObjId="g_1a7cc60_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-740 4073,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc3500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4073,-740 4118,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="15610@x" ObjectIDND1="g_1a7cc60@0" ObjectIDND2="g_1a7d690@0" ObjectIDZND0="4843@x" Pin0InfoVect0LinkObjId="g_1bc32a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-69749_0" Pin1InfoVect1LinkObjId="g_1a7cc60_0" Pin1InfoVect2LinkObjId="g_1a7d690_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4073,-740 4118,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc3760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-711 4612,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="g_1b52910@0" ObjectIDZND0="g_1b53200@0" ObjectIDZND1="12047@x" ObjectIDZND2="11969@x" Pin0InfoVect0LinkObjId="g_1b53200_0" Pin0InfoVect1LinkObjId="g_1bc39c0_0" Pin0InfoVect2LinkObjId="SW-63168_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b52910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-711 4612,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc39c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4635,-739 4680,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer" ObjectIDND0="g_1b53200@0" ObjectIDND1="g_1b52910@0" ObjectIDND2="11969@x" ObjectIDZND0="12047@x" Pin0InfoVect0LinkObjId="g_19d5060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b53200_0" Pin1InfoVect1LinkObjId="g_1b52910_0" Pin1InfoVect2LinkObjId="SW-63168_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4635,-739 4680,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bc3c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4073,-714 4073,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1a7d690@0" ObjectIDZND0="4843@x" ObjectIDZND1="15610@x" ObjectIDZND2="g_1a7cc60@0" Pin0InfoVect0LinkObjId="g_1bc32a0_0" Pin0InfoVect1LinkObjId="SW-69749_0" Pin0InfoVect2LinkObjId="g_1a7cc60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a7d690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4073,-714 4073,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc3e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-695 4167,-604 4222,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4843@x" ObjectIDZND0="12190@0" Pin0InfoVect0LinkObjId="SW-64539_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc32a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-695 4167,-604 4222,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc40e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-351 3880,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14545@0" Pin0InfoVect0LinkObjId="SW-68593_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc45a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-351 3880,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc4340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-351 3665,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14541@0" Pin0InfoVect0LinkObjId="SW-68589_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc45a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-351 3665,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc45a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-325 4347,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15947@0" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_1bcc380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-325 4347,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc4800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-227 4008,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14525@x" ObjectIDND1="g_1acab30@0" ObjectIDZND0="14548@0" Pin0InfoVect0LinkObjId="SW-68596_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68338_0" Pin1InfoVect1LinkObjId="g_1acab30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-227 4008,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc4a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-227 4052,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14548@1" ObjectIDZND0="g_1ae8760@0" Pin0InfoVect0LinkObjId="g_1ae8760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68596_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-227 4052,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc4cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-244 3986,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14525@0" ObjectIDZND0="g_1acab30@0" ObjectIDZND1="14548@x" Pin0InfoVect0LinkObjId="g_1acab30_0" Pin0InfoVect1LinkObjId="SW-68596_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-244 3986,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc4f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-269 3880,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14524@1" ObjectIDZND0="14545@1" Pin0InfoVect0LinkObjId="SW-68593_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68315_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-269 3880,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc5180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-225 3902,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14524@x" ObjectIDND1="g_1aca750@0" ObjectIDZND0="14546@0" Pin0InfoVect0LinkObjId="SW-68594_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68315_0" Pin1InfoVect1LinkObjId="g_1aca750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-225 3902,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc53e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3938,-225 3946,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14546@1" ObjectIDZND0="g_1aa0b60@0" Pin0InfoVect0LinkObjId="g_1aa0b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68594_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3938,-225 3946,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc5640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-242 3880,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14524@0" ObjectIDZND0="g_1aca750@0" ObjectIDZND1="14546@x" Pin0InfoVect0LinkObjId="g_1aca750_0" Pin0InfoVect1LinkObjId="SW-68594_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-242 3880,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc58a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-270 3771,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14523@1" ObjectIDZND0="14543@1" Pin0InfoVect0LinkObjId="SW-68591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-270 3771,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc5b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-226 3793,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14523@x" ObjectIDND1="g_1acaf10@0" ObjectIDZND0="14544@0" Pin0InfoVect0LinkObjId="SW-68592_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68292_0" Pin1InfoVect1LinkObjId="g_1acaf10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-226 3793,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc5d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3829,-226 3837,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14544@1" ObjectIDZND0="g_1aa3480@0" Pin0InfoVect0LinkObjId="g_1aa3480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68592_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3829,-226 3837,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc5fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-243 3771,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14523@0" ObjectIDZND0="g_1acaf10@0" ObjectIDZND1="14544@x" Pin0InfoVect0LinkObjId="g_1acaf10_0" Pin0InfoVect1LinkObjId="SW-68592_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-243 3771,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc6220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-270 3665,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14522@1" ObjectIDZND0="14541@1" Pin0InfoVect0LinkObjId="SW-68589_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-270 3665,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc6480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-226 3687,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="14522@x" ObjectIDND1="g_1acb2f0@0" ObjectIDZND0="14542@0" Pin0InfoVect0LinkObjId="SW-68590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68269_0" Pin1InfoVect1LinkObjId="g_1acb2f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-226 3687,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc66e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-226 3731,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14542@1" ObjectIDZND0="g_1ac9e20@0" Pin0InfoVect0LinkObjId="g_1ac9e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-226 3731,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc6940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-243 3665,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14522@0" ObjectIDZND0="g_1acb2f0@0" ObjectIDZND1="14542@x" Pin0InfoVect0LinkObjId="g_1acb2f0_0" Pin0InfoVect1LinkObjId="SW-68590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-243 3665,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc6ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-271 3986,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14525@1" ObjectIDZND0="14547@1" Pin0InfoVect0LinkObjId="SW-68595_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68338_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-271 3986,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc6e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-131 4010,-131 4010,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="12035@x" ObjectIDND1="16023@x" ObjectIDZND0="g_1aca3a0@0" Pin0InfoVect0LinkObjId="g_1aca3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63514_0" Pin1InfoVect1LinkObjId="EC-CX_SB.064Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-131 4010,-131 4010,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc7060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-132 3904,-132 3904,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16022@x" ObjectIDND1="12034@x" ObjectIDZND0="g_1aa39d0@0" Pin0InfoVect0LinkObjId="g_1aa39d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SB.063Ld_0" Pin1InfoVect1LinkObjId="SW-63513_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-132 3904,-132 3904,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc72c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-132 3795,-132 3795,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="12033@x" ObjectIDND1="16021@x" ObjectIDZND0="g_1aa1490@0" Pin0InfoVect0LinkObjId="g_1aa1490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63512_0" Pin1InfoVect1LinkObjId="EC-CX_SB.062Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-132 3795,-132 3795,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc7520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-132 3880,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1aa39d0@0" ObjectIDND1="12034@x" ObjectIDZND0="16022@0" Pin0InfoVect0LinkObjId="EC-CX_SB.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aa39d0_0" Pin1InfoVect1LinkObjId="SW-63513_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-132 3880,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc7780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-148 3880,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="12034@1" ObjectIDZND0="16022@x" ObjectIDZND1="g_1aa39d0@0" Pin0InfoVect0LinkObjId="EC-CX_SB.063Ld_0" Pin0InfoVect1LinkObjId="g_1aa39d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-148 3880,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc79e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-225 3880,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="14524@x" ObjectIDND1="14546@x" ObjectIDZND0="g_1aca750@1" Pin0InfoVect0LinkObjId="g_1aca750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68315_0" Pin1InfoVect1LinkObjId="SW-68594_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-225 3880,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc7c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-194 3880,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1aca750@0" ObjectIDZND0="12034@0" Pin0InfoVect0LinkObjId="SW-63513_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aca750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3880,-194 3880,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc7ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-131 3986,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="12035@x" ObjectIDND1="g_1aca3a0@0" ObjectIDZND0="16023@0" Pin0InfoVect0LinkObjId="EC-CX_SB.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63514_0" Pin1InfoVect1LinkObjId="g_1aca3a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-131 3986,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc8100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-146 3986,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="12035@1" ObjectIDZND0="16023@x" ObjectIDZND1="g_1aca3a0@0" Pin0InfoVect0LinkObjId="EC-CX_SB.064Ld_0" Pin0InfoVect1LinkObjId="g_1aca3a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-146 3986,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-182 3986,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12035@0" ObjectIDZND0="g_1acab30@0" Pin0InfoVect0LinkObjId="g_1acab30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-182 3986,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc85c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-213 3986,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1acab30@1" ObjectIDZND0="14525@x" ObjectIDZND1="14548@x" Pin0InfoVect0LinkObjId="SW-68338_0" Pin0InfoVect1LinkObjId="SW-68596_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acab30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-213 3986,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc8820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-132 3771,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="12033@x" ObjectIDND1="g_1aa1490@0" ObjectIDZND0="16021@0" Pin0InfoVect0LinkObjId="EC-CX_SB.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63512_0" Pin1InfoVect1LinkObjId="g_1aa1490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-132 3771,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc8a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-147 3771,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="12033@1" ObjectIDZND0="16021@x" ObjectIDZND1="g_1aa1490@0" Pin0InfoVect0LinkObjId="EC-CX_SB.062Ld_0" Pin0InfoVect1LinkObjId="g_1aa1490_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63512_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-147 3771,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc8ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-183 3771,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12033@0" ObjectIDZND0="g_1acaf10@0" Pin0InfoVect0LinkObjId="g_1acaf10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-183 3771,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc8f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-212 3771,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1acaf10@1" ObjectIDZND0="14523@x" ObjectIDZND1="14544@x" Pin0InfoVect0LinkObjId="SW-68292_0" Pin0InfoVect1LinkObjId="SW-68592_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acaf10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-212 3771,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc91a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-226 3665,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="14522@x" ObjectIDND1="14542@x" ObjectIDZND0="g_1acb2f0@1" Pin0InfoVect0LinkObjId="g_1acb2f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68269_0" Pin1InfoVect1LinkObjId="SW-68590_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-226 3665,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc9400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-351 3771,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14543@0" Pin0InfoVect0LinkObjId="SW-68591_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc45a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-351 3771,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc9660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-351 3986,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14547@0" Pin0InfoVect0LinkObjId="SW-68595_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc45a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-351 3986,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc98c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-351 4220,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="14552@0" Pin0InfoVect0LinkObjId="SW-68600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc45a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-351 4220,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc9b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-232 4159,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14550@1" ObjectIDZND0="g_1adda30@0" Pin0InfoVect0LinkObjId="g_1adda30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-232 4159,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc9d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-187 4093,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="14551@0" ObjectIDZND0="g_1a559d0@0" Pin0InfoVect0LinkObjId="g_1a559d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-187 4093,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc9fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-232 4093,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="14550@0" ObjectIDZND0="14526@x" ObjectIDZND1="g_1a559d0@0" Pin0InfoVect0LinkObjId="SW-68359_0" Pin0InfoVect1LinkObjId="g_1a559d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-232 4093,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bca240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-218 4093,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1a559d0@1" ObjectIDZND0="14526@x" ObjectIDZND1="14550@x" Pin0InfoVect0LinkObjId="SW-68359_0" Pin0InfoVect1LinkObjId="SW-68598_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a559d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-218 4093,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bca4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-232 4093,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1a559d0@0" ObjectIDND1="14550@x" ObjectIDZND0="14526@0" Pin0InfoVect0LinkObjId="SW-68359_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a559d0_0" Pin1InfoVect1LinkObjId="SW-68598_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-232 4093,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bca700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-151 4093,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="14551@1" ObjectIDZND0="28611@x" ObjectIDZND1="12030@x" Pin0InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb1_0" Pin0InfoVect1LinkObjId="SW-63509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68599_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-151 4093,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bca960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-131 4093,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28611@0" ObjectIDZND0="14551@x" ObjectIDZND1="12030@x" Pin0InfoVect0LinkObjId="SW-68599_0" Pin0InfoVect1LinkObjId="SW-63509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-131 4093,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcabc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-228 4286,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="14553@1" ObjectIDZND0="g_1b10d30@0" Pin0InfoVect0LinkObjId="g_1b10d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68601_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-228 4286,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-272 4220,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14527@1" ObjectIDZND0="14552@1" Pin0InfoVect0LinkObjId="SW-68600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-272 4220,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcb080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-183 4220,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="14554@0" ObjectIDZND0="g_1b13e80@0" Pin0InfoVect0LinkObjId="g_1b13e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68602_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-183 4220,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcb2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-228 4220,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="14553@0" ObjectIDZND0="14527@x" ObjectIDZND1="g_1b13e80@0" Pin0InfoVect0LinkObjId="SW-68377_0" Pin0InfoVect1LinkObjId="g_1b13e80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-228 4220,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcb540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-214 4220,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1b13e80@1" ObjectIDZND0="14527@x" ObjectIDZND1="14553@x" Pin0InfoVect0LinkObjId="SW-68377_0" Pin0InfoVect1LinkObjId="SW-68601_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b13e80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-214 4220,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcb7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-228 4220,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1b13e80@0" ObjectIDND1="14553@x" ObjectIDZND0="14527@0" Pin0InfoVect0LinkObjId="SW-68377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b13e80_0" Pin1InfoVect1LinkObjId="SW-68601_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-228 4220,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-147 4220,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="14554@1" ObjectIDZND0="28612@x" ObjectIDZND1="12031@x" Pin0InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb3_0" Pin0InfoVect1LinkObjId="SW-63510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68602_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-147 4220,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcbc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-127 4220,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28612@0" ObjectIDZND0="14554@x" ObjectIDZND1="12031@x" Pin0InfoVect0LinkObjId="SW-68602_0" Pin0InfoVect1LinkObjId="SW-63510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-127 4220,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcbec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-434 3760,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="15946@x" ObjectIDND1="g_1950d30@0" ObjectIDND2="18054@x" ObjectIDZND0="g_19522f0@0" Pin0InfoVect0LinkObjId="g_19522f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-70705_0" Pin1InfoVect1LinkObjId="g_1950d30_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-434 3760,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcc120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-409 3760,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="15946@0" ObjectIDZND0="g_19522f0@0" ObjectIDZND1="g_1950d30@0" ObjectIDZND2="18054@x" Pin0InfoVect0LinkObjId="g_19522f0_0" Pin0InfoVect1LinkObjId="g_1950d30_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-409 3760,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcc380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-373 3760,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15946@1" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_1bc45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-373 3760,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcc5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-434 3718,-434 3718,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_19522f0@0" ObjectIDND1="15946@x" ObjectIDND2="18054@x" ObjectIDZND0="g_1950d30@0" Pin0InfoVect0LinkObjId="g_1950d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_19522f0_0" Pin1InfoVect1LinkObjId="SW-70705_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-434 3718,-434 3718,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcc840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3905,-374 3905,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_1bc45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3905,-374 3905,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bccaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3905,-410 3905,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1a3f0c0@1" Pin0InfoVect0LinkObjId="g_1a3f0c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3905,-410 3905,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bccd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3905,-445 3905,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1a3f0c0@0" ObjectIDZND0="g_1a63650@0" Pin0InfoVect0LinkObjId="g_1a63650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a3f0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3905,-445 3905,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bccf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-270 4347,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a24470@0" ObjectIDZND0="15947@1" Pin0InfoVect0LinkObjId="SW-70711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a24470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-270 4347,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcd1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-161 4347,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1a42150@0" Pin0InfoVect0LinkObjId="g_1a42150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-161 4347,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcd420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-228 4347,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1a42150@1" ObjectIDZND0="g_1a24470@1" Pin0InfoVect0LinkObjId="g_1a24470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a42150_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-228 4347,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bcd680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-849 4119,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15608@1" ObjectIDZND0="15632@1" Pin0InfoVect0LinkObjId="SW-69736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-849 4119,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19d38a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-808 4119,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15633@1" ObjectIDZND0="15608@0" Pin0InfoVect0LinkObjId="SW-69725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-808 4119,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19d3b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-774 4119,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="15609@1" ObjectIDZND0="4843@x" ObjectIDZND1="15633@x" Pin0InfoVect0LinkObjId="g_1bc32a0_0" Pin0InfoVect1LinkObjId="SW-69736_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-774 4119,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19d3d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4681,-886 4681,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="12071@0" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_1bc2920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63584_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4681,-886 4681,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19d3fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-759 4119,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4843@1" ObjectIDZND0="15633@x" ObjectIDZND1="15609@x" Pin0InfoVect0LinkObjId="SW-69736_0" Pin0InfoVect1LinkObjId="SW-69737_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc32a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-759 4119,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19d4220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-773 4119,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4843@x" ObjectIDND1="15609@x" ObjectIDZND0="15633@0" Pin0InfoVect0LinkObjId="SW-69736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bc32a0_0" Pin1InfoVect1LinkObjId="SW-69737_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-773 4119,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19d4480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4681,-758 4681,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="12047@1" ObjectIDZND0="12072@x" ObjectIDZND1="g_1a9d000@0" ObjectIDZND2="11975@x" Pin0InfoVect0LinkObjId="SW-63584_0" Pin0InfoVect1LinkObjId="g_1a9d000_0" Pin0InfoVect2LinkObjId="SW-63432_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc39c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4681,-758 4681,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19d46e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4681,-773 4681,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="12047@x" ObjectIDND1="g_1a9d000@0" ObjectIDND2="11975@x" ObjectIDZND0="12072@0" Pin0InfoVect0LinkObjId="SW-63584_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bc39c0_0" Pin1InfoVect1LinkObjId="g_1a9d000_0" Pin1InfoVect2LinkObjId="SW-63432_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4681,-773 4681,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d4940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-720 4200,-720 4200,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4843@2" ObjectIDZND0="15616@0" Pin0InfoVect0LinkObjId="SW-69769_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc32a0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-720 4200,-720 4200,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d4ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-462 4200,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15616@1" ObjectIDZND0="15614@1" Pin0InfoVect0LinkObjId="SW-69765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-462 4200,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d4e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-417 4200,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15614@0" ObjectIDZND0="15615@0" Pin0InfoVect0LinkObjId="SW-69768_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-417 4200,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d5060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4598,-604 4632,-604 4680,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="12188@1" ObjectIDZND0="12047@x" Pin0InfoVect0LinkObjId="g_1bc39c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64538_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4598,-604 4632,-604 4680,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d52c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-351 4713,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11962@0" ObjectIDZND0="11986@0" Pin0InfoVect0LinkObjId="SW-63465_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d6f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-351 4713,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d5520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4771,-227 4780,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11987@1" ObjectIDZND0="g_19f6f10@0" Pin0InfoVect0LinkObjId="g_19f6f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63466_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4771,-227 4780,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d5780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-271 4713,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11985@1" ObjectIDZND0="11986@1" Pin0InfoVect0LinkObjId="SW-63465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63464_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-271 4713,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d59e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-182 4713,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12028@0" ObjectIDZND0="g_1a84060@0" Pin0InfoVect0LinkObjId="g_1a84060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63507_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-182 4713,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d5c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4588,-351 4588,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11962@0" ObjectIDZND0="12010@0" Pin0InfoVect0LinkObjId="SW-63489_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d6f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4588,-351 4588,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d5ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-227 4655,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="12011@1" ObjectIDZND0="g_1a87340@0" Pin0InfoVect0LinkObjId="g_1a87340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63490_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-227 4655,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d6100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4588,-271 4588,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12008@1" ObjectIDZND0="12010@1" Pin0InfoVect0LinkObjId="SW-63489_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63884_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4588,-271 4588,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d6360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4588,-182 4588,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12012@0" ObjectIDZND0="g_1a94020@0" Pin0InfoVect0LinkObjId="g_1a94020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4588,-182 4588,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d65c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-227 4588,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="12011@0" ObjectIDZND0="g_1a94020@0" ObjectIDZND1="12008@x" Pin0InfoVect0LinkObjId="g_1a94020_0" Pin0InfoVect1LinkObjId="SW-63884_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-227 4588,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d6820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4588,-213 4588,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1a94020@1" ObjectIDZND0="12008@x" ObjectIDZND1="12011@x" Pin0InfoVect0LinkObjId="SW-63884_0" Pin0InfoVect1LinkObjId="SW-63490_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a94020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4588,-213 4588,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d6a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4588,-227 4588,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1a94020@0" ObjectIDND1="12011@x" ObjectIDZND0="12008@0" Pin0InfoVect0LinkObjId="SW-63884_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a94020_0" Pin1InfoVect1LinkObjId="SW-63490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4588,-227 4588,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d6ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-351 4403,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11961@0" ObjectIDZND0="15951@0" Pin0InfoVect0LinkObjId="SW-70736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc45a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-351 4403,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d6f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-334 4526,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="12036@0" ObjectIDZND0="11962@0" Pin0InfoVect0LinkObjId="g_19d7b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63515_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-334 4526,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d71a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-297 4403,-280 4452,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15951@1" ObjectIDZND0="15963@1" Pin0InfoVect0LinkObjId="SW-70734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-297 4403,-280 4452,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d7400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-280 4526,-280 4526,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15963@0" ObjectIDZND0="12036@1" Pin0InfoVect0LinkObjId="SW-63515_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-280 4526,-280 4526,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d7660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-462 4756,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11966@1" ObjectIDZND0="11963@1" Pin0InfoVect0LinkObjId="SW-63162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63165_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-462 4756,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d78c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-417 4756,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11963@0" ObjectIDZND0="11965@0" Pin0InfoVect0LinkObjId="SW-63164_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-417 4756,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d7b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-365 4756,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11965@1" ObjectIDZND0="11962@0" Pin0InfoVect0LinkObjId="g_19d6f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63164_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-365 4756,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d7d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-403 4632,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="12027@0" ObjectIDZND0="g_199dd00@0" ObjectIDZND1="g_1a3e760@0" ObjectIDZND2="12196@x" Pin0InfoVect0LinkObjId="g_199dd00_0" Pin0InfoVect1LinkObjId="g_1a3e760_0" Pin0InfoVect2LinkObjId="SW-64576_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-403 4632,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d7fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-428 4590,-428 4590,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12027@x" ObjectIDND1="g_199dd00@0" ObjectIDND2="12196@x" ObjectIDZND0="g_1a3e760@0" Pin0InfoVect0LinkObjId="g_1a3e760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63506_0" Pin1InfoVect1LinkObjId="g_199dd00_0" Pin1InfoVect2LinkObjId="SW-64576_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-428 4590,-428 4590,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-367 4632,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="12027@1" ObjectIDZND0="11962@0" Pin0InfoVect0LinkObjId="g_19d6f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63506_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-367 4632,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d84a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5151,-737 5168,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15613@0" ObjectIDZND0="15611@0" Pin0InfoVect0LinkObjId="SW-69757_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5151,-737 5168,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19d8700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4681,-773 4725,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12072@x" ObjectIDND1="12047@x" ObjectIDND2="11975@x" ObjectIDZND0="g_1a9d000@0" Pin0InfoVect0LinkObjId="g_1a9d000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63584_0" Pin1InfoVect1LinkObjId="g_1bc39c0_0" Pin1InfoVect2LinkObjId="SW-63432_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4681,-773 4725,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d8960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-719 4756,-719 4756,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="12047@2" ObjectIDZND0="g_1a9ddb0@0" ObjectIDZND1="11966@x" Pin0InfoVect0LinkObjId="g_1a9ddb0_0" Pin0InfoVect1LinkObjId="SW-63165_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc39c0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-719 4756,-719 4756,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d8bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4711,-631 4711,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_1a9eb60@0" ObjectIDZND0="12047@x" ObjectIDZND1="11979@x" Pin0InfoVect0LinkObjId="g_1bc39c0_0" Pin0InfoVect1LinkObjId="SW-63458_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a9eb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4711,-631 4711,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d8e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4680,-678 4680,-649 4711,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="12047@0" ObjectIDZND0="g_1a9eb60@0" ObjectIDZND1="11979@x" Pin0InfoVect0LinkObjId="g_1a9eb60_0" Pin0InfoVect1LinkObjId="SW-63458_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc39c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4680,-678 4680,-649 4711,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d9080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4711,-649 5049,-649 5049,-299 5071,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="12047@x" ObjectIDND1="g_1a9eb60@0" ObjectIDZND0="11979@0" Pin0InfoVect0LinkObjId="SW-63458_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bc39c0_0" Pin1InfoVect1LinkObjId="g_1a9eb60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4711,-649 5049,-649 5049,-299 5071,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d92f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5108,-737 5124,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15612@1" ObjectIDZND0="15613@1" Pin0InfoVect0LinkObjId="SW-69759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69758_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5108,-737 5124,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19d9550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5525,-905 5536,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1a286a0@0" Pin0InfoVect0LinkObjId="g_1a286a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5525,-905 5536,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19d97b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5510,-702 5521,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1a2f040@0" Pin0InfoVect0LinkObjId="g_1a2f040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5510,-702 5521,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d9a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5327,-10 5285,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_19b01c0@0" ObjectIDZND0="15953@1" Pin0InfoVect0LinkObjId="SW-70754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19b01c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5327,-10 5285,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d9c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5249,-10 5217,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15953@0" ObjectIDZND0="12193@0" Pin0InfoVect0LinkObjId="g_1d3d580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5249,-10 5217,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19d9ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-1072 4284,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1a4b590@0" ObjectIDND1="15605@x" ObjectIDND2="15606@x" ObjectIDZND0="g_1a4d5a0@0" Pin0InfoVect0LinkObjId="g_1a4d5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a4b590_0" Pin1InfoVect1LinkObjId="SW-69508_0" Pin1InfoVect2LinkObjId="SW-69509_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-1072 4284,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19da130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-1072 4253,-1086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1a4d5a0@0" ObjectIDND1="15605@x" ObjectIDND2="15606@x" ObjectIDZND0="g_1a4b590@0" Pin0InfoVect0LinkObjId="g_1a4b590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a4d5a0_0" Pin1InfoVect1LinkObjId="SW-69508_0" Pin1InfoVect2LinkObjId="SW-69509_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-1072 4253,-1086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19da390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4635,-713 4635,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1b53200@0" ObjectIDZND0="12047@x" ObjectIDZND1="g_1b52910@0" ObjectIDZND2="11969@x" Pin0InfoVect0LinkObjId="g_1bc39c0_0" Pin0InfoVect1LinkObjId="g_1b52910_0" Pin0InfoVect2LinkObjId="SW-63168_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b53200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4635,-713 4635,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19da5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4635,-739 4612,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1b53200@0" ObjectIDND1="12047@x" ObjectIDZND0="g_1b52910@0" ObjectIDZND1="11969@x" Pin0InfoVect0LinkObjId="g_1b52910_0" Pin0InfoVect1LinkObjId="SW-63168_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b53200_0" Pin1InfoVect1LinkObjId="g_1bc39c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4635,-739 4612,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19da850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4587,-723 4587,-739 4612,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="11969@0" ObjectIDZND0="g_1b53200@0" ObjectIDZND1="12047@x" ObjectIDZND2="g_1b52910@0" Pin0InfoVect0LinkObjId="g_1b53200_0" Pin0InfoVect1LinkObjId="g_1bc39c0_0" Pin0InfoVect2LinkObjId="g_1b52910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4587,-723 4587,-739 4612,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19daab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5285,-807 5303,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14555@1" ObjectIDZND0="14528@1" Pin0InfoVect0LinkObjId="SW-68422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68603_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5285,-807 5303,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19dad10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5330,-807 5352,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14528@0" ObjectIDZND0="g_1b1b840@0" ObjectIDZND1="14556@x" Pin0InfoVect0LinkObjId="g_1b1b840_0" Pin0InfoVect1LinkObjId="SW-68604_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5330,-807 5352,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19daf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5368,-781 5352,-781 5352,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1b1b840@0" ObjectIDZND0="14528@x" ObjectIDZND1="14556@x" Pin0InfoVect0LinkObjId="SW-68422_0" Pin0InfoVect1LinkObjId="SW-68604_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b1b840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5368,-781 5352,-781 5352,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19db1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5352,-807 5373,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1b1b840@0" ObjectIDND1="14528@x" ObjectIDZND0="14556@0" Pin0InfoVect0LinkObjId="SW-68604_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b1b840_0" Pin1InfoVect1LinkObjId="SW-68422_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5352,-807 5373,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19db430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5349,-354 5333,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1ab5ae0@0" ObjectIDND1="14560@x" ObjectIDZND0="14530@0" Pin0InfoVect0LinkObjId="SW-68468_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ab5ae0_0" Pin1InfoVect1LinkObjId="SW-68608_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5349,-354 5333,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19db690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5365,-328 5349,-328 5349,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1ab5ae0@0" ObjectIDZND0="14530@x" ObjectIDZND1="14560@x" Pin0InfoVect0LinkObjId="SW-68468_0" Pin0InfoVect1LinkObjId="SW-68608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ab5ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5365,-328 5349,-328 5349,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19db8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5349,-354 5373,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1ab5ae0@0" ObjectIDND1="14530@x" ObjectIDZND0="14560@0" Pin0InfoVect0LinkObjId="SW-68608_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ab5ae0_0" Pin1InfoVect1LinkObjId="SW-68468_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5349,-354 5373,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dbb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-132 3689,-132 3689,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="12032@x" ObjectIDND1="16028@x" ObjectIDZND0="g_1ae9090@0" Pin0InfoVect0LinkObjId="g_1ae9090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63511_0" Pin1InfoVect1LinkObjId="EC-CX_SB.061Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-132 3689,-132 3689,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dbdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-132 3665,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="12032@x" ObjectIDND1="g_1ae9090@0" ObjectIDZND0="16028@0" Pin0InfoVect0LinkObjId="EC-CX_SB.061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63511_0" Pin1InfoVect1LinkObjId="g_1ae9090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-132 3665,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dc010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-193 3665,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1acb2f0@0" ObjectIDZND0="12032@0" Pin0InfoVect0LinkObjId="SW-63511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acb2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-193 3665,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dc270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-146 3665,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="12032@1" ObjectIDZND0="16028@x" ObjectIDZND1="g_1ae9090@0" Pin0InfoVect0LinkObjId="EC-CX_SB.061Ld_0" Pin0InfoVect1LinkObjId="g_1ae9090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-146 3665,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dc4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4735,-227 4713,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="11987@0" ObjectIDZND0="g_1a84060@0" ObjectIDZND1="11985@x" Pin0InfoVect0LinkObjId="g_1a84060_0" Pin0InfoVect1LinkObjId="SW-63464_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63466_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4735,-227 4713,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dc730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-244 4713,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11985@0" ObjectIDZND0="g_1a84060@0" ObjectIDZND1="11987@x" Pin0InfoVect0LinkObjId="g_1a84060_0" Pin0InfoVect1LinkObjId="SW-63466_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63464_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-244 4713,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dc990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-227 4713,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11985@x" ObjectIDND1="11987@x" ObjectIDZND0="g_1a84060@1" Pin0InfoVect0LinkObjId="g_1a84060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63464_0" Pin1InfoVect1LinkObjId="SW-63466_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-227 4713,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dcbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-130 4737,-130 4737,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="12028@x" ObjectIDND1="16024@x" ObjectIDZND0="g_19fa060@0" Pin0InfoVect0LinkObjId="g_19fa060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63507_0" Pin1InfoVect1LinkObjId="EC-CX_SB.072Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-130 4737,-130 4737,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-146 4713,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="12028@1" ObjectIDZND0="g_19fa060@0" ObjectIDZND1="16024@x" Pin0InfoVect0LinkObjId="g_19fa060_0" Pin0InfoVect1LinkObjId="EC-CX_SB.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63507_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-146 4713,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dd0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-130 4713,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="12028@x" ObjectIDND1="g_19fa060@0" ObjectIDZND0="16024@0" Pin0InfoVect0LinkObjId="EC-CX_SB.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-63507_0" Pin1InfoVect1LinkObjId="g_19fa060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-130 4713,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dd310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-99 4601,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="12114@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64506_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-99 4601,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dd570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-428 4632,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12027@x" ObjectIDND1="g_1a3e760@0" ObjectIDND2="12196@x" ObjectIDZND0="g_199dd00@1" Pin0InfoVect0LinkObjId="g_199dd00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63506_0" Pin1InfoVect1LinkObjId="g_1a3e760_0" Pin1InfoVect2LinkObjId="SW-64576_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-428 4632,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dd7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-463 4632,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_199dd00@0" ObjectIDZND0="g_1a999f0@0" Pin0InfoVect0LinkObjId="g_1a999f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_199dd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-463 4632,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19dda30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-1137 4110,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_1b73030@0" ObjectIDZND0="g_1b73e40@0" ObjectIDZND1="11720@1" ObjectIDZND2="15960@x" Pin0InfoVect0LinkObjId="g_1b73e40_0" Pin0InfoVect1LinkObjId="g_19de150_1" Pin0InfoVect2LinkObjId="SW-70990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b73030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-1137 4110,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19ddc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-1137 4135,-1146 4110,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1b73e40@0" ObjectIDZND0="g_1b73030@0" ObjectIDZND1="15960@x" ObjectIDZND2="15962@x" Pin0InfoVect0LinkObjId="g_1b73030_0" Pin0InfoVect1LinkObjId="SW-70990_0" Pin0InfoVect2LinkObjId="SW-70991_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b73e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-1137 4135,-1146 4110,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19ddef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1137 4110,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1b73030@0" ObjectIDND1="15960@x" ObjectIDND2="15962@x" ObjectIDZND0="g_1b73e40@0" ObjectIDZND1="11720@1" Pin0InfoVect0LinkObjId="g_1b73e40_0" Pin0InfoVect1LinkObjId="g_19de150_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b73030_0" Pin1InfoVect1LinkObjId="SW-70990_0" Pin1InfoVect2LinkObjId="SW-70991_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1137 4110,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19de150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1146 4110,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1b73030@0" ObjectIDND1="15960@x" ObjectIDND2="15962@x" ObjectIDZND0="11720@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b73030_0" Pin1InfoVect1LinkObjId="SW-70990_0" Pin1InfoVect2LinkObjId="SW-70991_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1146 4110,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19de3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4572,-1111 4585,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_1affb50@0" ObjectIDZND0="g_1b00c70@0" ObjectIDZND1="11856@1" ObjectIDZND2="15956@x" Pin0InfoVect0LinkObjId="g_1b00c70_0" Pin0InfoVect1LinkObjId="g_19dead0_1" Pin0InfoVect2LinkObjId="SW-70917_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1affb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4572,-1111 4585,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19de610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-1111 4611,-1120 4585,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1b00c70@0" ObjectIDZND0="g_1affb50@0" ObjectIDZND1="15956@x" ObjectIDZND2="15958@x" Pin0InfoVect0LinkObjId="g_1affb50_0" Pin0InfoVect1LinkObjId="SW-70917_0" Pin0InfoVect2LinkObjId="SW-70918_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b00c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-1111 4611,-1120 4585,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19de870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4585,-1111 4585,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1affb50@0" ObjectIDND1="15956@x" ObjectIDND2="15958@x" ObjectIDZND0="g_1b00c70@0" ObjectIDZND1="11856@1" Pin0InfoVect0LinkObjId="g_1b00c70_0" Pin0InfoVect1LinkObjId="g_19dead0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1affb50_0" Pin1InfoVect1LinkObjId="SW-70917_0" Pin1InfoVect2LinkObjId="SW-70918_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4585,-1111 4585,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19dead0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4585,-1120 4585,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1affb50@0" ObjectIDND1="15956@x" ObjectIDND2="15958@x" ObjectIDZND0="11856@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1affb50_0" Pin1InfoVect1LinkObjId="SW-70917_0" Pin1InfoVect2LinkObjId="SW-70918_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4585,-1120 4585,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19ded30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-983 4110,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="15959@0" ObjectIDZND0="15961@1" Pin0InfoVect0LinkObjId="SW-70990_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70977_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-983 4110,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19def90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5370,-197 5354,-197 5354,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1ab7a60@0" ObjectIDZND0="14531@x" ObjectIDZND1="14562@x" Pin0InfoVect0LinkObjId="SW-68491_0" Pin0InfoVect1LinkObjId="SW-68610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ab7a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5370,-197 5354,-197 5354,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19df1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5373,-223 5354,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="14562@0" ObjectIDZND0="g_1ab7a60@0" ObjectIDZND1="14531@x" Pin0InfoVect0LinkObjId="g_1ab7a60_0" Pin0InfoVect1LinkObjId="SW-68491_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5373,-223 5354,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19df450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-223 5333,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1ab7a60@0" ObjectIDND1="14562@x" ObjectIDZND0="14531@0" Pin0InfoVect0LinkObjId="SW-68491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ab7a60_0" Pin1InfoVect1LinkObjId="SW-68610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-223 5333,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19df6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-365 4200,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15615@1" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_1bc45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69768_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-365 4200,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_197d0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-428 4654,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="12027@x" ObjectIDND1="g_199dd00@0" ObjectIDND2="g_1a3e760@0" ObjectIDZND0="12196@0" Pin0InfoVect0LinkObjId="SW-64576_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63506_0" Pin1InfoVect1LinkObjId="g_199dd00_0" Pin1InfoVect2LinkObjId="g_1a3e760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-428 4654,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_197d340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4685,-428 4707,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="12196@1" ObjectIDZND0="g_1a98fc0@0" Pin0InfoVect0LinkObjId="g_1a98fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64576_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4685,-428 4707,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1981f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-136 4242,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="28612@x" ObjectIDND1="14554@x" ObjectIDZND0="12031@0" Pin0InfoVect0LinkObjId="SW-63510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb3_0" Pin1InfoVect1LinkObjId="SW-68602_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-136 4242,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1982160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-136 4295,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="12031@1" ObjectIDZND0="g_1af8540@0" Pin0InfoVect0LinkObjId="g_1af8540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-136 4295,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19823c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-140 4115,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="28611@x" ObjectIDND1="14551@x" ObjectIDZND0="12030@0" Pin0InfoVect0LinkObjId="SW-63509_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_Cb1_0" Pin1InfoVect1LinkObjId="SW-68599_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-140 4115,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1982620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-140 4168,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="12030@1" ObjectIDZND0="g_1b04de0@0" Pin0InfoVect0LinkObjId="g_1b04de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-140 4168,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1987af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-1015 4253,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="15605@0" ObjectIDZND0="g_1a4b590@0" ObjectIDZND1="g_1a4d5a0@0" ObjectIDZND2="15606@x" Pin0InfoVect0LinkObjId="g_1a4b590_0" Pin0InfoVect1LinkObjId="g_1a4d5a0_0" Pin0InfoVect2LinkObjId="SW-69509_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69508_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-1015 4253,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1987d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-1031 4253,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="15605@x" ObjectIDND1="15606@x" ObjectIDZND0="g_1a4b590@0" ObjectIDZND1="g_1a4d5a0@0" Pin0InfoVect0LinkObjId="g_1a4b590_0" Pin0InfoVect1LinkObjId="g_1a4d5a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-69508_0" Pin1InfoVect1LinkObjId="SW-69509_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-1031 4253,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1987fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-1031 4309,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a4c0b0@0" ObjectIDZND0="15606@1" Pin0InfoVect0LinkObjId="SW-69509_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a4c0b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-1031 4309,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1988210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-1031 4253,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="15606@0" ObjectIDZND0="15605@x" ObjectIDZND1="g_1a4b590@0" ObjectIDZND2="g_1a4d5a0@0" Pin0InfoVect0LinkObjId="SW-69508_0" Pin0InfoVect1LinkObjId="g_1a4b590_0" Pin0InfoVect2LinkObjId="g_1a4d5a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-1031 4253,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1988470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-946 4329,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="15607@1" ObjectIDZND0="g_1a4cb10@0" Pin0InfoVect0LinkObjId="g_1a4cb10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-946 4329,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1988f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1060 4110,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="15960@x" ObjectIDND1="15962@x" ObjectIDZND0="g_1b73e40@0" ObjectIDZND1="11720@1" ObjectIDZND2="g_1b73030@0" Pin0InfoVect0LinkObjId="g_1b73e40_0" Pin0InfoVect1LinkObjId="g_19de150_1" Pin0InfoVect2LinkObjId="g_1b73030_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-70990_0" Pin1InfoVect1LinkObjId="SW-70991_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1060 4110,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19891c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-1043 4110,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="15960@0" ObjectIDZND0="g_1b73e40@0" ObjectIDZND1="11720@1" ObjectIDZND2="g_1b73030@0" Pin0InfoVect0LinkObjId="g_1b73e40_0" Pin0InfoVect1LinkObjId="g_19de150_1" Pin0InfoVect2LinkObjId="g_1b73030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-1043 4110,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_198b950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4025,-1060 4047,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19fd570@0" ObjectIDZND0="15962@0" Pin0InfoVect0LinkObjId="SW-70991_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19fd570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4025,-1060 4047,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_198bbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-1060 4110,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="15962@1" ObjectIDZND0="g_1b73e40@0" ObjectIDZND1="11720@1" ObjectIDZND2="g_1b73030@0" Pin0InfoVect0LinkObjId="g_1b73e40_0" Pin0InfoVect1LinkObjId="g_19de150_1" Pin0InfoVect2LinkObjId="g_1b73030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70991_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-1060 4110,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_198c6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4585,-1014 4585,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="15956@0" ObjectIDZND0="g_1affb50@0" ObjectIDZND1="g_1b00c70@0" ObjectIDZND2="11856@1" Pin0InfoVect0LinkObjId="g_1affb50_0" Pin0InfoVect1LinkObjId="g_1b00c70_0" Pin0InfoVect2LinkObjId="g_19dead0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4585,-1014 4585,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_198c900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4585,-1037 4585,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="15956@x" ObjectIDND1="15958@x" ObjectIDZND0="g_1affb50@0" ObjectIDZND1="g_1b00c70@0" ObjectIDZND2="11856@1" Pin0InfoVect0LinkObjId="g_1affb50_0" Pin0InfoVect1LinkObjId="g_1b00c70_0" Pin0InfoVect2LinkObjId="g_19dead0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-70917_0" Pin1InfoVect1LinkObjId="SW-70918_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4585,-1037 4585,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_198cb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4508,-1037 4533,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b3c190@0" ObjectIDZND0="15958@0" Pin0InfoVect0LinkObjId="SW-70918_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3c190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4508,-1037 4533,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_198cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4567,-1037 4585,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="15958@1" ObjectIDZND0="15956@x" ObjectIDZND1="g_1affb50@0" ObjectIDZND2="g_1b00c70@0" Pin0InfoVect0LinkObjId="SW-70917_0" Pin0InfoVect1LinkObjId="g_1affb50_0" Pin0InfoVect2LinkObjId="g_1b00c70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70918_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4567,-1037 4585,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_198d020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4681,-773 4667,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="12072@x" ObjectIDND1="12047@x" ObjectIDND2="g_1a9d000@0" ObjectIDZND0="11975@1" Pin0InfoVect0LinkObjId="SW-63432_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-63584_0" Pin1InfoVect1LinkObjId="g_1bc39c0_0" Pin1InfoVect2LinkObjId="g_1a9d000_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4681,-773 4667,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_198d280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-773 4608,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11975@0" ObjectIDZND0="g_1a7c330@0" Pin0InfoVect0LinkObjId="g_1a7c330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63432_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-773 4608,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_198fe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5409,-807 5567,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="14556@1" ObjectIDZND0="37757@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5409,-807 5567,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_195a080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4681,-869 4681,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="12071@1" ObjectIDZND0="11974@1" Pin0InfoVect0LinkObjId="SW-63431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63584_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4681,-869 4681,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_195a2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4681,-823 4681,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11974@0" ObjectIDZND0="12072@1" Pin0InfoVect0LinkObjId="SW-63584_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4681,-823 4681,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195add0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4766,-698 4756,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_1a9ddb0@0" ObjectIDZND0="12047@x" ObjectIDZND1="11966@x" Pin0InfoVect0LinkObjId="g_1bc39c0_0" Pin0InfoVect1LinkObjId="SW-63165_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a9ddb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4766,-698 4756,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195b030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4756,-698 4756,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="12047@x" ObjectIDND1="g_1a9ddb0@0" ObjectIDZND0="11966@0" Pin0InfoVect0LinkObjId="SW-63165_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bc39c0_0" Pin1InfoVect1LinkObjId="g_1a9ddb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4756,-698 4756,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_195f170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-299 5151,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11978@0" ObjectIDZND0="11976@0" Pin0InfoVect0LinkObjId="SW-63455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63457_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-299 5151,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_195f3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5124,-299 5107,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11976@1" ObjectIDZND0="11979@1" Pin0InfoVect0LinkObjId="SW-63458_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63455_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5124,-299 5107,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-127 4604,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="12114@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-127 4604,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1961f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-434 3782,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_19522f0@0" ObjectIDND1="15946@x" ObjectIDND2="g_1950d30@0" ObjectIDZND0="18054@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_19522f0_0" Pin1InfoVect1LinkObjId="SW-70705_0" Pin1InfoVect2LinkObjId="g_1950d30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-434 3782,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19621b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-434 3829,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18054@1" ObjectIDZND0="g_1951890@0" Pin0InfoVect0LinkObjId="g_1951890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-434 3829,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cb09c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-202 4893,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20695@1" ObjectIDZND0="g_1974db0@0" Pin0InfoVect0LinkObjId="g_1974db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106119_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-202 4893,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cb0c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-111 4858,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34549@x" ObjectIDND1="20696@x" ObjectIDZND0="g_1cc7bb0@0" Pin0InfoVect0LinkObjId="g_1cc7bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SB.073Ld_0" Pin1InfoVect1LinkObjId="SW-106120_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-111 4858,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cb0e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-111 4834,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="20696@x" ObjectIDND1="g_1cc7bb0@0" ObjectIDZND0="34549@0" Pin0InfoVect0LinkObjId="EC-CX_SB.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106120_0" Pin1InfoVect1LinkObjId="g_1cc7bb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-111 4834,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cb10e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-119 4834,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="20696@1" ObjectIDZND0="34549@x" ObjectIDZND1="g_1cc7bb0@0" Pin0InfoVect0LinkObjId="EC-CX_SB.073Ld_0" Pin0InfoVect1LinkObjId="g_1cc7bb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-119 4834,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cc35d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-202 4834,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="20695@0" ObjectIDZND0="g_1cc4520@0" ObjectIDZND1="20694@x" ObjectIDZND2="g_1cc6ad0@0" Pin0InfoVect0LinkObjId="g_1cc4520_0" Pin0InfoVect1LinkObjId="SW-106118_0" Pin0InfoVect2LinkObjId="g_1cc6ad0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-202 4834,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cc3830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-351 4834,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11962@0" ObjectIDZND0="20693@0" Pin0InfoVect0LinkObjId="SW-106117_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d6f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-351 4834,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cc3a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-301 4834,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20693@1" ObjectIDZND0="20692@1" Pin0InfoVect0LinkObjId="SW-106116_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106117_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-301 4834,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cc3cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-264 4834,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20692@0" ObjectIDZND0="20694@1" Pin0InfoVect0LinkObjId="SW-106118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106116_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-264 4834,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cc5290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-202 4806,-211 4834,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1cc4520@0" ObjectIDZND0="20694@x" ObjectIDZND1="g_1cc6ad0@0" ObjectIDZND2="20695@x" Pin0InfoVect0LinkObjId="SW-106118_0" Pin0InfoVect1LinkObjId="g_1cc6ad0_0" Pin0InfoVect2LinkObjId="SW-106119_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cc4520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-202 4806,-211 4834,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cc5d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-211 4834,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1cc4520@0" ObjectIDND1="g_1cc6ad0@0" ObjectIDND2="20695@x" ObjectIDZND0="20694@0" Pin0InfoVect0LinkObjId="SW-106118_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cc4520_0" Pin1InfoVect1LinkObjId="g_1cc6ad0_0" Pin1InfoVect2LinkObjId="SW-106119_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-211 4834,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cc6870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-202 4834,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1cc6ad0@0" ObjectIDND1="20695@x" ObjectIDZND0="g_1cc4520@0" ObjectIDZND1="20694@x" Pin0InfoVect0LinkObjId="g_1cc4520_0" Pin0InfoVect1LinkObjId="SW-106118_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cc6ad0_0" Pin1InfoVect1LinkObjId="SW-106119_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-202 4834,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cc76f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-155 4834,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20696@0" ObjectIDZND0="g_1cc6ad0@0" Pin0InfoVect0LinkObjId="g_1cc6ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-155 4834,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cc7950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4834,-195 4834,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1cc6ad0@1" ObjectIDZND0="g_1cc4520@0" ObjectIDZND1="20694@x" ObjectIDZND2="20695@x" Pin0InfoVect0LinkObjId="g_1cc4520_0" Pin0InfoVect1LinkObjId="SW-106118_0" Pin0InfoVect2LinkObjId="SW-106119_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cc6ad0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4834,-195 4834,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd0dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5010,-202 5019,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20700@1" ObjectIDZND0="g_1cc8920@0" Pin0InfoVect0LinkObjId="g_1cc8920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5010,-202 5019,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd1020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-111 4984,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34550@x" ObjectIDND1="20701@x" ObjectIDZND0="g_1cd8190@0" Pin0InfoVect0LinkObjId="g_1cd8190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_SB.074Ld_0" Pin1InfoVect1LinkObjId="SW-106127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-111 4984,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd1280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-111 4960,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1cd8190@0" ObjectIDND1="20701@x" ObjectIDZND0="34550@0" Pin0InfoVect0LinkObjId="EC-CX_SB.074Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cd8190_0" Pin1InfoVect1LinkObjId="SW-106127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-111 4960,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd14e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-119 4960,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="20701@1" ObjectIDZND0="34550@x" ObjectIDZND1="g_1cd8190@0" Pin0InfoVect0LinkObjId="EC-CX_SB.074Ld_0" Pin0InfoVect1LinkObjId="g_1cd8190_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106127_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-119 4960,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd6010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-202 4960,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20700@0" ObjectIDZND0="20699@x" ObjectIDZND1="g_1cd8f00@0" ObjectIDZND2="g_1cd70b0@0" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="g_1cd8f00_0" Pin0InfoVect2LinkObjId="g_1cd70b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106126_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-202 4960,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd6270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-351 4960,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11962@0" ObjectIDZND0="20698@0" Pin0InfoVect0LinkObjId="SW-106124_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d6f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-351 4960,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd64d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-301 4960,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20698@1" ObjectIDZND0="20697@1" Pin0InfoVect0LinkObjId="SW-106123_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106124_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-301 4960,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd6730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-264 4960,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20697@0" ObjectIDZND0="20699@1" Pin0InfoVect0LinkObjId="SW-106125_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-264 4960,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd6990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4932,-202 4932,-211 4960,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1cd8f00@0" ObjectIDZND0="20699@x" ObjectIDZND1="g_1cd70b0@0" ObjectIDZND2="20700@x" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="g_1cd70b0_0" Pin0InfoVect2LinkObjId="SW-106126_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cd8f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4932,-202 4932,-211 4960,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd6bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-211 4960,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1cd8f00@0" ObjectIDND1="g_1cd70b0@0" ObjectIDND2="20700@x" ObjectIDZND0="20699@0" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cd8f00_0" Pin1InfoVect1LinkObjId="g_1cd70b0_0" Pin1InfoVect2LinkObjId="SW-106126_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-211 4960,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd6e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-202 4960,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1cd70b0@0" ObjectIDND1="20700@x" ObjectIDZND0="20699@x" ObjectIDZND1="g_1cd8f00@0" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="g_1cd8f00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cd70b0_0" Pin1InfoVect1LinkObjId="SW-106126_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-202 4960,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd7cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-155 4960,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="20701@0" ObjectIDZND0="g_1cd70b0@0" Pin0InfoVect0LinkObjId="g_1cd70b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-155 4960,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd7f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-195 4960,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1cd70b0@1" ObjectIDZND0="20699@x" ObjectIDZND1="g_1cd8f00@0" ObjectIDZND2="20700@x" Pin0InfoVect0LinkObjId="SW-106125_0" Pin0InfoVect1LinkObjId="g_1cd8f00_0" Pin0InfoVect2LinkObjId="SW-106126_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cd70b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-195 4960,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ce1840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4588,-84 4588,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="12112@0" ObjectIDZND0="12012@1" Pin0InfoVect0LinkObjId="SW-63491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_SB.CX_SB_2C_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4588,-84 4588,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce6600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-224 5252,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12193@0" ObjectIDZND0="14561@0" Pin0InfoVect0LinkObjId="SW-68609_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d9c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5217,-224 5252,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce6860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5288,-224 5306,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14561@1" ObjectIDZND0="14531@1" Pin0InfoVect0LinkObjId="SW-68491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68609_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5288,-224 5306,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce9200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-354 5251,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12193@0" ObjectIDZND0="14559@0" Pin0InfoVect0LinkObjId="SW-68607_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d9c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5217,-354 5251,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce9460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5287,-354 5306,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14559@1" ObjectIDZND0="14530@1" Pin0InfoVect0LinkObjId="SW-68468_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68607_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5287,-354 5306,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cee0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-567 5248,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12192@0" ObjectIDZND0="15954@0" Pin0InfoVect0LinkObjId="SW-70761_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc0a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5217,-567 5248,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cee300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5284,-567 5307,-567 5307,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15954@1" ObjectIDZND0="15948@1" Pin0InfoVect0LinkObjId="SW-70722_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5284,-567 5307,-567 5307,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cee560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-439 5247,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12193@0" ObjectIDZND0="15949@0" Pin0InfoVect0LinkObjId="SW-70724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d9c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5217,-439 5247,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cee7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5283,-439 5307,-439 5307,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15949@1" ObjectIDZND0="15948@0" Pin0InfoVect0LinkObjId="SW-70722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5283,-439 5307,-439 5307,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf6a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-656 5253,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12192@0" ObjectIDZND0="14557@0" Pin0InfoVect0LinkObjId="SW-68605_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc0a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5217,-656 5253,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf6cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5289,-656 5306,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="14557@1" ObjectIDZND0="14529@1" Pin0InfoVect0LinkObjId="SW-68445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68605_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5289,-656 5306,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cfacb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5516,-269 5527,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1cfa240@0" Pin0InfoVect0LinkObjId="g_1cfa240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5516,-269 5527,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfaf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5441,-223 5441,-270 5471,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="14562@x" ObjectIDND1="34566@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-68610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5441,-223 5441,-270 5471,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5475,-223 5441,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="34566@1" ObjectIDZND0="0@x" ObjectIDZND1="14562@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="SW-68610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5475,-223 5441,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfbc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5409,-223 5441,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="14562@1" ObjectIDZND0="0@x" ObjectIDZND1="34566@1" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5409,-223 5441,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d02ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-276 4093,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="14526@1" ObjectIDZND0="14549@1" Pin0InfoVect0LinkObjId="SW-68597_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68359_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-276 4093,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d03230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-329 4093,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="14549@0" ObjectIDZND0="11961@0" Pin0InfoVect0LinkObjId="g_1bc45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68597_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-329 4093,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d16f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5217,-933 5242,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12192@0" ObjectIDZND0="15965@0" Pin0InfoVect0LinkObjId="SW-68398_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc0a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5217,-933 5242,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d171a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5278,-933 5304,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15965@1" ObjectIDZND0="14533@1" Pin0InfoVect0LinkObjId="SW-68397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68398_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5278,-933 5304,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d17400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5331,-933 5352,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="14533@0" ObjectIDZND0="g_1a83700@0" ObjectIDZND1="15966@x" Pin0InfoVect0LinkObjId="g_1a83700_0" Pin0InfoVect1LinkObjId="SW-68399_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-68397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5331,-933 5352,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d203d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-950 4110,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15961@0" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_1bc2920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-70990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-950 4110,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d205c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-879 4119,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15632@0" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_1bc2920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-879 4119,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d207b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-946 4253,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="15605@x" ObjectIDND1="15607@x" ObjectIDZND0="12191@0" Pin0InfoVect0LinkObjId="g_1bc2920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-69508_0" Pin1InfoVect1LinkObjId="SW-69510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-946 4253,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d21190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-979 4253,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="15605@1" ObjectIDZND0="15607@x" ObjectIDZND1="12191@0" Pin0InfoVect0LinkObjId="SW-69510_0" Pin0InfoVect1LinkObjId="g_1bc2920_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69508_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-979 4253,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d213e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-946 4272,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="15605@x" ObjectIDND1="12191@0" ObjectIDZND0="15607@0" Pin0InfoVect0LinkObjId="SW-69510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-69508_0" Pin1InfoVect1LinkObjId="g_1bc2920_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-946 4272,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d31d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5572,-933 5412,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="37766@1" ObjectIDZND0="15966@1" Pin0InfoVect0LinkObjId="SW-68399_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5572,-933 5412,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d38190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5512,-413 5524,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5512,-413 5524,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d39630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5484,-969 5470,-969 5470,-905 5479,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1ab2890@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ab2890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5484,-969 5470,-969 5470,-905 5479,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d398a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5490,-381 5466,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d33f90@0" ObjectIDZND0="0@x" ObjectIDZND1="14560@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="SW-68608_0" Pin0InfoVect2LinkObjId="TF-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d33f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5490,-381 5466,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3a390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5466,-413 5466,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_1d33f90@0" ObjectIDZND1="14560@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1d33f90_0" Pin0InfoVect1LinkObjId="SW-68608_0" Pin0InfoVect2LinkObjId="TF-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5466,-413 5466,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3a5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5466,-381 5466,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_1d33f90@0" ObjectIDND1="0@x" ObjectIDZND0="14560@x" ObjectIDZND1="0@x" ObjectIDZND2="37763@1" Pin0InfoVect0LinkObjId="SW-68608_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="g_1d3a850_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d33f90_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5466,-381 5466,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3a850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5505,-316 5505,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="37763@1" ObjectIDZND1="14560@x" ObjectIDZND2="g_1d33f90@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-68608_0" Pin0InfoVect2LinkObjId="g_1d33f90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5505,-316 5505,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3b340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5565,-353 5505,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="37763@1" ObjectIDZND0="0@x" ObjectIDZND1="14560@x" ObjectIDZND2="g_1d33f90@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="SW-68608_0" Pin0InfoVect2LinkObjId="g_1d33f90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d3a850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5565,-353 5505,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3b5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5505,-353 5466,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="37763@1" ObjectIDZND0="14560@x" ObjectIDZND1="g_1d33f90@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-68608_0" Pin0InfoVect1LinkObjId="g_1d33f90_0" Pin0InfoVect2LinkObjId="TF-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="g_1d3a850_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5505,-353 5466,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d3b800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5577,-316 5551,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_19af750@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19af750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5577,-316 5551,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3cc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-737 4963,-737 4963,-524 4118,-524 4118,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="15612@0" ObjectIDZND0="4843@0" Pin0InfoVect0LinkObjId="g_1bc32a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69758_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-737 4963,-737 4963,-524 4118,-524 4118,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3ce40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-737 5217,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15611@1" ObjectIDZND0="12192@0" Pin0InfoVect0LinkObjId="g_1bc0a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-69757_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-737 5217,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3d580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-299 5217,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11978@1" ObjectIDZND0="12193@0" Pin0InfoVect0LinkObjId="g_19d9c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-63457_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-299 5217,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20681d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5514,-146 5525,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2066a50@0" Pin0InfoVect0LinkObjId="g_2066a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5514,-146 5525,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206a590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5293,-100 5310,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47266@1" ObjectIDZND0="47265@1" Pin0InfoVect0LinkObjId="SW-305069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-305070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5293,-100 5310,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206b510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5468,-146 5442,-146 5442,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_205fca0@0" ObjectIDZND1="47270@x" ObjectIDZND2="47268@x" Pin0InfoVect0LinkObjId="g_205fca0_0" Pin0InfoVect1LinkObjId="SW-305072_0" Pin0InfoVect2LinkObjId="SW-305071_0" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5468,-146 5442,-146 5442,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206c070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5442,-100 5565,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="g_205fca0@0" ObjectIDND2="47270@x" ObjectIDZND0="48151@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="g_205fca0_0" Pin1InfoVect2LinkObjId="SW-305072_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5442,-100 5565,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206c280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5337,-100 5377,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47265@0" ObjectIDZND0="47268@0" Pin0InfoVect0LinkObjId="SW-305071_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-305069_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5337,-100 5377,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206c4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5442,-100 5442,-75 5469,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="47270@x" ObjectIDND2="47268@x" ObjectIDZND0="g_205fca0@0" Pin0InfoVect0LinkObjId="g_205fca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="SW-305072_0" Pin1InfoVect2LinkObjId="SW-305071_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5442,-100 5442,-75 5469,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-84 5426,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47270@0" ObjectIDZND0="47268@x" ObjectIDZND1="0@x" ObjectIDZND2="g_205fca0@0" Pin0InfoVect0LinkObjId="SW-305071_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="g_205fca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-305072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-84 5426,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20703a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5413,-100 5426,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47268@1" ObjectIDZND0="47270@x" ObjectIDZND1="0@x" ObjectIDZND2="g_205fca0@0" Pin0InfoVect0LinkObjId="SW-305072_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="g_205fca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-305071_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5413,-100 5426,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2070600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5426,-100 5442,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="47270@x" ObjectIDND1="47268@x" ObjectIDZND0="0@x" ObjectIDZND1="g_205fca0@0" ObjectIDZND2="48151@1" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_205fca0_0" Pin0InfoVect2LinkObjId="g_206c070_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-305072_0" Pin1InfoVect1LinkObjId="SW-305071_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5426,-100 5442,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20710d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5257,-100 5217,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47266@0" ObjectIDZND0="12193@0" Pin0InfoVect0LinkObjId="g_19d9c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-305070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5257,-100 5217,-100 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3237" y="-1178"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3188" y="-1195"/></g>
   <g href="110kV双柏变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="4177" y="-750"/></g>
   <g href="110kV双柏变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4740" y="-750"/></g>
   <g href="AVC双柏站.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="93" x="3394" y="-1177"/></g>
   <g href="110kV双柏变GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="88" x="3147" y="-685"/></g>
   <g href="110kV双柏变110kV谢烟双线156断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4119" y="-1004"/></g>
   <g href="110kV双柏变110kV双鄂大线154间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4594" y="-972"/></g>
   <g href="110kV双柏变10kV瓦波里线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3674" y="-264"/></g>
   <g href="110kV双柏变10kV花箐线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3780" y="-264"/></g>
   <g href="110kV双柏变10kV城区Ⅰ回线06线间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3889" y="-263"/></g>
   <g href="110kV双柏变10kV朝阳线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3995" y="-265"/></g>
   <g href="110kV双柏变10kV1号电容器065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4102" y="-270"/></g>
   <g href="110kV双柏变10kV2号电容器071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4597" y="-265"/></g>
   <g href="110kV双柏变10kV玉楚柏线072间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4722" y="-265"/></g>
   <g href="110kV双柏变10kV和平线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4843" y="-285"/></g>
   <g href="110kV双柏变10kV环东线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4969" y="-285"/></g>
   <g href="110kV双柏变35kV双妥大线364间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5308" y="-248"/></g>
   <g href="110kV双柏变35kV爱尼山线362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5307" y="-379"/></g>
   <g href="110kV双柏变35kV分段断路器312间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="5316" y="-510"/></g>
   <g href="110kV双柏变10kV分段备自投012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4455" y="-304"/></g>
   <g href="110kV双柏变35kV双小妥线365间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5308" y="-681"/></g>
   <g href="110kV双柏变35kV雨龙线363间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5304" y="-832"/></g>
   <g href="110kV双柏变35kV鱼庄河线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5305" y="-958"/></g>
   <g href="110kV双柏变10kV3号电容器066间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4229" y="-266"/></g>
   <g href="cx_配调_配网接线图110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3519" y="-1154"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3519" y="-1189"/></g>
   <g href="110kV双柏变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="88" x="3147" y="-610"/></g>
   <g href="110kV双柏变CX_SB-368间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5311" y="-131"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-63860" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -864.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11974"/>
     <cge:Term_Ref ObjectID="16840"/>
    <cge:TPSR_Ref TObjectID="11974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63861" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -864.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11974"/>
     <cge:Term_Ref ObjectID="16840"/>
    <cge:TPSR_Ref TObjectID="11974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63857" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -864.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11974"/>
     <cge:Term_Ref ObjectID="16840"/>
    <cge:TPSR_Ref TObjectID="11974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63879" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4590.000000 34.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12008"/>
     <cge:Term_Ref ObjectID="16862"/>
    <cge:TPSR_Ref TObjectID="12008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63876" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4590.000000 34.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12008"/>
     <cge:Term_Ref ObjectID="16862"/>
    <cge:TPSR_Ref TObjectID="12008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-63873" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4703.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11985"/>
     <cge:Term_Ref ObjectID="16856"/>
    <cge:TPSR_Ref TObjectID="11985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63874" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4703.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11985"/>
     <cge:Term_Ref ObjectID="16856"/>
    <cge:TPSR_Ref TObjectID="11985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63869" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4703.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11985"/>
     <cge:Term_Ref ObjectID="16856"/>
    <cge:TPSR_Ref TObjectID="11985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-63866" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -280.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11976"/>
     <cge:Term_Ref ObjectID="16850"/>
    <cge:TPSR_Ref TObjectID="11976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63867" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -280.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11976"/>
     <cge:Term_Ref ObjectID="16850"/>
    <cge:TPSR_Ref TObjectID="11976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63863" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -280.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11976"/>
     <cge:Term_Ref ObjectID="16850"/>
    <cge:TPSR_Ref TObjectID="11976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-62940" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4859.000000 -777.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12042"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-62974" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4982.000000 -468.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-62975" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4982.000000 -468.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-62976" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4982.000000 -468.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-62978" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4982.000000 -468.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-62982" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4982.000000 -468.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-62986" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4982.000000 -468.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11962"/>
     <cge:Term_Ref ObjectID="16817"/>
    <cge:TPSR_Ref TObjectID="11962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-64554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1151.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-64555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1151.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-64556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1151.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-64560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1151.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-64562" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1151.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-64568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1151.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12192"/>
     <cge:Term_Ref ObjectID="17080"/>
    <cge:TPSR_Ref TObjectID="12192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-64557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5162.000000 -45.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-64558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5162.000000 -45.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-64559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5162.000000 -45.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-64561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5162.000000 -45.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-64565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5162.000000 -45.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-64569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5162.000000 -45.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12193"/>
     <cge:Term_Ref ObjectID="17081"/>
    <cge:TPSR_Ref TObjectID="12193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-69494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.000000 -716.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15617"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-69485" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4230.000000 -860.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15608"/>
     <cge:Term_Ref ObjectID="23024"/>
    <cge:TPSR_Ref TObjectID="15608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-69486" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4230.000000 -860.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15608"/>
     <cge:Term_Ref ObjectID="23024"/>
    <cge:TPSR_Ref TObjectID="15608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-69482" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4230.000000 -860.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15608"/>
     <cge:Term_Ref ObjectID="23024"/>
    <cge:TPSR_Ref TObjectID="15608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-69491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5133.000000 -724.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15613"/>
     <cge:Term_Ref ObjectID="23034"/>
    <cge:TPSR_Ref TObjectID="15613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-69492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5133.000000 -724.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15613"/>
     <cge:Term_Ref ObjectID="23034"/>
    <cge:TPSR_Ref TObjectID="15613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-69488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5133.000000 -724.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15613"/>
     <cge:Term_Ref ObjectID="23034"/>
    <cge:TPSR_Ref TObjectID="15613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-69498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4326.000000 -452.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15614"/>
     <cge:Term_Ref ObjectID="23036"/>
    <cge:TPSR_Ref TObjectID="15614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-69499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4326.000000 -452.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15614"/>
     <cge:Term_Ref ObjectID="23036"/>
    <cge:TPSR_Ref TObjectID="15614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-69495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4326.000000 -452.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="69495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15614"/>
     <cge:Term_Ref ObjectID="23036"/>
    <cge:TPSR_Ref TObjectID="15614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-64540" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -991.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-64541" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -991.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-64542" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -991.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-64546" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -991.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-64552" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -991.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="64552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12191"/>
     <cge:Term_Ref ObjectID="17079"/>
    <cge:TPSR_Ref TObjectID="12191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -11.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14527"/>
     <cge:Term_Ref ObjectID="21067"/>
    <cge:TPSR_Ref TObjectID="14527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -11.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14527"/>
     <cge:Term_Ref ObjectID="21067"/>
    <cge:TPSR_Ref TObjectID="14527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 -38.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14524"/>
     <cge:Term_Ref ObjectID="21061"/>
    <cge:TPSR_Ref TObjectID="14524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 -38.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14524"/>
     <cge:Term_Ref ObjectID="21061"/>
    <cge:TPSR_Ref TObjectID="14524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 -38.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14524"/>
     <cge:Term_Ref ObjectID="21061"/>
    <cge:TPSR_Ref TObjectID="14524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5724.000000 -374.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14530"/>
     <cge:Term_Ref ObjectID="21073"/>
    <cge:TPSR_Ref TObjectID="14530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5724.000000 -374.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14530"/>
     <cge:Term_Ref ObjectID="21073"/>
    <cge:TPSR_Ref TObjectID="14530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5724.000000 -374.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14530"/>
     <cge:Term_Ref ObjectID="21073"/>
    <cge:TPSR_Ref TObjectID="14530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4474.000000 -266.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15963"/>
     <cge:Term_Ref ObjectID="14505"/>
    <cge:TPSR_Ref TObjectID="15963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4474.000000 -266.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15963"/>
     <cge:Term_Ref ObjectID="14505"/>
    <cge:TPSR_Ref TObjectID="15963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70669" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4474.000000 -266.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15963"/>
     <cge:Term_Ref ObjectID="14505"/>
    <cge:TPSR_Ref TObjectID="15963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68536" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3965.000000 -37.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14525"/>
     <cge:Term_Ref ObjectID="21063"/>
    <cge:TPSR_Ref TObjectID="14525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68537" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3965.000000 -37.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14525"/>
     <cge:Term_Ref ObjectID="21063"/>
    <cge:TPSR_Ref TObjectID="14525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68532" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3965.000000 -37.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14525"/>
     <cge:Term_Ref ObjectID="21063"/>
    <cge:TPSR_Ref TObjectID="14525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68552" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5709.000000 -963.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14533"/>
     <cge:Term_Ref ObjectID="21077"/>
    <cge:TPSR_Ref TObjectID="14533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68553" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5709.000000 -963.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14533"/>
     <cge:Term_Ref ObjectID="21077"/>
    <cge:TPSR_Ref TObjectID="14533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68548" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5709.000000 -963.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14533"/>
     <cge:Term_Ref ObjectID="21077"/>
    <cge:TPSR_Ref TObjectID="14533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68566" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5709.000000 -680.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14529"/>
     <cge:Term_Ref ObjectID="21071"/>
    <cge:TPSR_Ref TObjectID="14529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68567" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5709.000000 -680.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14529"/>
     <cge:Term_Ref ObjectID="21071"/>
    <cge:TPSR_Ref TObjectID="14529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68562" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5709.000000 -680.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14529"/>
     <cge:Term_Ref ObjectID="21071"/>
    <cge:TPSR_Ref TObjectID="14529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70692" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4533.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15955"/>
     <cge:Term_Ref ObjectID="23517"/>
    <cge:TPSR_Ref TObjectID="15955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70693" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4533.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15955"/>
     <cge:Term_Ref ObjectID="23517"/>
    <cge:TPSR_Ref TObjectID="15955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70687" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4533.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15955"/>
     <cge:Term_Ref ObjectID="23517"/>
    <cge:TPSR_Ref TObjectID="15955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-70700" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -997.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15959"/>
     <cge:Term_Ref ObjectID="23525"/>
    <cge:TPSR_Ref TObjectID="15959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-70701" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -997.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15959"/>
     <cge:Term_Ref ObjectID="23525"/>
    <cge:TPSR_Ref TObjectID="15959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70695" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -997.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15959"/>
     <cge:Term_Ref ObjectID="23525"/>
    <cge:TPSR_Ref TObjectID="15959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-70668" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5408.000000 -509.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70668" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="15948"/>
     <cge:Term_Ref ObjectID="23503"/>
    <cge:TPSR_Ref TObjectID="15948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68515" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3646.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14522"/>
     <cge:Term_Ref ObjectID="21057"/>
    <cge:TPSR_Ref TObjectID="14522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68516" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3646.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14522"/>
     <cge:Term_Ref ObjectID="21057"/>
    <cge:TPSR_Ref TObjectID="14522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68511" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3646.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14522"/>
     <cge:Term_Ref ObjectID="21057"/>
    <cge:TPSR_Ref TObjectID="14522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68522" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3756.000000 -36.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14523"/>
     <cge:Term_Ref ObjectID="21059"/>
    <cge:TPSR_Ref TObjectID="14523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68523" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3756.000000 -36.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14523"/>
     <cge:Term_Ref ObjectID="21059"/>
    <cge:TPSR_Ref TObjectID="14523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68518" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3756.000000 -36.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14523"/>
     <cge:Term_Ref ObjectID="21059"/>
    <cge:TPSR_Ref TObjectID="14523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68559" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5700.000000 -843.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14528"/>
     <cge:Term_Ref ObjectID="21069"/>
    <cge:TPSR_Ref TObjectID="14528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68560" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5700.000000 -843.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14528"/>
     <cge:Term_Ref ObjectID="21069"/>
    <cge:TPSR_Ref TObjectID="14528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68555" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5700.000000 -843.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14528"/>
     <cge:Term_Ref ObjectID="21069"/>
    <cge:TPSR_Ref TObjectID="14528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-63852" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4870.000000 -457.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11963"/>
     <cge:Term_Ref ObjectID="16832"/>
    <cge:TPSR_Ref TObjectID="11963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-63853" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4870.000000 -457.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63853" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11963"/>
     <cge:Term_Ref ObjectID="16832"/>
    <cge:TPSR_Ref TObjectID="11963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-63849" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4870.000000 -457.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="63849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11963"/>
     <cge:Term_Ref ObjectID="16832"/>
    <cge:TPSR_Ref TObjectID="11963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68541" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -11.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14526"/>
     <cge:Term_Ref ObjectID="21065"/>
    <cge:TPSR_Ref TObjectID="14526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68538" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -11.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14526"/>
     <cge:Term_Ref ObjectID="21065"/>
    <cge:TPSR_Ref TObjectID="14526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-62971" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -470.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-62972" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -470.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-62973" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -470.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-62977" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -470.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-62979" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -470.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-62985" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -470.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11961"/>
     <cge:Term_Ref ObjectID="16816"/>
    <cge:TPSR_Ref TObjectID="11961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 -40.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20692"/>
     <cge:Term_Ref ObjectID="17280"/>
    <cge:TPSR_Ref TObjectID="20692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 -40.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20692"/>
     <cge:Term_Ref ObjectID="17280"/>
    <cge:TPSR_Ref TObjectID="20692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 -40.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20692"/>
     <cge:Term_Ref ObjectID="17280"/>
    <cge:TPSR_Ref TObjectID="20692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -40.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20697"/>
     <cge:Term_Ref ObjectID="28744"/>
    <cge:TPSR_Ref TObjectID="20697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -40.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20697"/>
     <cge:Term_Ref ObjectID="28744"/>
    <cge:TPSR_Ref TObjectID="20697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106108" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -40.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20697"/>
     <cge:Term_Ref ObjectID="28744"/>
    <cge:TPSR_Ref TObjectID="20697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-68580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5653.000000 -242.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14531"/>
     <cge:Term_Ref ObjectID="21075"/>
    <cge:TPSR_Ref TObjectID="14531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-68581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5653.000000 -242.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14531"/>
     <cge:Term_Ref ObjectID="21075"/>
    <cge:TPSR_Ref TObjectID="14531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-68576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5653.000000 -242.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="68576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="14531"/>
     <cge:Term_Ref ObjectID="21075"/>
    <cge:TPSR_Ref TObjectID="14531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-305122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5709.000000 -124.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="305122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47265"/>
     <cge:Term_Ref ObjectID="39075"/>
    <cge:TPSR_Ref TObjectID="47265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-305123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5709.000000 -124.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="305123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47265"/>
     <cge:Term_Ref ObjectID="39075"/>
    <cge:TPSR_Ref TObjectID="47265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-305119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5709.000000 -124.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="305119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47265"/>
     <cge:Term_Ref ObjectID="39075"/>
    <cge:TPSR_Ref TObjectID="47265"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3656.000000 -38.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16028" ObjectName="EC-CX_SB.061Ld"/>
    <cge:TPSR_Ref TObjectID="16028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -38.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16021" ObjectName="EC-CX_SB.062Ld"/>
    <cge:TPSR_Ref TObjectID="16021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -36.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16022" ObjectName="EC-CX_SB.063Ld"/>
    <cge:TPSR_Ref TObjectID="16022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -38.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16023" ObjectName="EC-CX_SB.064Ld"/>
    <cge:TPSR_Ref TObjectID="16023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -38.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16024" ObjectName="EC-CX_SB.072Ld"/>
    <cge:TPSR_Ref TObjectID="16024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -41.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34549" ObjectName="EC-CX_SB.073Ld"/>
    <cge:TPSR_Ref TObjectID="34549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SB.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 -41.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34550" ObjectName="EC-CX_SB.074Ld"/>
    <cge:TPSR_Ref TObjectID="34550"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_SB"/>
</svg>