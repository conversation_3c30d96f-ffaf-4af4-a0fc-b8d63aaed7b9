<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-102" aopId="256" id="thSvg" viewBox="3120 -1149 2044 1214">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1224" width="2054" x="3115" y="-1154"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3121" y="-1017"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3121" y="-1137"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3121" y="-537"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3806.000000 -1076.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3806.000000 -1021.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52817">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9381" ObjectName="SW-CX_CB.CX_CB_35167SW"/>
     <cge:Meas_Ref ObjectId="52817"/>
    <cge:TPSR_Ref TObjectID="9381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52816">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -925.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9380" ObjectName="SW-CX_CB.CX_CB_3516SW"/>
     <cge:Meas_Ref ObjectId="52816"/>
    <cge:TPSR_Ref TObjectID="9380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52818">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -831.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9382" ObjectName="SW-CX_CB.CX_CB_3511SW"/>
     <cge:Meas_Ref ObjectId="52818"/>
    <cge:TPSR_Ref TObjectID="9382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52904">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4190.000000 -836.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9393" ObjectName="SW-CX_CB.CX_CB_3901SW"/>
     <cge:Meas_Ref ObjectId="52904"/>
    <cge:TPSR_Ref TObjectID="9393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52907">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -897.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9394" ObjectName="SW-CX_CB.CX_CB_39017SW"/>
     <cge:Meas_Ref ObjectId="52907"/>
    <cge:TPSR_Ref TObjectID="9394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4860.000000 -1077.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52801">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9376" ObjectName="SW-CX_CB.CX_CB_35267SW"/>
     <cge:Meas_Ref ObjectId="52801"/>
    <cge:TPSR_Ref TObjectID="9376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52800">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -925.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9375" ObjectName="SW-CX_CB.CX_CB_3526SW"/>
     <cge:Meas_Ref ObjectId="52800"/>
    <cge:TPSR_Ref TObjectID="9375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52802">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -831.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9377" ObjectName="SW-CX_CB.CX_CB_3521SW"/>
     <cge:Meas_Ref ObjectId="52802"/>
    <cge:TPSR_Ref TObjectID="9377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52865">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -768.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9389" ObjectName="SW-CX_CB.CX_CB_3021SW"/>
     <cge:Meas_Ref ObjectId="52865"/>
    <cge:TPSR_Ref TObjectID="9389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52871">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -578.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9391" ObjectName="SW-CX_CB.CX_CB_4026SW"/>
     <cge:Meas_Ref ObjectId="52871"/>
    <cge:TPSR_Ref TObjectID="9391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52872">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9392" ObjectName="SW-CX_CB.CX_CB_4021SW"/>
     <cge:Meas_Ref ObjectId="52872"/>
    <cge:TPSR_Ref TObjectID="9392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52912">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 -484.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9397" ObjectName="SW-CX_CB.CX_CB_4902SW"/>
     <cge:Meas_Ref ObjectId="52912"/>
    <cge:TPSR_Ref TObjectID="9397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -545.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9398" ObjectName="SW-CX_CB.CX_CB_49027SW"/>
     <cge:Meas_Ref ObjectId="52915"/>
    <cge:TPSR_Ref TObjectID="9398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52796">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4446.000000 -514.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9373" ObjectName="SW-CX_CB.CX_CB_4122SW"/>
     <cge:Meas_Ref ObjectId="52796"/>
    <cge:TPSR_Ref TObjectID="9373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9372" ObjectName="SW-CX_CB.CX_CB_4121SW"/>
     <cge:Meas_Ref ObjectId="52794"/>
    <cge:TPSR_Ref TObjectID="9372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52908">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 -484.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9395" ObjectName="SW-CX_CB.CX_CB_4901SW"/>
     <cge:Meas_Ref ObjectId="52908"/>
    <cge:TPSR_Ref TObjectID="9395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52911">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -545.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9396" ObjectName="SW-CX_CB.CX_CB_49017SW"/>
     <cge:Meas_Ref ObjectId="52911"/>
    <cge:TPSR_Ref TObjectID="9396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52833">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -768.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9384" ObjectName="SW-CX_CB.CX_CB_3011SW"/>
     <cge:Meas_Ref ObjectId="52833"/>
    <cge:TPSR_Ref TObjectID="9384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52839">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -578.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9386" ObjectName="SW-CX_CB.CX_CB_4016SW"/>
     <cge:Meas_Ref ObjectId="52839"/>
    <cge:TPSR_Ref TObjectID="9386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52840">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9387" ObjectName="SW-CX_CB.CX_CB_4011SW"/>
     <cge:Meas_Ref ObjectId="52840"/>
    <cge:TPSR_Ref TObjectID="9387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9358" ObjectName="SW-CX_CB.CX_CB_4511SW"/>
     <cge:Meas_Ref ObjectId="52771"/>
    <cge:TPSR_Ref TObjectID="9358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -356.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9359" ObjectName="SW-CX_CB.CX_CB_45117SW"/>
     <cge:Meas_Ref ObjectId="52772"/>
    <cge:TPSR_Ref TObjectID="9359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9357" ObjectName="SW-CX_CB.CX_CB_4512SW"/>
     <cge:Meas_Ref ObjectId="52770"/>
    <cge:TPSR_Ref TObjectID="9357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9360" ObjectName="SW-CX_CB.CX_CB_4516SW"/>
     <cge:Meas_Ref ObjectId="52773"/>
    <cge:TPSR_Ref TObjectID="9360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52763">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9353" ObjectName="SW-CX_CB.CX_CB_4521SW"/>
     <cge:Meas_Ref ObjectId="52763"/>
    <cge:TPSR_Ref TObjectID="9353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -356.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9354" ObjectName="SW-CX_CB.CX_CB_45217SW"/>
     <cge:Meas_Ref ObjectId="52764"/>
    <cge:TPSR_Ref TObjectID="9354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9352" ObjectName="SW-CX_CB.CX_CB_4522SW"/>
     <cge:Meas_Ref ObjectId="52762"/>
    <cge:TPSR_Ref TObjectID="9352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3850.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9355" ObjectName="SW-CX_CB.CX_CB_4526SW"/>
     <cge:Meas_Ref ObjectId="52765"/>
    <cge:TPSR_Ref TObjectID="9355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52756">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4060.000000 -356.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9349" ObjectName="SW-CX_CB.CX_CB_45317SW"/>
     <cge:Meas_Ref ObjectId="52756"/>
    <cge:TPSR_Ref TObjectID="9349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9343" ObjectName="SW-CX_CB.CX_CB_4541SW"/>
     <cge:Meas_Ref ObjectId="52747"/>
    <cge:TPSR_Ref TObjectID="9343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52748">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -356.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9344" ObjectName="SW-CX_CB.CX_CB_45417SW"/>
     <cge:Meas_Ref ObjectId="52748"/>
    <cge:TPSR_Ref TObjectID="9344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9342" ObjectName="SW-CX_CB.CX_CB_4542SW"/>
     <cge:Meas_Ref ObjectId="52746"/>
    <cge:TPSR_Ref TObjectID="9342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52749">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9345" ObjectName="SW-CX_CB.CX_CB_4546SW"/>
     <cge:Meas_Ref ObjectId="52749"/>
    <cge:TPSR_Ref TObjectID="9345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9363" ObjectName="SW-CX_CB.CX_CB_4581SW"/>
     <cge:Meas_Ref ObjectId="52779"/>
    <cge:TPSR_Ref TObjectID="9363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52780">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5082.000000 -356.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9364" ObjectName="SW-CX_CB.CX_CB_45817SW"/>
     <cge:Meas_Ref ObjectId="52780"/>
    <cge:TPSR_Ref TObjectID="9364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52778">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9362" ObjectName="SW-CX_CB.CX_CB_4582SW"/>
     <cge:Meas_Ref ObjectId="52778"/>
    <cge:TPSR_Ref TObjectID="9362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9365" ObjectName="SW-CX_CB.CX_CB_4586SW"/>
     <cge:Meas_Ref ObjectId="52781"/>
    <cge:TPSR_Ref TObjectID="9365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9368" ObjectName="SW-CX_CB.CX_CB_4571SW"/>
     <cge:Meas_Ref ObjectId="52787"/>
    <cge:TPSR_Ref TObjectID="9368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4893.000000 -356.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9369" ObjectName="SW-CX_CB.CX_CB_45717SW"/>
     <cge:Meas_Ref ObjectId="52788"/>
    <cge:TPSR_Ref TObjectID="9369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9367" ObjectName="SW-CX_CB.CX_CB_4572SW"/>
     <cge:Meas_Ref ObjectId="52786"/>
    <cge:TPSR_Ref TObjectID="9367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52789">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9370" ObjectName="SW-CX_CB.CX_CB_4576SW"/>
     <cge:Meas_Ref ObjectId="52789"/>
    <cge:TPSR_Ref TObjectID="9370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52732">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4702.000000 -356.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9334" ObjectName="SW-CX_CB.CX_CB_45617SW"/>
     <cge:Meas_Ref ObjectId="52732"/>
    <cge:TPSR_Ref TObjectID="9334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52739">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9338" ObjectName="SW-CX_CB.CX_CB_4551SW"/>
     <cge:Meas_Ref ObjectId="52739"/>
    <cge:TPSR_Ref TObjectID="9338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52740">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 -356.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9339" ObjectName="SW-CX_CB.CX_CB_45517SW"/>
     <cge:Meas_Ref ObjectId="52740"/>
    <cge:TPSR_Ref TObjectID="9339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52738">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9337" ObjectName="SW-CX_CB.CX_CB_4552SW"/>
     <cge:Meas_Ref ObjectId="52738"/>
    <cge:TPSR_Ref TObjectID="9337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52741">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9340" ObjectName="SW-CX_CB.CX_CB_4556SW"/>
     <cge:Meas_Ref ObjectId="52741"/>
    <cge:TPSR_Ref TObjectID="9340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52755">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9348" ObjectName="SW-LF_CB.LF_CB_4531SW"/>
     <cge:Meas_Ref ObjectId="52755"/>
    <cge:TPSR_Ref TObjectID="9348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9347" ObjectName="SW-LF_CB.LF_CB_4532SW"/>
     <cge:Meas_Ref ObjectId="52754"/>
    <cge:TPSR_Ref TObjectID="9347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52731">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9333" ObjectName="SW-LF_CB.LF_CB_4561SW"/>
     <cge:Meas_Ref ObjectId="52731"/>
    <cge:TPSR_Ref TObjectID="9333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9332" ObjectName="SW-LF_CB.LF_CB_4562SW"/>
     <cge:Meas_Ref ObjectId="52730"/>
    <cge:TPSR_Ref TObjectID="9332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52757">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9350" ObjectName="SW-LF_CB.LF_CB_4536SW"/>
     <cge:Meas_Ref ObjectId="52757"/>
    <cge:TPSR_Ref TObjectID="9350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52733">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9335" ObjectName="SW-LF_CB.LF_CB_4566SW"/>
     <cge:Meas_Ref ObjectId="52733"/>
    <cge:TPSR_Ref TObjectID="9335"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3865.000000 -1013.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3865.000000 -1013.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_CB.CX_CB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="13350"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.707493 -630.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.707493 -630.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="9400" ObjectName="TF-CX_CB.CX_CB_2T"/>
    <cge:TPSR_Ref TObjectID="9400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_CB.CX_CB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="13346"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -630.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -630.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="9399" ObjectName="TF-CX_CB.CX_CB_1T"/>
    <cge:TPSR_Ref TObjectID="9399"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20f0200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3665.000000 -1044.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2441de0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4190.000000 -916.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2443280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 -926.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2443c90">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4213.000000 -1003.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24453a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 -1044.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2464330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.900576 -564.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2465470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.900576 -574.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2466160">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4676.900576 -651.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2475080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 -569.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2475d00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -574.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24769b0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4049.000000 -651.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2486710">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_248e660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2491d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2495800">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3692.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2497510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2498850">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.666667 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a0b60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.666667 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a45d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.666667 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a84a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.666667 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25715a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.666667 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25730a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.333333 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25790c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.333333 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257a330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.333333 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257ba00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.333333 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257d910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.333333 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257f140">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2587970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258b3e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258f2b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4261.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2590b20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2592040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_259a6d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_259e140">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5064.000000 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a2010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5093.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24970a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a6680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.666667 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25aeee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.666667 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b2950">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.666667 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b6570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.666667 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b8300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4806.666667 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b9820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.333333 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25bf840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.333333 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c0ab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.333333 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c2180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.333333 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c4740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.333333 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c5c70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ce490">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d1f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d5be0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d7590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261e1e0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3842.000000 -1068.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261fab0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4896.000000 -1069.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1055.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52671" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3862.000000 -920.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9379"/>
     <cge:Term_Ref ObjectID="13304"/>
    <cge:TPSR_Ref TObjectID="9379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52672" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3862.000000 -920.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52672" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9379"/>
     <cge:Term_Ref ObjectID="13304"/>
    <cge:TPSR_Ref TObjectID="9379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52669" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3862.000000 -920.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9379"/>
     <cge:Term_Ref ObjectID="13304"/>
    <cge:TPSR_Ref TObjectID="9379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52666" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4927.000000 -920.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52666" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9374"/>
     <cge:Term_Ref ObjectID="13294"/>
    <cge:TPSR_Ref TObjectID="9374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52667" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4927.000000 -920.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52667" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9374"/>
     <cge:Term_Ref ObjectID="13294"/>
    <cge:TPSR_Ref TObjectID="9374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52664" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4927.000000 -920.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9374"/>
     <cge:Term_Ref ObjectID="13294"/>
    <cge:TPSR_Ref TObjectID="9374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52683" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3876.000000 -570.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9385"/>
     <cge:Term_Ref ObjectID="13316"/>
    <cge:TPSR_Ref TObjectID="9385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52684" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3876.000000 -570.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9385"/>
     <cge:Term_Ref ObjectID="13316"/>
    <cge:TPSR_Ref TObjectID="9385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52680" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3876.000000 -570.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9385"/>
     <cge:Term_Ref ObjectID="13316"/>
    <cge:TPSR_Ref TObjectID="9385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52661" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -664.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52661" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9371"/>
     <cge:Term_Ref ObjectID="13288"/>
    <cge:TPSR_Ref TObjectID="9371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52662" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -664.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9371"/>
     <cge:Term_Ref ObjectID="13288"/>
    <cge:TPSR_Ref TObjectID="9371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52659" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -664.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9371"/>
     <cge:Term_Ref ObjectID="13288"/>
    <cge:TPSR_Ref TObjectID="9371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52695" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4967.000000 -570.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9390"/>
     <cge:Term_Ref ObjectID="13326"/>
    <cge:TPSR_Ref TObjectID="9390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52696" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4967.000000 -570.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9390"/>
     <cge:Term_Ref ObjectID="13326"/>
    <cge:TPSR_Ref TObjectID="9390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52692" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4967.000000 -570.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9390"/>
     <cge:Term_Ref ObjectID="13326"/>
    <cge:TPSR_Ref TObjectID="9390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9356"/>
     <cge:Term_Ref ObjectID="13258"/>
    <cge:TPSR_Ref TObjectID="9356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9356"/>
     <cge:Term_Ref ObjectID="13258"/>
    <cge:TPSR_Ref TObjectID="9356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52648" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9356"/>
     <cge:Term_Ref ObjectID="13258"/>
    <cge:TPSR_Ref TObjectID="9356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52645" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3893.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9351"/>
     <cge:Term_Ref ObjectID="13248"/>
    <cge:TPSR_Ref TObjectID="9351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52646" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3893.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9351"/>
     <cge:Term_Ref ObjectID="13248"/>
    <cge:TPSR_Ref TObjectID="9351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52643" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3893.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52643" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9351"/>
     <cge:Term_Ref ObjectID="13248"/>
    <cge:TPSR_Ref TObjectID="9351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52640" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4094.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9346"/>
     <cge:Term_Ref ObjectID="13238"/>
    <cge:TPSR_Ref TObjectID="9346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52641" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4094.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9346"/>
     <cge:Term_Ref ObjectID="13238"/>
    <cge:TPSR_Ref TObjectID="9346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52638" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4094.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9346"/>
     <cge:Term_Ref ObjectID="13238"/>
    <cge:TPSR_Ref TObjectID="9346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52635" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4295.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9341"/>
     <cge:Term_Ref ObjectID="13228"/>
    <cge:TPSR_Ref TObjectID="9341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52636" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4295.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9341"/>
     <cge:Term_Ref ObjectID="13228"/>
    <cge:TPSR_Ref TObjectID="9341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52633" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4295.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9341"/>
     <cge:Term_Ref ObjectID="13228"/>
    <cge:TPSR_Ref TObjectID="9341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52630" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4557.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9336"/>
     <cge:Term_Ref ObjectID="13218"/>
    <cge:TPSR_Ref TObjectID="9336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52631" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4557.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9336"/>
     <cge:Term_Ref ObjectID="13218"/>
    <cge:TPSR_Ref TObjectID="9336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52628" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4557.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9336"/>
     <cge:Term_Ref ObjectID="13218"/>
    <cge:TPSR_Ref TObjectID="9336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9331"/>
     <cge:Term_Ref ObjectID="13208"/>
    <cge:TPSR_Ref TObjectID="9331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9331"/>
     <cge:Term_Ref ObjectID="13208"/>
    <cge:TPSR_Ref TObjectID="9331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9331"/>
     <cge:Term_Ref ObjectID="13208"/>
    <cge:TPSR_Ref TObjectID="9331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52656" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4947.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9366"/>
     <cge:Term_Ref ObjectID="13278"/>
    <cge:TPSR_Ref TObjectID="9366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52657" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4947.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9366"/>
     <cge:Term_Ref ObjectID="13278"/>
    <cge:TPSR_Ref TObjectID="9366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52654" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4947.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9366"/>
     <cge:Term_Ref ObjectID="13278"/>
    <cge:TPSR_Ref TObjectID="9366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52651" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5128.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9361"/>
     <cge:Term_Ref ObjectID="13268"/>
    <cge:TPSR_Ref TObjectID="9361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52652" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5128.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9361"/>
     <cge:Term_Ref ObjectID="13268"/>
    <cge:TPSR_Ref TObjectID="9361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52649" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5128.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52649" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9361"/>
     <cge:Term_Ref ObjectID="13268"/>
    <cge:TPSR_Ref TObjectID="9361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-52713" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3636.000000 -865.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9328"/>
     <cge:Term_Ref ObjectID="13205"/>
    <cge:TPSR_Ref TObjectID="9328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-52715" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5127.000000 -511.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52715" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9330"/>
     <cge:Term_Ref ObjectID="13207"/>
    <cge:TPSR_Ref TObjectID="9330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-52714" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3611.000000 -511.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9329"/>
     <cge:Term_Ref ObjectID="13206"/>
    <cge:TPSR_Ref TObjectID="9329"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3874.000000 -1096.000000) translate(0,18)">35kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -962.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -962.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -962.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -962.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -962.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -962.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -962.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -524.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1103.500000) translate(0,16)">城北变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3709.000000 -1148.000000) translate(0,18)">上城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4164.000000 -1030.000000) translate(0,18)">35kVTV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4841.000000 -1119.000000) translate(0,18)">35kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4763.000000 -1148.000000) translate(0,18)">金城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4590.000000 -678.000000) translate(0,18)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3981.000000 -678.000000) translate(0,18)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3636.000000 -36.000000) translate(0,18)">铁路线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3683.000000 -338.000000) translate(0,15)">75/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3825.666667 -36.000000) translate(0,18)">石灰坝线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3871.666667 -340.000000) translate(0,15)">200/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4015.333333 -36.000000) translate(0,18)">城北III回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4059.333333 -342.000000) translate(0,15)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4205.000000 -36.000000) translate(0,18)">城北II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4251.000000 -339.000000) translate(0,15)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5037.000000 -36.000000) translate(0,18)">大横山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5082.000000 -343.000000) translate(0,15)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4847.666667 -36.000000) translate(0,18)">北厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4894.666667 -342.000000) translate(0,15)">200/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4647.333333 -36.000000) translate(0,18)">喜尧水渣厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4701.333333 -342.000000) translate(0,15)">200/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4468.000000 -36.000000) translate(0,18)">城北I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4516.000000 -341.000000) translate(0,15)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3874.000000 -1035.000000) translate(0,18)">35kV站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3746.000000 -910.000000) translate(0,15)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3744.000000 -957.000000) translate(0,15)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3744.000000 -863.000000) translate(0,15)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3749.000000 -1012.000000) translate(0,15)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4206.000000 -868.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4096.000000 -929.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4800.000000 -910.000000) translate(0,15)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4798.000000 -863.000000) translate(0,15)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4798.000000 -957.000000) translate(0,15)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -1012.000000) translate(0,15)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3582.000000 -844.000000) translate(0,15)">35kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4853.000000 -684.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3766.000000 -684.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3747.000000 -753.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3745.000000 -800.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4835.000000 -752.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4833.000000 -800.000000) translate(0,15)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4835.000000 -560.000000) translate(0,15)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4833.000000 -513.000000) translate(0,15)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4833.000000 -610.000000) translate(0,15)">4026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3747.000000 -560.000000) translate(0,15)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3745.000000 -610.000000) translate(0,15)">4016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3745.000000 -513.000000) translate(0,15)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4042.000000 -516.000000) translate(0,15)">4901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3938.000000 -577.000000) translate(0,15)">49017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3547.000000 -494.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5056.000000 -494.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3620.000000 -341.000000) translate(0,15)">451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3610.000000 -401.000000) translate(0,15)">4511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3610.000000 -278.000000) translate(0,15)">4512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3610.000000 -152.000000) translate(0,15)">4516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3814.000000 -341.000000) translate(0,15)">452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3804.000000 -401.000000) translate(0,15)">4521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3805.000000 -152.000000) translate(0,15)">4526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3804.000000 -278.000000) translate(0,15)">4522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3872.000000 -390.000000) translate(0,15)">45217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3681.000000 -390.000000) translate(0,15)">45117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4002.000000 -341.000000) translate(0,15)">453</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -278.000000) translate(0,15)">4532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -401.000000) translate(0,15)">4531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -152.000000) translate(0,15)">4536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4060.000000 -390.000000) translate(0,15)">45317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4194.000000 -341.000000) translate(0,15)">454</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -401.000000) translate(0,15)">4541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -152.000000) translate(0,15)">4546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -278.000000) translate(0,15)">4542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4250.000000 -390.000000) translate(0,15)">45417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5056.000000 -467.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3547.000000 -466.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4303.000000 -566.000000) translate(0,15)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -515.000000) translate(0,15)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4462.000000 -546.000000) translate(0,15)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4670.000000 -516.000000) translate(0,15)">4902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4560.000000 -577.000000) translate(0,15)">49027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4457.000000 -341.000000) translate(0,15)">455</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4447.000000 -401.000000) translate(0,15)">4551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4447.000000 -278.000000) translate(0,15)">4552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4513.000000 -390.000000) translate(0,15)">45517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4447.000000 -152.000000) translate(0,15)">4556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4643.000000 -341.000000) translate(0,15)">456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -401.000000) translate(0,15)">4561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -278.000000) translate(0,15)">4562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -152.000000) translate(0,15)">4566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4702.000000 -390.000000) translate(0,15)">45617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4840.000000 -341.000000) translate(0,15)">457</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4893.000000 -390.000000) translate(0,15)">45717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4830.000000 -401.000000) translate(0,15)">4571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4830.000000 -278.000000) translate(0,15)">4572</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4830.000000 -152.000000) translate(0,15)">4576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5024.000000 -341.000000) translate(0,15)">458</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5082.000000 -390.000000) translate(0,15)">45817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.000000 -152.000000) translate(0,15)">4586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.000000 -401.000000) translate(0,15)">4581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.000000 -278.000000) translate(0,15)">4582</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-52815">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -879.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9379" ObjectName="SW-CX_CB.CX_CB_351BK"/>
     <cge:Meas_Ref ObjectId="52815"/>
    <cge:TPSR_Ref TObjectID="9379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52799">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -879.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9374" ObjectName="SW-CX_CB.CX_CB_352BK"/>
     <cge:Meas_Ref ObjectId="52799"/>
    <cge:TPSR_Ref TObjectID="9374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.707493 -721.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9388" ObjectName="SW-CX_CB.CX_CB_302BK"/>
     <cge:Meas_Ref ObjectId="52862"/>
    <cge:TPSR_Ref TObjectID="9388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52868">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.707493 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9390" ObjectName="SW-CX_CB.CX_CB_402BK"/>
     <cge:Meas_Ref ObjectId="52868"/>
    <cge:TPSR_Ref TObjectID="9390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.759366 -535.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9371" ObjectName="SW-CX_CB.CX_CB_412BK"/>
     <cge:Meas_Ref ObjectId="52792"/>
    <cge:TPSR_Ref TObjectID="9371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52830">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -722.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9383" ObjectName="SW-CX_CB.CX_CB_301BK"/>
     <cge:Meas_Ref ObjectId="52830"/>
    <cge:TPSR_Ref TObjectID="9383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52836">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9385" ObjectName="SW-CX_CB.CX_CB_401BK"/>
     <cge:Meas_Ref ObjectId="52836"/>
    <cge:TPSR_Ref TObjectID="9385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9356" ObjectName="SW-CX_CB.CX_CB_451BK"/>
     <cge:Meas_Ref ObjectId="52768"/>
    <cge:TPSR_Ref TObjectID="9356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.666667 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9351" ObjectName="SW-CX_CB.CX_CB_452BK"/>
     <cge:Meas_Ref ObjectId="52760"/>
    <cge:TPSR_Ref TObjectID="9351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.333333 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9346" ObjectName="SW-CX_CB.CX_CB_453BK"/>
     <cge:Meas_Ref ObjectId="52752"/>
    <cge:TPSR_Ref TObjectID="9346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9341" ObjectName="SW-CX_CB.CX_CB_454BK"/>
     <cge:Meas_Ref ObjectId="52744"/>
    <cge:TPSR_Ref TObjectID="9341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9361" ObjectName="SW-CX_CB.CX_CB_458BK"/>
     <cge:Meas_Ref ObjectId="52776"/>
    <cge:TPSR_Ref TObjectID="9361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52784">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4870.666667 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9366" ObjectName="SW-CX_CB.CX_CB_457BK"/>
     <cge:Meas_Ref ObjectId="52784"/>
    <cge:TPSR_Ref TObjectID="9366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4679.333333 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9331" ObjectName="SW-CX_CB.CX_CB_456BK"/>
     <cge:Meas_Ref ObjectId="52728"/>
    <cge:TPSR_Ref TObjectID="9331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52736">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9336" ObjectName="SW-CX_CB.CX_CB_455BK"/>
     <cge:Meas_Ref ObjectId="52736"/>
    <cge:TPSR_Ref TObjectID="9336"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2615590">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3731.000000 -355.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2615fe0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -355.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26168d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 -355.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2617360">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4301.000000 -355.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2617df0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 -355.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2618880">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.000000 -355.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2619310">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -355.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2619da0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5134.000000 -355.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261a830">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -596.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261b2c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 -596.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261bd50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 -977.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261c7e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -977.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261d270">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -948.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer"/><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1114"/></g>
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1131"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 920.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 890.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 905.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 920.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.000000 890.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.000000 905.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4901.000000 570.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 540.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.000000 555.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 570.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3827.000000 540.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 555.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.000000 664.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4322.000000 634.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 649.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3667.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3832.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4043.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4018.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4478.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4675.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5061.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5075.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5050.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 865.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3515.000000 511.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 511.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_22b09c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1051 3723,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@1" ObjectIDND2="9381@x" ObjectIDZND0="g_20f0200@0" Pin0InfoVect0LinkObjId="g_20f0200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_1" Pin1InfoVect2LinkObjId="SW-52817_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1051 3723,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2420a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1121 3737,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="0@x" ObjectIDZND1="g_20f0200@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_20f0200_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1121 3737,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2420c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1082 3737,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDZND0="g_20f0200@0" ObjectIDZND1="0@1" ObjectIDZND2="9381@x" Pin0InfoVect0LinkObjId="g_20f0200_0" Pin0InfoVect1LinkObjId="SW-0_1" Pin0InfoVect2LinkObjId="SW-52817_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1082 3737,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24253f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1082 3756,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_20f0200@0" ObjectIDND1="0@x" ObjectIDND2="9381@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20f0200_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-52817_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1082 3756,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24255e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-1081 3818,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_261e1e0@0" Pin0InfoVect0LinkObjId="g_261e1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-1081 3818,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24257d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1026 3756,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9381@x" ObjectIDND1="9380@x" ObjectIDND2="g_20f0200@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52817_0" Pin1InfoVect1LinkObjId="SW-52816_0" Pin1InfoVect2LinkObjId="g_20f0200_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1026 3756,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24259c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-1026 3818,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-1026 3818,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24370b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1026 3737,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="9381@x" ObjectIDND2="9380@x" ObjectIDZND0="g_20f0200@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_20f0200_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-52817_0" Pin1InfoVect2LinkObjId="SW-52816_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1026 3737,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2437fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-983 3754,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_20f0200@0" ObjectIDND2="0@x" ObjectIDZND0="9381@0" Pin0InfoVect0LinkObjId="SW-52817_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_20f0200_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-983 3754,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24381b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-983 3806,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9381@1" ObjectIDZND0="g_261bd50@0" Pin0InfoVect0LinkObjId="g_261bd50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52817_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-983 3806,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2438a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-983 3737,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9381@x" ObjectIDND1="9380@x" ObjectIDZND0="0@x" ObjectIDZND1="g_20f0200@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_20f0200_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52817_0" Pin1InfoVect1LinkObjId="SW-52816_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-983 3737,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243a410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-966 3737,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9380@1" ObjectIDZND0="0@x" ObjectIDZND1="g_20f0200@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_20f0200_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-966 3737,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243b8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-914 3737,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9379@1" ObjectIDZND0="9380@0" Pin0InfoVect0LinkObjId="SW-52816_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52815_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-914 3737,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243d5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-821 3737,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9382@0" Pin0InfoVect0LinkObjId="SW-52818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-821 3737,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243d7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-872 3737,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9382@1" ObjectIDZND0="9379@0" Pin0InfoVect0LinkObjId="SW-52815_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52818_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-872 3737,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243f460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-821 4199,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9393@0" Pin0InfoVect0LinkObjId="SW-52904_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-821 4199,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-877 4199,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9393@1" ObjectIDZND0="g_2441de0@0" ObjectIDZND1="g_2443280@0" ObjectIDZND2="9394@x" Pin0InfoVect0LinkObjId="g_2441de0_0" Pin0InfoVect1LinkObjId="g_2443280_0" Pin0InfoVect2LinkObjId="SW-52907_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52904_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-877 4199,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2441a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-893 4161,-893 4161,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2441de0@0" ObjectIDND1="g_2443280@0" ObjectIDND2="9393@x" ObjectIDZND0="9394@0" Pin0InfoVect0LinkObjId="SW-52907_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2441de0_0" Pin1InfoVect1LinkObjId="g_2443280_0" Pin1InfoVect2LinkObjId="SW-52904_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-893 4161,-893 4161,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2441bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-938 4161,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9394@1" ObjectIDZND0="g_261d270@0" Pin0InfoVect0LinkObjId="g_261d270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52907_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-938 4161,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24423d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-952 4199,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2441de0@1" ObjectIDZND0="g_2443c90@0" Pin0InfoVect0LinkObjId="g_2443c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2441de0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-952 4199,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24425c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-909 4234,-909 4234,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9393@x" ObjectIDND1="9394@x" ObjectIDND2="g_2441de0@0" ObjectIDZND0="g_2443280@0" Pin0InfoVect0LinkObjId="g_2443280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52904_0" Pin1InfoVect1LinkObjId="SW-52907_0" Pin1InfoVect2LinkObjId="g_2441de0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-909 4234,-909 4234,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2442ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-893 4199,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9393@x" ObjectIDND1="9394@x" ObjectIDZND0="g_2441de0@0" ObjectIDZND1="g_2443280@0" Pin0InfoVect0LinkObjId="g_2441de0_0" Pin0InfoVect1LinkObjId="g_2443280_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52904_0" Pin1InfoVect1LinkObjId="SW-52907_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-893 4199,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2443090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-909 4199,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9393@x" ObjectIDND1="9394@x" ObjectIDND2="g_2443280@0" ObjectIDZND0="g_2441de0@0" Pin0InfoVect0LinkObjId="g_2441de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52904_0" Pin1InfoVect1LinkObjId="SW-52907_0" Pin1InfoVect2LinkObjId="g_2443280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-909 4199,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24451b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1051 4777,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="9376@x" ObjectIDND2="9375@x" ObjectIDZND0="g_24453a0@0" Pin0InfoVect0LinkObjId="g_24453a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-52801_0" Pin1InfoVect2LinkObjId="SW-52800_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1051 4777,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2445d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1121 4791,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_24453a0@0" ObjectIDZND1="9376@x" ObjectIDZND2="9375@x" Pin0InfoVect0LinkObjId="g_24453a0_0" Pin0InfoVect1LinkObjId="SW-52801_0" Pin0InfoVect2LinkObjId="SW-52800_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1121 4791,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2445f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1082 4791,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDZND0="g_24453a0@0" ObjectIDZND1="9376@x" ObjectIDZND2="9375@x" Pin0InfoVect0LinkObjId="g_24453a0_0" Pin0InfoVect1LinkObjId="SW-52801_0" Pin0InfoVect2LinkObjId="SW-52800_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1082 4791,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2448140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1083 4810,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_24453a0@0" ObjectIDND1="9376@x" ObjectIDND2="9375@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24453a0_0" Pin1InfoVect1LinkObjId="SW-52801_0" Pin1InfoVect2LinkObjId="SW-52800_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1083 4810,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2448360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-1082 4872,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_261fab0@0" Pin0InfoVect0LinkObjId="g_261fab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-1082 4872,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2449fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-983 4808,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9375@x" ObjectIDND1="0@x" ObjectIDND2="g_24453a0@0" ObjectIDZND0="9376@0" Pin0InfoVect0LinkObjId="SW-52801_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52800_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_24453a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-983 4808,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_244a200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4844,-983 4863,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9376@1" ObjectIDZND0="g_261c7e0@0" Pin0InfoVect0LinkObjId="g_261c7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52801_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4844,-983 4863,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_244c5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-966 4791,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="9375@1" ObjectIDZND0="9376@x" ObjectIDZND1="0@x" ObjectIDZND2="g_24453a0@0" Pin0InfoVect0LinkObjId="SW-52801_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_24453a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52800_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-966 4791,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_244e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-914 4791,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9374@1" ObjectIDZND0="9375@0" Pin0InfoVect0LinkObjId="SW-52800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-914 4791,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24507e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-821 4791,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9377@0" Pin0InfoVect0LinkObjId="SW-52802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-821 4791,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2450a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-872 4791,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9377@1" ObjectIDZND0="9374@0" Pin0InfoVect0LinkObjId="SW-52799_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-872 4791,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24523c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1050 4791,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_24453a0@0" ObjectIDZND0="9376@x" ObjectIDZND1="9375@x" Pin0InfoVect0LinkObjId="SW-52801_0" Pin0InfoVect1LinkObjId="SW-52800_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24453a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1050 4791,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2454ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-821 4826,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9389@1" Pin0InfoVect0LinkObjId="SW-52865_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-821 4826,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245bfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-635 4826,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="9400@0" ObjectIDZND0="9391@1" Pin0InfoVect0LinkObjId="SW-52871_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-635 4826,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245c220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-583 4826,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9391@0" ObjectIDZND0="9390@1" Pin0InfoVect0LinkObjId="SW-52868_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52871_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-583 4826,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245e9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-537 4826,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9390@0" ObjectIDZND0="9392@1" Pin0InfoVect0LinkObjId="SW-52872_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-537 4826,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245ec30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-486 4826,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9392@0" ObjectIDZND0="9330@0" Pin0InfoVect0LinkObjId="g_246cd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-486 4826,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2461420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-471 4663,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDZND0="9397@0" Pin0InfoVect0LinkObjId="SW-52912_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-471 4663,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2461680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-525 4663,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9397@1" ObjectIDZND0="g_2465470@0" ObjectIDZND1="g_2464330@0" ObjectIDZND2="9398@x" Pin0InfoVect0LinkObjId="g_2465470_0" Pin0InfoVect1LinkObjId="g_2464330_0" Pin0InfoVect2LinkObjId="SW-52915_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52912_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-525 4663,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2463e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-541 4625,-541 4625,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2465470@0" ObjectIDND1="g_2464330@0" ObjectIDND2="9397@x" ObjectIDZND0="9398@0" Pin0InfoVect0LinkObjId="SW-52915_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2465470_0" Pin1InfoVect1LinkObjId="g_2464330_0" Pin1InfoVect2LinkObjId="SW-52912_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-541 4625,-541 4625,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24640d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-586 4625,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9398@1" ObjectIDZND0="g_261b2c0@0" Pin0InfoVect0LinkObjId="g_261b2c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-586 4625,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2464af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-600 4663,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2464330@1" ObjectIDZND0="g_2466160@0" Pin0InfoVect0LinkObjId="g_2466160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2464330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-600 4663,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2464d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-557 4698,-557 4698,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9397@x" ObjectIDND1="9398@x" ObjectIDND2="g_2464330@0" ObjectIDZND0="g_2465470@0" Pin0InfoVect0LinkObjId="g_2465470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52912_0" Pin1InfoVect1LinkObjId="SW-52915_0" Pin1InfoVect2LinkObjId="g_2464330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-557 4698,-557 4698,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2464fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-541 4663,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9397@x" ObjectIDND1="9398@x" ObjectIDZND0="g_2465470@0" ObjectIDZND1="g_2464330@0" Pin0InfoVect0LinkObjId="g_2465470_0" Pin0InfoVect1LinkObjId="g_2464330_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52912_0" Pin1InfoVect1LinkObjId="SW-52915_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-541 4663,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2465210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-557 4663,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9397@x" ObjectIDND1="9398@x" ObjectIDND2="g_2465470@0" ObjectIDZND0="g_2464330@0" Pin0InfoVect0LinkObjId="g_2464330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52912_0" Pin1InfoVect1LinkObjId="SW-52915_0" Pin1InfoVect2LinkObjId="g_2465470_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-557 4663,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_246cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4455,-519 4455,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9373@0" ObjectIDZND0="9330@0" Pin0InfoVect0LinkObjId="g_245ec30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52796_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4455,-519 4455,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_246cf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-570 4294,-590 4455,-590 4455,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9371@1" ObjectIDZND0="9373@1" Pin0InfoVect0LinkObjId="SW-52796_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-570 4294,-590 4455,-590 4455,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_246f720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-471 4294,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDZND0="9372@0" Pin0InfoVect0LinkObjId="SW-52794_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-471 4294,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_246f980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-524 4294,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9372@1" ObjectIDZND0="9371@0" Pin0InfoVect0LinkObjId="SW-52792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-524 4294,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2472170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-471 4035,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDZND0="9395@0" Pin0InfoVect0LinkObjId="SW-52908_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-471 4035,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24723d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-525 4035,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9395@1" ObjectIDZND0="g_2475d00@0" ObjectIDZND1="g_2475080@0" ObjectIDZND2="9396@x" Pin0InfoVect0LinkObjId="g_2475d00_0" Pin0InfoVect1LinkObjId="g_2475080_0" Pin0InfoVect2LinkObjId="SW-52911_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52908_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-525 4035,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2474bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-541 3997,-541 3997,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2475d00@0" ObjectIDND1="g_2475080@0" ObjectIDND2="9395@x" ObjectIDZND0="9396@0" Pin0InfoVect0LinkObjId="SW-52911_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2475d00_0" Pin1InfoVect1LinkObjId="g_2475080_0" Pin1InfoVect2LinkObjId="SW-52908_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-541 3997,-541 3997,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2474e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-586 3997,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9396@1" ObjectIDZND0="g_261a830@0" Pin0InfoVect0LinkObjId="g_261a830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52911_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-586 3997,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2475840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-557 4070,-557 4070,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9395@x" ObjectIDND1="9396@x" ObjectIDND2="g_2475080@0" ObjectIDZND0="g_2475d00@0" Pin0InfoVect0LinkObjId="g_2475d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52908_0" Pin1InfoVect1LinkObjId="SW-52911_0" Pin1InfoVect2LinkObjId="g_2475080_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-557 4070,-557 4070,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2475aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-541 4035,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9395@x" ObjectIDND1="9396@x" ObjectIDZND0="g_2475d00@0" ObjectIDZND1="g_2475080@0" Pin0InfoVect0LinkObjId="g_2475d00_0" Pin0InfoVect1LinkObjId="g_2475080_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52908_0" Pin1InfoVect1LinkObjId="SW-52911_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-541 4035,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_247b7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-821 3738,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9384@1" Pin0InfoVect0LinkObjId="SW-52833_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-821 3738,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24829e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-635 3738,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="9399@0" ObjectIDZND0="9386@1" Pin0InfoVect0LinkObjId="SW-52839_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-635 3738,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2482c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-583 3738,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9386@0" ObjectIDZND0="9385@1" Pin0InfoVect0LinkObjId="SW-52836_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52839_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-583 3738,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2485410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-537 3738,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9385@0" ObjectIDZND0="9387@1" Pin0InfoVect0LinkObjId="SW-52840_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52836_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-537 3738,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2485670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-486 3738,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9387@0" ObjectIDZND0="9329@0" Pin0InfoVect0LinkObjId="g_25ef220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-486 3738,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24858d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-715 3738,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9399@1" ObjectIDZND0="9383@0" Pin0InfoVect0LinkObjId="SW-52830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-715 3738,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2485b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-757 3738,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9383@1" ObjectIDZND0="9384@0" Pin0InfoVect0LinkObjId="SW-52833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-757 3738,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2485d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-715 4826,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9400@1" ObjectIDZND0="9388@0" Pin0InfoVect0LinkObjId="SW-52862_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-715 4826,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2485ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-756 4826,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9388@1" ObjectIDZND0="9389@0" Pin0InfoVect0LinkObjId="SW-52865_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52862_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-756 4826,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2486250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-428 3680,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9329@0" ObjectIDND1="9358@x" ObjectIDZND0="g_2486710@0" Pin0InfoVect0LinkObjId="g_2486710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="SW-52771_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-428 3680,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24864b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-444 3668,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9329@0" ObjectIDZND0="g_2486710@0" ObjectIDZND1="9358@x" Pin0InfoVect0LinkObjId="g_2486710_0" Pin0InfoVect1LinkObjId="SW-52771_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-444 3668,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2489930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-427 3668,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDND1="g_2486710@0" ObjectIDZND0="9358@1" Pin0InfoVect0LinkObjId="SW-52771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="g_2486710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-427 3668,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2489b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-374 3668,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9358@0" ObjectIDZND0="9356@0" ObjectIDZND1="9359@x" Pin0InfoVect0LinkObjId="SW-52768_0" Pin0InfoVect1LinkObjId="SW-52772_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-374 3668,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_248c090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-361 3686,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9356@0" ObjectIDND1="9358@x" ObjectIDZND0="9359@0" Pin0InfoVect0LinkObjId="SW-52772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52768_0" Pin1InfoVect1LinkObjId="SW-52771_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-361 3686,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_248c2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-361 3735,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9359@1" ObjectIDZND0="g_2615590@0" Pin0InfoVect0LinkObjId="g_2615590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-361 3735,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_248e1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-361 3668,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9359@x" ObjectIDND1="9358@x" ObjectIDZND0="9356@1" Pin0InfoVect0LinkObjId="SW-52768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52772_0" Pin1InfoVect1LinkObjId="SW-52771_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-361 3668,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_248e400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-304 3680,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9356@x" ObjectIDND1="9357@x" ObjectIDZND0="g_248e660@0" Pin0InfoVect0LinkObjId="g_248e660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52768_0" Pin1InfoVect1LinkObjId="SW-52770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-304 3680,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_248f310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-318 3668,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9356@0" ObjectIDZND0="g_248e660@0" ObjectIDZND1="9357@x" Pin0InfoVect0LinkObjId="g_248e660_0" Pin0InfoVect1LinkObjId="SW-52770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-318 3668,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2491ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-303 3668,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9356@x" ObjectIDND1="g_248e660@0" ObjectIDZND0="9357@1" Pin0InfoVect0LinkObjId="SW-52770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52768_0" Pin1InfoVect1LinkObjId="g_248e660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-303 3668,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2494e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-174 3668,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2491d40@1" ObjectIDZND0="9360@1" Pin0InfoVect0LinkObjId="SW-52773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2491d40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-174 3668,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24950e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-113 3699,-113 3699,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9360@x" ObjectIDZND0="g_2495800@0" Pin0InfoVect0LinkObjId="g_2495800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-113 3699,-113 3699,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2495340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-125 3668,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9360@0" ObjectIDZND0="g_2495800@0" Pin0InfoVect0LinkObjId="g_2495800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-125 3668,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24955a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-113 3668,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_2495800@0" ObjectIDND1="9360@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2495800_0" Pin1InfoVect1LinkObjId="SW-52773_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-113 3668,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2423ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-241 3653,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2491d40@0" ObjectIDND1="9357@x" ObjectIDZND0="g_2497510@0" Pin0InfoVect0LinkObjId="g_2497510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2491d40_0" Pin1InfoVect1LinkObjId="SW-52770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-241 3653,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2423cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-251 3668,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9357@0" ObjectIDZND0="g_2497510@0" ObjectIDZND1="g_2491d40@0" Pin0InfoVect0LinkObjId="g_2497510_0" Pin0InfoVect1LinkObjId="g_2491d40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-251 3668,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2497320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-240 3668,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2497510@0" ObjectIDND1="9357@x" ObjectIDZND0="g_2491d40@0" Pin0InfoVect0LinkObjId="g_2491d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2497510_0" Pin1InfoVect1LinkObjId="SW-52770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-240 3668,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2498470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-428 3870,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9329@0" ObjectIDND1="9353@x" ObjectIDZND0="g_2498850@0" Pin0InfoVect0LinkObjId="g_2498850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="SW-52763_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-428 3870,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2498660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-444 3858,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9329@0" ObjectIDZND0="g_2498850@0" ObjectIDZND1="9353@x" Pin0InfoVect0LinkObjId="g_2498850_0" Pin0InfoVect1LinkObjId="SW-52763_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-444 3858,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-427 3858,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDND1="g_2498850@0" ObjectIDZND0="9353@1" Pin0InfoVect0LinkObjId="SW-52763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="g_2498850_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-427 3858,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-374 3858,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9353@0" ObjectIDZND0="9351@x" ObjectIDZND1="9354@x" Pin0InfoVect0LinkObjId="SW-52760_0" Pin0InfoVect1LinkObjId="SW-52764_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-374 3858,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-361 3876,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9351@x" ObjectIDND1="9353@x" ObjectIDZND0="9354@0" Pin0InfoVect0LinkObjId="SW-52764_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52760_0" Pin1InfoVect1LinkObjId="SW-52763_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-361 3876,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-361 3926,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9354@1" ObjectIDZND0="g_2615fe0@0" Pin0InfoVect0LinkObjId="g_2615fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52764_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-361 3926,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a06a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-361 3858,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9354@x" ObjectIDND1="9353@x" ObjectIDZND0="9351@1" Pin0InfoVect0LinkObjId="SW-52760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52764_0" Pin1InfoVect1LinkObjId="SW-52763_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-361 3858,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a0900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-304 3870,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9351@x" ObjectIDND1="9352@x" ObjectIDZND0="g_24a0b60@0" Pin0InfoVect0LinkObjId="g_24a0b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52760_0" Pin1InfoVect1LinkObjId="SW-52762_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-304 3870,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a1910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-318 3858,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9351@0" ObjectIDZND0="g_24a0b60@0" ObjectIDZND1="9352@x" Pin0InfoVect0LinkObjId="g_24a0b60_0" Pin0InfoVect1LinkObjId="SW-52762_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-318 3858,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a4370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-303 3858,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9351@x" ObjectIDND1="g_24a0b60@0" ObjectIDZND0="9352@1" Pin0InfoVect0LinkObjId="SW-52762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52760_0" Pin1InfoVect1LinkObjId="g_24a0b60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-303 3858,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a7b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3859,-174 3859,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_24a45d0@1" ObjectIDZND0="9355@1" Pin0InfoVect0LinkObjId="SW-52765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24a45d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3859,-174 3859,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a7d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3859,-113 3890,-113 3890,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9355@x" ObjectIDZND0="g_24a84a0@0" Pin0InfoVect0LinkObjId="g_24a84a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3859,-113 3890,-113 3890,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a7fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3859,-125 3859,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9355@0" ObjectIDZND0="g_24a84a0@0" Pin0InfoVect0LinkObjId="g_24a84a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3859,-125 3859,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3859,-113 3859,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_24a84a0@0" ObjectIDND1="9355@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24a84a0_0" Pin1InfoVect1LinkObjId="SW-52765_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3859,-113 3859,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24aa230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-241 3843,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_24a45d0@0" ObjectIDND1="9352@x" ObjectIDZND0="g_25715a0@0" Pin0InfoVect0LinkObjId="g_25715a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24a45d0_0" Pin1InfoVect1LinkObjId="SW-52762_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-241 3843,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24aa420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-251 3858,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9352@0" ObjectIDZND0="g_25715a0@0" ObjectIDZND1="g_24a45d0@0" Pin0InfoVect0LinkObjId="g_25715a0_0" Pin0InfoVect1LinkObjId="g_24a45d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-251 3858,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25713b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-240 3858,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25715a0@0" ObjectIDND1="9352@x" ObjectIDZND0="g_24a45d0@0" Pin0InfoVect0LinkObjId="g_24a45d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25715a0_0" Pin1InfoVect1LinkObjId="SW-52762_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-240 3858,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2572cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-428 4059,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9329@0" ObjectIDND1="9348@x" ObjectIDZND0="g_25730a0@0" Pin0InfoVect0LinkObjId="g_25730a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="SW-52755_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-428 4059,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2572eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-444 4047,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9329@0" ObjectIDZND0="g_25730a0@0" ObjectIDZND1="9348@x" Pin0InfoVect0LinkObjId="g_25730a0_0" Pin0InfoVect1LinkObjId="SW-52755_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-444 4047,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2573c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-427 4047,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDND1="g_25730a0@0" ObjectIDZND0="9348@1" Pin0InfoVect0LinkObjId="SW-52755_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="g_25730a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-427 4047,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2573ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-374 4047,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9348@0" ObjectIDZND0="9346@x" ObjectIDZND1="9349@x" Pin0InfoVect0LinkObjId="SW-52752_0" Pin0InfoVect1LinkObjId="SW-52756_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-374 4047,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2576670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-361 4065,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9346@x" ObjectIDND1="9348@x" ObjectIDZND0="9349@0" Pin0InfoVect0LinkObjId="SW-52756_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52752_0" Pin1InfoVect1LinkObjId="SW-52755_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-361 4065,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25768d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4101,-361 4113,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9349@1" ObjectIDZND0="g_26168d0@0" Pin0InfoVect0LinkObjId="g_26168d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52756_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4101,-361 4113,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2578c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-361 4047,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9349@x" ObjectIDND1="9348@x" ObjectIDZND0="9346@1" Pin0InfoVect0LinkObjId="SW-52752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52756_0" Pin1InfoVect1LinkObjId="SW-52755_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-361 4047,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2578e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-304 4059,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9346@x" ObjectIDND1="9347@x" ObjectIDZND0="g_25790c0@0" Pin0InfoVect0LinkObjId="g_25790c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52752_0" Pin1InfoVect1LinkObjId="SW-52754_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-304 4059,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2579e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-318 4047,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9346@0" ObjectIDZND0="g_25790c0@0" ObjectIDZND1="9347@x" Pin0InfoVect0LinkObjId="g_25790c0_0" Pin0InfoVect1LinkObjId="SW-52754_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-318 4047,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-303 4047,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9346@x" ObjectIDND1="g_25790c0@0" ObjectIDZND0="9347@1" Pin0InfoVect0LinkObjId="SW-52754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52752_0" Pin1InfoVect1LinkObjId="g_25790c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-303 4047,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257b080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-174 4047,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_257a330@1" ObjectIDZND0="9350@1" Pin0InfoVect0LinkObjId="SW-52757_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257a330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-174 4047,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-113 4078,-113 4078,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9350@x" ObjectIDZND0="g_257ba00@0" Pin0InfoVect0LinkObjId="g_257ba00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52757_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-113 4078,-113 4078,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257b540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-125 4047,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9350@0" ObjectIDZND0="g_257ba00@0" Pin0InfoVect0LinkObjId="g_257ba00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52757_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-125 4047,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257b7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-113 4047,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_257ba00@0" ObjectIDND1="9350@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_257ba00_0" Pin1InfoVect1LinkObjId="SW-52757_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-113 4047,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257d340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-241 4032,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_257a330@0" ObjectIDND1="9347@x" ObjectIDZND0="g_257d910@0" Pin0InfoVect0LinkObjId="g_257d910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_257a330_0" Pin1InfoVect1LinkObjId="SW-52754_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-241 4032,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257d530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-251 4047,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9347@0" ObjectIDZND0="g_257d910@0" ObjectIDZND1="g_257a330@0" Pin0InfoVect0LinkObjId="g_257d910_0" Pin0InfoVect1LinkObjId="g_257a330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-251 4047,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257d720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-240 4047,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_257d910@0" ObjectIDND1="9347@x" ObjectIDZND0="g_257a330@0" Pin0InfoVect0LinkObjId="g_257a330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_257d910_0" Pin1InfoVect1LinkObjId="SW-52754_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-240 4047,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257ed60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-428 4249,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9329@0" ObjectIDND1="9343@x" ObjectIDZND0="g_257f140@0" Pin0InfoVect0LinkObjId="g_257f140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="SW-52747_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-428 4249,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257ef50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-444 4237,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9329@0" ObjectIDZND0="g_257f140@0" ObjectIDZND1="9343@x" Pin0InfoVect0LinkObjId="g_257f140_0" Pin0InfoVect1LinkObjId="SW-52747_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-444 4237,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2582530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-427 4237,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDND1="g_257f140@0" ObjectIDZND0="9343@1" Pin0InfoVect0LinkObjId="SW-52747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="g_257f140_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-427 4237,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2582790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-374 4237,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9343@0" ObjectIDZND0="9341@x" ObjectIDZND1="9344@x" Pin0InfoVect0LinkObjId="SW-52744_0" Pin0InfoVect1LinkObjId="SW-52748_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52747_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-374 4237,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2584f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-361 4255,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9341@x" ObjectIDND1="9343@x" ObjectIDZND0="9344@0" Pin0InfoVect0LinkObjId="SW-52748_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52744_0" Pin1InfoVect1LinkObjId="SW-52747_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-361 4255,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2585180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4291,-361 4305,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9344@1" ObjectIDZND0="g_2617360@0" Pin0InfoVect0LinkObjId="g_2617360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52748_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4291,-361 4305,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25874b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-361 4237,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9344@x" ObjectIDND1="9343@x" ObjectIDZND0="9341@1" Pin0InfoVect0LinkObjId="SW-52744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52748_0" Pin1InfoVect1LinkObjId="SW-52747_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-361 4237,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2587710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-304 4249,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9341@x" ObjectIDND1="9342@x" ObjectIDZND0="g_2587970@0" Pin0InfoVect0LinkObjId="g_2587970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52744_0" Pin1InfoVect1LinkObjId="SW-52746_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-304 4249,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2588720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-318 4237,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9341@0" ObjectIDZND0="g_2587970@0" ObjectIDZND1="9342@x" Pin0InfoVect0LinkObjId="g_2587970_0" Pin0InfoVect1LinkObjId="SW-52746_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-318 4237,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258b180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-303 4237,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9341@x" ObjectIDND1="g_2587970@0" ObjectIDZND0="9342@1" Pin0InfoVect0LinkObjId="SW-52746_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52744_0" Pin1InfoVect1LinkObjId="g_2587970_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-303 4237,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258e930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-174 4237,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_258b3e0@1" ObjectIDZND0="9345@1" Pin0InfoVect0LinkObjId="SW-52749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258b3e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-174 4237,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258eb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-113 4268,-113 4268,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9345@x" ObjectIDZND0="g_258f2b0@0" Pin0InfoVect0LinkObjId="g_258f2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-113 4268,-113 4268,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258edf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-125 4237,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9345@0" ObjectIDZND0="g_258f2b0@0" Pin0InfoVect0LinkObjId="g_258f2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-125 4237,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258f050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-113 4237,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_258f2b0@0" ObjectIDND1="9345@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_258f2b0_0" Pin1InfoVect1LinkObjId="SW-52749_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-113 4237,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2590550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-241 4222,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_258b3e0@0" ObjectIDND1="9342@x" ObjectIDZND0="g_2590b20@0" Pin0InfoVect0LinkObjId="g_2590b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_258b3e0_0" Pin1InfoVect1LinkObjId="SW-52746_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-241 4222,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2590740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-251 4237,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9342@0" ObjectIDZND0="g_2590b20@0" ObjectIDZND1="g_258b3e0@0" Pin0InfoVect0LinkObjId="g_2590b20_0" Pin0InfoVect1LinkObjId="g_258b3e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-251 4237,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2590930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-240 4237,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2590b20@0" ObjectIDND1="9342@x" ObjectIDZND0="g_258b3e0@0" Pin0InfoVect0LinkObjId="g_258b3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2590b20_0" Pin1InfoVect1LinkObjId="SW-52746_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-240 4237,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2591c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-428 5081,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9330@0" ObjectIDND1="9363@x" ObjectIDZND0="g_2592040@0" Pin0InfoVect0LinkObjId="g_2592040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="SW-52779_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-428 5081,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2591e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-444 5069,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9330@0" ObjectIDZND0="g_2592040@0" ObjectIDZND1="9363@x" Pin0InfoVect0LinkObjId="g_2592040_0" Pin0InfoVect1LinkObjId="SW-52779_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-444 5069,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2595290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-427 5069,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDND1="g_2592040@0" ObjectIDZND0="9363@1" Pin0InfoVect0LinkObjId="SW-52779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="g_2592040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-427 5069,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25954f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-374 5069,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9363@0" ObjectIDZND0="9361@x" ObjectIDZND1="9364@x" Pin0InfoVect0LinkObjId="SW-52776_0" Pin0InfoVect1LinkObjId="SW-52780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-374 5069,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2597c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-361 5087,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9361@x" ObjectIDND1="9363@x" ObjectIDZND0="9364@0" Pin0InfoVect0LinkObjId="SW-52780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52776_0" Pin1InfoVect1LinkObjId="SW-52779_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-361 5087,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2597ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-361 5138,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9364@1" ObjectIDZND0="g_2619da0@0" Pin0InfoVect0LinkObjId="g_2619da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-361 5138,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259a210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-361 5069,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9364@x" ObjectIDND1="9363@x" ObjectIDZND0="9361@1" Pin0InfoVect0LinkObjId="SW-52776_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52780_0" Pin1InfoVect1LinkObjId="SW-52779_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-361 5069,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259a470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-304 5081,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9361@x" ObjectIDND1="9362@x" ObjectIDZND0="g_259a6d0@0" Pin0InfoVect0LinkObjId="g_259a6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52776_0" Pin1InfoVect1LinkObjId="SW-52778_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-304 5081,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259b480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-318 5069,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9361@0" ObjectIDZND0="g_259a6d0@0" ObjectIDZND1="9362@x" Pin0InfoVect0LinkObjId="g_259a6d0_0" Pin0InfoVect1LinkObjId="SW-52778_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-318 5069,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259dee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-303 5069,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9361@x" ObjectIDND1="g_259a6d0@0" ObjectIDZND0="9362@1" Pin0InfoVect0LinkObjId="SW-52778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52776_0" Pin1InfoVect1LinkObjId="g_259a6d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-303 5069,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a1690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-174 5069,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_259e140@1" ObjectIDZND0="9365@1" Pin0InfoVect0LinkObjId="SW-52781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_259e140_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-174 5069,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a18f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-113 5100,-113 5100,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9365@x" ObjectIDZND0="g_25a2010@0" Pin0InfoVect0LinkObjId="g_25a2010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-113 5100,-113 5100,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a1b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-125 5069,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9365@0" ObjectIDZND0="g_25a2010@0" Pin0InfoVect0LinkObjId="g_25a2010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-125 5069,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a1db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-113 5069,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_25a2010@0" ObjectIDND1="9365@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25a2010_0" Pin1InfoVect1LinkObjId="SW-52781_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-113 5069,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2496ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-241 5054,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_259e140@0" ObjectIDND1="9362@x" ObjectIDZND0="g_24970a0@0" Pin0InfoVect0LinkObjId="g_24970a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_259e140_0" Pin1InfoVect1LinkObjId="SW-52778_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-241 5054,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2496cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-251 5069,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9362@0" ObjectIDZND0="g_24970a0@0" ObjectIDZND1="g_259e140@0" Pin0InfoVect0LinkObjId="g_24970a0_0" Pin0InfoVect1LinkObjId="g_259e140_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-251 5069,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2496eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-240 5069,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_24970a0@0" ObjectIDND1="9362@x" ObjectIDZND0="g_259e140@0" Pin0InfoVect0LinkObjId="g_259e140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24970a0_0" Pin1InfoVect1LinkObjId="SW-52778_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-240 5069,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a62a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-428 4892,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9330@0" ObjectIDND1="9368@x" ObjectIDZND0="g_25a6680@0" Pin0InfoVect0LinkObjId="g_25a6680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="SW-52787_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-428 4892,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a6490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-444 4880,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9330@0" ObjectIDZND0="g_25a6680@0" ObjectIDZND1="9368@x" Pin0InfoVect0LinkObjId="g_25a6680_0" Pin0InfoVect1LinkObjId="SW-52787_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-444 4880,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a9aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-427 4880,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDND1="g_25a6680@0" ObjectIDZND0="9368@1" Pin0InfoVect0LinkObjId="SW-52787_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="g_25a6680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-427 4880,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a9d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-374 4880,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9368@0" ObjectIDZND0="9366@x" ObjectIDZND1="9369@x" Pin0InfoVect0LinkObjId="SW-52784_0" Pin0InfoVect1LinkObjId="SW-52788_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-374 4880,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ac490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-361 4898,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9366@x" ObjectIDND1="9368@x" ObjectIDZND0="9369@0" Pin0InfoVect0LinkObjId="SW-52788_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52784_0" Pin1InfoVect1LinkObjId="SW-52787_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-361 4898,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ac6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4934,-361 4950,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9369@1" ObjectIDZND0="g_2619310@0" Pin0InfoVect0LinkObjId="g_2619310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4934,-361 4950,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25aea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-361 4880,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9369@x" ObjectIDND1="9368@x" ObjectIDZND0="9366@1" Pin0InfoVect0LinkObjId="SW-52784_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52788_0" Pin1InfoVect1LinkObjId="SW-52787_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-361 4880,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25aec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-304 4892,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9366@x" ObjectIDND1="9367@x" ObjectIDZND0="g_25aeee0@0" Pin0InfoVect0LinkObjId="g_25aeee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52784_0" Pin1InfoVect1LinkObjId="SW-52786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-304 4892,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25afc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-318 4880,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9366@0" ObjectIDZND0="g_25aeee0@0" ObjectIDZND1="9367@x" Pin0InfoVect0LinkObjId="g_25aeee0_0" Pin0InfoVect1LinkObjId="SW-52786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-318 4880,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b26f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-303 4880,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9366@x" ObjectIDND1="g_25aeee0@0" ObjectIDZND0="9367@1" Pin0InfoVect0LinkObjId="SW-52786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52784_0" Pin1InfoVect1LinkObjId="g_25aeee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-303 4880,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b5bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-174 4881,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_25b2950@1" ObjectIDZND0="9370@1" Pin0InfoVect0LinkObjId="SW-52789_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b2950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-174 4881,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b5e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-113 4912,-113 4912,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9370@x" ObjectIDZND0="g_25b6570@0" Pin0InfoVect0LinkObjId="g_25b6570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-113 4912,-113 4912,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b60b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-125 4881,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9370@0" ObjectIDZND0="g_25b6570@0" Pin0InfoVect0LinkObjId="g_25b6570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-125 4881,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b6310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-113 4881,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_25b6570@0" ObjectIDND1="9370@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25b6570_0" Pin1InfoVect1LinkObjId="SW-52789_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-113 4881,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b7d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-241 4865,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25b2950@0" ObjectIDND1="9367@x" ObjectIDZND0="g_25b8300@0" Pin0InfoVect0LinkObjId="g_25b8300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25b2950_0" Pin1InfoVect1LinkObjId="SW-52786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-241 4865,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b7f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-251 4880,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9367@0" ObjectIDZND0="g_25b8300@0" ObjectIDZND1="g_25b2950@0" Pin0InfoVect0LinkObjId="g_25b8300_0" Pin0InfoVect1LinkObjId="g_25b2950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-251 4880,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b80f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-240 4880,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25b8300@0" ObjectIDND1="9367@x" ObjectIDZND0="g_25b2950@0" Pin0InfoVect0LinkObjId="g_25b2950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25b8300_0" Pin1InfoVect1LinkObjId="SW-52786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-240 4880,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b9440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-428 4701,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9330@0" ObjectIDND1="9333@x" ObjectIDZND0="g_25b9820@0" Pin0InfoVect0LinkObjId="g_25b9820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="SW-52731_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-428 4701,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b9630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-444 4689,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9330@0" ObjectIDZND0="g_25b9820@0" ObjectIDZND1="9333@x" Pin0InfoVect0LinkObjId="g_25b9820_0" Pin0InfoVect1LinkObjId="SW-52731_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-444 4689,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ba400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-427 4689,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDND1="g_25b9820@0" ObjectIDZND0="9333@1" Pin0InfoVect0LinkObjId="SW-52731_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="g_25b9820_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-427 4689,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ba660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-374 4689,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9333@0" ObjectIDZND0="9331@x" ObjectIDZND1="9334@x" Pin0InfoVect0LinkObjId="SW-52728_0" Pin0InfoVect1LinkObjId="SW-52732_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52731_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-374 4689,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bcdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-361 4707,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9331@x" ObjectIDND1="9333@x" ObjectIDZND0="9334@0" Pin0InfoVect0LinkObjId="SW-52732_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52728_0" Pin1InfoVect1LinkObjId="SW-52731_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-361 4707,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bd050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-361 4758,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9334@1" ObjectIDZND0="g_2618880@0" Pin0InfoVect0LinkObjId="g_2618880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52732_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-361 4758,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bf380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-361 4689,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9334@x" ObjectIDND1="9333@x" ObjectIDZND0="9331@1" Pin0InfoVect0LinkObjId="SW-52728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52732_0" Pin1InfoVect1LinkObjId="SW-52731_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-361 4689,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bf5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-304 4701,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9331@x" ObjectIDND1="9332@x" ObjectIDZND0="g_25bf840@0" Pin0InfoVect0LinkObjId="g_25bf840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52728_0" Pin1InfoVect1LinkObjId="SW-52730_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-304 4701,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c05f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-318 4689,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9331@0" ObjectIDZND0="g_25bf840@0" ObjectIDZND1="9332@x" Pin0InfoVect0LinkObjId="g_25bf840_0" Pin0InfoVect1LinkObjId="SW-52730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-318 4689,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c0850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-303 4689,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9331@x" ObjectIDND1="g_25bf840@0" ObjectIDZND0="9332@1" Pin0InfoVect0LinkObjId="SW-52730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52728_0" Pin1InfoVect1LinkObjId="g_25bf840_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-303 4689,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c1800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-174 4689,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_25c0ab0@1" ObjectIDZND0="9335@1" Pin0InfoVect0LinkObjId="SW-52733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c0ab0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-174 4689,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c1a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-113 4720,-113 4720,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9335@x" ObjectIDZND0="g_25c2180@0" Pin0InfoVect0LinkObjId="g_25c2180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-113 4720,-113 4720,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c1cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-125 4689,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9335@0" ObjectIDZND0="g_25c2180@0" Pin0InfoVect0LinkObjId="g_25c2180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-125 4689,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c1f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-113 4689,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_25c2180@0" ObjectIDND1="9335@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25c2180_0" Pin1InfoVect1LinkObjId="SW-52733_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-113 4689,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c4170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-241 4674,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25c0ab0@0" ObjectIDND1="9332@x" ObjectIDZND0="g_25c4740@0" Pin0InfoVect0LinkObjId="g_25c4740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25c0ab0_0" Pin1InfoVect1LinkObjId="SW-52730_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-241 4674,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c4360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-251 4689,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9332@0" ObjectIDZND0="g_25c4740@0" ObjectIDZND1="g_25c0ab0@0" Pin0InfoVect0LinkObjId="g_25c4740_0" Pin0InfoVect1LinkObjId="g_25c0ab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-251 4689,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c4550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-240 4689,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25c4740@0" ObjectIDND1="9332@x" ObjectIDZND0="g_25c0ab0@0" Pin0InfoVect0LinkObjId="g_25c0ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25c4740_0" Pin1InfoVect1LinkObjId="SW-52730_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-240 4689,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c5890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-428 4512,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9330@0" ObjectIDND1="9338@x" ObjectIDZND0="g_25c5c70@0" Pin0InfoVect0LinkObjId="g_25c5c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="SW-52739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-428 4512,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c5a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-444 4500,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9330@0" ObjectIDZND0="g_25c5c70@0" ObjectIDZND1="9338@x" Pin0InfoVect0LinkObjId="g_25c5c70_0" Pin0InfoVect1LinkObjId="SW-52739_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-444 4500,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c9050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-427 4500,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDND1="g_25c5c70@0" ObjectIDZND0="9338@1" Pin0InfoVect0LinkObjId="SW-52739_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="g_25c5c70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-427 4500,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c92b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-374 4500,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9338@0" ObjectIDZND0="9336@x" ObjectIDZND1="9339@x" Pin0InfoVect0LinkObjId="SW-52736_0" Pin0InfoVect1LinkObjId="SW-52740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-374 4500,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25cba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-361 4518,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9336@x" ObjectIDND1="9338@x" ObjectIDZND0="9339@0" Pin0InfoVect0LinkObjId="SW-52740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52736_0" Pin1InfoVect1LinkObjId="SW-52739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-361 4518,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25cbca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4554,-361 4568,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9339@1" ObjectIDZND0="g_2617df0@0" Pin0InfoVect0LinkObjId="g_2617df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4554,-361 4568,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25cdfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-361 4500,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9339@x" ObjectIDND1="9338@x" ObjectIDZND0="9336@1" Pin0InfoVect0LinkObjId="SW-52736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52740_0" Pin1InfoVect1LinkObjId="SW-52739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-361 4500,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ce230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-304 4512,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9336@x" ObjectIDND1="9337@x" ObjectIDZND0="g_25ce490@0" Pin0InfoVect0LinkObjId="g_25ce490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52736_0" Pin1InfoVect1LinkObjId="SW-52738_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-304 4512,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25cf240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-318 4500,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9336@0" ObjectIDZND0="g_25ce490@0" ObjectIDZND1="9337@x" Pin0InfoVect0LinkObjId="g_25ce490_0" Pin0InfoVect1LinkObjId="SW-52738_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-318 4500,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d1ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-303 4500,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9336@x" ObjectIDND1="g_25ce490@0" ObjectIDZND0="9337@1" Pin0InfoVect0LinkObjId="SW-52738_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52736_0" Pin1InfoVect1LinkObjId="g_25ce490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-303 4500,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d5260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-174 4500,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_25d1f00@1" ObjectIDZND0="9340@1" Pin0InfoVect0LinkObjId="SW-52741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25d1f00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-174 4500,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d54c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-113 4531,-113 4531,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9340@x" ObjectIDZND0="g_25d5be0@0" Pin0InfoVect0LinkObjId="g_25d5be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-113 4531,-113 4531,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d5720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-125 4500,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9340@0" ObjectIDZND0="g_25d5be0@0" Pin0InfoVect0LinkObjId="g_25d5be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-125 4500,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d5980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-113 4500,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_25d5be0@0" ObjectIDND1="9340@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25d5be0_0" Pin1InfoVect1LinkObjId="SW-52741_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-113 4500,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d6fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-241 4485,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25d1f00@0" ObjectIDND1="9337@x" ObjectIDZND0="g_25d7590@0" Pin0InfoVect0LinkObjId="g_25d7590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25d1f00_0" Pin1InfoVect1LinkObjId="SW-52738_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-241 4485,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d71b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-251 4500,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9337@0" ObjectIDZND0="g_25d7590@0" ObjectIDZND1="g_25d1f00@0" Pin0InfoVect0LinkObjId="g_25d7590_0" Pin0InfoVect1LinkObjId="g_25d1f00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52738_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-251 4500,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d73a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-240 4500,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25d7590@0" ObjectIDND1="9337@x" ObjectIDZND0="g_25d1f00@0" Pin0InfoVect0LinkObjId="g_25d1f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25d7590_0" Pin1InfoVect1LinkObjId="SW-52738_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-240 4500,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ef220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3739,-471 3739,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="9329@0" ObjectIDZND0="9329@0" Pin0InfoVect0LinkObjId="g_2485670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2485670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3739,-471 3739,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-471 4826,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="9330@0" ObjectIDZND0="9330@0" Pin0InfoVect0LinkObjId="g_245ec30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_245ec30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-471 4826,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f9110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-557 4035,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9395@x" ObjectIDND1="9396@x" ObjectIDND2="g_2475d00@0" ObjectIDZND0="g_2475080@0" Pin0InfoVect0LinkObjId="g_2475080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52908_0" Pin1InfoVect1LinkObjId="SW-52911_0" Pin1InfoVect2LinkObjId="g_2475d00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-557 4035,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f9300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-605 4035,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2475080@1" ObjectIDZND0="g_24769b0@0" Pin0InfoVect0LinkObjId="g_24769b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2475080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-605 4035,-626 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-821 5109,-821 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9328" ObjectName="BS-CX_CB.CX_CB_3IM"/>
    <cge:TPSR_Ref TObjectID="9328"/></metadata>
   <polyline fill="none" opacity="0" points="3582,-821 5109,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-471 5132,-471 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9330" ObjectName="BS-CX_CB.CX_CB_9IIM"/>
    <cge:TPSR_Ref TObjectID="9330"/></metadata>
   <polyline fill="none" opacity="0" points="4416,-471 5132,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-444 5132,-444 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9330" ObjectName="BS-CX_CB.CX_CB_9IIM"/>
    <cge:TPSR_Ref TObjectID="9330"/></metadata>
   <polyline fill="none" opacity="0" points="4416,-444 5132,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-444 4327,-444 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9329" ObjectName="BS-CX_CB.CX_CB_9IM"/>
    <cge:TPSR_Ref TObjectID="9329"/></metadata>
   <polyline fill="none" opacity="0" points="3582,-444 4327,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-471 4327,-471 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9329" ObjectName="BS-CX_CB.CX_CB_9IM"/>
    <cge:TPSR_Ref TObjectID="9329"/></metadata>
   <polyline fill="none" opacity="0" points="3582,-471 4327,-471 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1114"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1114"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1131"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1131"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" stationName="LF_CB"/>
</svg>