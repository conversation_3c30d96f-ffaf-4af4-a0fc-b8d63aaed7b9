<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-218" aopId="4063750" id="thSvg" product="E8000V2" version="1.0" viewBox="-196 -1064 2180 1122">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.974359" x1="16" x2="92" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="37" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="36" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="52" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="92" y1="22" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <circle cx="15" cy="19" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="10" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="10" x2="15" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="15" cy="41" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="11" x2="15" y1="50" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="45" y2="50"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.1087"/>
    <polyline points="58,100 64,100 " stroke-width="1.1087"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.1087"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="32" y2="97"/>
    <circle cx="24" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="26" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="6" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="8" y2="11"/>
    <circle cx="37" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="23" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="26" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="21" y1="15" y2="15"/>
    <circle cx="50" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="37" cy="24" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="73" x2="70" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="67" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="71" x2="71" y1="30" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="65" x2="77" y1="31" y2="31"/>
    <rect height="27" stroke-width="0.416667" width="14" x="64" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="71" x2="71" y1="78" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="38" y1="78" y2="78"/>
    <rect height="27" stroke-width="0.416667" width="14" x="30" y="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="2" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="0" x2="12" y1="11" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="51" x2="51" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="48" x2="51" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="51" x2="54" y1="15" y2="18"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4a6f550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a701a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4a70a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4a71710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4a72650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4a73270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a739b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4a74470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4a75690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4a75690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a76cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a76cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a78440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a78440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_4a79140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a7ad40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4a7b680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4a7c420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4a7cab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a7e2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a7edf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a7f570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4a7fd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a80e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a81790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a82280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4a82c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4a840e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4a84c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4a85b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4a86590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4a94d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4a87c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_4a888d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_4a898b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1132" width="2190" x="-201" y="-1069"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1975" x2="1984" y1="-394" y2="-394"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-147612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25796" ObjectName="SW-YA_XJC.YA_XJC_3811SW"/>
     <cge:Meas_Ref ObjectId="147612"/>
    <cge:TPSR_Ref TObjectID="25796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147614">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25798" ObjectName="SW-YA_XJC.YA_XJC_3816SW"/>
     <cge:Meas_Ref ObjectId="147614"/>
    <cge:TPSR_Ref TObjectID="25798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -732.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25808" ObjectName="SW-YA_XJC.YA_XJC_3011SW"/>
     <cge:Meas_Ref ObjectId="147676"/>
    <cge:TPSR_Ref TObjectID="25808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147753">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -386.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25814" ObjectName="SW-YA_XJC.YA_XJC_0011SW"/>
     <cge:Meas_Ref ObjectId="147753"/>
    <cge:TPSR_Ref TObjectID="25814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147818">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25843" ObjectName="SW-YA_XJC.YA_XJC_0536SW"/>
     <cge:Meas_Ref ObjectId="147818"/>
    <cge:TPSR_Ref TObjectID="25843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147816">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25841" ObjectName="SW-YA_XJC.YA_XJC_0531SW"/>
     <cge:Meas_Ref ObjectId="147816"/>
    <cge:TPSR_Ref TObjectID="25841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 653.000000 -938.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25797" ObjectName="SW-YA_XJC.YA_XJC_38167SW"/>
     <cge:Meas_Ref ObjectId="147613"/>
    <cge:TPSR_Ref TObjectID="25797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147635">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25802" ObjectName="SW-YA_XJC.YA_XJC_3826SW"/>
     <cge:Meas_Ref ObjectId="147635"/>
    <cge:TPSR_Ref TObjectID="25802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.000000 -938.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25801" ObjectName="SW-YA_XJC.YA_XJC_38267SW"/>
     <cge:Meas_Ref ObjectId="147634"/>
    <cge:TPSR_Ref TObjectID="25801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147633">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25800" ObjectName="SW-YA_XJC.YA_XJC_3821SW"/>
     <cge:Meas_Ref ObjectId="147633"/>
    <cge:TPSR_Ref TObjectID="25800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25806" ObjectName="SW-YA_XJC.YA_XJC_3836SW"/>
     <cge:Meas_Ref ObjectId="147656"/>
    <cge:TPSR_Ref TObjectID="25806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1486.000000 -938.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25805" ObjectName="SW-YA_XJC.YA_XJC_38367SW"/>
     <cge:Meas_Ref ObjectId="147655"/>
    <cge:TPSR_Ref TObjectID="25805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25804" ObjectName="SW-YA_XJC.YA_XJC_3831SW"/>
     <cge:Meas_Ref ObjectId="147654"/>
    <cge:TPSR_Ref TObjectID="25804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25813" ObjectName="SW-YA_XJC.YA_XJC_0016SW"/>
     <cge:Meas_Ref ObjectId="147752"/>
    <cge:TPSR_Ref TObjectID="25813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 -430.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25815" ObjectName="SW-YA_XJC.YA_XJC_00117SW"/>
     <cge:Meas_Ref ObjectId="147754"/>
    <cge:TPSR_Ref TObjectID="25815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147711">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -728.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25810" ObjectName="SW-YA_XJC.YA_XJC_3021SW"/>
     <cge:Meas_Ref ObjectId="147711"/>
    <cge:TPSR_Ref TObjectID="25810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -386.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25818" ObjectName="SW-YA_XJC.YA_XJC_0022SW"/>
     <cge:Meas_Ref ObjectId="147759"/>
    <cge:TPSR_Ref TObjectID="25818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147758">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25817" ObjectName="SW-YA_XJC.YA_XJC_0026SW"/>
     <cge:Meas_Ref ObjectId="147758"/>
    <cge:TPSR_Ref TObjectID="25817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 -430.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25819" ObjectName="SW-YA_XJC.YA_XJC_00227SW"/>
     <cge:Meas_Ref ObjectId="147760"/>
    <cge:TPSR_Ref TObjectID="25819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147745">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -716.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25811" ObjectName="SW-YA_XJC.YA_XJC_3901SW"/>
     <cge:Meas_Ref ObjectId="147745"/>
    <cge:TPSR_Ref TObjectID="25811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147864">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 976.000000 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25855" ObjectName="SW-YA_XJC.YA_XJC_0121SW"/>
     <cge:Meas_Ref ObjectId="147864"/>
    <cge:TPSR_Ref TObjectID="25855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147866">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1110.000000 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25857" ObjectName="SW-YA_XJC.YA_XJC_0122SW"/>
     <cge:Meas_Ref ObjectId="147866"/>
    <cge:TPSR_Ref TObjectID="25857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147865">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1140.000000 -441.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25856" ObjectName="SW-YA_XJC.YA_XJC_01220SW"/>
     <cge:Meas_Ref ObjectId="147865"/>
    <cge:TPSR_Ref TObjectID="25856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147815">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25840" ObjectName="SW-YA_XJC.YA_XJC_0532SW"/>
     <cge:Meas_Ref ObjectId="147815"/>
    <cge:TPSR_Ref TObjectID="25840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147875">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 344.000000 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25860" ObjectName="SW-YA_XJC.YA_XJC_0521SW"/>
     <cge:Meas_Ref ObjectId="147875"/>
    <cge:TPSR_Ref TObjectID="25860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147874">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 344.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25859" ObjectName="SW-YA_XJC.YA_XJC_0522SW"/>
     <cge:Meas_Ref ObjectId="147874"/>
    <cge:TPSR_Ref TObjectID="25859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25833" ObjectName="SW-YA_XJC.YA_XJC_0546SW"/>
     <cge:Meas_Ref ObjectId="147786"/>
    <cge:TPSR_Ref TObjectID="25833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147784">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25831" ObjectName="SW-YA_XJC.YA_XJC_0541SW"/>
     <cge:Meas_Ref ObjectId="147784"/>
    <cge:TPSR_Ref TObjectID="25831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147783">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25830" ObjectName="SW-YA_XJC.YA_XJC_0542SW"/>
     <cge:Meas_Ref ObjectId="147783"/>
    <cge:TPSR_Ref TObjectID="25830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147851">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 804.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25853" ObjectName="SW-YA_XJC.YA_XJC_0556SW"/>
     <cge:Meas_Ref ObjectId="147851"/>
    <cge:TPSR_Ref TObjectID="25853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147849">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 804.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25851" ObjectName="SW-YA_XJC.YA_XJC_0551SW"/>
     <cge:Meas_Ref ObjectId="147849"/>
    <cge:TPSR_Ref TObjectID="25851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147848">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 804.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25850" ObjectName="SW-YA_XJC.YA_XJC_0552SW"/>
     <cge:Meas_Ref ObjectId="147848"/>
    <cge:TPSR_Ref TObjectID="25850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265405">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 929.000000 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25820" ObjectName="SW-YA_XJC.YA_XJC_0901SW"/>
     <cge:Meas_Ref ObjectId="265405"/>
    <cge:TPSR_Ref TObjectID="25820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 993.000000 -306.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25821" ObjectName="SW-YA_XJC.YA_XJC_09017SW"/>
     <cge:Meas_Ref ObjectId="265406"/>
    <cge:TPSR_Ref TObjectID="25821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265409">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1131.000000 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25822" ObjectName="SW-YA_XJC.YA_XJC_0902SW"/>
     <cge:Meas_Ref ObjectId="265409"/>
    <cge:TPSR_Ref TObjectID="25822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1195.000000 -305.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25823" ObjectName="SW-YA_XJC.YA_XJC_09027SW"/>
     <cge:Meas_Ref ObjectId="265408"/>
    <cge:TPSR_Ref TObjectID="25823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25828" ObjectName="SW-YA_XJC.YA_XJC_0616SW"/>
     <cge:Meas_Ref ObjectId="147770"/>
    <cge:TPSR_Ref TObjectID="25828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147767">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25825" ObjectName="SW-YA_XJC.YA_XJC_0612SW"/>
     <cge:Meas_Ref ObjectId="147767"/>
    <cge:TPSR_Ref TObjectID="25825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25826" ObjectName="SW-YA_XJC.YA_XJC_0613SW"/>
     <cge:Meas_Ref ObjectId="147768"/>
    <cge:TPSR_Ref TObjectID="25826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1485.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25848" ObjectName="SW-YA_XJC.YA_XJC_0626SW"/>
     <cge:Meas_Ref ObjectId="147834"/>
    <cge:TPSR_Ref TObjectID="25848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1485.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25845" ObjectName="SW-YA_XJC.YA_XJC_0622SW"/>
     <cge:Meas_Ref ObjectId="147831"/>
    <cge:TPSR_Ref TObjectID="25845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1485.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25846" ObjectName="SW-YA_XJC.YA_XJC_0623SW"/>
     <cge:Meas_Ref ObjectId="147832"/>
    <cge:TPSR_Ref TObjectID="25846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147802">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1658.000000 -109.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25838" ObjectName="SW-YA_XJC.YA_XJC_0636SW"/>
     <cge:Meas_Ref ObjectId="147802"/>
    <cge:TPSR_Ref TObjectID="25838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147800">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1658.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25836" ObjectName="SW-YA_XJC.YA_XJC_0633SW"/>
     <cge:Meas_Ref ObjectId="147800"/>
    <cge:TPSR_Ref TObjectID="25836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182953">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1821.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27842" ObjectName="SW-YA_XJC.YA_XJC_0646SW"/>
     <cge:Meas_Ref ObjectId="182953"/>
    <cge:TPSR_Ref TObjectID="27842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182952">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1821.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27841" ObjectName="SW-YA_XJC.YA_XJC_0642SW"/>
     <cge:Meas_Ref ObjectId="182952"/>
    <cge:TPSR_Ref TObjectID="27841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1821.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27839" ObjectName="SW-YA_XJC.YA_XJC_0643SW"/>
     <cge:Meas_Ref ObjectId="182949"/>
    <cge:TPSR_Ref TObjectID="27839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265496">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 -310.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42945" ObjectName="SW-YA_XJC.YA_XJC_0511SW"/>
     <cge:Meas_Ref ObjectId="265496"/>
    <cge:TPSR_Ref TObjectID="42945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265497">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 -216.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42946" ObjectName="SW-YA_XJC.YA_XJC_0512SW"/>
     <cge:Meas_Ref ObjectId="265497"/>
    <cge:TPSR_Ref TObjectID="42946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265498">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 146.000000 -231.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42947" ObjectName="SW-YA_XJC.YA_XJC_05110SW"/>
     <cge:Meas_Ref ObjectId="265498"/>
    <cge:TPSR_Ref TObjectID="42947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265499">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 146.000000 -94.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42949" ObjectName="SW-YA_XJC.YA_XJC_05120SW"/>
     <cge:Meas_Ref ObjectId="265499"/>
    <cge:TPSR_Ref TObjectID="42949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147785">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 -238.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25832" ObjectName="SW-YA_XJC.YA_XJC_05410SW"/>
     <cge:Meas_Ref ObjectId="147785"/>
    <cge:TPSR_Ref TObjectID="25832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147817">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 460.000000 -238.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25842" ObjectName="SW-YA_XJC.YA_XJC_05310SW"/>
     <cge:Meas_Ref ObjectId="147817"/>
    <cge:TPSR_Ref TObjectID="25842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147876">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 304.000000 -240.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25861" ObjectName="SW-YA_XJC.YA_XJC_05210SW"/>
     <cge:Meas_Ref ObjectId="147876"/>
    <cge:TPSR_Ref TObjectID="25861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147850">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 -240.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25852" ObjectName="SW-YA_XJC.YA_XJC_05510SW"/>
     <cge:Meas_Ref ObjectId="147850"/>
    <cge:TPSR_Ref TObjectID="25852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147799">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1658.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25835" ObjectName="SW-YA_XJC.YA_XJC_0632SW"/>
     <cge:Meas_Ref ObjectId="147799"/>
    <cge:TPSR_Ref TObjectID="25835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147801">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1620.000000 -239.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25837" ObjectName="SW-YA_XJC.YA_XJC_06320SW"/>
     <cge:Meas_Ref ObjectId="147801"/>
    <cge:TPSR_Ref TObjectID="25837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1447.000000 -239.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25847" ObjectName="SW-YA_XJC.YA_XJC_06220SW"/>
     <cge:Meas_Ref ObjectId="147833"/>
    <cge:TPSR_Ref TObjectID="25847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182951">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1783.000000 -238.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27840" ObjectName="SW-YA_XJC.YA_XJC_06420SW"/>
     <cge:Meas_Ref ObjectId="182951"/>
    <cge:TPSR_Ref TObjectID="27840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1279.000000 -238.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25827" ObjectName="SW-YA_XJC.YA_XJC_06120SW"/>
     <cge:Meas_Ref ObjectId="147769"/>
    <cge:TPSR_Ref TObjectID="25827"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-779 1792,-779 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25791" ObjectName="BS-YA_XJC.YA_XJC_3M"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   <polyline fill="none" opacity="0" points="352,-779 1792,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="110,-370 1024,-370 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25792" ObjectName="BS-YA_XJC.YA_XJC_9IM"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   <polyline fill="none" opacity="0" points="110,-370 1024,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-370 1935,-370 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25793" ObjectName="BS-YA_XJC.YA_XJC_9IIM"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   <polyline fill="none" opacity="0" points="1083,-370 1935,-370 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YA_XJC.YA_XJC_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 301.000000 -68.000000)" xlink:href="#capacitor:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41921" ObjectName="CB-YA_XJC.YA_XJC_Cb1"/>
    <cge:TPSR_Ref TObjectID="41921"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YA_XJC.YA_XJC_Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36536"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 -637.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 -637.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25864" ObjectName="TF-YA_XJC.YA_XJC_Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_XJC.YA_XJC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36528"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 670.000000 -599.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 670.000000 -599.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25862" ObjectName="TF-YA_XJC.YA_XJC_1T"/>
    <cge:TPSR_Ref TObjectID="25862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_XJC.YA_XJC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36532"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1366.000000 -599.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1366.000000 -599.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25863" ObjectName="TF-YA_XJC.YA_XJC_2T"/>
    <cge:TPSR_Ref TObjectID="25863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 -0.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 -0.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3d39ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-779 636,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25796@0" Pin0InfoVect0LinkObjId="SW-147612_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_411e4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-779 636,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_405f1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-829 636,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25796@1" ObjectIDZND0="25795@0" Pin0InfoVect0LinkObjId="SW-147610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-829 636,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4141650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-872 636,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25795@1" ObjectIDZND0="25798@0" Pin0InfoVect0LinkObjId="SW-147614_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-872 636,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4149010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="551,-100 507,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_42ec080@0" ObjectIDZND0="34046@x" ObjectIDZND1="25843@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.482Ld_0" Pin0InfoVect1LinkObjId="SW-147818_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42ec080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="551,-100 507,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42f1660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-66 507,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34046@0" ObjectIDZND0="g_42ec080@0" ObjectIDZND1="25843@x" Pin0InfoVect0LinkObjId="g_42ec080_0" Pin0InfoVect1LinkObjId="SW-147818_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.482Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="507,-66 507,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c28a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-100 507,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_42ec080@0" ObjectIDND1="34046@x" ObjectIDZND0="25843@0" Pin0InfoVect0LinkObjId="SW-147818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_42ec080_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.482Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-100 507,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d907a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-945 636,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25797@0" ObjectIDZND0="25798@x" ObjectIDZND1="g_4396520@0" ObjectIDZND2="g_4141270@0" Pin0InfoVect0LinkObjId="SW-147614_0" Pin0InfoVect1LinkObjId="g_4396520_0" Pin0InfoVect2LinkObjId="g_4141270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="658,-945 636,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20aac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-928 636,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25798@1" ObjectIDZND0="25797@x" ObjectIDZND1="g_4396520@0" ObjectIDZND2="g_4141270@0" Pin0InfoVect0LinkObjId="SW-147613_0" Pin0InfoVect1LinkObjId="g_4396520_0" Pin0InfoVect2LinkObjId="g_4141270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147614_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="636,-928 636,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4406bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-945 636,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDZND0="g_4396520@0" ObjectIDZND1="g_4141270@0" ObjectIDZND2="38083@1" Pin0InfoVect0LinkObjId="g_4396520_0" Pin0InfoVect1LinkObjId="g_4141270_0" Pin0InfoVect2LinkObjId="g_434a340_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="636,-945 636,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_434a340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-992 636,-1011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDND2="g_4396520@0" ObjectIDZND0="38083@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="g_4396520_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-992 636,-1011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43f08b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-992 636,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_4396520@0" ObjectIDZND0="25797@x" ObjectIDZND1="25798@x" ObjectIDZND2="g_4141270@0" Pin0InfoVect0LinkObjId="SW-147613_0" Pin0InfoVect1LinkObjId="SW-147614_0" Pin0InfoVect2LinkObjId="g_4141270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4396520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="593,-992 636,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4344190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-872 1052,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25799@1" ObjectIDZND0="25802@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147631_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-872 1052,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4327ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,-945 1052,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25801@0" ObjectIDZND0="25802@x" ObjectIDZND1="g_42eae60@0" ObjectIDZND2="g_4382af0@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="g_42eae60_0" Pin0InfoVect2LinkObjId="g_4382af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147634_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-945 1052,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4470340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-928 1052,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25802@1" ObjectIDZND0="25801@x" ObjectIDZND1="g_42eae60@0" ObjectIDZND2="g_4382af0@0" Pin0InfoVect0LinkObjId="SW-147634_0" Pin0InfoVect1LinkObjId="g_42eae60_0" Pin0InfoVect2LinkObjId="g_4382af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147635_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-928 1052,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_419fb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-945 1052,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25802@x" ObjectIDND1="25801@x" ObjectIDZND0="g_42eae60@0" ObjectIDZND1="g_4382af0@0" ObjectIDZND2="34316@1" Pin0InfoVect0LinkObjId="g_42eae60_0" Pin0InfoVect1LinkObjId="g_4382af0_0" Pin0InfoVect2LinkObjId="g_4307e10_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147635_0" Pin1InfoVect1LinkObjId="SW-147634_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-945 1052,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4307e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-992 1052,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="25802@x" ObjectIDND1="25801@x" ObjectIDND2="g_42eae60@0" ObjectIDZND0="34316@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147635_0" Pin1InfoVect1LinkObjId="SW-147634_0" Pin1InfoVect2LinkObjId="g_42eae60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-992 1052,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41ca080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1009,-992 1052,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_42eae60@0" ObjectIDZND0="25802@x" ObjectIDZND1="25801@x" ObjectIDZND2="g_4382af0@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="SW-147634_0" Pin0InfoVect2LinkObjId="g_4382af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42eae60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1009,-992 1052,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce0230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-779 1052,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25800@0" Pin0InfoVect0LinkObjId="SW-147633_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_411e4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-779 1052,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e9eb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-845 1052,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25799@0" ObjectIDZND0="25800@1" Pin0InfoVect0LinkObjId="SW-147633_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-845 1052,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eca630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-992 1052,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_4382af0@0" ObjectIDZND0="25802@x" ObjectIDZND1="25801@x" ObjectIDZND2="g_42eae60@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="SW-147634_0" Pin0InfoVect2LinkObjId="g_42eae60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4382af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-992 1052,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4093f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-992 660,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDND2="g_4396520@0" ObjectIDZND0="g_4141270@0" Pin0InfoVect0LinkObjId="g_4141270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="g_4396520_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-992 660,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_418d300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-872 1469,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25803@1" ObjectIDZND0="25806@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-872 1469,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d3970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1491,-945 1469,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25805@0" ObjectIDZND0="25806@x" ObjectIDZND1="g_3e0d500@0" ObjectIDZND2="g_407f3b0@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="g_3e0d500_0" Pin0InfoVect2LinkObjId="g_407f3b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1491,-945 1469,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5d300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-928 1469,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25806@1" ObjectIDZND0="25805@x" ObjectIDZND1="g_3e0d500@0" ObjectIDZND2="g_407f3b0@0" Pin0InfoVect0LinkObjId="SW-147655_0" Pin0InfoVect1LinkObjId="g_3e0d500_0" Pin0InfoVect2LinkObjId="g_407f3b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-928 1469,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_372ceb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-945 1469,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25806@x" ObjectIDND1="25805@x" ObjectIDZND0="g_3e0d500@0" ObjectIDZND1="g_407f3b0@0" ObjectIDZND2="37772@1" Pin0InfoVect0LinkObjId="g_3e0d500_0" Pin0InfoVect1LinkObjId="g_407f3b0_0" Pin0InfoVect2LinkObjId="g_3220300_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147656_0" Pin1InfoVect1LinkObjId="SW-147655_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-945 1469,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3220300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-992 1469,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="25806@x" ObjectIDND1="25805@x" ObjectIDND2="g_3e0d500@0" ObjectIDZND0="37772@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147656_0" Pin1InfoVect1LinkObjId="SW-147655_0" Pin1InfoVect2LinkObjId="g_3e0d500_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-992 1469,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41bcfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-992 1469,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3e0d500@0" ObjectIDZND0="25806@x" ObjectIDZND1="25805@x" ObjectIDZND2="g_407f3b0@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="SW-147655_0" Pin0InfoVect2LinkObjId="g_407f3b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e0d500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-992 1469,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d1fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-779 1469,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25804@0" Pin0InfoVect0LinkObjId="SW-147654_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_411e4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-779 1469,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42ee2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-845 1469,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25803@0" ObjectIDZND0="25804@1" Pin0InfoVect0LinkObjId="SW-147654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147652_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-845 1469,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4074800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1493,-992 1469,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_407f3b0@0" ObjectIDZND0="25806@x" ObjectIDZND1="25805@x" ObjectIDZND2="g_3e0d500@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="SW-147655_0" Pin0InfoVect2LinkObjId="g_3e0d500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407f3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1493,-992 1469,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4119930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-779 705,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25808@1" Pin0InfoVect0LinkObjId="SW-147676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_411e4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-779 705,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_437b4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-737 705,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25808@0" ObjectIDZND0="25807@1" Pin0InfoVect0LinkObjId="SW-147673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-737 705,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42f9400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-698 705,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25807@0" ObjectIDZND0="25862@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-698 705,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d41e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-538 705,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_4318ba0@1" ObjectIDZND0="25813@1" Pin0InfoVect0LinkObjId="SW-147752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4318ba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-538 705,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40fe6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-437 775,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25814@x" ObjectIDND1="25812@x" ObjectIDZND0="25815@0" Pin0InfoVect0LinkObjId="SW-147754_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147753_0" Pin1InfoVect1LinkObjId="SW-147749_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-437 775,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e5650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-447 705,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25812@0" ObjectIDZND0="25815@x" ObjectIDZND1="25814@x" Pin0InfoVect0LinkObjId="SW-147754_0" Pin0InfoVect1LinkObjId="SW-147753_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="705,-447 705,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e2830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-437 705,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25815@x" ObjectIDND1="25812@x" ObjectIDZND0="25814@1" Pin0InfoVect0LinkObjId="SW-147753_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147754_0" Pin1InfoVect1LinkObjId="SW-147749_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-437 705,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41838f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-779 1401,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25810@1" Pin0InfoVect0LinkObjId="SW-147711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_411e4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-779 1401,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41be560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-733 1401,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25810@0" ObjectIDZND0="25809@1" Pin0InfoVect0LinkObjId="SW-147708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-733 1401,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37bef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-694 1401,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25809@0" ObjectIDZND0="25863@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-694 1401,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40836f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-603 1401,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="25863@0" ObjectIDZND0="g_43fdcd0@0" Pin0InfoVect0LinkObjId="g_43fdcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37bef40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-603 1401,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_406eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-437 1471,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="25816@x" ObjectIDND1="25818@x" ObjectIDZND0="25819@0" Pin0InfoVect0LinkObjId="SW-147760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147755_0" Pin1InfoVect1LinkObjId="SW-147759_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-437 1471,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d6a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-447 1401,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25816@0" ObjectIDZND0="25819@x" ObjectIDZND1="25818@x" Pin0InfoVect0LinkObjId="SW-147760_0" Pin0InfoVect1LinkObjId="SW-147759_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-447 1401,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3411700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-437 1401,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="25816@x" ObjectIDND1="25819@x" ObjectIDZND0="25818@1" Pin0InfoVect0LinkObjId="SW-147759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147755_0" Pin1InfoVect1LinkObjId="SW-147760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-437 1401,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_414a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-538 1401,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_43fdcd0@1" ObjectIDZND0="25817@1" Pin0InfoVect0LinkObjId="SW-147758_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43fdcd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-538 1401,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_444a210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-779 848,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="25791@0" ObjectIDZND0="g_4352390@1" Pin0InfoVect0LinkObjId="g_4352390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_411e4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="848,-779 848,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4439790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-710 848,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_4352390@0" ObjectIDZND0="25864@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4352390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="848,-710 848,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4443900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-710 1099,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25811@x" ObjectIDND1="g_444d690@0" ObjectIDZND0="g_433d120@0" Pin0InfoVect0LinkObjId="g_433d120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147745_0" Pin1InfoVect1LinkObjId="g_444d690_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-710 1099,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4469740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-721 1052,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25811@0" ObjectIDZND0="g_433d120@0" ObjectIDZND1="g_444d690@0" Pin0InfoVect0LinkObjId="g_433d120_0" Pin0InfoVect1LinkObjId="g_444d690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-721 1052,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4225a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="372,-135 353,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="g_4454570@0" ObjectIDZND0="41921@x" ObjectIDZND1="g_41f4fc0@0" Pin0InfoVect0LinkObjId="CB-YA_XJC.YA_XJC_Cb1_0" Pin0InfoVect1LinkObjId="g_41f4fc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4454570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="372,-135 353,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42073a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-162 353,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="g_41f4fc0@1" ObjectIDZND0="g_4454570@0" ObjectIDZND1="41921@x" Pin0InfoVect0LinkObjId="g_4454570_0" Pin0InfoVect1LinkObjId="CB-YA_XJC.YA_XJC_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f4fc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="353,-162 353,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41975c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-135 353,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_4454570@0" ObjectIDND1="g_41f4fc0@0" ObjectIDZND0="41921@0" Pin0InfoVect0LinkObjId="CB-YA_XJC.YA_XJC_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4454570_0" Pin1InfoVect1LinkObjId="g_41f4fc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="353,-135 353,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fdef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="701,-100 657,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_41e98b0@0" ObjectIDZND0="34044@x" ObjectIDZND1="25833@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.485Ld_0" Pin0InfoVect1LinkObjId="SW-147786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41e98b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="701,-100 657,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41995f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-66 657,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34044@0" ObjectIDZND0="g_41e98b0@0" ObjectIDZND1="25833@x" Pin0InfoVect0LinkObjId="g_41e98b0_0" Pin0InfoVect1LinkObjId="SW-147786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.485Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="657,-66 657,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_419e480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-100 657,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34044@x" ObjectIDND1="g_41e98b0@0" ObjectIDZND0="25833@0" Pin0InfoVect0LinkObjId="SW-147786_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.485Ld_0" Pin1InfoVect1LinkObjId="g_41e98b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="657,-100 657,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_412ffc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="857,-100 813,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_20b6070@0" ObjectIDZND0="34042@x" ObjectIDZND1="25853@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.486Ld_0" Pin0InfoVect1LinkObjId="SW-147851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b6070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="857,-100 813,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_431d8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-66 813,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34042@0" ObjectIDZND0="g_20b6070@0" ObjectIDZND1="25853@x" Pin0InfoVect0LinkObjId="g_20b6070_0" Pin0InfoVect1LinkObjId="SW-147851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.486Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="813,-66 813,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42ff790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-100 813,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34042@x" ObjectIDND1="g_20b6070@0" ObjectIDZND0="25853@0" Pin0InfoVect0LinkObjId="SW-147851_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.486Ld_0" Pin1InfoVect1LinkObjId="g_20b6070_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="813,-100 813,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4344e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="998,-313 938,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25821@0" ObjectIDZND0="25820@x" ObjectIDZND1="g_20b2260@0" Pin0InfoVect0LinkObjId="SW-265405_0" Pin0InfoVect1LinkObjId="g_20b2260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="998,-313 938,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_431e750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="938,-321 938,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25820@0" ObjectIDZND0="25821@x" ObjectIDZND1="g_20b2260@0" Pin0InfoVect0LinkObjId="SW-265406_0" Pin0InfoVect1LinkObjId="g_20b2260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="938,-321 938,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_421fd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="938,-313 938,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="25821@x" ObjectIDND1="25820@x" ObjectIDZND0="g_20b2260@0" Pin0InfoVect0LinkObjId="g_20b2260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265406_0" Pin1InfoVect1LinkObjId="SW-265405_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="938,-313 938,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4162fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-370 1140,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25793@0" ObjectIDZND0="25822@1" Pin0InfoVect0LinkObjId="SW-265409_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41c3a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-370 1140,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_209c430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-312 1140,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25823@0" ObjectIDZND0="25822@x" ObjectIDZND1="g_37f6ca0@0" Pin0InfoVect0LinkObjId="SW-265409_0" Pin0InfoVect1LinkObjId="g_37f6ca0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-312 1140,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2080f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-320 1140,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25822@0" ObjectIDZND0="25823@x" ObjectIDZND1="g_37f6ca0@0" Pin0InfoVect0LinkObjId="SW-265408_0" Pin0InfoVect1LinkObjId="g_37f6ca0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-320 1140,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20811d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-312 1140,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="25822@x" ObjectIDND1="25823@x" ObjectIDZND0="g_37f6ca0@0" Pin0InfoVect0LinkObjId="g_37f6ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265409_0" Pin1InfoVect1LinkObjId="SW-265408_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-312 1140,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4422db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-100 1326,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3751460@0" ObjectIDZND0="34043@x" ObjectIDZND1="25828@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.483Ld_0" Pin0InfoVect1LinkObjId="SW-147770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3751460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-100 1326,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43f0250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-66 1326,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34043@0" ObjectIDZND0="g_3751460@0" ObjectIDZND1="25828@x" Pin0InfoVect0LinkObjId="g_3751460_0" Pin0InfoVect1LinkObjId="SW-147770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.483Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-66 1326,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_434a8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-100 1326,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34043@x" ObjectIDND1="g_3751460@0" ObjectIDZND0="25828@0" Pin0InfoVect0LinkObjId="SW-147770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.483Ld_0" Pin1InfoVect1LinkObjId="g_3751460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-100 1326,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_440bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-322 1326,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25825@0" ObjectIDZND0="25824@x" ObjectIDZND1="25827@x" Pin0InfoVect0LinkObjId="SW-147765_0" Pin0InfoVect1LinkObjId="SW-147769_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-322 1326,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4408120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-314 1326,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25825@x" ObjectIDND1="25827@x" ObjectIDZND0="25824@1" Pin0InfoVect0LinkObjId="SW-147765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147767_0" Pin1InfoVect1LinkObjId="SW-147769_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-314 1326,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e0b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1538,-100 1494,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_4148d10@0" ObjectIDZND0="34047@x" ObjectIDZND1="25848@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.484Ld_0" Pin0InfoVect1LinkObjId="SW-147834_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4148d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1538,-100 1494,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4162420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-66 1494,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34047@0" ObjectIDZND0="g_4148d10@0" ObjectIDZND1="25848@x" Pin0InfoVect0LinkObjId="g_4148d10_0" Pin0InfoVect1LinkObjId="SW-147834_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.484Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-66 1494,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4059450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-100 1494,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34047@x" ObjectIDND1="g_4148d10@0" ObjectIDZND0="25848@0" Pin0InfoVect0LinkObjId="SW-147834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.484Ld_0" Pin1InfoVect1LinkObjId="g_4148d10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-100 1494,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379b980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1711,-101 1667,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1cb74b0@0" ObjectIDZND0="34045@x" ObjectIDZND1="25838@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.487Ld_0" Pin0InfoVect1LinkObjId="SW-147802_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cb74b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1711,-101 1667,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41811c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-67 1667,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34045@0" ObjectIDZND0="g_1cb74b0@0" ObjectIDZND1="25838@x" Pin0InfoVect0LinkObjId="g_1cb74b0_0" Pin0InfoVect1LinkObjId="SW-147802_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.487Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-67 1667,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3390d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-101 1667,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1cb74b0@0" ObjectIDND1="34045@x" ObjectIDZND0="25838@0" Pin0InfoVect0LinkObjId="SW-147802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cb74b0_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.487Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-101 1667,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2129f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1603,-47 1603,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1603,-47 1603,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4235bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1874,-100 1830,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_410f040@0" ObjectIDZND0="34048@x" ObjectIDZND1="27842@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.488Ld_0" Pin0InfoVect1LinkObjId="SW-182953_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_410f040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1874,-100 1830,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4242ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-66 1830,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34048@0" ObjectIDZND0="g_410f040@0" ObjectIDZND1="27842@x" Pin0InfoVect0LinkObjId="g_410f040_0" Pin0InfoVect1LinkObjId="SW-182953_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.488Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-66 1830,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4220310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-100 1830,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34048@x" ObjectIDND1="g_410f040@0" ObjectIDZND0="27842@0" Pin0InfoVect0LinkObjId="SW-182953_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.488Ld_0" Pin1InfoVect1LinkObjId="g_410f040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-100 1830,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41c8760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-710 1052,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25811@x" ObjectIDND1="g_433d120@0" ObjectIDZND0="g_444d690@0" Pin0InfoVect0LinkObjId="g_444d690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147745_0" Pin1InfoVect1LinkObjId="g_433d120_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-710 1052,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_436a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-659 1052,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_444d690@1" ObjectIDZND0="g_43373b0@0" Pin0InfoVect0LinkObjId="g_43373b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_444d690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-659 1052,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32335e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-170 155,-170 155,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="42946@x" ObjectIDND1="g_430fbd0@0" ObjectIDZND0="42949@0" Pin0InfoVect0LinkObjId="SW-265499_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265497_0" Pin1InfoVect1LinkObjId="g_430fbd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="193,-170 155,-170 155,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_434acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-170 193,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="42949@x" ObjectIDND1="42946@x" ObjectIDND2="g_430fbd0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-265499_0" Pin1InfoVect1LinkObjId="SW-265497_0" Pin1InfoVect2LinkObjId="g_430fbd0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="193,-170 193,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4342610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-307 155,-307 155,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42945@x" ObjectIDND1="42944@x" ObjectIDZND0="42947@0" Pin0InfoVect0LinkObjId="SW-265498_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265496_0" Pin1InfoVect1LinkObjId="SW-265494_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="193,-307 155,-307 155,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4224ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-315 193,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="42945@0" ObjectIDZND0="42944@x" ObjectIDZND1="42947@x" Pin0InfoVect0LinkObjId="SW-265494_0" Pin0InfoVect1LinkObjId="SW-265498_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265496_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="193,-315 193,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4228340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-307 193,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="42945@x" ObjectIDND1="42947@x" ObjectIDZND0="42944@1" Pin0InfoVect0LinkObjId="SW-265494_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265496_0" Pin1InfoVect1LinkObjId="SW-265498_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="193,-307 193,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4213ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-272 193,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42944@0" ObjectIDZND0="42946@1" Pin0InfoVect0LinkObjId="SW-265497_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265494_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="193,-272 193,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34fc920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-228 353,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25859@0" ObjectIDZND0="g_41f4fc0@0" Pin0InfoVect0LinkObjId="g_41f4fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147874_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="353,-228 353,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41db110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="656,-314 618,-314 618,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25831@x" ObjectIDND1="25829@x" ObjectIDZND0="25832@0" Pin0InfoVect0LinkObjId="SW-147785_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147784_0" Pin1InfoVect1LinkObjId="SW-147781_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="656,-314 618,-314 618,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401a0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-314 469,-314 469,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25841@x" ObjectIDND1="25839@x" ObjectIDZND0="25842@0" Pin0InfoVect0LinkObjId="SW-147817_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147816_0" Pin1InfoVect1LinkObjId="SW-147813_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-314 469,-314 469,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41146b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-316 313,-316 313,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25860@x" ObjectIDND1="25858@x" ObjectIDZND0="25861@0" Pin0InfoVect0LinkObjId="SW-147876_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147875_0" Pin1InfoVect1LinkObjId="SW-147872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="352,-316 313,-316 313,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4174a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-323 353,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25860@0" ObjectIDZND0="25858@x" ObjectIDZND1="25861@x" Pin0InfoVect0LinkObjId="SW-147872_0" Pin0InfoVect1LinkObjId="SW-147876_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="353,-323 353,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a8fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-316 353,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25860@x" ObjectIDND1="25861@x" ObjectIDZND0="25858@1" Pin0InfoVect0LinkObjId="SW-147872_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147875_0" Pin1InfoVect1LinkObjId="SW-147876_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="353,-316 353,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3af5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-322 507,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25841@0" ObjectIDZND0="25842@x" ObjectIDZND1="25839@x" Pin0InfoVect0LinkObjId="SW-147817_0" Pin0InfoVect1LinkObjId="SW-147813_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="507,-322 507,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c2dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-314 507,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25842@x" ObjectIDND1="25841@x" ObjectIDZND0="25839@1" Pin0InfoVect0LinkObjId="SW-147813_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147817_0" Pin1InfoVect1LinkObjId="SW-147816_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-314 507,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4237370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-322 657,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25831@0" ObjectIDZND0="25832@x" ObjectIDZND1="25829@x" Pin0InfoVect0LinkObjId="SW-147785_0" Pin0InfoVect1LinkObjId="SW-147781_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="657,-322 657,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4233390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-314 657,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25832@x" ObjectIDND1="25831@x" ObjectIDZND0="25829@1" Pin0InfoVect0LinkObjId="SW-147781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147785_0" Pin1InfoVect1LinkObjId="SW-147784_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="657,-314 657,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4234e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="812,-316 774,-316 774,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25851@x" ObjectIDND1="25849@x" ObjectIDZND0="25852@0" Pin0InfoVect0LinkObjId="SW-147850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147849_0" Pin1InfoVect1LinkObjId="SW-147846_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="812,-316 774,-316 774,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d9ad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-322 813,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25851@0" ObjectIDZND0="25852@x" ObjectIDZND1="25849@x" Pin0InfoVect0LinkObjId="SW-147850_0" Pin0InfoVect1LinkObjId="SW-147846_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147849_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="813,-322 813,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4154500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-316 813,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25852@x" ObjectIDND1="25851@x" ObjectIDZND0="25849@1" Pin0InfoVect0LinkObjId="SW-147846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147850_0" Pin1InfoVect1LinkObjId="SW-147849_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="813,-316 813,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b5780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="938,-357 938,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25820@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_4402360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="938,-357 938,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4402360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-391 705,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25814@0" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_32b5780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-391 705,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43fdac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-279 1830,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27838@0" ObjectIDZND0="27839@1" Pin0InfoVect0LinkObjId="SW-182949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-279 1830,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_210a9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-160 757,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4339eb0@1" ObjectIDZND0="g_3236b90@0" Pin0InfoVect0LinkObjId="g_3236b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4339eb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-160 757,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41c54f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-101 757,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3236b90@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3236b90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-101 757,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41c3a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-391 1401,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25818@0" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_42273a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-391 1401,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42273a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-358 1326,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25825@1" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_41c3a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147767_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-358 1326,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a76e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-358 1667,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25835@1" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_41c3a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-358 1667,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ecba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-358 1830,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27841@1" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_41c3a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182952_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-358 1830,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ed820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-315 1629,-315 1629,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25835@x" ObjectIDND1="25834@x" ObjectIDZND0="25837@0" Pin0InfoVect0LinkObjId="SW-147801_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147799_0" Pin1InfoVect1LinkObjId="SW-147797_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-315 1629,-315 1629,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33a2cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-315 1456,-315 1456,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25845@x" ObjectIDND1="25844@x" ObjectIDZND0="25847@0" Pin0InfoVect0LinkObjId="SW-147833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147831_0" Pin1InfoVect1LinkObjId="SW-147829_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-315 1456,-315 1456,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41172b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-314 1792,-314 1792,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="27841@x" ObjectIDND1="27838@x" ObjectIDZND0="27840@0" Pin0InfoVect0LinkObjId="SW-182951_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-182952_0" Pin1InfoVect1LinkObjId="SW-182947_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-314 1792,-314 1792,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_414ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-314 1288,-314 1288,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25825@x" ObjectIDND1="25824@x" ObjectIDZND0="25827@0" Pin0InfoVect0LinkObjId="SW-147769_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147767_0" Pin1InfoVect1LinkObjId="SW-147765_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-314 1288,-314 1288,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4170b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-322 1494,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25845@0" ObjectIDZND0="25847@x" ObjectIDZND1="25844@x" Pin0InfoVect0LinkObjId="SW-147833_0" Pin0InfoVect1LinkObjId="SW-147829_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-322 1494,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41017b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-315 1494,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25847@x" ObjectIDND1="25845@x" ObjectIDZND0="25844@1" Pin0InfoVect0LinkObjId="SW-147829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147833_0" Pin1InfoVect1LinkObjId="SW-147831_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-315 1494,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3687a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-322 1667,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25835@0" ObjectIDZND0="25837@x" ObjectIDZND1="25834@x" Pin0InfoVect0LinkObjId="SW-147801_0" Pin0InfoVect1LinkObjId="SW-147797_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147799_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-322 1667,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_368d100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-315 1667,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25837@x" ObjectIDND1="25835@x" ObjectIDZND0="25834@1" Pin0InfoVect0LinkObjId="SW-147797_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147801_0" Pin1InfoVect1LinkObjId="SW-147799_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-315 1667,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a7ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-322 1830,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27841@0" ObjectIDZND0="27840@x" ObjectIDZND1="27838@x" Pin0InfoVect0LinkObjId="SW-182951_0" Pin0InfoVect1LinkObjId="SW-182947_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182952_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-322 1830,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32e8340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-314 1830,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27840@x" ObjectIDND1="27841@x" ObjectIDZND0="27838@1" Pin0InfoVect0LinkObjId="SW-182947_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-182951_0" Pin1InfoVect1LinkObjId="SW-182952_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-314 1830,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e8690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-358 1494,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25845@1" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_41c3a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147831_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-358 1494,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e0dc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-351 193,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42945@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_32b5780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265496_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="193,-351 193,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4429360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-359 353,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25860@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_32b5780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147875_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="353,-359 353,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4236f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-358 507,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25841@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_32b5780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-358 507,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_421da80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-358 657,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25831@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_32b5780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147784_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="657,-358 657,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41f0370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-358 813,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25851@1" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_32b5780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147849_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="813,-358 813,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4085a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-205 757,-214 813,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4339eb0@0" ObjectIDZND0="25850@x" ObjectIDZND1="25853@x" Pin0InfoVect0LinkObjId="SW-147848_0" Pin0InfoVect1LinkObjId="SW-147851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4339eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="757,-205 757,-214 813,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4209870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-228 813,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25850@0" ObjectIDZND0="g_4339eb0@0" ObjectIDZND1="25853@x" Pin0InfoVect0LinkObjId="g_4339eb0_0" Pin0InfoVect1LinkObjId="SW-147851_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147848_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="813,-228 813,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4209fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-214 813,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_4339eb0@0" ObjectIDND1="25850@x" ObjectIDZND0="25853@1" Pin0InfoVect0LinkObjId="SW-147851_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4339eb0_0" Pin1InfoVect1LinkObjId="SW-147848_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="813,-214 813,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="985,-394 985,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25855@0" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_32b5780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="985,-394 985,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffbc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-394 1119,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25857@0" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_41c3a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-394 1119,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_411e4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-757 1052,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25811@1" ObjectIDZND0="25791@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-757 1052,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_413ea90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="985,-430 985,-457 1039,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25855@1" ObjectIDZND0="25854@1" Pin0InfoVect0LinkObjId="SW-147862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147864_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="985,-430 985,-457 1039,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4427950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1145,-448 1119,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25856@0" ObjectIDZND0="25854@x" ObjectIDZND1="25857@x" Pin0InfoVect0LinkObjId="SW-147862_0" Pin0InfoVect1LinkObjId="SW-147866_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1145,-448 1119,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20b4490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1066,-457 1119,-457 1119,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25854@0" ObjectIDZND0="25856@x" ObjectIDZND1="25857@x" Pin0InfoVect0LinkObjId="SW-147865_0" Pin0InfoVect1LinkObjId="SW-147866_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1066,-457 1119,-457 1119,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_207f7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-448 1119,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25856@x" ObjectIDND1="25854@x" ObjectIDZND0="25857@1" Pin0InfoVect0LinkObjId="SW-147866_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147865_0" Pin1InfoVect1LinkObjId="SW-147862_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-448 1119,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e9ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-597 744,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25862@x" ObjectIDND1="g_4318ba0@0" ObjectIDZND0="g_420f0e0@0" Pin0InfoVect0LinkObjId="g_420f0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_42f9400_0" Pin1InfoVect1LinkObjId="g_4318ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-597 744,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20c5c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-603 705,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25862@0" ObjectIDZND0="g_4318ba0@0" ObjectIDZND1="g_420f0e0@0" Pin0InfoVect0LinkObjId="g_4318ba0_0" Pin0InfoVect1LinkObjId="g_420f0e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42f9400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="705,-603 705,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4440190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-597 705,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25862@x" ObjectIDND1="g_420f0e0@0" ObjectIDZND0="g_4318ba0@0" Pin0InfoVect0LinkObjId="g_4318ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_42f9400_0" Pin1InfoVect1LinkObjId="g_420f0e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-597 705,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_432af20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-481 744,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="25813@x" ObjectIDND1="25812@x" ObjectIDZND0="g_40f1a90@0" Pin0InfoVect0LinkObjId="g_40f1a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147752_0" Pin1InfoVect1LinkObjId="SW-147749_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-481 744,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4306570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-488 705,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="25813@0" ObjectIDZND0="g_40f1a90@0" ObjectIDZND1="25812@x" Pin0InfoVect0LinkObjId="g_40f1a90_0" Pin0InfoVect1LinkObjId="SW-147749_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="705,-488 705,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_434c020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-481 705,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_40f1a90@0" ObjectIDND1="25813@x" ObjectIDZND0="25812@1" Pin0InfoVect0LinkObjId="SW-147749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40f1a90_0" Pin1InfoVect1LinkObjId="SW-147752_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-481 705,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4243750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-482 1440,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="25817@x" ObjectIDND1="25816@x" ObjectIDZND0="g_43067d0@0" Pin0InfoVect0LinkObjId="g_43067d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147758_0" Pin1InfoVect1LinkObjId="SW-147755_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-482 1440,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_430f970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-488 1401,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="25817@0" ObjectIDZND0="g_43067d0@0" ObjectIDZND1="25816@x" Pin0InfoVect0LinkObjId="g_43067d0_0" Pin0InfoVect1LinkObjId="SW-147755_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147758_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-488 1401,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42137b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-482 1401,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_43067d0@0" ObjectIDND1="25817@x" ObjectIDZND0="25816@1" Pin0InfoVect0LinkObjId="SW-147755_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_43067d0_0" Pin1InfoVect1LinkObjId="SW-147758_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-482 1401,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42fb2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="192,-194 222,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="42946@x" ObjectIDND1="42949@x" ObjectIDZND0="g_430fbd0@0" Pin0InfoVect0LinkObjId="g_430fbd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265497_0" Pin1InfoVect1LinkObjId="SW-265499_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="192,-194 222,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4327880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-221 193,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="42946@0" ObjectIDZND0="42949@x" ObjectIDZND1="g_430fbd0@0" Pin0InfoVect0LinkObjId="SW-265499_0" Pin0InfoVect1LinkObjId="g_430fbd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-265497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="193,-221 193,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4346630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-194 193,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="42946@x" ObjectIDND1="g_430fbd0@0" ObjectIDZND0="42949@x" Pin0InfoVect0LinkObjId="SW-265499_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-265497_0" Pin1InfoVect1LinkObjId="g_430fbd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="193,-194 193,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4117a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-271 373,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="25858@x" ObjectIDND1="25859@x" ObjectIDZND0="g_4346890@0" Pin0InfoVect0LinkObjId="g_4346890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147872_0" Pin1InfoVect1LinkObjId="SW-147874_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="353,-271 373,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_411c260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-279 353,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25858@0" ObjectIDZND0="25859@x" ObjectIDZND1="g_4346890@0" Pin0InfoVect0LinkObjId="SW-147874_0" Pin0InfoVect1LinkObjId="g_4346890_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="353,-279 353,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_411c4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-271 353,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="25858@x" ObjectIDND1="g_4346890@0" ObjectIDZND0="25859@1" Pin0InfoVect0LinkObjId="SW-147874_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147872_0" Pin1InfoVect1LinkObjId="g_4346890_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="353,-271 353,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41466a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-272 531,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="25839@x" ObjectIDND1="25840@x" ObjectIDZND0="g_40e63d0@0" Pin0InfoVect0LinkObjId="g_40e63d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147813_0" Pin1InfoVect1LinkObjId="SW-147815_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-272 531,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43341a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-279 507,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25839@0" ObjectIDZND0="25840@x" ObjectIDZND1="g_40e63d0@0" Pin0InfoVect0LinkObjId="SW-147815_0" Pin0InfoVect1LinkObjId="g_40e63d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147813_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="507,-279 507,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43343e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-272 507,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="25839@x" ObjectIDND1="g_40e63d0@0" ObjectIDZND0="25840@1" Pin0InfoVect0LinkObjId="SW-147815_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147813_0" Pin1InfoVect1LinkObjId="g_40e63d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-272 507,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ded40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-271 684,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="25829@x" ObjectIDND1="25830@x" ObjectIDZND0="g_4245030@0" Pin0InfoVect0LinkObjId="g_4245030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147781_0" Pin1InfoVect1LinkObjId="SW-147783_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="657,-271 684,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffd3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-279 657,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25829@0" ObjectIDZND0="25830@x" ObjectIDZND1="g_4245030@0" Pin0InfoVect0LinkObjId="SW-147783_0" Pin0InfoVect1LinkObjId="g_4245030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="657,-279 657,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffd620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-271 657,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="25829@x" ObjectIDND1="g_4245030@0" ObjectIDZND0="25830@1" Pin0InfoVect0LinkObjId="SW-147783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147781_0" Pin1InfoVect1LinkObjId="g_4245030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="657,-271 657,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44363a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-271 838,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="25849@x" ObjectIDND1="25850@x" ObjectIDZND0="g_4449630@0" Pin0InfoVect0LinkObjId="g_4449630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147846_0" Pin1InfoVect1LinkObjId="SW-147848_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="813,-271 838,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41d5d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-279 813,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25849@0" ObjectIDZND0="25850@x" ObjectIDZND1="g_4449630@0" Pin0InfoVect0LinkObjId="SW-147848_0" Pin0InfoVect1LinkObjId="g_4449630_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="813,-279 813,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41d5f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-271 813,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="25849@x" ObjectIDND1="g_4449630@0" ObjectIDZND0="25850@1" Pin0InfoVect0LinkObjId="SW-147848_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147846_0" Pin1InfoVect1LinkObjId="g_4449630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="813,-271 813,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407ef10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-270 1347,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="25824@x" ObjectIDND1="25826@x" ObjectIDZND0="g_41a9860@0" Pin0InfoVect0LinkObjId="g_41a9860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147765_0" Pin1InfoVect1LinkObjId="SW-147768_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-270 1347,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_442ea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-279 1326,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25824@0" ObjectIDZND0="25826@x" ObjectIDZND1="g_41a9860@0" Pin0InfoVect0LinkObjId="SW-147768_0" Pin0InfoVect1LinkObjId="g_41a9860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-279 1326,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_442ec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-270 1326,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="25824@x" ObjectIDND1="g_41a9860@0" ObjectIDZND0="25826@1" Pin0InfoVect0LinkObjId="SW-147768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147765_0" Pin1InfoVect1LinkObjId="g_41a9860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-270 1326,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4180a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1492,-270 1511,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="25844@x" ObjectIDND1="25846@x" ObjectIDZND0="g_331ff60@0" Pin0InfoVect0LinkObjId="g_331ff60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147829_0" Pin1InfoVect1LinkObjId="SW-147832_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1492,-270 1511,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20aa240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-279 1494,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25844@0" ObjectIDZND0="25846@x" ObjectIDZND1="g_331ff60@0" Pin0InfoVect0LinkObjId="SW-147832_0" Pin0InfoVect1LinkObjId="g_331ff60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-279 1494,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20aa470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-270 1494,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="25844@x" ObjectIDND1="g_331ff60@0" ObjectIDZND0="25846@1" Pin0InfoVect0LinkObjId="SW-147832_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147829_0" Pin1InfoVect1LinkObjId="g_331ff60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-270 1494,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_413f0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-274 1693,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="25834@x" ObjectIDND1="25836@x" ObjectIDZND0="g_41a25e0@0" Pin0InfoVect0LinkObjId="g_41a25e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147797_0" Pin1InfoVect1LinkObjId="SW-147800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-274 1693,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43e6360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-280 1667,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="25834@0" ObjectIDZND0="25836@x" ObjectIDZND1="g_41a25e0@0" Pin0InfoVect0LinkObjId="SW-147800_0" Pin0InfoVect1LinkObjId="g_41a25e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-280 1667,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43e65c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-274 1667,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="25834@x" ObjectIDND1="g_41a25e0@0" ObjectIDZND0="25836@1" Pin0InfoVect0LinkObjId="SW-147800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147797_0" Pin1InfoVect1LinkObjId="g_41a25e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-274 1667,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_422d890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-228 507,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25840@0" ObjectIDZND0="g_42fdbb0@0" Pin0InfoVect0LinkObjId="g_42fdbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-228 507,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4205430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-157 507,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_42fdbb0@1" ObjectIDZND0="25843@1" Pin0InfoVect0LinkObjId="SW-147818_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42fdbb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-157 507,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41691a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-228 657,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25830@0" ObjectIDZND0="g_4205690@0" Pin0InfoVect0LinkObjId="g_4205690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147783_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="657,-228 657,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4432a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="657,-161 657,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_4205690@1" ObjectIDZND0="25833@1" Pin0InfoVect0LinkObjId="SW-147786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4205690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="657,-161 657,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4242700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-149 1326,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25828@1" ObjectIDZND0="g_4432c60@1" Pin0InfoVect0LinkObjId="g_4432c60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-149 1326,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41b4ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-213 1326,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_4432c60@0" ObjectIDZND0="25826@0" Pin0InfoVect0LinkObjId="SW-147768_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4432c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-213 1326,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40f9ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-149 1494,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25848@1" ObjectIDZND0="g_41b4d40@1" Pin0InfoVect0LinkObjId="g_41b4d40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-149 1494,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40ed7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-212 1494,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_41b4d40@0" ObjectIDZND0="25846@0" Pin0InfoVect0LinkObjId="SW-147832_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41b4d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-212 1494,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_431c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-150 1667,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25838@1" ObjectIDZND0="g_40eda50@1" Pin0InfoVect0LinkObjId="g_40eda50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-150 1667,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4197c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-216 1667,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_40eda50@0" ObjectIDZND0="25836@0" Pin0InfoVect0LinkObjId="SW-147800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40eda50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-216 1667,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_432eb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-149 1830,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="27842@1" ObjectIDZND0="g_4197e80@1" Pin0InfoVect0LinkObjId="g_4197e80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182953_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-149 1830,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_435a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-220 1856,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_4197e80@0" ObjectIDND1="27839@x" ObjectIDZND0="g_432eda0@0" Pin0InfoVect0LinkObjId="g_432eda0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4197e80_0" Pin1InfoVect1LinkObjId="SW-182949_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-220 1856,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ce040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-213 1830,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_4197e80@0" ObjectIDZND0="g_432eda0@0" ObjectIDZND1="27839@x" Pin0InfoVect0LinkObjId="g_432eda0_0" Pin0InfoVect1LinkObjId="SW-182949_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4197e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-213 1830,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ce2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,-220 1830,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_432eda0@0" ObjectIDND1="g_4197e80@0" ObjectIDZND0="27839@0" Pin0InfoVect0LinkObjId="SW-182949_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_432eda0_0" Pin1InfoVect1LinkObjId="g_4197e80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-220 1830,-228 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="636" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1052" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1469" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="705" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1401" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="848" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1140" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="938" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="705" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1401" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1326" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1667" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1830" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1494" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="193" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="353" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="507" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="657" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="813" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="985" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1119" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1052" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-147474" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98.500000 -959.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25770" ObjectName="DYN-YA_XJC"/>
     <cge:Meas_Ref ObjectId="147474"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3451c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -16.000000 -1033.500000) translate(0,16)">西教场变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407c2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1004.000000 -1056.000000) translate(0,12)">35kV西大连线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41935f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1430.000000 -1046.000000) translate(0,12)">35kV姚西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_438b190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -1048.000000) translate(0,12)">35kV黄西太线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_417d6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -650.000000) translate(0,12)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_417d6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -650.000000) translate(0,27)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_417d6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -650.000000) translate(0,42)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_417d6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -650.000000) translate(0,57)">Ud=7.38%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_41c8f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_43078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4239d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -650.000000) translate(0,12)">SZ9-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4239d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -650.000000) translate(0,27)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4239d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -650.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4239d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -650.000000) translate(0,57)">Ud=7.25%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aea0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -102.400000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aea0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -102.400000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_414fe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1103.000000 -100.400000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_414fe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1103.000000 -100.400000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4171c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 360.000000 -348.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_413f850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 360.000000 -253.000000) translate(0,12)">0522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_412bf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 362.000000 -300.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4450300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1333.000000 -347.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_441e920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1333.000000 -253.000000) translate(0,12)">0613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4405fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1333.000000 -138.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43f0f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1335.000000 -300.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_434d070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1848.000000 -392.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_432e360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.000000 -347.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41bccf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.000000 -253.000000) translate(0,12)">0552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_422a4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.000000 -138.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_422af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -347.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4205cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -253.000000) translate(0,12)">0623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4173bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -138.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41a5c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 -300.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4197300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -347.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a765c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -253.000000) translate(0,12)">0542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b4ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -138.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41f4970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 -300.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a73d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -347.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2142780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -253.000000) translate(0,12)">0532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c54f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -138.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d6f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 516.000000 -300.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e8530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1160.000000 -474.000000) translate(0,12)">01220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41e1100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1126.000000 -419.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d8dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 -419.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_412eb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -481.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_417f340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -339.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40d9090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 945.000000 -346.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d6e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 -347.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4150130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1674.000000 -254.000000) translate(0,12)">0633</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35fe160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1674.000000 -139.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_410ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1676.000000 -301.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f7ce00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -397.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ad960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1203.000000 -338.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41009f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1147.000000 -345.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4164e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.000000 -631.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_372fa50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.000000 -463.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404fcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -416.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4077760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -513.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38728e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 -468.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_384edd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -758.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40797b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 713.000000 -715.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f5510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -463.000000) translate(0,12)">00227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2139000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -416.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42fdf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -513.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_414bda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1410.000000 -468.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b60470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -758.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4467400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1410.000000 -715.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43f5b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 282.000000 -785.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_433b8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -818.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4328300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -971.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4331e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -917.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41bc550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -866.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4216620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -746.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4218540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -817.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41735f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -971.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41f1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -917.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34fcc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -866.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2141e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -817.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41df050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.000000 -971.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41d3c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -917.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4318710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1478.000000 -866.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4069490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1837.000000 -347.000000) translate(0,12)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4442780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1837.000000 -253.000000) translate(0,12)">0643</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44238c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1837.000000 -138.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_442d750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1839.000000 -300.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_442d8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 216.000000 -1001.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4225610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 216.000000 -1038.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b5a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -143.000000 -625.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffcc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -609.000000) translate(0,12)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342fa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 634.000000 -29.000000) translate(0,12)">龙岗线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3411930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 799.000000 -32.000000) translate(0,12)">仁和线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_444e0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1309.000000 -27.000000) translate(0,12)">竹园线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4493b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1457.000000 -35.000000) translate(0,12)">观测站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4186850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1649.000000 -43.000000) translate(0,12)">官屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41e57e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1800.000000 -39.000000) translate(0,12)">金龟街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20b0020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -196.000000 -56.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_443e300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -66.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_443e300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -66.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_4233070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -96.000000) translate(0,17)">5712404</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41ff260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 553.000000 -670.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a77590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -668.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1fc58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 217.000000 -923.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f9250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 200.000000 -340.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f7ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 200.000000 -246.000000) translate(0,12)">0512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4103d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 202.000000 -293.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e69d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 169.000000 -68.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4071190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 102.000000 -262.000000) translate(0,12)">05110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404f520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -142.000000) translate(0,12)">05120</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_405db90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 259.000000 -284.000000) translate(0,12)">05210</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4248d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 292.000000 -48.000000) translate(0,12)">西教场变1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c72e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 417.000000 -293.000000) translate(0,12)">05310</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4166690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -132.000000) translate(0,12)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4166690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -132.000000) translate(0,27)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4166690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -132.000000) translate(0,42)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4166690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -132.000000) translate(0,57)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4166690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -132.000000) translate(0,72)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41668e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.000000 -299.000000) translate(0,12)">05410</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4386160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 724.000000 -300.000000) translate(0,12)">05510</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4386370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -279.000000) translate(0,12)">06120</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41803a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -280.000000) translate(0,12)">06220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_417e240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 -284.000000) translate(0,12)">06320</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_417e480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1734.000000 -296.000000) translate(0,12)">06420</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41cfe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 822.000000 -300.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_420a210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 486.000000 -39.000000) translate(0,12)">城区线</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-147610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -837.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25795" ObjectName="SW-YA_XJC.YA_XJC_381BK"/>
     <cge:Meas_Ref ObjectId="147610"/>
    <cge:TPSR_Ref TObjectID="25795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147673">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -690.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25807" ObjectName="SW-YA_XJC.YA_XJC_301BK"/>
     <cge:Meas_Ref ObjectId="147673"/>
    <cge:TPSR_Ref TObjectID="25807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147749">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25812" ObjectName="SW-YA_XJC.YA_XJC_001BK"/>
     <cge:Meas_Ref ObjectId="147749"/>
    <cge:TPSR_Ref TObjectID="25812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147813">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25839" ObjectName="SW-YA_XJC.YA_XJC_053BK"/>
     <cge:Meas_Ref ObjectId="147813"/>
    <cge:TPSR_Ref TObjectID="25839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -837.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25799" ObjectName="SW-YA_XJC.YA_XJC_382BK"/>
     <cge:Meas_Ref ObjectId="147631"/>
    <cge:TPSR_Ref TObjectID="25799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147652">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 -837.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25803" ObjectName="SW-YA_XJC.YA_XJC_383BK"/>
     <cge:Meas_Ref ObjectId="147652"/>
    <cge:TPSR_Ref TObjectID="25803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -686.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25809" ObjectName="SW-YA_XJC.YA_XJC_302BK"/>
     <cge:Meas_Ref ObjectId="147708"/>
    <cge:TPSR_Ref TObjectID="25809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147755">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25816" ObjectName="SW-YA_XJC.YA_XJC_002BK"/>
     <cge:Meas_Ref ObjectId="147755"/>
    <cge:TPSR_Ref TObjectID="25816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147862">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.000000 -447.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25854" ObjectName="SW-YA_XJC.YA_XJC_012BK"/>
     <cge:Meas_Ref ObjectId="147862"/>
    <cge:TPSR_Ref TObjectID="25854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147872">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 344.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25858" ObjectName="SW-YA_XJC.YA_XJC_052BK"/>
     <cge:Meas_Ref ObjectId="147872"/>
    <cge:TPSR_Ref TObjectID="25858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25829" ObjectName="SW-YA_XJC.YA_XJC_054BK"/>
     <cge:Meas_Ref ObjectId="147781"/>
    <cge:TPSR_Ref TObjectID="25829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147846">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 804.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25849" ObjectName="SW-YA_XJC.YA_XJC_055BK"/>
     <cge:Meas_Ref ObjectId="147846"/>
    <cge:TPSR_Ref TObjectID="25849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25824" ObjectName="SW-YA_XJC.YA_XJC_061BK"/>
     <cge:Meas_Ref ObjectId="147765"/>
    <cge:TPSR_Ref TObjectID="25824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147829">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1485.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25844" ObjectName="SW-YA_XJC.YA_XJC_062BK"/>
     <cge:Meas_Ref ObjectId="147829"/>
    <cge:TPSR_Ref TObjectID="25844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1821.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27838" ObjectName="SW-YA_XJC.YA_XJC_064BK"/>
     <cge:Meas_Ref ObjectId="182947"/>
    <cge:TPSR_Ref TObjectID="27838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-265494">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 -264.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42944" ObjectName="SW-YA_XJC.YA_XJC_051BK"/>
     <cge:Meas_Ref ObjectId="265494"/>
    <cge:TPSR_Ref TObjectID="42944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1658.000000 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25834" ObjectName="SW-YA_XJC.YA_XJC_063BK"/>
     <cge:Meas_Ref ObjectId="147797"/>
    <cge:TPSR_Ref TObjectID="25834"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_XJC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xidalianTxjc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1052,-1041 1052,-1013 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34316" ObjectName="AC-35kV.LN_xidalianTxjc"/>
    <cge:TPSR_Ref TObjectID="34316_SS-218"/></metadata>
   <polyline fill="none" opacity="0" points="1052,-1041 1052,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YA" endPointId="0" endStationName="YA_XJC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yaoxi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1469,-1030 1469,-1002 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37772" ObjectName="AC-35kV.LN_yaoxi"/>
    <cge:TPSR_Ref TObjectID="37772_SS-218"/></metadata>
   <polyline fill="none" opacity="0" points="1469,-1030 1469,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_XJC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_huangxitaiXJC" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="636,-1009 636,-1030 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38083" ObjectName="AC-35kV.LN_huangxitaiXJC"/>
    <cge:TPSR_Ref TObjectID="38083_SS-218"/></metadata>
   <polyline fill="none" opacity="0" points="636,-1009 636,-1030 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_42ec080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4141270">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 655.000000 -986.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4382af0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -986.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_407f3b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1488.000000 -986.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4318ba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.000000 -533.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43fdcd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -533.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4352390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 843.000000 -705.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_433d120">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -656.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4454570">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 420.500000 -121.500000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41e98b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b6070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 850.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3751460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1363.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4148d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cb74b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1704.000000 -47.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_410f040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1867.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_444d690">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -654.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f4fc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.000000 -157.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4339eb0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 762.000000 -210.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3236b90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -96.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_420f0e0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 798.500000 -590.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40f1a90">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 798.500000 -474.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43067d0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1494.500000 -475.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_430fbd0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 276.500000 -187.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4346890">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 427.500000 -264.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e63d0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 585.500000 -265.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4245030">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 738.500000 -264.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4449630">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 892.500000 -264.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41a9860">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1401.500000 -263.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331ff60">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1565.500000 -263.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41a25e0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1747.500000 -267.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42fdbb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 502.000000 -152.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4205690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 -156.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4432c60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1321.000000 -155.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41b4d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.000000 -154.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40eda50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1662.000000 -158.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4197e80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1825.000000 -155.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_432eda0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1910.500000 -213.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -883.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25795"/>
     <cge:Term_Ref ObjectID="36392"/>
    <cge:TPSR_Ref TObjectID="25795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -883.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25795"/>
     <cge:Term_Ref ObjectID="36392"/>
    <cge:TPSR_Ref TObjectID="25795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -883.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25795"/>
     <cge:Term_Ref ObjectID="36392"/>
    <cge:TPSR_Ref TObjectID="25795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -878.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25799"/>
     <cge:Term_Ref ObjectID="36400"/>
    <cge:TPSR_Ref TObjectID="25799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -878.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25799"/>
     <cge:Term_Ref ObjectID="36400"/>
    <cge:TPSR_Ref TObjectID="25799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147501" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -878.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25799"/>
     <cge:Term_Ref ObjectID="36400"/>
    <cge:TPSR_Ref TObjectID="25799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25803"/>
     <cge:Term_Ref ObjectID="36408"/>
    <cge:TPSR_Ref TObjectID="25803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25803"/>
     <cge:Term_Ref ObjectID="36408"/>
    <cge:TPSR_Ref TObjectID="25803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25803"/>
     <cge:Term_Ref ObjectID="36408"/>
    <cge:TPSR_Ref TObjectID="25803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -731.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25807"/>
     <cge:Term_Ref ObjectID="36416"/>
    <cge:TPSR_Ref TObjectID="25807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -731.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25807"/>
     <cge:Term_Ref ObjectID="36416"/>
    <cge:TPSR_Ref TObjectID="25807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -731.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25807"/>
     <cge:Term_Ref ObjectID="36416"/>
    <cge:TPSR_Ref TObjectID="25807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -484.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25816"/>
     <cge:Term_Ref ObjectID="36434"/>
    <cge:TPSR_Ref TObjectID="25816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147539" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -484.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25816"/>
     <cge:Term_Ref ObjectID="36434"/>
    <cge:TPSR_Ref TObjectID="25816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147535" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -484.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25816"/>
     <cge:Term_Ref ObjectID="36434"/>
    <cge:TPSR_Ref TObjectID="25816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -734.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25809"/>
     <cge:Term_Ref ObjectID="36420"/>
    <cge:TPSR_Ref TObjectID="25809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -734.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25809"/>
     <cge:Term_Ref ObjectID="36420"/>
    <cge:TPSR_Ref TObjectID="25809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147517" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -734.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25809"/>
     <cge:Term_Ref ObjectID="36420"/>
    <cge:TPSR_Ref TObjectID="25809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25858"/>
     <cge:Term_Ref ObjectID="36518"/>
    <cge:TPSR_Ref TObjectID="25858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25858"/>
     <cge:Term_Ref ObjectID="36518"/>
    <cge:TPSR_Ref TObjectID="25858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25858"/>
     <cge:Term_Ref ObjectID="36518"/>
    <cge:TPSR_Ref TObjectID="25858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 647.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25829"/>
     <cge:Term_Ref ObjectID="36460"/>
    <cge:TPSR_Ref TObjectID="25829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 647.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25829"/>
     <cge:Term_Ref ObjectID="36460"/>
    <cge:TPSR_Ref TObjectID="25829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 647.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25829"/>
     <cge:Term_Ref ObjectID="36460"/>
    <cge:TPSR_Ref TObjectID="25829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1485.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25844"/>
     <cge:Term_Ref ObjectID="36490"/>
    <cge:TPSR_Ref TObjectID="25844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1485.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25844"/>
     <cge:Term_Ref ObjectID="36490"/>
    <cge:TPSR_Ref TObjectID="25844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1485.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25844"/>
     <cge:Term_Ref ObjectID="36490"/>
    <cge:TPSR_Ref TObjectID="25844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -530.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25854"/>
     <cge:Term_Ref ObjectID="36510"/>
    <cge:TPSR_Ref TObjectID="25854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -530.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25854"/>
     <cge:Term_Ref ObjectID="36510"/>
    <cge:TPSR_Ref TObjectID="25854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -530.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25854"/>
     <cge:Term_Ref ObjectID="36510"/>
    <cge:TPSR_Ref TObjectID="25854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-147523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 -760.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-147524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 -760.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-147525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 -760.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-147526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 -760.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-147543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -462.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-147544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -462.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-147545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -462.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-147546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -462.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-147547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 -461.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-147548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 -461.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-147549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 -461.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-147550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 -461.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-182933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1847.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="182933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27838"/>
     <cge:Term_Ref ObjectID="39403"/>
    <cge:TPSR_Ref TObjectID="27838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-182934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1847.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="182934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27838"/>
     <cge:Term_Ref ObjectID="39403"/>
    <cge:TPSR_Ref TObjectID="27838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-182925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1847.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="182925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27838"/>
     <cge:Term_Ref ObjectID="39403"/>
    <cge:TPSR_Ref TObjectID="27838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-147533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 -574.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25862"/>
     <cge:Term_Ref ObjectID="36526"/>
    <cge:TPSR_Ref TObjectID="25862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-147534" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 -574.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25862"/>
     <cge:Term_Ref ObjectID="36526"/>
    <cge:TPSR_Ref TObjectID="25862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-147541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1539.000000 -596.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25863"/>
     <cge:Term_Ref ObjectID="36533"/>
    <cge:TPSR_Ref TObjectID="25863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-147542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1539.000000 -596.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25863"/>
     <cge:Term_Ref ObjectID="36533"/>
    <cge:TPSR_Ref TObjectID="25863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-265801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 179.000000 13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="265801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42944"/>
     <cge:Term_Ref ObjectID="18876"/>
    <cge:TPSR_Ref TObjectID="42944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-265802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 179.000000 13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="265802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42944"/>
     <cge:Term_Ref ObjectID="18876"/>
    <cge:TPSR_Ref TObjectID="42944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-265798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 179.000000 13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="265798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42944"/>
     <cge:Term_Ref ObjectID="18876"/>
    <cge:TPSR_Ref TObjectID="42944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25839"/>
     <cge:Term_Ref ObjectID="36480"/>
    <cge:TPSR_Ref TObjectID="25839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25839"/>
     <cge:Term_Ref ObjectID="36480"/>
    <cge:TPSR_Ref TObjectID="25839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25839"/>
     <cge:Term_Ref ObjectID="36480"/>
    <cge:TPSR_Ref TObjectID="25839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25849"/>
     <cge:Term_Ref ObjectID="36500"/>
    <cge:TPSR_Ref TObjectID="25849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25849"/>
     <cge:Term_Ref ObjectID="36500"/>
    <cge:TPSR_Ref TObjectID="25849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25849"/>
     <cge:Term_Ref ObjectID="36500"/>
    <cge:TPSR_Ref TObjectID="25849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 2.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25824"/>
     <cge:Term_Ref ObjectID="36450"/>
    <cge:TPSR_Ref TObjectID="25824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 2.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25824"/>
     <cge:Term_Ref ObjectID="36450"/>
    <cge:TPSR_Ref TObjectID="25824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 2.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25824"/>
     <cge:Term_Ref ObjectID="36450"/>
    <cge:TPSR_Ref TObjectID="25824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25834"/>
     <cge:Term_Ref ObjectID="36470"/>
    <cge:TPSR_Ref TObjectID="25834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25834"/>
     <cge:Term_Ref ObjectID="36470"/>
    <cge:TPSR_Ref TObjectID="25834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25834"/>
     <cge:Term_Ref ObjectID="36470"/>
    <cge:TPSR_Ref TObjectID="25834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 615.000000 -480.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25812"/>
     <cge:Term_Ref ObjectID="36426"/>
    <cge:TPSR_Ref TObjectID="25812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 615.000000 -480.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25812"/>
     <cge:Term_Ref ObjectID="36426"/>
    <cge:TPSR_Ref TObjectID="25812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 615.000000 -480.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25812"/>
     <cge:Term_Ref ObjectID="36426"/>
    <cge:TPSR_Ref TObjectID="25812"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="205" y="-1008"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="205" y="-1047"/></g>
   <g href="AVC西教场站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="209" y="-939"/></g>
   <g href="35kV西教场变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="62" x="-145" y="-625"/></g>
   <g href="35kV西教场变35kV黄西太线381间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="645" y="-866"/></g>
   <g href="35kV西教场变35kV西大连线382间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1061" y="-866"/></g>
   <g href="35kV西教场变35kV姚西线383间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1478" y="-866"/></g>
   <g href="35kV西教场变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="553" y="-670"/></g>
   <g href="35kV西教场变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1246" y="-668"/></g>
   <g href="35kV西教场变10kV1号电容器052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="362" y="-300"/></g>
   <g href="35kV西教场变10kV城区线053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="516" y="-300"/></g>
   <g href="35kV西教场变10kV龙岗线054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="666" y="-300"/></g>
   <g href="35kV西教场变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1041" y="-481"/></g>
   <g href="35kV西教场变10kV麻纺厂线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1335" y="-300"/></g>
   <g href="35kV西教场变10kV观测站线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1503" y="-300"/></g>
   <g href="35kV西教场变10kV官屯线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1676" y="-301"/></g>
   <g href="35kV西教场变10kV金龟街线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1839" y="-300"/></g>
   <g href="35kV西教场变10kV仁和线055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="821" y="-301"/></g>
   <g href="35kV西教场变10kV备用一051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="202" y="-293"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4183710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.000000 744.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_212ac60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.000000 759.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_417c8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.000000 716.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_414f920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.000000 730.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4130410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 882.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2101ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 867.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f7c550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 852.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ee840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 561.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_420a3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 576.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41f5400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 222.000000 446.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20af0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 222.000000 461.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3266050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 214.000000 418.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2080190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 222.000000 432.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3286fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.000000 -5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2121570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 277.000000 -20.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35ac280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 -35.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41723a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 590.000000 733.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4197800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 579.000000 718.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4170850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 703.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41da7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 564.000000 481.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4185880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 553.000000 466.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_406c010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 451.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_410f6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 581.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3799400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 596.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_437c930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 445.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40d8a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 460.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1773.000000 417.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41bdf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 431.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43fe9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 881.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_414f650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 866.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39db1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1132.000000 851.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4148610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.000000 880.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4139a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1514.000000 865.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4326de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1539.000000 850.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_213a990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1297.000000 735.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4065880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1286.000000 720.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d7160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1311.000000 705.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38643b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1294.000000 485.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_440cde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1283.000000 470.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4089340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 455.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_421fa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 986.000000 530.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4a7a220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 975.000000 515.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_212e2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1000.000000 500.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41cd460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 442.000000 -4.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41c3730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -19.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209b700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 456.000000 -34.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41a7110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 589.000000 -6.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41a3f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2133f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 -36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4189cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 -6.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c3910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4170530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 784.000000 -36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_393e750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 -3.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c2730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 -18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42eec40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 -33.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41288b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1429.000000 -4.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4120250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -19.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40fc450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -34.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40039f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1627.000000 -9.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e4cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1616.000000 -24.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4162080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1641.000000 -39.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404f970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1793.000000 -5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_209e000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1782.000000 -20.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ec9d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1807.000000 -35.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fa3e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 120.000000 -12.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b54b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 109.000000 -27.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_393eeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 134.000000 -42.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.482Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 502.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34046" ObjectName="EC-YA_XJC.482Ld"/>
    <cge:TPSR_Ref TObjectID="34046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.485Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34044" ObjectName="EC-YA_XJC.485Ld"/>
    <cge:TPSR_Ref TObjectID="34044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.486Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34042" ObjectName="EC-YA_XJC.486Ld"/>
    <cge:TPSR_Ref TObjectID="34042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.483Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1321.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34043" ObjectName="EC-YA_XJC.483Ld"/>
    <cge:TPSR_Ref TObjectID="34043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.484Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34047" ObjectName="EC-YA_XJC.484Ld"/>
    <cge:TPSR_Ref TObjectID="34047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.487Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1662.000000 -46.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34045" ObjectName="EC-YA_XJC.487Ld"/>
    <cge:TPSR_Ref TObjectID="34045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.488Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1825.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34048" ObjectName="EC-YA_XJC.488Ld"/>
    <cge:TPSR_Ref TObjectID="34048"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="205" y="-1008"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="205" y="-1008"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="205" y="-1047"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="205" y="-1047"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="209,-939 206,-942 206,-887 209,-890 209,-939" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="209,-939 206,-942 265,-942 262,-939 209,-939" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="209,-890 206,-887 265,-887 262,-890 209,-890" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="262,-939 265,-942 265,-887 262,-890 262,-939" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="49" stroke="rgb(255,255,255)" width="53" x="209" y="-939"/>
     <rect fill="none" height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="209" y="-939"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="62" x="-145" y="-625"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="62" x="-145" y="-625"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="645" y="-866"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="645" y="-866"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1061" y="-866"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1061" y="-866"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1478" y="-866"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1478" y="-866"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="553" y="-670"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="553" y="-670"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1246" y="-668"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1246" y="-668"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="362" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="362" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="516" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="516" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="666" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="666" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1041" y="-481"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1041" y="-481"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1335" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1335" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1503" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1503" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1676" y="-301"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1676" y="-301"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1839" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1839" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="821" y="-301"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="821" y="-301"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="202" y="-293"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="202" y="-293"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_4396520">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -922.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42eae60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 -922.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e0d500">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1404.000000 -922.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43373b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.000000 -615.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b2260">
    <use class="BV-10KV" transform="matrix(1.820513 -0.000000 0.000000 -1.725490 869.000000 -105.000000)" xlink:href="#voltageTransformer:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37f6ca0">
    <use class="BV-10KV" transform="matrix(1.804946 -0.000000 0.000000 -1.697084 1071.753678 -106.892433)" xlink:href="#voltageTransformer:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -69.000000 -994.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -814.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217887" ObjectName="YA_XJC:YA_XJC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219742" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -774.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219742" ObjectName="YA_XJC:YA_XJC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -895.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217887" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -854.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217887" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YA_XJC"/>
</svg>