<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-319" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-336 -1014 2052 1122">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="currentTransformer:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="37" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="23" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="23" y1="32" y2="30"/>
    <circle cx="29" cy="7" r="7.5" stroke-width="1"/>
    <circle cx="34" cy="29" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="5" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="35" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="35" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="23" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="23" y1="20" y2="18"/>
    <circle cx="34" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="32" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="26" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="32" y1="5" y2="5"/>
    <circle cx="23" cy="29" r="7.5" stroke-width="1"/>
    <circle cx="23" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="2" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="3" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="35" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="35" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="41" x2="41" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="48" x2="48" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="48" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="22" x2="22" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="29" x2="29" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="29" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="7" x2="7" y1="47" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="10" y1="46" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="13" y1="55" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="50" y2="50"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape177">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_2281bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape31_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="2" x2="26" y1="9" y2="33"/>
   </symbol>
   <symbol id="switch2:shape31_1">
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="14" y1="37" y2="5"/>
   </symbol>
   <symbol id="switch2:shape31-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="2" x2="26" y1="9" y2="33"/>
   </symbol>
   <symbol id="switch2:shape31-UnNor2">
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="14" y1="37" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="transformer2:shape99_0">
    <circle cx="40" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <circle cx="76" cy="48" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="70" x2="88" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="79" x2="88" y1="58" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="79" x2="70" y1="58" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="69" y1="47" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="69" y1="86" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="69" x2="67" y1="86" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="69" x2="69" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="39" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="32" y1="24" y2="31"/>
   </symbol>
   <symbol id="transformer2:shape99_1">
    <circle cx="40" cy="64" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="78" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="39" y1="62" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="32" y1="69" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape98">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="22" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="24" y2="24"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="35" y1="12" y2="12"/>
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a491c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a49e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a91b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a92710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a93880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a94390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a94dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a95710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a9b190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9da70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9e820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a9ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa7e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa8f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa0b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa1660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa3670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa4810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa5430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b67610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b68050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b62fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b645c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1132" width="2062" x="-341" y="-1019"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1563" x2="1563" y1="-770" y2="-738"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1563" x2="1563" y1="-731" y2="-711"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1558" x2="1567" y1="-711" y2="-711"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1559" x2="1565" y1="-709" y2="-709"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1560" x2="1565" y1="-707" y2="-707"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="687" x2="687" y1="-622" y2="-601"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1562" x2="1562" y1="-913" y2="-881"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1562" x2="1562" y1="-874" y2="-854"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1557" x2="1566" y1="-854" y2="-854"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1558" x2="1564" y1="-852" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1559" x2="1564" y1="-850" y2="-850"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="687,-576 682,-565 693,-565 687,-576 687,-575 687,-576 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="687,-590 682,-601 693,-601 687,-590 687,-591 687,-590 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="942,-544 947,-555 936,-555 942,-544 942,-545 942,-544 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="942,-530 947,-519 936,-519 942,-530 942,-529 942,-530 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="212,-145 206,-145 209,-136 212,-144 212,-145 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="212,-114 206,-114 209,-123 212,-115 212,-114 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-140 454,-140 457,-131 460,-139 460,-140 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-109 454,-109 457,-118 460,-110 460,-109 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-153 901,-153 904,-144 907,-152 907,-153 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-122 901,-122 904,-131 907,-123 907,-122 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1079,-146 1073,-146 1076,-137 1079,-145 1079,-146 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1079,-115 1073,-115 1076,-124 1079,-116 1079,-115 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1222,-146 1216,-146 1219,-137 1222,-145 1222,-146 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1222,-115 1216,-115 1219,-124 1222,-116 1222,-115 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-146 1368,-146 1371,-137 1374,-145 1374,-146 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-115 1368,-115 1371,-124 1374,-116 1374,-115 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-147 1534,-147 1537,-138 1540,-146 1540,-147 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-116 1534,-116 1537,-125 1540,-117 1540,-116 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="328,-143 322,-143 325,-134 328,-142 328,-143 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="328,-112 322,-112 325,-121 328,-113 328,-112 " stroke="rgb(255,255,0)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-297148">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46083" ObjectName="SW-CX_LJQGF.CX_LJQGF_301BK"/>
     <cge:Meas_Ref ObjectId="297148"/>
    <cge:TPSR_Ref TObjectID="46083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297135">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 -745.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46076" ObjectName="SW-CX_LJQGF.CX_LJQGF_101BK"/>
     <cge:Meas_Ref ObjectId="297135"/>
    <cge:TPSR_Ref TObjectID="46076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297181">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 718.000000 -232.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46102" ObjectName="SW-CX_LJQGF.CX_LJQGF_363BK"/>
     <cge:Meas_Ref ObjectId="297181"/>
    <cge:TPSR_Ref TObjectID="46102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297187">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 573.000000 -206.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46106" ObjectName="SW-CX_LJQGF.CX_LJQGF_362BK"/>
     <cge:Meas_Ref ObjectId="297187"/>
    <cge:TPSR_Ref TObjectID="46106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 200.000000 -213.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297157">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 449.000000 -208.693356)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46088" ObjectName="SW-CX_LJQGF.CX_LJQGF_361BK"/>
     <cge:Meas_Ref ObjectId="297157"/>
    <cge:TPSR_Ref TObjectID="46088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297152">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 895.000000 -221.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46085" ObjectName="SW-CX_LJQGF.CX_LJQGF_364BK"/>
     <cge:Meas_Ref ObjectId="297152"/>
    <cge:TPSR_Ref TObjectID="46085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297162">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.000000 -214.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46091" ObjectName="SW-CX_LJQGF.CX_LJQGF_365BK"/>
     <cge:Meas_Ref ObjectId="297162"/>
    <cge:TPSR_Ref TObjectID="46091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297167">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1210.000000 -214.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46094" ObjectName="SW-CX_LJQGF.CX_LJQGF_366BK"/>
     <cge:Meas_Ref ObjectId="297167"/>
    <cge:TPSR_Ref TObjectID="46094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297171">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1362.000000 -214.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46096" ObjectName="SW-CX_LJQGF.CX_LJQGF_367BK"/>
     <cge:Meas_Ref ObjectId="297171"/>
    <cge:TPSR_Ref TObjectID="46096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297176">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.000000 -215.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46099" ObjectName="SW-CX_LJQGF.CX_LJQGF_368BK"/>
     <cge:Meas_Ref ObjectId="297176"/>
    <cge:TPSR_Ref TObjectID="46099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1522.000000 -789.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 -216.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ca4630">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 -868.000000)" xlink:href="#currentTransformer:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 204.000000 -76.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 453.000000 -71.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 -84.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LJQGF.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -77.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46553" ObjectName="SM-CX_LJQGF.P1"/>
    <cge:TPSR_Ref TObjectID="46553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LJQGF.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.000000 -77.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46554" ObjectName="SM-CX_LJQGF.P2"/>
    <cge:TPSR_Ref TObjectID="46554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LJQGF.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1366.000000 -77.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46555" ObjectName="SM-CX_LJQGF.P3"/>
    <cge:TPSR_Ref TObjectID="46555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LJQGF.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1532.000000 -78.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46556" ObjectName="SM-CX_LJQGF.P4"/>
    <cge:TPSR_Ref TObjectID="46556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 -74.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_ZHY" endPointId="0" endStationName="CX_LJQGF" flowDrawDirect="1" flowShape="0" id="AC-110kV.zhilu_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="821,-947 821,-989 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46133" ObjectName="AC-110kV.zhilu_line"/>
    <cge:TPSR_Ref TObjectID="46133_SS-319"/></metadata>
   <polyline fill="none" opacity="0" points="821,-947 821,-989 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20eed50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 882.000000 -871.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ef590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -378.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ac77c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 727.000000 -659.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1a04780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="377,-384 364,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20ef590@0" ObjectIDZND0="46110@1" Pin0InfoVect0LinkObjId="SW-297191_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20ef590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="377,-384 364,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a658f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-518 304,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1c983c0@1" ObjectIDZND0="g_1d900b0@0" Pin0InfoVect0LinkObjId="g_1d900b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c983c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-518 304,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da6f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-354 820,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46121@0" ObjectIDZND0="46084@0" Pin0InfoVect0LinkObjId="SW-297149_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c8b630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-354 820,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a05480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-665 745,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46079@0" ObjectIDZND0="g_1ac77c0@0" Pin0InfoVect0LinkObjId="g_1ac77c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="763,-665 745,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c4da40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-857 820,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="currentTransformer" EndDevType2="lightningRod" ObjectIDND0="46078@1" ObjectIDZND0="46080@x" ObjectIDZND1="g_1ca4630@0" ObjectIDZND2="g_1acb1e0@0" Pin0InfoVect0LinkObjId="SW-297139_0" Pin0InfoVect1LinkObjId="g_1ca4630_0" Pin0InfoVect2LinkObjId="g_1acb1e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297137_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="820,-857 820,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1c507c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-877 833,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_1ca4630@0" ObjectIDND1="g_1acb1e0@0" ObjectIDND2="46133@1" ObjectIDZND0="46080@0" Pin0InfoVect0LinkObjId="SW-297139_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ca4630_0" Pin1InfoVect1LinkObjId="g_1acb1e0_0" Pin1InfoVect2LinkObjId="g_1f100d0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-877 833,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cc8010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="869,-877 886,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46080@1" ObjectIDZND0="g_20eed50@0" Pin0InfoVect0LinkObjId="g_20eed50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297139_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="869,-877 886,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cc8270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-918 798,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="currentTransformer" ObjectIDND0="46080@x" ObjectIDND1="46078@x" ObjectIDND2="g_1acb1e0@0" ObjectIDZND0="g_1ca4630@0" Pin0InfoVect0LinkObjId="g_1ca4630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-297139_0" Pin1InfoVect1LinkObjId="SW-297137_0" Pin1InfoVect2LinkObjId="g_1acb1e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-918 798,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cc84d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-877 820,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="currentTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="46080@x" ObjectIDND1="46078@x" ObjectIDZND0="g_1ca4630@0" ObjectIDZND1="g_1acb1e0@0" ObjectIDZND2="46133@1" Pin0InfoVect0LinkObjId="g_1ca4630_0" Pin0InfoVect1LinkObjId="g_1acb1e0_0" Pin0InfoVect2LinkObjId="g_1f100d0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297139_0" Pin1InfoVect1LinkObjId="SW-297137_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="820,-877 820,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19e34e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-780 820,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46076@1" ObjectIDZND0="46078@0" Pin0InfoVect0LinkObjId="SW-297137_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297135_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-780 820,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19e36d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="759,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19e38c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-551 742,-541 689,-541 689,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="46081@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="742,-551 742,-541 689,-541 689,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19e3ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-587 742,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46081@1" ObjectIDZND0="g_1c55be0@0" Pin0InfoVect0LinkObjId="g_1c55be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297140_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="742,-587 742,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19e44c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="687,-621 687,-625 712,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_1c55be0@0" ObjectIDZND1="46081@x" Pin0InfoVect0LinkObjId="g_1c55be0_0" Pin0InfoVect1LinkObjId="SW-297140_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="687,-621 687,-625 712,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19e46f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-625 712,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46081@x" ObjectIDZND0="g_1c55be0@0" Pin0InfoVect0LinkObjId="g_1c55be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-625 712,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20edf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="822,-625 742,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="46081@x" ObjectIDZND1="g_1c55be0@0" Pin0InfoVect0LinkObjId="SW-297140_0" Pin0InfoVect1LinkObjId="g_1c55be0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="822,-625 742,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20ee1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-625 712,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46081@x" ObjectIDZND0="g_1c55be0@0" Pin0InfoVect0LinkObjId="g_1c55be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="742,-625 712,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ee450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-439 820,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46083@0" ObjectIDZND0="46084@1" Pin0InfoVect0LinkObjId="SW-297149_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297148_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-439 820,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ee6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-558 820,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="46580@0" ObjectIDZND0="46083@1" Pin0InfoVect0LinkObjId="SW-297148_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-558 820,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1efcb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-573 942,-573 942,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_1efbe90@0" ObjectIDND1="g_1efbe90@0" ObjectIDND2="46082@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1efbe90_0" Pin1InfoVect1LinkObjId="g_1efbe90_0" Pin1InfoVect2LinkObjId="SW-297147_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="916,-573 942,-573 942,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4d460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-542 916,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1efbe90@0" ObjectIDZND0="g_1efbe90@0" ObjectIDZND1="46082@x" ObjectIDZND2="46082@x" Pin0InfoVect0LinkObjId="g_1efbe90_0" Pin0InfoVect1LinkObjId="SW-297147_0" Pin0InfoVect2LinkObjId="SW-297147_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1efbe90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="916,-542 916,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4d6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1efbe90@0" ObjectIDND1="46082@x" ObjectIDZND0="g_1efbe90@0" ObjectIDZND1="46082@x" Pin0InfoVect0LinkObjId="g_1efbe90_0" Pin0InfoVect1LinkObjId="SW-297147_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1efbe90_0" Pin1InfoVect1LinkObjId="SW-297147_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="916,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4d920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="887,-505 887,-496 942,-496 942,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="46082@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="887,-505 887,-496 942,-496 942,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="887,-573 821,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="46082@x" ObjectIDND1="g_1efbe90@0" ObjectIDND2="g_1efbe90@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-297147_0" Pin1InfoVect1LinkObjId="g_1efbe90_0" Pin1InfoVect2LinkObjId="g_1efbe90_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="887,-573 821,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1e6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="887,-541 887,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="46082@1" ObjectIDZND0="g_1efbe90@0" ObjectIDZND1="g_1efbe90@0" ObjectIDZND2="46082@x" Pin0InfoVect0LinkObjId="g_1efbe90_0" Pin0InfoVect1LinkObjId="g_1efbe90_0" Pin0InfoVect2LinkObjId="SW-297147_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297147_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="887,-541 887,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1e940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="887,-573 916,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="46082@x" ObjectIDZND0="g_1efbe90@0" ObjectIDZND1="g_1efbe90@0" ObjectIDZND2="46082@x" Pin0InfoVect0LinkObjId="g_1efbe90_0" Pin0InfoVect1LinkObjId="g_1efbe90_0" Pin0InfoVect2LinkObjId="SW-297147_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="887,-573 916,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1eba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-474 304,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c983c0@0" ObjectIDND1="g_1357380@0" ObjectIDZND0="46109@1" Pin0InfoVect0LinkObjId="SW-297190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c983c0_0" Pin1InfoVect1LinkObjId="g_1357380_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-474 304,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1f670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-487 304,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1c983c0@0" ObjectIDZND0="g_1357380@0" ObjectIDZND1="46109@x" Pin0InfoVect0LinkObjId="g_1357380_0" Pin0InfoVect1LinkObjId="SW-297190_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c983c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="304,-487 304,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c8a6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-469 266,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1c983c0@0" ObjectIDND1="46109@x" ObjectIDZND0="g_1357380@0" Pin0InfoVect0LinkObjId="g_1357380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c983c0_0" Pin1InfoVect1LinkObjId="SW-297190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-469 266,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c8a900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="328,-384 304,-384 304,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="46110@0" ObjectIDZND0="46109@x" ObjectIDZND1="46121@0" Pin0InfoVect0LinkObjId="SW-297190_0" Pin0InfoVect1LinkObjId="g_1c8b630_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="328,-384 304,-384 304,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c8b3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-416 304,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="46109@0" ObjectIDZND0="46110@x" ObjectIDZND1="46121@0" Pin0InfoVect0LinkObjId="SW-297191_0" Pin0InfoVect1LinkObjId="g_1c8b630_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="304,-416 304,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c8b630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-385 304,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="46110@x" ObjectIDND1="46109@x" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_15daec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297191_0" Pin1InfoVect1LinkObjId="SW-297190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-385 304,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166ec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-68 726,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_22817c0@0" ObjectIDZND0="46104@x" ObjectIDZND1="46105@x" Pin0InfoVect0LinkObjId="SW-297183_0" Pin0InfoVect1LinkObjId="SW-297184_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22817c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="726,-68 726,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-97 726,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="46105@0" ObjectIDZND0="g_22817c0@0" ObjectIDZND1="46104@x" Pin0InfoVect0LinkObjId="g_22817c0_0" Pin0InfoVect1LinkObjId="SW-297183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="754,-97 726,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-97 726,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_22817c0@0" ObjectIDND1="46105@x" ObjectIDZND0="46104@0" Pin0InfoVect0LinkObjId="SW-297183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22817c0_0" Pin1InfoVect1LinkObjId="SW-297184_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-97 726,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_166f9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="743,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15daec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-334 904,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46086@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297153_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-334 904,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a4a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-321 1076,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46092@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297163_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-321 1076,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c52370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-327 1219,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46111@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-327 1219,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1efa190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-327 1371,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46097@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297172_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-327 1371,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207b110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-328 1537,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46100@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297177_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-328 1537,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f395c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1531,-853 1531,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1531,-853 1531,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f39820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1531,-695 1531,-642 1530,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e4aa30@0" ObjectIDZND0="g_19cf410@1" Pin0InfoVect0LinkObjId="g_19cf410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e4aa30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1531,-695 1531,-642 1530,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f39a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1530,-597 1530,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_19cf410@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19cf410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1530,-597 1530,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f39ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1563,-770 1531,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="g_1e4aa30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1e4aa30_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1563,-770 1531,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f3a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1531,-798 1531,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1e4aa30@0" Pin0InfoVect0LinkObjId="g_1e4aa30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1531,-798 1531,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f3aa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1531,-770 1531,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_1e4aa30@1" Pin0InfoVect0LinkObjId="g_1e4aa30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1531,-770 1531,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f3ac90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,-913 1531,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,-913 1531,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f15a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1531,-889 1531,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1531,-889 1531,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f15ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1531,-913 1531,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1531,-913 1531,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f17e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="799,-665 820,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="46079@1" ObjectIDZND0="46077@x" ObjectIDZND1="46580@x" Pin0InfoVect0LinkObjId="SW-297136_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297138_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="799,-665 820,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f18f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-753 820,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="46076@0" ObjectIDZND0="47822@0" Pin0InfoVect0LinkObjId="g_1e0c8f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-753 820,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f191b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="908,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="908,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f19ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-643 820,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46580@1" ObjectIDZND0="46077@x" ObjectIDZND1="46079@x" Pin0InfoVect0LinkObjId="SW-297136_0" Pin0InfoVect1LinkObjId="SW-297138_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="821,-643 820,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ccd210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-665 820,-682 820,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="46079@x" ObjectIDND1="46580@x" ObjectIDZND0="46077@0" Pin0InfoVect0LinkObjId="SW-297136_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-297138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-665 820,-682 820,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ccf880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="325,-324 325,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="325,-324 325,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd2bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="209,-321 209,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="209,-321 209,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd2da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="583,-312 583,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46107@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297188_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="583,-312 583,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd2f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="727,-337 727,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46103@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297182_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="727,-337 727,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd36e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-192 582,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="g_20f1db0@0" ObjectIDZND0="0@x" ObjectIDZND1="46106@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-297187_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20f1db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="625,-192 582,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd4150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="582,-114 582,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="g_20f1db0@0" ObjectIDZND1="46106@x" Pin0InfoVect0LinkObjId="g_20f1db0_0" Pin0InfoVect1LinkObjId="SW-297187_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="582,-114 582,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd43b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="582,-192 582,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="g_20f1db0@0" ObjectIDND1="0@x" ObjectIDZND0="46106@0" Pin0InfoVect0LinkObjId="SW-297187_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20f1db0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-192 582,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd4c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-181 458,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_19e76a0@0" ObjectIDZND0="46088@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-297157_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19e76a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="481,-181 458,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd4df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-217 458,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="46088@0" ObjectIDZND0="g_19e76a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_19e76a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297157_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="458,-217 458,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd4fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-181 458,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="generator" ObjectIDND0="g_19e76a0@0" ObjectIDND1="46088@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19e76a0_0" Pin1InfoVect1LinkObjId="SW-297157_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-181 458,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd6b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="233,-184 209,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_1cd51d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cd51d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="233,-184 209,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1dad0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="209,-222 209,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="0@0" ObjectIDZND0="g_1cd51d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1cd51d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="209,-222 209,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1dad340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="209,-184 209,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="generator" ObjectIDND0="g_1cd51d0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cd51d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="209,-184 209,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1dad5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="345,-189 325,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_1cd5da0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cd5da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="345,-189 325,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1dae070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="325,-220 325,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="0@0" ObjectIDZND0="g_1cd5da0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1cd5da0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="325,-220 325,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1dae2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="325,-189 325,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="generator" ObjectIDND0="g_1cd5da0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cd5da0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="325,-189 325,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1daed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-309 458,-354 457,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46089@1" ObjectIDZND0="46121@0" Pin0InfoVect0LinkObjId="g_1c8b630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297158_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-309 458,-354 457,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db08c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="751,-207 726,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1dafb10@0" ObjectIDZND0="46104@x" ObjectIDZND1="46102@x" Pin0InfoVect0LinkObjId="SW-297183_0" Pin0InfoVect1LinkObjId="SW-297181_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dafb10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="751,-207 726,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db13b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-143 726,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="46104@1" ObjectIDZND0="g_1dafb10@0" ObjectIDZND1="46102@x" Pin0InfoVect0LinkObjId="g_1dafb10_0" Pin0InfoVect1LinkObjId="SW-297181_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="726,-143 726,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db1610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-207 726,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1dafb10@0" ObjectIDND1="46104@x" ObjectIDZND0="46102@0" Pin0InfoVect0LinkObjId="SW-297181_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dafb10_0" Pin1InfoVect1LinkObjId="SW-297183_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-207 726,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20eaf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-188 903,-188 904,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_1db1870@0" ObjectIDZND0="46085@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-297152_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1db1870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="931,-188 903,-188 904,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20eba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-230 904,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="46085@0" ObjectIDZND0="g_1db1870@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1db1870_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="904,-230 904,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ebce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-186 904,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="generator" ObjectIDND0="g_1db1870@0" ObjectIDND1="46085@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1db1870_0" Pin1InfoVect1LinkObjId="SW-297152_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-186 904,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ebf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-187 1076,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_20e78d0@0" ObjectIDZND0="46091@x" ObjectIDZND1="46553@x" Pin0InfoVect0LinkObjId="SW-297162_0" Pin0InfoVect1LinkObjId="SM-CX_LJQGF.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e78d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-187 1076,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20eca30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-223 1076,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="46091@0" ObjectIDZND0="g_20e78d0@0" ObjectIDZND1="46553@x" Pin0InfoVect0LinkObjId="g_20e78d0_0" Pin0InfoVect1LinkObjId="SM-CX_LJQGF.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-223 1076,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ecc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-187 1076,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="generator" ObjectIDND0="g_20e78d0@0" ObjectIDND1="46091@x" ObjectIDZND0="46553@0" Pin0InfoVect0LinkObjId="SM-CX_LJQGF.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20e78d0_0" Pin1InfoVect1LinkObjId="SW-297162_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-187 1076,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d9fe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1249,-188 1219,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_20e8680@0" ObjectIDZND0="46094@x" ObjectIDZND1="46554@x" Pin0InfoVect0LinkObjId="SW-297167_0" Pin0InfoVect1LinkObjId="SM-CX_LJQGF.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e8680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1249,-188 1219,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da0830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-223 1219,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="46094@0" ObjectIDZND0="g_20e8680@0" ObjectIDZND1="46554@x" Pin0InfoVect0LinkObjId="g_20e8680_0" Pin0InfoVect1LinkObjId="SM-CX_LJQGF.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297167_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-223 1219,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da0a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-188 1219,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="generator" ObjectIDND0="g_20e8680@0" ObjectIDND1="46094@x" ObjectIDZND0="46554@0" Pin0InfoVect0LinkObjId="SM-CX_LJQGF.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20e8680_0" Pin1InfoVect1LinkObjId="SW-297167_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-188 1219,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da0cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1396,-188 1371,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_20e9430@0" ObjectIDZND0="46096@x" ObjectIDZND1="46555@x" Pin0InfoVect0LinkObjId="SW-297171_0" Pin0InfoVect1LinkObjId="SM-CX_LJQGF.P3_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e9430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1396,-188 1371,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da17e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-223 1371,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="46096@0" ObjectIDZND0="g_20e9430@0" ObjectIDZND1="46555@x" Pin0InfoVect0LinkObjId="g_20e9430_0" Pin0InfoVect1LinkObjId="SM-CX_LJQGF.P3_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297171_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-223 1371,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da1a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-188 1371,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="generator" ObjectIDND0="g_20e9430@0" ObjectIDND1="46096@x" ObjectIDZND0="46555@0" Pin0InfoVect0LinkObjId="SM-CX_LJQGF.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20e9430_0" Pin1InfoVect1LinkObjId="SW-297171_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-188 1371,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da1ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1568,-188 1537,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="generator" ObjectIDND0="g_20ea1e0@0" ObjectIDZND0="46099@x" ObjectIDZND1="46556@x" Pin0InfoVect0LinkObjId="SW-297176_0" Pin0InfoVect1LinkObjId="SM-CX_LJQGF.P4_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20ea1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1568,-188 1537,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da2790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-224 1537,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="46099@0" ObjectIDZND0="g_20ea1e0@0" ObjectIDZND1="46556@x" Pin0InfoVect0LinkObjId="g_20ea1e0_0" Pin0InfoVect1LinkObjId="SM-CX_LJQGF.P4_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-224 1537,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da29f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-188 1537,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="generator" ObjectIDND0="g_20ea1e0@0" ObjectIDND1="46099@x" ObjectIDZND0="46556@0" Pin0InfoVect0LinkObjId="SM-CX_LJQGF.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20ea1e0_0" Pin1InfoVect1LinkObjId="SW-297176_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-188 1537,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f1e250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-277 458,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46089@0" ObjectIDZND0="46088@1" Pin0InfoVect0LinkObjId="SW-297157_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-277 458,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d98050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="583,-280 583,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46107@0" ObjectIDZND0="46106@1" Pin0InfoVect0LinkObjId="SW-297187_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="583,-280 583,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d9aaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="727,-305 727,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46103@0" ObjectIDZND0="46102@1" Pin0InfoVect0LinkObjId="SW-297181_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="727,-305 727,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d9d610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-299 904,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46086@0" ObjectIDZND0="46085@1" Pin0InfoVect0LinkObjId="SW-297152_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-299 904,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d9d870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1077,-289 1077,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46092@0" ObjectIDZND0="46091@1" Pin0InfoVect0LinkObjId="SW-297162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297163_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1077,-289 1077,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f486f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1220,-292 1220,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46111@0" ObjectIDZND0="46094@1" Pin0InfoVect0LinkObjId="SW-297167_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297218_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1220,-292 1220,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f4b7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-292 1371,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46097@0" ObjectIDZND0="46096@1" Pin0InfoVect0LinkObjId="SW-297171_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297172_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-292 1371,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f4e8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1538,-255 1538,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46099@1" ObjectIDZND0="46100@0" Pin0InfoVect0LinkObjId="SW-297177_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297176_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1538,-255 1538,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eb57d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="325,-289 325,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="325,-289 325,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eb5a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="209,-289 209,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="209,-289 209,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f100d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-939 820,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="currentTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1acb1e0@0" ObjectIDND1="g_1ca4630@0" ObjectIDND2="46080@x" ObjectIDZND0="46133@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1acb1e0_0" Pin1InfoVect1LinkObjId="g_1ca4630_0" Pin1InfoVect2LinkObjId="SW-297139_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-939 820,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f10a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-939 820,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1acb1e0@0" ObjectIDZND0="g_1ca4630@0" ObjectIDZND1="46080@x" ObjectIDZND2="46078@x" Pin0InfoVect0LinkObjId="g_1ca4630_0" Pin0InfoVect1LinkObjId="SW-297139_0" Pin0InfoVect2LinkObjId="SW-297137_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acb1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="841,-939 820,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f10c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-939 820,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1acb1e0@0" ObjectIDND1="46133@1" ObjectIDZND0="g_1ca4630@0" ObjectIDZND1="46080@x" ObjectIDZND2="46078@x" Pin0InfoVect0LinkObjId="g_1ca4630_0" Pin0InfoVect1LinkObjId="SW-297139_0" Pin0InfoVect2LinkObjId="SW-297137_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1acb1e0_0" Pin1InfoVect1LinkObjId="g_1f100d0_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="820,-939 820,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1e0c8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="820,-718 820,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46077@1" ObjectIDZND0="47822@0" Pin0InfoVect0LinkObjId="g_1f18f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297136_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="820,-718 820,-739 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="304" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="820" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="904" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="583" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="727" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="457" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="1076" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="1219" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="1371" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="1537" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="209" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46121" cx="325" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47822" cx="820" cy="-739" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47822" cx="820" cy="-739" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-297128" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 -849.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46072" ObjectName="DYN-CX_LJQGF"/>
     <cge:Meas_Ref ObjectId="297128"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,374)">联系方式：0878-6660299</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cc3650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -553.000000) translate(0,12)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ab8520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 178.000000 -379.000000) translate(0,15)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a13290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a13290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a13290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a13290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a13290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a13290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a13290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1ac1e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -140.000000 -929.500000) translate(0,16)">鲁家箐光伏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1a04ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 8.000000) translate(0,13)">SCB11-315/0.4GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1a04ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 8.000000) translate(0,29)">35±2×2.5%kV D,yn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eeaca0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1496.000000 -436.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d337e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 755.000000 -687.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d33cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 842.000000 -911.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d33f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 828.000000 -778.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d34150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 749.000000 -579.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f50f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 828.000000 -396.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f51190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -460.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f513d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 311.000000 -441.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c39a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 326.000000 -410.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac8250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -703.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac8250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -703.000000) translate(0,27)">SZ18-110000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac8250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -703.000000) translate(0,42)">110/110/37MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac8250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -703.000000) translate(0,57)">115+8×1.25%/3710.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac8250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -703.000000) translate(0,72)">Ud%=12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c8b890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 676.000000 -27.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b76450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 716.500000 -1.000000) translate(0,15)">26MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f1480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.500000 -13.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac9520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 778.000000 -85.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac9a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 735.000000 -264.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac9c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 590.000000 -235.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac6b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -130.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a4ec70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 191.000000 -64.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a4f340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 442.000000 -59.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_166e760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 -235.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15db120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 886.000000 -72.000000) translate(0,15)">预留储能回路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a4a390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 -65.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a4a390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 -65.000000) translate(0,33)">1~6号升压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a4a390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 -65.000000) translate(0,51)">（6*3.15MW）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c525d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -64.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c525d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -64.000000) translate(0,33)">7~15号升压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c525d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -64.000000) translate(0,51)">（9*3.15MW）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1323.000000 -61.000000) translate(0,15)">3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1323.000000 -61.000000) translate(0,33)">16~21号升压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1323.000000 -61.000000) translate(0,51)">（6*3.15MW）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0dfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1486.000000 -60.000000) translate(0,15)">4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0dfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1486.000000 -60.000000) translate(0,33)">22~27号升压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a0dfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1486.000000 -60.000000) translate(0,51)">（6*3.15MW）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0e220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 -245.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1f15f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1560.000000 -539.000000) translate(0,13)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1f15f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1560.000000 -539.000000) translate(0,29)">S11-315/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1f15f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1560.000000 -539.000000) translate(0,45)">10.5+2×2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1f15f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1560.000000 -539.000000) translate(0,61)">D,yn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f17300" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1590.000000 -744.000000) translate(0,15)">鲁家箐光伏支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ccd470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 846.000000 -535.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ccfae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 307.000000 -62.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cd1610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 913.000000 -251.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cd1b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -244.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cd1d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1380.000000 -244.000000) translate(0,12)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cd20f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -245.000000) translate(0,12)">368</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cd1f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -882.000000) translate(0,12)">A011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cd26a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1487.000000 -823.000000) translate(0,12)">A01</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cd2890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -960.000000) translate(0,12)">N75号杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cd4610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -269.000000) translate(0,12)">3687</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f01890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -324.000000 -554.000000) translate(0,15)">AGC全厂总有功实时值（MW）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f034b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -457.000000) translate(0,15)">调度AVC控制目标电压实时值（kV）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f1cf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -321.000000 -512.000000) translate(0,15)">AGC有功控制模式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f1d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 -1014.000000) translate(0,16)">指鲁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ebb8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 474.000000 -300.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ebbf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 599.000000 -303.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ebc140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 743.000000 -328.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ebc380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 920.000000 -322.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ebc5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1093.000000 -312.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ebc800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1236.000000 -315.000000) translate(0,12)">3661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ebca40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 -315.000000) translate(0,12)">3671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ebcc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1554.000000 -315.000000) translate(0,12)">3681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 835.000000 -843.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 835.000000 -713.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f13470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -619.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1e0bde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 0.000000 -802.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="730,-130 773,-130 773,-102 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="726,-185 721,-195 731,-195 726,-185 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="726,-174 721,-164 731,-164 726,-174 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="582,-159 577,-169 587,-169 582,-159 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="582,-148 577,-138 587,-138 582,-148 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-297190">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -411.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46109" ObjectName="SW-CX_LJQGF.CX_LJQGF_3901SW"/>
     <cge:Meas_Ref ObjectId="297190"/>
    <cge:TPSR_Ref TObjectID="46109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297149">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 -366.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46084" ObjectName="SW-CX_LJQGF.CX_LJQGF_3016SW"/>
     <cge:Meas_Ref ObjectId="297149"/>
    <cge:TPSR_Ref TObjectID="46084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297140">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 733.000000 -546.333333)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46081" ObjectName="SW-CX_LJQGF.CX_LJQGF_1010SW"/>
     <cge:Meas_Ref ObjectId="297140"/>
    <cge:TPSR_Ref TObjectID="46081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297138">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 758.000000 -660.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46079" ObjectName="SW-CX_LJQGF.CX_LJQGF_10117SW"/>
     <cge:Meas_Ref ObjectId="297138"/>
    <cge:TPSR_Ref TObjectID="46079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297139">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 828.000000 -872.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46080" ObjectName="SW-CX_LJQGF.CX_LJQGF_10167SW"/>
     <cge:Meas_Ref ObjectId="297139"/>
    <cge:TPSR_Ref TObjectID="46080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297191">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 323.000000 -379.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46110" ObjectName="SW-CX_LJQGF.CX_LJQGF_39010SW"/>
     <cge:Meas_Ref ObjectId="297191"/>
    <cge:TPSR_Ref TObjectID="46110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297147">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 896.000000 -500.333333)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46082" ObjectName="SW-CX_LJQGF.CX_LJQGF_3010SW"/>
     <cge:Meas_Ref ObjectId="297147"/>
    <cge:TPSR_Ref TObjectID="46082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297184">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 749.000000 -90.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46105" ObjectName="SW-CX_LJQGF.CX_LJQGF_36367SW"/>
     <cge:Meas_Ref ObjectId="297184"/>
    <cge:TPSR_Ref TObjectID="46105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297183">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46104" ObjectName="SW-CX_LJQGF.CX_LJQGF_3636SW"/>
     <cge:Meas_Ref ObjectId="297183"/>
    <cge:TPSR_Ref TObjectID="46104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1522.000000 -848.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297158">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.000000 -272.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46089" ObjectName="SW-CX_LJQGF.CX_LJQGF_3611SW"/>
     <cge:Meas_Ref ObjectId="297158"/>
    <cge:TPSR_Ref TObjectID="46089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297188">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.000000 -275.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46107" ObjectName="SW-CX_LJQGF.CX_LJQGF_3621SW"/>
     <cge:Meas_Ref ObjectId="297188"/>
    <cge:TPSR_Ref TObjectID="46107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297182">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.000000 -300.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46103" ObjectName="SW-CX_LJQGF.CX_LJQGF_3631SW"/>
     <cge:Meas_Ref ObjectId="297182"/>
    <cge:TPSR_Ref TObjectID="46103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297153">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 890.000000 -294.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46086" ObjectName="SW-CX_LJQGF.CX_LJQGF_3641SW"/>
     <cge:Meas_Ref ObjectId="297153"/>
    <cge:TPSR_Ref TObjectID="46086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297163">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -284.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46092" ObjectName="SW-CX_LJQGF.CX_LJQGF_3651SW"/>
     <cge:Meas_Ref ObjectId="297163"/>
    <cge:TPSR_Ref TObjectID="46092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297218">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1206.000000 -287.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46111" ObjectName="SW-CX_LJQGF.CX_LJQGF_3661SW"/>
     <cge:Meas_Ref ObjectId="297218"/>
    <cge:TPSR_Ref TObjectID="46111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297172">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 -287.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46097" ObjectName="SW-CX_LJQGF.CX_LJQGF_3671SW"/>
     <cge:Meas_Ref ObjectId="297172"/>
    <cge:TPSR_Ref TObjectID="46097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297177">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1524.000000 -287.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46100" ObjectName="SW-CX_LJQGF.CX_LJQGF_3681SW"/>
     <cge:Meas_Ref ObjectId="297177"/>
    <cge:TPSR_Ref TObjectID="46100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 195.000000 -284.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.000000 -284.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297137">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.000000 -820.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46078" ObjectName="SW-CX_LJQGF.CX_LJQGF_1016SW"/>
     <cge:Meas_Ref ObjectId="297137"/>
    <cge:TPSR_Ref TObjectID="46078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297136">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.000000 -681.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46077" ObjectName="SW-CX_LJQGF.CX_LJQGF_1011SW"/>
     <cge:Meas_Ref ObjectId="297136"/>
    <cge:TPSR_Ref TObjectID="46077"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJQGF.CX_LJQGF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-354 1612,-354 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46121" ObjectName="BS-CX_LJQGF.CX_LJQGF_3IM"/>
    <cge:TPSR_Ref TObjectID="46121"/></metadata>
   <polyline fill="none" opacity="0" points="171,-354 1612,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJQGF.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="802,-739 844,-739 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47822" ObjectName="BS-CX_LJQGF.XM"/>
    <cge:TPSR_Ref TObjectID="47822"/></metadata>
   <polyline fill="none" opacity="0" points="802,-739 844,-739 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c983c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -482.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e4aa30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1526.000000 -690.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ed180">
    <use class="BV-35KV" transform="matrix(-0.996485 -0.000000 0.000000 -1.000000 877.782051 -478.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c55be0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 705.000000 -530.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acb1e0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 -931.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1357380">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 259.000000 -465.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1efbe90">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 924.000000 -484.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22817c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -38.000000)" xlink:href="#lightningRod:shape177"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20f1db0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 632.000000 -138.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e76a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 488.000000 -124.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19cf410">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1535.000000 -592.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cd51d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 240.000000 -127.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cd5da0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 352.000000 -132.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dafb10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 758.000000 -150.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1db1870">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 938.000000 -131.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e78d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1112.000000 -130.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e8680">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1256.000000 -131.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e9430">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1403.000000 -131.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ea1e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1575.000000 -131.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-297257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -475.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-297258" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -475.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297258" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-297259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -475.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-297263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -475.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-297260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -475.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-297264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -475.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -780.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -780.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -780.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -451.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -451.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -451.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 61.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46088"/>
     <cge:Term_Ref ObjectID="29304"/>
    <cge:TPSR_Ref TObjectID="46088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297281" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 61.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46088"/>
     <cge:Term_Ref ObjectID="29304"/>
    <cge:TPSR_Ref TObjectID="46088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 61.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46088"/>
     <cge:Term_Ref ObjectID="29304"/>
    <cge:TPSR_Ref TObjectID="46088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297324" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 561.000000 62.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297324" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46106"/>
     <cge:Term_Ref ObjectID="29938"/>
    <cge:TPSR_Ref TObjectID="46106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297325" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 561.000000 62.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46106"/>
     <cge:Term_Ref ObjectID="29938"/>
    <cge:TPSR_Ref TObjectID="46106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297323" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 561.000000 62.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297323" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46106"/>
     <cge:Term_Ref ObjectID="29938"/>
    <cge:TPSR_Ref TObjectID="46106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 62.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46085"/>
     <cge:Term_Ref ObjectID="29298"/>
    <cge:TPSR_Ref TObjectID="46085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 62.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46085"/>
     <cge:Term_Ref ObjectID="29298"/>
    <cge:TPSR_Ref TObjectID="46085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 62.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46085"/>
     <cge:Term_Ref ObjectID="29298"/>
    <cge:TPSR_Ref TObjectID="46085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297289" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1058.000000 62.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297289" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46091"/>
     <cge:Term_Ref ObjectID="29419"/>
    <cge:TPSR_Ref TObjectID="46091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297290" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1058.000000 62.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297290" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46091"/>
     <cge:Term_Ref ObjectID="29419"/>
    <cge:TPSR_Ref TObjectID="46091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297286" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1058.000000 62.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46091"/>
     <cge:Term_Ref ObjectID="29419"/>
    <cge:TPSR_Ref TObjectID="46091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297298" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 62.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46094"/>
     <cge:Term_Ref ObjectID="29425"/>
    <cge:TPSR_Ref TObjectID="46094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 62.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46094"/>
     <cge:Term_Ref ObjectID="29425"/>
    <cge:TPSR_Ref TObjectID="46094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 62.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46094"/>
     <cge:Term_Ref ObjectID="29425"/>
    <cge:TPSR_Ref TObjectID="46094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 62.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46096"/>
     <cge:Term_Ref ObjectID="29454"/>
    <cge:TPSR_Ref TObjectID="46096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297308" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 62.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46096"/>
     <cge:Term_Ref ObjectID="29454"/>
    <cge:TPSR_Ref TObjectID="46096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 62.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46096"/>
     <cge:Term_Ref ObjectID="29454"/>
    <cge:TPSR_Ref TObjectID="46096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-297316" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1533.000000 62.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46099"/>
     <cge:Term_Ref ObjectID="29662"/>
    <cge:TPSR_Ref TObjectID="46099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1533.000000 62.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46099"/>
     <cge:Term_Ref ObjectID="29662"/>
    <cge:TPSR_Ref TObjectID="46099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297313" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1533.000000 62.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297313" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46099"/>
     <cge:Term_Ref ObjectID="29662"/>
    <cge:TPSR_Ref TObjectID="46099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-297322" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 77.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46102"/>
     <cge:Term_Ref ObjectID="29930"/>
    <cge:TPSR_Ref TObjectID="46102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-297319" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 77.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46102"/>
     <cge:Term_Ref ObjectID="29930"/>
    <cge:TPSR_Ref TObjectID="46102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="0" MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="OT4  " rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -51.000000 -553.000000) translate(0,12)">OT4   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="0" MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="OT4  " rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -52.000000 -456.000000) translate(0,12)">OT4   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="0" MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="OT4  " rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -87.000000 -512.000000) translate(0,12)">OT4   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="-167" y="-940"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-216" y="-957"/></g>
   <g href="110kV鲁家箐光伏电站AVC1.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-27" y="-813"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da2d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 56.000000 419.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da3270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 72.000000 404.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da34b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 64.000000 448.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da36f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 64.000000 463.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da3930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 64.000000 477.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da3b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 70.000000 433.666667) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da3f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.000000 776.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da4250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 761.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da4490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.000000 746.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da51b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 451.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da5470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 436.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da56b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 913.000000 421.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da5ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 363.000000 -63.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efda30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 -78.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efdc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 377.000000 -93.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f11840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 993.000000 592.666667) translate(0,12)">油温1(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f11ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.000000 562.000000) translate(0,12)">绕组温度(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f12320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1000.000000 608.000000) translate(0,12)">档位(档)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f125a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 992.000000 577.333333) translate(0,12)">油温2(℃)：</text>
   <metadata/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1d900b0">
    <use class="BV-35KV" transform="matrix(1.641026 -0.000000 0.000000 -1.696970 273.000000 -533.000000)" xlink:href="#voltageTransformer:shape98"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -179.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-297254" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -591.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297254" ObjectName="CX_LJQGF:CX_LJQGF_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-297256" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1095.000000 -559.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297256" ObjectName="CX_LJQGF:CX_LJQGF_1T_Tmp3"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-297255" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -576.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297255" ObjectName="CX_LJQGF:CX_LJQGF_1T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-297253" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -608.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="297253" ObjectName="CX_LJQGF:CX_LJQGF_1T_Tp"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="1556" y="-755"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="1555" y="-898"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="-167" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="-167" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-216" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-216" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-27,-813 -30,-816 -30,-762 -27,-765 -27,-813" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-27,-813 -30,-816 119,-816 116,-813 -27,-813" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="-27,-765 -30,-762 119,-762 116,-765 -27,-765" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="116,-813 119,-816 119,-762 116,-765 116,-813" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="-27" y="-813"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-27" y="-813"/>
    </a>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -21.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -21.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.000000 -455.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.000000 -455.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LJQGF.1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38069"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 -556.000000)" xlink:href="#transformer2:shape99_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 -556.000000)" xlink:href="#transformer2:shape99_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="46580" ObjectName="TF-CX_LJQGF.1T"/>
    <cge:TPSR_Ref TObjectID="46580"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LJQGF"/>
</svg>