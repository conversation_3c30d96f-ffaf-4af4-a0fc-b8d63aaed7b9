<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-332" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -1263 2442 1660">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape204">
    <rect fill="none" height="18" stroke="rgb(60,120,255)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape205">
    <rect fill="rgb(0,255,0)" fillStyle="1" height="18" stroke="rgb(60,120,255)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape206">
    <rect fill="none" height="16" stroke="rgb(60,120,255)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape207">
    <rect fill="none" height="16" stroke="rgb(60,120,255)" stroke-width="1" width="8" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape20">
    <rect fill="rgb(0,255,0)" fillStyle="1" height="18" stroke="rgb(0,255,0)" stroke-width="1" width="32" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape21">
    <rect fill="rgb(0,255,0)" fillStyle="1" height="18" stroke="rgb(255,0,0)" stroke-width="1" width="32" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape22">
    <rect fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="33" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape23">
    <rect fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="33" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape189">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="13" y1="21" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="13,64 38,64 38,35 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="13" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="32" x2="44" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="41" x2="35" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="39" x2="37" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="0"/>
    <circle cx="13" cy="66" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="80" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="84" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="88" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="60" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="64" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="68" y2="64"/>
    <ellipse cx="13" cy="82" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="lightningRod:shape147">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27195" x1="7" x2="7" y1="6" y2="15"/>
    <rect height="25" stroke-width="1" width="12" x="1" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="48" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="reactance:shape3">
    <polyline arcFlag="1" points="13,39 11,39 9,38 8,38 6,37 5,36 3,35 2,33 1,31 1,30 0,28 0,26 0,24 1,22 1,21 2,19 3,18 5,16 6,15 8,14 9,14 11,13 13,13 15,13 17,14 18,14 20,15 21,16 23,18 24,19 25,21 25,22 26,24 26,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="14" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
   </symbol>
   <symbol id="switch2:shape27_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape17_0">
    <circle cx="42" cy="31" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="30" y2="101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="24" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="15" y2="24"/>
   </symbol>
   <symbol id="transformer:shape17_1">
    <ellipse cx="42" cy="62" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="42" x2="35" y1="72" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="42" y1="80" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="42" x2="42" y1="63" y2="72"/>
   </symbol>
   <symbol id="transformer:shape17-2">
    <ellipse cx="71" cy="47" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="89" x2="74" y1="48" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="74" y1="57" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="89" x2="74" y1="48" y2="40"/>
   </symbol>
   <symbol id="voltageTransformer:shape69">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="35" x2="35" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="24" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="25" x2="25" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="18" x2="18" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="42" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="42" x2="42" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="28" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="4" x2="4" y1="26" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="19" y1="17" y2="15"/>
    <circle cx="29" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="28" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="29" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="28" x2="26" y1="6" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="30" x2="32" y1="6" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="32" x2="26" y1="3" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="24" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="19" y1="6" y2="4"/>
    <circle cx="19" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="18" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape146">
    <circle cx="23" cy="32" r="7.5" stroke-width="0.804311"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="17" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="25" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="3" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="47" y2="37"/>
    <circle cx="34" cy="27" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="20" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="18" y2="21"/>
    <circle cx="33" cy="14" r="7.5" stroke-width="0.804311"/>
    <circle cx="44" cy="20" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="32" x2="29" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="35" x2="32" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="47" x2="43" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="43" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="43" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="21" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="29" y2="32"/>
    <circle cx="23" cy="21" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_360bb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2df4890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_360db00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_360e6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_360f8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3610400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3610c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3611720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f74e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f74e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3615270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3615270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3616f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3616f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3617e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3619a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_361a710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_361b4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_361bc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_361d4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_361e040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_361e8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_361f090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3620170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3620af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36215e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3621fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3623610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3624040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3625240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3625eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_362c3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_362cec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3627490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3628a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1670" width="2452" x="-335" y="-1268"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.05263" x1="1163" x2="1157" y1="-1181" y2="-1181"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.125" x1="1161" x2="1159" y1="-1183" y2="-1183"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.05263" x1="1164" x2="1156" y1="-1179" y2="-1179"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1181" x2="1181" y1="-1155" y2="-1155"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1171" x2="1171" y1="-1155" y2="-1155"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="759" x2="789" y1="-395" y2="-395"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="789" x2="789" y1="-395" y2="-439"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1495" x2="1525" y1="-395" y2="-395"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1525" x2="1525" y1="-395" y2="-439"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1024" x2="1024" y1="-403" y2="-403"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-307420">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1235.000000 -1016.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47645" ObjectName="SW-CX_QXS.CX_QXS_2916SW"/>
     <cge:Meas_Ref ObjectId="307420"/>
    <cge:TPSR_Ref TObjectID="47645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307419">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1235.000000 -861.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47644" ObjectName="SW-CX_QXS.CX_QXS_2911SW"/>
     <cge:Meas_Ref ObjectId="307419"/>
    <cge:TPSR_Ref TObjectID="47644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307421">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 -921.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47646" ObjectName="SW-CX_QXS.CX_QXS_29117SW"/>
     <cge:Meas_Ref ObjectId="307421"/>
    <cge:TPSR_Ref TObjectID="47646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307422">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1173.000000 -996.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47647" ObjectName="SW-CX_QXS.CX_QXS_29160SW"/>
     <cge:Meas_Ref ObjectId="307422"/>
    <cge:TPSR_Ref TObjectID="47647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307423">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1265.000000 -1081.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47648" ObjectName="SW-CX_QXS.CX_QXS_29167SW"/>
     <cge:Meas_Ref ObjectId="307423"/>
    <cge:TPSR_Ref TObjectID="47648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307431">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1492.000000 -912.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47649" ObjectName="SW-CX_QXS.CX_QXS_2901SW"/>
     <cge:Meas_Ref ObjectId="307431"/>
    <cge:TPSR_Ref TObjectID="47649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307433">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1517.000000 -991.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47651" ObjectName="SW-CX_QXS.CX_QXS_29017SW"/>
     <cge:Meas_Ref ObjectId="307433"/>
    <cge:TPSR_Ref TObjectID="47651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307432">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1523.000000 -882.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47650" ObjectName="SW-CX_QXS.CX_QXS_29010SW"/>
     <cge:Meas_Ref ObjectId="307432"/>
    <cge:TPSR_Ref TObjectID="47650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307441">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -561.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47659" ObjectName="SW-CX_QXS.CX_QXS_20167SW"/>
     <cge:Meas_Ref ObjectId="307441"/>
    <cge:TPSR_Ref TObjectID="47659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307439">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1120.000000 -741.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47657" ObjectName="SW-CX_QXS.CX_QXS_20117SW"/>
     <cge:Meas_Ref ObjectId="307439"/>
    <cge:TPSR_Ref TObjectID="47657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307437">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 -782.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47655" ObjectName="SW-CX_QXS.CX_QXS_2011SW"/>
     <cge:Meas_Ref ObjectId="307437"/>
    <cge:TPSR_Ref TObjectID="47655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307438">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 -586.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47656" ObjectName="SW-CX_QXS.CX_QXS_2016SW"/>
     <cge:Meas_Ref ObjectId="307438"/>
    <cge:TPSR_Ref TObjectID="47656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307440">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.000000 -647.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47658" ObjectName="SW-CX_QXS.CX_QXS_20160SW"/>
     <cge:Meas_Ref ObjectId="307440"/>
    <cge:TPSR_Ref TObjectID="47658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 950.000000 -195.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47664" ObjectName="SW-CX_QXS.CX_QXS_301XC1"/>
     <cge:Meas_Ref ObjectId="307445"/>
    <cge:TPSR_Ref TObjectID="47664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307445">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 950.000000 -112.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47663" ObjectName="SW-CX_QXS.CX_QXS_301XC"/>
     <cge:Meas_Ref ObjectId="307445"/>
    <cge:TPSR_Ref TObjectID="47663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.000000 -109.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47666" ObjectName="SW-CX_QXS.CX_QXS_302XC"/>
     <cge:Meas_Ref ObjectId="307447"/>
    <cge:TPSR_Ref TObjectID="47666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.000000 -197.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47667" ObjectName="SW-CX_QXS.CX_QXS_302XC1"/>
     <cge:Meas_Ref ObjectId="307447"/>
    <cge:TPSR_Ref TObjectID="47667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307456">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 144.000000 11.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47673" ObjectName="SW-CX_QXS.CX_QXS_371XC"/>
     <cge:Meas_Ref ObjectId="307456"/>
    <cge:TPSR_Ref TObjectID="47673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307456">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 144.000000 91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47674" ObjectName="SW-CX_QXS.CX_QXS_371XC1"/>
     <cge:Meas_Ref ObjectId="307456"/>
    <cge:TPSR_Ref TObjectID="47674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307457">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 180.000000 240.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47675" ObjectName="SW-CX_QXS.CX_QXS_37167SW"/>
     <cge:Meas_Ref ObjectId="307457"/>
    <cge:TPSR_Ref TObjectID="47675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307453">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.000000 9.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47668" ObjectName="SW-CX_QXS.CX_QXS_3901XC"/>
     <cge:Meas_Ref ObjectId="307453"/>
    <cge:TPSR_Ref TObjectID="47668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307453">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.000000 89.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47669" ObjectName="SW-CX_QXS.CX_QXS_3901XC1"/>
     <cge:Meas_Ref ObjectId="307453"/>
    <cge:TPSR_Ref TObjectID="47669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307461">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 10.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47677" ObjectName="SW-CX_QXS.CX_QXS_375XC"/>
     <cge:Meas_Ref ObjectId="307461"/>
    <cge:TPSR_Ref TObjectID="47677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307461">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 90.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47678" ObjectName="SW-CX_QXS.CX_QXS_375XC1"/>
     <cge:Meas_Ref ObjectId="307461"/>
    <cge:TPSR_Ref TObjectID="47678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307462">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 468.000000 238.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47679" ObjectName="SW-CX_QXS.CX_QXS_37567SW"/>
     <cge:Meas_Ref ObjectId="307462"/>
    <cge:TPSR_Ref TObjectID="47679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307481">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 932.000000 13.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47693" ObjectName="SW-CX_QXS.CX_QXS_385XC"/>
     <cge:Meas_Ref ObjectId="307481"/>
    <cge:TPSR_Ref TObjectID="47693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307481">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 932.000000 93.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47694" ObjectName="SW-CX_QXS.CX_QXS_385XC1"/>
     <cge:Meas_Ref ObjectId="307481"/>
    <cge:TPSR_Ref TObjectID="47694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307482">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.000000 241.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47695" ObjectName="SW-CX_QXS.CX_QXS_38567SW"/>
     <cge:Meas_Ref ObjectId="307482"/>
    <cge:TPSR_Ref TObjectID="47695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307476">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.000000 12.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47689" ObjectName="SW-CX_QXS.CX_QXS_383XC"/>
     <cge:Meas_Ref ObjectId="307476"/>
    <cge:TPSR_Ref TObjectID="47689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307476">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.000000 92.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47690" ObjectName="SW-CX_QXS.CX_QXS_383XC1"/>
     <cge:Meas_Ref ObjectId="307476"/>
    <cge:TPSR_Ref TObjectID="47690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307477">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 240.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47691" ObjectName="SW-CX_QXS.CX_QXS_38367SW"/>
     <cge:Meas_Ref ObjectId="307477"/>
    <cge:TPSR_Ref TObjectID="47691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307471">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 685.000000 10.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47685" ObjectName="SW-CX_QXS.CX_QXS_379XC"/>
     <cge:Meas_Ref ObjectId="307471"/>
    <cge:TPSR_Ref TObjectID="47685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307471">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 685.000000 90.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47686" ObjectName="SW-CX_QXS.CX_QXS_379XC1"/>
     <cge:Meas_Ref ObjectId="307471"/>
    <cge:TPSR_Ref TObjectID="47686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307472">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 721.000000 238.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47687" ObjectName="SW-CX_QXS.CX_QXS_37967SW"/>
     <cge:Meas_Ref ObjectId="307472"/>
    <cge:TPSR_Ref TObjectID="47687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307466">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 13.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47681" ObjectName="SW-CX_QXS.CX_QXS_377XC"/>
     <cge:Meas_Ref ObjectId="307466"/>
    <cge:TPSR_Ref TObjectID="47681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307466">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 93.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47682" ObjectName="SW-CX_QXS.CX_QXS_377XC1"/>
     <cge:Meas_Ref ObjectId="307466"/>
    <cge:TPSR_Ref TObjectID="47682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307467">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 585.000000 241.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47683" ObjectName="SW-CX_QXS.CX_QXS_37767SW"/>
     <cge:Meas_Ref ObjectId="307467"/>
    <cge:TPSR_Ref TObjectID="47683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307511">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 209.000000 -171.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47718" ObjectName="SW-CX_QXS.CX_QXS_373XC1"/>
     <cge:Meas_Ref ObjectId="307511"/>
    <cge:TPSR_Ref TObjectID="47718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307511">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 209.000000 -91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47717" ObjectName="SW-CX_QXS.CX_QXS_373XC"/>
     <cge:Meas_Ref ObjectId="307511"/>
    <cge:TPSR_Ref TObjectID="47717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307512">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 251.000000 -278.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47719" ObjectName="SW-CX_QXS.CX_QXS_37367SW"/>
     <cge:Meas_Ref ObjectId="307512"/>
    <cge:TPSR_Ref TObjectID="47719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.000000 -171.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47722" ObjectName="SW-CX_QXS.CX_QXS_381XC1"/>
     <cge:Meas_Ref ObjectId="307516"/>
    <cge:TPSR_Ref TObjectID="47722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.000000 -91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47721" ObjectName="SW-CX_QXS.CX_QXS_381XC"/>
     <cge:Meas_Ref ObjectId="307516"/>
    <cge:TPSR_Ref TObjectID="47721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307519">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 783.000000 -273.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47725" ObjectName="SW-CX_QXS.CX_QXS_38160SW"/>
     <cge:Meas_Ref ObjectId="307519"/>
    <cge:TPSR_Ref TObjectID="47725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307517">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 763.000000 -372.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47723" ObjectName="SW-CX_QXS.CX_QXS_3816SW"/>
     <cge:Meas_Ref ObjectId="307517"/>
    <cge:TPSR_Ref TObjectID="47723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307520">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 770.000000 -453.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47726" ObjectName="SW-CX_QXS.CX_QXS_38167SW"/>
     <cge:Meas_Ref ObjectId="307520"/>
    <cge:TPSR_Ref TObjectID="47726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307518">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 763.000000 -490.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47724" ObjectName="SW-CX_QXS.CX_QXS_3817SW"/>
     <cge:Meas_Ref ObjectId="307518"/>
    <cge:TPSR_Ref TObjectID="47724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1480.000000 -171.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47729" ObjectName="SW-CX_QXS.CX_QXS_376XC1"/>
     <cge:Meas_Ref ObjectId="307524"/>
    <cge:TPSR_Ref TObjectID="47729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1480.000000 -91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47728" ObjectName="SW-CX_QXS.CX_QXS_376XC"/>
     <cge:Meas_Ref ObjectId="307524"/>
    <cge:TPSR_Ref TObjectID="47728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307527">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1519.000000 -273.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47732" ObjectName="SW-CX_QXS.CX_QXS_37660SW"/>
     <cge:Meas_Ref ObjectId="307527"/>
    <cge:TPSR_Ref TObjectID="47732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307525">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1499.000000 -372.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47730" ObjectName="SW-CX_QXS.CX_QXS_3766SW"/>
     <cge:Meas_Ref ObjectId="307525"/>
    <cge:TPSR_Ref TObjectID="47730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307528">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1506.000000 -453.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47733" ObjectName="SW-CX_QXS.CX_QXS_37667SW"/>
     <cge:Meas_Ref ObjectId="307528"/>
    <cge:TPSR_Ref TObjectID="47733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307526">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1499.000000 -490.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47731" ObjectName="SW-CX_QXS.CX_QXS_3767SW"/>
     <cge:Meas_Ref ObjectId="307526"/>
    <cge:TPSR_Ref TObjectID="47731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307486">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1285.000000 12.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47697" ObjectName="SW-CX_QXS.CX_QXS_372XC"/>
     <cge:Meas_Ref ObjectId="307486"/>
    <cge:TPSR_Ref TObjectID="47697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307486">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1285.000000 92.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47698" ObjectName="SW-CX_QXS.CX_QXS_372XC1"/>
     <cge:Meas_Ref ObjectId="307486"/>
    <cge:TPSR_Ref TObjectID="47698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307487">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1321.000000 240.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47699" ObjectName="SW-CX_QXS.CX_QXS_37267SW"/>
     <cge:Meas_Ref ObjectId="307487"/>
    <cge:TPSR_Ref TObjectID="47699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307491">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 17.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47701" ObjectName="SW-CX_QXS.CX_QXS_374XC"/>
     <cge:Meas_Ref ObjectId="307491"/>
    <cge:TPSR_Ref TObjectID="47701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307491">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 97.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47702" ObjectName="SW-CX_QXS.CX_QXS_374XC1"/>
     <cge:Meas_Ref ObjectId="307491"/>
    <cge:TPSR_Ref TObjectID="47702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307492">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1450.000000 245.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47703" ObjectName="SW-CX_QXS.CX_QXS_37467SW"/>
     <cge:Meas_Ref ObjectId="307492"/>
    <cge:TPSR_Ref TObjectID="47703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307496">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1559.000000 14.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47705" ObjectName="SW-CX_QXS.CX_QXS_378XC"/>
     <cge:Meas_Ref ObjectId="307496"/>
    <cge:TPSR_Ref TObjectID="47705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307496">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1559.000000 94.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47706" ObjectName="SW-CX_QXS.CX_QXS_378XC1"/>
     <cge:Meas_Ref ObjectId="307496"/>
    <cge:TPSR_Ref TObjectID="47706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307497">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1595.000000 242.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47707" ObjectName="SW-CX_QXS.CX_QXS_37867SW"/>
     <cge:Meas_Ref ObjectId="307497"/>
    <cge:TPSR_Ref TObjectID="47707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307501">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1701.000000 16.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47709" ObjectName="SW-CX_QXS.CX_QXS_380XC"/>
     <cge:Meas_Ref ObjectId="307501"/>
    <cge:TPSR_Ref TObjectID="47709"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307501">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1701.000000 96.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47710" ObjectName="SW-CX_QXS.CX_QXS_380XC1"/>
     <cge:Meas_Ref ObjectId="307501"/>
    <cge:TPSR_Ref TObjectID="47710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307502">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1737.000000 244.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47711" ObjectName="SW-CX_QXS.CX_QXS_38067SW"/>
     <cge:Meas_Ref ObjectId="307502"/>
    <cge:TPSR_Ref TObjectID="47711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307506">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1845.000000 15.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47713" ObjectName="SW-CX_QXS.CX_QXS_382XC"/>
     <cge:Meas_Ref ObjectId="307506"/>
    <cge:TPSR_Ref TObjectID="47713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307506">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1845.000000 95.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47714" ObjectName="SW-CX_QXS.CX_QXS_382XC1"/>
     <cge:Meas_Ref ObjectId="307506"/>
    <cge:TPSR_Ref TObjectID="47714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307507">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1881.000000 243.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47715" ObjectName="SW-CX_QXS.CX_QXS_38267SW"/>
     <cge:Meas_Ref ObjectId="307507"/>
    <cge:TPSR_Ref TObjectID="47715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307454">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1990.000000 9.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47670" ObjectName="SW-CX_QXS.CX_QXS_3902XC"/>
     <cge:Meas_Ref ObjectId="307454"/>
    <cge:TPSR_Ref TObjectID="47670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307454">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1990.000000 89.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47671" ObjectName="SW-CX_QXS.CX_QXS_3902XC1"/>
     <cge:Meas_Ref ObjectId="307454"/>
    <cge:TPSR_Ref TObjectID="47671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307442">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1025.901961 -557.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47660" ObjectName="SW-CX_QXS.CX_QXS_2010SW"/>
     <cge:Meas_Ref ObjectId="307442"/>
    <cge:TPSR_Ref TObjectID="47660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307443">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1030.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47661" ObjectName="SW-CX_QXS.CX_QXS_3010SW"/>
     <cge:Meas_Ref ObjectId="307443"/>
    <cge:TPSR_Ref TObjectID="47661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307434">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1733.000000 -925.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47652" ObjectName="SW-CX_QXS.CX_QXS_2121SW"/>
     <cge:Meas_Ref ObjectId="307434"/>
    <cge:TPSR_Ref TObjectID="47652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307435">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1815.000000 -858.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47653" ObjectName="SW-CX_QXS.CX_QXS_21217SW"/>
     <cge:Meas_Ref ObjectId="307435"/>
    <cge:TPSR_Ref TObjectID="47653"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2efdce0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1156.000000 -1163.000000)" xlink:href="#voltageTransformer:shape69"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_317edd0">
    <use class="BV-220KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1536.000000 -1050.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e61200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 282.000000 205.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3190a00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1967.000000 205.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 741.000000 -561.000000)" xlink:href="#reactance:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.000000 -561.000000)" xlink:href="#reactance:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ed5e20">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 -1175.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3616a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1145.000000 -335.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31642a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 105.000000 242.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df0c90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 306.000000 165.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e325a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 272.000000 167.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e25040">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 393.000000 241.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3110840">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 244.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3140860">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 767.000000 243.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3e600">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 241.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e2ac70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 510.000000 244.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df9010">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 176.000000 -281.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ec2ab0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 228.000000 -281.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3040bc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 206.000000 -495.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_316e640">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 708.000000 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3182600">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 763.000000 -269.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34ed2a0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 737.000000 -652.000000)" xlink:href="#lightningRod:shape147"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34f6420">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1444.000000 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34fab60">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1499.000000 -269.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a35d0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1473.000000 -652.000000)" xlink:href="#lightningRod:shape147"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36aed50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1246.000000 243.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bd4a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 248.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35e62c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1520.000000 245.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35f4620">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1662.000000 247.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3602970">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1806.000000 246.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3190030">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1991.000000 53.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3193c20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.000000 167.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3197230">
    <use class="BV-220KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 971.000000 -559.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3197bd0">
    <use class="BV-220KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 994.000000 -556.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -1026.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47643"/>
     <cge:Term_Ref ObjectID="45279"/>
    <cge:TPSR_Ref TObjectID="47643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307182" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -1026.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47643"/>
     <cge:Term_Ref ObjectID="45279"/>
    <cge:TPSR_Ref TObjectID="47643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307178" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -1026.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47643"/>
     <cge:Term_Ref ObjectID="45279"/>
    <cge:TPSR_Ref TObjectID="47643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307202" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -720.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307202" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47654"/>
     <cge:Term_Ref ObjectID="45301"/>
    <cge:TPSR_Ref TObjectID="47654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307203" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -720.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307203" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47654"/>
     <cge:Term_Ref ObjectID="45301"/>
    <cge:TPSR_Ref TObjectID="47654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307193" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -720.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307193" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47654"/>
     <cge:Term_Ref ObjectID="45301"/>
    <cge:TPSR_Ref TObjectID="47654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 -167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47720"/>
     <cge:Term_Ref ObjectID="45433"/>
    <cge:TPSR_Ref TObjectID="47720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 -167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47720"/>
     <cge:Term_Ref ObjectID="45433"/>
    <cge:TPSR_Ref TObjectID="47720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 -167.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47720"/>
     <cge:Term_Ref ObjectID="45433"/>
    <cge:TPSR_Ref TObjectID="47720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307214" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -196.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47662"/>
     <cge:Term_Ref ObjectID="45317"/>
    <cge:TPSR_Ref TObjectID="47662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -196.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47662"/>
     <cge:Term_Ref ObjectID="45317"/>
    <cge:TPSR_Ref TObjectID="47662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307205" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -196.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47662"/>
     <cge:Term_Ref ObjectID="45317"/>
    <cge:TPSR_Ref TObjectID="47662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307226" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -196.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307226" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47665"/>
     <cge:Term_Ref ObjectID="45323"/>
    <cge:TPSR_Ref TObjectID="47665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -196.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47665"/>
     <cge:Term_Ref ObjectID="45323"/>
    <cge:TPSR_Ref TObjectID="47665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307217" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -196.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307217" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47665"/>
     <cge:Term_Ref ObjectID="45323"/>
    <cge:TPSR_Ref TObjectID="47665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 -167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47727"/>
     <cge:Term_Ref ObjectID="45447"/>
    <cge:TPSR_Ref TObjectID="47727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307415" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 -167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307415" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47727"/>
     <cge:Term_Ref ObjectID="45447"/>
    <cge:TPSR_Ref TObjectID="47727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 -167.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47727"/>
     <cge:Term_Ref ObjectID="45447"/>
    <cge:TPSR_Ref TObjectID="47727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307390" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -168.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307390" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47716"/>
     <cge:Term_Ref ObjectID="45425"/>
    <cge:TPSR_Ref TObjectID="47716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -168.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47716"/>
     <cge:Term_Ref ObjectID="45425"/>
    <cge:TPSR_Ref TObjectID="47716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -168.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47716"/>
     <cge:Term_Ref ObjectID="45425"/>
    <cge:TPSR_Ref TObjectID="47716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307258" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 126.000000 346.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307258" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47672"/>
     <cge:Term_Ref ObjectID="45337"/>
    <cge:TPSR_Ref TObjectID="47672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 126.000000 346.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47672"/>
     <cge:Term_Ref ObjectID="45337"/>
    <cge:TPSR_Ref TObjectID="47672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 126.000000 346.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47672"/>
     <cge:Term_Ref ObjectID="45337"/>
    <cge:TPSR_Ref TObjectID="47672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307270" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307270" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47676"/>
     <cge:Term_Ref ObjectID="45345"/>
    <cge:TPSR_Ref TObjectID="47676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47676"/>
     <cge:Term_Ref ObjectID="45345"/>
    <cge:TPSR_Ref TObjectID="47676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47676"/>
     <cge:Term_Ref ObjectID="45345"/>
    <cge:TPSR_Ref TObjectID="47676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307282" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 352.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47680"/>
     <cge:Term_Ref ObjectID="45353"/>
    <cge:TPSR_Ref TObjectID="47680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 352.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47680"/>
     <cge:Term_Ref ObjectID="45353"/>
    <cge:TPSR_Ref TObjectID="47680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 352.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47680"/>
     <cge:Term_Ref ObjectID="45353"/>
    <cge:TPSR_Ref TObjectID="47680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 349.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47684"/>
     <cge:Term_Ref ObjectID="45361"/>
    <cge:TPSR_Ref TObjectID="47684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 349.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47684"/>
     <cge:Term_Ref ObjectID="45361"/>
    <cge:TPSR_Ref TObjectID="47684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307291" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 349.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47684"/>
     <cge:Term_Ref ObjectID="45361"/>
    <cge:TPSR_Ref TObjectID="47684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 348.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47688"/>
     <cge:Term_Ref ObjectID="45369"/>
    <cge:TPSR_Ref TObjectID="47688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 348.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47688"/>
     <cge:Term_Ref ObjectID="45369"/>
    <cge:TPSR_Ref TObjectID="47688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307303" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 348.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47688"/>
     <cge:Term_Ref ObjectID="45369"/>
    <cge:TPSR_Ref TObjectID="47688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307318" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 349.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307318" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47692"/>
     <cge:Term_Ref ObjectID="45377"/>
    <cge:TPSR_Ref TObjectID="47692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307319" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 349.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47692"/>
     <cge:Term_Ref ObjectID="45377"/>
    <cge:TPSR_Ref TObjectID="47692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307315" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 349.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307315" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47692"/>
     <cge:Term_Ref ObjectID="45377"/>
    <cge:TPSR_Ref TObjectID="47692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307330" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 345.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307330" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47696"/>
     <cge:Term_Ref ObjectID="45385"/>
    <cge:TPSR_Ref TObjectID="47696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307331" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 345.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307331" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47696"/>
     <cge:Term_Ref ObjectID="45385"/>
    <cge:TPSR_Ref TObjectID="47696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307327" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 345.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307327" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47696"/>
     <cge:Term_Ref ObjectID="45385"/>
    <cge:TPSR_Ref TObjectID="47696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307342" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 348.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307342" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47700"/>
     <cge:Term_Ref ObjectID="45393"/>
    <cge:TPSR_Ref TObjectID="47700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307343" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 348.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307343" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47700"/>
     <cge:Term_Ref ObjectID="45393"/>
    <cge:TPSR_Ref TObjectID="47700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307339" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 348.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307339" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47700"/>
     <cge:Term_Ref ObjectID="45393"/>
    <cge:TPSR_Ref TObjectID="47700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307354" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 346.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307354" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47704"/>
     <cge:Term_Ref ObjectID="45401"/>
    <cge:TPSR_Ref TObjectID="47704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307355" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 346.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307355" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47704"/>
     <cge:Term_Ref ObjectID="45401"/>
    <cge:TPSR_Ref TObjectID="47704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307351" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1536.000000 346.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307351" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47704"/>
     <cge:Term_Ref ObjectID="45401"/>
    <cge:TPSR_Ref TObjectID="47704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307366" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307366" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47708"/>
     <cge:Term_Ref ObjectID="45409"/>
    <cge:TPSR_Ref TObjectID="47708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307367" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307367" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47708"/>
     <cge:Term_Ref ObjectID="45409"/>
    <cge:TPSR_Ref TObjectID="47708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307363" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307363" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47708"/>
     <cge:Term_Ref ObjectID="45409"/>
    <cge:TPSR_Ref TObjectID="47708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307378" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 349.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307378" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47712"/>
     <cge:Term_Ref ObjectID="45417"/>
    <cge:TPSR_Ref TObjectID="47712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307379" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 349.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307379" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47712"/>
     <cge:Term_Ref ObjectID="45417"/>
    <cge:TPSR_Ref TObjectID="47712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 349.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47712"/>
     <cge:Term_Ref ObjectID="45417"/>
    <cge:TPSR_Ref TObjectID="47712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-307185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -957.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47639"/>
     <cge:Term_Ref ObjectID="45274"/>
    <cge:TPSR_Ref TObjectID="47639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-307186" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -957.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307186" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47639"/>
     <cge:Term_Ref ObjectID="45274"/>
    <cge:TPSR_Ref TObjectID="47639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-307187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -957.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47639"/>
     <cge:Term_Ref ObjectID="45274"/>
    <cge:TPSR_Ref TObjectID="47639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-307191" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -957.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307191" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47639"/>
     <cge:Term_Ref ObjectID="45274"/>
    <cge:TPSR_Ref TObjectID="47639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-307188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -957.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47639"/>
     <cge:Term_Ref ObjectID="45274"/>
    <cge:TPSR_Ref TObjectID="47639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-307192" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -957.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307192" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47639"/>
     <cge:Term_Ref ObjectID="45274"/>
    <cge:TPSR_Ref TObjectID="47639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-307233" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -166.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47640"/>
     <cge:Term_Ref ObjectID="45275"/>
    <cge:TPSR_Ref TObjectID="47640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-307234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -166.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47640"/>
     <cge:Term_Ref ObjectID="45275"/>
    <cge:TPSR_Ref TObjectID="47640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-307235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -166.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47640"/>
     <cge:Term_Ref ObjectID="45275"/>
    <cge:TPSR_Ref TObjectID="47640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-307239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -166.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47640"/>
     <cge:Term_Ref ObjectID="45275"/>
    <cge:TPSR_Ref TObjectID="47640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-307236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -166.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47640"/>
     <cge:Term_Ref ObjectID="45275"/>
    <cge:TPSR_Ref TObjectID="47640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-307240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -166.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47640"/>
     <cge:Term_Ref ObjectID="45275"/>
    <cge:TPSR_Ref TObjectID="47640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-307241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -170.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47641"/>
     <cge:Term_Ref ObjectID="45276"/>
    <cge:TPSR_Ref TObjectID="47641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-307242" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -170.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47641"/>
     <cge:Term_Ref ObjectID="45276"/>
    <cge:TPSR_Ref TObjectID="47641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-307243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -170.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47641"/>
     <cge:Term_Ref ObjectID="45276"/>
    <cge:TPSR_Ref TObjectID="47641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-307247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -170.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47641"/>
     <cge:Term_Ref ObjectID="45276"/>
    <cge:TPSR_Ref TObjectID="47641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-307244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -170.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47641"/>
     <cge:Term_Ref ObjectID="45276"/>
    <cge:TPSR_Ref TObjectID="47641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-307248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2031.000000 -170.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47641"/>
     <cge:Term_Ref ObjectID="45276"/>
    <cge:TPSR_Ref TObjectID="47641"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="-227" y="-940"/></g>
   <g href="cx_索引_接线图_省地共调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-307418">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1235.000000 -944.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47643" ObjectName="SW-CX_QXS.CX_QXS_291BK"/>
     <cge:Meas_Ref ObjectId="307418"/>
    <cge:TPSR_Ref TObjectID="47643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307436">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 -674.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47654" ObjectName="SW-CX_QXS.CX_QXS_201BK"/>
     <cge:Meas_Ref ObjectId="307436"/>
    <cge:TPSR_Ref TObjectID="47654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307444">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 -148.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47662" ObjectName="SW-CX_QXS.CX_QXS_301BK"/>
     <cge:Meas_Ref ObjectId="307444"/>
    <cge:TPSR_Ref TObjectID="47662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307446">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.000000 -146.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47665" ObjectName="SW-CX_QXS.CX_QXS_302BK"/>
     <cge:Meas_Ref ObjectId="307446"/>
    <cge:TPSR_Ref TObjectID="47665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307455">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 145.000000 58.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47672" ObjectName="SW-CX_QXS.CX_QXS_371BK"/>
     <cge:Meas_Ref ObjectId="307455"/>
    <cge:TPSR_Ref TObjectID="47672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307460">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 433.000000 57.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47676" ObjectName="SW-CX_QXS.CX_QXS_375BK"/>
     <cge:Meas_Ref ObjectId="307460"/>
    <cge:TPSR_Ref TObjectID="47676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307480">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.000000 60.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47692" ObjectName="SW-CX_QXS.CX_QXS_385BK"/>
     <cge:Meas_Ref ObjectId="307480"/>
    <cge:TPSR_Ref TObjectID="47692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307475">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 59.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47688" ObjectName="SW-CX_QXS.CX_QXS_383BK"/>
     <cge:Meas_Ref ObjectId="307475"/>
    <cge:TPSR_Ref TObjectID="47688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 686.000000 57.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47684" ObjectName="SW-CX_QXS.CX_QXS_379BK"/>
     <cge:Meas_Ref ObjectId="307470"/>
    <cge:TPSR_Ref TObjectID="47684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307465">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 550.000000 60.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47680" ObjectName="SW-CX_QXS.CX_QXS_377BK"/>
     <cge:Meas_Ref ObjectId="307465"/>
    <cge:TPSR_Ref TObjectID="47680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307510">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 210.000000 -125.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47716" ObjectName="SW-CX_QXS.CX_QXS_373BK"/>
     <cge:Meas_Ref ObjectId="307510"/>
    <cge:TPSR_Ref TObjectID="47716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.000000 -125.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47720" ObjectName="SW-CX_QXS.CX_QXS_381BK"/>
     <cge:Meas_Ref ObjectId="307515"/>
    <cge:TPSR_Ref TObjectID="47720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1481.000000 -125.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47727" ObjectName="SW-CX_QXS.CX_QXS_376BK"/>
     <cge:Meas_Ref ObjectId="307523"/>
    <cge:TPSR_Ref TObjectID="47727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307485">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1286.000000 59.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47696" ObjectName="SW-CX_QXS.CX_QXS_372BK"/>
     <cge:Meas_Ref ObjectId="307485"/>
    <cge:TPSR_Ref TObjectID="47696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307490">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1415.000000 64.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47700" ObjectName="SW-CX_QXS.CX_QXS_374BK"/>
     <cge:Meas_Ref ObjectId="307490"/>
    <cge:TPSR_Ref TObjectID="47700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307495">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1560.000000 61.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47704" ObjectName="SW-CX_QXS.CX_QXS_378BK"/>
     <cge:Meas_Ref ObjectId="307495"/>
    <cge:TPSR_Ref TObjectID="47704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307500">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1702.000000 63.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47708" ObjectName="SW-CX_QXS.CX_QXS_380BK"/>
     <cge:Meas_Ref ObjectId="307500"/>
    <cge:TPSR_Ref TObjectID="47708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307505">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1846.000000 62.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47712" ObjectName="SW-CX_QXS.CX_QXS_382BK"/>
     <cge:Meas_Ref ObjectId="307505"/>
    <cge:TPSR_Ref TObjectID="47712"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_QXS.CX_QXS_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1014,-847 1714,-847 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47639" ObjectName="BS-CX_QXS.CX_QXS_2IM"/>
    <cge:TPSR_Ref TObjectID="47639"/></metadata>
   <polyline fill="none" opacity="0" points="1014,-847 1714,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QXS.CX_QXS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="119,-67 1056,-67 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47640" ObjectName="BS-CX_QXS.CX_QXS_3IM"/>
    <cge:TPSR_Ref TObjectID="47640"/></metadata>
   <polyline fill="none" opacity="0" points="119,-67 1056,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_QXS.CX_QXS_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-67 2092,-67 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47641" ObjectName="BS-CX_QXS.CX_QXS_3IIM"/>
    <cge:TPSR_Ref TObjectID="47641"/></metadata>
   <polyline fill="none" opacity="0" points="1176,-67 2092,-67 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_3122a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1266,-928 1244,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47646@0" ObjectIDZND0="47644@x" ObjectIDZND1="47643@x" Pin0InfoVect0LinkObjId="SW-307419_0" Pin0InfoVect1LinkObjId="SW-307418_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1266,-928 1244,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fe0520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-902 1244,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47644@1" ObjectIDZND0="47646@x" ObjectIDZND1="47643@x" Pin0InfoVect0LinkObjId="SW-307421_0" Pin0InfoVect1LinkObjId="SW-307418_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307419_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-902 1244,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e7bc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-928 1244,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="47646@x" ObjectIDND1="47644@x" ObjectIDZND0="47643@0" Pin0InfoVect0LinkObjId="SW-307418_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307421_0" Pin1InfoVect1LinkObjId="SW-307419_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-928 1244,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e7b4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1003 1244,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47647@0" ObjectIDZND0="47643@x" ObjectIDZND1="47645@x" Pin0InfoVect0LinkObjId="SW-307418_0" Pin0InfoVect1LinkObjId="SW-307420_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1003 1244,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fadf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-979 1244,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47643@1" ObjectIDZND0="47647@x" ObjectIDZND1="47645@x" Pin0InfoVect0LinkObjId="SW-307422_0" Pin0InfoVect1LinkObjId="SW-307420_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307418_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-979 1244,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2edb1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-1003 1244,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="47647@x" ObjectIDND1="47643@x" ObjectIDZND0="47645@1" Pin0InfoVect0LinkObjId="SW-307420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307422_0" Pin1InfoVect1LinkObjId="SW-307418_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-1003 1244,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f94190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-1088 1244,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="47648@0" ObjectIDZND0="47645@x" ObjectIDZND1="g_2efdce0@0" ObjectIDZND2="g_2ed5e20@0" Pin0InfoVect0LinkObjId="SW-307420_0" Pin0InfoVect1LinkObjId="g_2efdce0_0" Pin0InfoVect2LinkObjId="g_2ed5e20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-1088 1244,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3160bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-1057 1244,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="47645@0" ObjectIDZND0="47648@x" ObjectIDZND1="g_2efdce0@0" ObjectIDZND2="g_2ed5e20@0" Pin0InfoVect0LinkObjId="SW-307423_0" Pin0InfoVect1LinkObjId="g_2efdce0_0" Pin0InfoVect2LinkObjId="g_2ed5e20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-1057 1244,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3158eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1265,-1183 1244,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ed5e20@0" ObjectIDZND0="g_2efdce0@0" ObjectIDZND1="47648@x" ObjectIDZND2="47645@x" Pin0InfoVect0LinkObjId="g_2efdce0_0" Pin0InfoVect1LinkObjId="SW-307423_0" Pin0InfoVect2LinkObjId="SW-307420_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ed5e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1265,-1183 1244,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_361c050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-1183 1244,-1233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" ObjectIDND0="g_2ed5e20@0" ObjectIDND1="g_2efdce0@0" ObjectIDND2="47648@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ed5e20_0" Pin1InfoVect1LinkObjId="g_2efdce0_0" Pin1InfoVect2LinkObjId="SW-307423_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-1183 1244,-1233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3624d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1208,-1133 1244,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2efdce0@0" ObjectIDZND0="47648@x" ObjectIDZND1="47645@x" ObjectIDZND2="g_2ed5e20@0" Pin0InfoVect0LinkObjId="SW-307423_0" Pin0InfoVect1LinkObjId="SW-307420_0" Pin0InfoVect2LinkObjId="g_2ed5e20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efdce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1208,-1133 1244,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e06660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-1088 1244,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="47648@x" ObjectIDND1="47645@x" ObjectIDZND0="g_2efdce0@0" ObjectIDZND1="g_2ed5e20@0" Pin0InfoVect0LinkObjId="g_2efdce0_0" Pin0InfoVect1LinkObjId="g_2ed5e20_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307423_0" Pin1InfoVect1LinkObjId="SW-307420_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-1088 1244,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_310b6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-1133 1244,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2efdce0@0" ObjectIDND1="47648@x" ObjectIDND2="47645@x" ObjectIDZND0="g_2ed5e20@0" Pin0InfoVect0LinkObjId="g_2ed5e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2efdce0_0" Pin1InfoVect1LinkObjId="SW-307423_0" Pin1InfoVect2LinkObjId="SW-307420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-1133 1244,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310b350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-1158 1160,-1158 1160,-1179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-1158 1160,-1158 1160,-1179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f41250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-866 1244,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47644@0" ObjectIDZND0="47639@0" Pin0InfoVect0LinkObjId="g_2e23810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-866 1244,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_362d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1522,-998 1501,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="47651@0" ObjectIDZND0="47649@x" ObjectIDZND1="g_317edd0@0" Pin0InfoVect0LinkObjId="SW-307431_0" Pin0InfoVect1LinkObjId="g_317edd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307433_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1522,-998 1501,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2df1f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-953 1501,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="47649@1" ObjectIDZND0="47651@x" ObjectIDZND1="g_317edd0@0" Pin0InfoVect0LinkObjId="SW-307433_0" Pin0InfoVect1LinkObjId="g_317edd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-953 1501,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2df5b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-998 1501,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="47651@x" ObjectIDND1="47649@x" ObjectIDZND0="g_317edd0@0" Pin0InfoVect0LinkObjId="g_317edd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307433_0" Pin1InfoVect1LinkObjId="SW-307431_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-998 1501,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e23810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-889 1501,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="47650@0" ObjectIDZND0="47639@0" ObjectIDZND1="47649@x" Pin0InfoVect0LinkObjId="g_2f41250_0" Pin0InfoVect1LinkObjId="SW-307431_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307432_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-889 1501,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e34160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-847 1501,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47639@0" ObjectIDZND0="47650@x" ObjectIDZND1="47649@x" Pin0InfoVect0LinkObjId="SW-307432_0" Pin0InfoVect1LinkObjId="SW-307431_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f41250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-847 1501,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3101800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-889 1501,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="47650@x" ObjectIDND1="47639@0" ObjectIDZND0="47649@0" Pin0InfoVect0LinkObjId="SW-307431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307432_0" Pin1InfoVect1LinkObjId="g_2f41250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-889 1501,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e1c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1149,-343 1105,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_3616a30@0" ObjectIDZND0="47664@x" ObjectIDZND1="47667@x" ObjectIDZND2="47734@x" Pin0InfoVect0LinkObjId="SW-307445_0" Pin0InfoVect1LinkObjId="SW-307447_0" Pin0InfoVect2LinkObjId="g_2e7a810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3616a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1149,-343 1105,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3614c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-289 1105,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="47664@x" ObjectIDND1="47667@x" ObjectIDZND0="g_3616a30@0" ObjectIDZND1="47734@x" Pin0InfoVect0LinkObjId="g_3616a30_0" Pin0InfoVect1LinkObjId="g_2e7a810_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307445_0" Pin1InfoVect1LinkObjId="SW-307447_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-289 1105,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e7a810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-343 1105,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="transformer" ObjectIDND0="g_3616a30@0" ObjectIDND1="47664@x" ObjectIDND2="47667@x" ObjectIDZND0="47734@0" Pin0InfoVect0LinkObjId="g_3172fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3616a30_0" Pin1InfoVect1LinkObjId="SW-307445_0" Pin1InfoVect2LinkObjId="SW-307447_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-343 1105,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36182d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-654 1105,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47658@0" ObjectIDZND0="47656@x" ObjectIDZND1="47654@x" Pin0InfoVect0LinkObjId="SW-307438_0" Pin0InfoVect1LinkObjId="SW-307436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-654 1105,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e339b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-627 1105,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47656@1" ObjectIDZND0="47658@x" ObjectIDZND1="47654@x" Pin0InfoVect0LinkObjId="SW-307440_0" Pin0InfoVect1LinkObjId="SW-307436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307438_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-627 1105,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e301e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-654 1105,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="47658@x" ObjectIDND1="47656@x" ObjectIDZND0="47654@0" Pin0InfoVect0LinkObjId="SW-307436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307440_0" Pin1InfoVect1LinkObjId="SW-307438_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-654 1105,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e43d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-823 1105,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47655@1" ObjectIDZND0="47639@0" Pin0InfoVect0LinkObjId="g_2f41250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-823 1105,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e43070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-748 1105,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47657@0" ObjectIDZND0="47654@x" ObjectIDZND1="47655@x" Pin0InfoVect0LinkObjId="SW-307436_0" Pin0InfoVect1LinkObjId="SW-307437_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-748 1105,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e06c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-709 1105,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47654@1" ObjectIDZND0="47657@x" ObjectIDZND1="47655@x" Pin0InfoVect0LinkObjId="SW-307439_0" Pin0InfoVect1LinkObjId="SW-307437_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-709 1105,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f8fa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-748 1105,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="47657@x" ObjectIDND1="47654@x" ObjectIDZND0="47655@0" Pin0InfoVect0LinkObjId="SW-307437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307439_0" Pin1InfoVect1LinkObjId="SW-307436_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-748 1105,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3172fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1138,-568 1105,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="47659@0" ObjectIDZND0="47734@x" ObjectIDZND1="47656@x" Pin0InfoVect0LinkObjId="g_2e7a810_0" Pin0InfoVect1LinkObjId="SW-307438_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307441_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1138,-568 1105,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e00280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-513 1105,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47734@1" ObjectIDZND0="47659@x" ObjectIDZND1="47656@x" Pin0InfoVect0LinkObjId="SW-307441_0" Pin0InfoVect1LinkObjId="SW-307438_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7a810_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-513 1105,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e25ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-568 1105,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="47659@x" ObjectIDND1="47734@x" ObjectIDZND0="47656@0" Pin0InfoVect0LinkObjId="SW-307438_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307441_0" Pin1InfoVect1LinkObjId="g_2e7a810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-568 1105,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e74eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-183 960,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47662@1" ObjectIDZND0="47664@1" Pin0InfoVect0LinkObjId="SW-307445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307444_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-183 960,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efd6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-219 960,-289 1105,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="47664@0" ObjectIDZND0="g_3616a30@0" ObjectIDZND1="47734@x" ObjectIDZND2="47667@x" Pin0InfoVect0LinkObjId="g_3616a30_0" Pin0InfoVect1LinkObjId="g_2e7a810_0" Pin0InfoVect2LinkObjId="SW-307447_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307445_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="960,-219 960,-289 1105,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e40650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-67 960,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47663@0" Pin0InfoVect0LinkObjId="SW-307445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-67 960,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_312d210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-136 960,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47663@1" ObjectIDZND0="47662@0" Pin0InfoVect0LinkObjId="SW-307444_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307445_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-136 960,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_312d470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-289 1257,-289 1257,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3616a30@0" ObjectIDND1="47734@x" ObjectIDND2="47664@x" ObjectIDZND0="47667@0" Pin0InfoVect0LinkObjId="SW-307447_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3616a30_0" Pin1InfoVect1LinkObjId="g_2e7a810_0" Pin1InfoVect2LinkObjId="SW-307445_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1105,-289 1257,-289 1257,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361fa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1257,-116 1257,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47666@0" ObjectIDZND0="47641@0" Pin0InfoVect0LinkObjId="g_3196ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1257,-116 1257,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361fcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1257,-181 1257,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47665@1" ObjectIDZND0="47667@1" Pin0InfoVect0LinkObjId="SW-307447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307446_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1257,-181 1257,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3123070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,-67 154,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47673@0" Pin0InfoVect0LinkObjId="SW-307456_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="154,-67 154,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31268a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,4 154,22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47673@1" ObjectIDZND0="47672@1" Pin0InfoVect0LinkObjId="SW-307455_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="154,4 154,22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3126ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,49 154,67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47672@0" ObjectIDZND0="47674@1" Pin0InfoVect0LinkObjId="SW-307456_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307455_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="154,49 154,67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3a640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="112,188 154,188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_31642a0@0" ObjectIDZND0="47674@x" ObjectIDZND1="47675@x" Pin0InfoVect0LinkObjId="SW-307456_0" Pin0InfoVect1LinkObjId="SW-307457_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31642a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="112,188 154,188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2deff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,84 154,188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47674@0" ObjectIDZND0="g_31642a0@0" ObjectIDZND1="47675@x" Pin0InfoVect0LinkObjId="g_31642a0_0" Pin0InfoVect1LinkObjId="SW-307457_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="154,84 154,188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2df0180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,188 154,302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_31642a0@0" ObjectIDND1="47674@x" ObjectIDND2="47675@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_31642a0_0" Pin1InfoVect1LinkObjId="SW-307456_0" Pin1InfoVect2LinkObjId="SW-307457_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="154,188 154,302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_303ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="189,188 154,188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47675@0" ObjectIDZND0="g_31642a0@0" ObjectIDZND1="47674@x" Pin0InfoVect0LinkObjId="g_31642a0_0" Pin0InfoVect1LinkObjId="SW-307456_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307457_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="189,188 154,188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e22b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-67 315,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47668@0" Pin0InfoVect0LinkObjId="SW-307453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-67 315,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2df0a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,2 315,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="47668@1" ObjectIDZND0="47669@1" Pin0InfoVect0LinkObjId="SW-307453_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,2 315,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e31d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="279,113 315,113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e325a0@0" ObjectIDZND0="47669@x" ObjectIDZND1="g_2df0c90@0" Pin0InfoVect0LinkObjId="SW-307453_0" Pin0InfoVect1LinkObjId="g_2df0c90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e325a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="279,113 315,113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3615960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,82 315,113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47669@0" ObjectIDZND0="g_2e325a0@0" ObjectIDZND1="g_2df0c90@0" Pin0InfoVect0LinkObjId="g_2e325a0_0" Pin0InfoVect1LinkObjId="g_2df0c90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="315,82 315,113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3615bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,113 315,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e325a0@0" ObjectIDND1="47669@x" ObjectIDZND0="g_2df0c90@0" Pin0InfoVect0LinkObjId="g_2df0c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e325a0_0" Pin1InfoVect1LinkObjId="SW-307453_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,113 315,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3048e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="442,-67 442,-14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47677@0" Pin0InfoVect0LinkObjId="SW-307461_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="442,-67 442,-14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e24b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="442,3 442,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47677@1" ObjectIDZND0="47676@1" Pin0InfoVect0LinkObjId="SW-307460_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307461_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="442,3 442,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e24de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="442,48 442,66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47676@0" ObjectIDZND0="47678@1" Pin0InfoVect0LinkObjId="SW-307461_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="442,48 442,66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfbd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="400,187 442,187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e25040@0" ObjectIDZND0="47678@x" ObjectIDZND1="47679@x" Pin0InfoVect0LinkObjId="SW-307461_0" Pin0InfoVect1LinkObjId="SW-307462_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e25040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="400,187 442,187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfbfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="442,83 442,187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47678@0" ObjectIDZND0="g_2e25040@0" ObjectIDZND1="47679@x" Pin0InfoVect0LinkObjId="g_2e25040_0" Pin0InfoVect1LinkObjId="SW-307462_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307461_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="442,83 442,187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfc210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="442,187 442,301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47678@x" ObjectIDND1="g_2e25040@0" ObjectIDND2="47679@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307461_0" Pin1InfoVect1LinkObjId="g_2e25040_0" Pin1InfoVect2LinkObjId="SW-307462_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="442,187 442,301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_317f3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="477,187 442,187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47679@0" ObjectIDZND0="47678@x" ObjectIDZND1="g_2e25040@0" Pin0InfoVect0LinkObjId="SW-307461_0" Pin0InfoVect1LinkObjId="g_2e25040_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307462_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="477,187 442,187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3107a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-67 942,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47693@0" Pin0InfoVect0LinkObjId="SW-307481_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,-67 942,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3107ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,6 942,24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47693@1" ObjectIDZND0="47692@1" Pin0InfoVect0LinkObjId="SW-307480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307481_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,6 942,24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31105e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,51 942,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47692@0" ObjectIDZND0="47694@1" Pin0InfoVect0LinkObjId="SW-307481_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,51 942,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e35ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="900,190 942,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3110840@0" ObjectIDZND0="47694@x" ObjectIDZND1="47695@x" Pin0InfoVect0LinkObjId="SW-307481_0" Pin0InfoVect1LinkObjId="SW-307482_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3110840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="900,190 942,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e35d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,86 942,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47694@0" ObjectIDZND0="g_3110840@0" ObjectIDZND1="47695@x" Pin0InfoVect0LinkObjId="g_3110840_0" Pin0InfoVect1LinkObjId="SW-307482_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307481_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="942,86 942,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e35fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,190 942,304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47694@x" ObjectIDND1="g_3110840@0" ObjectIDND2="47695@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307481_0" Pin1InfoVect1LinkObjId="g_3110840_0" Pin1InfoVect2LinkObjId="SW-307482_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="942,190 942,304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e417a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,190 942,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47695@0" ObjectIDZND0="47694@x" ObjectIDZND1="g_3110840@0" Pin0InfoVect0LinkObjId="SW-307481_0" Pin0InfoVect1LinkObjId="g_3110840_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="977,190 942,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e241c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-67 816,-12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47689@0" Pin0InfoVect0LinkObjId="SW-307476_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-67 816,-12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e24420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,5 816,23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47689@1" ObjectIDZND0="47688@1" Pin0InfoVect0LinkObjId="SW-307475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307476_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,5 816,23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e24680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,50 816,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47688@0" ObjectIDZND0="47690@1" Pin0InfoVect0LinkObjId="SW-307476_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,50 816,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3163010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,189 816,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3140860@0" ObjectIDZND0="47690@x" ObjectIDZND1="47691@x" Pin0InfoVect0LinkObjId="SW-307476_0" Pin0InfoVect1LinkObjId="SW-307477_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3140860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="774,189 816,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3163270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,85 816,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47690@0" ObjectIDZND0="g_3140860@0" ObjectIDZND1="47691@x" Pin0InfoVect0LinkObjId="g_3140860_0" Pin0InfoVect1LinkObjId="SW-307477_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307476_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="816,85 816,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31634d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,189 816,303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47690@x" ObjectIDND1="g_3140860@0" ObjectIDND2="47691@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307476_0" Pin1InfoVect1LinkObjId="g_3140860_0" Pin1InfoVect2LinkObjId="SW-307477_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="816,189 816,303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e8ead0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,189 816,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47691@0" ObjectIDZND0="47690@x" ObjectIDZND1="g_3140860@0" Pin0InfoVect0LinkObjId="SW-307476_0" Pin0InfoVect1LinkObjId="g_3140860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307477_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="851,189 816,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3dee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,-67 695,-14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47685@0" Pin0InfoVect0LinkObjId="SW-307471_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="695,-67 695,-14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3e140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,3 695,21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47685@1" ObjectIDZND0="47684@1" Pin0InfoVect0LinkObjId="SW-307470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307471_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="695,3 695,21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3e3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,48 695,66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47684@0" ObjectIDZND0="47686@1" Pin0InfoVect0LinkObjId="SW-307471_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="695,48 695,66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfaeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,187 695,187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e3e600@0" ObjectIDZND0="47686@x" ObjectIDZND1="47687@x" Pin0InfoVect0LinkObjId="SW-307471_0" Pin0InfoVect1LinkObjId="SW-307472_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3e600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="653,187 695,187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfb110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,83 695,187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47686@0" ObjectIDZND0="g_2e3e600@0" ObjectIDZND1="47687@x" Pin0InfoVect0LinkObjId="g_2e3e600_0" Pin0InfoVect1LinkObjId="SW-307472_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="695,83 695,187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfb370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,187 695,301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47686@x" ObjectIDND1="g_2e3e600@0" ObjectIDND2="47687@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307471_0" Pin1InfoVect1LinkObjId="g_2e3e600_0" Pin1InfoVect2LinkObjId="SW-307472_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="695,187 695,301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfb5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="730,187 695,187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47687@0" ObjectIDZND0="47686@x" ObjectIDZND1="g_2e3e600@0" Pin0InfoVect0LinkObjId="SW-307471_0" Pin0InfoVect1LinkObjId="g_2e3e600_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="730,187 695,187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_317cb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,-67 559,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47681@0" Pin0InfoVect0LinkObjId="SW-307466_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="559,-67 559,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e2a7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,6 559,24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47681@1" ObjectIDZND0="47680@1" Pin0InfoVect0LinkObjId="SW-307465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307466_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="559,6 559,24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e2aa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,51 559,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47680@0" ObjectIDZND0="47682@1" Pin0InfoVect0LinkObjId="SW-307466_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307465_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="559,51 559,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebf390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="517,190 559,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e2ac70@0" ObjectIDZND0="47682@x" ObjectIDZND1="47683@x" Pin0InfoVect0LinkObjId="SW-307466_0" Pin0InfoVect1LinkObjId="SW-307467_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e2ac70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="517,190 559,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebf5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,86 559,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47682@0" ObjectIDZND0="g_2e2ac70@0" ObjectIDZND1="47683@x" Pin0InfoVect0LinkObjId="g_2e2ac70_0" Pin0InfoVect1LinkObjId="SW-307467_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307466_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="559,86 559,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebf850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,190 559,304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47682@x" ObjectIDND1="g_2e2ac70@0" ObjectIDND2="47683@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307466_0" Pin1InfoVect1LinkObjId="g_2e2ac70_0" Pin1InfoVect2LinkObjId="SW-307467_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="559,190 559,304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebfab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="594,190 559,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47683@0" ObjectIDZND0="47682@x" ObjectIDZND1="g_2e2ac70@0" Pin0InfoVect0LinkObjId="SW-307466_0" Pin0InfoVect1LinkObjId="g_2e2ac70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307467_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="594,190 559,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e8d840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="219,-67 219,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47717@0" Pin0InfoVect0LinkObjId="SW-307511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="219,-67 219,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e8daa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="219,-115 219,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47717@1" ObjectIDZND0="47716@0" Pin0InfoVect0LinkObjId="SW-307510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="219,-115 219,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e8dd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="219,-160 219,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47716@1" ObjectIDZND0="47718@1" Pin0InfoVect0LinkObjId="SW-307511_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="219,-160 219,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ec18e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,-227 219,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2df9010@0" ObjectIDZND0="47718@x" ObjectIDZND1="47719@x" ObjectIDZND2="g_2ec2ab0@0" Pin0InfoVect0LinkObjId="SW-307511_0" Pin0InfoVect1LinkObjId="SW-307512_0" Pin0InfoVect2LinkObjId="g_2ec2ab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2df9010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="183,-227 219,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ec25f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="219,-195 219,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47718@0" ObjectIDZND0="g_2df9010@0" ObjectIDZND1="47719@x" ObjectIDZND2="g_2ec2ab0@0" Pin0InfoVect0LinkObjId="g_2df9010_0" Pin0InfoVect1LinkObjId="SW-307512_0" Pin0InfoVect2LinkObjId="g_2ec2ab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="219,-195 219,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ec2850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-227 219,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47719@0" ObjectIDZND0="g_2df9010@0" ObjectIDZND1="47718@x" ObjectIDZND2="g_2ec2ab0@0" Pin0InfoVect0LinkObjId="g_2df9010_0" Pin0InfoVect1LinkObjId="SW-307511_0" Pin0InfoVect2LinkObjId="g_2ec2ab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="260,-227 219,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3040700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="219,-227 219,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2df9010@0" ObjectIDND1="47718@x" ObjectIDND2="47719@x" ObjectIDZND0="g_2ec2ab0@1" Pin0InfoVect0LinkObjId="g_2ec2ab0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2df9010_0" Pin1InfoVect1LinkObjId="SW-307511_0" Pin1InfoVect2LinkObjId="SW-307512_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="219,-227 219,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3040960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="219,-365 219,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2ec2ab0@0" ObjectIDZND0="g_3040bc0@0" Pin0InfoVect0LinkObjId="g_3040bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ec2ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="219,-365 219,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_316df20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-67 754,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47640@0" ObjectIDZND0="47721@0" Pin0InfoVect0LinkObjId="SW-307516_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-67 754,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_316e180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-115 754,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47721@1" ObjectIDZND0="47720@0" Pin0InfoVect0LinkObjId="SW-307515_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307516_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-115 754,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_316e3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-160 754,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47720@1" ObjectIDZND0="47722@1" Pin0InfoVect0LinkObjId="SW-307516_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307515_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-160 754,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3181430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="792,-222 754,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47725@0" ObjectIDZND0="47722@x" ObjectIDZND1="g_316e640@0" ObjectIDZND2="g_3182600@0" Pin0InfoVect0LinkObjId="SW-307516_0" Pin0InfoVect1LinkObjId="g_316e640_0" Pin0InfoVect2LinkObjId="g_3182600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307519_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="792,-222 754,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3182140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-195 754,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47722@0" ObjectIDZND0="47725@x" ObjectIDZND1="g_316e640@0" ObjectIDZND2="g_3182600@0" Pin0InfoVect0LinkObjId="SW-307519_0" Pin0InfoVect1LinkObjId="g_316e640_0" Pin0InfoVect2LinkObjId="g_3182600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="754,-195 754,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31823a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="715,-222 754,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_316e640@0" ObjectIDZND0="47725@x" ObjectIDZND1="47722@x" ObjectIDZND2="g_3182600@0" Pin0InfoVect0LinkObjId="SW-307519_0" Pin0InfoVect1LinkObjId="SW-307516_0" Pin0InfoVect2LinkObjId="g_3182600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_316e640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="715,-222 754,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3183080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-222 754,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="47725@x" ObjectIDND1="47722@x" ObjectIDND2="g_316e640@0" ObjectIDZND0="g_3182600@1" Pin0InfoVect0LinkObjId="g_3182600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307519_0" Pin1InfoVect1LinkObjId="SW-307516_0" Pin1InfoVect2LinkObjId="g_316e640_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-222 754,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3188880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-353 754,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3182600@0" ObjectIDZND0="47723@0" Pin0InfoVect0LinkObjId="SW-307517_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3182600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-353 754,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34eb600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-446 754,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="47726@x" ObjectIDND1="47723@x" ObjectIDZND0="47724@x" Pin0InfoVect0LinkObjId="SW-307518_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307520_0" Pin1InfoVect1LinkObjId="SW-307517_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-446 754,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ec0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-446 754,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47726@0" ObjectIDZND0="47724@x" ObjectIDZND1="47723@x" Pin0InfoVect0LinkObjId="SW-307518_0" Pin0InfoVect1LinkObjId="SW-307517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="775,-446 754,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ec330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-446 754,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="47724@x" ObjectIDND1="47726@x" ObjectIDZND0="47723@1" Pin0InfoVect0LinkObjId="SW-307517_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307518_0" Pin1InfoVect1LinkObjId="SW-307520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-446 754,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ece00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-495 754,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47724@0" ObjectIDZND0="47726@x" ObjectIDZND1="47723@x" Pin0InfoVect0LinkObjId="SW-307520_0" Pin0InfoVect1LinkObjId="SW-307517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307518_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="754,-495 754,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ed040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-483 784,-483 784,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="47726@x" ObjectIDND1="47723@x" ObjectIDND2="47724@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307520_0" Pin1InfoVect1LinkObjId="SW-307517_0" Pin1InfoVect2LinkObjId="SW-307518_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="754,-483 784,-483 784,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34f5f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-115 1490,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47728@1" ObjectIDZND0="47727@0" Pin0InfoVect0LinkObjId="SW-307523_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307524_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-115 1490,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34f61c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-160 1490,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47727@1" ObjectIDZND0="47729@1" Pin0InfoVect0LinkObjId="SW-307524_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307523_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-160 1490,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34fa440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-222 1490,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47732@0" ObjectIDZND0="47729@x" ObjectIDZND1="g_34fab60@0" ObjectIDZND2="g_34f6420@0" Pin0InfoVect0LinkObjId="SW-307524_0" Pin0InfoVect1LinkObjId="g_34fab60_0" Pin0InfoVect2LinkObjId="g_34f6420_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307527_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-222 1490,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34fa6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-195 1490,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47729@0" ObjectIDZND0="47732@x" ObjectIDZND1="g_34fab60@0" ObjectIDZND2="g_34f6420@0" Pin0InfoVect0LinkObjId="SW-307527_0" Pin0InfoVect1LinkObjId="g_34fab60_0" Pin0InfoVect2LinkObjId="g_34f6420_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-195 1490,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34fa900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1451,-222 1490,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34f6420@0" ObjectIDZND0="47729@x" ObjectIDZND1="47732@x" ObjectIDZND2="g_34fab60@0" Pin0InfoVect0LinkObjId="SW-307524_0" Pin0InfoVect1LinkObjId="SW-307527_0" Pin0InfoVect2LinkObjId="g_34fab60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34f6420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1451,-222 1490,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3698630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-222 1490,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="47729@x" ObjectIDND1="47732@x" ObjectIDND2="g_34f6420@0" ObjectIDZND0="g_34fab60@1" Pin0InfoVect0LinkObjId="g_34fab60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307524_0" Pin1InfoVect1LinkObjId="SW-307527_0" Pin1InfoVect2LinkObjId="g_34f6420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-222 1490,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369dbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-353 1490,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34fab60@0" ObjectIDZND0="47730@0" Pin0InfoVect0LinkObjId="SW-307525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fab60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-353 1490,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a1360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1520,-525 1520,-549 1490,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="reactance" ObjectIDZND0="47731@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307526_0" Pin0InfoVect1LinkObjId="g_2efdce0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1520,-525 1520,-549 1490,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a15c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-531 1490,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="reactance" ObjectIDND0="47731@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="g_2efdce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307526_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-531 1490,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a2530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-548 1490,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="reactance" ObjectIDND0="47731@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2efdce0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-548 1490,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36a2790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-608 1490,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_36a35d0@0" Pin0InfoVect0LinkObjId="g_36a35d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efdce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-608 1490,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a29f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-446 1490,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="47733@x" ObjectIDND1="47730@x" ObjectIDZND0="47731@x" Pin0InfoVect0LinkObjId="SW-307526_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307528_0" Pin1InfoVect1LinkObjId="SW-307525_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-446 1490,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a2c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1511,-446 1490,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47733@0" ObjectIDZND0="47731@x" ObjectIDZND1="47730@x" Pin0InfoVect0LinkObjId="SW-307526_0" Pin0InfoVect1LinkObjId="SW-307525_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307528_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1511,-446 1490,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a2eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-446 1490,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="47733@x" ObjectIDND1="47731@x" ObjectIDZND0="47730@1" Pin0InfoVect0LinkObjId="SW-307525_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307528_0" Pin1InfoVect1LinkObjId="SW-307526_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-446 1490,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a3110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-495 1490,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47731@0" ObjectIDZND0="47733@x" ObjectIDZND1="47730@x" Pin0InfoVect0LinkObjId="SW-307528_0" Pin0InfoVect1LinkObjId="SW-307525_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-495 1490,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a3370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-483 1520,-483 1520,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="47731@x" ObjectIDND1="47733@x" ObjectIDND2="47730@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307526_0" Pin1InfoVect1LinkObjId="SW-307528_0" Pin1InfoVect2LinkObjId="SW-307525_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-483 1520,-483 1520,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ae630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1295,-67 1295,-12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47641@0" ObjectIDZND0="47697@0" Pin0InfoVect0LinkObjId="SW-307486_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_361fa80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1295,-67 1295,-12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ae890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1295,5 1295,23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47697@1" ObjectIDZND0="47696@1" Pin0InfoVect0LinkObjId="SW-307485_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1295,5 1295,23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36aeaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1295,50 1295,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47696@0" ObjectIDZND0="47698@1" Pin0InfoVect0LinkObjId="SW-307486_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1295,50 1295,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b2d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1253,189 1295,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36aed50@0" ObjectIDZND0="47698@x" ObjectIDZND1="47699@x" Pin0InfoVect0LinkObjId="SW-307486_0" Pin0InfoVect1LinkObjId="SW-307487_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36aed50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1253,189 1295,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b2fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1295,85 1295,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47698@0" ObjectIDZND0="g_36aed50@0" ObjectIDZND1="47699@x" Pin0InfoVect0LinkObjId="g_36aed50_0" Pin0InfoVect1LinkObjId="SW-307487_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307486_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1295,85 1295,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b3230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1295,189 1295,303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47698@x" ObjectIDND1="g_36aed50@0" ObjectIDND2="47699@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307486_0" Pin1InfoVect1LinkObjId="g_36aed50_0" Pin1InfoVect2LinkObjId="SW-307487_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1295,189 1295,303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b3490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,189 1295,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47699@0" ObjectIDZND0="47698@x" ObjectIDZND1="g_36aed50@0" Pin0InfoVect0LinkObjId="SW-307486_0" Pin0InfoVect1LinkObjId="g_36aed50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307487_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1330,189 1295,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36bcd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,-67 1424,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47641@0" ObjectIDZND0="47701@0" Pin0InfoVect0LinkObjId="SW-307491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_361fa80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1424,-67 1424,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36bcfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,10 1424,28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47701@1" ObjectIDZND0="47700@1" Pin0InfoVect0LinkObjId="SW-307490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307491_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1424,10 1424,28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36bd240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,55 1424,73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47700@0" ObjectIDZND0="47702@1" Pin0InfoVect0LinkObjId="SW-307491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1424,55 1424,73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dbc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1382,194 1424,194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36bd4a0@0" ObjectIDZND0="47702@x" ObjectIDZND1="47703@x" Pin0InfoVect0LinkObjId="SW-307491_0" Pin0InfoVect1LinkObjId="SW-307492_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36bd4a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1382,194 1424,194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dbed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,90 1424,194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47702@0" ObjectIDZND0="g_36bd4a0@0" ObjectIDZND1="47703@x" Pin0InfoVect0LinkObjId="g_36bd4a0_0" Pin0InfoVect1LinkObjId="SW-307492_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1424,90 1424,194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dc130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1424,194 1424,308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47702@x" ObjectIDND1="g_36bd4a0@0" ObjectIDND2="47703@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307491_0" Pin1InfoVect1LinkObjId="g_36bd4a0_0" Pin1InfoVect2LinkObjId="SW-307492_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1424,194 1424,308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dc390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1459,194 1424,194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47703@0" ObjectIDZND0="47702@x" ObjectIDZND1="g_36bd4a0@0" Pin0InfoVect0LinkObjId="SW-307491_0" Pin0InfoVect1LinkObjId="g_36bd4a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1459,194 1424,194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e5ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1569,-67 1569,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47641@0" ObjectIDZND0="47705@0" Pin0InfoVect0LinkObjId="SW-307496_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_361fa80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-67 1569,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e5e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1569,7 1569,25 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47705@1" ObjectIDZND0="47704@1" Pin0InfoVect0LinkObjId="SW-307495_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307496_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,7 1569,25 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35e6060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1569,52 1569,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47704@0" ObjectIDZND0="47706@1" Pin0InfoVect0LinkObjId="SW-307496_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307495_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,52 1569,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ea2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1527,191 1569,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_35e62c0@0" ObjectIDZND0="47706@x" ObjectIDZND1="47707@x" Pin0InfoVect0LinkObjId="SW-307496_0" Pin0InfoVect1LinkObjId="SW-307497_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e62c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1527,191 1569,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ea540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1569,87 1569,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47706@0" ObjectIDZND0="g_35e62c0@0" ObjectIDZND1="47707@x" Pin0InfoVect0LinkObjId="g_35e62c0_0" Pin0InfoVect1LinkObjId="SW-307497_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307496_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1569,87 1569,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ea7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1569,191 1569,305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47706@x" ObjectIDND1="g_35e62c0@0" ObjectIDND2="47707@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307496_0" Pin1InfoVect1LinkObjId="g_35e62c0_0" Pin1InfoVect2LinkObjId="SW-307497_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1569,191 1569,305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35eaa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1604,191 1569,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47707@0" ObjectIDZND0="47706@x" ObjectIDZND1="g_35e62c0@0" Pin0InfoVect0LinkObjId="SW-307496_0" Pin0InfoVect1LinkObjId="g_35e62c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1604,191 1569,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f3f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1711,-67 1711,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47641@0" ObjectIDZND0="47709@0" Pin0InfoVect0LinkObjId="SW-307501_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_361fa80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1711,-67 1711,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f4160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1711,9 1711,27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47709@1" ObjectIDZND0="47708@1" Pin0InfoVect0LinkObjId="SW-307500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307501_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1711,9 1711,27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f43c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1711,54 1711,72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47708@0" ObjectIDZND0="47710@1" Pin0InfoVect0LinkObjId="SW-307501_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1711,54 1711,72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f8640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1669,193 1711,193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_35f4620@0" ObjectIDZND0="47710@x" ObjectIDZND1="47711@x" Pin0InfoVect0LinkObjId="SW-307501_0" Pin0InfoVect1LinkObjId="SW-307502_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f4620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1669,193 1711,193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f88a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1711,89 1711,193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47710@0" ObjectIDZND0="g_35f4620@0" ObjectIDZND1="47711@x" Pin0InfoVect0LinkObjId="g_35f4620_0" Pin0InfoVect1LinkObjId="SW-307502_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307501_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1711,89 1711,193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f8b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1711,193 1711,307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47710@x" ObjectIDND1="g_35f4620@0" ObjectIDND2="47711@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307501_0" Pin1InfoVect1LinkObjId="g_35f4620_0" Pin1InfoVect2LinkObjId="SW-307502_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1711,193 1711,307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f8d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1746,193 1711,193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47711@0" ObjectIDZND0="47710@x" ObjectIDZND1="g_35f4620@0" Pin0InfoVect0LinkObjId="SW-307501_0" Pin0InfoVect1LinkObjId="g_35f4620_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307502_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1746,193 1711,193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3602250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,-67 1855,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47641@0" ObjectIDZND0="47713@0" Pin0InfoVect0LinkObjId="SW-307506_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_361fa80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1855,-67 1855,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36024b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,8 1855,26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47713@1" ObjectIDZND0="47712@1" Pin0InfoVect0LinkObjId="SW-307505_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307506_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1855,8 1855,26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3602710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,53 1855,71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47712@0" ObjectIDZND0="47714@1" Pin0InfoVect0LinkObjId="SW-307506_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307505_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1855,53 1855,71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3606990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1813,192 1855,192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3602970@0" ObjectIDZND0="47714@x" ObjectIDZND1="47715@x" Pin0InfoVect0LinkObjId="SW-307506_0" Pin0InfoVect1LinkObjId="SW-307507_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3602970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1813,192 1855,192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3606bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,88 1855,192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47714@0" ObjectIDZND0="g_3602970@0" ObjectIDZND1="47715@x" Pin0InfoVect0LinkObjId="g_3602970_0" Pin0InfoVect1LinkObjId="SW-307507_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1855,88 1855,192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3606e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,192 1855,306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="47714@x" ObjectIDND1="g_3602970@0" ObjectIDND2="47715@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307506_0" Pin1InfoVect1LinkObjId="g_3602970_0" Pin1InfoVect2LinkObjId="SW-307507_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1855,192 1855,306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36070b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1890,192 1855,192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47715@0" ObjectIDZND0="47714@x" ObjectIDZND1="g_3602970@0" Pin0InfoVect0LinkObjId="SW-307506_0" Pin0InfoVect1LinkObjId="g_3602970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307507_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1890,192 1855,192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_318fdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,-67 2000,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47641@0" ObjectIDZND0="47670@0" Pin0InfoVect0LinkObjId="SW-307454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_361fa80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-67 2000,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3194950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1964,113 2000,113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_3193c20@0" ObjectIDZND0="47671@x" ObjectIDZND1="g_3190a00@0" Pin0InfoVect0LinkObjId="SW-307454_0" Pin0InfoVect1LinkObjId="g_3190a00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3193c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1964,113 2000,113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3194bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,82 2000,113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="47671@0" ObjectIDZND0="g_3193c20@0" ObjectIDZND1="g_3190a00@0" Pin0InfoVect0LinkObjId="g_3193c20_0" Pin0InfoVect1LinkObjId="g_3190a00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307454_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2000,82 2000,113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3196460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,113 2000,210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="47671@x" ObjectIDND1="g_3193c20@0" ObjectIDZND0="g_3190a00@0" Pin0InfoVect0LinkObjId="g_3190a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307454_0" Pin1InfoVect1LinkObjId="g_3193c20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,113 2000,210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3196680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,2 2000,16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="47670@1" ObjectIDZND0="g_3190030@0" Pin0InfoVect0LinkObjId="g_3190030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,2 2000,16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31968a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2000,48 2000,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3190030@1" ObjectIDZND0="47671@1" Pin0InfoVect0LinkObjId="SW-307454_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3190030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,48 2000,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3196ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-98 1490,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47728@0" ObjectIDZND0="47641@0" Pin0InfoVect0LinkObjId="g_361fa80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-98 1490,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3198440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="965,-512 965,-497 989,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_3197230@0" ObjectIDZND0="g_3197bd0@0" ObjectIDZND1="47660@x" ObjectIDZND2="47734@x" Pin0InfoVect0LinkObjId="g_3197bd0_0" Pin0InfoVect1LinkObjId="SW-307442_0" Pin0InfoVect2LinkObjId="g_2e7a810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3197230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="965,-512 965,-497 989,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_31986a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-497 989,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="g_3197230@0" ObjectIDND1="47660@x" ObjectIDND2="47734@x" ObjectIDZND0="g_3197bd0@1" Pin0InfoVect0LinkObjId="g_3197bd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3197230_0" Pin1InfoVect1LinkObjId="SW-307442_0" Pin1InfoVect2LinkObjId="g_2e7a810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-497 989,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3199e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-573 989,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_319a080@0" ObjectIDZND0="g_3197bd0@0" Pin0InfoVect0LinkObjId="g_3197bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_319a080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-573 989,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_319d310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="964,-570 964,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_3198900@0" ObjectIDZND0="g_3197230@1" Pin0InfoVect0LinkObjId="g_3197230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3198900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="964,-570 964,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_319d570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1017,-552 1017,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="47660@0" ObjectIDZND0="g_3199390@0" Pin0InfoVect0LinkObjId="g_3199390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1017,-552 1017,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_319d7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1017,-516 1017,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="47660@1" ObjectIDZND0="g_3197230@0" ObjectIDZND1="g_3197bd0@0" ObjectIDZND2="47734@x" Pin0InfoVect0LinkObjId="g_3197230_0" Pin0InfoVect1LinkObjId="g_3197bd0_0" Pin0InfoVect2LinkObjId="g_2e7a810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307442_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1017,-516 1017,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_319da30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-497 1017,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="g_3197230@0" ObjectIDND1="g_3197bd0@0" ObjectIDZND0="47660@x" ObjectIDZND1="47734@x" Pin0InfoVect0LinkObjId="SW-307442_0" Pin0InfoVect1LinkObjId="g_2e7a810_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3197230_0" Pin1InfoVect1LinkObjId="g_3197bd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="989,-497 1017,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_319dc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1017,-497 1104,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="47660@x" ObjectIDND1="g_3197230@0" ObjectIDND2="g_3197bd0@0" ObjectIDZND0="47734@x" Pin0InfoVect0LinkObjId="g_2e7a810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307442_0" Pin1InfoVect1LinkObjId="g_3197230_0" Pin1InfoVect2LinkObjId="g_3197bd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1017,-497 1104,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31a1240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1021,-357 1021,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_31a14a0@0" Pin0InfoVect0LinkObjId="g_31a14a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1021,-357 1021,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31a3050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1021,-397 1021,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="47661@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1021,-397 1021,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31a32b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,-449 1021,-449 1021,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="47734@x" ObjectIDZND0="47661@1" Pin0InfoVect0LinkObjId="SW-307443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7a810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1104,-449 1021,-449 1021,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31a3510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1160,-473 1207,-473 1207,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" ObjectIDND0="47734@2" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7a810_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1160,-473 1207,-473 1207,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_31a7140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1658,-847 1658,-930 1738,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47639@0" ObjectIDZND0="47652@0" Pin0InfoVect0LinkObjId="SW-307434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f41250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1658,-847 1658,-930 1738,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_31aa690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-909 1824,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="47653@0" ObjectIDZND0="47652@x" Pin0InfoVect0LinkObjId="SW-307434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-909 1824,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_31ab180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1774,-930 1824,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="47652@1" ObjectIDZND0="47653@x" Pin0InfoVect0LinkObjId="SW-307435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1774,-930 1824,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_31ab3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-930 1921,-930 1921,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="47653@x" ObjectIDND1="47652@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307435_0" Pin1InfoVect1LinkObjId="SW-307434_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-930 1921,-930 1921,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b1c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,210 315,160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2e61200@0" ObjectIDZND0="g_2df0c90@1" Pin0InfoVect0LinkObjId="g_2df0c90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e61200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,210 315,160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31b5070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-608 754,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_34ed2a0@0" Pin0InfoVect0LinkObjId="g_34ed2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efdce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-608 754,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b5260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-525 784,-546 754,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="reactance" ObjectIDZND0="47724@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307518_0" Pin0InfoVect1LinkObjId="g_2efdce0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="784,-525 784,-546 754,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b5bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-531 754,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="reactance" ObjectIDND0="47724@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="g_2efdce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307518_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-531 754,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b5de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="754,-546 754,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="reactance" ObjectIDND0="47724@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2efdce0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307518_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="754,-546 754,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_363ca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1257,-154 1257,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47665@0" ObjectIDZND0="47666@1" Pin0InfoVect0LinkObjId="SW-307447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307446_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1257,-154 1257,-133 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-0" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.000000 -507.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-0" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1516.000000 -507.000000)" xlink:href="#dynamicPoint:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-0" type="0">
    <use transform="matrix(0.000000 -0.848485 -0.736842 -0.000000 1029.000000 -357.000000)" xlink:href="#dynamicPoint:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-307149" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 -863.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47617" ObjectName="DYN-CX_QXS"/>
     <cge:Meas_Ref ObjectId="307149"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b80e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 989.000000 898.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b95a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1005.000000 883.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ba130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 997.000000 927.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ba690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 997.000000 942.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ba910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 997.000000 956.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bab50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 912.666667) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31baf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1316.000000 1027.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bb830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1305.000000 1012.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bc0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1330.000000 997.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bc420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 41.000000 108.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bc6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 57.000000 93.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bc8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 49.000000 137.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bcb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 49.000000 152.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bcd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 49.000000 166.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bcfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.000000 122.666667) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bd2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1962.000000 114.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bd550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1978.000000 99.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bd790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1970.000000 143.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bd9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1970.000000 158.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bdc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1970.000000 172.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bde50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1976.000000 128.666667) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31be270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1552.000000 168.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31be530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1541.000000 153.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31be770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1566.000000 138.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31beb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 809.000000 167.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bee50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 798.000000 152.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bf090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 137.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bf4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1014.000000 196.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bf770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 181.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bf9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 166.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bfdd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1311.000000 197.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31c0090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1300.000000 182.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31c02d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 167.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31c06f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1177.000000 721.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31c09b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1166.000000 706.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_363a980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 691.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_363ada0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 280.000000 169.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_363b060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 269.000000 154.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_363b2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 294.000000 139.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_363b6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.000000 -345.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_363b980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 43.000000 -360.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_363bbc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 68.000000 -375.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3198900" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 970.009804 -565.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3199390" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1023.500000 -569.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_319a080" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 995.009804 -568.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31a14a0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1027.009804 -351.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：  19388324676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,395)">                 0878 7715258</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31271d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31271d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31271d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31271d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31271d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31271d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31271d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_361da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">青香树光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_303d050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 174.000000 -522.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31069c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 115.000000 322.000000) translate(0,12)">35kV储能装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3147cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 276.000000 274.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3147cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 276.000000 274.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3119ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 398.000000 326.000000) translate(0,12)">35kV集电线路1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_311a970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 324.000000) translate(0,12)">35kV集电线路2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3149330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 654.000000 321.000000) translate(0,12)">35kV集电线路3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3149760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.000000 320.000000) translate(0,12)">35kV集电线路4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3149aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 319.000000) translate(0,12)">35kV集电线路5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(60,120,255)" font-family="SimSun" font-size="15" graphid="g_34eda00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 -649.000000) translate(0,12)">SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ee010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.000000 -680.000000) translate(0,12)">35kV1号SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(60,120,255)" font-family="SimSun" font-size="15" graphid="g_36a3d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -649.000000) translate(0,12)">SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a43a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1457.000000 -680.000000) translate(0,12)">35kV2号SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b36f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1257.000000 318.000000) translate(0,12)">35kV集电线路6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35dc5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 323.000000) translate(0,12)">35kV集电线路7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35eac60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1531.000000 320.000000) translate(0,12)">35kV集电线路8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f8fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1673.000000 322.000000) translate(0,12)">35kV集电线路9</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3607310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1817.000000 321.000000) translate(0,12)">35kV集电线路10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3194e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1961.000000 274.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3194e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1961.000000 274.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1215.000000 -584.000000) translate(0,12)">#1号主变:230MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ab640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -973.000000) translate(0,12)">291</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31abc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1251.000000 -891.000000) translate(0,12)">2911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31abeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1287.000000 -915.000000) translate(0,12)">29117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ac0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1181.000000 -1029.000000) translate(0,12)">29160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ac330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1277.000000 -1114.000000) translate(0,12)">29167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ac570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1251.000000 -1046.000000) translate(0,12)">2916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ac7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1508.000000 -942.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ac9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1200.000000 -1263.000000) translate(0,12)">220kV香平线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ad310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1531.000000 -915.000000) translate(0,12)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ad550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -1024.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ad790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1740.000000 -956.000000) translate(0,12)">2121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ad9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1833.000000 -893.000000) translate(0,12)">21217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31adc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 981.000000 -839.000000) translate(0,12)">220kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ae1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1114.000000 -703.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ae450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1112.000000 -812.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ae690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1128.000000 -774.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ae8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -680.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31aeb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1112.000000 -616.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31aed50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1141.000000 -594.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31aef90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1028.000000 -541.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31af1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -422.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31af410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -177.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31af650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1266.000000 -175.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31af890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 763.000000 -154.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31afad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 801.000000 -252.000000) translate(0,12)">38160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31afd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 707.000000 -401.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31aff50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 778.000000 -470.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b0190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 717.000000 -520.000000) translate(0,12)">3817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b03d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1499.000000 -154.000000) translate(0,12)">376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b0610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 -252.000000) translate(0,12)">37660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b0850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1444.000000 -403.000000) translate(0,12)">3766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b0a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1513.000000 -470.000000) translate(0,12)">37667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b0cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1442.000000 -518.000000) translate(0,12)">3767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b0f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 228.000000 -154.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b1150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 269.000000 -257.000000) translate(0,12)">37367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b1390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 60.000000 -58.000000) translate(0,12)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b15d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 163.000000 28.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b1810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 174.000000 242.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b1a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 30.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b1e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 27.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b2130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 460.000000 243.000000) translate(0,12)">37567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b2370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 30.000000) translate(0,12)">377</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b25b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 247.000000) translate(0,12)">37767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b27f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 704.000000 27.000000) translate(0,12)">379</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b2a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 243.000000) translate(0,12)">37967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b2c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 29.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b2eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 246.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b30f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 30.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b3330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 245.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b3570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1304.000000 29.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b37b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1311.000000 240.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b39f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 34.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b3c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1440.000000 251.000000) translate(0,12)">37467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b3e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1578.000000 31.000000) translate(0,12)">378</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b40b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1588.000000 246.000000) translate(0,12)">37867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b42f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1720.000000 33.000000) translate(0,12)">380</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b4530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1730.000000 247.000000) translate(0,12)">38067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b4770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1864.000000 32.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b49b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1875.000000 246.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b4bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2013.000000 27.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b4e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2053.000000 -59.000000) translate(0,12)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b6010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -517.000000) translate(0,12)">#1主变</text>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_QXS.CX_QXS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="45462"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -425.000000)" xlink:href="#transformer:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="45464"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -425.000000)" xlink:href="#transformer:shape17_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="45466"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -425.000000)" xlink:href="#transformer:shape17-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="47734" ObjectName="TF-CX_QXS.CX_QXS_1T"/>
    <cge:TPSR_Ref TObjectID="47734"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="47639" cx="1244" cy="-847" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47639" cx="1501" cy="-847" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47639" cx="1105" cy="-847" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="960" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="154" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="315" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="442" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="942" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="816" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="695" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="559" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="219" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47640" cx="754" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47641" cx="1295" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47641" cx="1424" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47641" cx="1569" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47641" cx="1711" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47641" cx="1855" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47641" cx="2000" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47641" cx="1490" cy="-67" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47639" cx="1658" cy="-847" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_QXS"/>
</svg>