<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-65" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3175 -1275 2789 1527">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape53">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="127" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="127" x2="127" y1="36" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="127" x2="127" y1="2" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="114" x2="127" y1="23" y2="23"/>
    <polyline points="127,36 128,36 130,35 132,35 133,34 135,33 136,32 137,30 138,28 139,27 139,25 139,23 139,21 139,19 138,18 137,16 136,15 135,13 133,12 132,11 130,11 128,10 127,10 125,10 123,11 121,11 120,12 118,13 117,15 116,16 115,18 114,19 114,21 114,23 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.529576" x1="127" x2="127" y1="99" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="137" x2="117" y1="52" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="137" x2="117" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="151" x2="151" y1="50" y2="41"/>
    <polyline arcFlag="1" points="151,50 152,50 152,50 153,50 154,50 154,51 155,51 156,52 156,52 156,53 157,54 157,54 157,55 157,56 157,57 157,57 156,58 156,59 156,59 155,60 154,60 154,61 153,61 152,61 152,61 151,61 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="151,61 152,61 152,61 153,61 154,61 154,62 155,62 156,63 156,63 156,64 157,65 157,65 157,66 157,67 157,68 157,68 156,69 156,70 156,70 155,71 154,71 154,72 153,72 152,72 152,72 151,72 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="151,72 152,72 152,72 153,72 154,72 154,73 155,73 156,74 156,74 156,75 157,76 157,76 157,77 157,78 157,79 157,79 156,80 156,81 156,81 155,82 154,82 154,83 153,83 152,83 152,83 151,83 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="151" x2="151" y1="92" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="151" x2="127" y1="92" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="151" x2="127" y1="40" y2="40"/>
    <rect height="26" stroke-width="0.398039" width="12" x="121" y="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="162" x2="162" y1="82" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="14" x2="14" y1="36" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="14" x2="14" y1="2" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="1" x2="14" y1="23" y2="23"/>
    <polyline points="14,36 15,36 17,35 19,35 20,34 22,33 23,32 24,30 25,28 26,27 26,25 26,23 26,21 26,19 25,18 24,16 23,15 22,13 20,12 19,11 17,11 15,10 14,10 12,10 10,11 8,11 7,12 5,13 4,15 3,16 2,18 1,19 1,21 1,23 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.529576" x1="14" x2="14" y1="99" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="24" x2="4" y1="52" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="24" x2="4" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="38" x2="38" y1="50" y2="41"/>
    <polyline arcFlag="1" points="38,50 39,50 39,50 40,50 41,50 41,51 42,51 43,52 43,52 43,53 44,54 44,54 44,55 44,56 44,57 44,57 43,58 43,59 43,59 42,60 41,60 41,61 40,61 39,61 39,61 38,61 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="38,61 39,61 39,61 40,61 41,61 41,62 42,62 43,63 43,63 43,64 44,65 44,65 44,66 44,67 44,68 44,68 43,69 43,70 43,70 42,71 41,71 41,72 40,72 39,72 39,72 38,72 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="38,72 39,72 39,72 40,72 41,72 41,73 42,73 43,74 43,74 43,75 44,76 44,76 44,77 44,78 44,79 44,79 43,80 43,81 43,81 42,82 41,82 41,83 40,83 39,83 39,83 38,83 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="38" x2="38" y1="92" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="38" x2="14" y1="92" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="38" x2="14" y1="40" y2="40"/>
    <rect height="26" stroke-width="0.398039" width="12" x="8" y="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="49" x2="49" y1="82" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="67" x2="67" y1="36" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="67" x2="67" y1="2" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="54" x2="67" y1="23" y2="23"/>
    <polyline points="67,36 68,36 70,35 72,35 73,34 75,33 76,32 77,30 78,28 79,27 79,25 79,23 79,21 79,19 78,18 77,16 76,15 75,13 73,12 72,11 70,11 68,10 67,10 65,10 63,11 61,11 60,12 58,13 57,15 56,16 55,18 54,19 54,21 54,23 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.529576" x1="67" x2="67" y1="99" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="77" x2="57" y1="52" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="77" x2="57" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="91" x2="91" y1="50" y2="41"/>
    <polyline arcFlag="1" points="91,50 92,50 92,50 93,50 94,50 94,51 95,51 96,52 96,52 96,53 97,54 97,54 97,55 97,56 97,57 97,57 96,58 96,59 96,59 95,60 94,60 94,61 93,61 92,61 92,61 91,61 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="91,61 92,61 92,61 93,61 94,61 94,62 95,62 96,63 96,63 96,64 97,65 97,65 97,66 97,67 97,68 97,68 96,69 96,70 96,70 95,71 94,71 94,72 93,72 92,72 92,72 91,72 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="91,72 92,72 92,72 93,72 94,72 94,73 95,73 96,74 96,74 96,75 97,76 97,76 97,77 97,78 97,79 97,79 96,80 96,81 96,81 95,82 94,82 94,83 93,83 92,83 92,83 91,83 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="91" x2="91" y1="92" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="91" x2="67" y1="92" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="91" x2="67" y1="40" y2="40"/>
    <rect height="26" stroke-width="0.398039" width="12" x="61" y="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="102" x2="102" y1="82" y2="48"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape114">
    <ellipse cx="25" cy="38" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="63" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="26" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="51" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="52" x2="52" y1="62" y2="32"/>
    <rect height="26" stroke-width="0.416667" width="13" x="45" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="53" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="40" x2="29" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="35" x2="35" y1="8" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="31" x2="38" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="32" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="62" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="27" y2="17"/>
    <ellipse cx="15" cy="34" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <ellipse cx="8" cy="38" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="15" cy="43" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape12">
    <polyline points="9,14 3,17 1,18 1,19 1,19 3,21 6,22 10,24 11,25 11,25 11,26 10,27 6,28 3,30 2,30 2,31 2,32 3,33 6,34 10,36 11,36 11,37 11,38 10,38 6,40 3,41 2,42 2,43 2,44 3,44 6,46 10,47 11,48 11,49 11,49 10,50 6,52 3,53 1,55 1,55 1,56 3,57 9,60 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="15" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="0" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.13333"/>
    <polyline points="58,100 64,100 " stroke-width="1.13333"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.13333"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26edd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_229e6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26efa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26f0710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26f1910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26f2520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26f30d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26f3b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26eee50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26eee50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26f6eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26f6eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26f8a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26f8a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_26f9aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26fb7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_26fc3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_26fd1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26fdb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26ff1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26ffee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27007a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2700f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2702040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27029c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27034b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2703e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27052a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2705e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2706d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27077b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2715f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27090a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_270a100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_270b650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1537" width="2799" x="3170" y="-1280"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4540,63 4539,63 4539,63 4538,63 4538,63 4537,63 4537,62 4537,62 4537,62 4536,61 4536,61 4536,60 4536,60 4536,59 4536,59 4536,58 4536,58 4537,57 4537,57 4537,56 4537,56 4538,56 4538,56 4539,55 4539,55 4540,55 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4540,71 4539,71 4539,71 4538,71 4538,71 4537,70 4537,70 4537,70 4537,69 4536,69 4536,68 4536,68 4536,67 4536,67 4536,66 4536,66 4536,65 4537,65 4537,65 4537,64 4537,64 4538,64 4538,63 4539,63 4539,63 4540,63 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4540,87 4539,87 4539,86 4538,86 4538,86 4537,86 4537,85 4537,85 4537,85 4536,84 4536,84 4536,83 4536,83 4536,82 4536,82 4536,81 4536,81 4537,80 4537,80 4537,80 4537,79 4538,79 4538,79 4539,79 4539,78 4540,78 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4540,94 4539,94 4539,94 4538,94 4538,94 4537,93 4537,93 4537,93 4537,92 4536,92 4536,91 4536,91 4536,90 4536,90 4536,89 4536,89 4536,88 4537,88 4537,88 4537,87 4537,87 4538,87 4538,86 4539,86 4539,86 4540,86 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4601,91 4600,90 4600,90 4599,90 4599,90 4599,90 4598,89 4598,89 4598,89 4597,88 4597,88 4597,87 4597,87 4597,86 4597,86 4597,85 4597,85 4598,84 4598,84 4598,84 4599,83 4599,83 4599,83 4600,83 4600,82 4601,82 " stroke="rgb(50,205,50)" stroke-width="0.0277671"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4826,68 4825,68 4825,68 4824,68 4824,68 4823,68 4823,67 4823,67 4823,67 4822,66 4822,66 4822,65 4822,65 4822,64 4822,64 4822,63 4822,63 4823,62 4823,62 4823,61 4823,61 4824,61 4824,61 4825,60 4825,60 4826,60 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4826,76 4825,76 4825,76 4824,76 4824,76 4823,75 4823,75 4823,75 4823,74 4822,74 4822,73 4822,73 4822,72 4822,72 4822,71 4822,71 4822,70 4823,70 4823,70 4823,69 4823,69 4824,69 4824,68 4825,68 4825,68 4826,68 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4826,92 4825,92 4825,91 4824,91 4824,91 4823,91 4823,90 4823,90 4823,90 4822,89 4822,89 4822,88 4822,88 4822,87 4822,87 4822,86 4822,86 4823,85 4823,85 4823,85 4823,84 4824,84 4824,84 4825,84 4825,83 4826,83 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4826,99 4825,99 4825,99 4824,99 4824,99 4823,98 4823,98 4823,98 4823,97 4822,97 4822,96 4822,96 4822,95 4822,95 4822,94 4822,94 4822,93 4823,93 4823,93 4823,92 4823,92 4824,92 4824,91 4825,91 4825,91 4826,91 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4887,96 4886,95 4886,95 4885,95 4885,95 4885,95 4884,94 4884,94 4884,94 4883,93 4883,93 4883,92 4883,92 4883,91 4883,91 4883,90 4883,90 4884,89 4884,89 4884,89 4885,88 4885,88 4885,88 4886,88 4886,87 4887,87 " stroke="rgb(50,205,50)" stroke-width="0.0277671"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="4531" x2="4531" y1="55" y2="71"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="4531" x2="4531" y1="78" y2="94"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4540" x2="4564" y1="94" y2="94"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4540" x2="4559" y1="79" y2="79"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4540" x2="4545" y1="71" y2="71"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4540" x2="4545" y1="56" y2="56"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4564" x2="4564" y1="99" y2="94"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4564" x2="4581" y1="100" y2="100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4581" y1="79" y2="79"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4601" x2="4601" y1="22" y2="87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.637931" x1="4596" x2="4601" y1="91" y2="103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4601" x2="4601" y1="103" y2="110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="4605" x2="4597" y1="124" y2="124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="4603" x2="4599" y1="126" y2="126"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.339777" x1="4602" x2="4600" y1="128" y2="128"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.197315" x1="4601" x2="4601" y1="121" y2="124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4581" x2="4581" y1="87" y2="79"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4585" x2="4581" y1="90" y2="90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4585" x2="4581" y1="90" y2="87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4581" x2="4581" y1="91" y2="100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4601" x2="4523" y1="22" y2="22"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4507" x2="4507" y1="22" y2="40"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4490" x2="4490" y1="22" y2="40"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4523" x2="4515" y1="30" y2="30"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4508" x2="4532" y1="73" y2="48"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="4817" x2="4817" y1="60" y2="76"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="4817" x2="4817" y1="83" y2="99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4826" x2="4850" y1="99" y2="99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4826" x2="4845" y1="84" y2="84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4826" x2="4831" y1="76" y2="76"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4826" x2="4831" y1="61" y2="61"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4850" x2="4850" y1="104" y2="99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4850" x2="4867" y1="105" y2="105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4855" x2="4867" y1="84" y2="84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4887" x2="4887" y1="27" y2="92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.637931" x1="4882" x2="4887" y1="96" y2="108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4887" x2="4887" y1="108" y2="115"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="4891" x2="4883" y1="129" y2="129"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="4889" x2="4885" y1="131" y2="131"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.339777" x1="4888" x2="4886" y1="133" y2="133"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.197315" x1="4887" x2="4887" y1="126" y2="129"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4867" x2="4867" y1="92" y2="84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4871" x2="4867" y1="95" y2="95"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4871" x2="4867" y1="95" y2="92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4867" x2="4867" y1="96" y2="105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4887" x2="4809" y1="27" y2="27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4793" x2="4793" y1="27" y2="45"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4776" x2="4776" y1="27" y2="45"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4809" x2="4801" y1="35" y2="35"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4794" x2="4818" y1="78" y2="53"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5962" x2="5962" y1="-350" y2="-315"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="58" stroke="rgb(0,255,0)" stroke-width="1" width="37" x="4511" y="43"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(0,255,0)" stroke-width="1" width="34" x="4554" y="72"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(0,255,0)" stroke-width="1" width="10" x="4559" y="75"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="11" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="4598" y="109"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="58" stroke="rgb(0,255,0)" stroke-width="1" width="37" x="4797" y="48"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(0,255,0)" stroke-width="1" width="34" x="4840" y="77"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(0,255,0)" stroke-width="1" width="10" x="4845" y="80"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="11" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="4884" y="114"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-39006">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 -878.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6240" ObjectName="SW-CX_TJT.CX_TJT_3411SW"/>
     <cge:Meas_Ref ObjectId="39006"/>
    <cge:TPSR_Ref TObjectID="6240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-98021">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 -970.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6238" ObjectName="SW-CX_TJT.CX_TJT_3416SW"/>
     <cge:Meas_Ref ObjectId="98021"/>
    <cge:TPSR_Ref TObjectID="6238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39005">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 -1025.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6239" ObjectName="SW-CX_TJT.CX_TJT_34167SW"/>
     <cge:Meas_Ref ObjectId="39005"/>
    <cge:TPSR_Ref TObjectID="6239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39007">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -1048.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6241" ObjectName="SW-CX_TJT.CX_TJT_3415SW"/>
     <cge:Meas_Ref ObjectId="39007"/>
    <cge:TPSR_Ref TObjectID="6241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39008">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 -1124.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6242" ObjectName="SW-CX_TJT.CX_TJT_3419SW"/>
     <cge:Meas_Ref ObjectId="39008"/>
    <cge:TPSR_Ref TObjectID="6242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39027">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -878.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6246" ObjectName="SW-CX_TJT.CX_TJT_3421SW"/>
     <cge:Meas_Ref ObjectId="39027"/>
    <cge:TPSR_Ref TObjectID="6246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39026">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -970.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6245" ObjectName="SW-CX_TJT.CX_TJT_3426SW"/>
     <cge:Meas_Ref ObjectId="39026"/>
    <cge:TPSR_Ref TObjectID="6245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39025">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4459.000000 -1025.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6244" ObjectName="SW-CX_TJT.CX_TJT_34267SW"/>
     <cge:Meas_Ref ObjectId="39025"/>
    <cge:TPSR_Ref TObjectID="6244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39028">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 -1048.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6247" ObjectName="SW-CX_TJT.CX_TJT_3425SW"/>
     <cge:Meas_Ref ObjectId="39028"/>
    <cge:TPSR_Ref TObjectID="6247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39029">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4458.000000 -1117.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6248" ObjectName="SW-CX_TJT.CX_TJT_3429SW"/>
     <cge:Meas_Ref ObjectId="39029"/>
    <cge:TPSR_Ref TObjectID="6248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38995">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -810.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6229" ObjectName="SW-CX_TJT.CX_TJT_3901SW"/>
     <cge:Meas_Ref ObjectId="38995"/>
    <cge:TPSR_Ref TObjectID="6229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38996">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 -797.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6230" ObjectName="SW-CX_TJT.CX_TJT_39017SW"/>
     <cge:Meas_Ref ObjectId="38996"/>
    <cge:TPSR_Ref TObjectID="6230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39002">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -811.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6236" ObjectName="SW-CX_TJT.CX_TJT_3801SW"/>
     <cge:Meas_Ref ObjectId="39002"/>
    <cge:TPSR_Ref TObjectID="6236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39065">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -917.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6256" ObjectName="SW-CX_TJT.CX_TJT_3151SW"/>
     <cge:Meas_Ref ObjectId="39065"/>
    <cge:TPSR_Ref TObjectID="6256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -1009.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6257" ObjectName="SW-CX_TJT.CX_TJT_3155SW"/>
     <cge:Meas_Ref ObjectId="39066"/>
    <cge:TPSR_Ref TObjectID="6257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39425">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -813.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6357" ObjectName="SW-CX_TJT.CX_TJT_3011SW"/>
     <cge:Meas_Ref ObjectId="39425"/>
    <cge:TPSR_Ref TObjectID="6357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39426">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -492.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6358" ObjectName="SW-CX_TJT.CX_TJT_0016SW"/>
     <cge:Meas_Ref ObjectId="39426"/>
    <cge:TPSR_Ref TObjectID="6358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39427">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -384.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6359" ObjectName="SW-CX_TJT.CX_TJT_0011SW"/>
     <cge:Meas_Ref ObjectId="39427"/>
    <cge:TPSR_Ref TObjectID="6359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39428">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -437.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6360" ObjectName="SW-CX_TJT.CX_TJT_00117SW"/>
     <cge:Meas_Ref ObjectId="39428"/>
    <cge:TPSR_Ref TObjectID="6360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39394">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3631.000000 -309.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6346" ObjectName="SW-CX_TJT.CX_TJT_0481SW"/>
     <cge:Meas_Ref ObjectId="39394"/>
    <cge:TPSR_Ref TObjectID="6346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39396">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3631.000000 -205.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6348" ObjectName="SW-CX_TJT.CX_TJT_0486SW"/>
     <cge:Meas_Ref ObjectId="39396"/>
    <cge:TPSR_Ref TObjectID="6348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39395">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3578.000000 -298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6347" ObjectName="SW-CX_TJT.CX_TJT_04817SW"/>
     <cge:Meas_Ref ObjectId="39395"/>
    <cge:TPSR_Ref TObjectID="6347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39397">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3576.000000 -193.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6349" ObjectName="SW-CX_TJT.CX_TJT_04867SW"/>
     <cge:Meas_Ref ObjectId="39397"/>
    <cge:TPSR_Ref TObjectID="6349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39341">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6337" ObjectName="SW-CX_TJT.CX_TJT_0151SW"/>
     <cge:Meas_Ref ObjectId="39341"/>
    <cge:TPSR_Ref TObjectID="6337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6339" ObjectName="SW-CX_TJT.CX_TJT_0155SW"/>
     <cge:Meas_Ref ObjectId="39343"/>
    <cge:TPSR_Ref TObjectID="6339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3699.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6338" ObjectName="SW-CX_TJT.CX_TJT_01517SW"/>
     <cge:Meas_Ref ObjectId="39342"/>
    <cge:TPSR_Ref TObjectID="6338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39049">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4681.000000 -1123.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6254" ObjectName="SW-CX_TJT.CX_TJT_3439SW"/>
     <cge:Meas_Ref ObjectId="39049"/>
    <cge:TPSR_Ref TObjectID="6254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39048">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -1048.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6253" ObjectName="SW-CX_TJT.CX_TJT_3435SW"/>
     <cge:Meas_Ref ObjectId="39048"/>
    <cge:TPSR_Ref TObjectID="6253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39047">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4729.000000 -878.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6252" ObjectName="SW-CX_TJT.CX_TJT_3431SW"/>
     <cge:Meas_Ref ObjectId="39047"/>
    <cge:TPSR_Ref TObjectID="6252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39045">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4729.000000 -970.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6250" ObjectName="SW-CX_TJT.CX_TJT_3436SW"/>
     <cge:Meas_Ref ObjectId="39045"/>
    <cge:TPSR_Ref TObjectID="6250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39046">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 -1025.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6251" ObjectName="SW-CX_TJT.CX_TJT_34367SW"/>
     <cge:Meas_Ref ObjectId="39046"/>
    <cge:TPSR_Ref TObjectID="6251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39201">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6295" ObjectName="SW-CX_TJT.CX_TJT_0471SW"/>
     <cge:Meas_Ref ObjectId="39201"/>
    <cge:TPSR_Ref TObjectID="6295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39203">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6297" ObjectName="SW-CX_TJT.CX_TJT_0472SW"/>
     <cge:Meas_Ref ObjectId="39203"/>
    <cge:TPSR_Ref TObjectID="6297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39202">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6296" ObjectName="SW-CX_TJT.CX_TJT_04717SW"/>
     <cge:Meas_Ref ObjectId="39202"/>
    <cge:TPSR_Ref TObjectID="6296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39204">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3829.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6298" ObjectName="SW-CX_TJT.CX_TJT_0475SW"/>
     <cge:Meas_Ref ObjectId="39204"/>
    <cge:TPSR_Ref TObjectID="6298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39181">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6289" ObjectName="SW-CX_TJT.CX_TJT_0461SW"/>
     <cge:Meas_Ref ObjectId="39181"/>
    <cge:TPSR_Ref TObjectID="6289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39183">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6291" ObjectName="SW-CX_TJT.CX_TJT_0462SW"/>
     <cge:Meas_Ref ObjectId="39183"/>
    <cge:TPSR_Ref TObjectID="6291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39182">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6290" ObjectName="SW-CX_TJT.CX_TJT_04617SW"/>
     <cge:Meas_Ref ObjectId="39182"/>
    <cge:TPSR_Ref TObjectID="6290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39184">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6292" ObjectName="SW-CX_TJT.CX_TJT_0465SW"/>
     <cge:Meas_Ref ObjectId="39184"/>
    <cge:TPSR_Ref TObjectID="6292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4097.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6283" ObjectName="SW-CX_TJT.CX_TJT_0451SW"/>
     <cge:Meas_Ref ObjectId="39161"/>
    <cge:TPSR_Ref TObjectID="6283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39163">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4097.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6285" ObjectName="SW-CX_TJT.CX_TJT_0452SW"/>
     <cge:Meas_Ref ObjectId="39163"/>
    <cge:TPSR_Ref TObjectID="6285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6284" ObjectName="SW-CX_TJT.CX_TJT_04517SW"/>
     <cge:Meas_Ref ObjectId="39162"/>
    <cge:TPSR_Ref TObjectID="6284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39164">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6286" ObjectName="SW-CX_TJT.CX_TJT_0455SW"/>
     <cge:Meas_Ref ObjectId="39164"/>
    <cge:TPSR_Ref TObjectID="6286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39141">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6277" ObjectName="SW-CX_TJT.CX_TJT_0441SW"/>
     <cge:Meas_Ref ObjectId="39141"/>
    <cge:TPSR_Ref TObjectID="6277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39143">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6279" ObjectName="SW-CX_TJT.CX_TJT_0442SW"/>
     <cge:Meas_Ref ObjectId="39143"/>
    <cge:TPSR_Ref TObjectID="6279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6278" ObjectName="SW-CX_TJT.CX_TJT_04417SW"/>
     <cge:Meas_Ref ObjectId="39142"/>
    <cge:TPSR_Ref TObjectID="6278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39144">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6280" ObjectName="SW-CX_TJT.CX_TJT_0445SW"/>
     <cge:Meas_Ref ObjectId="39144"/>
    <cge:TPSR_Ref TObjectID="6280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39121">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6271" ObjectName="SW-CX_TJT.CX_TJT_0431SW"/>
     <cge:Meas_Ref ObjectId="39121"/>
    <cge:TPSR_Ref TObjectID="6271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6273" ObjectName="SW-CX_TJT.CX_TJT_0432SW"/>
     <cge:Meas_Ref ObjectId="39123"/>
    <cge:TPSR_Ref TObjectID="6273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6272" ObjectName="SW-CX_TJT.CX_TJT_04317SW"/>
     <cge:Meas_Ref ObjectId="39122"/>
    <cge:TPSR_Ref TObjectID="6272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6274" ObjectName="SW-CX_TJT.CX_TJT_0435SW"/>
     <cge:Meas_Ref ObjectId="39124"/>
    <cge:TPSR_Ref TObjectID="6274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39101">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6265" ObjectName="SW-CX_TJT.CX_TJT_0421SW"/>
     <cge:Meas_Ref ObjectId="39101"/>
    <cge:TPSR_Ref TObjectID="6265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39103">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6267" ObjectName="SW-CX_TJT.CX_TJT_0422SW"/>
     <cge:Meas_Ref ObjectId="39103"/>
    <cge:TPSR_Ref TObjectID="6267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39102">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4381.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6266" ObjectName="SW-CX_TJT.CX_TJT_04217SW"/>
     <cge:Meas_Ref ObjectId="39102"/>
    <cge:TPSR_Ref TObjectID="6266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39104">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6268" ObjectName="SW-CX_TJT.CX_TJT_0425SW"/>
     <cge:Meas_Ref ObjectId="39104"/>
    <cge:TPSR_Ref TObjectID="6268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-98119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6259" ObjectName="SW-CX_TJT.CX_TJT_0411SW"/>
     <cge:Meas_Ref ObjectId="98119"/>
    <cge:TPSR_Ref TObjectID="6259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39085">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6263" ObjectName="SW-CX_TJT.CX_TJT_0416SW"/>
     <cge:Meas_Ref ObjectId="39085"/>
    <cge:TPSR_Ref TObjectID="6263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39082">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6260" ObjectName="SW-CX_TJT.CX_TJT_04117SW"/>
     <cge:Meas_Ref ObjectId="39082"/>
    <cge:TPSR_Ref TObjectID="6260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39084">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6262" ObjectName="SW-CX_TJT.CX_TJT_0415SW"/>
     <cge:Meas_Ref ObjectId="39084"/>
    <cge:TPSR_Ref TObjectID="6262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39241">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6307" ObjectName="SW-CX_TJT.CX_TJT_0521SW"/>
     <cge:Meas_Ref ObjectId="39241"/>
    <cge:TPSR_Ref TObjectID="6307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39243">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6309" ObjectName="SW-CX_TJT.CX_TJT_0522SW"/>
     <cge:Meas_Ref ObjectId="39243"/>
    <cge:TPSR_Ref TObjectID="6309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39242">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6308" ObjectName="SW-CX_TJT.CX_TJT_05217SW"/>
     <cge:Meas_Ref ObjectId="39242"/>
    <cge:TPSR_Ref TObjectID="6308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39244">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4919.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6310" ObjectName="SW-CX_TJT.CX_TJT_0525SW"/>
     <cge:Meas_Ref ObjectId="39244"/>
    <cge:TPSR_Ref TObjectID="6310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5072.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6313" ObjectName="SW-CX_TJT.CX_TJT_0531SW"/>
     <cge:Meas_Ref ObjectId="39261"/>
    <cge:TPSR_Ref TObjectID="6313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5072.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6315" ObjectName="SW-CX_TJT.CX_TJT_0532SW"/>
     <cge:Meas_Ref ObjectId="39263"/>
    <cge:TPSR_Ref TObjectID="6315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5018.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6314" ObjectName="SW-CX_TJT.CX_TJT_05317SW"/>
     <cge:Meas_Ref ObjectId="39262"/>
    <cge:TPSR_Ref TObjectID="6314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5031.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6316" ObjectName="SW-CX_TJT.CX_TJT_0535SW"/>
     <cge:Meas_Ref ObjectId="39264"/>
    <cge:TPSR_Ref TObjectID="6316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5183.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6319" ObjectName="SW-CX_TJT.CX_TJT_0541SW"/>
     <cge:Meas_Ref ObjectId="39281"/>
    <cge:TPSR_Ref TObjectID="6319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39283">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5183.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6321" ObjectName="SW-CX_TJT.CX_TJT_0542SW"/>
     <cge:Meas_Ref ObjectId="39283"/>
    <cge:TPSR_Ref TObjectID="6321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5130.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6320" ObjectName="SW-CX_TJT.CX_TJT_05417SW"/>
     <cge:Meas_Ref ObjectId="39282"/>
    <cge:TPSR_Ref TObjectID="6320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39284">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5143.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6322" ObjectName="SW-CX_TJT.CX_TJT_0545SW"/>
     <cge:Meas_Ref ObjectId="39284"/>
    <cge:TPSR_Ref TObjectID="6322"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5296.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6325" ObjectName="SW-CX_TJT.CX_TJT_0551SW"/>
     <cge:Meas_Ref ObjectId="39301"/>
    <cge:TPSR_Ref TObjectID="6325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5296.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6327" ObjectName="SW-CX_TJT.CX_TJT_0552SW"/>
     <cge:Meas_Ref ObjectId="39303"/>
    <cge:TPSR_Ref TObjectID="6327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5243.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6326" ObjectName="SW-CX_TJT.CX_TJT_05517SW"/>
     <cge:Meas_Ref ObjectId="39302"/>
    <cge:TPSR_Ref TObjectID="6326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5256.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6328" ObjectName="SW-CX_TJT.CX_TJT_0555SW"/>
     <cge:Meas_Ref ObjectId="39304"/>
    <cge:TPSR_Ref TObjectID="6328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39321">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5410.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6331" ObjectName="SW-CX_TJT.CX_TJT_0561SW"/>
     <cge:Meas_Ref ObjectId="39321"/>
    <cge:TPSR_Ref TObjectID="6331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5410.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6333" ObjectName="SW-CX_TJT.CX_TJT_0562SW"/>
     <cge:Meas_Ref ObjectId="39323"/>
    <cge:TPSR_Ref TObjectID="6333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5356.000000 -295.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6332" ObjectName="SW-CX_TJT.CX_TJT_05617SW"/>
     <cge:Meas_Ref ObjectId="39322"/>
    <cge:TPSR_Ref TObjectID="6332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5369.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6334" ObjectName="SW-CX_TJT.CX_TJT_0565SW"/>
     <cge:Meas_Ref ObjectId="39324"/>
    <cge:TPSR_Ref TObjectID="6334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5465.000000 -304.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6341" ObjectName="SW-CX_TJT.CX_TJT_0251SW"/>
     <cge:Meas_Ref ObjectId="39359"/>
    <cge:TPSR_Ref TObjectID="6341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39361">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5465.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6343" ObjectName="SW-CX_TJT.CX_TJT_0255SW"/>
     <cge:Meas_Ref ObjectId="39361"/>
    <cge:TPSR_Ref TObjectID="6343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5490.000000 -293.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6342" ObjectName="SW-CX_TJT.CX_TJT_02517SW"/>
     <cge:Meas_Ref ObjectId="39360"/>
    <cge:TPSR_Ref TObjectID="6342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39409">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5583.000000 -305.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6351" ObjectName="SW-CX_TJT.CX_TJT_0571SW"/>
     <cge:Meas_Ref ObjectId="39409"/>
    <cge:TPSR_Ref TObjectID="6351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39411">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5583.000000 -201.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6353" ObjectName="SW-CX_TJT.CX_TJT_0576SW"/>
     <cge:Meas_Ref ObjectId="39411"/>
    <cge:TPSR_Ref TObjectID="6353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39410">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5610.000000 -293.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6352" ObjectName="SW-CX_TJT.CX_TJT_05717SW"/>
     <cge:Meas_Ref ObjectId="39410"/>
    <cge:TPSR_Ref TObjectID="6352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5609.000000 -188.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6354" ObjectName="SW-CX_TJT.CX_TJT_05767SW"/>
     <cge:Meas_Ref ObjectId="39412"/>
    <cge:TPSR_Ref TObjectID="6354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11135" ObjectName="SW-CX_TJT.CX_TJT_0122SW"/>
     <cge:Meas_Ref ObjectId="39378"/>
    <cge:TPSR_Ref TObjectID="11135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39377">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4718.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11134" ObjectName="SW-CX_TJT.CX_TJT_0121SW"/>
     <cge:Meas_Ref ObjectId="39377"/>
    <cge:TPSR_Ref TObjectID="11134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-98466">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4718.000000 -233.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11136" ObjectName="SW-CX_TJT.CX_TJT_01217SW"/>
     <cge:Meas_Ref ObjectId="98466"/>
    <cge:TPSR_Ref TObjectID="11136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6231" ObjectName="SW-CX_TJT.CX_TJT_0901SW"/>
     <cge:Meas_Ref ObjectId="38997"/>
    <cge:TPSR_Ref TObjectID="6231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38998">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -434.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6232" ObjectName="SW-CX_TJT.CX_TJT_09017SW"/>
     <cge:Meas_Ref ObjectId="38998"/>
    <cge:TPSR_Ref TObjectID="6232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38999">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5205.000000 -384.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6233" ObjectName="SW-CX_TJT.CX_TJT_0902SW"/>
     <cge:Meas_Ref ObjectId="38999"/>
    <cge:TPSR_Ref TObjectID="6233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39000">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 -435.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6234" ObjectName="SW-CX_TJT.CX_TJT_09027SW"/>
     <cge:Meas_Ref ObjectId="39000"/>
    <cge:TPSR_Ref TObjectID="6234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39205">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 -17.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6299" ObjectName="SW-CX_TJT.CX_TJT_0476SW"/>
     <cge:Meas_Ref ObjectId="39205"/>
    <cge:TPSR_Ref TObjectID="6299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -15.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6311" ObjectName="SW-CX_TJT.CX_TJT_0526SW"/>
     <cge:Meas_Ref ObjectId="39245"/>
    <cge:TPSR_Ref TObjectID="6311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39265">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5072.000000 -16.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6317" ObjectName="SW-CX_TJT.CX_TJT_0536SW"/>
     <cge:Meas_Ref ObjectId="39265"/>
    <cge:TPSR_Ref TObjectID="6317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5183.000000 -16.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6323" ObjectName="SW-CX_TJT.CX_TJT_0546SW"/>
     <cge:Meas_Ref ObjectId="39285"/>
    <cge:TPSR_Ref TObjectID="6323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5410.000000 -17.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6335" ObjectName="SW-CX_TJT.CX_TJT_0566SW"/>
     <cge:Meas_Ref ObjectId="39325"/>
    <cge:TPSR_Ref TObjectID="6335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-98125">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4514.000000 21.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31724" ObjectName="SW-CX_TJT.CX_TJT_0010SW"/>
     <cge:Meas_Ref ObjectId="98125"/>
    <cge:TPSR_Ref TObjectID="31724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-98126">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.000000 26.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31725" ObjectName="SW-CX_TJT.CX_TJT_0020SW"/>
     <cge:Meas_Ref ObjectId="98126"/>
    <cge:TPSR_Ref TObjectID="31725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39221">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4833.000000 -309.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6303" ObjectName="SW-CX_TJT.CX_TJT_0512SW"/>
     <cge:Meas_Ref ObjectId="39221"/>
    <cge:TPSR_Ref TObjectID="6303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39222">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -294.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6302" ObjectName="SW-CX_TJT.CX_TJT_05127SW"/>
     <cge:Meas_Ref ObjectId="39222"/>
    <cge:TPSR_Ref TObjectID="6302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39223">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -190.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6305" ObjectName="SW-CX_TJT.CX_TJT_0516SW"/>
     <cge:Meas_Ref ObjectId="39223"/>
    <cge:TPSR_Ref TObjectID="6305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39224">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4791.000000 -123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6304" ObjectName="SW-CX_TJT.CX_TJT_0515SW"/>
     <cge:Meas_Ref ObjectId="39224"/>
    <cge:TPSR_Ref TObjectID="6304"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_TJT.CX_TJT_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-867 4805,-867 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6228" ObjectName="BS-CX_TJT.CX_TJT_3IM"/>
    <cge:TPSR_Ref TObjectID="6228"/></metadata>
   <polyline fill="none" opacity="0" points="4105,-867 4805,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TJT.CX_TJT_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-365 4641,-365 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11140" ObjectName="BS-CX_TJT.CX_TJT_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="11140"/></metadata>
   <polyline fill="none" opacity="0" points="3589,-365 4641,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TJT.CX_TJT_9ⅠPM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3708,-183 4577,-183 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11141" ObjectName="BS-CX_TJT.CX_TJT_9ⅠPM"/>
    <cge:TPSR_Ref TObjectID="11141"/></metadata>
   <polyline fill="none" opacity="0" points="3708,-183 4577,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TJT.CX_TJT_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4693,-365 5642,-365 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11138" ObjectName="BS-CX_TJT.CX_TJT_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="11138"/></metadata>
   <polyline fill="none" opacity="0" points="4693,-365 5642,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TJT.CX_TJT_9ⅡPm">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-183 5487,-183 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11139" ObjectName="BS-CX_TJT.CX_TJT_9ⅡPm"/>
    <cge:TPSR_Ref TObjectID="11139"/></metadata>
   <polyline fill="none" opacity="0" points="4753,-183 5487,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TJT.CX_TJT_Pm">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4112,-1103 4814,-1103 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11089" ObjectName="BS-CX_TJT.CX_TJT_Pm"/>
    <cge:TPSR_Ref TObjectID="11089"/></metadata>
   <polyline fill="none" opacity="0" points="4112,-1103 4814,-1103 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_TJT.CX_TJT_048Cb">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.000000 22.000000)" xlink:href="#capacitor:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40677" ObjectName="CB-CX_TJT.CX_TJT_048Cb"/>
    <cge:TPSR_Ref TObjectID="40677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_TJT.CX_TJT_057Cb">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5525.000000 20.000000)" xlink:href="#capacitor:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40678" ObjectName="CB-CX_TJT.CX_TJT_057Cb"/>
    <cge:TPSR_Ref TObjectID="40678"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 -663.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 -663.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_TJT.CX_TJT_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9748"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4122.000000 -616.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 4122.000000 -616.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6361" ObjectName="TF-CX_TJT.CX_TJT_1T"/>
    <cge:TPSR_Ref TObjectID="6361"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_18b89e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4232.500000 -1120.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b2f5e0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4143.500000 -1141.500000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbd420">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4364.500000 -1134.500000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f1da0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4451.500000 -1113.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18a3ec0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -754.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bab360">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -767.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b6f9c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -547.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b69fa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3635.000000 -126.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b08030">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4587.500000 -1140.500000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b083c0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4674.500000 -1119.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1baf900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 -63.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b879d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 -54.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b409d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.000000 -54.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aefe60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4213.000000 -54.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aeb050">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -54.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b79ae0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -54.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b19df0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4551.000000 -54.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ae95b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 -61.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b37dc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5076.000000 -63.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acf860">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5187.000000 -62.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab75f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5300.000000 -61.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a62820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5414.000000 -63.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a8a780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5587.000000 -122.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a19500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5180.000000 -551.000000)" xlink:href="#lightningRod:shape114"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a97b00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4464.000000 -736.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a3acf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4369.000000 -550.000000)" xlink:href="#lightningRod:shape114"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d65670">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4289.000000 -1140.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d66620">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 -1134.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d67370">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4733.000000 -1139.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d760e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4589.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d778f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 36.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d79ad0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4514.000000 115.000000)" xlink:href="#lightningRod:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d7e3b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4836.000000 -50.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d82e70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 41.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d84660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.000000 120.000000)" xlink:href="#lightningRod:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d92580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dae1c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.000000 -1200.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dafee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.000000 -1202.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1db1ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 -1210.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3279.500000 -1135.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-38994" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4285.000000 -636.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38994" ObjectName="CX_TJT:CX_TJT_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38983" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -476.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38983" ObjectName="CX_TJT:CX_TJT_001BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38984" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -460.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38984" ObjectName="CX_TJT:CX_TJT_001BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38985" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -444.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38985" ObjectName="CX_TJT:CX_TJT_001BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38987" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -428.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38987" ObjectName="CX_TJT:CX_TJT_001BK_3U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38986" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -412.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38986" ObjectName="CX_TJT:CX_TJT_001BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38983" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5620.000000 -472.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38983" ObjectName="CX_TJT:CX_TJT_001BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38984" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5620.000000 -456.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38984" ObjectName="CX_TJT:CX_TJT_001BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38985" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5620.000000 -440.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38985" ObjectName="CX_TJT:CX_TJT_001BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38987" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5620.000000 -424.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38987" ObjectName="CX_TJT:CX_TJT_001BK_3U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38986" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5620.000000 -408.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38986" ObjectName="CX_TJT:CX_TJT_001BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38840" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 192.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38840" ObjectName="CX_TJT:CX_TJT_051BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38841" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 207.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38841" ObjectName="CX_TJT:CX_TJT_051BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38762" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 222.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38762" ObjectName="CX_TJT:CX_TJT_051BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38842" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 237.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38842" ObjectName="CX_TJT:CX_TJT_051BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38972" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4043.000000 -898.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38972" ObjectName="CX_TJT:CX_TJT_301BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38973" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4043.000000 -874.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38973" ObjectName="CX_TJT:CX_TJT_301BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38974" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4043.000000 -852.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38974" ObjectName="CX_TJT:CX_TJT_301BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-38976" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4043.000000 -831.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38976" ObjectName="CX_TJT:CX_TJT_301BK_3U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-40748" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4043.000000 -810.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40748" ObjectName="CX_TJT:CX_TJT_301BK_Ubc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-97973" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -1158.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97973" ObjectName="CX_TJT:CX_TJT_341BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-97974" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -1158.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97974" ObjectName="CX_TJT:CX_TJT_342BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-97975" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -1158.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97975" ObjectName="CX_TJT:CX_TJT_343BK_U"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38752" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -983.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6255"/>
     <cge:Term_Ref ObjectID="9534"/>
    <cge:TPSR_Ref TObjectID="6255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -983.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6255"/>
     <cge:Term_Ref ObjectID="9534"/>
    <cge:TPSR_Ref TObjectID="6255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -983.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6255"/>
     <cge:Term_Ref ObjectID="9534"/>
    <cge:TPSR_Ref TObjectID="6255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -983.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6255"/>
     <cge:Term_Ref ObjectID="9534"/>
    <cge:TPSR_Ref TObjectID="6255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38716" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -980.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38716" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6237"/>
     <cge:Term_Ref ObjectID="9498"/>
    <cge:TPSR_Ref TObjectID="6237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -980.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6237"/>
     <cge:Term_Ref ObjectID="9498"/>
    <cge:TPSR_Ref TObjectID="6237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38713" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -980.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6237"/>
     <cge:Term_Ref ObjectID="9498"/>
    <cge:TPSR_Ref TObjectID="6237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38718" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -980.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6237"/>
     <cge:Term_Ref ObjectID="9498"/>
    <cge:TPSR_Ref TObjectID="6237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38728" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -979.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38728" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6243"/>
     <cge:Term_Ref ObjectID="9510"/>
    <cge:TPSR_Ref TObjectID="6243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -979.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6243"/>
     <cge:Term_Ref ObjectID="9510"/>
    <cge:TPSR_Ref TObjectID="6243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38725" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -979.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38725" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6243"/>
     <cge:Term_Ref ObjectID="9510"/>
    <cge:TPSR_Ref TObjectID="6243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38730" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -979.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38730" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6243"/>
     <cge:Term_Ref ObjectID="9510"/>
    <cge:TPSR_Ref TObjectID="6243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4851.000000 -982.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6249"/>
     <cge:Term_Ref ObjectID="9522"/>
    <cge:TPSR_Ref TObjectID="6249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4851.000000 -982.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6249"/>
     <cge:Term_Ref ObjectID="9522"/>
    <cge:TPSR_Ref TObjectID="6249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4851.000000 -982.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6249"/>
     <cge:Term_Ref ObjectID="9522"/>
    <cge:TPSR_Ref TObjectID="6249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-97976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4851.000000 -982.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6249"/>
     <cge:Term_Ref ObjectID="9522"/>
    <cge:TPSR_Ref TObjectID="6249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38980" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4276.000000 -818.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38980" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6355"/>
     <cge:Term_Ref ObjectID="9734"/>
    <cge:TPSR_Ref TObjectID="6355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4276.000000 -818.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6355"/>
     <cge:Term_Ref ObjectID="9734"/>
    <cge:TPSR_Ref TObjectID="6355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38977" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4276.000000 -818.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6355"/>
     <cge:Term_Ref ObjectID="9734"/>
    <cge:TPSR_Ref TObjectID="6355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4276.000000 -818.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6355"/>
     <cge:Term_Ref ObjectID="9734"/>
    <cge:TPSR_Ref TObjectID="6355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3642.628571 61.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6345"/>
     <cge:Term_Ref ObjectID="9714"/>
    <cge:TPSR_Ref TObjectID="6345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3642.628571 61.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6345"/>
     <cge:Term_Ref ObjectID="9714"/>
    <cge:TPSR_Ref TObjectID="6345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38969" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5622.000000 67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6350"/>
     <cge:Term_Ref ObjectID="9724"/>
    <cge:TPSR_Ref TObjectID="6350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5622.000000 67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6350"/>
     <cge:Term_Ref ObjectID="9724"/>
    <cge:TPSR_Ref TObjectID="6350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-38912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.014747 -172.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6336"/>
     <cge:Term_Ref ObjectID="9696"/>
    <cge:TPSR_Ref TObjectID="6336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-38913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.014747 -172.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6336"/>
     <cge:Term_Ref ObjectID="9696"/>
    <cge:TPSR_Ref TObjectID="6336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-38914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.014747 -172.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6336"/>
     <cge:Term_Ref ObjectID="9696"/>
    <cge:TPSR_Ref TObjectID="6336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-38915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.014747 -172.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6336"/>
     <cge:Term_Ref ObjectID="9696"/>
    <cge:TPSR_Ref TObjectID="6336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38836" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3871.729954 41.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38836" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6294"/>
     <cge:Term_Ref ObjectID="9612"/>
    <cge:TPSR_Ref TObjectID="6294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38837" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3871.729954 41.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38837" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6294"/>
     <cge:Term_Ref ObjectID="9612"/>
    <cge:TPSR_Ref TObjectID="6294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38833" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3871.729954 41.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6294"/>
     <cge:Term_Ref ObjectID="9612"/>
    <cge:TPSR_Ref TObjectID="6294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38838" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3871.729954 41.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38838" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6294"/>
     <cge:Term_Ref ObjectID="9612"/>
    <cge:TPSR_Ref TObjectID="6294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38824" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.445161 42.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6288"/>
     <cge:Term_Ref ObjectID="9600"/>
    <cge:TPSR_Ref TObjectID="6288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38825" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.445161 42.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6288"/>
     <cge:Term_Ref ObjectID="9600"/>
    <cge:TPSR_Ref TObjectID="6288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38821" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.445161 42.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6288"/>
     <cge:Term_Ref ObjectID="9600"/>
    <cge:TPSR_Ref TObjectID="6288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38826" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.445161 42.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6288"/>
     <cge:Term_Ref ObjectID="9600"/>
    <cge:TPSR_Ref TObjectID="6288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38812" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4104.160369 42.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6282"/>
     <cge:Term_Ref ObjectID="9588"/>
    <cge:TPSR_Ref TObjectID="6282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38813" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4104.160369 42.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38813" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6282"/>
     <cge:Term_Ref ObjectID="9588"/>
    <cge:TPSR_Ref TObjectID="6282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38809" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4104.160369 42.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38809" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6282"/>
     <cge:Term_Ref ObjectID="9588"/>
    <cge:TPSR_Ref TObjectID="6282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4104.160369 42.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6282"/>
     <cge:Term_Ref ObjectID="9588"/>
    <cge:TPSR_Ref TObjectID="6282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38800" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.875576 43.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6276"/>
     <cge:Term_Ref ObjectID="9576"/>
    <cge:TPSR_Ref TObjectID="6276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.875576 43.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6276"/>
     <cge:Term_Ref ObjectID="9576"/>
    <cge:TPSR_Ref TObjectID="6276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38797" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.875576 43.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6276"/>
     <cge:Term_Ref ObjectID="9576"/>
    <cge:TPSR_Ref TObjectID="6276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.875576 43.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6276"/>
     <cge:Term_Ref ObjectID="9576"/>
    <cge:TPSR_Ref TObjectID="6276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.590783 42.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6270"/>
     <cge:Term_Ref ObjectID="9564"/>
    <cge:TPSR_Ref TObjectID="6270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.590783 42.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6270"/>
     <cge:Term_Ref ObjectID="9564"/>
    <cge:TPSR_Ref TObjectID="6270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.590783 42.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6270"/>
     <cge:Term_Ref ObjectID="9564"/>
    <cge:TPSR_Ref TObjectID="6270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.590783 42.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6270"/>
     <cge:Term_Ref ObjectID="9564"/>
    <cge:TPSR_Ref TObjectID="6270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.305991 43.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6264"/>
     <cge:Term_Ref ObjectID="9552"/>
    <cge:TPSR_Ref TObjectID="6264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.305991 43.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6264"/>
     <cge:Term_Ref ObjectID="9552"/>
    <cge:TPSR_Ref TObjectID="6264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.305991 43.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6264"/>
     <cge:Term_Ref ObjectID="9552"/>
    <cge:TPSR_Ref TObjectID="6264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.305991 43.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6264"/>
     <cge:Term_Ref ObjectID="9552"/>
    <cge:TPSR_Ref TObjectID="6264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38760" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.021198 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38760" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6258"/>
     <cge:Term_Ref ObjectID="9540"/>
    <cge:TPSR_Ref TObjectID="6258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40713" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.021198 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6258"/>
     <cge:Term_Ref ObjectID="9540"/>
    <cge:TPSR_Ref TObjectID="6258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38755" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.021198 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38755" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6258"/>
     <cge:Term_Ref ObjectID="9540"/>
    <cge:TPSR_Ref TObjectID="6258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38761" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.021198 30.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38761" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6258"/>
     <cge:Term_Ref ObjectID="9540"/>
    <cge:TPSR_Ref TObjectID="6258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.736406 -258.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6344"/>
     <cge:Term_Ref ObjectID="9712"/>
    <cge:TPSR_Ref TObjectID="6344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.736406 -258.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6344"/>
     <cge:Term_Ref ObjectID="9712"/>
    <cge:TPSR_Ref TObjectID="6344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.736406 -258.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6344"/>
     <cge:Term_Ref ObjectID="9712"/>
    <cge:TPSR_Ref TObjectID="6344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.736406 -258.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6344"/>
     <cge:Term_Ref ObjectID="9712"/>
    <cge:TPSR_Ref TObjectID="6344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38860" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.166820 41.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6306"/>
     <cge:Term_Ref ObjectID="9636"/>
    <cge:TPSR_Ref TObjectID="6306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.166820 41.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6306"/>
     <cge:Term_Ref ObjectID="9636"/>
    <cge:TPSR_Ref TObjectID="6306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38857" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.166820 41.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6306"/>
     <cge:Term_Ref ObjectID="9636"/>
    <cge:TPSR_Ref TObjectID="6306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38862" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.166820 41.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38862" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6306"/>
     <cge:Term_Ref ObjectID="9636"/>
    <cge:TPSR_Ref TObjectID="6306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5077.882028 42.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6312"/>
     <cge:Term_Ref ObjectID="9648"/>
    <cge:TPSR_Ref TObjectID="6312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5077.882028 42.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6312"/>
     <cge:Term_Ref ObjectID="9648"/>
    <cge:TPSR_Ref TObjectID="6312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5077.882028 42.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6312"/>
     <cge:Term_Ref ObjectID="9648"/>
    <cge:TPSR_Ref TObjectID="6312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5077.882028 42.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6312"/>
     <cge:Term_Ref ObjectID="9648"/>
    <cge:TPSR_Ref TObjectID="6312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5197.597235 43.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6318"/>
     <cge:Term_Ref ObjectID="9660"/>
    <cge:TPSR_Ref TObjectID="6318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5197.597235 43.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6318"/>
     <cge:Term_Ref ObjectID="9660"/>
    <cge:TPSR_Ref TObjectID="6318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5197.597235 43.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6318"/>
     <cge:Term_Ref ObjectID="9660"/>
    <cge:TPSR_Ref TObjectID="6318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5197.597235 43.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6318"/>
     <cge:Term_Ref ObjectID="9660"/>
    <cge:TPSR_Ref TObjectID="6318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5305.312442 40.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6324"/>
     <cge:Term_Ref ObjectID="9672"/>
    <cge:TPSR_Ref TObjectID="6324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5305.312442 40.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6324"/>
     <cge:Term_Ref ObjectID="9672"/>
    <cge:TPSR_Ref TObjectID="6324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5305.312442 40.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6324"/>
     <cge:Term_Ref ObjectID="9672"/>
    <cge:TPSR_Ref TObjectID="6324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5305.312442 40.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6324"/>
     <cge:Term_Ref ObjectID="9672"/>
    <cge:TPSR_Ref TObjectID="6324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5425.027650 41.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6330"/>
     <cge:Term_Ref ObjectID="9684"/>
    <cge:TPSR_Ref TObjectID="6330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5425.027650 41.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6330"/>
     <cge:Term_Ref ObjectID="9684"/>
    <cge:TPSR_Ref TObjectID="6330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5425.027650 41.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6330"/>
     <cge:Term_Ref ObjectID="9684"/>
    <cge:TPSR_Ref TObjectID="6330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5425.027650 41.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6330"/>
     <cge:Term_Ref ObjectID="9684"/>
    <cge:TPSR_Ref TObjectID="6330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -493.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6356"/>
     <cge:Term_Ref ObjectID="9736"/>
    <cge:TPSR_Ref TObjectID="6356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38992" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -493.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6356"/>
     <cge:Term_Ref ObjectID="9736"/>
    <cge:TPSR_Ref TObjectID="6356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38988" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -493.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6356"/>
     <cge:Term_Ref ObjectID="9736"/>
    <cge:TPSR_Ref TObjectID="6356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-38993" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -493.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6356"/>
     <cge:Term_Ref ObjectID="9736"/>
    <cge:TPSR_Ref TObjectID="6356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-38924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5503.000000 -173.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6340"/>
     <cge:Term_Ref ObjectID="9704"/>
    <cge:TPSR_Ref TObjectID="6340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-38925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5503.000000 -173.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6340"/>
     <cge:Term_Ref ObjectID="9704"/>
    <cge:TPSR_Ref TObjectID="6340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-38926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5503.000000 -173.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6340"/>
     <cge:Term_Ref ObjectID="9704"/>
    <cge:TPSR_Ref TObjectID="6340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-38927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5503.000000 -173.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6340"/>
     <cge:Term_Ref ObjectID="9704"/>
    <cge:TPSR_Ref TObjectID="6340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-106027" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -651.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106027" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6361"/>
     <cge:Term_Ref ObjectID="9746"/>
    <cge:TPSR_Ref TObjectID="6361"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3291" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3291" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3243" y="-1211"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3243" y="-1211"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4167" y="-992"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4167" y="-992"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4303" y="-953"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4303" y="-953"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4524" y="-953"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4524" y="-953"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4747" y="-953"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4747" y="-953"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3649" y="-283"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3649" y="-283"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3770" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3770" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3887" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3887" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4001" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4001" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4115" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4115" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4227" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4227" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4339" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4339" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4452" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4452" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4565" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4565" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4662" y="-318"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4662" y="-318"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4977" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4977" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5089" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5089" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5201" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5201" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5314" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5314" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5427" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5427" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5483" y="-278"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5483" y="-278"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5601" y="-279"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5601" y="-279"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="61" x="3203" y="-751"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="61" x="3203" y="-751"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3483" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3483" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3483" y="-1212"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3483" y="-1212"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4851" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4851" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3535,-1137 3532,-1140 3532,-1086 3535,-1089 3535,-1137" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3535,-1137 3532,-1140 3596,-1140 3593,-1137 3535,-1137" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3535,-1089 3532,-1086 3596,-1086 3593,-1089 3535,-1089" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3593,-1137 3596,-1140 3596,-1086 3593,-1089 3593,-1137" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="48" stroke="rgb(255,255,255)" width="58" x="3535" y="-1137"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(0,0,0)" width="58" x="3535" y="-1137"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="99" x="4199" y="-694"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="99" x="4199" y="-694"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3175" y="-706"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3175" y="-706"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3291" y="-1194"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3243" y="-1211"/></g>
   <g href="35kV田家屯变35kV旁母315断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4167" y="-992"/></g>
   <g href="35kV田家屯变35kV东田线341断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4303" y="-953"/></g>
   <g href="35kV田家屯变35kV田马线342断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4524" y="-953"/></g>
   <g href="35kV田家屯变35kV白田线343断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4747" y="-953"/></g>
   <g href="35kV田家屯变10kV电容器组048断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3649" y="-283"/></g>
   <g href="35kV田家屯变10kVⅠ段旁母015断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3770" y="-280"/></g>
   <g href="35kV田家屯变10kV太阳历公园线047断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3887" y="-280"/></g>
   <g href="35kV田家屯变10kV云南开关厂线046断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4001" y="-280"/></g>
   <g href="35kV田家屯变10kV车坪线045断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4115" y="-280"/></g>
   <g href="35kV田家屯变10kV永盛花园线044断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4227" y="-280"/></g>
   <g href="35kV田家屯变10kV东瓜Ⅳ回线043断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4339" y="-280"/></g>
   <g href="35kV田家屯变10kV赵家湾线042断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4452" y="-280"/></g>
   <g href="35kV田家屯变10kV1号接地变及消弧线圈041断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4565" y="-280"/></g>
   <g href="35kV田家屯变10kV母线分段012断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4662" y="-318"/></g>
   <g href="35kV田家屯变10kV田东联络线052断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4977" y="-280"/></g>
   <g href="35kV田家屯变10kV公司Ⅱ回线053断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5089" y="-280"/></g>
   <g href="35kV田家屯变10kV城区线054断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5201" y="-280"/></g>
   <g href="35kV田家屯变10kV东瓜Ⅴ回线055断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5314" y="-280"/></g>
   <g href="35kV田家屯变10kV彝人古镇线056断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5427" y="-280"/></g>
   <g href="35kV田家屯变10kVⅡ段旁母025断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5483" y="-278"/></g>
   <g href="35kV田家屯变10kV电容器组057断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5601" y="-279"/></g>
   <g href="35kV田家屯变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="61" x="3203" y="-751"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3483" y="-1177"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3483" y="-1212"/></g>
   <g href="35kV田家屯变10kV2号接地变及消弧线圈051断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4851" y="-280"/></g>
   <g href="AVC田家屯站.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(0,0,0)" width="58" x="3535" y="-1137"/></g>
   <g href="35kV田家屯变35kV1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="99" x="4199" y="-694"/></g>
   <g href="35kV田家屯变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3175" y="-706"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5554.371429 98.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a452b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 30.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a46cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d558a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3581.000000 -61.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d55e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 -76.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d561c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 939.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d56d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.257143 969.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d56fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.257143 954.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d57210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4050.257143 985.000000) translate(0,12)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d57b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 774.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d57df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4205.257143 804.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5ea80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.257143 789.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5ecc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.257143 820.000000) translate(0,12)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5eff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 447.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5f260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4205.257143 477.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5f4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.257143 462.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5f6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.257143 493.000000) translate(0,12)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5fe40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.257143 174.000000) translate(0,12)">Ua(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d601f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.257143 158.333333) translate(0,12)">Ub(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d60690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.257143 142.666667) translate(0,12)">Uc(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d60c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3699.257143 127.000000) translate(0,12)">Uab(KV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d613c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5442.257143 175.000000) translate(0,12)">Ua(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5442.257143 159.333333) translate(0,12)">Ub(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5442.257143 143.666667) translate(0,12)">Uc(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5434.257143 128.000000) translate(0,12)">Uab(KV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6d4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.257143 474.000000) translate(0,12)">Ua(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6dac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.257143 458.833333) translate(0,12)">Ub(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6dd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.257143 443.666667) translate(0,12)">Uc(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6df40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3565.257143 412.000000) translate(0,12)">Uab(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6e180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3582.257143 428.166667) translate(0,12)">Uo(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6f9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5562.257143 471.000000) translate(0,12)">Ua(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6fc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5562.257143 455.833333) translate(0,12)">Ub(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6fe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5562.257143 440.666667) translate(0,12)">Uc(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d70090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5554.257143 409.000000) translate(0,12)">Uab(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d702d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5571.257143 425.166667) translate(0,12)">Uo(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db6dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4372.000000 936.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.257143 966.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4367.257143 951.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db75b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.257143 982.000000) translate(0,12)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db78e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4591.000000 935.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4563.257143 965.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.257143 950.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.257143 981.000000) translate(0,12)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db8300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4814.000000 935.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db8570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.257143 965.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db87b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.257143 950.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db89f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.257143 981.000000) translate(0,12)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1db8d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -87.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1db9a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3812.257143 -56.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbb450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.257143 -71.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbbd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.257143 -41.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbc6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -88.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbc970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.257143 -57.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbcbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.257143 -72.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbcdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.257143 -42.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbd120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -87.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbd390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.257143 -56.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbd5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.257143 -71.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbd810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.257143 -41.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbdb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -89.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbddb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.257143 -58.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbdff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.257143 -73.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbe230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.257143 -43.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbe560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -88.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbe7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.257143 -57.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbea10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.257143 -72.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbec50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.257143 -42.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbef80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4417.000000 -89.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbf1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.257143 -58.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbf430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.257143 -73.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbf670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.257143 -43.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbf9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4943.000000 -87.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbfc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.257143 -56.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dbfe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4938.257143 -71.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc0090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4926.257143 -41.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc03c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -87.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc0630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5018.257143 -56.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc0870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5041.257143 -71.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc0ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.257143 -41.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc0de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5164.000000 -88.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc1050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.257143 -57.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc1290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5159.257143 -72.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc14d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5147.257143 -42.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc1800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5272.000000 -86.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc1a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5244.257143 -55.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc1cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5267.257143 -70.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc1ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5255.257143 -40.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc2220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5392.000000 -88.000000) translate(0,11)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc2490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5364.257143 -57.333333) translate(0,11)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc26d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5387.257143 -72.666667) translate(0,11)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1dc2910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5375.257143 -42.000000) translate(0,11)">P(KW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de2900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.257143 876.833333) translate(0,12)">Ub(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de2b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.257143 854.666667) translate(0,12)">Uc(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de2db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3962.257143 813.000000) translate(0,12)">Uab(KV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de2ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.257143 834.166667) translate(0,12)">Uo(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de3230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.257143 899.000000) translate(0,12)">Ua(KV):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-39003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 -924.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6237" ObjectName="SW-CX_TJT.CX_TJT_341BK"/>
     <cge:Meas_Ref ObjectId="39003"/>
    <cge:TPSR_Ref TObjectID="6237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39024">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -924.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6243" ObjectName="SW-CX_TJT.CX_TJT_342BK"/>
     <cge:Meas_Ref ObjectId="39024"/>
    <cge:TPSR_Ref TObjectID="6243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39064">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -963.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6255" ObjectName="SW-CX_TJT.CX_TJT_315BK"/>
     <cge:Meas_Ref ObjectId="39064"/>
    <cge:TPSR_Ref TObjectID="6255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -767.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6355" ObjectName="SW-CX_TJT.CX_TJT_301BK"/>
     <cge:Meas_Ref ObjectId="39423"/>
    <cge:TPSR_Ref TObjectID="6355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39424">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -446.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6356" ObjectName="SW-CX_TJT.CX_TJT_001BK"/>
     <cge:Meas_Ref ObjectId="39424"/>
    <cge:TPSR_Ref TObjectID="6356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39393">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3631.000000 -254.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6345" ObjectName="SW-CX_TJT.CX_TJT_048BK"/>
     <cge:Meas_Ref ObjectId="39393"/>
    <cge:TPSR_Ref TObjectID="6345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6336" ObjectName="SW-CX_TJT.CX_TJT_015BK"/>
     <cge:Meas_Ref ObjectId="39340"/>
    <cge:TPSR_Ref TObjectID="6336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4729.000000 -924.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6249" ObjectName="SW-CX_TJT.CX_TJT_343BK"/>
     <cge:Meas_Ref ObjectId="39044"/>
    <cge:TPSR_Ref TObjectID="6249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39200">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6294" ObjectName="SW-CX_TJT.CX_TJT_047BK"/>
     <cge:Meas_Ref ObjectId="39200"/>
    <cge:TPSR_Ref TObjectID="6294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39180">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6288" ObjectName="SW-CX_TJT.CX_TJT_046BK"/>
     <cge:Meas_Ref ObjectId="39180"/>
    <cge:TPSR_Ref TObjectID="6288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4097.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6282" ObjectName="SW-CX_TJT.CX_TJT_045BK"/>
     <cge:Meas_Ref ObjectId="39160"/>
    <cge:TPSR_Ref TObjectID="6282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39140">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6276" ObjectName="SW-CX_TJT.CX_TJT_044BK"/>
     <cge:Meas_Ref ObjectId="39140"/>
    <cge:TPSR_Ref TObjectID="6276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6270" ObjectName="SW-CX_TJT.CX_TJT_043BK"/>
     <cge:Meas_Ref ObjectId="39120"/>
    <cge:TPSR_Ref TObjectID="6270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39100">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6264" ObjectName="SW-CX_TJT.CX_TJT_042BK"/>
     <cge:Meas_Ref ObjectId="39100"/>
    <cge:TPSR_Ref TObjectID="6264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6258" ObjectName="SW-CX_TJT.CX_TJT_041BK"/>
     <cge:Meas_Ref ObjectId="39080"/>
    <cge:TPSR_Ref TObjectID="6258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39240">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6306" ObjectName="SW-CX_TJT.CX_TJT_052BK"/>
     <cge:Meas_Ref ObjectId="39240"/>
    <cge:TPSR_Ref TObjectID="6306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39260">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5072.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6312" ObjectName="SW-CX_TJT.CX_TJT_053BK"/>
     <cge:Meas_Ref ObjectId="39260"/>
    <cge:TPSR_Ref TObjectID="6312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39280">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5183.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6318" ObjectName="SW-CX_TJT.CX_TJT_054BK"/>
     <cge:Meas_Ref ObjectId="39280"/>
    <cge:TPSR_Ref TObjectID="6318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5296.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6324" ObjectName="SW-CX_TJT.CX_TJT_055BK"/>
     <cge:Meas_Ref ObjectId="39300"/>
    <cge:TPSR_Ref TObjectID="6324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5410.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6330" ObjectName="SW-CX_TJT.CX_TJT_056BK"/>
     <cge:Meas_Ref ObjectId="39320"/>
    <cge:TPSR_Ref TObjectID="6330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39358">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5465.000000 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6340" ObjectName="SW-CX_TJT.CX_TJT_025BK"/>
     <cge:Meas_Ref ObjectId="39358"/>
    <cge:TPSR_Ref TObjectID="6340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5583.000000 -250.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6350" ObjectName="SW-CX_TJT.CX_TJT_057BK"/>
     <cge:Meas_Ref ObjectId="39408"/>
    <cge:TPSR_Ref TObjectID="6350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4652.000000 -284.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6344" ObjectName="SW-CX_TJT.CX_TJT_012BK"/>
     <cge:Meas_Ref ObjectId="39376"/>
    <cge:TPSR_Ref TObjectID="6344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39220">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4833.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6300" ObjectName="SW-CX_TJT.CX_TJT_051BK"/>
     <cge:Meas_Ref ObjectId="39220"/>
    <cge:TPSR_Ref TObjectID="6300"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DG" endPointId="0" endStationName="CX_TJT" flowDrawDirect="1" flowShape="0" id="AC-35kV.dongtian_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4294,-1216 4294,-1239 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37444" ObjectName="AC-35kV.dongtian_line"/>
    <cge:TPSR_Ref TObjectID="37444_SS-65"/></metadata>
   <polyline fill="none" opacity="0" points="4294,-1216 4294,-1239 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_BLXC" endPointId="0" endStationName="CX_TJT" flowDrawDirect="1" flowShape="0" id="AC-35kV.baitian_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4738,-1223 4738,-1242 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37443" ObjectName="AC-35kV.baitian_line"/>
    <cge:TPSR_Ref TObjectID="37443_SS-65"/></metadata>
   <polyline fill="none" opacity="0" points="4738,-1223 4738,-1242 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1bd73f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 -1021.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b05c80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -1021.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a77610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4563.000000 -793.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b10b90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -433.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc4600" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 -294.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc6020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3535.000000 -189.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c04100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3658.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b82bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 -1021.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b31b40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b47880" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bed3d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b43280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4115.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b8cfb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b0cd60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b5a330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4453.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1adff20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a14160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5089.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b2aac0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5202.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a5ec90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5315.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a827e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5545.000000 -289.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a8e120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5665.000000 -289.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c67a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5664.000000 -184.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c77f10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 -430.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a185b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5285.000000 -431.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d61ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4718.000000 -195.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1da0010" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -291.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1da0aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4740.000000 -290.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6228" cx="4294" cy="-867" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11089" cx="4332" cy="-1103" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6228" cx="4515" cy="-867" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11089" cx="4553" cy="-1103" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6228" cx="4498" cy="-867" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6228" cx="4761" cy="-867" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6228" cx="4158" cy="-867" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11089" cx="4158" cy="-1103" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6228" cx="4158" cy="-867" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11089" cx="4776" cy="-1103" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6228" cx="4738" cy="-867" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11141" cx="3838" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11141" cx="3952" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11141" cx="4066" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11141" cx="4178" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11141" cx="4290" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11141" cx="4403" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11139" cx="4928" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11139" cx="5040" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11139" cx="5152" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11139" cx="5265" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11139" cx="5378" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11139" cx="5474" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11139" cx="4800" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11141" cx="3761" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11141" cx="4516" cy="-183" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="4157" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="3640" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="3761" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="3878" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="3992" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="4106" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="4218" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="4330" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="4443" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="4556" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="4403" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11140" cx="4620" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="4968" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="5081" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="5192" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="5305" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="5419" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="5474" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="5592" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="5214" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="4842" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11138" cx="4727" cy="-365" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a1bb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.000000 -1265.000000) translate(0,15)">至110kV东瓜变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a27ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -1180.000000) translate(0,15)">东田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a28860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4495.000000 -1275.000000) translate(0,15)">至35kV马石铺</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a27420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4524.000000 -1180.000000) translate(0,15)">田马线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a27690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4697.000000 -1268.000000) translate(0,15)">至110kV白龙新村变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2b130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -1182.000000) translate(0,15)">白田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2b3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4773.000000 -1126.000000) translate(0,15)">旁母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2bae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4453.000000 -718.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2c170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -657.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2cd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5165.000000 -575.000000) translate(0,15)">II段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2d620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4364.000000 -570.000000) translate(0,15)">I段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2d8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 17.000000) translate(0,15)">古镇Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2e720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5047.000000 15.000000) translate(0,15)">公司II回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2efa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5177.000000 15.000000) translate(0,15)">城区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2f720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5267.000000 15.000000) translate(0,15)">古镇Ⅴ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a2fd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5389.000000 15.000000) translate(0,15)">彝人古镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a908e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5549.000000 38.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a91890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3575.000000 34.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a91d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 15.000000) translate(0,15)">太阳历公园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a92a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3952.000000 15.000000) translate(0,15)">云南开关厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a93cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 15.000000) translate(0,15)">车坪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a944b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4181.000000 15.000000) translate(0,15)">永盛花园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a95170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4298.000000 15.000000) translate(0,15)">古镇Ⅳ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a957b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4423.000000 15.000000) translate(0,15)">赵家湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a96160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -54.000000) translate(0,15)">分支箱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a96dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -54.000000) translate(0,15)">分支箱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a97200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4229.000000 -54.000000) translate(0,15)">分支箱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a97440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4118.000000 -54.000000) translate(0,15)">分支箱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a97680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4000.000000 -54.000000) translate(0,15)">分支箱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a978c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5317.000000 -56.000000) translate(0,15)">分支箱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a98360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -953.000000) translate(0,12)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a99040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4745.000000 -908.000000) translate(0,12)">3431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a99370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4745.000000 -1000.000000) translate(0,12)">3436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a998b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4685.000000 -1056.000000) translate(0,12)">34367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a99e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4783.000000 -1078.000000) translate(0,12)">3435</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9a380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4688.000000 -1154.000000) translate(0,12)">3439</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9a8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4524.000000 -953.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9ae50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -908.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9b340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -1000.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9b580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 -1056.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9b7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -1078.000000) translate(0,12)">3425</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9ba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4465.000000 -1153.000000) translate(0,12)">3429</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9bc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4303.000000 -953.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9be80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -1000.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9c0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -908.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9c300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -1056.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9c540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 -1078.000000) translate(0,12)">3415</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9c780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4244.000000 -1155.000000) translate(0,12)">3419</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9c9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -841.000000) translate(0,12)">3801</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9d210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4505.000000 -840.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9d490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -826.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9d6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4167.000000 -992.000000) translate(0,12)">315</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9d910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4165.000000 -947.000000) translate(0,12)">3151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4165.000000 -1039.000000) translate(0,12)">3155</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9dd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4167.000000 -796.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9dfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4165.000000 -843.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9e210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4166.000000 -475.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9e450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4164.000000 -522.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9e690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4164.000000 -414.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -468.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9eb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -413.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9ed50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4422.000000 -465.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9ef90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3649.000000 -283.000000) translate(0,12)">048</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9f1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3581.000000 -329.000000) translate(0,12)">04817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3647.000000 -339.000000) translate(0,12)">0481</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9f650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3647.000000 -235.000000) translate(0,12)">0486</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3579.000000 -224.000000) translate(0,12)">04867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9fad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3770.000000 -280.000000) translate(0,12)">015</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9fd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3768.000000 -336.000000) translate(0,12)">0151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9ff50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3703.000000 -326.000000) translate(0,12)">01517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa0190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3768.000000 -232.000000) translate(0,12)">0155</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa03d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -280.000000) translate(0,12)">047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa0610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -232.000000) translate(0,12)">0472</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa0850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -336.000000) translate(0,12)">0471</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa0a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3819.000000 -326.000000) translate(0,12)">04717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa0cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3845.000000 -160.000000) translate(0,12)">0475</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa0f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4001.000000 -280.000000) translate(0,12)">046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa1150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -336.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa1390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -232.000000) translate(0,12)">0462</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa15d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -326.000000) translate(0,12)">04617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa1810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -160.000000) translate(0,12)">0465</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa4460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -47.000000) translate(0,12)">0476</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa4950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 -280.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa4b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -336.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa4dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -232.000000) translate(0,12)">0452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa5010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4048.000000 -326.000000) translate(0,12)">04517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa5250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4073.000000 -160.000000) translate(0,12)">0455</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa5490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -280.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa56d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4225.000000 -336.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa5910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4225.000000 -232.000000) translate(0,12)">0442</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa5b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -160.000000) translate(0,12)">0445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa5d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -326.000000) translate(0,12)">04417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa5fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 -280.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -336.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -232.000000) translate(0,12)">0432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -326.000000) translate(0,12)">04317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa68d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4297.000000 -160.000000) translate(0,12)">0435</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 -280.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -336.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -232.000000) translate(0,12)">0422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa71d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4384.000000 -326.000000) translate(0,12)">04217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -160.000000) translate(0,12)">0425</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -280.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4562.000000 -334.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.000000 -229.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4498.000000 -326.000000) translate(0,12)">04117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4523.000000 -160.000000) translate(0,12)">0415</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa8190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -318.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa83d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4977.000000 -280.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa8610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -336.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa8850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -232.000000) translate(0,12)">0522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa8a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4909.000000 -326.000000) translate(0,12)">05217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa8cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4935.000000 -160.000000) translate(0,12)">0525</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a35ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -45.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a364f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5089.000000 -280.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a36730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5087.000000 -336.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a36970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5087.000000 -232.000000) translate(0,12)">0532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a36bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5021.000000 -326.000000) translate(0,12)">05317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a36df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5047.000000 -160.000000) translate(0,12)">0535</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a37030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5087.000000 -46.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a37270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -280.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a374b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -336.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a376f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -232.000000) translate(0,12)">0542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a37930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5134.000000 -326.000000) translate(0,12)">05417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a37b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5159.000000 -160.000000) translate(0,12)">0545</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a37db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -46.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a37ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5314.000000 -280.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a38230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5312.000000 -336.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a38470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5312.000000 -232.000000) translate(0,12)">0552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a386b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5247.000000 -326.000000) translate(0,12)">05517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a388f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5272.000000 -160.000000) translate(0,12)">0555</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a38b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5427.000000 -280.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a38d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5425.000000 -336.000000) translate(0,12)">0561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a38fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5425.000000 -232.000000) translate(0,12)">0562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5359.000000 -326.000000) translate(0,12)">05617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a39430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5385.000000 -160.000000) translate(0,12)">0565</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a39670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5425.000000 -47.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a398b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5483.000000 -278.000000) translate(0,12)">025</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a39af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5481.000000 -334.000000) translate(0,12)">0251</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a39d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5481.000000 -230.000000) translate(0,12)">0255</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a39f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5507.000000 -322.000000) translate(0,12)">02517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3a1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5601.000000 -279.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3a3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5597.000000 -338.000000) translate(0,12)">0571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3a630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5599.000000 -231.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3a870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5624.000000 -216.000000) translate(0,12)">05767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3aab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5620.000000 -320.000000) translate(0,12)">05717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3c6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5221.000000 -414.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3cce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5232.000000 -466.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a477e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -885.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1a48230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3331.500000 -1183.500000) translate(0,16)">田家屯变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a4a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3189.000000 -605.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a51950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -1043.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a51950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -1043.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a51950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -1043.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a51950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -1043.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a51950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -1043.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a51950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -1043.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a51950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -1043.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a54c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4654.000000 -282.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a54ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -336.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a55230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4735.000000 -336.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d54070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -262.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d54280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5557.000000 -388.000000) translate(0,12)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d54750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -181.000000) translate(0,12)">10kVⅡ段旁母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d54fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3651.000000 -388.000000) translate(0,12)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d55530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3668.000000 -200.000000) translate(0,12)">10kVⅠ段旁母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d69750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4210.257143 -636.000000) translate(0,12)">温度(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6a3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4210.257143 -653.000000) translate(0,12)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d74850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4508.000000 135.000000) translate(0,15)">1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d74850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4508.000000 135.000000) translate(0,33)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d7d450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3313.257143 -243.000000) translate(0,16)">4777</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7e160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4532.000000 -11.000000) translate(0,12)">0010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d81ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4792.000000 133.000000) translate(0,15)">2号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d81ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4792.000000 133.000000) translate(0,33)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d88870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4816.000000 -4.000000) translate(0,12)">0020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d96a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4795.000000 -342.000000) translate(0,12)">0512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d97090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4775.000000 -291.000000) translate(0,12)">05127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d972d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4851.000000 -280.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d97510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -221.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d97750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4755.000000 -147.000000) translate(0,12)">0515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da3f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3205.000000 -747.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1da5140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3494.000000 -1169.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1a29690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3494.000000 -1204.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db39a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3986.000000 -672.000000) translate(0,15)">SFZ9-10000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db39a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3986.000000 -672.000000) translate(0,33)">35±3*2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1dc2b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -207.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1dc2b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -207.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1dc5010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3313.000000 -188.500000) translate(0,16)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1ddc760" transform="matrix(0.950820 -0.000000 -0.000000 0.827586 3543.557377 -1120.448276) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1de0830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -694.000000) translate(0,15)">35kV1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de3470" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4127.623377 -1157.800000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de3e40" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4354.623377 -1157.800000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1de44b0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4581.623377 -1157.800000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_20ee680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3177.000000 -705.000000) translate(0,20)">隔刀远控</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4557" cy="-35" fill="none" fillStyle="0" r="13" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4491" cy="31" fill="none" fillStyle="0" r="10" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4506" cy="31" fill="none" fillStyle="0" r="10" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4843" cy="-30" fill="none" fillStyle="0" r="13" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4777" cy="36" fill="none" fillStyle="0" r="10" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4792" cy="36" fill="none" fillStyle="0" r="10" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b13650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -725.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1b948c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-867 4294,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6228@0" ObjectIDZND0="6240@0" Pin0InfoVect0LinkObjId="SW-39006_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b60d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-867 4294,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c16d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-919 4294,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6240@1" ObjectIDZND0="6237@0" Pin0InfoVect0LinkObjId="SW-39003_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-919 4294,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c18e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-959 4294,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6237@1" ObjectIDZND0="6238@0" Pin0InfoVect0LinkObjId="SW-98021_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-959 4294,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c19050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-1011 4294,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="6238@1" ObjectIDZND0="g_1d65670@0" ObjectIDZND1="6242@x" ObjectIDZND2="6241@x" Pin0InfoVect0LinkObjId="g_1d65670_0" Pin0InfoVect1LinkObjId="SW-39008_0" Pin0InfoVect2LinkObjId="SW-39007_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-98021_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-1011 4294,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd7010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-1030 4279,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d65670@0" ObjectIDND1="6242@x" ObjectIDND2="6241@x" ObjectIDZND0="6239@1" Pin0InfoVect0LinkObjId="SW-39005_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d65670_0" Pin1InfoVect1LinkObjId="SW-39008_0" Pin1InfoVect2LinkObjId="SW-39007_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-1030 4279,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd7200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4243,-1030 4224,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6239@0" ObjectIDZND0="g_1bd73f0@0" Pin0InfoVect0LinkObjId="g_1bd73f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4243,-1030 4224,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd7a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-1030 4294,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="6238@x" ObjectIDND1="6239@x" ObjectIDZND0="g_1d65670@0" ObjectIDZND1="6242@x" ObjectIDZND2="6241@x" Pin0InfoVect0LinkObjId="g_1d65670_0" Pin0InfoVect1LinkObjId="SW-39008_0" Pin0InfoVect2LinkObjId="SW-39007_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-98021_0" Pin1InfoVect1LinkObjId="SW-39005_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-1030 4294,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b2db50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-1043 4332,-1043 4332,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6238@x" ObjectIDND1="6239@x" ObjectIDND2="g_1d65670@0" ObjectIDZND0="6241@0" Pin0InfoVect0LinkObjId="SW-39007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-98021_0" Pin1InfoVect1LinkObjId="SW-39005_0" Pin1InfoVect2LinkObjId="g_1d65670_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-1043 4332,-1043 4332,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b2dd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1089 4332,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6241@1" ObjectIDZND0="11089@0" Pin0InfoVect0LinkObjId="g_1bbb7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39007_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1089 4332,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf70a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-867 4515,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6228@0" ObjectIDZND0="6246@0" Pin0InfoVect0LinkObjId="SW-39027_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b60d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-867 4515,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b52050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-919 4515,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6246@1" ObjectIDZND0="6243@0" Pin0InfoVect0LinkObjId="SW-39024_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39027_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-919 4515,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b539d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-959 4515,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6243@1" ObjectIDZND0="6245@0" Pin0InfoVect0LinkObjId="SW-39026_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-959 4515,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b53bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-1011 4515,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="6245@1" ObjectIDZND0="g_1d66620@0" ObjectIDZND1="6248@x" ObjectIDZND2="6247@x" Pin0InfoVect0LinkObjId="g_1d66620_0" Pin0InfoVect1LinkObjId="SW-39029_0" Pin0InfoVect2LinkObjId="SW-39028_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39026_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-1011 4515,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b058a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-1030 4500,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d66620@0" ObjectIDND1="6248@x" ObjectIDND2="6247@x" ObjectIDZND0="6244@1" Pin0InfoVect0LinkObjId="SW-39025_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d66620_0" Pin1InfoVect1LinkObjId="SW-39029_0" Pin1InfoVect2LinkObjId="SW-39028_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-1030 4500,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b05a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-1030 4445,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6244@0" ObjectIDZND0="g_1b05c80@0" Pin0InfoVect0LinkObjId="g_1b05c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-1030 4445,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b062b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-1030 4515,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="6245@x" ObjectIDND1="6244@x" ObjectIDZND0="g_1d66620@0" ObjectIDZND1="6248@x" ObjectIDZND2="6247@x" Pin0InfoVect0LinkObjId="g_1d66620_0" Pin0InfoVect1LinkObjId="SW-39029_0" Pin0InfoVect2LinkObjId="SW-39028_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39026_0" Pin1InfoVect1LinkObjId="SW-39025_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-1030 4515,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbb5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-1043 4553,-1043 4553,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6245@x" ObjectIDND1="6244@x" ObjectIDND2="g_1d66620@0" ObjectIDZND0="6247@0" Pin0InfoVect0LinkObjId="SW-39028_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39026_0" Pin1InfoVect1LinkObjId="SW-39025_0" Pin1InfoVect2LinkObjId="g_1d66620_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-1043 4553,-1043 4553,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbb7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-1089 4553,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6247@1" ObjectIDZND0="11089@0" Pin0InfoVect0LinkObjId="g_1b2dd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39028_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-1089 4553,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbb990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-1043 4515,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6245@x" ObjectIDND1="6244@x" ObjectIDND2="6247@x" ObjectIDZND0="g_1d66620@0" ObjectIDZND1="6248@x" Pin0InfoVect0LinkObjId="g_1d66620_0" Pin0InfoVect1LinkObjId="SW-39029_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39026_0" Pin1InfoVect1LinkObjId="SW-39025_0" Pin1InfoVect2LinkObjId="SW-39028_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-1043 4515,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbd230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-1122 4499,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="6245@x" ObjectIDND1="6244@x" ObjectIDND2="6247@x" ObjectIDZND0="6248@1" Pin0InfoVect0LinkObjId="SW-39029_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39026_0" Pin1InfoVect1LinkObjId="SW-39025_0" Pin1InfoVect2LinkObjId="SW-39028_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-1122 4499,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbd6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-1129 4227,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6242@0" ObjectIDZND0="g_18b89e0@0" Pin0InfoVect0LinkObjId="g_18b89e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-1129 4227,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbd8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4196,-1129 4179,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_18b89e0@1" ObjectIDZND0="g_1b2f5e0@0" Pin0InfoVect0LinkObjId="g_1b2f5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18b89e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4196,-1129 4179,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4463,-1122 4446,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6248@0" ObjectIDZND0="g_19f1da0@0" Pin0InfoVect0LinkObjId="g_19f1da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39029_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4463,-1122 4446,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-1122 4400,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_19f1da0@1" ObjectIDZND0="g_1bbd420@0" Pin0InfoVect0LinkObjId="g_1bbd420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19f1da0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-1122 4400,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c2fc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4498,-867 4498,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6228@0" ObjectIDZND0="6229@1" Pin0InfoVect0LinkObjId="SW-38995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b60d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4498,-867 4498,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a75990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4498,-815 4498,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6229@0" ObjectIDZND0="g_18a3ec0@0" ObjectIDZND1="g_1a97b00@0" ObjectIDZND2="6230@x" Pin0InfoVect0LinkObjId="g_18a3ec0_0" Pin0InfoVect1LinkObjId="g_1a97b00_0" Pin0InfoVect2LinkObjId="SW-38996_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4498,-815 4498,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a77230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4498,-802 4515,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_18a3ec0@0" ObjectIDND1="g_1a97b00@0" ObjectIDND2="6229@x" ObjectIDZND0="6230@0" Pin0InfoVect0LinkObjId="SW-38996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18a3ec0_0" Pin1InfoVect1LinkObjId="g_1a97b00_0" Pin1InfoVect2LinkObjId="SW-38995_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4498,-802 4515,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a77420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-802 4568,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6230@1" ObjectIDZND0="g_1a77610@0" Pin0InfoVect0LinkObjId="g_1a77610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-802 4568,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a77c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-802 4471,-802 4471,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_18a3ec0@0" ObjectIDND1="6229@x" ObjectIDND2="6230@x" ObjectIDZND0="g_1a97b00@0" Pin0InfoVect0LinkObjId="g_1a97b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18a3ec0_0" Pin1InfoVect1LinkObjId="SW-38995_0" Pin1InfoVect2LinkObjId="SW-38996_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-802 4471,-802 4471,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b13270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4498,-802 4498,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1a97b00@0" ObjectIDND1="6229@x" ObjectIDND2="6230@x" ObjectIDZND0="g_18a3ec0@1" Pin0InfoVect0LinkObjId="g_18a3ec0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a97b00_0" Pin1InfoVect1LinkObjId="SW-38995_0" Pin1InfoVect2LinkObjId="SW-38996_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4498,-802 4498,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b13460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4498,-759 4498,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_18a3ec0@0" ObjectIDZND0="g_1b13650@0" Pin0InfoVect0LinkObjId="g_1b13650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18a3ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4498,-759 4498,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bab170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-867 4761,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6228@0" ObjectIDZND0="6236@1" Pin0InfoVect0LinkObjId="SW-39002_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b60d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-867 4761,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bab950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-816 4761,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6236@0" ObjectIDZND0="g_1bab360@1" Pin0InfoVect0LinkObjId="g_1bab360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-816 4761,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1babb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-772 4761,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1bab360@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bab360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-772 4761,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b60700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-1103 4158,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11089@0" ObjectIDZND0="6257@1" Pin0InfoVect0LinkObjId="SW-39066_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b2dd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-1103 4158,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b608f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-1014 4158,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6257@0" ObjectIDZND0="6255@1" Pin0InfoVect0LinkObjId="SW-39064_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-1014 4158,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b60b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-971 4158,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6255@0" ObjectIDZND0="6256@1" Pin0InfoVect0LinkObjId="SW-39065_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39064_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-971 4158,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b60d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-922 4158,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6256@0" ObjectIDZND0="6228@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39065_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-922 4158,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-775 4158,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6355@0" ObjectIDZND0="6361@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-775 4158,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6f5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-867 4158,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6228@0" ObjectIDZND0="6357@1" Pin0InfoVect0LinkObjId="SW-39425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b60d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-867 4158,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6f7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-818 4158,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6357@0" ObjectIDZND0="6355@1" Pin0InfoVect0LinkObjId="SW-39423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-818 4158,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6fc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-620 4157,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="6361@0" ObjectIDZND0="g_1b6f9c0@0" Pin0InfoVect0LinkObjId="g_1b6f9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b6f3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-620 4157,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be3e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-552 4157,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b6f9c0@1" ObjectIDZND0="6358@1" Pin0InfoVect0LinkObjId="SW-39426_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b6f9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-552 4157,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be40b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-497 4157,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6358@0" ObjectIDZND0="6356@1" Pin0InfoVect0LinkObjId="SW-39424_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-497 4157,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be42d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-365 4157,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6359@0" Pin0InfoVect0LinkObjId="SW-39427_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-365 4157,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be44f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-425 4157,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6359@1" ObjectIDZND0="6356@x" ObjectIDZND1="6360@x" Pin0InfoVect0LinkObjId="SW-39424_0" Pin0InfoVect1LinkObjId="SW-39428_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39427_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-425 4157,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be4710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-442 4157,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6359@x" ObjectIDND1="6360@x" ObjectIDZND0="6356@0" Pin0InfoVect0LinkObjId="SW-39424_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39427_0" Pin1InfoVect1LinkObjId="SW-39428_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-442 4157,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b10750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-442 4136,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6356@x" ObjectIDND1="6359@x" ObjectIDZND0="6360@1" Pin0InfoVect0LinkObjId="SW-39428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39424_0" Pin1InfoVect1LinkObjId="SW-39427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-442 4136,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b10970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4100,-442 4079,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6360@0" ObjectIDZND0="g_1b10b90@0" Pin0InfoVect0LinkObjId="g_1b10b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4100,-442 4079,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b75c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-365 3640,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6346@1" Pin0InfoVect0LinkObjId="SW-39394_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-365 3640,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b75e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-243 3640,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6348@1" ObjectIDZND0="6345@0" Pin0InfoVect0LinkObjId="SW-39393_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-243 3640,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc41c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-303 3619,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6345@x" ObjectIDND1="6346@x" ObjectIDZND0="6347@1" Pin0InfoVect0LinkObjId="SW-39395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39393_0" Pin1InfoVect1LinkObjId="SW-39394_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-303 3619,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc43e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3583,-303 3562,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6347@0" ObjectIDZND0="g_1bc4600@0" Pin0InfoVect0LinkObjId="g_1bc4600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3583,-303 3562,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc57a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-289 3640,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6345@1" ObjectIDZND0="6346@x" ObjectIDZND1="6347@x" Pin0InfoVect0LinkObjId="SW-39394_0" Pin0InfoVect1LinkObjId="SW-39395_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-289 3640,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc59c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-303 3640,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6345@x" ObjectIDND1="6347@x" ObjectIDZND0="6346@0" Pin0InfoVect0LinkObjId="SW-39394_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39393_0" Pin1InfoVect1LinkObjId="SW-39395_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-303 3640,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc5be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-198 3617,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1b69fa0@0" ObjectIDND1="6348@x" ObjectIDZND0="6349@1" Pin0InfoVect0LinkObjId="SW-39397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b69fa0_0" Pin1InfoVect1LinkObjId="SW-39396_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-198 3617,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc5e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3581,-198 3560,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6349@0" ObjectIDZND0="g_1bc6020@0" Pin0InfoVect0LinkObjId="g_1bc6020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3581,-198 3560,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b69d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-198 3640,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1b69fa0@0" ObjectIDND1="6349@x" ObjectIDZND0="6348@0" Pin0InfoVect0LinkObjId="SW-39396_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b69fa0_0" Pin1InfoVect1LinkObjId="SW-39397_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-198 3640,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6a270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-77 3640,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="40677@0" ObjectIDZND0="g_1b69fa0@1" Pin0InfoVect0LinkObjId="g_1b69fa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_TJT.CX_TJT_048Cb_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-77 3640,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6a490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-184 3640,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1b69fa0@0" ObjectIDZND0="6348@x" ObjectIDZND1="6349@x" Pin0InfoVect0LinkObjId="SW-39396_0" Pin0InfoVect1LinkObjId="SW-39397_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b69fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-184 3640,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c018f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-365 3761,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6337@1" Pin0InfoVect0LinkObjId="SW-39341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-365 3761,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c01b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-240 3761,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6339@1" ObjectIDZND0="6336@0" Pin0InfoVect0LinkObjId="SW-39340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39343_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-240 3761,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c03cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-300 3740,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6336@x" ObjectIDND1="6337@x" ObjectIDZND0="6338@1" Pin0InfoVect0LinkObjId="SW-39342_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39340_0" Pin1InfoVect1LinkObjId="SW-39341_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-300 3740,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c03ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3704,-300 3683,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6338@0" ObjectIDZND0="g_1c04100@0" Pin0InfoVect0LinkObjId="g_1c04100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3704,-300 3683,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd8310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-286 3761,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6336@1" ObjectIDZND0="6337@x" ObjectIDZND1="6338@x" Pin0InfoVect0LinkObjId="SW-39341_0" Pin0InfoVect1LinkObjId="SW-39342_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-286 3761,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd8530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-300 3761,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6336@x" ObjectIDND1="6338@x" ObjectIDZND0="6337@0" Pin0InfoVect0LinkObjId="SW-39341_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39340_0" Pin1InfoVect1LinkObjId="SW-39342_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-300 3761,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd8fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-183 3761,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11141@0" ObjectIDZND0="6339@0" Pin0InfoVect0LinkObjId="SW-39343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-183 3761,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b07e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-1128 4722,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="6250@x" ObjectIDND1="6251@x" ObjectIDND2="6253@x" ObjectIDZND0="6254@1" Pin0InfoVect0LinkObjId="SW-39049_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39045_0" Pin1InfoVect1LinkObjId="SW-39046_0" Pin1InfoVect2LinkObjId="SW-39048_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-1128 4722,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b08760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4686,-1128 4669,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6254@0" ObjectIDZND0="g_1b083c0@0" Pin0InfoVect0LinkObjId="g_1b083c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39049_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4686,-1128 4669,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b089c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-1128 4623,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1b083c0@1" ObjectIDZND0="g_1b08030@0" Pin0InfoVect0LinkObjId="g_1b08030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b083c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-1128 4623,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b09040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-1043 4776,-1043 4776,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6250@x" ObjectIDND1="6251@x" ObjectIDND2="g_1d67370@0" ObjectIDZND0="6253@0" Pin0InfoVect0LinkObjId="SW-39048_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39045_0" Pin1InfoVect1LinkObjId="SW-39046_0" Pin1InfoVect2LinkObjId="g_1d67370_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-1043 4776,-1043 4776,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b092a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-1089 4776,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6253@1" ObjectIDZND0="11089@0" Pin0InfoVect0LinkObjId="g_1b2dd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39048_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-1089 4776,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b09920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-867 4738,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6228@0" ObjectIDZND0="6252@0" Pin0InfoVect0LinkObjId="SW-39047_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b60d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-867 4738,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b09f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-919 4738,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6252@1" ObjectIDZND0="6249@0" Pin0InfoVect0LinkObjId="SW-39044_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39047_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-919 4738,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6c640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-959 4738,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6249@1" ObjectIDZND0="6250@0" Pin0InfoVect0LinkObjId="SW-39045_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-959 4738,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-1011 4738,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="6250@1" ObjectIDZND0="g_1d67370@0" ObjectIDZND1="6254@x" ObjectIDZND2="6253@x" Pin0InfoVect0LinkObjId="g_1d67370_0" Pin0InfoVect1LinkObjId="SW-39049_0" Pin0InfoVect2LinkObjId="SW-39048_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39045_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-1011 4738,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6eec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-1030 4723,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d67370@0" ObjectIDND1="6254@x" ObjectIDND2="6253@x" ObjectIDZND0="6251@1" Pin0InfoVect0LinkObjId="SW-39046_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d67370_0" Pin1InfoVect1LinkObjId="SW-39049_0" Pin1InfoVect2LinkObjId="SW-39048_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-1030 4723,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6f120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-1030 4668,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6251@0" ObjectIDZND0="g_1b82bd0@0" Pin0InfoVect0LinkObjId="g_1b82bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39046_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-1030 4668,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b835f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-1030 4738,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="6250@x" ObjectIDND1="6251@x" ObjectIDZND0="g_1d67370@0" ObjectIDZND1="6254@x" ObjectIDZND2="6253@x" Pin0InfoVect0LinkObjId="g_1d67370_0" Pin0InfoVect1LinkObjId="SW-39049_0" Pin0InfoVect2LinkObjId="SW-39048_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39045_0" Pin1InfoVect1LinkObjId="SW-39046_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-1030 4738,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1f180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-365 3878,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6295@1" Pin0InfoVect0LinkObjId="SW-39201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-365 3878,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-241 3878,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6297@1" ObjectIDZND0="6294@0" Pin0InfoVect0LinkObjId="SW-39200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39203_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-241 3878,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b31680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-300 3857,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6294@x" ObjectIDND1="6295@x" ObjectIDZND0="6296@1" Pin0InfoVect0LinkObjId="SW-39202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39200_0" Pin1InfoVect1LinkObjId="SW-39201_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-300 3857,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b318e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3821,-300 3800,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6296@0" ObjectIDZND0="g_1b31b40@0" Pin0InfoVect0LinkObjId="g_1b31b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3821,-300 3800,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b32570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-286 3878,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6294@1" ObjectIDZND0="6295@x" ObjectIDZND1="6296@x" Pin0InfoVect0LinkObjId="SW-39201_0" Pin0InfoVect1LinkObjId="SW-39202_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-286 3878,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b327d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-300 3878,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6294@x" ObjectIDND1="6296@x" ObjectIDZND0="6295@0" Pin0InfoVect0LinkObjId="SW-39201_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39200_0" Pin1InfoVect1LinkObjId="SW-39202_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-300 3878,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1baf440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-183 3838,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11141@0" ObjectIDZND0="6298@1" Pin0InfoVect0LinkObjId="SW-39204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-183 3838,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1baf6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-135 3838,-128 3878,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6298@0" ObjectIDZND0="6297@x" ObjectIDZND1="g_1baf900@0" Pin0InfoVect0LinkObjId="SW-39203_0" Pin0InfoVect1LinkObjId="g_1baf900_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-135 3838,-128 3878,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb0550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-121 3878,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1baf900@0" ObjectIDZND0="6297@x" ObjectIDZND1="6298@x" Pin0InfoVect0LinkObjId="SW-39203_0" Pin0InfoVect1LinkObjId="SW-39204_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1baf900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-121 3878,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb07b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-128 3878,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6298@x" ObjectIDND1="g_1baf900@0" ObjectIDZND0="6297@0" Pin0InfoVect0LinkObjId="SW-39203_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39204_0" Pin1InfoVect1LinkObjId="g_1baf900_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-128 3878,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af5c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-365 3992,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6289@1" Pin0InfoVect0LinkObjId="SW-39181_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-365 3992,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af5e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-241 3992,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6291@1" ObjectIDZND0="6288@0" Pin0InfoVect0LinkObjId="SW-39180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-241 3992,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b473c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-300 3971,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6288@x" ObjectIDND1="6289@x" ObjectIDZND0="6290@1" Pin0InfoVect0LinkObjId="SW-39182_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39180_0" Pin1InfoVect1LinkObjId="SW-39181_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-300 3971,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b47620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-300 3914,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6290@0" ObjectIDZND0="g_1b47880@0" Pin0InfoVect0LinkObjId="g_1b47880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-300 3914,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b48290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-286 3992,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6288@1" ObjectIDZND0="6289@x" ObjectIDZND1="6290@x" Pin0InfoVect0LinkObjId="SW-39181_0" Pin0InfoVect1LinkObjId="SW-39182_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-286 3992,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b484f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-300 3992,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6288@x" ObjectIDND1="6290@x" ObjectIDZND0="6289@0" Pin0InfoVect0LinkObjId="SW-39181_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39180_0" Pin1InfoVect1LinkObjId="SW-39182_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-300 3992,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b87510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-183 3952,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11141@0" ObjectIDZND0="6292@1" Pin0InfoVect0LinkObjId="SW-39184_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-183 3952,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b87770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-135 3952,-128 3992,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6292@0" ObjectIDZND0="g_1b879d0@0" ObjectIDZND1="6291@x" Pin0InfoVect0LinkObjId="g_1b879d0_0" Pin0InfoVect1LinkObjId="SW-39183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-135 3952,-128 3992,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b88530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-13 3992,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34500@0" ObjectIDZND0="g_1b879d0@1" Pin0InfoVect0LinkObjId="g_1b879d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.046Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-13 3992,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b88770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-112 3992,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1b879d0@0" ObjectIDZND0="6291@x" ObjectIDZND1="6292@x" Pin0InfoVect0LinkObjId="SW-39183_0" Pin0InfoVect1LinkObjId="SW-39184_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b879d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-112 3992,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b889d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-128 3992,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1b879d0@0" ObjectIDND1="6292@x" ObjectIDZND0="6291@0" Pin0InfoVect0LinkObjId="SW-39183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b879d0_0" Pin1InfoVect1LinkObjId="SW-39184_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-128 3992,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bea700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-365 4106,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6283@1" Pin0InfoVect0LinkObjId="SW-39161_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-365 4106,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bea960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-241 4106,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6285@1" ObjectIDZND0="6282@0" Pin0InfoVect0LinkObjId="SW-39160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39163_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-241 4106,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1becf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-300 4085,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6282@x" ObjectIDND1="6283@x" ObjectIDZND0="6284@1" Pin0InfoVect0LinkObjId="SW-39162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39160_0" Pin1InfoVect1LinkObjId="SW-39161_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-300 4085,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bed170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-300 4028,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6284@0" ObjectIDZND0="g_1bed3d0@0" Pin0InfoVect0LinkObjId="g_1bed3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-300 4028,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b3d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-286 4106,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6282@1" ObjectIDZND0="6283@x" ObjectIDZND1="6284@x" Pin0InfoVect0LinkObjId="SW-39161_0" Pin0InfoVect1LinkObjId="SW-39162_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39160_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-286 4106,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b3db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-300 4106,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6282@x" ObjectIDND1="6284@x" ObjectIDZND0="6283@0" Pin0InfoVect0LinkObjId="SW-39161_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39160_0" Pin1InfoVect1LinkObjId="SW-39162_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-300 4106,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b40510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4066,-183 4066,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11141@0" ObjectIDZND0="6286@1" Pin0InfoVect0LinkObjId="SW-39164_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4066,-183 4066,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b40770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4066,-135 4066,-128 4106,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6286@0" ObjectIDZND0="g_1b409d0@0" ObjectIDZND1="6285@x" Pin0InfoVect0LinkObjId="g_1b409d0_0" Pin0InfoVect1LinkObjId="SW-39163_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39164_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4066,-135 4066,-128 4106,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a578b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-15 4106,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34501@0" ObjectIDZND0="g_1b409d0@1" Pin0InfoVect0LinkObjId="g_1b409d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.045Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-15 4106,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a57b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-112 4106,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1b409d0@0" ObjectIDZND0="6285@x" ObjectIDZND1="6286@x" Pin0InfoVect0LinkObjId="SW-39163_0" Pin0InfoVect1LinkObjId="SW-39164_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b409d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-112 4106,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a57d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-128 4106,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1b409d0@0" ObjectIDND1="6286@x" ObjectIDZND0="6285@0" Pin0InfoVect0LinkObjId="SW-39163_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b409d0_0" Pin1InfoVect1LinkObjId="SW-39164_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-128 4106,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7b470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-365 4218,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6277@1" Pin0InfoVect0LinkObjId="SW-39141_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-365 4218,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7b6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-242 4218,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6279@1" ObjectIDZND0="6276@0" Pin0InfoVect0LinkObjId="SW-39140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39143_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-242 4218,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b42dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-300 4197,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6276@x" ObjectIDND1="6277@x" ObjectIDZND0="6278@1" Pin0InfoVect0LinkObjId="SW-39142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39140_0" Pin1InfoVect1LinkObjId="SW-39141_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-300 4197,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b43020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-300 4140,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6278@0" ObjectIDZND0="g_1b43280@0" Pin0InfoVect0LinkObjId="g_1b43280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-300 4140,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b43d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-286 4218,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6276@1" ObjectIDZND0="6277@x" ObjectIDZND1="6278@x" Pin0InfoVect0LinkObjId="SW-39141_0" Pin0InfoVect1LinkObjId="SW-39142_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39140_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-286 4218,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b43f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-300 4218,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6276@x" ObjectIDND1="6278@x" ObjectIDZND0="6277@0" Pin0InfoVect0LinkObjId="SW-39141_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39140_0" Pin1InfoVect1LinkObjId="SW-39142_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-300 4218,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aef9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-183 4178,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11141@0" ObjectIDZND0="6280@1" Pin0InfoVect0LinkObjId="SW-39144_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-183 4178,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aefc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-135 4178,-128 4218,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6280@0" ObjectIDZND0="g_1aefe60@0" ObjectIDZND1="6279@x" Pin0InfoVect0LinkObjId="g_1aefe60_0" Pin0InfoVect1LinkObjId="SW-39143_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39144_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-135 4178,-128 4218,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af0b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-15 4218,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34502@0" ObjectIDZND0="g_1aefe60@1" Pin0InfoVect0LinkObjId="g_1aefe60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.044Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-15 4218,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af0d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-112 4218,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1aefe60@0" ObjectIDZND0="6279@x" ObjectIDZND1="6280@x" Pin0InfoVect0LinkObjId="SW-39143_0" Pin0InfoVect1LinkObjId="SW-39144_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aefe60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-112 4218,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af0ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-128 4218,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1aefe60@0" ObjectIDND1="6280@x" ObjectIDZND0="6279@0" Pin0InfoVect0LinkObjId="SW-39143_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aefe60_0" Pin1InfoVect1LinkObjId="SW-39144_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-128 4218,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcebf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4330,-365 4330,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6271@1" Pin0InfoVect0LinkObjId="SW-39121_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4330,-365 4330,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4330,-240 4330,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6273@1" ObjectIDZND0="6270@0" Pin0InfoVect0LinkObjId="SW-39120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39123_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4330,-240 4330,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8caf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4330,-300 4309,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6270@x" ObjectIDND1="6271@x" ObjectIDZND0="6272@1" Pin0InfoVect0LinkObjId="SW-39122_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39120_0" Pin1InfoVect1LinkObjId="SW-39121_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4330,-300 4309,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8cd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-300 4252,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6272@0" ObjectIDZND0="g_1b8cfb0@0" Pin0InfoVect0LinkObjId="g_1b8cfb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39122_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-300 4252,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8da40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4330,-286 4330,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6270@1" ObjectIDZND0="6271@x" ObjectIDZND1="6272@x" Pin0InfoVect0LinkObjId="SW-39121_0" Pin0InfoVect1LinkObjId="SW-39122_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4330,-286 4330,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8dca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4330,-300 4330,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6270@x" ObjectIDND1="6272@x" ObjectIDZND0="6271@0" Pin0InfoVect0LinkObjId="SW-39121_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39120_0" Pin1InfoVect1LinkObjId="SW-39122_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4330,-300 4330,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aeab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-183 4290,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11141@0" ObjectIDZND0="6274@1" Pin0InfoVect0LinkObjId="SW-39124_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-183 4290,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aeadf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-135 4290,-128 4330,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6274@0" ObjectIDZND0="g_1aeb050@0" ObjectIDZND1="6273@x" Pin0InfoVect0LinkObjId="g_1aeb050_0" Pin0InfoVect1LinkObjId="SW-39123_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39124_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-135 4290,-128 4330,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aebda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4330,-15 4330,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34503@0" ObjectIDZND0="g_1aeb050@1" Pin0InfoVect0LinkObjId="g_1aeb050_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.043Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4330,-15 4330,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aec000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4330,-112 4330,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1aeb050@0" ObjectIDZND0="6273@x" ObjectIDZND1="6274@x" Pin0InfoVect0LinkObjId="SW-39123_0" Pin0InfoVect1LinkObjId="SW-39124_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aeb050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4330,-112 4330,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aec260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4330,-128 4330,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1aeb050@0" ObjectIDND1="6274@x" ObjectIDZND0="6273@0" Pin0InfoVect0LinkObjId="SW-39123_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1aeb050_0" Pin1InfoVect1LinkObjId="SW-39124_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4330,-128 4330,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-365 4443,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6265@1" Pin0InfoVect0LinkObjId="SW-39101_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-365 4443,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9e5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-239 4443,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6267@1" ObjectIDZND0="6264@0" Pin0InfoVect0LinkObjId="SW-39100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39103_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-239 4443,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b0c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-300 4422,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6264@x" ObjectIDND1="6265@x" ObjectIDZND0="6266@1" Pin0InfoVect0LinkObjId="SW-39102_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39100_0" Pin1InfoVect1LinkObjId="SW-39101_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-300 4422,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b0cb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-300 4365,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6266@0" ObjectIDZND0="g_1b0cd60@0" Pin0InfoVect0LinkObjId="g_1b0cd60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39102_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-300 4365,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b0d7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-286 4443,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6264@1" ObjectIDZND0="6265@x" ObjectIDZND1="6266@x" Pin0InfoVect0LinkObjId="SW-39101_0" Pin0InfoVect1LinkObjId="SW-39102_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-286 4443,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b0da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-300 4443,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6264@x" ObjectIDND1="6266@x" ObjectIDZND0="6265@0" Pin0InfoVect0LinkObjId="SW-39101_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39100_0" Pin1InfoVect1LinkObjId="SW-39102_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-300 4443,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b104b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-183 4403,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11141@0" ObjectIDZND0="6268@1" Pin0InfoVect0LinkObjId="SW-39104_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-183 4403,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b79880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-135 4403,-128 4443,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6268@0" ObjectIDZND0="g_1b79ae0@0" ObjectIDZND1="6267@x" Pin0InfoVect0LinkObjId="g_1b79ae0_0" Pin0InfoVect1LinkObjId="SW-39103_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39104_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-135 4403,-128 4443,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b7a830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-16 4443,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34504@0" ObjectIDZND0="g_1b79ae0@1" Pin0InfoVect0LinkObjId="g_1b79ae0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.042Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-16 4443,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b7aa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-112 4443,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1b79ae0@0" ObjectIDZND0="6267@x" ObjectIDZND1="6268@x" Pin0InfoVect0LinkObjId="SW-39103_0" Pin0InfoVect1LinkObjId="SW-39104_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b79ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-112 4443,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b7acf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-128 4443,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1b79ae0@0" ObjectIDND1="6268@x" ObjectIDZND0="6267@0" Pin0InfoVect0LinkObjId="SW-39103_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b79ae0_0" Pin1InfoVect1LinkObjId="SW-39104_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-128 4443,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b576e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-365 4556,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6259@1" Pin0InfoVect0LinkObjId="SW-98119_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-365 4556,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b59e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-300 4535,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6258@x" ObjectIDND1="6259@x" ObjectIDZND0="6260@1" Pin0InfoVect0LinkObjId="SW-39082_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39080_0" Pin1InfoVect1LinkObjId="SW-98119_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-300 4535,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b5a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-300 4478,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6260@0" ObjectIDZND0="g_1b5a330@0" Pin0InfoVect0LinkObjId="g_1b5a330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39082_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-300 4478,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b16e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-286 4556,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6258@1" ObjectIDZND0="6259@x" ObjectIDZND1="6260@x" Pin0InfoVect0LinkObjId="SW-98119_0" Pin0InfoVect1LinkObjId="SW-39082_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-286 4556,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b170a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-300 4556,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6258@x" ObjectIDND1="6260@x" ObjectIDZND0="6259@0" Pin0InfoVect0LinkObjId="SW-98119_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39080_0" Pin1InfoVect1LinkObjId="SW-39082_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-300 4556,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b19930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4516,-183 4516,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11141@0" ObjectIDZND0="6262@1" Pin0InfoVect0LinkObjId="SW-39084_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4516,-183 4516,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b19b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4516,-135 4516,-128 4556,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6262@0" ObjectIDZND0="g_1b19df0@0" ObjectIDZND1="6263@x" Pin0InfoVect0LinkObjId="g_1b19df0_0" Pin0InfoVect1LinkObjId="SW-39085_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39084_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4516,-135 4516,-128 4556,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1ab40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-112 4556,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1b19df0@0" ObjectIDZND0="6263@x" ObjectIDZND1="6262@x" Pin0InfoVect0LinkObjId="SW-39085_0" Pin0InfoVect1LinkObjId="SW-39084_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b19df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-112 4556,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1ada0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-128 4556,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1b19df0@0" ObjectIDND1="6262@x" ObjectIDZND0="6263@0" Pin0InfoVect0LinkObjId="SW-39085_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b19df0_0" Pin1InfoVect1LinkObjId="SW-39084_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-128 4556,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae3580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-365 4968,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11138@0" ObjectIDZND0="6307@1" Pin0InfoVect0LinkObjId="SW-39241_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-365 4968,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae37e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-240 4968,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6309@1" ObjectIDZND0="6306@0" Pin0InfoVect0LinkObjId="SW-39240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-240 4968,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae5f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-300 4947,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6306@x" ObjectIDND1="6307@x" ObjectIDZND0="6308@1" Pin0InfoVect0LinkObjId="SW-39242_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39240_0" Pin1InfoVect1LinkObjId="SW-39241_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-300 4947,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae61d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4911,-300 4907,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6308@0" ObjectIDZND0="g_1da0010@0" Pin0InfoVect0LinkObjId="g_1da0010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4911,-300 4907,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae6430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-286 4968,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6306@1" ObjectIDZND0="6307@x" ObjectIDZND1="6308@x" Pin0InfoVect0LinkObjId="SW-39241_0" Pin0InfoVect1LinkObjId="SW-39242_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-286 4968,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae6690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-300 4968,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6306@x" ObjectIDND1="6308@x" ObjectIDZND0="6307@0" Pin0InfoVect0LinkObjId="SW-39241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39240_0" Pin1InfoVect1LinkObjId="SW-39242_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-300 4968,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae90f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-183 4928,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11139@0" ObjectIDZND0="6310@1" Pin0InfoVect0LinkObjId="SW-39244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da2280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-183 4928,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae9350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-135 4928,-128 4968,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6310@0" ObjectIDZND0="6309@x" ObjectIDZND1="g_1ae95b0@0" Pin0InfoVect0LinkObjId="SW-39243_0" Pin0InfoVect1LinkObjId="g_1ae95b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-135 4928,-128 4968,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba4d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-119 4968,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ae95b0@0" ObjectIDZND0="6309@x" ObjectIDZND1="6310@x" Pin0InfoVect0LinkObjId="SW-39243_0" Pin0InfoVect1LinkObjId="SW-39244_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ae95b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-119 4968,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba4fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-128 4968,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6310@x" ObjectIDND1="g_1ae95b0@0" ObjectIDZND0="6309@0" Pin0InfoVect0LinkObjId="SW-39243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39244_0" Pin1InfoVect1LinkObjId="g_1ae95b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-128 4968,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1add070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-365 5081,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11138@0" ObjectIDZND0="6313@1" Pin0InfoVect0LinkObjId="SW-39261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-365 5081,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1add2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-243 5081,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6315@1" ObjectIDZND0="6312@0" Pin0InfoVect0LinkObjId="SW-39260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-243 5081,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1adfa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-300 5059,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6312@x" ObjectIDND1="6313@x" ObjectIDZND0="6314@1" Pin0InfoVect0LinkObjId="SW-39262_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39260_0" Pin1InfoVect1LinkObjId="SW-39261_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-300 5059,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1adfcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5023,-300 5002,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6314@0" ObjectIDZND0="g_1adff20@0" Pin0InfoVect0LinkObjId="g_1adff20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39262_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5023,-300 5002,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae09b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-286 5081,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6312@1" ObjectIDZND0="6313@x" ObjectIDZND1="6314@x" Pin0InfoVect0LinkObjId="SW-39261_0" Pin0InfoVect1LinkObjId="SW-39262_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-286 5081,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae0c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-300 5081,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6312@x" ObjectIDND1="6314@x" ObjectIDZND0="6313@0" Pin0InfoVect0LinkObjId="SW-39261_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39260_0" Pin1InfoVect1LinkObjId="SW-39262_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-300 5081,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b37900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-183 5040,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11139@0" ObjectIDZND0="6316@1" Pin0InfoVect0LinkObjId="SW-39264_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da2280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-183 5040,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b37b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-135 5040,-128 5081,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6316@0" ObjectIDZND0="6315@x" ObjectIDZND1="g_1b37dc0@0" Pin0InfoVect0LinkObjId="SW-39263_0" Pin0InfoVect1LinkObjId="g_1b37dc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-135 5040,-128 5081,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b38b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-121 5081,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1b37dc0@0" ObjectIDZND0="6315@x" ObjectIDZND1="6316@x" Pin0InfoVect0LinkObjId="SW-39263_0" Pin0InfoVect1LinkObjId="SW-39264_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b37dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-121 5081,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b38d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-128 5081,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6316@x" ObjectIDND1="g_1b37dc0@0" ObjectIDZND0="6315@0" Pin0InfoVect0LinkObjId="SW-39263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39264_0" Pin1InfoVect1LinkObjId="g_1b37dc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-128 5081,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a112b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-365 5192,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11138@0" ObjectIDZND0="6319@1" Pin0InfoVect0LinkObjId="SW-39281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-365 5192,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a11510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-243 5192,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6321@1" ObjectIDZND0="6318@0" Pin0InfoVect0LinkObjId="SW-39280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-243 5192,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a13ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-300 5171,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6318@x" ObjectIDND1="6319@x" ObjectIDZND0="6320@1" Pin0InfoVect0LinkObjId="SW-39282_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39280_0" Pin1InfoVect1LinkObjId="SW-39281_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-300 5171,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a13f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5135,-300 5114,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6320@0" ObjectIDZND0="g_1a14160@0" Pin0InfoVect0LinkObjId="g_1a14160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39282_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5135,-300 5114,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a14bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-286 5192,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6318@1" ObjectIDZND0="6319@x" ObjectIDZND1="6320@x" Pin0InfoVect0LinkObjId="SW-39281_0" Pin0InfoVect1LinkObjId="SW-39282_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-286 5192,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a14e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-300 5192,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6318@x" ObjectIDND1="6320@x" ObjectIDZND0="6319@0" Pin0InfoVect0LinkObjId="SW-39281_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39280_0" Pin1InfoVect1LinkObjId="SW-39282_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-300 5192,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1acf3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-183 5152,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11139@0" ObjectIDZND0="6322@1" Pin0InfoVect0LinkObjId="SW-39284_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da2280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-183 5152,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1acf600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5152,-135 5152,-128 5192,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6322@0" ObjectIDZND0="6321@x" ObjectIDZND1="g_1acf860@0" Pin0InfoVect0LinkObjId="SW-39283_0" Pin0InfoVect1LinkObjId="g_1acf860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39284_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5152,-135 5152,-128 5192,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad05b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-120 5192,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1acf860@0" ObjectIDZND0="6321@x" ObjectIDZND1="6322@x" Pin0InfoVect0LinkObjId="SW-39283_0" Pin0InfoVect1LinkObjId="SW-39284_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acf860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-120 5192,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad0810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-128 5192,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6322@x" ObjectIDND1="g_1acf860@0" ObjectIDZND0="6321@0" Pin0InfoVect0LinkObjId="SW-39283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39284_0" Pin1InfoVect1LinkObjId="g_1acf860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-128 5192,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b27c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5305,-365 5305,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11138@0" ObjectIDZND0="6325@1" Pin0InfoVect0LinkObjId="SW-39301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5305,-365 5305,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b27e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5305,-240 5305,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6327@1" ObjectIDZND0="6324@0" Pin0InfoVect0LinkObjId="SW-39300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39303_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5305,-240 5305,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2a600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5305,-300 5284,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6324@x" ObjectIDND1="6325@x" ObjectIDZND0="6326@1" Pin0InfoVect0LinkObjId="SW-39302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39300_0" Pin1InfoVect1LinkObjId="SW-39301_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5305,-300 5284,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2a860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5248,-300 5227,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6326@0" ObjectIDZND0="g_1b2aac0@0" Pin0InfoVect0LinkObjId="g_1b2aac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5248,-300 5227,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2b550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5305,-286 5305,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6324@1" ObjectIDZND0="6325@x" ObjectIDZND1="6326@x" Pin0InfoVect0LinkObjId="SW-39301_0" Pin0InfoVect1LinkObjId="SW-39302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5305,-286 5305,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2b7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5305,-300 5305,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6324@x" ObjectIDND1="6326@x" ObjectIDZND0="6325@0" Pin0InfoVect0LinkObjId="SW-39301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39300_0" Pin1InfoVect1LinkObjId="SW-39302_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5305,-300 5305,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab7130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5265,-183 5265,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11139@0" ObjectIDZND0="6328@1" Pin0InfoVect0LinkObjId="SW-39304_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da2280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5265,-183 5265,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab7390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5265,-135 5265,-128 5305,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6328@0" ObjectIDZND0="6327@x" ObjectIDZND1="g_1ab75f0@0" Pin0InfoVect0LinkObjId="SW-39303_0" Pin0InfoVect1LinkObjId="g_1ab75f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5265,-135 5265,-128 5305,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab8340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5305,-13 5305,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34508@0" ObjectIDZND0="g_1ab75f0@1" Pin0InfoVect0LinkObjId="g_1ab75f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.055Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5305,-13 5305,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab85a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5305,-119 5305,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ab75f0@0" ObjectIDZND0="6327@x" ObjectIDZND1="6328@x" Pin0InfoVect0LinkObjId="SW-39303_0" Pin0InfoVect1LinkObjId="SW-39304_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ab75f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5305,-119 5305,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab8800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5305,-128 5305,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6328@x" ObjectIDND1="g_1ab75f0@0" ObjectIDZND0="6327@0" Pin0InfoVect0LinkObjId="SW-39303_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39304_0" Pin1InfoVect1LinkObjId="g_1ab75f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5305,-128 5305,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a5bfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5419,-365 5419,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11138@0" ObjectIDZND0="6331@1" Pin0InfoVect0LinkObjId="SW-39321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5419,-365 5419,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a5c240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5419,-243 5419,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6333@1" ObjectIDZND0="6330@0" Pin0InfoVect0LinkObjId="SW-39320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39323_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5419,-243 5419,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a5e7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5419,-300 5397,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6330@x" ObjectIDND1="6331@x" ObjectIDZND0="6332@1" Pin0InfoVect0LinkObjId="SW-39322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39320_0" Pin1InfoVect1LinkObjId="SW-39321_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5419,-300 5397,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a5ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5361,-300 5340,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6332@0" ObjectIDZND0="g_1a5ec90@0" Pin0InfoVect0LinkObjId="g_1a5ec90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5361,-300 5340,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a5f6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5419,-286 5419,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6330@1" ObjectIDZND0="6331@x" ObjectIDZND1="6332@x" Pin0InfoVect0LinkObjId="SW-39321_0" Pin0InfoVect1LinkObjId="SW-39322_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5419,-286 5419,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a5f900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5419,-300 5419,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6330@x" ObjectIDND1="6332@x" ObjectIDZND0="6331@0" Pin0InfoVect0LinkObjId="SW-39321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39320_0" Pin1InfoVect1LinkObjId="SW-39322_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5419,-300 5419,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a62360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5378,-183 5378,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11139@0" ObjectIDZND0="6334@1" Pin0InfoVect0LinkObjId="SW-39324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da2280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5378,-183 5378,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a625c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5378,-135 5378,-128 5419,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6334@0" ObjectIDZND0="6333@x" ObjectIDZND1="g_1a62820@0" Pin0InfoVect0LinkObjId="SW-39323_0" Pin0InfoVect1LinkObjId="g_1a62820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5378,-135 5378,-128 5419,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a63570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5419,-121 5419,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1a62820@0" ObjectIDZND0="6333@x" ObjectIDZND1="6334@x" Pin0InfoVect0LinkObjId="SW-39323_0" Pin0InfoVect1LinkObjId="SW-39324_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5419,-121 5419,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a637d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5419,-128 5419,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6334@x" ObjectIDND1="g_1a62820@0" ObjectIDZND0="6333@0" Pin0InfoVect0LinkObjId="SW-39323_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39324_0" Pin1InfoVect1LinkObjId="g_1a62820_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5419,-128 5419,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7e980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5474,-365 5474,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11138@0" ObjectIDZND0="6341@1" Pin0InfoVect0LinkObjId="SW-39359_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5474,-365 5474,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5474,-241 5474,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6343@1" ObjectIDZND0="6340@0" Pin0InfoVect0LinkObjId="SW-39358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39361_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5474,-241 5474,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7ee40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5495,-298 5474,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6342@0" ObjectIDZND0="6340@x" ObjectIDZND1="6341@x" Pin0InfoVect0LinkObjId="SW-39358_0" Pin0InfoVect1LinkObjId="SW-39359_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5495,-298 5474,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7f0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5474,-284 5474,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6340@1" ObjectIDZND0="6341@x" ObjectIDZND1="6342@x" Pin0InfoVect0LinkObjId="SW-39359_0" Pin0InfoVect1LinkObjId="SW-39360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39358_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5474,-284 5474,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7f300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5474,-298 5474,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6340@x" ObjectIDND1="6342@x" ObjectIDZND0="6341@0" Pin0InfoVect0LinkObjId="SW-39359_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39358_0" Pin1InfoVect1LinkObjId="SW-39360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5474,-298 5474,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7f560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5474,-183 5474,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11139@0" ObjectIDZND0="6343@0" Pin0InfoVect0LinkObjId="SW-39361_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da2280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5474,-183 5474,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a82580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5531,-298 5550,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6342@1" ObjectIDZND0="g_1a827e0@0" Pin0InfoVect0LinkObjId="g_1a827e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5531,-298 5550,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8a2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-365 5592,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11138@0" ObjectIDZND0="6351@1" Pin0InfoVect0LinkObjId="SW-39409_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-365 5592,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8a520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-239 5592,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6353@1" ObjectIDZND0="6350@0" Pin0InfoVect0LinkObjId="SW-39408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39411_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-239 5592,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8b4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-79 5592,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="40678@0" ObjectIDZND0="g_1a8a780@1" Pin0InfoVect0LinkObjId="g_1a8a780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_TJT.CX_TJT_057Cb_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-79 5592,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8b730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5615,-298 5592,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6352@0" ObjectIDZND0="6350@x" ObjectIDZND1="6351@x" Pin0InfoVect0LinkObjId="SW-39408_0" Pin0InfoVect1LinkObjId="SW-39409_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5615,-298 5592,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8dec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5651,-298 5670,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6352@1" ObjectIDZND0="g_1a8e120@0" Pin0InfoVect0LinkObjId="g_1a8e120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5651,-298 5670,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-285 5592,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6350@1" ObjectIDZND0="6351@x" ObjectIDZND1="6352@x" Pin0InfoVect0LinkObjId="SW-39409_0" Pin0InfoVect1LinkObjId="SW-39410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-285 5592,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8f6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-298 5592,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6350@x" ObjectIDND1="6352@x" ObjectIDZND0="6351@0" Pin0InfoVect0LinkObjId="SW-39409_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39408_0" Pin1InfoVect1LinkObjId="SW-39410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-298 5592,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c67810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5650,-193 5669,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6354@1" ObjectIDZND0="g_1c67a70@0" Pin0InfoVect0LinkObjId="g_1c67a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5650,-193 5669,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c68500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5614,-193 5591,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6354@0" ObjectIDZND0="g_1a8a780@0" ObjectIDZND1="6353@x" Pin0InfoVect0LinkObjId="g_1a8a780_0" Pin0InfoVect1LinkObjId="SW-39411_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5614,-193 5591,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c68ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-206 5592,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6353@0" ObjectIDZND0="g_1a8a780@0" ObjectIDZND1="6354@x" Pin0InfoVect0LinkObjId="g_1a8a780_0" Pin0InfoVect1LinkObjId="SW-39412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-206 5592,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c69250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5592,-193 5592,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6353@x" ObjectIDND1="6354@x" ObjectIDZND0="g_1a8a780@0" Pin0InfoVect0LinkObjId="g_1a8a780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39411_0" Pin1InfoVect1LinkObjId="SW-39412_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5592,-193 5592,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c752c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-365 4403,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11140@0" ObjectIDZND0="6231@0" Pin0InfoVect0LinkObjId="SW-38997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dce800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-365 4403,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c75520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-439 4403,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6232@0" ObjectIDZND0="g_1a3acf0@0" ObjectIDZND1="6231@x" Pin0InfoVect0LinkObjId="g_1a3acf0_0" Pin0InfoVect1LinkObjId="SW-38997_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-439 4403,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c77cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4460,-439 4479,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6232@1" ObjectIDZND0="g_1c77f10@0" Pin0InfoVect0LinkObjId="g_1c77f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4460,-439 4479,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c79230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-424 4403,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6231@1" ObjectIDZND0="g_1a3acf0@0" ObjectIDZND1="6232@x" Pin0InfoVect0LinkObjId="g_1a3acf0_0" Pin0InfoVect1LinkObjId="SW-38998_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-424 4403,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c79490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-439 4403,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6231@x" ObjectIDND1="6232@x" ObjectIDZND0="g_1a3acf0@0" Pin0InfoVect0LinkObjId="g_1a3acf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38997_0" Pin1InfoVect1LinkObjId="SW-38998_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-439 4403,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a15960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5214,-365 5214,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11138@0" ObjectIDZND0="6233@0" Pin0InfoVect0LinkObjId="SW-38999_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5214,-365 5214,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a15bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5235,-440 5214,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6234@0" ObjectIDZND0="g_1a19500@0" ObjectIDZND1="6233@x" Pin0InfoVect0LinkObjId="g_1a19500_0" Pin0InfoVect1LinkObjId="SW-38999_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5235,-440 5214,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a18350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5271,-440 5290,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6234@1" ObjectIDZND0="g_1a185b0@0" Pin0InfoVect0LinkObjId="g_1a185b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5271,-440 5290,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a19040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5214,-425 5214,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6233@1" ObjectIDZND0="g_1a19500@0" ObjectIDZND1="6234@x" Pin0InfoVect0LinkObjId="g_1a19500_0" Pin0InfoVect1LinkObjId="SW-39000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38999_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5214,-425 5214,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a192a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5214,-440 5214,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6233@x" ObjectIDND1="6234@x" ObjectIDZND0="g_1a19500@0" Pin0InfoVect0LinkObjId="g_1a19500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38999_0" Pin1InfoVect1LinkObjId="SW-39000_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5214,-440 5214,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa3fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-12 3878,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="34499@0" ObjectIDZND0="6299@0" Pin0InfoVect0LinkObjId="SW-39205_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.047Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-12 3878,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa4200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-58 3878,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6299@1" ObjectIDZND0="g_1baf900@1" Pin0InfoVect0LinkObjId="g_1baf900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39205_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-58 3878,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aab460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-11 4968,-20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="34505@0" ObjectIDZND0="6311@0" Pin0InfoVect0LinkObjId="SW-39245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.052Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-11 4968,-20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aab6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-56 4968,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6311@1" ObjectIDZND0="g_1ae95b0@1" Pin0InfoVect0LinkObjId="g_1ae95b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-56 4968,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a30280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-11 5081,-21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="34506@0" ObjectIDZND0="6317@0" Pin0InfoVect0LinkObjId="SW-39265_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.053Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-11 5081,-21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a304e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-57 5081,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6317@1" ObjectIDZND0="g_1b37dc0@1" Pin0InfoVect0LinkObjId="g_1b37dc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39265_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-57 5081,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a32d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-12 5192,-21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="34507@0" ObjectIDZND0="6323@0" Pin0InfoVect0LinkObjId="SW-39285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.054Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-12 5192,-21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a32fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5192,-57 5192,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6323@1" ObjectIDZND0="g_1acf860@1" Pin0InfoVect0LinkObjId="g_1acf860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5192,-57 5192,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a35a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5419,-13 5419,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="34509@0" ObjectIDZND0="6335@0" Pin0InfoVect0LinkObjId="SW-39325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_TJT.056Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5419,-13 5419,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a35c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5419,-58 5419,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6335@1" ObjectIDZND0="g_1a62820@1" Pin0InfoVect0LinkObjId="g_1a62820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5419,-58 5419,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a54a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-1129 4294,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="6242@1" ObjectIDZND0="6238@x" ObjectIDZND1="6239@x" ObjectIDZND2="6241@x" Pin0InfoVect0LinkObjId="SW-98021_0" Pin0InfoVect1LinkObjId="SW-39005_0" Pin0InfoVect2LinkObjId="SW-39007_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39008_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-1129 4294,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d651b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-1043 4294,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6238@x" ObjectIDND1="6239@x" ObjectIDND2="6241@x" ObjectIDZND0="g_1d65670@0" ObjectIDZND1="6242@x" Pin0InfoVect0LinkObjId="g_1d65670_0" Pin0InfoVect1LinkObjId="SW-39008_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-98021_0" Pin1InfoVect1LinkObjId="SW-39005_0" Pin1InfoVect2LinkObjId="SW-39007_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-1043 4294,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d65410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-1043 4738,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6250@x" ObjectIDND1="6251@x" ObjectIDND2="6253@x" ObjectIDZND0="g_1d67370@0" ObjectIDZND1="6254@x" Pin0InfoVect0LinkObjId="g_1d67370_0" Pin0InfoVect1LinkObjId="SW-39049_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39045_0" Pin1InfoVect1LinkObjId="SW-39046_0" Pin1InfoVect2LinkObjId="SW-39048_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-1043 4738,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d663c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-1129 4294,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6238@x" ObjectIDND1="6239@x" ObjectIDND2="6241@x" ObjectIDZND0="g_1d65670@1" Pin0InfoVect0LinkObjId="g_1d65670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-98021_0" Pin1InfoVect1LinkObjId="SW-39005_0" Pin1InfoVect2LinkObjId="SW-39007_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-1129 4294,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d68950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-1122 4515,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6245@x" ObjectIDND1="6244@x" ObjectIDND2="6247@x" ObjectIDZND0="g_1d66620@1" Pin0InfoVect0LinkObjId="g_1d66620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39026_0" Pin1InfoVect1LinkObjId="SW-39025_0" Pin1InfoVect2LinkObjId="SW-39028_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-1122 4515,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d68bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-1128 4738,-1144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6250@x" ObjectIDND1="6251@x" ObjectIDND2="6253@x" ObjectIDZND0="g_1d67370@1" Pin0InfoVect0LinkObjId="g_1d67370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-39045_0" Pin1InfoVect1LinkObjId="SW-39046_0" Pin1InfoVect2LinkObjId="SW-39048_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-1128 4738,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d76b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-243 4556,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="6258@0" Pin0InfoVect0LinkObjId="SW-39080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-243 4556,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d771a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4523,-21 4523,-33 4556,-33 4556,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="31724@1" ObjectIDZND0="g_1b19df0@1" Pin0InfoVect0LinkObjId="g_1b19df0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-98125_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4523,-21 4523,-33 4556,-33 4556,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d789d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,30 4523,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d778f0@0" ObjectIDZND0="g_1d79ad0@0" ObjectIDZND1="31724@x" Pin0InfoVect0LinkObjId="g_1d79ad0_0" Pin0InfoVect1LinkObjId="SW-98125_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d778f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4532,30 4523,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d794c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4523,16 4523,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="31724@0" ObjectIDZND0="g_1d778f0@0" ObjectIDZND1="g_1d79ad0@0" Pin0InfoVect0LinkObjId="g_1d778f0_0" Pin0InfoVect1LinkObjId="g_1d79ad0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-98125_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4523,16 4523,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d79720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4523,30 4523,49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d778f0@0" ObjectIDND1="31724@x" ObjectIDZND0="g_1d79ad0@0" Pin0InfoVect0LinkObjId="g_1d79ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d778f0_0" Pin1InfoVect1LinkObjId="SW-98125_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4523,30 4523,49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d827b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-16 4809,-28 4842,-28 4842,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="31725@1" ObjectIDZND0="g_1d7e3b0@1" Pin0InfoVect0LinkObjId="g_1d7e3b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-98126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-16 4809,-28 4842,-28 4842,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d83df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4818,35 4809,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1d82e70@0" ObjectIDZND0="31725@x" ObjectIDZND1="g_1d84660@0" Pin0InfoVect0LinkObjId="SW-98126_0" Pin0InfoVect1LinkObjId="g_1d84660_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d82e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4818,35 4809,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d84050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,21 4809,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="31725@0" ObjectIDZND0="g_1d82e70@0" ObjectIDZND1="g_1d84660@0" Pin0InfoVect0LinkObjId="g_1d82e70_0" Pin0InfoVect1LinkObjId="g_1d84660_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-98126_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4809,21 4809,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d842b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,35 4809,54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d82e70@0" ObjectIDND1="31725@x" ObjectIDZND0="g_1d84660@0" Pin0InfoVect0LinkObjId="g_1d84660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d82e70_0" Pin1InfoVect1LinkObjId="SW-98126_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,35 4809,54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8f8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-299 4765,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6302@0" ObjectIDZND0="g_1da0aa0@0" Pin0InfoVect0LinkObjId="g_1da0aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39222_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-299 4765,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8fb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-299 4843,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6302@1" ObjectIDZND0="6303@x" ObjectIDZND1="6300@x" Pin0InfoVect0LinkObjId="SW-39221_0" Pin0InfoVect1LinkObjId="SW-39220_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39222_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-299 4843,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d932b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-247 4842,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1d92580@0" ObjectIDZND0="6300@x" ObjectIDZND1="6305@x" Pin0InfoVect0LinkObjId="SW-39220_0" Pin0InfoVect1LinkObjId="SW-39223_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d92580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-247 4842,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d93da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4842,-259 4842,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6300@0" ObjectIDZND0="g_1d92580@0" ObjectIDZND1="6305@x" Pin0InfoVect0LinkObjId="g_1d92580_0" Pin0InfoVect1LinkObjId="SW-39223_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4842,-259 4842,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d94000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4842,-247 4842,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1d92580@0" ObjectIDND1="6300@x" ObjectIDZND0="6305@1" Pin0InfoVect0LinkObjId="SW-39223_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d92580_0" Pin1InfoVect1LinkObjId="SW-39220_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4842,-247 4842,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4841,-195 4841,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6305@0" ObjectIDZND0="6304@x" ObjectIDZND1="g_1d7e3b0@0" Pin0InfoVect0LinkObjId="SW-39224_0" Pin0InfoVect1LinkObjId="g_1d7e3b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39223_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4841,-195 4841,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4842,-365 4842,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11138@0" ObjectIDZND0="6303@1" Pin0InfoVect0LinkObjId="SW-39221_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dcea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4842,-365 4842,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da1dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4842,-313 4842,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6303@0" ObjectIDZND0="6300@x" ObjectIDZND1="6302@x" Pin0InfoVect0LinkObjId="SW-39220_0" Pin0InfoVect1LinkObjId="SW-39222_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39221_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4842,-313 4842,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da2020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4842,-299 4842,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6303@x" ObjectIDND1="6302@x" ObjectIDZND0="6300@1" Pin0InfoVect0LinkObjId="SW-39220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39221_0" Pin1InfoVect1LinkObjId="SW-39222_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4842,-299 4842,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da2280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4800,-164 4800,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6304@1" ObjectIDZND0="11139@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39224_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4800,-164 4800,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da8160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4800,-128 4841,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6304@0" ObjectIDZND0="6305@x" ObjectIDZND1="g_1d7e3b0@0" Pin0InfoVect0LinkObjId="SW-39223_0" Pin0InfoVect1LinkObjId="g_1d7e3b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39224_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4800,-128 4841,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da8350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4841,-128 4841,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6305@x" ObjectIDND1="6304@x" ObjectIDZND0="g_1d7e3b0@0" Pin0InfoVect0LinkObjId="g_1d7e3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39223_0" Pin1InfoVect1LinkObjId="SW-39224_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4841,-128 4841,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da8a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4596,-236 4596,-251 4556,-251 4556,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d760e0@0" ObjectIDZND0="6263@1" Pin0InfoVect0LinkObjId="SW-39085_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d760e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4596,-236 4596,-251 4556,-251 4556,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1daef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-1207 4294,-1207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1dae1c0@0" ObjectIDZND0="g_1d65670@0" ObjectIDZND1="37444@1" Pin0InfoVect0LinkObjId="g_1d65670_0" Pin0InfoVect1LinkObjId="g_1dafc80_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dae1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-1207 4294,-1207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1dafa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-1198 4294,-1207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1d65670@0" ObjectIDZND0="g_1dae1c0@0" ObjectIDZND1="37444@1" Pin0InfoVect0LinkObjId="g_1dae1c0_0" Pin0InfoVect1LinkObjId="g_1dafc80_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d65670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-1198 4294,-1207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1dafc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-1207 4294,-1216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_1dae1c0@0" ObjectIDND1="g_1d65670@0" ObjectIDZND0="37444@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dae1c0_0" Pin1InfoVect1LinkObjId="g_1d65670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-1207 4294,-1216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db0c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-1209 4515,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_1dafee0@0" ObjectIDZND0="g_1d66620@0" ObjectIDZND1="47292@x" Pin0InfoVect0LinkObjId="g_1d66620_0" Pin0InfoVect1LinkObjId="EC-CX_TJT.342Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dafee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-1209 4515,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db1780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-1192 4515,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_1d66620@0" ObjectIDZND0="g_1dafee0@0" ObjectIDZND1="47292@x" Pin0InfoVect0LinkObjId="g_1dafee0_0" Pin0InfoVect1LinkObjId="EC-CX_TJT.342Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d66620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-1192 4515,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db19e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-1209 4515,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_1dafee0@0" ObjectIDND1="g_1d66620@0" ObjectIDZND0="47292@0" Pin0InfoVect0LinkObjId="EC-CX_TJT.342Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dafee0_0" Pin1InfoVect1LinkObjId="g_1d66620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-1209 4515,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db1c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1217 4738,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1db1ea0@0" ObjectIDZND0="g_1d67370@0" ObjectIDZND1="37443@1" Pin0InfoVect0LinkObjId="g_1d67370_0" Pin0InfoVect1LinkObjId="g_1db3740_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1db1ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1217 4738,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db34e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-1197 4738,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1d67370@0" ObjectIDZND0="g_1db1ea0@0" ObjectIDZND1="37443@1" Pin0InfoVect0LinkObjId="g_1db1ea0_0" Pin0InfoVect1LinkObjId="g_1db3740_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d67370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-1197 4738,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1db3740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-1217 4738,-1223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_1db1ea0@0" ObjectIDND1="g_1d67370@0" ObjectIDZND0="37443@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1db1ea0_0" Pin1InfoVect1LinkObjId="g_1d67370_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-1217 4738,-1223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dc65e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-221 4727,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d61ce0@0" ObjectIDZND0="11136@0" Pin0InfoVect0LinkObjId="SW-98466_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d61ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-221 4727,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dc67f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-274 4727,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="11136@1" ObjectIDZND0="6344@x" ObjectIDZND1="11134@x" Pin0InfoVect0LinkObjId="SW-39376_0" Pin0InfoVect1LinkObjId="SW-39377_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-98466_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-274 4727,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dce800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-347 4620,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11135@1" ObjectIDZND0="11140@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-347 4620,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-347 4727,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11134@1" ObjectIDZND0="11138@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-347 4727,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcf550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4688,-294 4727,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6344@0" ObjectIDZND0="11134@x" ObjectIDZND1="11136@x" Pin0InfoVect0LinkObjId="SW-39377_0" Pin0InfoVect1LinkObjId="SW-98466_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4688,-294 4727,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcf7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-294 4727,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6344@x" ObjectIDND1="11136@x" ObjectIDZND0="11134@0" Pin0InfoVect0LinkObjId="SW-39377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39376_0" Pin1InfoVect1LinkObjId="SW-98466_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-294 4727,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcfa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-294 4620,-294 4620,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6344@1" ObjectIDZND0="11135@0" Pin0InfoVect0LinkObjId="SW-39378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-294 4620,-294 4620,-311 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4567,-39 4562,-41 4551,-29 4545,-33 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4564,-27 4564,-29 4556,-34 4555,-33 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4526,48 4532,48 4532,53 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4853,-34 4848,-36 4837,-24 4831,-28 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4850,-22 4850,-24 4842,-29 4841,-28 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4812,53 4818,53 4818,58 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37334" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3460.000000 -1103.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5916" ObjectName="DYN-CX_TJT"/>
     <cge:Meas_Ref ObjectId="37334"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.047Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 15.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34499" ObjectName="EC-CX_TJT.047Ld"/>
    <cge:TPSR_Ref TObjectID="34499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.046Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 14.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34500" ObjectName="EC-CX_TJT.046Ld"/>
    <cge:TPSR_Ref TObjectID="34500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.045Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4097.000000 12.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34501" ObjectName="EC-CX_TJT.045Ld"/>
    <cge:TPSR_Ref TObjectID="34501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 12.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34502" ObjectName="EC-CX_TJT.044Ld"/>
    <cge:TPSR_Ref TObjectID="34502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 12.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34503" ObjectName="EC-CX_TJT.043Ld"/>
    <cge:TPSR_Ref TObjectID="34503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.042Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 11.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34504" ObjectName="EC-CX_TJT.042Ld"/>
    <cge:TPSR_Ref TObjectID="34504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34505" ObjectName="EC-CX_TJT.052Ld"/>
    <cge:TPSR_Ref TObjectID="34505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5072.000000 16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34506" ObjectName="EC-CX_TJT.053Ld"/>
    <cge:TPSR_Ref TObjectID="34506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5183.000000 15.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34507" ObjectName="EC-CX_TJT.054Ld"/>
    <cge:TPSR_Ref TObjectID="34507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5296.000000 14.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34508" ObjectName="EC-CX_TJT.055Ld"/>
    <cge:TPSR_Ref TObjectID="34508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.056Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5410.000000 14.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34509" ObjectName="EC-CX_TJT.056Ld"/>
    <cge:TPSR_Ref TObjectID="34509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_TJT.342Ld">
    <use class="BKBV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4524.000000 -1254.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47292" ObjectName="EC-CX_TJT.342Ld"/>
    <cge:TPSR_Ref TObjectID="47292"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_TJT"/>
</svg>