<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-4" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="2151 -1233 3323 1700">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape17_0">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape17_1">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3897180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3897b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38983b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3898e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3899a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_389a660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_389aec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_389b7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_389be80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_389c7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_389c7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_389db30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_389db30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_389e530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_389f840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a0380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a0970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38a0f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a1c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a2470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a2aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38a31f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a3a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a42a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a4ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38a5e50" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a7570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38a7d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38a8ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38a9820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_38aaae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_38ab830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1710" width="3333" x="2146" y="-1238"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2153" x2="3104" y1="-1008" y2="-1008"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2619" x2="2619" y1="-1008" y2="-1008"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2158" x2="3104" y1="-961" y2="-961"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2154" x2="3103" y1="-913" y2="-913"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2156" x2="3103" y1="-865" y2="-865"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2156" x2="3102" y1="-817" y2="-817"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2153" x2="3103" y1="-769" y2="-769"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2153" x2="3104" y1="-721" y2="-721"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2154" x2="3104" y1="-673" y2="-673"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2156" x2="3104" y1="-625" y2="-625"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2154" x2="3104" y1="-577" y2="-577"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2640" x2="2640" y1="-471" y2="-471"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3905" x2="4864" y1="-997" y2="-997"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3905" x2="4864" y1="-950" y2="-950"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3905" x2="4863" y1="-902" y2="-902"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3905" x2="4863" y1="-854" y2="-854"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3904" x2="4863" y1="-806" y2="-806"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3905" x2="4863" y1="-757" y2="-757"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3905" x2="4864" y1="-710" y2="-710"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3906" x2="4863" y1="-662" y2="-662"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3905" x2="4863" y1="-614" y2="-614"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3906" x2="4864" y1="-566" y2="-566"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2637" x2="2637" y1="-385" y2="-385"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2633" x2="3105" y1="-385" y2="-385"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2633" x2="3105" y1="-337" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2634" x2="3105" y1="-289" y2="-289"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2634" x2="3105" y1="-241" y2="-241"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2634" x2="3105" y1="-193" y2="-193"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2634" x2="3106" y1="-145" y2="-145"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2634" x2="3105" y1="-97" y2="-97"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3935" x2="3935" y1="-384" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3903" x2="4382" y1="-384" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3903" x2="4382" y1="-336" y2="-336"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3904" x2="4382" y1="-288" y2="-288"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3904" x2="4382" y1="-240" y2="-240"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3904" x2="4382" y1="-192" y2="-192"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3904" x2="4383" y1="-144" y2="-144"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3904" x2="4382" y1="-96" y2="-96"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2153" x2="2633" y1="-385" y2="-385"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2153" x2="2634" y1="-337" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2153" x2="2634" y1="-289" y2="-289"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2153" x2="2633" y1="-241" y2="-241"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2153" x2="2633" y1="-193" y2="-193"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2153" x2="2633" y1="-145" y2="-145"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2153" x2="2633" y1="-97" y2="-97"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2152" x2="2633" y1="-49" y2="-49"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="2153" x2="2633" y1="-1" y2="-1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4383" x2="4863" y1="-384" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4383" x2="4862" y1="-336" y2="-336"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4383" x2="4862" y1="-288" y2="-288"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4383" x2="4863" y1="-240" y2="-240"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4383" x2="4863" y1="-192" y2="-192"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4383" x2="4863" y1="-144" y2="-144"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4383" x2="4863" y1="-96" y2="-96"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4382" x2="4863" y1="-48" y2="-48"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4383" x2="4863" y1="0" y2="0"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4520" x2="4864" y1="-527" y2="-527"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4520" x2="4863" y1="-479" y2="-479"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5472" x2="5472" y1="-438" y2="-438"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4520" x2="4520" y1="-1028" y2="-432"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2747" x2="2747" y1="-1042" y2="-434"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3111" x2="3111" y1="-1008" y2="-1008"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2747" x2="3104" y1="-528" y2="-528"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2746" x2="3103" y1="-481" y2="-481"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2760" x2="4025" y1="47" y2="47"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2760" x2="4025" y1="95" y2="95"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2760" x2="4025" y1="143" y2="143"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2760" x2="4025" y1="191" y2="191"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2760" x2="4025" y1="239" y2="239"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2760" x2="4025" y1="287" y2="287"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2762" x2="4025" y1="335" y2="335"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3064" x2="3064" y1="-1" y2="383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="2955" x2="2955" y1="393" y2="393"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3384" x2="3384" y1="1" y2="382"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3208" x2="3206" y1="465" y2="465"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3720" x2="3720" y1="-2" y2="384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4024" x2="4024" y1="-1" y2="382"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="384" stroke="rgb(0,238,0)" stroke-width="0.722846" width="1264" x="2761" y="-1"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="44" stroke="rgb(0,72,216)" stroke-width="1" width="95" x="3298" y="-348"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="2594" y="-1232"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="44" stroke="rgb(0,72,216)" stroke-width="1" width="95" x="3613" y="-347"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="383" stroke="rgb(0,238,0)" stroke-width="1.44195" width="471" x="2634" y="-432"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="384" stroke="rgb(0,238,0)" stroke-width="0.722846" width="479" x="3904" y="-432"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="479" stroke="rgb(0,238,0)" stroke-width="1.51411" width="481" x="2153" y="-432"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(0,238,0)" stroke-width="0.757053" width="481" x="4383" y="-432"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-218467">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3306.000000 -369.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41485" ObjectName="SW-CX_XJH.CX_XJH_1ATS_aSW"/>
     <cge:Meas_Ref ObjectId="218467"/>
    <cge:TPSR_Ref TObjectID="41485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3353.000000 -370.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41486" ObjectName="SW-CX_XJH.CX_XJH_1ATS_bSW"/>
     <cge:Meas_Ref ObjectId="218468"/>
    <cge:TPSR_Ref TObjectID="41486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218482">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3622.000000 -366.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41487" ObjectName="SW-CX_XJH.CX_XJH_2ATS_aSW"/>
     <cge:Meas_Ref ObjectId="218482"/>
    <cge:TPSR_Ref TObjectID="41487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218454">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41488" ObjectName="SW-CX_XJH.CX_XJH_2ATS_bSW"/>
     <cge:Meas_Ref ObjectId="218454"/>
    <cge:TPSR_Ref TObjectID="41488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3370.000000 -231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3611.000000 -239.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-13100">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -0.891304 3673.000000 -915.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2000" ObjectName="SW-CX_XJH.CX_XJH_3462SW"/>
     <cge:Meas_Ref ObjectId="13100"/>
    <cge:TPSR_Ref TObjectID="2000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-13559">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.142857 -0.891304 0.000000 3444.500000 -1022.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2049" ObjectName="SW-CX_XJH.CX_XJH_3121SW"/>
     <cge:Meas_Ref ObjectId="13559"/>
    <cge:TPSR_Ref TObjectID="2049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-13560">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.142857 -0.891304 -0.000000 3590.500000 -1022.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2050" ObjectName="SW-CX_XJH.CX_XJH_3122SW"/>
     <cge:Meas_Ref ObjectId="13560"/>
    <cge:TPSR_Ref TObjectID="2050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-13088">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -1.039855 3303.000000 -902.166667)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1998" ObjectName="SW-CX_XJH.CX_XJH_3441SW"/>
     <cge:Meas_Ref ObjectId="13088"/>
    <cge:TPSR_Ref TObjectID="1998"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3366.000000 -164.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3366.000000 -164.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -171.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -171.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_XJH.CX_XJH_PDB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="27552"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3282.000000 -740.899083)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3282.000000 -740.899083)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="19749" ObjectName="TF-CX_XJH.CX_XJH_PDB_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_XJH.CX_XJH_PDB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="41044"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -738.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -738.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28808" ObjectName="TF-CX_XJH.CX_XJH_PDB_2T"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_3c615b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3331,-617 3315,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="breaker" ObjectIDND0="g_3c6de10@0" ObjectIDZND0="g_3c33580@0" ObjectIDZND1="41481@x" ObjectIDZND2="41483@x" Pin0InfoVect0LinkObjId="g_3c33580_0" Pin0InfoVect1LinkObjId="SW-218470_0" Pin0InfoVect2LinkObjId="SW-218456_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c6de10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3331,-617 3315,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cdefc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3315,-468 3315,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41481@0" ObjectIDZND0="41485@1" Pin0InfoVect0LinkObjId="SW-218467_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-468 3315,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c76840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3315,-549 3315,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="41483@x" ObjectIDND1="g_3c6de10@0" ObjectIDND2="g_3c33580@0" ObjectIDZND0="41481@1" Pin0InfoVect0LinkObjId="SW-218470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218456_0" Pin1InfoVect1LinkObjId="g_3c6de10_0" Pin1InfoVect2LinkObjId="g_3c33580_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-549 3315,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d004e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3631,-489 3631,-549 3315,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="41483@1" ObjectIDZND0="41481@x" ObjectIDZND1="g_3c6de10@0" ObjectIDZND2="g_3c33580@0" Pin0InfoVect0LinkObjId="SW-218470_0" Pin0InfoVect1LinkObjId="g_3c6de10_0" Pin0InfoVect2LinkObjId="g_3c33580_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3631,-489 3631,-549 3315,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d11860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3631,-460 3631,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41483@0" ObjectIDZND0="41487@1" Pin0InfoVect0LinkObjId="SW-218482_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3631,-460 3631,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c46ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-459 3685,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41484@0" ObjectIDZND0="41488@1" Pin0InfoVect0LinkObjId="SW-218454_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218455_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-459 3685,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3362,-467 3362,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41482@0" ObjectIDZND0="41486@1" Pin0InfoVect0LinkObjId="SW-218468_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218469_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3362,-467 3362,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c81c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3315,-374 3315,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="41485@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218467_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-374 3315,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3f0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3362,-375 3362,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="41486@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3362,-375 3362,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c2b440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3631,-371 3631,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="41487@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3631,-371 3631,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d03050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-373 3685,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="41488@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218454_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-373 3685,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c64b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3315,-656 3315,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="breaker" ObjectIDND0="g_3c33580@1" ObjectIDZND0="g_3c6de10@0" ObjectIDZND1="41481@x" ObjectIDZND2="41483@x" Pin0InfoVect0LinkObjId="g_3c6de10_0" Pin0InfoVect1LinkObjId="SW-218470_0" Pin0InfoVect2LinkObjId="SW-218456_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c33580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-656 3315,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3cb7d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3315,-617 3315,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="g_3c6de10@0" ObjectIDND1="g_3c33580@0" ObjectIDZND0="41481@x" ObjectIDZND1="41483@x" Pin0InfoVect0LinkObjId="SW-218470_0" Pin0InfoVect1LinkObjId="SW-218456_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c6de10_0" Pin1InfoVect1LinkObjId="g_3c33580_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-617 3315,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cbdc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3684,-645 3684,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3ca2660@0" ObjectIDZND0="28808@0" Pin0InfoVect0LinkObjId="g_3c41c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ca2660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3684,-645 3684,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c1d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3379,-284 3342,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3379,-284 3342,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3d199d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3342,-304 3342,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" EndDevType1="busSection" ObjectIDZND0="0@0" ObjectIDZND1="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3342,-304 3342,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c31200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3341,-283 3341,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3341,-283 3341,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3d14c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3379,-283 3379,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3379,-283 3379,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ccff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3379,-284 3379,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="busSection" ObjectIDND0="0@0" ObjectIDND1="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3379,-284 3379,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3809a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3660,-304 3660,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" EndDevType1="switch" ObjectIDZND0="0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3660,-304 3660,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c30b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-127 3659,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-127 3659,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3cbd2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3379,-236 3379,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3379,-236 3379,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cadf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-902 3683,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1999@1" ObjectIDZND0="2000@0" Pin0InfoVect0LinkObjId="SW-13100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13099_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-902 3683,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cb4b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="2281@0" ObjectIDZND0="2281@0" Pin0InfoVect0LinkObjId="g_3cbfd80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cbfd80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbd570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3313,-909 3313,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1998@0" ObjectIDZND0="1997@1" Pin0InfoVect0LinkObjId="SW-13087_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3313,-909 3313,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3313,-945 3313,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1998@1" ObjectIDZND0="2280@0" Pin0InfoVect0LinkObjId="g_3c71020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3313,-945 3313,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc9230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3511,-1012 3554,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2048@0" ObjectIDZND0="2050@1" Pin0InfoVect0LinkObjId="SW-13560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3511,-1012 3554,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c7c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3441,-1012 3484,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2049@0" ObjectIDZND0="2048@1" Pin0InfoVect0LinkObjId="SW-13557_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13559_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3441,-1012 3484,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbfd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3586,-1012 3586,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2050@0" ObjectIDZND0="2281@0" Pin0InfoVect0LinkObjId="g_3cb4b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3586,-1012 3586,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c71020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3408,-1012 3408,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2049@1" ObjectIDZND0="2280@0" Pin0InfoVect0LinkObjId="g_3c1ea40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13559_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3408,-1012 3408,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c41c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-875 3683,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="1999@0" ObjectIDZND0="28808@1" Pin0InfoVect0LinkObjId="g_3cbdc20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13099_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-875 3683,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3313,-868 3313,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="1997@0" ObjectIDZND0="19749@1" Pin0InfoVect0LinkObjId="g_3c2c840_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13087_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3313,-868 3313,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c2c840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3314,-708 3314,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3c33580@0" ObjectIDZND0="19749@0" Pin0InfoVect0LinkObjId="g_375c880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c33580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3314,-708 3314,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35a4930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-280 3660,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-280 3660,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c86b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-242 3620,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-242 3620,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d06da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3621,-514 3362,-514 3362,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="41482@1" Pin0InfoVect0LinkObjId="SW-218469_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3621,-514 3362,-514 3362,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c98820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3701,-574 3685,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_3d14340@0" ObjectIDZND0="g_3ca2660@0" ObjectIDZND1="41484@x" Pin0InfoVect0LinkObjId="g_3ca2660_0" Pin0InfoVect1LinkObjId="SW-218455_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d14340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3701,-574 3685,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cd7250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-574 3685,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_3d14340@0" ObjectIDND1="41484@x" ObjectIDZND0="g_3ca2660@1" Pin0InfoVect0LinkObjId="g_3ca2660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d14340_0" Pin1InfoVect1LinkObjId="SW-218455_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-574 3685,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c39990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3644,-513 3685,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDZND0="g_3d14340@0" ObjectIDZND1="g_3ca2660@0" ObjectIDZND2="41484@x" Pin0InfoVect0LinkObjId="g_3d14340_0" Pin0InfoVect1LinkObjId="g_3ca2660_0" Pin0InfoVect2LinkObjId="SW-218455_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3644,-513 3685,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3d03b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-574 3685,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3d14340@0" ObjectIDND1="g_3ca2660@0" ObjectIDZND0="41484@x" Pin0InfoVect0LinkObjId="SW-218455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d14340_0" Pin1InfoVect1LinkObjId="g_3ca2660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-574 3685,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_377cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3685,-513 3685,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3d14340@0" ObjectIDND1="g_3ca2660@0" ObjectIDZND0="41484@1" Pin0InfoVect0LinkObjId="SW-218455_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d14340_0" Pin1InfoVect1LinkObjId="g_3ca2660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3685,-513 3685,-485 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="2281" cx="3683" cy="-959" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2281" cx="3683" cy="-959" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2280" cx="3313" cy="-958" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2281" cx="3586" cy="-959" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2280" cx="3408" cy="-958" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3659" cy="-127" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3341" cy="-131" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="267" x="2704" y="-1211"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="267" x="2704" y="-1211"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="2667" y="-1228"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="2667" y="-1228"/></g>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_220kV_谢家河变电站.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="267" x="2704" y="-1211"/></g>
   <g href="楚雄地区_220kV_谢家河变电站.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="2667" y="-1228"/></g>
  </g><g id="Rectangle_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="608" stroke="rgb(0,238,0)" stroke-width="3.03937" width="949" x="2155" y="-1040"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="598" stroke="rgb(0,255,0)" stroke-width="2.99007" width="958" x="3905" y="-1029"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3237,-131 3417,-131 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3237,-131 3417,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3584,-127 3766,-127 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3584,-127 3766,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XJH.CX_XJH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3459,-959 3186,-959 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2280" ObjectName="BS-CX_XJH.CX_XJH_3IM"/>
    <cge:TPSR_Ref TObjectID="2280"/></metadata>
   <polyline fill="none" opacity="0" points="3459,-959 3186,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XJH.CX_XJH_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3815,-959 3540,-959 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2281" ObjectName="BS-CX_XJH.CX_XJH_3IIM"/>
    <cge:TPSR_Ref TObjectID="2281"/></metadata>
   <polyline fill="none" opacity="0" points="3815,-959 3540,-959 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 2743.500000 -1155.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12722" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2592.000000 -989.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12722" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12723" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2592.000000 -944.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12723" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218486" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -231.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218486" ObjectName="CX_XJH:CX_XJH_Zyb1_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218487" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -187.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218487" ObjectName="CX_XJH:CX_XJH_Zyb1_Ib"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218488" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -138.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218488" ObjectName="CX_XJH:CX_XJH_Zyb1_Ic"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12719" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2592.000000 -754.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12719" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12720" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2592.000000 -705.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12720" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12721" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2592.000000 -660.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12721" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12724" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2592.000000 -612.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12724" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12731" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -982.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12731" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12732" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -932.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12732" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12725" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -889.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12725" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12726" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -842.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12726" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12727" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -793.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12727" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12728" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -750.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12728" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12729" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -703.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12729" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12730" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -646.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12730" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12733" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -595.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12733" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218489" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 -371.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218489" ObjectName="CX_XJH:CX_XJH_Zyb1_Ua1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218490" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 -322.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218490" ObjectName="CX_XJH:CX_XJH_Zyb1_Ub1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218491" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 -275.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218491" ObjectName="CX_XJH:CX_XJH_Zyb1_Uc1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218493" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 -229.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218493" ObjectName="CX_XJH:CX_XJH_Zyb1_Ua2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218494" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 -180.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218494" ObjectName="CX_XJH:CX_XJH_Zyb1_Ub2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218495" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 -134.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218495" ObjectName="CX_XJH:CX_XJH_Zyb1_Uc2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218500" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 -85.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218500" ObjectName="CX_XJH:CX_XJH_Zyb1_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218501" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 -39.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218501" ObjectName="CX_XJH:CX_XJH_Zyb1_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218502" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2478.000000 12.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218502" ObjectName="CX_XJH:CX_XJH_Zyb1_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218483" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -372.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218483" ObjectName="CX_XJH:CX_XJH_Zyb1_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218484" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -326.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218484" ObjectName="CX_XJH:CX_XJH_Zyb1_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218485" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -281.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218485" ObjectName="CX_XJH:CX_XJH_Zyb1_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218499" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -86.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218499" ObjectName="CX_XJH:CX_XJH_Zyb1_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12716" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2591.000000 -900.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12716" ObjectName="CX_XJH:CX_XJH_344BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12718" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2591.000000 -852.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12718" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12719" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2591.000000 -806.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12719" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218506" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -225.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218506" ObjectName="CX_XJH:CX_XJH_Zyb2_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218507" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -179.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218507" ObjectName="CX_XJH:CX_XJH_Zyb2_Ib"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218508" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -131.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218508" ObjectName="CX_XJH:CX_XJH_Zyb2_Ic"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218503" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -371.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218503" ObjectName="CX_XJH:CX_XJH_Zyb2_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218504" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -326.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218504" ObjectName="CX_XJH:CX_XJH_Zyb2_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218505" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -275.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218505" ObjectName="CX_XJH:CX_XJH_Zyb2_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218515" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.000000 -83.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218515" ObjectName="CX_XJH:CX_XJH_Zyb2_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218509" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -373.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218509" ObjectName="CX_XJH:CX_XJH_Zyb2_Ua1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218510" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -325.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218510" ObjectName="CX_XJH:CX_XJH_Zyb2_Ub1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218511" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -277.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218511" ObjectName="CX_XJH:CX_XJH_Zyb2_Uc1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218512" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -228.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218512" ObjectName="CX_XJH:CX_XJH_Zyb2_Ua2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218513" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -179.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218513" ObjectName="CX_XJH:CX_XJH_Zyb2_Ub2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218514" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -132.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218514" ObjectName="CX_XJH:CX_XJH_Zyb2_Uc2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218516" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -84.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218516" ObjectName="CX_XJH:CX_XJH_Zyb2_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218517" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -34.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218517" ObjectName="CX_XJH:CX_XJH_Zyb2_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218518" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 14.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218518" ObjectName="CX_XJH:CX_XJH_Zyb2_Q"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_366d450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2741.500000 -1200.500000) translate(0,16)">谢家河站用变一次接线图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cb4940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3330.000000 -790.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cb4940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3330.000000 -790.000000) translate(0,33)">S11-400/35GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c9f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -487.000000) translate(0,12)">11QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2de8140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -480.000000) translate(0,12)">22QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb4d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3325.000000 -333.000000) translate(0,12)">1ATS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c826c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -331.000000) translate(0,12)">2ATS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c65ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3580.000000 -480.000000) translate(0,12)">21QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c4cac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3378.000000 -487.000000) translate(0,12)">12QS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cf3450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3285.000000 -398.000000) translate(0,12)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cf93e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3376.000000 -396.000000) translate(0,12)">B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b7c1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3700.000000 -399.000000) translate(0,12)">B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6d540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3603.000000 -393.000000) translate(0,12)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c5cff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3691.000000 -153.000000) translate(0,12)">400VⅡ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bd6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3527.000000 -204.000000) translate(0,12)">JDG4-0.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bd6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3527.000000 -204.000000) translate(0,27)">380/100V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c4c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -204.000000) translate(0,12)">JDG4-0.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c4c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -204.000000) translate(0,27)">380/100V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c8fa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3238.000000 -158.000000) translate(0,12)">4000VⅠ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c34470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3221.000000 -996.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c86e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -1001.000000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b8ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3693.000000 -896.000000) translate(0,12)">346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ccb930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3693.000000 -945.000000) translate(0,12)">3462</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c2b080" transform="matrix(1.000000 -0.000000 -0.000000 1.166667 3325.000000 -892.000000) translate(0,12)">344</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cba950" transform="matrix(1.000000 -0.000000 -0.000000 1.166667 3325.000000 -938.166667) translate(0,12)">3441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cde970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 -1037.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.000000 -1042.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c6b420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3716.000000 -792.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c6b420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3716.000000 -792.000000) translate(0,33)">S11-400/35GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d066a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3480.000000 -998.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_35a4bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2447.000000 -1031.000000) translate(0,15)">遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3c939f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2169.000000 -993.000000) translate(0,15)">35kV1号站用变进线344断路器有功P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3caf5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -987.000000) translate(0,15)"> MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3c66640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2169.000000 -948.000000) translate(0,15)">35kV1号站用变进线344断路器无功Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d09a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -942.000000) translate(0,15)"> MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3c48310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3061.000000 -229.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3c76a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2169.000000 -897.000000) translate(0,15)">35kV1号站用变进线344断路器A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca2e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3061.000000 -185.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3c832d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2169.000000 -803.000000) translate(0,15)">35kV1号站用变进线344断路器A相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3c7ee20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3061.000000 -139.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cb6bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2169.000000 -754.000000) translate(0,15)">35kV1号站用变进线344断路器A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_36e0e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -752.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cca540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2169.000000 -706.000000) translate(0,15)">35kV1号站用变进线344断路器B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3c9a3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -703.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3670600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2169.000000 -658.000000) translate(0,15)">35kV1号站用变进线344断路器C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3809cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -658.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cd34b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2169.000000 -609.000000) translate(0,15)">35kV1号站用变进线344断路器功率因数Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_3d1c750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2861.000000 -1032.000000) translate(0,15)">保护信号量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_377d030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -993.000000) translate(0,15)">35kV1号站用变344保护动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_36e3510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -947.000000) translate(0,15)">35kV1号站用变344电流加速动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_36a2f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -900.000000) translate(0,15)">35kV1号站用变344过流Ⅰ段动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3c75e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -853.000000) translate(0,15)">35kV1号站用变344过流Ⅱ段动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cfafb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -804.000000) translate(0,15)">35kV1号站用变重瓦斯动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cfb670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -757.000000) translate(0,15)">35kV1号站用变344SF6气体压力低报警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cb0150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -706.000000) translate(0,15)">35kV1号站用变344弹簧未储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf5da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -658.000000) translate(0,15)">35kV1号站用变344过负荷告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_369aca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -615.000000) translate(0,15)">35kV1号站用变344控制回路断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_36b8470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -561.000000) translate(0,15)">35kV1号站用变344母线TV断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d1a870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -517.000000) translate(0,15)">35kV1号站用变本体轻瓦斯告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d07030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -469.000000) translate(0,15)">35kV1号站用变本体温度高告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_3d0fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4190.000000 -1022.000000) translate(0,15)">遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d10160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -981.000000) translate(0,15)">35kV2号站用变进线346断路器有功P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3c2a5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -980.000000) translate(0,15)"> MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cb95e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -933.000000) translate(0,15)">35kV2号站用变进线346断路器无功Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d1a290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -930.000000) translate(0,15)"> MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d1a4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -883.000000) translate(0,15)">35kV2号站用变进线346断路器A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_37259d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -887.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ca4740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -838.000000) translate(0,15)">35kV2号站用变进线346断路器B相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3704900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -840.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3704b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -791.000000) translate(0,15)">35kV2号站用变进线346断路器C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_36c20a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -791.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab9a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -742.000000) translate(0,15)">35kV2号站用变进线346断路器A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_37aaa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -748.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_37aac70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -693.000000) translate(0,15)">35kV2号站用变进线346断路器B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_37aaeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -698.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf6220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -650.000000) translate(0,15)">35kV2号站用变进线346断路器C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d19c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -644.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d19e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -596.000000) translate(0,15)">35kV2号站用变进线346断路器功率因数Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_361f550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -1020.000000) translate(0,15)">保护信号量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_397b940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -982.000000) translate(0,15)">35kV2号站用变346保护动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d03f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -934.000000) translate(0,15)">35kV2号站用变346电流加速动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d0d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -887.000000) translate(0,15)">35kV2号站用变346过流Ⅰ段动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d13900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -840.000000) translate(0,15)">35kV2号站用变346过流Ⅱ段动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d0b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -791.000000) translate(0,15)">35kV2号站用变本体重瓦斯动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d0c2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -743.000000) translate(0,15)">35kV2号站用变346SF6气压力报警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3902e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -693.000000) translate(0,15)">35kV2号站用变346弹簧未储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3d05510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -650.000000) translate(0,15)">35kV2号站用变346过负荷告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cfa9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -600.000000) translate(0,15)">35kV2号站用变346控制回路断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf4d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -555.000000) translate(0,15)">35kV2号站用变346母线TV断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf7c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -511.000000) translate(0,15)">35kV2号站用变本体轻瓦斯告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_397c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -461.000000) translate(0,15)">35kV2号站用变本体温度高告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_397e5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2804.000000 255.000000) translate(0,15)">0.4kV站用变K001弹簧未储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3980b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2804.000000 303.000000) translate(0,15)">0.4kV站用变K002弹簧未储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3de3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2804.000000 352.000000) translate(0,15)">0.4kV站用变K003弹簧未储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3de5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 15.000000) translate(0,15)">0.4kV站用电K001机构故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3de7ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2804.000000 64.000000) translate(0,15)">0.4kV站用电K002机构故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3dea010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2804.000000 111.000000) translate(0,15)">0.4kV站用电K002控制回路断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3884930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2804.000000 160.000000) translate(0,15)">0.4kV站用电K003机构故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3886950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2804.000000 207.000000) translate(0,15)">0.4kV站用电K003控制回路断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_3886e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2862.000000 13.000000) translate(0,15)">站用电信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3888bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 64.000000) translate(0,15)">0.4kV站用电K001控制回路断线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_388abf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 111.000000) translate(0,15)">站用电1ATS控制器装置故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_388cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 158.000000) translate(0,15)">站用电1ATS控制器装置总告警</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3abb040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 206.571429) translate(0,15)">站用电1ATS电源1异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3abd060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 255.142857) translate(0,15)">站用电1ATS电源2异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3abf080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 302.714286) translate(0,15)">400VⅠ段母线电压异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ac10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 351.285714) translate(0,15)">站用电1ATS控制器切换动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ac30d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3433.000000 13.857143) translate(0,15)">站用电1ATS控制器闭锁切换</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ac5100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3433.000000 62.428571) translate(0,15)">站用电1ATS控制器自充放电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_40f56c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3433.000000 110.000000) translate(0,15)">站用电1ATS控制器过流保护动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_40f7480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3433.000000 158.571429) translate(0,15)">站用电1ATS控制器零序过流动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_4101110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3425.000000 208.428571) translate(0,15)">站用电1ATS控制器ATS-0位置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_4103140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3425.000000 257.000000) translate(0,15)">站用电2ATS控制器装置故障</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_4105170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3425.000000 305.000000) translate(0,15)">站用电2ATS电源1异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_4107190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3425.000000 351.952381) translate(0,15)">站用电2ATS电源2异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3eab090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3766.000000 19.904762) translate(0,15)">400VⅡ段母线电压异常</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ead020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3765.000000 62.857143) translate(0,15)">站用电2ATS控制器切换动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3eaf060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3765.000000 110.809524) translate(0,15)">站用电2ATS控制器闭锁切换</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3eb10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3765.000000 157.761905) translate(0,15)">站用电2ATS控制器自充放电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3eb30e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3761.000000 206.714286) translate(0,15)">站用电2ATS控制器过流保护动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3eb5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3761.000000 255.666667) translate(0,15)">站用电2ATS控制器零序过流动作</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ebf3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.000000 308.428571) translate(0,15)">站用电2ATS控制器ATS-0位置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ebf8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2169.000000 -849.000000) translate(0,15)">35kV1号站用变进线344断路器A相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec0ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2651.000000 -372.000000) translate(0,15)">400VⅠ段母线 A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec0fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2651.000000 -320.000000) translate(0,15)">400VⅠ段母线 B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec1220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2651.000000 -225.000000) translate(0,15)">400VⅠ段母线 A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec1460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2651.000000 -178.000000) translate(0,15)">400VⅠ段母线 B相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2651.000000 -131.000000) translate(0,15)">400VⅠ段母线 C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec18e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 -370.000000) translate(0,15)">站用电1_1号进线 A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec1e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 -320.000000) translate(0,15)">站用电1_1号进线 B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec20a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2651.000000 -275.000000) translate(0,15)">400VⅠ段母线 C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec22d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 -227.000000) translate(0,15)">站用电1_2号进线 A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec2530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 -175.000000) translate(0,15)">站用电1_2号进线 B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec2780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 -123.000000) translate(0,15)">站用电1_2号进线 C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec29d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2651.000000 -84.000000) translate(0,15)">站用电1母线频率F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec2f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 -82.000000) translate(0,15)">站用电1功率因数Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec3180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 -270.000000) translate(0,15)">站用电1_1号进线 C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec33e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 -36.000000) translate(0,15)">站用电1有功功率P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec3610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2175.000000 18.000000) translate(0,15)">站用电1无功功率Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec3850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -367.000000) translate(0,15)">400VⅡ段母线 A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec3a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -322.000000) translate(0,15)">400VⅡ段母线 B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec3cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -271.000000) translate(0,15)">400VⅡ段母线 C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec3f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -175.000000) translate(0,15)">400VⅡ段母线 B相电流Ib</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec4150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -127.000000) translate(0,15)">400VⅡ段母线 C相电流Ic</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec4390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -221.000000) translate(0,15)">400VⅡ段母线 A相电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec45d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -369.000000) translate(0,15)">站用电2_1号进线 A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec4830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -321.000000) translate(0,15)">站用电2_1号进线 B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec4a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -225.000000) translate(0,15)">站用电2_2号进线 A相电压Ua</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec4cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -177.000000) translate(0,15)">站用电2_2号进线 B相电压Ub</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec4f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -129.000000) translate(0,15)">站用电2_2号进线 C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec5170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -79.000000) translate(0,15)">站用电2母线频率F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec53a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -78.000000) translate(0,15)">站用电2功率因数Cos</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec55e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -272.000000) translate(0,15)">站用电2_1号进线 C相电压Uc</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec5840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -34.000000) translate(0,15)">站用电2有功功率P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec5a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 16.000000) translate(0,15)">站用电2无功功率Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec60d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -371.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec6740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -322.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec6db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -275.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec7420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -231.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec7a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -180.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3ec8100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -128.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e36bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2565.000000 -37.000000) translate(0,15)"> MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e37230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2565.000000 14.000000) translate(0,15)"> MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e378a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3055.000000 -366.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e37f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3055.000000 -326.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e38580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3055.000000 -281.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e39800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -896.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e39a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -850.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e39c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2688.000000 -802.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3b350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4322.000000 -221.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3b9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4322.000000 -175.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3c030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4322.000000 -127.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3c6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4317.000000 -367.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3cd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4317.000000 -322.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3d380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4317.000000 -271.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.000000 -365.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3e490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.000000 -321.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3eb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.000000 -274.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3f170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.000000 -228.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3f7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.000000 -180.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e3fe50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.000000 -131.000000) translate(0,15)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e408f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4795.000000 -35.000000) translate(0,15)"> MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_3e40f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4795.000000 11.000000) translate(0,15)"> MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_3e41280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2790.000000 -417.000000) translate(0,15)">400VⅠ段母线遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_3e420c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 -416.000000) translate(0,15)">400VⅡ段母线遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_3e430c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2372.000000 -414.000000) translate(0,15)">遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_3e44250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4580.000000 -414.000000) translate(0,15)">遥测量</text>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3500,-446 " stroke="rgb(0,72,216)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3644,-514 3644,-515 3644,-517 3644,-518 3643,-519 3642,-520 3641,-521 3640,-521 3639,-522 3638,-523 3637,-523 3635,-524 3634,-524 3632,-524 3631,-524 3629,-523 3628,-523 3626,-523 3625,-522 3624,-521 3623,-520 3622,-519 3622,-518 3621,-517 3621,-516 3621,-515 3621,-514 3621,-513 " stroke="rgb(0,72,216)" stroke-width="1"/>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3c33580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3309.000000 -651.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ca2660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3680.000000 -588.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c6de10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3327.000000 -609.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d14340">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3697.000000 -566.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-218470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3306.000000 -458.634146)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41481" ObjectName="SW-CX_XJH.CX_XJH_1ATS_11qsBK"/>
     <cge:Meas_Ref ObjectId="218470"/>
    <cge:TPSR_Ref TObjectID="41481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218455">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -450.634146)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41484" ObjectName="SW-CX_XJH.CX_XJH2ATS_22qsBK"/>
     <cge:Meas_Ref ObjectId="218455"/>
    <cge:TPSR_Ref TObjectID="41484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218456">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3622.000000 -451.634146)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41483" ObjectName="SW-CX_XJH.CX_XJH_2ATS_21qsBK"/>
     <cge:Meas_Ref ObjectId="218456"/>
    <cge:TPSR_Ref TObjectID="41483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218469">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3353.000000 -458.634146)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41482" ObjectName="SW-CX_XJH.CX_XJH_1ATS_12qsBK"/>
     <cge:Meas_Ref ObjectId="218469"/>
    <cge:TPSR_Ref TObjectID="41482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-13087">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.166667 1.000000 0.000000 3303.500000 -910.583333)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1997" ObjectName="SW-CX_XJH.CX_XJH_344BK"/>
     <cge:Meas_Ref ObjectId="13087"/>
    <cge:TPSR_Ref TObjectID="1997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-13099">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3673.500000 -911.500000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1999" ObjectName="SW-CX_XJH.CX_XJH_346BK"/>
     <cge:Meas_Ref ObjectId="13099"/>
    <cge:TPSR_Ref TObjectID="1999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-13557">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3475.000000 -1002.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2048" ObjectName="SW-CX_XJH.CX_XJH_312BK"/>
     <cge:Meas_Ref ObjectId="13557"/>
    <cge:TPSR_Ref TObjectID="2048"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173424" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -969.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173424"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173427" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -923.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173427"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173425" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -876.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173425"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173426" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -829.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173426"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173421" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -780.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173421"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173420" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -733.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173420"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173418" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -682.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173418"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173429" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -635.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173429"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173419" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -591.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173419"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173428" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -537.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173428"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173422" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -493.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173422"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173423" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -445.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173423"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173436" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -958.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173436"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173439" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -910.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173439"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173437" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -863.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173437"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173438" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -816.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173438"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173433" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -767.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173433"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173432" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -719.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173432"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173430" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -669.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173430"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173441" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -621.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173441"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173431" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -573.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173431"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173440" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -531.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173440"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173434" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -488.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173434"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173435" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -437.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173435"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173153" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 279.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173153"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173156" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 325.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173156"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173159" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 374.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173159"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173155" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3066.000000 38.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173155"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173158" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 85.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173158"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173157" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 134.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173157"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173161" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 183.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173161"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173160" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 231.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173160"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-173154" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3066.000000 86.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="173154"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218480" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3066.000000 134.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218480"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218479" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3066.000000 182.333333)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218479"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218478" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3066.000000 229.809524)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218478"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218477" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3066.000000 276.285714)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218477"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218476" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3066.000000 324.761905)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218476"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218475" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3066.000000 373.238095)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218475"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218474" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 36.714286)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218474"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218473" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 87.190476)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218473"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218472" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 135.666667)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218472"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218471" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 184.142857)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218471"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218466" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 230.523810)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218466"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218465" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 279.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218465"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218464" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 328.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218464"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218463" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 372.904762)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218463"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218462" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 40.809524)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218462"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218461" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 85.714286)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218461"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218460" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 134.619048)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218460"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218459" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 183.523810)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218459"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218458" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 230.428571)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218458"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218457" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 279.333333)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218457"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-218481" type="0">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 328.857143)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="218481"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>