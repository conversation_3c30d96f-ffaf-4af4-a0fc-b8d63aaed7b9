<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-177" aopId="3949070" id="thSvg" product="E8000V2" version="1.0" viewBox="3051 -1231 2067 1319">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="67" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <polyline arcFlag="1" points="44,21 45,21 46,21 46,21 47,22 47,22 48,23 48,23 49,24 49,24 49,25 49,26 50,27 50,28 50,28 49,29 49,30 49,31 49,31 48,32 48,32 47,33 47,33 46,34 46,34 45,34 44,34 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,34 45,34 46,34 46,34 47,35 47,35 48,36 48,36 49,37 49,37 49,38 49,39 50,40 50,41 50,41 49,42 49,43 49,44 49,44 48,45 48,45 47,46 47,46 46,47 46,47 45,47 44,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,47 45,47 46,47 46,47 47,48 47,48 48,49 48,49 49,50 49,50 49,51 49,52 50,53 50,54 50,54 49,55 49,56 49,57 49,57 48,58 48,58 47,59 47,59 46,60 46,60 45,60 44,60 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="13" y2="13"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="32"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="21" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="21" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="29" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="88" y2="88"/>
    <polyline points="25,101 27,101 29,100 30,100 32,99 33,98 35,97 36,95 37,93 37,92 38,90 38,88 38,86 37,84 37,83 36,81 35,80 33,78 32,77 30,76 29,76 27,75 25,75 23,75 21,76 20,76 18,77 17,78 15,80 14,81 13,83 13,84 12,86 12,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="67" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="68" y2="68"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape40_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape24_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.13333"/>
    <polyline points="58,100 64,100 " stroke-width="1.13333"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.13333"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape80">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape127">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="71" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="86" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="98" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="11" y1="95" y2="86"/>
    <rect height="13" stroke-width="1" width="5" x="4" y="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="78" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="4" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="5" y1="111" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="3" y1="107" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="107" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="25" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="45" y1="25" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="39" x2="39" y1="93" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="42" x2="39" y1="90" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="39" x2="36" y1="93" y2="90"/>
    <circle cx="38" cy="92" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="41" y1="79" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="38" y1="79" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="43" y1="79" y2="79"/>
    <ellipse cx="25" cy="79" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="24" cy="92" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="39" cy="79" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="59" x2="63" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="57" x2="65" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="61" x2="61" y1="73" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="67" x2="55" y1="73" y2="73"/>
    <rect height="27" stroke-width="0.416667" width="14" x="54" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="61" x2="61" y1="25" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="61" y1="25" y2="25"/>
    <rect height="27" stroke-width="0.416667" width="14" x="20" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="25" x2="25" y1="78" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="28" x2="25" y1="75" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="25" x2="22" y1="78" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="24" x2="24" y1="93" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="27" x2="24" y1="90" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="24" x2="21" y1="93" y2="90"/>
   </symbol>
   <symbol id="voltageTransformer:shape128">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="71" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="25" x2="22" y1="92" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="28" x2="25" y1="89" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="25" x2="25" y1="92" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="39" x2="36" y1="78" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="42" x2="39" y1="75" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="39" x2="39" y1="78" y2="81"/>
    <rect height="27" stroke-width="0.416667" width="14" x="34" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="41" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="59"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="73" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="79" y2="79"/>
    <ellipse cx="25" cy="79" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="25" cy="91" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="39" cy="79" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="24" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="27" y1="80" y2="76"/>
    <circle cx="39" cy="91" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="40" x2="37" y1="92" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="43" x2="40" y1="89" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="40" x2="40" y1="92" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="25" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="58" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="58" y1="107" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="55" y1="107" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="57" y1="111" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="56" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="58" y1="78" y2="84"/>
    <rect height="13" stroke-width="1" width="5" x="56" y="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="63" y1="95" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="98" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="63" x2="63" y1="86" y2="83"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e87d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e88e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e89860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e8a7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e8ba70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e8c680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e8d230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e8dc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e8f270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e8f270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e90be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e90be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e92a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e92a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1e93a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e95700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e962f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e97110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e97a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e99110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e99910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e9a000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e9aa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e9bc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e9c580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e9d320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e9daf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e9ef20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e9fa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ea0ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ea16f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1eafec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ea2fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ea45d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ea5b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1329" width="2077" x="3046" y="-1236"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4974" x2="4974" y1="-102" y2="15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5027" x2="4974" y1="15" y2="15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5026" x2="5002" y1="-94" y2="-94"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5002" x2="5002" y1="-102" y2="-94"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5109" x2="5118" y1="-393" y2="-393"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3070" y="-1135"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3052" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3452" y="-1038"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-123609">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -708.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22787" ObjectName="SW-WD_MJ.WD_MJ_301BK"/>
     <cge:Meas_Ref ObjectId="123609"/>
    <cge:TPSR_Ref TObjectID="22787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123611">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -446.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22789" ObjectName="SW-WD_MJ.WD_MJ_001BK"/>
     <cge:Meas_Ref ObjectId="123611"/>
    <cge:TPSR_Ref TObjectID="22789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123198">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 -274.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22758" ObjectName="SW-WD_MJ.WD_MJ_072BK"/>
     <cge:Meas_Ref ObjectId="123198"/>
    <cge:TPSR_Ref TObjectID="22758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123504">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3554.000000 -274.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22785" ObjectName="SW-WD_MJ.WD_MJ_071BK"/>
     <cge:Meas_Ref ObjectId="123504"/>
    <cge:TPSR_Ref TObjectID="22785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123241">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -278.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22762" ObjectName="SW-WD_MJ.WD_MJ_073BK"/>
     <cge:Meas_Ref ObjectId="123241"/>
    <cge:TPSR_Ref TObjectID="22762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 -270.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22765" ObjectName="SW-WD_MJ.WD_MJ_074BK"/>
     <cge:Meas_Ref ObjectId="123285"/>
    <cge:TPSR_Ref TObjectID="22765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4131.000000 -270.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22779" ObjectName="SW-WD_MJ.WD_MJ_075BK"/>
     <cge:Meas_Ref ObjectId="123416"/>
    <cge:TPSR_Ref TObjectID="22779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123329">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 -270.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22773" ObjectName="SW-WD_MJ.WD_MJ_077BK"/>
     <cge:Meas_Ref ObjectId="123329"/>
    <cge:TPSR_Ref TObjectID="22773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123684">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 -270.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22806" ObjectName="SW-WD_MJ.WD_MJ_078BK"/>
     <cge:Meas_Ref ObjectId="123684"/>
    <cge:TPSR_Ref TObjectID="22806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4818.000000 -270.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22770" ObjectName="SW-WD_MJ.WD_MJ_079BK"/>
     <cge:Meas_Ref ObjectId="123303"/>
    <cge:TPSR_Ref TObjectID="22770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123460">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 -900.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22782" ObjectName="SW-WD_MJ.WD_MJ_371BK"/>
     <cge:Meas_Ref ObjectId="123460"/>
    <cge:TPSR_Ref TObjectID="22782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221273">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4460.000000 -704.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34617" ObjectName="SW-WD_MJ.WD_MJ_302BK"/>
     <cge:Meas_Ref ObjectId="221273"/>
    <cge:TPSR_Ref TObjectID="34617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4460.000000 -446.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34620" ObjectName="SW-WD_MJ.WD_MJ_002BK"/>
     <cge:Meas_Ref ObjectId="221301"/>
    <cge:TPSR_Ref TObjectID="34620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -439.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34614" ObjectName="SW-WD_MJ.WD_MJ_012BK"/>
     <cge:Meas_Ref ObjectId="221307"/>
    <cge:TPSR_Ref TObjectID="34614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123372">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -266.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22776" ObjectName="SW-WD_MJ.WD_MJ_076BK"/>
     <cge:Meas_Ref ObjectId="123372"/>
    <cge:TPSR_Ref TObjectID="22776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123535">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 -273.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34610" ObjectName="SW-WD_MJ.WD_MJ_081BK"/>
     <cge:Meas_Ref ObjectId="123535"/>
    <cge:TPSR_Ref TObjectID="34610"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2194f40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -959.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2114ac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 -964.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a3a10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 -910.000000)" xlink:href="#voltageTransformer:shape127"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d8f430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 -460.000000)" xlink:href="#voltageTransformer:shape128"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c18300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3682.000000 -460.000000)" xlink:href="#voltageTransformer:shape128"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="WD_MJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_damaogaoTmj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4242,-1167 4242,-1131 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38123" ObjectName="AC-35kV.LN_damaogaoTmj"/>
    <cge:TPSR_Ref TObjectID="38123_SS-177"/></metadata>
   <polyline fill="none" opacity="0" points="4242,-1167 4242,-1131 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-WD_MJ.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 10.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33177" ObjectName="EC-WD_MJ.072Ld"/>
    <cge:TPSR_Ref TObjectID="33177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_MJ.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3850.000000 15.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33178" ObjectName="EC-WD_MJ.073Ld"/>
    <cge:TPSR_Ref TObjectID="33178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_MJ.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 18.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33179" ObjectName="EC-WD_MJ.074Ld"/>
    <cge:TPSR_Ref TObjectID="33179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_MJ.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 19.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33180" ObjectName="EC-WD_MJ.075Ld"/>
    <cge:TPSR_Ref TObjectID="33180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_MJ.076Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4388.000000 22.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33181" ObjectName="EC-WD_MJ.076Ld"/>
    <cge:TPSR_Ref TObjectID="33181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_MJ.077Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 20.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33182" ObjectName="EC-WD_MJ.077Ld"/>
    <cge:TPSR_Ref TObjectID="33182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_MJ.078Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4678.000000 20.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33183" ObjectName="EC-WD_MJ.078Ld"/>
    <cge:TPSR_Ref TObjectID="33183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_MJ.079Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 20.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33184" ObjectName="EC-WD_MJ.079Ld"/>
    <cge:TPSR_Ref TObjectID="33184"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2199af0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -49.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_214c090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4899.000000 -49.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22add10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -936.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2105fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -946.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_210be60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.000000 -888.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2196690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 -1103.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2196ea0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -1102.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c052a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 -756.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20f99e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 -890.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22c1030" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 -752.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_22b22b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-829 4063,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22812@0" ObjectIDZND0="22788@1" Pin0InfoVect0LinkObjId="SW-123610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-829 4063,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cd7090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-454 4062,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22789@0" ObjectIDZND0="22791@1" Pin0InfoVect0LinkObjId="SW-123613_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123611_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4062,-454 4062,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cddd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-405 4062,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22791@0" ObjectIDZND0="22813@0" Pin0InfoVect0LinkObjId="g_1c25880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4062,-405 4062,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ccad70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-716 4063,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22787@0" ObjectIDZND0="22809@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-716 4063,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23edc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3624,-196 3624,-211 3563,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="g_2134280@0" ObjectIDZND0="34804@x" ObjectIDZND1="22802@x" Pin0InfoVect0LinkObjId="CB-WD_MJ.WD_MJ_cb1_0" Pin0InfoVect1LinkObjId="SW-123674_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2134280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3624,-196 3624,-211 3563,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206c910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3563,-125 3563,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34804@0" ObjectIDZND0="g_2134280@0" ObjectIDZND1="22802@x" Pin0InfoVect0LinkObjId="g_2134280_0" Pin0InfoVect1LinkObjId="SW-123674_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-WD_MJ.WD_MJ_cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3563,-125 3563,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fab940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-500 4062,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22790@0" ObjectIDZND0="22789@1" Pin0InfoVect0LinkObjId="SW-123611_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123612_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4062,-500 4062,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22780c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,-193 4059,-208 3996,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ff9750@0" ObjectIDZND0="22767@x" ObjectIDZND1="22798@x" Pin0InfoVect0LinkObjId="SW-123287_0" Pin0InfoVect1LinkObjId="SW-123670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ff9750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4059,-193 4059,-208 3996,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fe1db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-208 3996,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1ff9750@0" ObjectIDND1="22798@x" ObjectIDZND0="22767@1" Pin0InfoVect0LinkObjId="SW-123287_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ff9750_0" Pin1InfoVect1LinkObjId="SW-123670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-208 3996,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2280f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4058,-44 4058,-59 3996,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_201e530@0" ObjectIDZND0="33179@x" ObjectIDZND1="22767@x" Pin0InfoVect0LinkObjId="EC-WD_MJ.074Ld_0" Pin0InfoVect1LinkObjId="SW-123287_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_201e530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4058,-44 4058,-59 3996,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23cdbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-3 3996,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33179@0" ObjectIDZND0="g_201e530@0" ObjectIDZND1="22767@x" Pin0InfoVect0LinkObjId="g_201e530_0" Pin0InfoVect1LinkObjId="SW-123287_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_MJ.074Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-3 3996,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af5c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-59 3996,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33179@x" ObjectIDND1="g_201e530@0" ObjectIDZND0="22767@0" Pin0InfoVect0LinkObjId="SW-123287_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_MJ.074Ld_0" Pin1InfoVect1LinkObjId="g_201e530_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-59 3996,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ba440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4202,-43 4202,-58 4140,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1c48ee0@0" ObjectIDZND0="33180@x" ObjectIDZND1="22781@x" Pin0InfoVect0LinkObjId="EC-WD_MJ.075Ld_0" Pin0InfoVect1LinkObjId="SW-123418_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c48ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4202,-43 4202,-58 4140,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b13580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-2 4140,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33180@0" ObjectIDZND0="22781@x" ObjectIDZND1="g_1c48ee0@0" Pin0InfoVect0LinkObjId="SW-123418_0" Pin0InfoVect1LinkObjId="g_1c48ee0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_MJ.075Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4140,-2 4140,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b137e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-58 4140,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33180@x" ObjectIDND1="g_1c48ee0@0" ObjectIDZND0="22781@0" Pin0InfoVect0LinkObjId="SW-123418_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_MJ.075Ld_0" Pin1InfoVect1LinkObjId="g_1c48ee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4140,-58 4140,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22b1eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4455,-43 4455,-58 4394,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_22b31a0@0" ObjectIDZND0="22778@x" ObjectIDZND1="33181@x" Pin0InfoVect0LinkObjId="SW-123374_0" Pin0InfoVect1LinkObjId="EC-WD_MJ.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b31a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4455,-43 4455,-58 4394,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c61960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-206 4537,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1c61b70@0" ObjectIDND1="22799@x" ObjectIDZND0="22775@1" Pin0InfoVect0LinkObjId="SW-123331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c61b70_0" Pin1InfoVect1LinkObjId="SW-123671_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-206 4537,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ceac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-45 4599,-60 4538,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1cea160@0" ObjectIDZND0="33182@x" ObjectIDZND1="22775@x" Pin0InfoVect0LinkObjId="EC-WD_MJ.077Ld_0" Pin0InfoVect1LinkObjId="SW-123331_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cea160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4599,-45 4599,-60 4538,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ceaeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-1 4537,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33182@0" ObjectIDZND0="g_1cea160@0" ObjectIDZND1="22775@x" Pin0InfoVect0LinkObjId="g_1cea160_0" Pin0InfoVect1LinkObjId="SW-123331_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_MJ.077Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-1 4537,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ceb110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-60 4537,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1cea160@0" ObjectIDND1="33182@x" ObjectIDZND0="22775@0" Pin0InfoVect0LinkObjId="SW-123331_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cea160_0" Pin1InfoVect1LinkObjId="EC-WD_MJ.077Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-60 4537,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fabbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4746,-191 4746,-206 4683,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1c59dd0@0" ObjectIDZND0="22811@x" ObjectIDZND1="22805@x" Pin0InfoVect0LinkObjId="SW-123692_0" Pin0InfoVect1LinkObjId="SW-123683_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c59dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4746,-191 4746,-206 4683,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c59bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-206 4683,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1c59dd0@0" ObjectIDND1="22805@x" ObjectIDZND0="22811@1" Pin0InfoVect0LinkObjId="SW-123692_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c59dd0_0" Pin1InfoVect1LinkObjId="SW-123683_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-206 4683,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d2fe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-55 4683,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1b154b0@0" ObjectIDND1="33183@x" ObjectIDND2="22761@x" ObjectIDZND0="22811@0" Pin0InfoVect0LinkObjId="SW-123692_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b154b0_0" Pin1InfoVect1LinkObjId="EC-WD_MJ.078Ld_0" Pin1InfoVect2LinkObjId="SW-123210_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-55 4683,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d30080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-24 4714,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="33183@x" ObjectIDND1="22811@x" ObjectIDND2="22761@x" ObjectIDZND0="g_1b154b0@0" Pin0InfoVect0LinkObjId="g_1b154b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-WD_MJ.078Ld_0" Pin1InfoVect1LinkObjId="SW-123692_0" Pin1InfoVect2LinkObjId="SW-123210_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-24 4714,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d30b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-1 4683,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="33183@0" ObjectIDZND0="g_1b154b0@0" ObjectIDZND1="22811@x" ObjectIDZND2="22761@x" Pin0InfoVect0LinkObjId="g_1b154b0_0" Pin0InfoVect1LinkObjId="SW-123692_0" Pin0InfoVect2LinkObjId="SW-123210_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_MJ.078Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-1 4683,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d30db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-24 4683,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1b154b0@0" ObjectIDND1="33183@x" ObjectIDZND0="22811@x" ObjectIDZND1="22761@x" Pin0InfoVect0LinkObjId="SW-123692_0" Pin0InfoVect1LinkObjId="SW-123210_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b154b0_0" Pin1InfoVect1LinkObjId="EC-WD_MJ.078Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-24 4683,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2198900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-55 4712,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="22811@x" ObjectIDND1="g_1b154b0@0" ObjectIDND2="33183@x" ObjectIDZND0="22761@0" Pin0InfoVect0LinkObjId="SW-123210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-123692_0" Pin1InfoVect1LinkObjId="g_1b154b0_0" Pin1InfoVect2LinkObjId="EC-WD_MJ.078Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-55 4712,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2198b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-55 4759,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22761@1" ObjectIDZND0="g_2199af0@0" Pin0InfoVect0LinkObjId="g_2199af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-55 4759,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2199630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-58 4393,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_22b31a0@0" ObjectIDND1="33181@x" ObjectIDZND0="22778@0" Pin0InfoVect0LinkObjId="SW-123374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22b31a0_0" Pin1InfoVect1LinkObjId="EC-WD_MJ.076Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-58 4393,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2199890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,1 4393,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33181@0" ObjectIDZND0="g_22b31a0@0" ObjectIDZND1="22778@x" Pin0InfoVect0LinkObjId="g_22b31a0_0" Pin0InfoVect1LinkObjId="SW-123374_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_MJ.076Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4393,1 4393,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b15ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-191 4890,-206 4827,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2146260@0" ObjectIDZND0="22771@x" ObjectIDZND1="22769@x" Pin0InfoVect0LinkObjId="SW-123309_0" Pin0InfoVect1LinkObjId="SW-123302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2146260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-191 4890,-206 4827,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2146050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-206 4827,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2146260@0" ObjectIDND1="22769@x" ObjectIDZND0="22771@1" Pin0InfoVect0LinkObjId="SW-123309_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2146260_0" Pin1InfoVect1LinkObjId="SW-123302_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-206 4827,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2279580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-55 4827,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_203f200@0" ObjectIDND1="33184@x" ObjectIDND2="22772@x" ObjectIDZND0="22771@0" Pin0InfoVect0LinkObjId="SW-123309_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_203f200_0" Pin1InfoVect1LinkObjId="EC-WD_MJ.079Ld_0" Pin1InfoVect2LinkObjId="SW-123310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-55 4827,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22797e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-24 4858,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="33184@x" ObjectIDND1="22771@x" ObjectIDND2="22772@x" ObjectIDZND0="g_203f200@0" Pin0InfoVect0LinkObjId="g_203f200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-WD_MJ.079Ld_0" Pin1InfoVect1LinkObjId="SW-123309_0" Pin1InfoVect2LinkObjId="SW-123310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-24 4858,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2279a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-1 4827,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="33184@0" ObjectIDZND0="g_203f200@0" ObjectIDZND1="22771@x" ObjectIDZND2="22772@x" Pin0InfoVect0LinkObjId="g_203f200_0" Pin0InfoVect1LinkObjId="SW-123309_0" Pin0InfoVect2LinkObjId="SW-123310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_MJ.079Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-1 4827,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2279ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-24 4827,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_203f200@0" ObjectIDND1="33184@x" ObjectIDZND0="22771@x" ObjectIDZND1="22772@x" Pin0InfoVect0LinkObjId="SW-123309_0" Pin0InfoVect1LinkObjId="SW-123310_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_203f200_0" Pin1InfoVect1LinkObjId="EC-WD_MJ.079Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-24 4827,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_214bbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-55 4856,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="22771@x" ObjectIDND1="g_203f200@0" ObjectIDND2="33184@x" ObjectIDZND0="22772@0" Pin0InfoVect0LinkObjId="SW-123310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-123309_0" Pin1InfoVect1LinkObjId="g_203f200_0" Pin1InfoVect2LinkObjId="EC-WD_MJ.079Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-55 4856,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_214be30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-55 4903,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22772@1" ObjectIDZND0="g_214c090@0" Pin0InfoVect0LinkObjId="g_214c090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-55 4903,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22bcbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-829 4242,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22812@0" ObjectIDZND0="22784@0" Pin0InfoVect0LinkObjId="SW-123462_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-829 4242,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ad850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-1021 4187,-1021 4187,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22783@x" ObjectIDND1="22794@x" ObjectIDND2="22792@x" ObjectIDZND0="22793@1" Pin0InfoVect0LinkObjId="SW-123624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-123461_0" Pin1InfoVect1LinkObjId="SW-123625_0" Pin1InfoVect2LinkObjId="SW-123623_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-1021 4187,-1021 4187,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22adab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4187,-970 4187,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22793@0" ObjectIDZND0="g_22add10@0" Pin0InfoVect0LinkObjId="g_22add10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4187,-970 4187,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226da00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-1040 4186,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22792@x" ObjectIDND1="22783@x" ObjectIDND2="22793@x" ObjectIDZND0="22794@1" Pin0InfoVect0LinkObjId="SW-123625_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-123623_0" Pin1InfoVect1LinkObjId="SW-123461_0" Pin1InfoVect2LinkObjId="SW-123624_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-1040 4186,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2114860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-1040 4263,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22794@x" ObjectIDND1="22783@x" ObjectIDND2="22793@x" ObjectIDZND0="22792@0" Pin0InfoVect0LinkObjId="SW-123623_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-123625_0" Pin1InfoVect1LinkObjId="SW-123461_0" Pin1InfoVect2LinkObjId="SW-123624_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-1040 4263,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2271270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-1040 4242,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="22794@x" ObjectIDND1="22792@x" ObjectIDND2="22783@x" ObjectIDZND0="38123@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-123625_0" Pin1InfoVect1LinkObjId="SW-123623_0" Pin1InfoVect2LinkObjId="SW-123461_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-1040 4242,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a5e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-1004 4242,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="22783@1" ObjectIDZND0="22794@x" ObjectIDZND1="22792@x" ObjectIDZND2="38123@1" Pin0InfoVect0LinkObjId="SW-123625_0" Pin0InfoVect1LinkObjId="SW-123623_0" Pin0InfoVect2LinkObjId="g_2271270_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123461_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-1004 4242,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a6030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-1021 4242,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="22783@x" ObjectIDND1="22793@x" ObjectIDZND0="22794@x" ObjectIDZND1="22792@x" ObjectIDZND2="38123@1" Pin0InfoVect0LinkObjId="SW-123625_0" Pin0InfoVect1LinkObjId="SW-123623_0" Pin0InfoVect2LinkObjId="g_2271270_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123461_0" Pin1InfoVect1LinkObjId="SW-123624_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-1021 4242,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a6a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-935 4242,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22782@1" ObjectIDZND0="22783@x" ObjectIDZND1="34396@x" Pin0InfoVect0LinkObjId="SW-123461_0" Pin0InfoVect1LinkObjId="SW-221266_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-935 4242,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a6c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-952 4242,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22782@x" ObjectIDND1="34396@x" ObjectIDZND0="22783@0" Pin0InfoVect0LinkObjId="SW-123461_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123460_0" Pin1InfoVect1LinkObjId="SW-221266_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-952 4242,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a7780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-880 4242,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22784@1" ObjectIDZND0="22782@x" ObjectIDZND1="34397@x" Pin0InfoVect0LinkObjId="SW-123460_0" Pin0InfoVect1LinkObjId="SW-221438_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123462_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-880 4242,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a79e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-894 4242,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22784@x" ObjectIDND1="34397@x" ObjectIDZND0="22782@0" Pin0InfoVect0LinkObjId="SW-123460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123462_0" Pin1InfoVect1LinkObjId="SW-221438_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-894 4242,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2102c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-952 4274,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22782@x" ObjectIDND1="22783@x" ObjectIDZND0="34396@0" Pin0InfoVect0LinkObjId="SW-221266_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123460_0" Pin1InfoVect1LinkObjId="SW-123461_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-952 4274,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2102ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-952 4331,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34396@1" ObjectIDZND0="g_2105fc0@0" Pin0InfoVect0LinkObjId="g_2105fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-952 4331,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2105b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-894 4275,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22784@x" ObjectIDND1="22782@x" ObjectIDZND0="34397@0" Pin0InfoVect0LinkObjId="SW-221438_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123462_0" Pin1InfoVect1LinkObjId="SW-123460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-894 4275,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2105d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4311,-894 4330,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34397@1" ObjectIDZND0="g_210be60@0" Pin0InfoVect0LinkObjId="g_210be60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221438_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4311,-894 4330,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_210d7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4150,-1040 4125,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="22794@0" ObjectIDZND0="34398@x" ObjectIDZND1="g_2194360@0" ObjectIDZND2="g_2194f40@0" Pin0InfoVect0LinkObjId="SW-221265_0" Pin0InfoVect1LinkObjId="g_2194360_0" Pin0InfoVect2LinkObjId="g_2194f40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123625_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4150,-1040 4125,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226b1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4299,-1040 4327,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="22792@1" ObjectIDZND0="34399@x" ObjectIDZND1="g_203a070@0" ObjectIDZND2="g_2114ac0@0" Pin0InfoVect0LinkObjId="SW-221264_0" Pin0InfoVect1LinkObjId="g_203a070_0" Pin0InfoVect2LinkObjId="g_2114ac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123623_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4299,-1040 4327,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a6d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4125,-1040 4125,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="22794@x" ObjectIDND1="g_2194360@0" ObjectIDND2="g_2194f40@0" ObjectIDZND0="34398@0" Pin0InfoVect0LinkObjId="SW-221265_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-123625_0" Pin1InfoVect1LinkObjId="g_2194360_0" Pin1InfoVect2LinkObjId="g_2194f40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4125,-1040 4125,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a6f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4125,-1091 4125,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34398@1" ObjectIDZND0="g_2196690@0" Pin0InfoVect0LinkObjId="g_2196690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221265_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4125,-1091 4125,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2195aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4327,-1040 4327,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="22792@x" ObjectIDND1="g_203a070@0" ObjectIDND2="g_2114ac0@0" ObjectIDZND0="34399@0" Pin0InfoVect0LinkObjId="SW-221264_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-123623_0" Pin1InfoVect1LinkObjId="g_203a070_0" Pin1InfoVect2LinkObjId="g_2114ac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4327,-1040 4327,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2195d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4327,-1091 4327,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34399@1" ObjectIDZND0="g_2196ea0@0" Pin0InfoVect0LinkObjId="g_2196ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4327,-1091 4327,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b9f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-775 4063,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22788@0" ObjectIDZND0="22787@x" ObjectIDZND1="34400@x" Pin0InfoVect0LinkObjId="SW-123609_0" Pin0InfoVect1LinkObjId="SW-221272_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-775 4063,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ba170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-762 4063,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22788@x" ObjectIDND1="34400@x" ObjectIDZND0="22787@1" Pin0InfoVect0LinkObjId="SW-123609_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123610_0" Pin1InfoVect1LinkObjId="SW-221272_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-762 4063,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c04de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-762 4048,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22788@x" ObjectIDND1="22787@x" ObjectIDZND0="34400@1" Pin0InfoVect0LinkObjId="SW-221272_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123610_0" Pin1InfoVect1LinkObjId="SW-123609_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-762 4048,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c05040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-762 3997,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34400@0" ObjectIDZND0="g_1c052a0@0" Pin0InfoVect0LinkObjId="g_1c052a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221272_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-762 3997,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_203c230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-1023 4095,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2194360@0" ObjectIDZND0="22794@x" ObjectIDZND1="34398@x" ObjectIDZND2="g_2194f40@0" Pin0InfoVect0LinkObjId="SW-123625_0" Pin0InfoVect1LinkObjId="SW-221265_0" Pin0InfoVect2LinkObjId="g_2194f40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2194360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-1023 4095,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_203c420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-1040 4125,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2194360@0" ObjectIDND1="g_2194f40@0" ObjectIDZND0="22794@x" ObjectIDZND1="34398@x" Pin0InfoVect0LinkObjId="SW-123625_0" Pin0InfoVect1LinkObjId="SW-221265_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2194360_0" Pin1InfoVect1LinkObjId="g_2194f40_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-1040 4125,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_203c610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4060,-1025 4060,-1040 4095,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2194f40@0" ObjectIDZND0="22794@x" ObjectIDZND1="34398@x" ObjectIDZND2="g_2194360@0" Pin0InfoVect0LinkObjId="SW-123625_0" Pin0InfoVect1LinkObjId="SW-221265_0" Pin0InfoVect2LinkObjId="g_2194360_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2194f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4060,-1025 4060,-1040 4095,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_203d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4357,-1027 4357,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_203a070@0" ObjectIDZND0="22792@x" ObjectIDZND1="34399@x" ObjectIDZND2="g_2114ac0@0" Pin0InfoVect0LinkObjId="SW-123623_0" Pin0InfoVect1LinkObjId="SW-221264_0" Pin0InfoVect2LinkObjId="g_2114ac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_203a070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4357,-1027 4357,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_203d2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4357,-1040 4327,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_203a070@0" ObjectIDND1="g_2114ac0@0" ObjectIDZND0="22792@x" ObjectIDZND1="34399@x" Pin0InfoVect0LinkObjId="SW-123623_0" Pin0InfoVect1LinkObjId="SW-221264_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_203a070_0" Pin1InfoVect1LinkObjId="g_2114ac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4357,-1040 4327,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a37d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-1030 4382,-1040 4357,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2114ac0@0" ObjectIDZND0="g_203a070@0" ObjectIDZND1="22792@x" ObjectIDZND2="34399@x" Pin0InfoVect0LinkObjId="g_203a070_0" Pin0InfoVect1LinkObjId="SW-123623_0" Pin0InfoVect2LinkObjId="SW-221264_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2114ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-1030 4382,-1040 4357,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f9780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-829 4597,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22812@0" ObjectIDZND0="22795@0" Pin0InfoVect0LinkObjId="SW-123626_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-829 4597,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fa430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-896 4613,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="22795@x" ObjectIDND1="g_20a3a10@0" ObjectIDZND0="34401@0" Pin0InfoVect0LinkObjId="SW-221271_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123626_0" Pin1InfoVect1LinkObjId="g_20a3a10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-896 4613,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fa690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-896 4670,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34401@1" ObjectIDZND0="g_20f99e0@0" Pin0InfoVect0LinkObjId="g_20f99e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-896 4670,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fdd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-881 4597,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="22795@1" ObjectIDZND0="34401@x" ObjectIDZND1="g_20a3a10@0" Pin0InfoVect0LinkObjId="SW-221271_0" Pin0InfoVect1LinkObjId="g_20a3a10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-881 4597,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fdf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-915 4597,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_20a3a10@0" ObjectIDZND0="34401@x" ObjectIDZND1="22795@x" Pin0InfoVect0LinkObjId="SW-221271_0" Pin0InfoVect1LinkObjId="SW-123626_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a3a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-915 4597,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22be670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-758 4469,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="34619@x" ObjectIDND1="34618@x" ObjectIDZND0="34617@1" Pin0InfoVect0LinkObjId="SW-221273_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123689_0" Pin1InfoVect1LinkObjId="SW-221274_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-758 4469,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c0b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-758 4454,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="34617@x" ObjectIDND1="34618@x" ObjectIDZND0="34619@1" Pin0InfoVect0LinkObjId="SW-123689_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-221273_0" Pin1InfoVect1LinkObjId="SW-221274_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-758 4454,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c0dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-758 4403,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34619@0" ObjectIDZND0="g_22c1030@0" Pin0InfoVect0LinkObjId="g_22c1030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123689_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-758 4403,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f78f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4041,-546 4062,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_21487c0@0" ObjectIDZND0="22790@x" ObjectIDZND1="22809@x" Pin0InfoVect0LinkObjId="SW-123612_0" Pin0InfoVect1LinkObjId="g_1ccad70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21487c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4041,-546 4062,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f82c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-620 4062,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22809@0" ObjectIDZND0="22790@x" ObjectIDZND1="g_21487c0@0" Pin0InfoVect0LinkObjId="SW-123612_0" Pin0InfoVect1LinkObjId="g_21487c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ccad70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4062,-620 4062,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f84e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-546 4062,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_21487c0@0" ObjectIDND1="22809@x" ObjectIDZND0="22790@1" Pin0InfoVect0LinkObjId="SW-123612_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21487c0_0" Pin1InfoVect1LinkObjId="g_1ccad70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4062,-546 4062,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f8740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4600,-191 4600,-206 4537,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1c61b70@0" ObjectIDZND0="22775@x" ObjectIDZND1="22799@x" Pin0InfoVect0LinkObjId="SW-123331_0" Pin0InfoVect1LinkObjId="SW-123671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c61b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4600,-191 4600,-206 4537,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a8e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3563,-385 3563,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22813@0" ObjectIDZND0="22786@1" Pin0InfoVect0LinkObjId="SW-123505_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cddd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3563,-385 3563,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a9080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3563,-325 3563,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22786@0" ObjectIDZND0="22785@1" Pin0InfoVect0LinkObjId="SW-123504_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123505_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3563,-325 3563,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a92e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3563,-211 3563,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="g_2134280@0" ObjectIDND1="34804@x" ObjectIDZND0="22802@0" Pin0InfoVect0LinkObjId="SW-123674_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2134280_0" Pin1InfoVect1LinkObjId="CB-WD_MJ.WD_MJ_cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3563,-211 3563,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a9540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3563,-256 3563,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22802@1" ObjectIDZND0="22785@0" Pin0InfoVect0LinkObjId="SW-123504_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123674_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3563,-256 3563,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a97a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-283 3707,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22758@0" ObjectIDZND0="22796@1" Pin0InfoVect0LinkObjId="SW-123668_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123198_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-283 3707,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_214db90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-198 3770,-198 3770,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22760@x" ObjectIDND1="22796@x" ObjectIDZND0="g_200c7d0@0" Pin0InfoVect0LinkObjId="g_200c7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123200_0" Pin1InfoVect1LinkObjId="SW-123668_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-198 3770,-198 3770,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_214ddf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-72 3742,-78 3707,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_212d0b0@0" ObjectIDZND0="33177@x" ObjectIDZND1="22760@x" Pin0InfoVect0LinkObjId="EC-WD_MJ.072Ld_0" Pin0InfoVect1LinkObjId="SW-123200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_212d0b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-72 3742,-78 3707,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_214e8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-11 3707,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33177@0" ObjectIDZND0="g_212d0b0@0" ObjectIDZND1="22760@x" Pin0InfoVect0LinkObjId="g_212d0b0_0" Pin0InfoVect1LinkObjId="SW-123200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_MJ.072Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-11 3707,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_214f3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-156 3855,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22764@1" ObjectIDZND0="22797@x" ObjectIDZND1="g_20ab090@0" Pin0InfoVect0LinkObjId="SW-123669_0" Pin0InfoVect1LinkObjId="g_20ab090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-156 3855,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2138190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-228 3855,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_20ab090@0" ObjectIDND1="22764@x" ObjectIDZND0="22797@0" Pin0InfoVect0LinkObjId="SW-123669_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20ab090_0" Pin1InfoVect1LinkObjId="SW-123243_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-228 3855,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21383f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3915,-210 3915,-228 3855,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_20ab090@0" ObjectIDZND0="22797@x" ObjectIDZND1="22764@x" Pin0InfoVect0LinkObjId="SW-123669_0" Pin0InfoVect1LinkObjId="SW-123243_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20ab090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3915,-210 3915,-228 3855,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2139100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-95 3855,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_21395c0@0" ObjectIDND1="33178@x" ObjectIDND2="g_1c55d30@0" ObjectIDZND0="22764@0" Pin0InfoVect0LinkObjId="SW-123243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21395c0_0" Pin1InfoVect1LinkObjId="EC-WD_MJ.073Ld_0" Pin1InfoVect2LinkObjId="g_1c55d30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-95 3855,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2139360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-85 3811,-95 3855,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="g_21395c0@0" ObjectIDZND0="22764@x" ObjectIDZND1="33178@x" ObjectIDZND2="g_1c55d30@0" Pin0InfoVect0LinkObjId="SW-123243_0" Pin0InfoVect1LinkObjId="EC-WD_MJ.073Ld_0" Pin0InfoVect2LinkObjId="g_1c55d30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21395c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-85 3811,-95 3855,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c540f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-6 3855,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="33178@0" ObjectIDZND0="22764@x" ObjectIDZND1="g_21395c0@0" ObjectIDZND2="g_1c55d30@0" Pin0InfoVect0LinkObjId="SW-123243_0" Pin0InfoVect1LinkObjId="g_21395c0_0" Pin0InfoVect2LinkObjId="g_1c55d30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_MJ.073Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-6 3855,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c2880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3923,-44 3923,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1c55d30@1" Pin0InfoVect0LinkObjId="g_1c55d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2194f40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3923,-44 3923,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22c2ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3923,-88 3923,-95 3855,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_1c55d30@0" ObjectIDZND0="22764@x" ObjectIDZND1="g_21395c0@0" ObjectIDZND2="33178@x" Pin0InfoVect0LinkObjId="SW-123243_0" Pin0InfoVect1LinkObjId="g_21395c0_0" Pin0InfoVect2LinkObjId="EC-WD_MJ.073Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c55d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3923,-88 3923,-95 3855,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b04fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-192 4203,-207 4140,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ce1720@0" ObjectIDZND0="22781@x" ObjectIDZND1="22801@x" Pin0InfoVect0LinkObjId="SW-123418_0" Pin0InfoVect1LinkObjId="SW-123673_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ce1720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-192 4203,-207 4140,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b05220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-207 4140,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1ce1720@0" ObjectIDND1="22801@x" ObjectIDZND0="22781@1" Pin0InfoVect0LinkObjId="SW-123418_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ce1720_0" Pin1InfoVect1LinkObjId="SW-123673_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4140,-207 4140,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa54c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4183,-385 4183,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22813@0" ObjectIDZND0="34615@0" Pin0InfoVect0LinkObjId="SW-221308_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cddd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4183,-385 4183,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac7a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-396 4363,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34616@0" ObjectIDZND0="34608@0" Pin0InfoVect0LinkObjId="g_1d9d430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-396 4363,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac7cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-428 4363,-449 4286,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34616@1" ObjectIDZND0="34614@0" Pin0InfoVect0LinkObjId="SW-221307_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221309_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-428 4363,-449 4286,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac7f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-449 4183,-449 4183,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34614@1" ObjectIDZND0="34615@1" Pin0InfoVect0LinkObjId="SW-221308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-449 4183,-449 4183,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_227d3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-114 4393,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22778@1" ObjectIDZND0="g_1c49a20@0" ObjectIDZND1="22800@x" Pin0InfoVect0LinkObjId="g_1c49a20_0" Pin0InfoVect1LinkObjId="SW-123672_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-114 4393,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_227d610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-204 4456,-204 4456,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22778@x" ObjectIDND1="22800@x" ObjectIDZND0="g_1c49a20@0" Pin0InfoVect0LinkObjId="g_1c49a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123374_0" Pin1InfoVect1LinkObjId="SW-123672_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-204 4456,-204 4456,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dc9910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-385 4393,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="34608@0" ObjectIDZND0="22777@1" Pin0InfoVect0LinkObjId="SW-123373_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ac7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-385 4393,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1abbd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-385 4537,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="34608@0" ObjectIDZND0="22774@1" Pin0InfoVect0LinkObjId="SW-123330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ac7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-385 4537,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1abbfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-328 4537,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22774@0" ObjectIDZND0="22773@1" Pin0InfoVect0LinkObjId="SW-123329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-328 4537,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1abc220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-206 4537,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22775@x" ObjectIDND1="g_1c61b70@0" ObjectIDZND0="22799@0" Pin0InfoVect0LinkObjId="SW-123671_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123331_0" Pin1InfoVect1LinkObjId="g_1c61b70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-206 4537,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1abc480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-258 4537,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22799@1" ObjectIDZND0="22773@0" Pin0InfoVect0LinkObjId="SW-123329_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123671_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-258 4537,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab4370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-385 4683,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="34608@0" ObjectIDZND0="22804@1" Pin0InfoVect0LinkObjId="SW-123682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ac7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-385 4683,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab45d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-326 4683,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22804@0" ObjectIDZND0="22806@1" Pin0InfoVect0LinkObjId="SW-123684_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-326 4683,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab4830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-279 4683,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22806@0" ObjectIDZND0="22805@1" Pin0InfoVect0LinkObjId="SW-123683_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-279 4683,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab4a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-223 4683,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22805@0" ObjectIDZND0="22811@x" ObjectIDZND1="g_1c59dd0@0" Pin0InfoVect0LinkObjId="SW-123692_0" Pin0InfoVect1LinkObjId="g_1c59dd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123683_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-223 4683,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c6cad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-385 4827,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="34608@0" ObjectIDZND0="22768@1" Pin0InfoVect0LinkObjId="SW-123301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ac7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-385 4827,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c6cd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-327 4827,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22768@0" ObjectIDZND0="22770@1" Pin0InfoVect0LinkObjId="SW-123303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-327 4827,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c6cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-279 4827,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22770@0" ObjectIDZND0="22769@1" Pin0InfoVect0LinkObjId="SW-123302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-279 4827,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c1f340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-224 4827,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22769@0" ObjectIDZND0="22771@x" ObjectIDZND1="g_2146260@0" Pin0InfoVect0LinkObjId="SW-123309_0" Pin0InfoVect1LinkObjId="g_2146260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-224 4827,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c245b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-385 4797,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="34608@0" ObjectIDZND0="34803@0" Pin0InfoVect0LinkObjId="SW-123688_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ac7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-385 4797,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c24810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-446 4797,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="34803@1" ObjectIDZND0="g_1d8f430@0" Pin0InfoVect0LinkObjId="g_1d8f430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123688_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-446 4797,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c25690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-203 4393,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22778@x" ObjectIDND1="g_1c49a20@0" ObjectIDZND0="22800@0" Pin0InfoVect0LinkObjId="SW-123672_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123374_0" Pin1InfoVect1LinkObjId="g_1c49a20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-203 4393,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c25880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-357 4140,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22780@1" ObjectIDZND0="22813@0" Pin0InfoVect0LinkObjId="g_1cddd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123417_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4140,-357 4140,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c25a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-357 3855,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22763@1" ObjectIDZND0="22813@0" Pin0InfoVect0LinkObjId="g_1cddd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-357 3855,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c25c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-357 3995,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22766@1" ObjectIDZND0="22813@0" Pin0InfoVect0LinkObjId="g_1cddd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-357 3995,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c25e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-310 3707,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22758@1" ObjectIDZND0="22759@0" Pin0InfoVect0LinkObjId="SW-123199_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123198_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-310 3707,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c260c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-357 3707,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22759@1" ObjectIDZND0="22813@0" Pin0InfoVect0LinkObjId="g_1cddd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123199_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-357 3707,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c262f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-275 3855,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22797@1" ObjectIDZND0="22762@0" Pin0InfoVect0LinkObjId="SW-123241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123669_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-275 3855,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c26520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-314 3855,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22762@1" ObjectIDZND0="22763@0" Pin0InfoVect0LinkObjId="SW-123242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-314 3855,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c26750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-207 4140,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1ce1720@0" ObjectIDND1="22781@x" ObjectIDZND0="22801@0" Pin0InfoVect0LinkObjId="SW-123673_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ce1720_0" Pin1InfoVect1LinkObjId="SW-123418_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4140,-207 4140,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c26980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-208 3996,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22767@x" ObjectIDND1="g_1ff9750@0" ObjectIDZND0="22798@0" Pin0InfoVect0LinkObjId="SW-123670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-123287_0" Pin1InfoVect1LinkObjId="g_1ff9750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-208 3996,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8da30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-78 3707,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33177@x" ObjectIDND1="g_212d0b0@0" ObjectIDZND0="22760@0" Pin0InfoVect0LinkObjId="SW-123200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_MJ.072Ld_0" Pin1InfoVect1LinkObjId="g_212d0b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-78 3707,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8e500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-123 3707,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22760@1" ObjectIDZND0="g_200c7d0@0" ObjectIDZND1="22796@x" Pin0InfoVect0LinkObjId="g_200c7d0_0" Pin0InfoVect1LinkObjId="SW-123668_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-123 3707,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8e760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-225 3707,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22796@0" ObjectIDZND0="g_200c7d0@0" ObjectIDZND1="22760@x" Pin0InfoVect0LinkObjId="g_200c7d0_0" Pin0InfoVect1LinkObjId="SW-123200_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123668_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-225 3707,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8e9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-829 4469,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22812@0" ObjectIDZND0="34618@1" Pin0InfoVect0LinkObjId="SW-221274_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-829 4469,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d8ec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-771 4469,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="34618@0" ObjectIDZND0="34617@x" ObjectIDZND1="34619@x" Pin0InfoVect0LinkObjId="SW-221273_0" Pin0InfoVect1LinkObjId="SW-123689_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221274_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-771 4469,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c17e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-385 3723,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22813@0" ObjectIDZND0="22803@0" Pin0InfoVect0LinkObjId="SW-123678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cddd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-385 3723,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c180a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-446 3723,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="22803@1" ObjectIDZND0="g_1c18300@0" Pin0InfoVect0LinkObjId="g_1c18300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-446 3723,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d98500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-481 4469,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34620@1" ObjectIDZND0="34622@0" Pin0InfoVect0LinkObjId="SW-221302_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-481 4469,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9d1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-454 4469,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34620@0" ObjectIDZND0="34621@1" Pin0InfoVect0LinkObjId="SW-221303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-454 4469,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9d430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-404 4469,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34621@0" ObjectIDZND0="34608@0" Pin0InfoVect0LinkObjId="g_1ac7a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-404 4469,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d82d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-712 4469,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="34617@0" ObjectIDZND0="34623@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221273_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-712 4469,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d82f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-256 3996,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22798@1" ObjectIDZND0="22765@0" Pin0InfoVect0LinkObjId="SW-123285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-256 3996,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d831e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-306 3996,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22765@1" ObjectIDZND0="22766@0" Pin0InfoVect0LinkObjId="SW-123286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3996,-306 3996,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d83440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-256 4140,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22801@1" ObjectIDZND0="22779@0" Pin0InfoVect0LinkObjId="SW-123416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123673_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4140,-256 4140,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d836a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-306 4140,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22779@1" ObjectIDZND0="22780@0" Pin0InfoVect0LinkObjId="SW-123417_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4140,-306 4140,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d83900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-256 4393,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22800@1" ObjectIDZND0="22776@0" Pin0InfoVect0LinkObjId="SW-123372_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123672_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-256 4393,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d83b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-302 4393,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22776@1" ObjectIDZND0="22777@0" Pin0InfoVect0LinkObjId="SW-123373_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123372_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-302 4393,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2deb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-385 5026,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="34608@0" ObjectIDZND0="34612@1" Pin0InfoVect0LinkObjId="SW-123190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ac7a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-385 5026,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-324 5026,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34612@0" ObjectIDZND0="34610@1" Pin0InfoVect0LinkObjId="SW-123535_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-123190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-324 5026,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-255 5026,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22807@1" ObjectIDZND0="34610@0" Pin0InfoVect0LinkObjId="SW-123535_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221305_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-255 5026,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7f060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-94 5026,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="34805@0" ObjectIDZND0="34611@0" Pin0InfoVect0LinkObjId="SW-221304_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-WD_MJ.WD_MJ_cb2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-94 5026,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1daaa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-223 5026,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22807@0" ObjectIDZND0="34611@x" ObjectIDZND1="g_1d861d0@0" Pin0InfoVect0LinkObjId="SW-221304_0" Pin0InfoVect1LinkObjId="g_1d861d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221305_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-223 5026,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dab370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-144 5026,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34611@1" ObjectIDZND0="22807@x" ObjectIDZND1="g_1d861d0@0" Pin0InfoVect0LinkObjId="SW-221305_0" Pin0InfoVect1LinkObjId="g_1d861d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221304_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-144 5026,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dab560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-210 5087,-210 5087,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22807@x" ObjectIDND1="34611@x" ObjectIDZND0="g_1d861d0@0" Pin0InfoVect0LinkObjId="g_1d861d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-221305_0" Pin1InfoVect1LinkObjId="SW-221304_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-210 5087,-210 5087,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dc1a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4446,-539 4469,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1dc0f00@0" ObjectIDZND0="34622@x" ObjectIDZND1="34623@x" Pin0InfoVect0LinkObjId="SW-221302_0" Pin0InfoVect1LinkObjId="g_1d82d20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dc0f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4446,-539 4469,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dc2580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-603 4469,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34623@0" ObjectIDZND0="g_1dc0f00@0" ObjectIDZND1="34622@x" Pin0InfoVect0LinkObjId="g_1dc0f00_0" Pin0InfoVect1LinkObjId="SW-221302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d82d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-603 4469,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dc27e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-539 4469,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1dc0f00@0" ObjectIDND1="34623@x" ObjectIDZND0="34622@1" Pin0InfoVect0LinkObjId="SW-221302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dc0f00_0" Pin1InfoVect1LinkObjId="g_1d82d20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-539 4469,-531 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22812" cx="4242" cy="-829" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22812" cx="4597" cy="-829" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22812" cx="4063" cy="-829" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34608" cx="4393" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34608" cx="4537" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34608" cx="4683" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34608" cx="4827" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34608" cx="4797" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22812" cx="4469" cy="-829" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34608" cx="4469" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34608" cx="5026" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22813" cx="3563" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22813" cx="4183" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22813" cx="4140" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22813" cx="3855" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22813" cx="3995" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22813" cx="3707" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22813" cx="3723" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22813" cx="4062" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-119713" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3386.500000 -994.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22310" ObjectName="DYN-WD_MJ"/>
     <cge:Meas_Ref ObjectId="119713"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ccdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3091.000000 -941.000000) translate(0,18)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ccdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3091.000000 -941.000000) translate(0,40)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ccdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3091.000000 -941.000000) translate(0,62)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ccdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3091.000000 -941.000000) translate(0,84)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ccdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3091.000000 -941.000000) translate(0,106)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ccdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3091.000000 -941.000000) translate(0,128)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ccdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3091.000000 -941.000000) translate(0,150)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ccdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3091.000000 -941.000000) translate(0,172)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ccdf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3091.000000 -941.000000) translate(0,194)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,18)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,40)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,62)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,84)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,106)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,128)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,150)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,172)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,194)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,238)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,260)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,282)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,304)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,326)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,348)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3093.000000 -529.000000) translate(0,370)">联系方式：8860006</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="38" graphid="g_2078010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3213.000000 -1107.500000) translate(0,31)">猫街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2043e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -732.000000) translate(0,15)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2043e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -732.000000) translate(0,33)">SFZ7-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2043e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -732.000000) translate(0,51)">8000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2043e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -732.000000) translate(0,69)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2043e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -732.000000) translate(0,87)">Y/△-11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2043e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -732.000000) translate(0,105)">Ud=7.46%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226dc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -1176.000000) translate(0,15)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226dc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -1176.000000) translate(0,33)">猫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226dc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -1176.000000) translate(0,51)">高</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_226dc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -1176.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_203ada0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 -856.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2107450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 -1043.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2108520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -961.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2108e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -959.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2037b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3731.500000 -303.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2037dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3585.500000 -306.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -84.000000) translate(0,12)">07867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4690.000000 -108.000000) translate(0,12)">0786</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4704.500000 -301.000000) translate(0,12)">078</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4148.000000 -108.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.500000 -300.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219b890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3713.000000 -108.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219bad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -108.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219bff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4017.500000 -300.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219c270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -108.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219c4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4416.500000 -299.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4546.000000 -108.000000) translate(0,12)">0776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219c930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.500000 -301.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219cb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 -81.000000) translate(0,12)">07967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_219d090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4834.000000 -108.000000) translate(0,12)">0796</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228da40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4848.500000 -298.000000) translate(0,12)">079</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228dc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -148.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228e100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.500000 -306.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228e380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -474.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4073.000000 -526.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 -795.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228ea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.500000 -738.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_228ec80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4121.000000 -993.000000) translate(0,16)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -873.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4250.000000 -996.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -929.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4159.000000 -1066.000000) translate(0,12)">3713</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4268.000000 -1068.000000) translate(0,12)">3719</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22708a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3096.000000 -703.000000) translate(0,15)">公共信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_22aa810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3393.000000 -1084.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_22ab860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3393.000000 -1119.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210c8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -949.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210cde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.500000 -922.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2195f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -1079.000000) translate(0,12)">37137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2196450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4334.000000 -1080.000000) translate(0,12)">37197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203b5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4006.000000 -790.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20fa9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4604.000000 -875.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20fd0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4616.000000 -922.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bdd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4488.000000 -474.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22be1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4479.000000 -794.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22be430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4485.000000 -733.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f6cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4412.000000 -785.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22c2d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3911.000000 48.000000) translate(0,16)">2号站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22c2d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3911.000000 48.000000) translate(0,36)">用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c24a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -613.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c24a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -613.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c1c020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3674.000000 -610.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c1c020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3674.000000 -610.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d86f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.500000 -305.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d781e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4493.000000 -425.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d786d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4489.000000 -525.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d78910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -427.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d78b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -742.000000) translate(0,15)">2号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d78b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -742.000000) translate(0,33)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d78b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -742.000000) translate(0,51)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d78b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -742.000000) translate(0,69)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d78b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -742.000000) translate(0,87)">Uk=7.21%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d794c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -346.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d79750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3582.000000 -246.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d79990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3726.000000 -347.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d79bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -247.000000) translate(0,12)">0723</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d79e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3873.000000 -347.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7a050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 -262.000000) translate(0,12)">0733</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7a290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4015.000000 -347.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7a4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4016.000000 -244.000000) translate(0,12)">0743</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7a710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -349.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7a950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -245.000000) translate(0,12)">0753</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7ab90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4260.000000 -473.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7add0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4200.000000 -418.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -416.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7b250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -351.000000) translate(0,12)">0762</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7b490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4416.000000 -243.000000) translate(0,12)">0763</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7b6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -350.000000) translate(0,12)">0772</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7b910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4557.000000 -249.000000) translate(0,12)">0773</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4701.000000 -344.000000) translate(0,12)">0782</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7bd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.000000 -244.000000) translate(0,12)">0783</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7bfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4844.000000 -348.000000) translate(0,12)">0792</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7c210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4850.000000 -245.000000) translate(0,12)">0793</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7c450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5045.000000 -351.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7c690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5047.000000 -245.000000) translate(0,12)">0813</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7c8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4917.000000 -105.000000) translate(0,12)">08167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7f2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5041.000000 -133.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7f7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -435.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7f9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -438.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d7fc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3439.000000 -407.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d7fe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4985.000000 -407.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d800b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -651.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1d80550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3468.500000 -1027.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_1da9e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3506.000000 -10.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_1daa4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.000000 21.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc3560" transform="matrix(1.000000 0.000000 0.000000 1.000000 3679.000000 -70.000000) translate(0,15)">猫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc3560" transform="matrix(1.000000 0.000000 0.000000 1.000000 3679.000000 -70.000000) translate(0,33)">街</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc3560" transform="matrix(1.000000 0.000000 0.000000 1.000000 3679.000000 -70.000000) translate(0,51)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc3560" transform="matrix(1.000000 0.000000 0.000000 1.000000 3679.000000 -70.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc3dd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3832.000000 -76.000000) translate(0,15)">10kV迤纳厂线073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc4970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3971.000000 -65.000000) translate(0,15)">咪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc4970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3971.000000 -65.000000) translate(0,33)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc4970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3971.000000 -65.000000) translate(0,51)">咱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc4970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3971.000000 -65.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc5510" transform="matrix(1.000000 0.000000 0.000000 1.000000 4113.000000 -67.000000) translate(0,15)">收</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc5510" transform="matrix(1.000000 0.000000 0.000000 1.000000 4113.000000 -67.000000) translate(0,33)">费</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc5510" transform="matrix(1.000000 0.000000 0.000000 1.000000 4113.000000 -67.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc5510" transform="matrix(1.000000 0.000000 0.000000 1.000000 4113.000000 -67.000000) translate(0,69)">专</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc5510" transform="matrix(1.000000 0.000000 0.000000 1.000000 4113.000000 -67.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc60b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4367.000000 -62.000000) translate(0,15)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc60b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4367.000000 -62.000000) translate(0,33)">家</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc60b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4367.000000 -62.000000) translate(0,51)">村</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc60b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4367.000000 -62.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0d110" transform="matrix(1.000000 0.000000 0.000000 1.000000 4512.000000 -61.000000) translate(0,15)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0d110" transform="matrix(1.000000 0.000000 0.000000 1.000000 4512.000000 -61.000000) translate(0,33)">庆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0d110" transform="matrix(1.000000 0.000000 0.000000 1.000000 4512.000000 -61.000000) translate(0,51)">关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0d110" transform="matrix(1.000000 0.000000 0.000000 1.000000 4512.000000 -61.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0dc50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4659.000000 -58.000000) translate(0,15)">恒</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0dc50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4659.000000 -58.000000) translate(0,33)">雄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0dc50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4659.000000 -58.000000) translate(0,51)">专</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0dc50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4659.000000 -58.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0e560" transform="matrix(1.000000 0.000000 0.000000 1.000000 4802.000000 -53.000000) translate(0,15)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0e560" transform="matrix(1.000000 0.000000 0.000000 1.000000 4802.000000 -53.000000) translate(0,33)">翔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0e560" transform="matrix(1.000000 0.000000 0.000000 1.000000 4802.000000 -53.000000) translate(0,51)">专</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d0e560" transform="matrix(1.000000 0.000000 0.000000 1.000000 4802.000000 -53.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d143c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3068.000000 -120.500000) translate(0,16)">武定巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d15ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3202.000000 -140.500000) translate(0,16)">18787878990</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d15ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3202.000000 -140.500000) translate(0,36)">18787842893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d15ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3202.000000 -140.500000) translate(0,56)">13987880311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d17ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -669.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d18d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3416.000000 -506.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3416.000000 -521.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3416.000000 -537.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1acb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3422.000000 -489.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1af30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3428.000000 -429.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1bad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.000000 -474.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1bd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.000000 -458.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1bf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3408.000000 -443.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1c1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5001.000000 -504.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1c410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5001.000000 -519.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1c650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5001.000000 -535.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1c890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5007.000000 -487.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1cad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -427.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1cd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -472.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1cf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -456.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1d190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -441.000000) translate(0,12)">Uca(kV):</text>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-151 3700,-136 3715,-136 3707,-151 3707,-151 3707,-151 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-161 3699,-176 3714,-176 3706,-161 3706,-161 3706,-161 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3563,-155 3556,-140 3571,-140 3563,-155 3563,-155 3563,-155 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3563,-178 3556,-193 3571,-193 3563,-178 3563,-178 3563,-178 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-567 4055,-552 4070,-552 4062,-567 4062,-567 4062,-567 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-583 4055,-598 4070,-598 4062,-583 4062,-583 4062,-583 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-187 3848,-172 3863,-172 3855,-187 3855,-187 3855,-187 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-199 3848,-214 3863,-214 3855,-199 3855,-199 3855,-199 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-153 3989,-138 4004,-138 3996,-153 3996,-153 3996,-153 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3996,-176 3989,-191 4004,-191 3996,-176 3996,-176 3996,-176 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-152 4133,-137 4148,-137 4140,-152 4140,-152 4140,-152 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-175 4133,-190 4148,-190 4140,-175 4140,-175 4140,-175 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-150 4386,-135 4401,-135 4393,-150 4393,-150 4393,-150 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-173 4386,-188 4401,-188 4393,-173 4393,-173 4393,-173 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-151 4530,-136 4545,-136 4537,-151 4537,-151 4537,-151 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-174 4530,-189 4545,-189 4537,-174 4537,-174 4537,-174 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-151 4676,-136 4691,-136 4683,-151 4683,-151 4683,-151 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-174 4676,-189 4691,-189 4683,-174 4683,-174 4683,-174 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-151 4820,-136 4835,-136 4827,-151 4827,-151 4827,-151 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-174 4820,-189 4835,-189 4827,-174 4827,-174 4827,-174 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-563 4462,-548 4477,-548 4469,-563 4469,-563 4469,-563 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-579 4462,-594 4477,-594 4469,-579 4469,-579 4469,-579 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-95 3909,-88 3909,-103 3894,-95 3894,-95 3894,-95 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-95 3867,-88 3867,-103 3882,-95 3882,-95 3882,-95 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-165 5019,-150 5034,-150 5026,-165 5026,-165 5026,-165 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-177 5019,-192 5034,-192 5026,-177 5026,-177 5026,-177 " stroke="rgb(0,255,0)"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-123610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -770.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22788" ObjectName="SW-WD_MJ.WD_MJ_3011SW"/>
     <cge:Meas_Ref ObjectId="123610"/>
    <cge:TPSR_Ref TObjectID="22788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123612">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -495.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22790" ObjectName="SW-WD_MJ.WD_MJ_0016SW"/>
     <cge:Meas_Ref ObjectId="123612"/>
    <cge:TPSR_Ref TObjectID="22790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123200">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 -82.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22760" ObjectName="SW-WD_MJ.WD_MJ_0726SW"/>
     <cge:Meas_Ref ObjectId="123200"/>
    <cge:TPSR_Ref TObjectID="22760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123243">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -115.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22764" ObjectName="SW-WD_MJ.WD_MJ_0736SW"/>
     <cge:Meas_Ref ObjectId="123243"/>
    <cge:TPSR_Ref TObjectID="22764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123287">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 -77.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22767" ObjectName="SW-WD_MJ.WD_MJ_0746SW"/>
     <cge:Meas_Ref ObjectId="123287"/>
    <cge:TPSR_Ref TObjectID="22767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123418">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4131.000000 -76.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22781" ObjectName="SW-WD_MJ.WD_MJ_0756SW"/>
     <cge:Meas_Ref ObjectId="123418"/>
    <cge:TPSR_Ref TObjectID="22781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -73.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22778" ObjectName="SW-WD_MJ.WD_MJ_0766SW"/>
     <cge:Meas_Ref ObjectId="123374"/>
    <cge:TPSR_Ref TObjectID="22778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 -78.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22775" ObjectName="SW-WD_MJ.WD_MJ_0776SW"/>
     <cge:Meas_Ref ObjectId="123331"/>
    <cge:TPSR_Ref TObjectID="22775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123692">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 -78.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22811" ObjectName="SW-WD_MJ.WD_MJ_0786SW"/>
     <cge:Meas_Ref ObjectId="123692"/>
    <cge:TPSR_Ref TObjectID="22811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123210">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4707.000000 -50.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22761" ObjectName="SW-WD_MJ.WD_MJ_07867SW"/>
     <cge:Meas_Ref ObjectId="123210"/>
    <cge:TPSR_Ref TObjectID="22761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123309">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4818.000000 -78.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22771" ObjectName="SW-WD_MJ.WD_MJ_0796SW"/>
     <cge:Meas_Ref ObjectId="123309"/>
    <cge:TPSR_Ref TObjectID="22771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123310">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4851.000000 -50.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22772" ObjectName="SW-WD_MJ.WD_MJ_07967SW"/>
     <cge:Meas_Ref ObjectId="123310"/>
    <cge:TPSR_Ref TObjectID="22772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123462">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 -839.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22784" ObjectName="SW-WD_MJ.WD_MJ_3711SW"/>
     <cge:Meas_Ref ObjectId="123462"/>
    <cge:TPSR_Ref TObjectID="22784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123461">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 -963.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22783" ObjectName="SW-WD_MJ.WD_MJ_3716SW"/>
     <cge:Meas_Ref ObjectId="123461"/>
    <cge:TPSR_Ref TObjectID="22783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123624">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.000000 -965.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22793" ObjectName="SW-WD_MJ.WD_MJ_37167SW"/>
     <cge:Meas_Ref ObjectId="123624"/>
    <cge:TPSR_Ref TObjectID="22793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123625">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4145.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22794" ObjectName="SW-WD_MJ.WD_MJ_3713SW"/>
     <cge:Meas_Ref ObjectId="123625"/>
    <cge:TPSR_Ref TObjectID="22794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123623">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4258.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22792" ObjectName="SW-WD_MJ.WD_MJ_3719SW"/>
     <cge:Meas_Ref ObjectId="123623"/>
    <cge:TPSR_Ref TObjectID="22792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221266">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -947.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34396" ObjectName="SW-WD_MJ.WD_MJ_37160SW"/>
     <cge:Meas_Ref ObjectId="221266"/>
    <cge:TPSR_Ref TObjectID="34396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4270.000000 -889.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34397" ObjectName="SW-WD_MJ.WD_MJ_37117SW"/>
     <cge:Meas_Ref ObjectId="221438"/>
    <cge:TPSR_Ref TObjectID="34397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221265">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4116.000000 -1050.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34398" ObjectName="SW-WD_MJ.WD_MJ_37137SW"/>
     <cge:Meas_Ref ObjectId="221265"/>
    <cge:TPSR_Ref TObjectID="34398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221264">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.000000 -1050.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34399" ObjectName="SW-WD_MJ.WD_MJ_37197SW"/>
     <cge:Meas_Ref ObjectId="221264"/>
    <cge:TPSR_Ref TObjectID="34399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221272">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -757.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34400" ObjectName="SW-WD_MJ.WD_MJ_30117SW"/>
     <cge:Meas_Ref ObjectId="221272"/>
    <cge:TPSR_Ref TObjectID="34400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123626">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -840.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22795" ObjectName="SW-WD_MJ.WD_MJ_3901SW"/>
     <cge:Meas_Ref ObjectId="123626"/>
    <cge:TPSR_Ref TObjectID="22795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4608.000000 -891.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34401" ObjectName="SW-WD_MJ.WD_MJ_39017SW"/>
     <cge:Meas_Ref ObjectId="221271"/>
    <cge:TPSR_Ref TObjectID="34401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221274">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4460.000000 -766.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34618" ObjectName="SW-WD_MJ.WD_MJ_3021SW"/>
     <cge:Meas_Ref ObjectId="221274"/>
    <cge:TPSR_Ref TObjectID="34618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123689">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -753.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34619" ObjectName="SW-WD_MJ.WD_MJ_30217SW"/>
     <cge:Meas_Ref ObjectId="123689"/>
    <cge:TPSR_Ref TObjectID="34619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123613">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 -400.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22791" ObjectName="SW-WD_MJ.WD_MJ_0011SW"/>
     <cge:Meas_Ref ObjectId="123613"/>
    <cge:TPSR_Ref TObjectID="22791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123505">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 -320.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22786" ObjectName="SW-WD_MJ.WD_MJ_0711SW"/>
     <cge:Meas_Ref ObjectId="123505"/>
    <cge:TPSR_Ref TObjectID="22786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123674">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 -219.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22802" ObjectName="SW-WD_MJ.WD_MJ_0716SW"/>
     <cge:Meas_Ref ObjectId="123674"/>
    <cge:TPSR_Ref TObjectID="22802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123668">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.000000 -220.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22796" ObjectName="SW-WD_MJ.WD_MJ_0723SW"/>
     <cge:Meas_Ref ObjectId="123668"/>
    <cge:TPSR_Ref TObjectID="22796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123199">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.000000 -320.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22759" ObjectName="SW-WD_MJ.WD_MJ_0721SW"/>
     <cge:Meas_Ref ObjectId="123199"/>
    <cge:TPSR_Ref TObjectID="22759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123242">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.000000 -320.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22763" ObjectName="SW-WD_MJ.WD_MJ_0731SW"/>
     <cge:Meas_Ref ObjectId="123242"/>
    <cge:TPSR_Ref TObjectID="22763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 -320.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22766" ObjectName="SW-WD_MJ.WD_MJ_0741SW"/>
     <cge:Meas_Ref ObjectId="123286"/>
    <cge:TPSR_Ref TObjectID="22766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.000000 -238.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22797" ObjectName="SW-WD_MJ.WD_MJ_0733SW"/>
     <cge:Meas_Ref ObjectId="123669"/>
    <cge:TPSR_Ref TObjectID="22797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123670">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 -219.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22798" ObjectName="SW-WD_MJ.WD_MJ_0743SW"/>
     <cge:Meas_Ref ObjectId="123670"/>
    <cge:TPSR_Ref TObjectID="22798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123417">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.000000 -320.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22780" ObjectName="SW-WD_MJ.WD_MJ_0751SW"/>
     <cge:Meas_Ref ObjectId="123417"/>
    <cge:TPSR_Ref TObjectID="22780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.000000 -219.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22801" ObjectName="SW-WD_MJ.WD_MJ_0753SW"/>
     <cge:Meas_Ref ObjectId="123673"/>
    <cge:TPSR_Ref TObjectID="22801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.000000 -391.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34615" ObjectName="SW-WD_MJ.WD_MJ_0121SW"/>
     <cge:Meas_Ref ObjectId="221308"/>
    <cge:TPSR_Ref TObjectID="34615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221309">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4331.000000 -391.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34616" ObjectName="SW-WD_MJ.WD_MJ_0122SW"/>
     <cge:Meas_Ref ObjectId="221309"/>
    <cge:TPSR_Ref TObjectID="34616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123373">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -325.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22777" ObjectName="SW-WD_MJ.WD_MJ_0762SW"/>
     <cge:Meas_Ref ObjectId="123373"/>
    <cge:TPSR_Ref TObjectID="22777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -219.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22800" ObjectName="SW-WD_MJ.WD_MJ_0763SW"/>
     <cge:Meas_Ref ObjectId="123672"/>
    <cge:TPSR_Ref TObjectID="22800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123330">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.000000 -323.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22774" ObjectName="SW-WD_MJ.WD_MJ_0772SW"/>
     <cge:Meas_Ref ObjectId="123330"/>
    <cge:TPSR_Ref TObjectID="22774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.000000 -221.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22799" ObjectName="SW-WD_MJ.WD_MJ_0773SW"/>
     <cge:Meas_Ref ObjectId="123671"/>
    <cge:TPSR_Ref TObjectID="22799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -321.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22804" ObjectName="SW-WD_MJ.WD_MJ_0782SW"/>
     <cge:Meas_Ref ObjectId="123682"/>
    <cge:TPSR_Ref TObjectID="22804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -218.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22805" ObjectName="SW-WD_MJ.WD_MJ_0783SW"/>
     <cge:Meas_Ref ObjectId="123683"/>
    <cge:TPSR_Ref TObjectID="22805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -322.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22768" ObjectName="SW-WD_MJ.WD_MJ_0792SW"/>
     <cge:Meas_Ref ObjectId="123301"/>
    <cge:TPSR_Ref TObjectID="22768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -219.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22769" ObjectName="SW-WD_MJ.WD_MJ_0793SW"/>
     <cge:Meas_Ref ObjectId="123302"/>
    <cge:TPSR_Ref TObjectID="22769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123688">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4765.000000 -409.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34803" ObjectName="SW-WD_MJ.WD_MJ_0902SW"/>
     <cge:Meas_Ref ObjectId="123688"/>
    <cge:TPSR_Ref TObjectID="34803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3691.000000 -409.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22803" ObjectName="SW-WD_MJ.WD_MJ_0901SW"/>
     <cge:Meas_Ref ObjectId="123678"/>
    <cge:TPSR_Ref TObjectID="22803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.000000 -494.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34622" ObjectName="SW-WD_MJ.WD_MJ_0026SW"/>
     <cge:Meas_Ref ObjectId="221302"/>
    <cge:TPSR_Ref TObjectID="34622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.000000 -399.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34621" ObjectName="SW-WD_MJ.WD_MJ_0022SW"/>
     <cge:Meas_Ref ObjectId="221303"/>
    <cge:TPSR_Ref TObjectID="34621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-123190">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 -319.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34612" ObjectName="SW-WD_MJ.WD_MJ_0812SW"/>
     <cge:Meas_Ref ObjectId="123190"/>
    <cge:TPSR_Ref TObjectID="34612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221305">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 -218.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22807" ObjectName="SW-WD_MJ.WD_MJ_0813SW"/>
     <cge:Meas_Ref ObjectId="221305"/>
    <cge:TPSR_Ref TObjectID="22807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5010.500000 -96.500000)" xlink:href="#switch2:shape24_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22808" ObjectName="SW-WD_MJ.WD_MJ_08167SW"/>
     <cge:Meas_Ref ObjectId="221306"/>
    <cge:TPSR_Ref TObjectID="22808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34611" ObjectName="SW-WD_MJ.WD_MJ_0816SW"/>
     <cge:Meas_Ref ObjectId="221304"/>
    <cge:TPSR_Ref TObjectID="34611"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-WD_MJ.WD_MJ_cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3538.000000 -16.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34804" ObjectName="CB-WD_MJ.WD_MJ_cb1"/>
    <cge:TPSR_Ref TObjectID="34804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-WD_MJ.WD_MJ_cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5001.000000 15.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34805" ObjectName="CB-WD_MJ.WD_MJ_cb2"/>
    <cge:TPSR_Ref TObjectID="34805"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_200c7d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 -127.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_212d0b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3735.000000 -18.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2134280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3617.000000 -142.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ab090">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 -156.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff9750">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -139.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_201e530">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4051.000000 10.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ce1720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -138.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c48ee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 11.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c49a20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -135.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b31a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4448.000000 11.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c61b70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -137.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cea160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 9.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c59dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 -137.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b154b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4710.000000 -16.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2146260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 -137.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_203f200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4854.000000 -16.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2194360">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 -969.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_203a070">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.000000 -973.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21487c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3984.000000 -539.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21395c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 -31.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c55d30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3914.000000 -51.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d861d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5080.000000 -141.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dc0f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -532.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-123081" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -934.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22812"/>
     <cge:Term_Ref ObjectID="32087"/>
    <cge:TPSR_Ref TObjectID="22812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-221339" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -934.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221339" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22812"/>
     <cge:Term_Ref ObjectID="32087"/>
    <cge:TPSR_Ref TObjectID="22812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-221340" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -934.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221340" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22812"/>
     <cge:Term_Ref ObjectID="32087"/>
    <cge:TPSR_Ref TObjectID="22812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-123075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -934.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22812"/>
     <cge:Term_Ref ObjectID="32087"/>
    <cge:TPSR_Ref TObjectID="22812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-123087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -934.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22812"/>
     <cge:Term_Ref ObjectID="32087"/>
    <cge:TPSR_Ref TObjectID="22812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-123105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3478.000000 -536.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22813"/>
     <cge:Term_Ref ObjectID="32088"/>
    <cge:TPSR_Ref TObjectID="22813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-221342" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3478.000000 -536.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221342" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22813"/>
     <cge:Term_Ref ObjectID="32088"/>
    <cge:TPSR_Ref TObjectID="22813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-221343" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3478.000000 -536.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221343" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22813"/>
     <cge:Term_Ref ObjectID="32088"/>
    <cge:TPSR_Ref TObjectID="22813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-256071" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3478.000000 -536.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256071" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22813"/>
     <cge:Term_Ref ObjectID="32088"/>
    <cge:TPSR_Ref TObjectID="22813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-123119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3478.000000 -536.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22813"/>
     <cge:Term_Ref ObjectID="32088"/>
    <cge:TPSR_Ref TObjectID="22813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-123120" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3478.000000 -536.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22813"/>
     <cge:Term_Ref ObjectID="32088"/>
    <cge:TPSR_Ref TObjectID="22813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-123133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3478.000000 -536.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22813"/>
     <cge:Term_Ref ObjectID="32088"/>
    <cge:TPSR_Ref TObjectID="22813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-123093" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3478.000000 -536.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123093" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22813"/>
     <cge:Term_Ref ObjectID="32088"/>
    <cge:TPSR_Ref TObjectID="22813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-221355" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -534.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221355" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34608"/>
     <cge:Term_Ref ObjectID="50395"/>
    <cge:TPSR_Ref TObjectID="34608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-221356" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -534.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221356" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34608"/>
     <cge:Term_Ref ObjectID="50395"/>
    <cge:TPSR_Ref TObjectID="34608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-221357" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -534.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221357" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34608"/>
     <cge:Term_Ref ObjectID="50395"/>
    <cge:TPSR_Ref TObjectID="34608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-221361" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -534.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221361" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34608"/>
     <cge:Term_Ref ObjectID="50395"/>
    <cge:TPSR_Ref TObjectID="34608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-221358" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -534.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221358" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34608"/>
     <cge:Term_Ref ObjectID="50395"/>
    <cge:TPSR_Ref TObjectID="34608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-221359" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -534.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221359" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34608"/>
     <cge:Term_Ref ObjectID="50395"/>
    <cge:TPSR_Ref TObjectID="34608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-221360" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -534.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221360" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34608"/>
     <cge:Term_Ref ObjectID="50395"/>
    <cge:TPSR_Ref TObjectID="34608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-123099" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -534.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34608"/>
     <cge:Term_Ref ObjectID="50395"/>
    <cge:TPSR_Ref TObjectID="34608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -1228.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22782"/>
     <cge:Term_Ref ObjectID="32023"/>
    <cge:TPSR_Ref TObjectID="22782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -1228.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22782"/>
     <cge:Term_Ref ObjectID="32023"/>
    <cge:TPSR_Ref TObjectID="22782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123108" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -1228.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22782"/>
     <cge:Term_Ref ObjectID="32023"/>
    <cge:TPSR_Ref TObjectID="22782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -756.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22787"/>
     <cge:Term_Ref ObjectID="32033"/>
    <cge:TPSR_Ref TObjectID="22787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123131" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -756.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22787"/>
     <cge:Term_Ref ObjectID="32033"/>
    <cge:TPSR_Ref TObjectID="22787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -756.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22787"/>
     <cge:Term_Ref ObjectID="32033"/>
    <cge:TPSR_Ref TObjectID="22787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -762.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34617"/>
     <cge:Term_Ref ObjectID="50408"/>
    <cge:TPSR_Ref TObjectID="34617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -762.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34617"/>
     <cge:Term_Ref ObjectID="50408"/>
    <cge:TPSR_Ref TObjectID="34617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -762.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34617"/>
     <cge:Term_Ref ObjectID="50408"/>
    <cge:TPSR_Ref TObjectID="34617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4003.000000 -493.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22789"/>
     <cge:Term_Ref ObjectID="32037"/>
    <cge:TPSR_Ref TObjectID="22789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123144" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4003.000000 -493.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22789"/>
     <cge:Term_Ref ObjectID="32037"/>
    <cge:TPSR_Ref TObjectID="22789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4003.000000 -493.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22789"/>
     <cge:Term_Ref ObjectID="32037"/>
    <cge:TPSR_Ref TObjectID="22789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4579.000000 -492.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34620"/>
     <cge:Term_Ref ObjectID="50414"/>
    <cge:TPSR_Ref TObjectID="34620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4579.000000 -492.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34620"/>
     <cge:Term_Ref ObjectID="50414"/>
    <cge:TPSR_Ref TObjectID="34620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4579.000000 -492.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34620"/>
     <cge:Term_Ref ObjectID="50414"/>
    <cge:TPSR_Ref TObjectID="34620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -519.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34614"/>
     <cge:Term_Ref ObjectID="50402"/>
    <cge:TPSR_Ref TObjectID="34614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-221362" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -519.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221362" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34614"/>
     <cge:Term_Ref ObjectID="50402"/>
    <cge:TPSR_Ref TObjectID="34614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-221368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -519.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34614"/>
     <cge:Term_Ref ObjectID="50402"/>
    <cge:TPSR_Ref TObjectID="34614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3690.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22758"/>
     <cge:Term_Ref ObjectID="31975"/>
    <cge:TPSR_Ref TObjectID="22758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123077" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3690.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22758"/>
     <cge:Term_Ref ObjectID="31975"/>
    <cge:TPSR_Ref TObjectID="22758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3690.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22758"/>
     <cge:Term_Ref ObjectID="31975"/>
    <cge:TPSR_Ref TObjectID="22758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123082" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3838.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22762"/>
     <cge:Term_Ref ObjectID="31983"/>
    <cge:TPSR_Ref TObjectID="22762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123083" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3838.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123083" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22762"/>
     <cge:Term_Ref ObjectID="31983"/>
    <cge:TPSR_Ref TObjectID="22762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123078" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3838.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123078" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22762"/>
     <cge:Term_Ref ObjectID="31983"/>
    <cge:TPSR_Ref TObjectID="22762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22765"/>
     <cge:Term_Ref ObjectID="31989"/>
    <cge:TPSR_Ref TObjectID="22765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22765"/>
     <cge:Term_Ref ObjectID="31989"/>
    <cge:TPSR_Ref TObjectID="22765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22765"/>
     <cge:Term_Ref ObjectID="31989"/>
    <cge:TPSR_Ref TObjectID="22765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22779"/>
     <cge:Term_Ref ObjectID="32017"/>
    <cge:TPSR_Ref TObjectID="22779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22779"/>
     <cge:Term_Ref ObjectID="32017"/>
    <cge:TPSR_Ref TObjectID="22779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22779"/>
     <cge:Term_Ref ObjectID="32017"/>
    <cge:TPSR_Ref TObjectID="22779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4376.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22776"/>
     <cge:Term_Ref ObjectID="32011"/>
    <cge:TPSR_Ref TObjectID="22776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4376.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22776"/>
     <cge:Term_Ref ObjectID="32011"/>
    <cge:TPSR_Ref TObjectID="22776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123096" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4376.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22776"/>
     <cge:Term_Ref ObjectID="32011"/>
    <cge:TPSR_Ref TObjectID="22776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22773"/>
     <cge:Term_Ref ObjectID="32005"/>
    <cge:TPSR_Ref TObjectID="22773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22773"/>
     <cge:Term_Ref ObjectID="32005"/>
    <cge:TPSR_Ref TObjectID="22773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123090" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123090" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22773"/>
     <cge:Term_Ref ObjectID="32005"/>
    <cge:TPSR_Ref TObjectID="22773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123175" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22806"/>
     <cge:Term_Ref ObjectID="32071"/>
    <cge:TPSR_Ref TObjectID="22806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22806"/>
     <cge:Term_Ref ObjectID="32071"/>
    <cge:TPSR_Ref TObjectID="22806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123171" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22806"/>
     <cge:Term_Ref ObjectID="32071"/>
    <cge:TPSR_Ref TObjectID="22806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-123181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22770"/>
     <cge:Term_Ref ObjectID="31999"/>
    <cge:TPSR_Ref TObjectID="22770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123182" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22770"/>
     <cge:Term_Ref ObjectID="31999"/>
    <cge:TPSR_Ref TObjectID="22770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123177" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 39.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22770"/>
     <cge:Term_Ref ObjectID="31999"/>
    <cge:TPSR_Ref TObjectID="22770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-221372" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5011.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221372" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34610"/>
     <cge:Term_Ref ObjectID="50396"/>
    <cge:TPSR_Ref TObjectID="34610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-221374" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5011.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221374" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34610"/>
     <cge:Term_Ref ObjectID="50396"/>
    <cge:TPSR_Ref TObjectID="34610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-123121" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22785"/>
     <cge:Term_Ref ObjectID="32029"/>
    <cge:TPSR_Ref TObjectID="22785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-123115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22785"/>
     <cge:Term_Ref ObjectID="32029"/>
    <cge:TPSR_Ref TObjectID="22785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-221347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -597.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221347" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34623"/>
     <cge:Term_Ref ObjectID="50423"/>
    <cge:TPSR_Ref TObjectID="34623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-221345" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -597.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221345" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34623"/>
     <cge:Term_Ref ObjectID="50423"/>
    <cge:TPSR_Ref TObjectID="34623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-125811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -618.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22809"/>
     <cge:Term_Ref ObjectID="32077"/>
    <cge:TPSR_Ref TObjectID="22809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-123148" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -618.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="123148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22809"/>
     <cge:Term_Ref ObjectID="32077"/>
    <cge:TPSR_Ref TObjectID="22809"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="55" qtmmishow="hidden" width="202" x="3165" y="-1117"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="93" qtmmishow="hidden" width="101" x="3093" y="-1146"/></g>
   <g href="35kV猫街变35kV大猫高线371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="30" x="4199" y="-932"/></g>
   <g href="cx_配调_配网接线图35_武定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3382" y="-1092"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3382" y="-1127"/></g>
   <g href="35kV猫街变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="69" x="4545" y="-652"/></g>
   <g href="AVC猫街站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3452" y="-1039"/></g>
   <g href="35kV猫街变10kV1号电容器组071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="3579" y="-309"/></g>
   <g href="35kV猫街变10kV猫街镇线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="3725" y="-306"/></g>
   <g href="35kV猫街变10kV咪三咱线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4011" y="-303"/></g>
   <g href="35kV猫街变10kV收费站专线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4155" y="-303"/></g>
   <g href="35kV猫街变10kV迤纳厂线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="3872" y="-309"/></g>
   <g href="35kV猫街变10kV龙庆关线077间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4552" y="-304"/></g>
   <g href="35kV猫街变10kV三家村线076间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4410" y="-302"/></g>
   <g href="35kV猫街变10kV恒雄专线078间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4698" y="-304"/></g>
   <g href="35kV猫街变10kV万翔专线079间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4842" y="-301"/></g>
   <g href="35kV猫街变10kV2号电容器组081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="5042" y="-308"/></g>
   <g href="35kV猫街变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="71" x="3094" y="-704"/></g>
   <g href="35kV猫街变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4257" y="-476"/></g>
   <g href="35kV猫街变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="4110" y="-669"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1db1b80" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3856.192982 920.851351) translate(0,10)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1db32a0" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3855.192982 936.331081) translate(0,10)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1db39d0" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3856.192982 905.033784) translate(0,10)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1db3f30" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3845.000000 889.216216) translate(0,10)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1db41b0" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3866.385965 873.736486) translate(0,10)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1db4dc0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4213.803279 1231.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1db61b0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4204.000000 1216.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1db7260" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4224.098361 1200.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1db9d30" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3646.803279 -37.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1db9f60" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3637.000000 -52.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1dba1a0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3657.098361 -68.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1dbdd40" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3492.000000 -50.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1dbdfa0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3512.098361 -66.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1dbe2d0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4950.000000 -50.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1dbe530" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4970.098361 -66.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1dbede0" transform="matrix(1.135101 -0.000000 0.000000 -1.598309 4504.056818 582.025370) translate(0,10)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1dbfa80" transform="matrix(1.135101 -0.000000 0.000000 -1.598309 4504.056818 600.000000) translate(0,10)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1dc04b0" transform="matrix(1.135101 -0.000000 0.000000 -1.598309 4095.056818 604.025370) translate(0,10)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1dc0750" transform="matrix(1.135101 -0.000000 0.000000 -1.598309 4095.056818 622.000000) translate(0,10)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d0eed0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4126.803279 759.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d0f120" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4117.000000 744.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d0f360" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4137.098361 728.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d0f690" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4534.803279 764.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d0f8f0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4525.000000 749.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d0fb30" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4545.098361 733.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d0fe60" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3791.803279 -33.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d100c0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3782.000000 -48.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d10300" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3802.098361 -64.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d10630" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4073.803279 -36.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d10890" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4064.000000 -51.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d10ad0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4084.098361 -67.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d10e00" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4330.803279 -36.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d11060" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4321.000000 -51.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d112a0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4341.098361 -67.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d115d0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4477.803279 -36.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d11830" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4468.000000 -51.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d11a70" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4488.098361 -67.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d11da0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4623.803279 -38.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d12000" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4614.000000 -53.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d12240" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4634.098361 -69.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d12570" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4767.803279 -38.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d127d0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4758.000000 -53.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d12a10" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4778.098361 -69.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d12d40" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3955.803279 496.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d12fa0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3946.000000 481.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d131e0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3966.098361 465.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d13510" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4216.803279 520.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d13770" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4207.000000 505.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d139b0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4227.098361 489.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d13ce0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4531.803279 492.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d13f40" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4522.000000 477.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1d14180" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4542.098361 461.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_MJ.WD_MJ.WD_MJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3439,-385 4212,-385 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22813" ObjectName="BS-WD_MJ.WD_MJ.WD_MJ_9IM"/>
    <cge:TPSR_Ref TObjectID="22813"/></metadata>
   <polyline fill="none" opacity="0" points="3439,-385 4212,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_MJ.WD_MJ.WD_MJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-829 4732,-829 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22812" ObjectName="BS-WD_MJ.WD_MJ.WD_MJ_3IM"/>
    <cge:TPSR_Ref TObjectID="22812"/></metadata>
   <polyline fill="none" opacity="0" points="3917,-829 4732,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_MJ.WD_MJ.WD_MJ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4315,-385 5088,-385 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34608" ObjectName="BS-WD_MJ.WD_MJ.WD_MJ_9IIM"/>
    <cge:TPSR_Ref TObjectID="34608"/></metadata>
   <polyline fill="none" opacity="0" points="4315,-385 5088,-385 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="22" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3133.000000 -1027.513514) translate(0,18)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-222204" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3208.538462 -857.966362) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="222204" ObjectName="WD_MJ:WD_MJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-222205" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3210.538462 -812.966362) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="222205" ObjectName="WD_MJ:WD_MJ_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-222204" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3207.538462 -943.966362) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="222204" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-222204" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3208.538462 -899.966362) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="222204" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="PieSliceF_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" points="5058,-894 " stroke="rgb(255,255,255)"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="55" qtmmishow="hidden" width="202" x="3165" y="-1117"/>
    </a>
   <metadata/><rect fill="white" height="55" opacity="0" stroke="white" transform="" width="202" x="3165" y="-1117"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="93" qtmmishow="hidden" width="101" x="3093" y="-1146"/>
    </a>
   <metadata/><rect fill="white" height="93" opacity="0" stroke="white" transform="" width="101" x="3093" y="-1146"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="30" x="4199" y="-932"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="30" x="4199" y="-932"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3382" y="-1092"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3382" y="-1092"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3382" y="-1127"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3382" y="-1127"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="69" x="4545" y="-652"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="69" x="4545" y="-652"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3452" y="-1039"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3452" y="-1039"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="3579" y="-309"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="3579" y="-309"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="3725" y="-306"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="3725" y="-306"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4011" y="-303"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4011" y="-303"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4155" y="-303"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4155" y="-303"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="3872" y="-309"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="3872" y="-309"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4552" y="-304"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4552" y="-304"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4410" y="-302"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4410" y="-302"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4698" y="-304"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4698" y="-304"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4842" y="-301"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4842" y="-301"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="5042" y="-308"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="5042" y="-308"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="71" x="3094" y="-704"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="71" x="3094" y="-704"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4257" y="-476"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4257" y="-476"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="4110" y="-669"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="4110" y="-669"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 49.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 49.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_MJ.WD_MJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="50422"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 -598.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 -598.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="34623" ObjectName="TF-WD_MJ.WD_MJ_2T"/>
    <cge:TPSR_Ref TObjectID="34623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_MJ.WD_MJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="32079"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.954545 -0.000000 0.000000 -0.882353 4025.000000 -616.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.954545 -0.000000 0.000000 -0.882353 4025.000000 -616.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22809" ObjectName="TF-WD_MJ.WD_MJ_1T"/>
    <cge:TPSR_Ref TObjectID="22809"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_MJ"/>
</svg>