<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-249" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3097 -1215 2440 1367">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="generator:shape3">
    <polyline arcFlag="1" points="26,-19 26,-18 26,-17 26,-16 25,-15 25,-14 24,-13 24,-13 23,-12 22,-12 21,-11 20,-11 19,-11 18,-10 17,-11 16,-11 15,-11 14,-12 14,-12 13,-13 12,-13 12,-14 11,-15 11,-16 11,-17 10,-18 11,-19 " stroke-width="0.06"/>
    <circle cx="25" cy="-20" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="41,-19 42,-20 41,-20 41,-21 41,-22 40,-23 40,-24 39,-25 38,-25 38,-26 37,-26 36,-27 35,-27 34,-27 33,-27 32,-27 31,-26 30,-26 29,-25 28,-25 28,-24 27,-23 27,-22 26,-21 26,-20 26,-20 26,-19 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape182">
    <polyline arcFlag="1" points="12,29 10,29 9,29 8,29 7,28 6,28 5,27 4,26 3,25 2,24 1,22 1,21 1,19 0,18 0,16 1,15 1,13 1,12 2,10 3,9 4,8 5,7 6,6 7,5 8,5 9,5 10,5 12,5 " stroke-width="0.402481"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape22">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="55" x2="60" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="46" x2="51" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="38" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="30" x2="35" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="86" x2="72" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="18" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape180">
    <polyline arcFlag="1" points="5,12 5,10 5,9 5,8 6,7 6,6 7,5 8,4 9,3 10,2 12,1 13,1 15,1 16,0 18,0 19,1 21,1 22,1 24,2 25,3 26,4 27,5 28,6 29,7 29,8 29,9 29,10 29,12 " stroke-width="0.402481"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape31_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="2" x2="26" y1="9" y2="33"/>
   </symbol>
   <symbol id="switch2:shape31_1">
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="14" y1="37" y2="5"/>
   </symbol>
   <symbol id="switch2:shape31-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="2" x2="26" y1="9" y2="33"/>
   </symbol>
   <symbol id="switch2:shape31-UnNor2">
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="14" y1="37" y2="5"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape17_0">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape17_1">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="voltageTransformer:shape26">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="74" y2="9"/>
    <rect height="27" stroke-width="0.416667" width="14" x="26" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="29" y2="63"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="76" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="82" y2="82"/>
    <circle cx="31" cy="81" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="22" cy="92" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="38" cy="93" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="19" y1="92" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="24" x2="21" y1="90" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="93" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="30" x2="28" y1="81" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="33" x2="30" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="30" x2="30" y1="82" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="42" y1="96" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="37" y1="96" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="39" y1="96" y2="91"/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="voltageTransformer:shape122">
    <ellipse cx="27" cy="15" rx="7" ry="7.5" stroke-width="0.726474"/>
    <ellipse cx="7" cy="15" rx="7" ry="7.5" stroke-width="0.726474"/>
    <ellipse cx="17" cy="22" rx="7" ry="7.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="27" x2="27" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="24" x2="27" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="30" x2="27" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="17" x2="17" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="14" x2="17" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="20" x2="17" y1="9" y2="7"/>
    <ellipse cx="17" cy="8" rx="7" ry="7.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="4" x2="8" y1="16" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="4" x2="8" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="8" x2="8" y1="13" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="17" x2="17" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="14" x2="17" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="20" x2="17" y1="25" y2="23"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2315fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2317130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2317b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2318310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2319330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2319fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_231ab70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_231b570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dfa180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dfa180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_231e550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_231e550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23204d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23204d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_23214f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23230f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2323ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2324aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23253e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2326450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23270e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23279a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2328160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23295c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2329d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_232a6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_232b070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_232c4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_232d0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_232e0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_232ed60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_233d560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_233dd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2330f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2332530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1377" width="2450" x="3092" y="-1220"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4843" x2="4889" y1="-928" y2="-909"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5044" x2="5084" y1="-928" y2="-908"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5422" x2="5469" y1="-929" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3613" x2="3651" y1="-66" y2="-66"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3651" x2="3651" y1="-66" y2="-31"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3945" x2="3982" y1="-65" y2="-65"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3945" x2="3945" y1="-67" y2="-63"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3982" x2="3982" y1="-65" y2="-30"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3742" x2="3778" y1="-66" y2="-66"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3742" x2="3742" y1="-68" y2="-64"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3778" x2="3778" y1="-66" y2="-31"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3613" x2="3613" y1="-68" y2="-64"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5204" x2="5204" y1="-1132" y2="-1169"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-597"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3098" y="-585"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="36" stroke="rgb(60,120,255)" stroke-width="1" width="65" x="5212" y="-687"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-191344">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 -730.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28957" ObjectName="SW-CX_HCP.CX_HCP_301XC"/>
     <cge:Meas_Ref ObjectId="191344"/>
    <cge:TPSR_Ref TObjectID="28957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191344">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 -654.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28958" ObjectName="SW-CX_HCP.CX_HCP_301XC1"/>
     <cge:Meas_Ref ObjectId="191344"/>
    <cge:TPSR_Ref TObjectID="28958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191381">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -387.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28960" ObjectName="SW-CX_HCP.CX_HCP_001XC"/>
     <cge:Meas_Ref ObjectId="191381"/>
    <cge:TPSR_Ref TObjectID="28960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191381">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -311.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28961" ObjectName="SW-CX_HCP.CX_HCP_001XC1"/>
     <cge:Meas_Ref ObjectId="191381"/>
    <cge:TPSR_Ref TObjectID="28961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191053">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.000000 -260.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28917" ObjectName="SW-CX_HCP.CX_HCP_061XC"/>
     <cge:Meas_Ref ObjectId="191053"/>
    <cge:TPSR_Ref TObjectID="28917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191053">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.000000 -184.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28918" ObjectName="SW-CX_HCP.CX_HCP_061XC1"/>
     <cge:Meas_Ref ObjectId="191053"/>
    <cge:TPSR_Ref TObjectID="28918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191082">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4073.000000 -260.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28926" ObjectName="SW-CX_HCP.CX_HCP_062XC"/>
     <cge:Meas_Ref ObjectId="191082"/>
    <cge:TPSR_Ref TObjectID="28926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191082">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4073.000000 -184.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28927" ObjectName="SW-CX_HCP.CX_HCP_062XC1"/>
     <cge:Meas_Ref ObjectId="191082"/>
    <cge:TPSR_Ref TObjectID="28927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -261.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28930" ObjectName="SW-CX_HCP.CX_HCP_063XC"/>
     <cge:Meas_Ref ObjectId="191107"/>
    <cge:TPSR_Ref TObjectID="28930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -185.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28931" ObjectName="SW-CX_HCP.CX_HCP_063XC1"/>
     <cge:Meas_Ref ObjectId="191107"/>
    <cge:TPSR_Ref TObjectID="28931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4302.000000 -260.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28934" ObjectName="SW-CX_HCP.CX_HCP_012XC"/>
     <cge:Meas_Ref ObjectId="191138"/>
    <cge:TPSR_Ref TObjectID="28934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4302.000000 -184.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28935" ObjectName="SW-CX_HCP.CX_HCP_012XC1"/>
     <cge:Meas_Ref ObjectId="191138"/>
    <cge:TPSR_Ref TObjectID="28935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191083">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28928" ObjectName="SW-CX_HCP.CX_HCP_06267SW"/>
     <cge:Meas_Ref ObjectId="191083"/>
    <cge:TPSR_Ref TObjectID="28928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191108">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -124.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28932" ObjectName="SW-CX_HCP.CX_HCP_06367SW"/>
     <cge:Meas_Ref ObjectId="191108"/>
    <cge:TPSR_Ref TObjectID="28932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191253">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.000000 -260.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28950" ObjectName="SW-CX_HCP.CX_HCP_064XC"/>
     <cge:Meas_Ref ObjectId="191253"/>
    <cge:TPSR_Ref TObjectID="28950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191253">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.000000 -184.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28951" ObjectName="SW-CX_HCP.CX_HCP_064XC1"/>
     <cge:Meas_Ref ObjectId="191253"/>
    <cge:TPSR_Ref TObjectID="28951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191254">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 -122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28969" ObjectName="SW-CX_HCP.CX_HCP_06467SW"/>
     <cge:Meas_Ref ObjectId="191254"/>
    <cge:TPSR_Ref TObjectID="28969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191191">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 -260.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28942" ObjectName="SW-CX_HCP.CX_HCP_065XC"/>
     <cge:Meas_Ref ObjectId="191191"/>
    <cge:TPSR_Ref TObjectID="28942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191191">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 -184.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28943" ObjectName="SW-CX_HCP.CX_HCP_065XC1"/>
     <cge:Meas_Ref ObjectId="191191"/>
    <cge:TPSR_Ref TObjectID="28943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28944" ObjectName="SW-CX_HCP.CX_HCP_06567SW"/>
     <cge:Meas_Ref ObjectId="191192"/>
    <cge:TPSR_Ref TObjectID="28944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191163">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -261.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28938" ObjectName="SW-CX_HCP.CX_HCP_066XC"/>
     <cge:Meas_Ref ObjectId="191163"/>
    <cge:TPSR_Ref TObjectID="28938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191163">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -185.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28939" ObjectName="SW-CX_HCP.CX_HCP_066XC1"/>
     <cge:Meas_Ref ObjectId="191163"/>
    <cge:TPSR_Ref TObjectID="28939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191164">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 -123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28940" ObjectName="SW-CX_HCP.CX_HCP_06667SW"/>
     <cge:Meas_Ref ObjectId="191164"/>
    <cge:TPSR_Ref TObjectID="28940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -258.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28954" ObjectName="SW-CX_HCP.CX_HCP_067XC"/>
     <cge:Meas_Ref ObjectId="191282"/>
    <cge:TPSR_Ref TObjectID="28954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -182.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28955" ObjectName="SW-CX_HCP.CX_HCP_067XC1"/>
     <cge:Meas_Ref ObjectId="191282"/>
    <cge:TPSR_Ref TObjectID="28955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191604">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28952" ObjectName="SW-CX_HCP.CX_HCP_06767SW"/>
     <cge:Meas_Ref ObjectId="191604"/>
    <cge:TPSR_Ref TObjectID="28952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191215">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -261.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28946" ObjectName="SW-CX_HCP.CX_HCP_068XC"/>
     <cge:Meas_Ref ObjectId="191215"/>
    <cge:TPSR_Ref TObjectID="28946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191215">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -185.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28947" ObjectName="SW-CX_HCP.CX_HCP_068XC1"/>
     <cge:Meas_Ref ObjectId="191215"/>
    <cge:TPSR_Ref TObjectID="28947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191216">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 -123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28948" ObjectName="SW-CX_HCP.CX_HCP_06867SW"/>
     <cge:Meas_Ref ObjectId="191216"/>
    <cge:TPSR_Ref TObjectID="28948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.978261 3891.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28992" ObjectName="SW-CX_HCP.CX_HCP_0901SW"/>
     <cge:Meas_Ref ObjectId="191806"/>
    <cge:TPSR_Ref TObjectID="28992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191807">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4655.000000 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28993" ObjectName="SW-CX_HCP.CX_HCP_0902SW"/>
     <cge:Meas_Ref ObjectId="191807"/>
    <cge:TPSR_Ref TObjectID="28993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -893.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28963" ObjectName="SW-CX_HCP.CX_HCP_361XC"/>
     <cge:Meas_Ref ObjectId="191579"/>
    <cge:TPSR_Ref TObjectID="28963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -817.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28964" ObjectName="SW-CX_HCP.CX_HCP_361XC1"/>
     <cge:Meas_Ref ObjectId="191579"/>
    <cge:TPSR_Ref TObjectID="28964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191584">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 -905.454545)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28966" ObjectName="SW-CX_HCP.CX_HCP_3613SW"/>
     <cge:Meas_Ref ObjectId="191584"/>
    <cge:TPSR_Ref TObjectID="28966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191586">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -746.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28967" ObjectName="SW-CX_HCP.CX_HCP_3901SW"/>
     <cge:Meas_Ref ObjectId="191586"/>
    <cge:TPSR_Ref TObjectID="28967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4791.000000 -642.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4791.000000 -566.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 -643.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 -567.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 -643.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 -567.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5074.000000 -642.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5074.000000 -566.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5376.000000 -642.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5376.000000 -566.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.000000 -806.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.000000 -730.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5002.000000 -804.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5002.000000 -729.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5194.000000 -806.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5194.000000 -731.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5380.000000 -805.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5381.000000 -729.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5194.000000 -979.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5194.000000 -903.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191057">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 -5.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28920" ObjectName="SW-CX_HCP.CX_HCP_06137SW"/>
     <cge:Meas_Ref ObjectId="191057"/>
    <cge:TPSR_Ref TObjectID="28920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191063">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3955.000000 -4.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28924" ObjectName="SW-CX_HCP.CX_HCP_06117SW"/>
     <cge:Meas_Ref ObjectId="191063"/>
    <cge:TPSR_Ref TObjectID="28924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -5.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28922" ObjectName="SW-CX_HCP.CX_HCP_06127SW"/>
     <cge:Meas_Ref ObjectId="191060"/>
    <cge:TPSR_Ref TObjectID="28922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191056">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3587.000000 -45.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28919" ObjectName="SW-CX_HCP.CX_HCP_0613SW"/>
     <cge:Meas_Ref ObjectId="191056"/>
    <cge:TPSR_Ref TObjectID="28919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -46.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28921" ObjectName="SW-CX_HCP.CX_HCP_0612SW"/>
     <cge:Meas_Ref ObjectId="191059"/>
    <cge:TPSR_Ref TObjectID="28921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191062">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 -44.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28923" ObjectName="SW-CX_HCP.CX_HCP_0611SW"/>
     <cge:Meas_Ref ObjectId="191062"/>
    <cge:TPSR_Ref TObjectID="28923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191582">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -959.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28965" ObjectName="SW-CX_HCP.CX_HCP_3616SW"/>
     <cge:Meas_Ref ObjectId="191582"/>
    <cge:TPSR_Ref TObjectID="28965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5080.000000 -221.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191140">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 -214.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28936" ObjectName="SW-CX_HCP.CX_HCP_0122SW"/>
     <cge:Meas_Ref ObjectId="191140"/>
    <cge:TPSR_Ref TObjectID="28936"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_HCP.P1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -18.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43429" ObjectName="SM-CX_HCP.P1"/>
    <cge:TPSR_Ref TObjectID="43429"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LT" endPointId="0" endStationName="CX_HCP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_Longhuang" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4068,-1132 4068,-1186 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48672" ObjectName="AC-35kV.LN_Longhuang"/>
    <cge:TPSR_Ref TObjectID="48672_SS-249"/></metadata>
   <polyline fill="none" opacity="0" points="4068,-1132 4068,-1186 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HCP.CX_HCP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="41297"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -490.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -490.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28968" ObjectName="TF-CX_HCP.CX_HCP_1T"/>
    <cge:TPSR_Ref TObjectID="28968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -9.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -9.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -8.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -8.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4848.000000 -5.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4848.000000 -5.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 77.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 77.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 -912.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 -912.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 -910.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 -910.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5179.000000 -1018.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5179.000000 -1018.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5365.000000 -911.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5365.000000 -911.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_380e250">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 -587.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37b4d00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 -421.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3861580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -114.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32f6850">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4078.000000 -114.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_348e450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -115.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3302b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4097.371238 -117.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_380bf80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.371238 -119.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_386ed40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.371238 -117.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b1cc00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4501.000000 -115.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3605030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4520.371238 -117.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37719c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.371238 -117.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2308730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 -116.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37cd2a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4768.371238 -118.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a8ec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4868.000000 -113.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33be690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.371238 -115.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38328c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5010.371238 -118.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b31740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -412.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_378b460">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.000000 -647.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3797fd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -927.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_387f7a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -1058.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34774d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.371238 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_391a730">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -684.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345dce0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4283.000000 -672.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37b0610">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3923.000000 20.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_378fdb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -704.000000)" xlink:href="#lightningRod:shape182"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a53b10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5072.000000 -703.000000)" xlink:href="#lightningRod:shape182"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37f1680">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5457.000000 -705.000000)" xlink:href="#lightningRod:shape182"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3883700">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.000000 -1058.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36c5900">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5002.000000 -1058.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3840c50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5194.000000 -1113.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39f76e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5380.000000 -1059.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_388dbb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5199.000000 -836.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37f8240">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5110.000000 -544.000000)" xlink:href="#lightningRod:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36c6f70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5112.000000 -504.000000)" xlink:href="#lightningRod:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37e1ed0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4980.000000 -501.000000)" xlink:href="#lightningRod:shape180"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_365a710">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 16.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a9f490">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3989.000000 -1039.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-190863" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -903.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28912"/>
     <cge:Term_Ref ObjectID="41186"/>
    <cge:TPSR_Ref TObjectID="28912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-190864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -903.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28912"/>
     <cge:Term_Ref ObjectID="41186"/>
    <cge:TPSR_Ref TObjectID="28912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-190865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -903.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28912"/>
     <cge:Term_Ref ObjectID="41186"/>
    <cge:TPSR_Ref TObjectID="28912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-190868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -903.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28912"/>
     <cge:Term_Ref ObjectID="41186"/>
    <cge:TPSR_Ref TObjectID="28912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-191165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -903.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28912"/>
     <cge:Term_Ref ObjectID="41186"/>
    <cge:TPSR_Ref TObjectID="28912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-191808" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -903.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191808" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28912"/>
     <cge:Term_Ref ObjectID="41186"/>
    <cge:TPSR_Ref TObjectID="28912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-190869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -413.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28913"/>
     <cge:Term_Ref ObjectID="41187"/>
    <cge:TPSR_Ref TObjectID="28913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-190870" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -413.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28913"/>
     <cge:Term_Ref ObjectID="41187"/>
    <cge:TPSR_Ref TObjectID="28913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-190871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -413.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28913"/>
     <cge:Term_Ref ObjectID="41187"/>
    <cge:TPSR_Ref TObjectID="28913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-190874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -413.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28913"/>
     <cge:Term_Ref ObjectID="41187"/>
    <cge:TPSR_Ref TObjectID="28913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-191166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -413.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28913"/>
     <cge:Term_Ref ObjectID="41187"/>
    <cge:TPSR_Ref TObjectID="28913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-191809" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -413.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191809" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28913"/>
     <cge:Term_Ref ObjectID="41187"/>
    <cge:TPSR_Ref TObjectID="28913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 92.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28916"/>
     <cge:Term_Ref ObjectID="41191"/>
    <cge:TPSR_Ref TObjectID="28916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 92.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28916"/>
     <cge:Term_Ref ObjectID="41191"/>
    <cge:TPSR_Ref TObjectID="28916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 92.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28916"/>
     <cge:Term_Ref ObjectID="41191"/>
    <cge:TPSR_Ref TObjectID="28916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-191811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 92.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28916"/>
     <cge:Term_Ref ObjectID="41191"/>
    <cge:TPSR_Ref TObjectID="28916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28925"/>
     <cge:Term_Ref ObjectID="41209"/>
    <cge:TPSR_Ref TObjectID="28925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28925"/>
     <cge:Term_Ref ObjectID="41209"/>
    <cge:TPSR_Ref TObjectID="28925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28925"/>
     <cge:Term_Ref ObjectID="41209"/>
    <cge:TPSR_Ref TObjectID="28925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28929"/>
     <cge:Term_Ref ObjectID="41217"/>
    <cge:TPSR_Ref TObjectID="28929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28929"/>
     <cge:Term_Ref ObjectID="41217"/>
    <cge:TPSR_Ref TObjectID="28929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28929"/>
     <cge:Term_Ref ObjectID="41217"/>
    <cge:TPSR_Ref TObjectID="28929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -154.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28933"/>
     <cge:Term_Ref ObjectID="41225"/>
    <cge:TPSR_Ref TObjectID="28933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -154.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28933"/>
     <cge:Term_Ref ObjectID="41225"/>
    <cge:TPSR_Ref TObjectID="28933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -154.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28933"/>
     <cge:Term_Ref ObjectID="41225"/>
    <cge:TPSR_Ref TObjectID="28933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28949"/>
     <cge:Term_Ref ObjectID="41257"/>
    <cge:TPSR_Ref TObjectID="28949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28949"/>
     <cge:Term_Ref ObjectID="41257"/>
    <cge:TPSR_Ref TObjectID="28949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28949"/>
     <cge:Term_Ref ObjectID="41257"/>
    <cge:TPSR_Ref TObjectID="28949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4618.000000 14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28941"/>
     <cge:Term_Ref ObjectID="41241"/>
    <cge:TPSR_Ref TObjectID="28941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4618.000000 14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28941"/>
     <cge:Term_Ref ObjectID="41241"/>
    <cge:TPSR_Ref TObjectID="28941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4618.000000 14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28941"/>
     <cge:Term_Ref ObjectID="41241"/>
    <cge:TPSR_Ref TObjectID="28941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28937"/>
     <cge:Term_Ref ObjectID="41233"/>
    <cge:TPSR_Ref TObjectID="28937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28937"/>
     <cge:Term_Ref ObjectID="41233"/>
    <cge:TPSR_Ref TObjectID="28937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28937"/>
     <cge:Term_Ref ObjectID="41233"/>
    <cge:TPSR_Ref TObjectID="28937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28953"/>
     <cge:Term_Ref ObjectID="41265"/>
    <cge:TPSR_Ref TObjectID="28953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28953"/>
     <cge:Term_Ref ObjectID="41265"/>
    <cge:TPSR_Ref TObjectID="28953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28953"/>
     <cge:Term_Ref ObjectID="41265"/>
    <cge:TPSR_Ref TObjectID="28953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4984.000000 14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28945"/>
     <cge:Term_Ref ObjectID="41249"/>
    <cge:TPSR_Ref TObjectID="28945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4984.000000 14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28945"/>
     <cge:Term_Ref ObjectID="41249"/>
    <cge:TPSR_Ref TObjectID="28945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4984.000000 14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28945"/>
     <cge:Term_Ref ObjectID="41249"/>
    <cge:TPSR_Ref TObjectID="28945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190962" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -891.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28962"/>
     <cge:Term_Ref ObjectID="41283"/>
    <cge:TPSR_Ref TObjectID="28962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190963" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -891.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190963" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28962"/>
     <cge:Term_Ref ObjectID="41283"/>
    <cge:TPSR_Ref TObjectID="28962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -891.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28962"/>
     <cge:Term_Ref ObjectID="41283"/>
    <cge:TPSR_Ref TObjectID="28962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -740.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28956"/>
     <cge:Term_Ref ObjectID="41271"/>
    <cge:TPSR_Ref TObjectID="28956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -740.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28956"/>
     <cge:Term_Ref ObjectID="41271"/>
    <cge:TPSR_Ref TObjectID="28956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -740.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28956"/>
     <cge:Term_Ref ObjectID="41271"/>
    <cge:TPSR_Ref TObjectID="28956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-190956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4142.000000 -386.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28959"/>
     <cge:Term_Ref ObjectID="41277"/>
    <cge:TPSR_Ref TObjectID="28959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-190957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4142.000000 -386.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28959"/>
     <cge:Term_Ref ObjectID="41277"/>
    <cge:TPSR_Ref TObjectID="28959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-190953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4142.000000 -386.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28959"/>
     <cge:Term_Ref ObjectID="41277"/>
    <cge:TPSR_Ref TObjectID="28959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-190875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -406.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28914"/>
     <cge:Term_Ref ObjectID="41188"/>
    <cge:TPSR_Ref TObjectID="28914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-190876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -406.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28914"/>
     <cge:Term_Ref ObjectID="41188"/>
    <cge:TPSR_Ref TObjectID="28914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-190877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -406.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28914"/>
     <cge:Term_Ref ObjectID="41188"/>
    <cge:TPSR_Ref TObjectID="28914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-190880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -406.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28914"/>
     <cge:Term_Ref ObjectID="41188"/>
    <cge:TPSR_Ref TObjectID="28914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-191167" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -406.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191167" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28914"/>
     <cge:Term_Ref ObjectID="41188"/>
    <cge:TPSR_Ref TObjectID="28914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-191810" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -406.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191810" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28914"/>
     <cge:Term_Ref ObjectID="41188"/>
    <cge:TPSR_Ref TObjectID="28914"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1195"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1178"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1195"/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4992,-122 5001,-122 4996,-116 4992,-122 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-129 4635,-129 4630,-123 4626,-129 " stroke="rgb(60,120,255)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2385860">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 -372.000000)" xlink:href="#voltageTransformer:shape26"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331f730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4631.000000 -374.000000)" xlink:href="#voltageTransformer:shape26"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37f3a70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4024.000000 -830.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3861750">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 -644.000000)" xlink:href="#voltageTransformer:shape122"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_375cf30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 46.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3658720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3584.000000 56.000000)" xlink:href="#voltageTransformer:shape122"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aa01c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4131.000000 -989.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 -24.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 -22.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3906f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-789 4002,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28912@0" ObjectIDZND0="28957@0" Pin0InfoVect0LinkObjId="SW-191344_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a4aaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-789 4002,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39eb9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-737 4002,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28957@1" ObjectIDZND0="28956@1" Pin0InfoVect0LinkObjId="SW-191343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-737 4002,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d9a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-694 4002,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28956@0" ObjectIDZND0="28958@1" Pin0InfoVect0LinkObjId="SW-191344_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-694 4002,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b39840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-592 4002,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_380e250@1" ObjectIDZND0="28968@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_380e250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-592 4002,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3771230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-495 4001,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="28968@0" ObjectIDZND0="g_37b4d00@0" Pin0InfoVect0LinkObjId="g_37b4d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b39840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-495 4001,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a7c520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-394 4001,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28960@1" ObjectIDZND0="28959@1" Pin0InfoVect0LinkObjId="SW-191380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-394 4001,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_387d8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-351 4001,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28959@0" ObjectIDZND0="28961@1" Pin0InfoVect0LinkObjId="SW-191381_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-351 4001,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3791f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-318 4001,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28961@0" ObjectIDZND0="28913@0" Pin0InfoVect0LinkObjId="g_38c4db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-318 4001,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38cf2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-299 3838,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28913@0" ObjectIDZND0="28917@0" Pin0InfoVect0LinkObjId="SW-191053_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3791f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-299 3838,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ef950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-267 3838,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28917@1" ObjectIDZND0="28916@1" Pin0InfoVect0LinkObjId="SW-191052_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191053_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-267 3838,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a75060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-224 3838,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28916@0" ObjectIDZND0="28918@1" Pin0InfoVect0LinkObjId="SW-191053_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191052_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-224 3838,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a755e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-191 3838,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28918@0" ObjectIDZND0="g_3861580@0" Pin0InfoVect0LinkObjId="g_3861580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191053_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-191 3838,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25860b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-299 4083,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28913@0" ObjectIDZND0="28926@0" Pin0InfoVect0LinkObjId="SW-191082_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3791f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-299 4083,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b89540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-267 4083,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28926@1" ObjectIDZND0="28925@1" Pin0InfoVect0LinkObjId="SW-191081_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191082_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-267 4083,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3abf250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-224 4083,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28925@0" ObjectIDZND0="28927@1" Pin0InfoVect0LinkObjId="SW-191082_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191081_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-224 4083,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_398d2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-119 4083,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_32f6850@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32f6850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-119 4083,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_377da80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-299 4208,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28913@0" ObjectIDZND0="28930@0" Pin0InfoVect0LinkObjId="SW-191107_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3791f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-299 4208,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3388b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-268 4208,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28930@1" ObjectIDZND0="28929@1" Pin0InfoVect0LinkObjId="SW-191106_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191107_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-268 4208,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3230570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-225 4208,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28929@0" ObjectIDZND0="28931@1" Pin0InfoVect0LinkObjId="SW-191107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-225 4208,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a78ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-120 4208,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_348e450@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_348e450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-120 4208,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b891d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4312,-299 4312,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28913@0" ObjectIDZND0="28934@0" Pin0InfoVect0LinkObjId="SW-191138_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3791f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4312,-299 4312,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3861a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4312,-267 4312,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28934@1" ObjectIDZND0="28933@1" Pin0InfoVect0LinkObjId="SW-191137_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191138_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4312,-267 4312,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3c7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4312,-224 4312,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28933@0" ObjectIDZND0="28935@1" Pin0InfoVect0LinkObjId="SW-191138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191137_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4312,-224 4312,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317a8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-181 4104,-181 4104,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28927@x" ObjectIDND1="g_32f6850@0" ObjectIDND2="28928@x" ObjectIDZND0="g_3302b50@0" Pin0InfoVect0LinkObjId="g_3302b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191082_0" Pin1InfoVect1LinkObjId="g_32f6850_0" Pin1InfoVect2LinkObjId="SW-191083_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-181 4104,-181 4104,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e24b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-191 4083,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28927@0" ObjectIDZND0="g_3302b50@0" ObjectIDZND1="g_32f6850@0" ObjectIDZND2="28928@x" Pin0InfoVect0LinkObjId="g_3302b50_0" Pin0InfoVect1LinkObjId="g_32f6850_0" Pin0InfoVect2LinkObjId="SW-191083_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191082_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-191 4083,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c4c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-181 4083,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3302b50@0" ObjectIDND1="28927@x" ObjectIDND2="28928@x" ObjectIDZND0="g_32f6850@0" Pin0InfoVect0LinkObjId="g_32f6850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3302b50_0" Pin1InfoVect1LinkObjId="SW-191082_0" Pin1InfoVect2LinkObjId="SW-191083_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-181 4083,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a10d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-183 4229,-183 4229,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28931@x" ObjectIDND1="g_348e450@0" ObjectIDND2="28932@x" ObjectIDZND0="g_380bf80@0" Pin0InfoVect0LinkObjId="g_380bf80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191107_0" Pin1InfoVect1LinkObjId="g_348e450_0" Pin1InfoVect2LinkObjId="SW-191108_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-183 4229,-183 4229,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b32c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-192 4208,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28931@0" ObjectIDZND0="g_380bf80@0" ObjectIDZND1="g_348e450@0" ObjectIDZND2="28932@x" Pin0InfoVect0LinkObjId="g_380bf80_0" Pin0InfoVect1LinkObjId="g_348e450_0" Pin0InfoVect2LinkObjId="SW-191108_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-192 4208,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a181e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-183 4208,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_380bf80@0" ObjectIDND1="28931@x" ObjectIDND2="28932@x" ObjectIDZND0="g_348e450@0" Pin0InfoVect0LinkObjId="g_348e450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_380bf80_0" Pin1InfoVect1LinkObjId="SW-191107_0" Pin1InfoVect2LinkObjId="SW-191108_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-183 4208,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b367c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4312,-181 4292,-181 4292,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="28935@x" ObjectIDND1="28936@x" ObjectIDZND0="g_386ed40@0" Pin0InfoVect0LinkObjId="g_386ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-191138_0" Pin1InfoVect1LinkObjId="SW-191140_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4312,-181 4292,-181 4292,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37bdb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4312,-191 4312,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="28935@0" ObjectIDZND0="g_386ed40@0" ObjectIDZND1="28936@x" Pin0InfoVect0LinkObjId="g_386ed40_0" Pin0InfoVect1LinkObjId="SW-191140_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4312,-191 4312,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3813660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4061,-163 4061,-181 4083,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="28928@1" ObjectIDZND0="g_3302b50@0" ObjectIDZND1="28927@x" ObjectIDZND2="g_32f6850@0" Pin0InfoVect0LinkObjId="g_3302b50_0" Pin0InfoVect1LinkObjId="SW-191082_0" Pin0InfoVect2LinkObjId="g_32f6850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191083_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4061,-163 4061,-181 4083,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2e170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-165 4186,-183 4208,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="28932@1" ObjectIDZND0="g_380bf80@0" ObjectIDZND1="28931@x" ObjectIDZND2="g_348e450@0" Pin0InfoVect0LinkObjId="g_380bf80_0" Pin0InfoVect1LinkObjId="SW-191107_0" Pin0InfoVect2LinkObjId="g_348e450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191108_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-165 4186,-183 4208,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38cccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4061,-127 4061,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28928@0" ObjectIDZND0="g_380eae0@0" Pin0InfoVect0LinkObjId="g_380eae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191083_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4061,-127 4061,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3952190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-129 4186,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28932@0" ObjectIDZND0="g_3823280@0" Pin0InfoVect0LinkObjId="g_3823280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191108_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-129 4186,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ca9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-299 4506,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28914@0" ObjectIDZND0="28950@0" Pin0InfoVect0LinkObjId="SW-191253_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c7d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-299 4506,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3314c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-267 4506,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28950@1" ObjectIDZND0="28949@1" Pin0InfoVect0LinkObjId="SW-191252_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191253_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-267 4506,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38cd140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-224 4506,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28949@0" ObjectIDZND0="28951@1" Pin0InfoVect0LinkObjId="SW-191253_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191252_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-224 4506,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c2770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-120 4506,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3b1cc00@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b1cc00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-120 4506,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38210d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-181 4527,-181 4527,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28951@x" ObjectIDND1="g_3b1cc00@0" ObjectIDND2="28969@x" ObjectIDZND0="g_3605030@0" Pin0InfoVect0LinkObjId="g_3605030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191253_0" Pin1InfoVect1LinkObjId="g_3b1cc00_0" Pin1InfoVect2LinkObjId="SW-191254_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-181 4527,-181 4527,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343cae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-191 4506,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28951@0" ObjectIDZND0="g_3605030@0" ObjectIDZND1="g_3b1cc00@0" ObjectIDZND2="28969@x" Pin0InfoVect0LinkObjId="g_3605030_0" Pin0InfoVect1LinkObjId="g_3b1cc00_0" Pin0InfoVect2LinkObjId="SW-191254_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191253_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-191 4506,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2fa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-181 4506,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28951@x" ObjectIDND1="g_3605030@0" ObjectIDND2="28969@x" ObjectIDZND0="g_3b1cc00@0" Pin0InfoVect0LinkObjId="g_3b1cc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191253_0" Pin1InfoVect1LinkObjId="g_3605030_0" Pin1InfoVect2LinkObjId="SW-191254_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-181 4506,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b307a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-163 4484,-181 4506,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28969@1" ObjectIDZND0="28951@x" ObjectIDZND1="g_3605030@0" ObjectIDZND2="g_3b1cc00@0" Pin0InfoVect0LinkObjId="SW-191253_0" Pin0InfoVect1LinkObjId="g_3605030_0" Pin0InfoVect2LinkObjId="g_3b1cc00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191254_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-163 4484,-181 4506,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38acde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-127 4484,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28969@0" ObjectIDZND0="g_3774b60@0" Pin0InfoVect0LinkObjId="g_3774b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191254_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-127 4484,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37a2a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4630,-299 4630,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28914@0" ObjectIDZND0="28942@0" Pin0InfoVect0LinkObjId="SW-191191_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c7d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4630,-299 4630,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a35140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4630,-267 4630,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28942@1" ObjectIDZND0="28941@1" Pin0InfoVect0LinkObjId="SW-191190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191191_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4630,-267 4630,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b2f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4630,-224 4630,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28941@0" ObjectIDZND0="28943@1" Pin0InfoVect0LinkObjId="SW-191191_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4630,-224 4630,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38139a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4630,-181 4651,-181 4651,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="28943@x" ObjectIDND1="28944@x" ObjectIDND2="0@x" ObjectIDZND0="g_37719c0@0" Pin0InfoVect0LinkObjId="g_37719c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191191_0" Pin1InfoVect1LinkObjId="SW-191192_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4630,-181 4651,-181 4651,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37cd840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4630,-191 4630,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="28943@0" ObjectIDZND0="g_37719c0@0" ObjectIDZND1="28944@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_37719c0_0" Pin0InfoVect1LinkObjId="SW-191192_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4630,-191 4630,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b38580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4630,-181 4630,-49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="28943@x" ObjectIDND1="g_37719c0@0" ObjectIDND2="28944@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191191_0" Pin1InfoVect1LinkObjId="g_37719c0_0" Pin1InfoVect2LinkObjId="SW-191192_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4630,-181 4630,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a37960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-163 4608,-181 4630,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="28944@1" ObjectIDZND0="28943@x" ObjectIDZND1="g_37719c0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-191191_0" Pin0InfoVect1LinkObjId="g_37719c0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191192_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-163 4608,-181 4630,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3307920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-127 4608,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28944@0" ObjectIDZND0="g_3324c70@0" Pin0InfoVect0LinkObjId="g_3324c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-127 4608,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38592c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-299 4754,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28914@0" ObjectIDZND0="28938@0" Pin0InfoVect0LinkObjId="SW-191163_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c7d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-299 4754,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2345b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-268 4754,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28938@1" ObjectIDZND0="28937@1" Pin0InfoVect0LinkObjId="SW-191162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191163_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-268 4754,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2548030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-225 4754,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28937@0" ObjectIDZND0="28939@1" Pin0InfoVect0LinkObjId="SW-191163_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-225 4754,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37a4050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-121 4754,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2308730@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2308730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-121 4754,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3609020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-182 4775,-182 4775,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28939@x" ObjectIDND1="g_2308730@0" ObjectIDND2="28940@x" ObjectIDZND0="g_37cd2a0@0" Pin0InfoVect0LinkObjId="g_37cd2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191163_0" Pin1InfoVect1LinkObjId="g_2308730_0" Pin1InfoVect2LinkObjId="SW-191164_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-182 4775,-182 4775,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3851d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-192 4754,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28939@0" ObjectIDZND0="g_37cd2a0@0" ObjectIDZND1="g_2308730@0" ObjectIDZND2="28940@x" Pin0InfoVect0LinkObjId="g_37cd2a0_0" Pin0InfoVect1LinkObjId="g_2308730_0" Pin0InfoVect2LinkObjId="SW-191164_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191163_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-192 4754,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_383c7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-182 4754,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28939@x" ObjectIDND1="g_37cd2a0@0" ObjectIDND2="28940@x" ObjectIDZND0="g_2308730@0" Pin0InfoVect0LinkObjId="g_2308730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191163_0" Pin1InfoVect1LinkObjId="g_37cd2a0_0" Pin1InfoVect2LinkObjId="SW-191164_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-182 4754,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3896260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4732,-164 4732,-182 4754,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28940@1" ObjectIDZND0="28939@x" ObjectIDZND1="g_37cd2a0@0" ObjectIDZND2="g_2308730@0" Pin0InfoVect0LinkObjId="SW-191163_0" Pin0InfoVect1LinkObjId="g_37cd2a0_0" Pin0InfoVect2LinkObjId="g_2308730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191164_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4732,-164 4732,-182 4754,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39495d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4732,-128 4732,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28940@0" ObjectIDZND0="g_3a12380@0" Pin0InfoVect0LinkObjId="g_3a12380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191164_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4732,-128 4732,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3952cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-299 4873,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28914@0" ObjectIDZND0="28954@0" Pin0InfoVect0LinkObjId="SW-191282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c7d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-299 4873,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b31e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-265 4873,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28954@1" ObjectIDZND0="28953@1" Pin0InfoVect0LinkObjId="SW-191281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-265 4873,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3952610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-222 4873,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28953@0" ObjectIDZND0="28955@1" Pin0InfoVect0LinkObjId="SW-191282_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-222 4873,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257c920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-118 4873,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_33a8ec0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a8ec0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-118 4873,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ac270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-179 4894,-179 4894,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28955@x" ObjectIDND1="g_33a8ec0@0" ObjectIDND2="28952@x" ObjectIDZND0="g_33be690@0" Pin0InfoVect0LinkObjId="g_33be690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191282_0" Pin1InfoVect1LinkObjId="g_33a8ec0_0" Pin1InfoVect2LinkObjId="SW-191604_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-179 4894,-179 4894,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2f7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-189 4873,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28955@0" ObjectIDZND0="g_33be690@0" ObjectIDZND1="g_33a8ec0@0" ObjectIDZND2="28952@x" Pin0InfoVect0LinkObjId="g_33be690_0" Pin0InfoVect1LinkObjId="g_33a8ec0_0" Pin0InfoVect2LinkObjId="SW-191604_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191282_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-189 4873,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38456c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-179 4873,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28955@x" ObjectIDND1="g_33be690@0" ObjectIDND2="28952@x" ObjectIDZND0="g_33a8ec0@0" Pin0InfoVect0LinkObjId="g_33a8ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191282_0" Pin1InfoVect1LinkObjId="g_33be690_0" Pin1InfoVect2LinkObjId="SW-191604_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-179 4873,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f7f9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-161 4851,-179 4873,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28952@1" ObjectIDZND0="28955@x" ObjectIDZND1="g_33be690@0" ObjectIDZND2="g_33a8ec0@0" Pin0InfoVect0LinkObjId="SW-191282_0" Pin0InfoVect1LinkObjId="g_33be690_0" Pin0InfoVect2LinkObjId="g_33a8ec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-161 4851,-179 4873,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a63740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-125 4851,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28952@0" ObjectIDZND0="g_37fe550@0" Pin0InfoVect0LinkObjId="g_37fe550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-125 4851,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31d3a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-299 4996,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28914@0" ObjectIDZND0="28946@0" Pin0InfoVect0LinkObjId="SW-191215_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c7d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-299 4996,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38da800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-268 4996,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28946@1" ObjectIDZND0="28945@1" Pin0InfoVect0LinkObjId="SW-191214_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191215_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-268 4996,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3791810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-225 4996,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28945@0" ObjectIDZND0="28947@1" Pin0InfoVect0LinkObjId="SW-191215_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-225 4996,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37b42f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-182 5017,-182 5017,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="28947@x" ObjectIDND1="28948@x" ObjectIDND2="0@x" ObjectIDZND0="g_38328c0@0" Pin0InfoVect0LinkObjId="g_38328c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191215_0" Pin1InfoVect1LinkObjId="SW-191216_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-182 5017,-182 5017,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3812890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-192 4996,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="28947@0" ObjectIDZND0="g_38328c0@0" ObjectIDZND1="28948@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_38328c0_0" Pin0InfoVect1LinkObjId="SW-191216_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191215_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-192 4996,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3494a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-182 4996,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="28947@x" ObjectIDND1="g_38328c0@0" ObjectIDND2="28948@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191215_0" Pin1InfoVect1LinkObjId="g_38328c0_0" Pin1InfoVect2LinkObjId="SW-191216_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-182 4996,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38993d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-164 4974,-182 4996,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="28948@1" ObjectIDZND0="28947@x" ObjectIDZND1="g_38328c0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-191215_0" Pin0InfoVect1LinkObjId="g_38328c0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191216_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-164 4974,-182 4996,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37f0750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-128 4974,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28948@0" ObjectIDZND0="g_35aca20@0" Pin0InfoVect0LinkObjId="g_35aca20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191216_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-128 4974,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3f4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5087,-299 5087,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28914@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c7d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5087,-299 5087,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32c5760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5087,-215 5087,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5087,-215 5087,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c4db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3900,-324 3900,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28992@0" ObjectIDZND0="28913@0" Pin0InfoVect0LinkObjId="g_3791f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3900,-324 3900,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_360b040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4664,-382 4664,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_331f730@0" ObjectIDZND0="28993@1" Pin0InfoVect0LinkObjId="SW-191807_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_331f730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4664,-382 4664,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37c7d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4664,-323 4664,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28993@0" ObjectIDZND0="28914@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4664,-323 4664,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2efd200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3900,-382 3900,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2385860@0" ObjectIDZND0="28992@1" Pin0InfoVect0LinkObjId="SW-191806_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2385860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3900,-382 3900,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39072e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-484 3926,-465 3914,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_35a86f0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a86f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-484 3926,-465 3914,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b32e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4690,-486 4690,-467 4678,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_3b30540@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b30540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4690,-486 4690,-467 4678,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37679c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-419 4001,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3b31740@0" ObjectIDZND0="g_37b4d00@0" ObjectIDZND1="28960@x" Pin0InfoVect0LinkObjId="g_37b4d00_0" Pin0InfoVect1LinkObjId="SW-191381_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b31740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-419 4001,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2dcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-426 4001,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_37b4d00@1" ObjectIDZND0="28960@x" ObjectIDZND1="g_3b31740@0" Pin0InfoVect0LinkObjId="SW-191381_0" Pin0InfoVect1LinkObjId="g_3b31740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37b4d00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-426 4001,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344a3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-419 4001,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_37b4d00@0" ObjectIDND1="g_3b31740@0" ObjectIDZND0="28960@0" Pin0InfoVect0LinkObjId="SW-191381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37b4d00_0" Pin1InfoVect1LinkObjId="g_3b31740_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-419 4001,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b32280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3980,-654 4002,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_378b460@0" ObjectIDZND0="28958@x" ObjectIDZND1="g_380e250@0" Pin0InfoVect0LinkObjId="SW-191344_0" Pin0InfoVect1LinkObjId="g_380e250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_378b460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3980,-654 4002,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a60c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-661 4002,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28958@0" ObjectIDZND0="g_378b460@0" ObjectIDZND1="g_380e250@0" Pin0InfoVect0LinkObjId="g_378b460_0" Pin0InfoVect1LinkObjId="g_380e250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191344_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-661 4002,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3792d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-654 4002,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_378b460@0" ObjectIDND1="28958@x" ObjectIDZND0="g_380e250@0" Pin0InfoVect0LinkObjId="g_380e250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_378b460_0" Pin1InfoVect1LinkObjId="SW-191344_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-654 4002,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a4aaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-825 4067,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28964@0" ObjectIDZND0="28912@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191579_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-825 4067,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3769d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-900 4068,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28963@1" ObjectIDZND0="28962@1" Pin0InfoVect0LinkObjId="SW-191578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-900 4068,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38812b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-857 4068,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28962@0" ObjectIDZND0="28964@1" Pin0InfoVect0LinkObjId="SW-191579_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-857 4068,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3abc600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-935 4090,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="28963@x" ObjectIDND1="28965@x" ObjectIDND2="28966@x" ObjectIDZND0="g_3797fd0@0" Pin0InfoVect0LinkObjId="g_3797fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191579_0" Pin1InfoVect1LinkObjId="SW-191582_0" Pin1InfoVect2LinkObjId="SW-191584_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-935 4090,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a38450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-935 4068,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3797fd0@0" ObjectIDND1="28965@x" ObjectIDND2="28966@x" ObjectIDZND0="28963@0" Pin0InfoVect0LinkObjId="SW-191579_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3797fd0_0" Pin1InfoVect1LinkObjId="SW-191582_0" Pin1InfoVect2LinkObjId="SW-191584_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-935 4068,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_260fa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-953 4068,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="28965@1" ObjectIDZND0="g_3797fd0@0" ObjectIDZND1="28963@x" ObjectIDZND2="28966@x" Pin0InfoVect0LinkObjId="g_3797fd0_0" Pin0InfoVect1LinkObjId="SW-191579_0" Pin0InfoVect2LinkObjId="SW-191584_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191582_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-953 4068,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_376da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-944 4068,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="28965@x" ObjectIDND1="28966@x" ObjectIDZND0="g_3797fd0@0" ObjectIDZND1="28963@x" Pin0InfoVect0LinkObjId="g_3797fd0_0" Pin0InfoVect1LinkObjId="SW-191579_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-191582_0" Pin1InfoVect1LinkObjId="SW-191584_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-944 4068,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3490700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-929 4032,-944 4068,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="28966@0" ObjectIDZND0="28965@x" ObjectIDZND1="g_3797fd0@0" ObjectIDZND2="28963@x" Pin0InfoVect0LinkObjId="SW-191582_0" Pin0InfoVect1LinkObjId="g_3797fd0_0" Pin0InfoVect2LinkObjId="SW-191579_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191584_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-929 4032,-944 4068,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3861cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-912 4032,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="28966@1" ObjectIDZND0="g_37f3a70@0" Pin0InfoVect0LinkObjId="g_37f3a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191584_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-912 4032,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_365e630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-789 4254,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28912@0" ObjectIDZND0="28967@0" Pin0InfoVect0LinkObjId="SW-191586_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a4aaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-789 4254,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a339d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-16 4253,-32 4208,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_386a1b0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_386a1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-16 4253,-32 4208,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38c5850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4552,-16 4552,-32 4506,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_3738b40@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3738b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4552,-16 4552,-32 4506,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38c42a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4918,-13 4918,-29 4873,-29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_37b71c0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37b71c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4918,-13 4918,-29 4873,-29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34918b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3877,-109 3877,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3861580@0" ObjectIDND1="43429@x" ObjectIDND2="28919@x" ObjectIDZND0="g_34774d0@0" Pin0InfoVect0LinkObjId="g_34774d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3861580_0" Pin1InfoVect1LinkObjId="SM-CX_HCP.P1_0" Pin1InfoVect2LinkObjId="SW-191056_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3877,-109 3877,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-109 3877,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3861580@0" ObjectIDND1="43429@x" ObjectIDND2="28919@x" ObjectIDZND0="g_34774d0@0" Pin0InfoVect0LinkObjId="g_34774d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3861580_0" Pin1InfoVect1LinkObjId="SM-CX_HCP.P1_0" Pin1InfoVect2LinkObjId="SW-191056_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-109 3877,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38594e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-689 4254,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_391a730@1" ObjectIDZND0="g_3861750@0" Pin0InfoVect0LinkObjId="g_3861750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_391a730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-689 4254,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e870b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-726 4290,-736 4254,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_345dce0@0" ObjectIDZND0="28967@x" ObjectIDZND1="g_391a730@0" Pin0InfoVect0LinkObjId="SW-191586_0" Pin0InfoVect1LinkObjId="g_391a730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_345dce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-726 4290,-736 4254,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a497f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-753 4254,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28967@1" ObjectIDZND0="g_345dce0@0" ObjectIDZND1="g_391a730@0" Pin0InfoVect0LinkObjId="g_345dce0_0" Pin0InfoVect1LinkObjId="g_391a730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191586_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-753 4254,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3751c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-736 4254,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_345dce0@0" ObjectIDND1="28967@x" ObjectIDZND0="g_391a730@0" Pin0InfoVect0LinkObjId="g_391a730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_345dce0_0" Pin1InfoVect1LinkObjId="SW-191586_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-736 4254,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b55e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-119 3838,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="g_3861580@1" ObjectIDZND0="g_34774d0@0" ObjectIDZND1="43429@x" ObjectIDZND2="28919@x" Pin0InfoVect0LinkObjId="g_34774d0_0" Pin0InfoVect1LinkObjId="SM-CX_HCP.P1_0" Pin0InfoVect2LinkObjId="SW-191056_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3861580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-119 3838,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3810330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-109 3838,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_34774d0@0" ObjectIDND1="g_3861580@0" ObjectIDND2="28919@x" ObjectIDZND0="43429@0" Pin0InfoVect0LinkObjId="SM-CX_HCP.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34774d0_0" Pin1InfoVect1LinkObjId="g_3861580_0" Pin1InfoVect2LinkObjId="SW-191056_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-109 3838,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_383e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,15 3932,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_37b0610@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37b0610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3932,15 3932,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348b790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,-31 3932,-16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="28924@x" ObjectIDND1="28923@x" ObjectIDZND0="g_37b0610@0" Pin0InfoVect0LinkObjId="g_37b0610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-191063_0" Pin1InfoVect1LinkObjId="SW-191062_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3932,-31 3932,-16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3842090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4801,-682 4801,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_378fdb0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_378fdb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4801,-682 4801,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38a3b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4801,-649 4801,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4801,-649 4801,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25ceb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4801,-606 4801,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4801,-606 4801,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37fab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-682 4889,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_378fdb0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_378fdb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-682 4889,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_389f450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-650 4889,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-650 4889,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37fbfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-607 4889,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-607 4889,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_363b010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-683 4997,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3a53b10@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3a53b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-683 4997,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b3bf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-650 4997,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-650 4997,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35abab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-607 4997,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-607 4997,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_360ea90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5084,-683 5084,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3a53b10@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3a53b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5084,-683 5084,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_253f180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5084,-649 5084,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5084,-649 5084,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eb2690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5084,-606 5084,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5084,-606 5084,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37c5b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5386,-682 5386,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_37f1680@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37f1680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5386,-682 5386,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f598f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5386,-649 5386,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5386,-649 5386,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_385a010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5386,-606 5386,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5386,-606 5386,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b65d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="4774,-682 4801,-682 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_378fdb0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_378fdb0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4774,-682 4801,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38ad460">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="4801,-682 4889,-682 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_378fdb0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_378fdb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4801,-682 4889,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38ad6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="4889,-682 4922,-682 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_378fdb0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_378fdb0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-682 4922,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b66bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="4968,-683 4997,-683 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3a53b10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3a53b10_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-683 4997,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35d07a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="5084,-683 5116,-683 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_3a53b10@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3a53b10_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5084,-683 5116,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37ebc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="5358,-682 5386,-682 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="g_37f1680@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_37f1680_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5358,-682 5386,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37bec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4811,-917 4811,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4811,-917 4811,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39f8100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4811,-813 4811,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4811,-813 4811,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b3ae80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4811,-765 4811,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4811,-765 4811,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b3b0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4811,-719 4811,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4811,-719 4811,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3755010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5012,-812 5012,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5012,-812 5012,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b1b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5012,-769 5012,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5012,-769 5012,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a798f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5012,-719 5012,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5012,-719 5012,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2585770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-814 5204,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-814 5204,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37f4050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-771 5204,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-771 5204,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37f42b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-719 5204,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-719 5204,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38b1300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5391,-812 5391,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5391,-812 5391,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_386e2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5391,-769 5391,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5391,-769 5391,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37d66a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5391,-719 5391,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5391,-719 5391,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37d6900">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="4997,-683 5084,-683 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_3a53b10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3a53b10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-683 5084,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3784e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4843,-904 4843,-936 4811,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_35a8bb0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a8bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4843,-904 4843,-936 4811,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37cb2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5044,-902 5044,-934 5012,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_37cb500@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37cb500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5044,-902 5044,-934 5012,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3888720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5012,-915 5012,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5012,-915 5012,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35a5ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5236,-1011 5236,-1043 5204,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_35a6120@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a6120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5236,-1011 5236,-1043 5204,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3617c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5422,-903 5422,-935 5390,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_3617e50@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3617e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5422,-903 5422,-935 5390,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b66840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5390,-916 5390,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5390,-916 5390,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_378fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-709 4889,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_378fdb0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_378fdb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-709 4889,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a05ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-909 4889,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_378fdb0@1" Pin0InfoVect0LinkObjId="g_378fdb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-909 4889,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a062d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5084,-908 5084,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3a53b10@1" Pin0InfoVect0LinkObjId="g_3a53b10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5084,-908 5084,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a54190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5084,-708 5084,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3a53b10@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a53b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5084,-708 5084,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37f1290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5469,-910 5469,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_37f1680@1" Pin0InfoVect0LinkObjId="g_37f1680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5469,-910 5469,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3883050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5469,-710 5469,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_37f1680@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37f1680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5469,-710 5469,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3918010">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="5386,-682 5469,-682 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_37f1680@0" Pin0InfoVect0LinkObjId="g_37f1680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5386,-682 5469,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3918270">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="5469,-682 5506,-682 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_37f1680@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37f1680_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5469,-682 5506,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36c5440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4811,-1077 4811,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3883700@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3883700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4811,-1077 4811,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36c56a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4811,-1127 4811,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4811,-1127 4811,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3840790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5012,-1125 5012,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_36c5900@0" Pin0InfoVect0LinkObjId="g_36c5900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5012,-1125 5012,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38409f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5012,-1075 5012,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5012,-1075 5012,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38d7d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5390,-1126 5390,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_39f76e0@0" Pin0InfoVect0LinkObjId="g_39f76e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5390,-1126 5390,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38d7fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5390,-1076 5390,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5390,-1076 5390,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38d8220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-1023 5204,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-1023 5204,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36a4450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-986 5204,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-986 5204,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_388d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-943 5204,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-943 5204,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_389ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-910 5204,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_388dbb0@0" Pin0InfoVect0LinkObjId="g_388dbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-910 5204,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_389ec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-841 5204,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_388dbb0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_388dbb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-841 5204,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_382dec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5244,-719 5244,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5244,-719 5244,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37f91c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5115,-553 5084,-553 5084,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_37f8240@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37f8240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5115,-553 5084,-553 5084,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36c6d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5225,-651 5225,-553 5194,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_37f8240@1" Pin0InfoVect0LinkObjId="g_37f8240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5225,-651 5225,-553 5194,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37e17b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-513 5117,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_37e1ed0@1" ObjectIDZND0="g_36c6f70@0" Pin0InfoVect0LinkObjId="g_36c6f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37e1ed0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-513 5117,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37e1a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-652 5266,-513 5196,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_36c6f70@1" Pin0InfoVect0LinkObjId="g_36c6f70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-652 5266,-513 5196,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37e1c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4985,-513 4889,-513 4889,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_37e1ed0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37e1ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4985,-513 4889,-513 4889,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3836800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-574 4997,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-574 4997,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3836a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-481 5386,-481 5386,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-481 5386,-481 5386,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3836cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4801,-573 4801,-481 4803,-481 4997,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4801,-573 4801,-481 4803,-481 4997,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35c8c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3651,-31 3651,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28920@1" ObjectIDZND0="g_35c8ea0@0" Pin0InfoVect0LinkObjId="g_35c8ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191057_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3651,-31 3651,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-30 3982,-12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28924@1" ObjectIDZND0="g_35a44a0@0" Pin0InfoVect0LinkObjId="g_35a44a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-30 3982,-12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a4ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-109 3728,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="g_34774d0@0" ObjectIDND1="g_3861580@0" ObjectIDND2="43429@x" ObjectIDZND0="28921@1" Pin0InfoVect0LinkObjId="SW-191059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34774d0_0" Pin1InfoVect1LinkObjId="g_3861580_0" Pin1InfoVect2LinkObjId="SM-CX_HCP.P1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-109 3728,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a59e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-109 3728,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_34774d0@0" ObjectIDND1="g_3861580@0" ObjectIDND2="43429@x" ObjectIDZND0="28919@x" ObjectIDZND1="28921@x" Pin0InfoVect0LinkObjId="SW-191056_0" Pin0InfoVect1LinkObjId="SW-191059_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34774d0_0" Pin1InfoVect1LinkObjId="g_3861580_0" Pin1InfoVect2LinkObjId="SM-CX_HCP.P1_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-109 3728,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35ca8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-109 3601,-109 3601,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="g_34774d0@0" ObjectIDND1="g_3861580@0" ObjectIDND2="43429@x" ObjectIDZND0="28919@1" Pin0InfoVect0LinkObjId="SW-191056_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34774d0_0" Pin1InfoVect1LinkObjId="g_3861580_0" Pin1InfoVect2LinkObjId="SM-CX_HCP.P1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-109 3601,-109 3601,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35cd8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-31 3742,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_375cf30@0" ObjectIDND1="28921@x" ObjectIDZND0="28922@0" Pin0InfoVect0LinkObjId="SW-191060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_375cf30_0" Pin1InfoVect1LinkObjId="SW-191059_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-31 3742,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35cdb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-51 3728,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="28921@0" ObjectIDZND0="28922@x" ObjectIDZND1="g_375cf30@0" Pin0InfoVect0LinkObjId="SW-191060_0" Pin0InfoVect1LinkObjId="g_375cf30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-51 3728,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35cdd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-31 3728,-20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="28922@x" ObjectIDND1="28921@x" ObjectIDZND0="g_375cf30@0" Pin0InfoVect0LinkObjId="g_375cf30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-191060_0" Pin1InfoVect1LinkObjId="SW-191059_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-31 3728,-20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35ce170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-31 3778,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28922@1" ObjectIDZND0="g_35ce3d0@0" Pin0InfoVect0LinkObjId="g_35ce3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-31 3778,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3658260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3877,-109 3932,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="generator" ObjectIDND0="g_34774d0@0" ObjectIDND1="g_3861580@0" ObjectIDND2="43429@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34774d0_0" Pin1InfoVect1LinkObjId="g_3861580_0" Pin1InfoVect2LinkObjId="SM-CX_HCP.P1_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3877,-109 3932,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36584c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,-109 3932,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="28923@1" Pin0InfoVect0LinkObjId="SW-191062_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3932,-109 3932,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365a4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,11 3601,26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_365a710@1" ObjectIDZND0="g_3658720@0" Pin0InfoVect0LinkObjId="g_3658720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_365a710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,11 3601,26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365af90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-31 3601,-20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="28920@x" ObjectIDND1="28919@x" ObjectIDZND0="g_365a710@0" Pin0InfoVect0LinkObjId="g_365a710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-191057_0" Pin1InfoVect1LinkObjId="SW-191056_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-31 3601,-20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365ba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-31 3601,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="28920@0" ObjectIDZND0="g_365a710@0" ObjectIDZND1="28919@x" Pin0InfoVect0LinkObjId="g_365a710_0" Pin0InfoVect1LinkObjId="SW-191056_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-31 3601,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-31 3601,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="28920@x" ObjectIDND1="g_365a710@0" ObjectIDZND0="28919@0" Pin0InfoVect0LinkObjId="SW-191056_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-191057_0" Pin1InfoVect1LinkObjId="g_365a710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-31 3601,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365c7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3946,-30 3932,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="28924@0" ObjectIDZND0="g_37b0610@0" ObjectIDZND1="28923@x" Pin0InfoVect0LinkObjId="g_37b0610_0" Pin0InfoVect1LinkObjId="SW-191062_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191063_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3946,-30 3932,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_365ca30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,-30 3932,-49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_37b0610@0" ObjectIDND1="28924@x" ObjectIDZND0="28923@0" Pin0InfoVect0LinkObjId="SW-191062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37b0610_0" Pin1InfoVect1LinkObjId="SW-191063_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3932,-30 3932,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a9cd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5204,-1132 5204,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3840c50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3840c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5204,-1132 5204,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a9e930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4046,-1046 4068,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="g_3a9f490@0" ObjectIDZND0="28965@x" ObjectIDZND1="g_387f7a0@0" ObjectIDZND2="g_3aa01c0@0" Pin0InfoVect0LinkObjId="SW-191582_0" Pin0InfoVect1LinkObjId="g_387f7a0_0" Pin0InfoVect2LinkObjId="g_3aa01c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a9f490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4046,-1046 4068,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a9f2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-1046 4068,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_3a9f490@0" ObjectIDND1="g_387f7a0@0" ObjectIDND2="g_3aa01c0@0" ObjectIDZND0="28965@0" Pin0InfoVect0LinkObjId="SW-191582_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3a9f490_0" Pin1InfoVect1LinkObjId="g_387f7a0_0" Pin1InfoVect2LinkObjId="g_3aa01c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-1046 4068,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3aa0f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-1055 4139,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_387f7a0@0" ObjectIDND1="28965@x" ObjectIDND2="g_3a9f490@0" ObjectIDZND0="g_3aa01c0@0" Pin0InfoVect0LinkObjId="g_3aa01c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_387f7a0_0" Pin1InfoVect1LinkObjId="SW-191582_0" Pin1InfoVect2LinkObjId="g_3a9f490_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-1055 4139,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a7820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-1063 4068,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="g_387f7a0@1" ObjectIDZND0="28965@x" ObjectIDZND1="g_3a9f490@0" ObjectIDZND2="g_3aa01c0@0" Pin0InfoVect0LinkObjId="SW-191582_0" Pin0InfoVect1LinkObjId="g_3a9f490_0" Pin0InfoVect2LinkObjId="g_3aa01c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_387f7a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-1063 4068,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36a7a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-1055 4068,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_387f7a0@0" ObjectIDND1="g_3aa01c0@0" ObjectIDZND0="28965@x" ObjectIDZND1="g_3a9f490@0" Pin0InfoVect0LinkObjId="SW-191582_0" Pin0InfoVect1LinkObjId="g_3a9f490_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_387f7a0_0" Pin1InfoVect1LinkObjId="g_3aa01c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-1055 4068,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a84b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4437,-299 4437,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28914@0" ObjectIDZND0="28936@0" Pin0InfoVect0LinkObjId="SW-191140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c7d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4437,-299 4437,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36a86a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4437,-208 4437,-165 4312,-165 4312,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="28936@1" ObjectIDZND0="g_386ed40@0" ObjectIDZND1="28935@x" Pin0InfoVect0LinkObjId="g_386ed40_0" Pin0InfoVect1LinkObjId="SW-191138_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191140_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4437,-208 4437,-165 4312,-165 4312,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bf110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-1116 4068,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_387f7a0@0" ObjectIDZND0="48672@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_387f7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-1116 4068,-1137 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-189150" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3418.000000 -1099.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28807" ObjectName="DYN-CX_HCP"/>
     <cge:Meas_Ref ObjectId="189150"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9af10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -13.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9b910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.000000 -28.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9c490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ac8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 880.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36acb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 865.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36acd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 895.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ad860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4046.000000 727.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ada50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 712.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36adc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 742.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36adee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 372.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ae140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 357.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ae380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 387.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b11d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 844.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b1ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 860.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b8ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 875.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b9170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 890.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b93f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 904.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b9630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 828.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35ba2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 352.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35ba540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 368.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35ba780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 383.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35ba9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 398.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bac00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 412.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bae40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 336.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4948.000000 346.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bb3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4962.000000 362.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bb630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4956.000000 377.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bb870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4956.000000 392.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bbab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4956.000000 406.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bbcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4964.000000 330.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bc390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.000000 -120.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bc5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 -105.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bc820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3774.000000 -90.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bca60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 -137.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="25" graphid="g_35bdc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 79.000000) translate(0,20)">M</text>
    <circle DF8003:Layer="PUBLIC" cx="4080" cy="66" fill="none" fillStyle="0" r="24" stroke="rgb(0,255,0)" stroke-width="1"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="25" graphid="g_35be400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 77.000000) translate(0,20)">M</text>
    <circle DF8003:Layer="PUBLIC" cx="4757" cy="64" fill="none" fillStyle="0" r="24" stroke="rgb(0,255,0)" stroke-width="1"/>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HCP.CX_HCP_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-789 4433,-789 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28912" ObjectName="BS-CX_HCP.CX_HCP_3IM"/>
    <cge:TPSR_Ref TObjectID="28912"/></metadata>
   <polyline fill="none" opacity="0" points="3889,-789 4433,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HCP.CX_HCP_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-299 4349,-299 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28913" ObjectName="BS-CX_HCP.CX_HCP_9IM"/>
    <cge:TPSR_Ref TObjectID="28913"/></metadata>
   <polyline fill="none" opacity="0" points="3766,-299 4349,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HCP.CX_HCP_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-299 5157,-299 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28914" ObjectName="BS-CX_HCP.CX_HCP_9IIM"/>
    <cge:TPSR_Ref TObjectID="28914"/></metadata>
   <polyline fill="none" opacity="0" points="4402,-299 5157,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4773,-719 4921,-719 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4773,-719 4921,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-719 5115,-719 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4967,-719 5115,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5162,-719 5310,-719 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5162,-719 5310,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5356,-719 5504,-719 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5356,-719 5504,-719 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="28912" cx="4002" cy="-789" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28912" cx="4067" cy="-789" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28912" cx="4254" cy="-789" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5012" cy="-719" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5204" cy="-719" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5391" cy="-719" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4811" cy="-719" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28913" cx="4001" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28913" cx="3838" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28913" cx="4083" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28913" cx="4208" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28913" cx="4312" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28913" cx="3900" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28914" cx="4506" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28914" cx="4630" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28914" cx="4754" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28914" cx="4873" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28914" cx="4996" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28914" cx="5087" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28914" cx="4664" cy="-299" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5244" cy="-719" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-191343">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 -686.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28956" ObjectName="SW-CX_HCP.CX_HCP_301BK"/>
     <cge:Meas_Ref ObjectId="191343"/>
    <cge:TPSR_Ref TObjectID="28956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191380">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 -343.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28959" ObjectName="SW-CX_HCP.CX_HCP_001BK"/>
     <cge:Meas_Ref ObjectId="191380"/>
    <cge:TPSR_Ref TObjectID="28959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3829.000000 -216.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28916" ObjectName="SW-CX_HCP.CX_HCP_061BK"/>
     <cge:Meas_Ref ObjectId="191052"/>
    <cge:TPSR_Ref TObjectID="28916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191081">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 -216.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28925" ObjectName="SW-CX_HCP.CX_HCP_062BK"/>
     <cge:Meas_Ref ObjectId="191081"/>
    <cge:TPSR_Ref TObjectID="28925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28929" ObjectName="SW-CX_HCP.CX_HCP_063BK"/>
     <cge:Meas_Ref ObjectId="191106"/>
    <cge:TPSR_Ref TObjectID="28929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -216.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28933" ObjectName="SW-CX_HCP.CX_HCP_012BK"/>
     <cge:Meas_Ref ObjectId="191137"/>
    <cge:TPSR_Ref TObjectID="28933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191252">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -216.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28949" ObjectName="SW-CX_HCP.CX_HCP_064BK"/>
     <cge:Meas_Ref ObjectId="191252"/>
    <cge:TPSR_Ref TObjectID="28949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191190">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 -216.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28941" ObjectName="SW-CX_HCP.CX_HCP_065BK"/>
     <cge:Meas_Ref ObjectId="191190"/>
    <cge:TPSR_Ref TObjectID="28941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28937" ObjectName="SW-CX_HCP.CX_HCP_066BK"/>
     <cge:Meas_Ref ObjectId="191162"/>
    <cge:TPSR_Ref TObjectID="28937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4864.000000 -214.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28953" ObjectName="SW-CX_HCP.CX_HCP_067BK"/>
     <cge:Meas_Ref ObjectId="191281"/>
    <cge:TPSR_Ref TObjectID="28953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191214">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28945" ObjectName="SW-CX_HCP.CX_HCP_068BK"/>
     <cge:Meas_Ref ObjectId="191214"/>
    <cge:TPSR_Ref TObjectID="28945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4059.000000 -849.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28962" ObjectName="SW-CX_HCP.CX_HCP_361BK"/>
     <cge:Meas_Ref ObjectId="191578"/>
    <cge:TPSR_Ref TObjectID="28962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4792.000000 -598.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4880.000000 -599.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4988.000000 -599.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5075.000000 -598.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5377.000000 -598.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.000000 -757.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5003.000000 -761.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5195.000000 -763.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5382.000000 -761.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5195.000000 -935.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3750b90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3956.000000 -808.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f55cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -570.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_360ddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1055.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_360ddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1055.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_360ddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1055.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_360ddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1055.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_360ddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1055.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_360ddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1055.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_360ddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1055.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_39d01c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3276.000000 -1167.500000) translate(0,16)">黄草坡电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b6e3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -428.000000) translate(0,15)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b6e3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -428.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b6e3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -428.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b6e3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -428.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b6e3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -428.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a7a570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -428.000000) translate(0,15)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a7a570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -428.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a7a570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -428.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a7a570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -428.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a7a570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -428.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375e130" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4283.000000 -662.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375e130" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4283.000000 -662.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_338a9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.860941 -9.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3487a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4493.860941 -8.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bd7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4862.860941 -7.000000) translate(0,12)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(60,120,255)" font-family="SimSun" font-size="18" graphid="g_382d2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5222.000000 -676.000000) translate(0,15)">ATSE</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3836f10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4745.000000 -757.000000) translate(0,12)">0.4kVⅠ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3836f10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4745.000000 -757.000000) translate(0,27)">段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3662390" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4950.000000 -757.000000) translate(0,12)">0.4kVⅡ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3662390" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4950.000000 -757.000000) translate(0,27)">段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36628f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5223.000000 -744.000000) translate(0,12)">0.4kV保安段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c5ac0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5484.000000 -757.000000) translate(0,12)">0.4kVⅢ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c5ac0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5484.000000 -757.000000) translate(0,27)">段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c6020" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5027.000000 -474.000000) translate(0,12)">站用电接线示意图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c7f60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4790.000000 -1159.000000) translate(0,12)">10kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c7f60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4790.000000 -1159.000000) translate(0,27)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c83c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4987.000000 -1159.000000) translate(0,12)">10kV2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c83c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4987.000000 -1159.000000) translate(0,27)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c8600" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5369.000000 -1159.000000) translate(0,12)">10kV3号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c8600" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5369.000000 -1159.000000) translate(0,27)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c8840" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5162.000000 -1215.000000) translate(0,12)">10kV富苍线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c8840" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5162.000000 -1215.000000) translate(0,27)">黄草支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c8840" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5162.000000 -1215.000000) translate(0,42)">小黑箐分支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36a5900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -570.000000) translate(0,10)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36a5900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -570.000000) translate(0,22)">SF11-16000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36a5900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -570.000000) translate(0,34)">38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36a5900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -570.000000) translate(0,46)">Ud=8%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36a5900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -570.000000) translate(0,58)">16000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36a5900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -570.000000) translate(0,70)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_365cc90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3768.000000 -321.000000) translate(0,12)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_369d430" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5091.000000 -323.000000) translate(0,12)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_369d670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5010.860941 -109.000000) translate(0,12)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36cb810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3566.860941 58.000000) translate(0,12)">1号仪表TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36cc610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.860941 59.000000) translate(0,12)">1号励磁TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ccea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3812.860941 50.000000) translate(0,12)">1号汽轮</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ccea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3812.860941 50.000000) translate(0,27)">发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36cdc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3902.860941 78.000000) translate(0,12)">1号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ce0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4056.860941 -10.000000) translate(0,12)">1号引风机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ce820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4745.860941 -7.000000) translate(0,12)">2号引风机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ceaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.860941 -7.000000) translate(0,12)">渗沥液站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36cfd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5234.860941 -1068.000000) translate(0,12)">施工变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3affbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4077.000000 -878.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b03930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4080.000000 -995.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b03e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.000000 -931.000000) translate(0,12)">3613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b04060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -769.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b04570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4011.000000 -715.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b047f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4010.000000 -372.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b04a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -245.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b04c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3550.000000 -73.000000) translate(0,12)">0613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b04eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3655.000000 -39.000000) translate(0,12)">06137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b051f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3682.000000 -74.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b05650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3782.000000 -39.000000) translate(0,12)">06127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b05890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -72.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b05ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3986.000000 -38.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b05d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -245.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b05f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4009.000000 -152.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b06190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -246.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b063d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -154.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b06610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -245.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b06850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4515.000000 -245.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b06a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -152.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364e130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -245.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364e370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -152.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364e5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4763.000000 -246.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364e7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4679.000000 -153.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364ea30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4882.000000 -243.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364ec70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -150.000000) translate(0,12)">06767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364eeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5005.000000 -246.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364f1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -153.000000) translate(0,12)">06867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364f600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5102.000000 -260.000000) translate(0,12)">0232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a9cfe0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3233.000000 -213.000000) translate(0,15)">6086067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9df60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3974.000000 -846.000000) translate(0,12)">计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a7ce0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4154.000000 -1007.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ac300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -250.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b0450" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4084.000000 -1172.000000) translate(0,12)">龙黄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b0c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.000000 -349.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b0ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4671.000000 -348.000000) translate(0,12)">0902</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_380eae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 -93.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3823280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4180.000000 -95.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3774b60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4478.000000 -93.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3324c70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4602.000000 -93.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a12380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4726.000000 -94.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37fe550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -91.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35aca20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 -94.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a86f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -479.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b30540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.000000 -481.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_386a1b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 2.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3738b40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 2.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37b71c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4912.000000 5.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a8bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4837.000000 -886.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37cb500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5038.000000 -884.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a6120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 -993.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3617e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5416.000000 -885.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35c8ea0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3645.000000 5.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a44a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 6.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35ce3d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 5.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HCP"/>
</svg>