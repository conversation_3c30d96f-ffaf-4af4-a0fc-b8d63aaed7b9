<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-288" aopId="1835526" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1159 2725 1414">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape45">
    <polyline arcFlag="1" points="19,100 17,100 15,99 14,99 12,98 11,97 9,96 8,94 7,92 7,91 6,89 6,87 6,85 7,83 7,82 8,80 9,79 11,77 12,76 14,75 15,75 17,74 19,74 21,74 23,75 24,75 26,76 27,77 29,79 30,80 31,82 31,83 32,85 32,87 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="100" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="87" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape175">
    <polyline DF8003:Layer="PUBLIC" points="6,4 0,16 12,16 6,4 6,5 6,4 "/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="voltageTransformer:shape135">
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="52"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
   </symbol>
   <symbol id="voltageTransformer:shape144">
    <circle cx="17" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="0" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="3" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="2" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="5" x2="10" y1="37" y2="37"/>
    <polyline points="5,7 5,37 " stroke-width="1"/>
    <circle cx="17" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="15" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="30" x2="30" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="34" x2="30" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="34" x2="30" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="27" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="33" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="41" x2="38" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="30" x2="27" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="39" y2="36"/>
    <circle cx="30" cy="23" r="7.5" stroke-width="0.804311"/>
    <circle cx="29" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="22" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="14" y1="25" y2="25"/>
    <circle cx="40" cy="30" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21805e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2180e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2181920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21824f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21836e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2184200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2184dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_21858b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2186f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2186f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21886a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21886a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_218a400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_218a400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_218b360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_218cfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_218dc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_218e9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_218f140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21964b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2196ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2197300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2197d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2198f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2199880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_219a370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2190c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2191880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_21922d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2193470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2194090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21955c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_235d380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23500e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2351bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1424" width="2735" x="11" y="-1164"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-240071">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.594799 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40138" ObjectName="SW-CX_FHS.CX_FHS_001BK"/>
     <cge:Meas_Ref ObjectId="240071"/>
    <cge:TPSR_Ref TObjectID="40138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 618.763970 -111.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40159" ObjectName="SW-CX_FHS.CX_FHS_032BK"/>
     <cge:Meas_Ref ObjectId="240111"/>
    <cge:TPSR_Ref TObjectID="40159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1529.091742 -117.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40197" ObjectName="SW-CX_FHS.CX_FHS_012BK"/>
     <cge:Meas_Ref ObjectId="240158"/>
    <cge:TPSR_Ref TObjectID="40197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240131">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1741.118337 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40175" ObjectName="SW-CX_FHS.CX_FHS_041BK"/>
     <cge:Meas_Ref ObjectId="240131"/>
    <cge:TPSR_Ref TObjectID="40175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240141">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2030.286780 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40183" ObjectName="SW-CX_FHS.CX_FHS_043BK"/>
     <cge:Meas_Ref ObjectId="240141"/>
    <cge:TPSR_Ref TObjectID="40183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240136">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1892.689765 -110.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40179" ObjectName="SW-CX_FHS.CX_FHS_042BK"/>
     <cge:Meas_Ref ObjectId="240136"/>
    <cge:TPSR_Ref TObjectID="40179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240152">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.189765 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40191" ObjectName="SW-CX_FHS.CX_FHS_045BK"/>
     <cge:Meas_Ref ObjectId="240152"/>
    <cge:TPSR_Ref TObjectID="40191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240079">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1892.708955 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40144" ObjectName="SW-CX_FHS.CX_FHS_002BK"/>
     <cge:Meas_Ref ObjectId="240079"/>
    <cge:TPSR_Ref TObjectID="40144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240069">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.594799 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40217" ObjectName="SW-CX_FHS.CX_FHS_301BK"/>
     <cge:Meas_Ref ObjectId="240069"/>
    <cge:TPSR_Ref TObjectID="40217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240086">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1507.000000 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40147" ObjectName="SW-CX_FHS.CX_FHS_371BK"/>
     <cge:Meas_Ref ObjectId="240086"/>
    <cge:TPSR_Ref TObjectID="40147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240077">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1892.708955 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40141" ObjectName="SW-CX_FHS.CX_FHS_302BK"/>
     <cge:Meas_Ref ObjectId="240077"/>
    <cge:TPSR_Ref TObjectID="40141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240126">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.577072 -109.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40171" ObjectName="SW-CX_FHS.CX_FHS_036BK"/>
     <cge:Meas_Ref ObjectId="240126"/>
    <cge:TPSR_Ref TObjectID="40171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240105">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 471.449580 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40153" ObjectName="SW-CX_FHS.CX_FHS_031BK"/>
     <cge:Meas_Ref ObjectId="240105"/>
    <cge:TPSR_Ref TObjectID="40153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240116">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.947280 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40163" ObjectName="SW-CX_FHS.CX_FHS_033BK"/>
     <cge:Meas_Ref ObjectId="240116"/>
    <cge:TPSR_Ref TObjectID="40163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240121">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 924.859443 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40167" ObjectName="SW-CX_FHS.CX_FHS_034BK"/>
     <cge:Meas_Ref ObjectId="240121"/>
    <cge:TPSR_Ref TObjectID="40167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240146">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2191.286780 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40187" ObjectName="SW-CX_FHS.CX_FHS_044BK"/>
     <cge:Meas_Ref ObjectId="240146"/>
    <cge:TPSR_Ref TObjectID="40187"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b368f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1451.000000 -862.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b09f60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1424.000000 -463.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a838b0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1372.536191 32.500000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a26bf0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1671.910448 14.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_FHS.CX_FHS_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2343.189765 180.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40314" ObjectName="CB-CX_FHS.CX_FHS_Cb2"/>
    <cge:TPSR_Ref TObjectID="40314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_FHS.CX_FHS_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.449580 180.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40313" ObjectName="CB-CX_FHS.CX_FHS_Cb1"/>
    <cge:TPSR_Ref TObjectID="40313"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1071.332393 -24.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1071.332393 -24.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_FHS.CX_FHS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="60680"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.594799 -377.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.594799 -377.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="40211" ObjectName="TF-CX_FHS.CX_FHS_1T"/>
    <cge:TPSR_Ref TObjectID="40211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_FHS.CX_FHS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="60684"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1870.708955 -376.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1870.708955 -376.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="40212" ObjectName="TF-CX_FHS.CX_FHS_2T"/>
    <cge:TPSR_Ref TObjectID="40212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2512.892324 61.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2512.892324 61.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b6c240">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1576.000000 -803.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b5ebe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -517.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b5f110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1385.000000 -564.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a00920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.763970 -8.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b80da0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 595.000000 -27.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b5b690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1745.118337 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a70410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1713.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a54440">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2034.286780 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b0de60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2006.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af9150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1896.689765 -7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acc360">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1866.000000 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b62730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2500.892324 -7.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af5780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2357.189765 -9.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a6dac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 -864.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a1b5c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1125.000000 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a92cf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.577072 -25.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a01750">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2315.000000 81.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a19000">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 427.000000 84.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a98fb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.449580 -27.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a99c90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.594799 -474.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9a6b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1897.000000 -476.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa4ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1461.000000 -1005.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa67a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa74d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.000000 112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19d5080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.947280 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19d8b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e28b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 928.859443 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e6380">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 898.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c02a30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2195.286780 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c06400">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2167.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0ff70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2316.892324 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a73780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2392.892324 112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a743f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2535.089552 -117.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a7ae50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.421945 -121.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a7ec30">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 446.500000 177.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a7f7d0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2330.500000 177.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a80370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.594799 -314.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a81250">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1897.000000 -309.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a82130">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1346.536191 -41.500000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a82b00">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1281.536191 -53.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a256b0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1645.910448 -59.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a25f00">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1580.910448 -72.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240224" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1829.000000 -625.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40141"/>
     <cge:Term_Ref ObjectID="60538"/>
    <cge:TPSR_Ref TObjectID="40141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240225" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1829.000000 -625.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240225" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40141"/>
     <cge:Term_Ref ObjectID="60538"/>
    <cge:TPSR_Ref TObjectID="40141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1829.000000 -625.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40141"/>
     <cge:Term_Ref ObjectID="60538"/>
    <cge:TPSR_Ref TObjectID="40141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1457.000000 -775.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40147"/>
     <cge:Term_Ref ObjectID="60550"/>
    <cge:TPSR_Ref TObjectID="40147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1457.000000 -775.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40147"/>
     <cge:Term_Ref ObjectID="60550"/>
    <cge:TPSR_Ref TObjectID="40147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1457.000000 -775.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40147"/>
     <cge:Term_Ref ObjectID="60550"/>
    <cge:TPSR_Ref TObjectID="40147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240210" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -284.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40138"/>
     <cge:Term_Ref ObjectID="60532"/>
    <cge:TPSR_Ref TObjectID="40138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240211" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -284.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240211" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40138"/>
     <cge:Term_Ref ObjectID="60532"/>
    <cge:TPSR_Ref TObjectID="40138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240201" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -284.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40138"/>
     <cge:Term_Ref ObjectID="60532"/>
    <cge:TPSR_Ref TObjectID="40138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1809.000000 -284.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40144"/>
     <cge:Term_Ref ObjectID="60544"/>
    <cge:TPSR_Ref TObjectID="40144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1809.000000 -284.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40144"/>
     <cge:Term_Ref ObjectID="60544"/>
    <cge:TPSR_Ref TObjectID="40144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1809.000000 -284.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40144"/>
     <cge:Term_Ref ObjectID="60544"/>
    <cge:TPSR_Ref TObjectID="40144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 162.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40159"/>
     <cge:Term_Ref ObjectID="60574"/>
    <cge:TPSR_Ref TObjectID="40159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 162.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40159"/>
     <cge:Term_Ref ObjectID="60574"/>
    <cge:TPSR_Ref TObjectID="40159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 162.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40159"/>
     <cge:Term_Ref ObjectID="60574"/>
    <cge:TPSR_Ref TObjectID="40159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240289" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 797.000000 162.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240289" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40163"/>
     <cge:Term_Ref ObjectID="60582"/>
    <cge:TPSR_Ref TObjectID="40163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240290" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 797.000000 162.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240290" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40163"/>
     <cge:Term_Ref ObjectID="60582"/>
    <cge:TPSR_Ref TObjectID="40163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240286" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 797.000000 162.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40163"/>
     <cge:Term_Ref ObjectID="60582"/>
    <cge:TPSR_Ref TObjectID="40163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240301" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.000000 161.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40171"/>
     <cge:Term_Ref ObjectID="60598"/>
    <cge:TPSR_Ref TObjectID="40171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240302" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.000000 161.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240302" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40171"/>
     <cge:Term_Ref ObjectID="60598"/>
    <cge:TPSR_Ref TObjectID="40171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240298" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.000000 161.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40171"/>
     <cge:Term_Ref ObjectID="60598"/>
    <cge:TPSR_Ref TObjectID="40171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -75.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40197"/>
     <cge:Term_Ref ObjectID="60650"/>
    <cge:TPSR_Ref TObjectID="40197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240278" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -75.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40197"/>
     <cge:Term_Ref ObjectID="60650"/>
    <cge:TPSR_Ref TObjectID="40197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240274" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -75.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240274" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40197"/>
     <cge:Term_Ref ObjectID="60650"/>
    <cge:TPSR_Ref TObjectID="40197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1732.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40175"/>
     <cge:Term_Ref ObjectID="60606"/>
    <cge:TPSR_Ref TObjectID="40175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240308" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1732.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40175"/>
     <cge:Term_Ref ObjectID="60606"/>
    <cge:TPSR_Ref TObjectID="40175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1732.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40175"/>
     <cge:Term_Ref ObjectID="60606"/>
    <cge:TPSR_Ref TObjectID="40175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240313" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1905.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240313" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40179"/>
     <cge:Term_Ref ObjectID="60614"/>
    <cge:TPSR_Ref TObjectID="40179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1905.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40179"/>
     <cge:Term_Ref ObjectID="60614"/>
    <cge:TPSR_Ref TObjectID="40179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240310" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1905.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40179"/>
     <cge:Term_Ref ObjectID="60614"/>
    <cge:TPSR_Ref TObjectID="40179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240319" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2057.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40183"/>
     <cge:Term_Ref ObjectID="60622"/>
    <cge:TPSR_Ref TObjectID="40183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240320" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2057.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40183"/>
     <cge:Term_Ref ObjectID="60622"/>
    <cge:TPSR_Ref TObjectID="40183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240316" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2057.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40183"/>
     <cge:Term_Ref ObjectID="60622"/>
    <cge:TPSR_Ref TObjectID="40183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240325" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2216.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40187"/>
     <cge:Term_Ref ObjectID="60630"/>
    <cge:TPSR_Ref TObjectID="40187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240326" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2216.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240326" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40187"/>
     <cge:Term_Ref ObjectID="60630"/>
    <cge:TPSR_Ref TObjectID="40187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240322" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2216.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40187"/>
     <cge:Term_Ref ObjectID="60630"/>
    <cge:TPSR_Ref TObjectID="40187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-240214" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 -478.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40211"/>
     <cge:Term_Ref ObjectID="60678"/>
    <cge:TPSR_Ref TObjectID="40211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-240213" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 -478.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40211"/>
     <cge:Term_Ref ObjectID="60678"/>
    <cge:TPSR_Ref TObjectID="40211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-240240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1780.000000 -474.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40212"/>
     <cge:Term_Ref ObjectID="60682"/>
    <cge:TPSR_Ref TObjectID="40212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-240239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1780.000000 -474.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40212"/>
     <cge:Term_Ref ObjectID="60682"/>
    <cge:TPSR_Ref TObjectID="40212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="50" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 505.000000 64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40313"/>
     <cge:Term_Ref ObjectID="60854"/>
    <cge:TPSR_Ref TObjectID="40313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="50" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 505.000000 64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40313"/>
     <cge:Term_Ref ObjectID="60854"/>
    <cge:TPSR_Ref TObjectID="40313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240331" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 225.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240331" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40153"/>
     <cge:Term_Ref ObjectID="60562"/>
    <cge:TPSR_Ref TObjectID="40153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240328" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 225.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240328" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40153"/>
     <cge:Term_Ref ObjectID="60562"/>
    <cge:TPSR_Ref TObjectID="40153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240336" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2378.000000 223.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240336" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40191"/>
     <cge:Term_Ref ObjectID="60638"/>
    <cge:TPSR_Ref TObjectID="40191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240333" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2378.000000 223.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40191"/>
     <cge:Term_Ref ObjectID="60638"/>
    <cge:TPSR_Ref TObjectID="40191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-240253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -768.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40214"/>
     <cge:Term_Ref ObjectID="60686"/>
    <cge:TPSR_Ref TObjectID="40214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-240254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -768.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40214"/>
     <cge:Term_Ref ObjectID="60686"/>
    <cge:TPSR_Ref TObjectID="40214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-240255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -768.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40214"/>
     <cge:Term_Ref ObjectID="60686"/>
    <cge:TPSR_Ref TObjectID="40214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-240259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -768.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40214"/>
     <cge:Term_Ref ObjectID="60686"/>
    <cge:TPSR_Ref TObjectID="40214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-240256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -768.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40214"/>
     <cge:Term_Ref ObjectID="60686"/>
    <cge:TPSR_Ref TObjectID="40214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-240260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -293.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40215"/>
     <cge:Term_Ref ObjectID="60687"/>
    <cge:TPSR_Ref TObjectID="40215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-240261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -293.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40215"/>
     <cge:Term_Ref ObjectID="60687"/>
    <cge:TPSR_Ref TObjectID="40215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-240262" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -293.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40215"/>
     <cge:Term_Ref ObjectID="60687"/>
    <cge:TPSR_Ref TObjectID="40215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-240266" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -293.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40215"/>
     <cge:Term_Ref ObjectID="60687"/>
    <cge:TPSR_Ref TObjectID="40215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-240263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -293.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40215"/>
     <cge:Term_Ref ObjectID="60687"/>
    <cge:TPSR_Ref TObjectID="40215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-240267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 -291.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40216"/>
     <cge:Term_Ref ObjectID="60688"/>
    <cge:TPSR_Ref TObjectID="40216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-240268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 -291.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40216"/>
     <cge:Term_Ref ObjectID="60688"/>
    <cge:TPSR_Ref TObjectID="40216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-240269" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 -291.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240269" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40216"/>
     <cge:Term_Ref ObjectID="60688"/>
    <cge:TPSR_Ref TObjectID="40216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-240273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 -291.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40216"/>
     <cge:Term_Ref ObjectID="60688"/>
    <cge:TPSR_Ref TObjectID="40216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-240270" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 -291.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240270" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40216"/>
     <cge:Term_Ref ObjectID="60688"/>
    <cge:TPSR_Ref TObjectID="40216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-240295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 162.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40167"/>
     <cge:Term_Ref ObjectID="60590"/>
    <cge:TPSR_Ref TObjectID="40167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 162.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40167"/>
     <cge:Term_Ref ObjectID="60590"/>
    <cge:TPSR_Ref TObjectID="40167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-240292" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 162.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40167"/>
     <cge:Term_Ref ObjectID="60590"/>
    <cge:TPSR_Ref TObjectID="40167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-240198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -626.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40217"/>
     <cge:Term_Ref ObjectID="60689"/>
    <cge:TPSR_Ref TObjectID="40217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-240199" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -626.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240199" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40217"/>
     <cge:Term_Ref ObjectID="60689"/>
    <cge:TPSR_Ref TObjectID="40217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-240189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -626.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40217"/>
     <cge:Term_Ref ObjectID="60689"/>
    <cge:TPSR_Ref TObjectID="40217"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-240087">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1507.000000 -980.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40148" ObjectName="SW-CX_FHS.CX_FHS_3716SW"/>
     <cge:Meas_Ref ObjectId="240087"/>
    <cge:TPSR_Ref TObjectID="40148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240090">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1529.000000 -1038.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40152" ObjectName="SW-CX_FHS.CX_FHS_37167SW"/>
     <cge:Meas_Ref ObjectId="240090"/>
    <cge:TPSR_Ref TObjectID="40152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240089">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.000000 -966.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40151" ObjectName="SW-CX_FHS.CX_FHS_37160SW"/>
     <cge:Meas_Ref ObjectId="240089"/>
    <cge:TPSR_Ref TObjectID="40151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240072">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.594799 -215.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40139" ObjectName="SW-CX_FHS.CX_FHS_001XC"/>
     <cge:Meas_Ref ObjectId="240072"/>
    <cge:TPSR_Ref TObjectID="40139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240072">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.594799 -280.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40140" ObjectName="SW-CX_FHS.CX_FHS_001XC1"/>
     <cge:Meas_Ref ObjectId="240072"/>
    <cge:TPSR_Ref TObjectID="40140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240113">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 660.000000 -40.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40162" ObjectName="SW-CX_FHS.CX_FHS_03260SW"/>
     <cge:Meas_Ref ObjectId="240113"/>
    <cge:TPSR_Ref TObjectID="40162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 617.763970 -150.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40160" ObjectName="SW-CX_FHS.CX_FHS_032XC"/>
     <cge:Meas_Ref ObjectId="240112"/>
    <cge:TPSR_Ref TObjectID="40160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 617.763970 -85.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40161" ObjectName="SW-CX_FHS.CX_FHS_032XC1"/>
     <cge:Meas_Ref ObjectId="240112"/>
    <cge:TPSR_Ref TObjectID="40161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.091742 -91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40199" ObjectName="SW-CX_FHS.CX_FHS_012XC1"/>
     <cge:Meas_Ref ObjectId="240159"/>
    <cge:TPSR_Ref TObjectID="40199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1373.091742 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40200" ObjectName="SW-CX_FHS.CX_FHS_0121SW"/>
     <cge:Meas_Ref ObjectId="240160"/>
    <cge:TPSR_Ref TObjectID="40200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1373.091742 -98.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40200" ObjectName="SW-CX_FHS.CX_FHS_0121SW"/>
     <cge:Meas_Ref ObjectId="240160"/>
    <cge:TPSR_Ref TObjectID="40200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.091742 -156.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40198" ObjectName="SW-CX_FHS.CX_FHS_012XC"/>
     <cge:Meas_Ref ObjectId="240159"/>
    <cge:TPSR_Ref TObjectID="40198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1778.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40178" ObjectName="SW-CX_FHS.CX_FHS_04160SW"/>
     <cge:Meas_Ref ObjectId="240133"/>
    <cge:TPSR_Ref TObjectID="40178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1740.118337 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40176" ObjectName="SW-CX_FHS.CX_FHS_041XC"/>
     <cge:Meas_Ref ObjectId="240132"/>
    <cge:TPSR_Ref TObjectID="40176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1740.118337 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40177" ObjectName="SW-CX_FHS.CX_FHS_041XC1"/>
     <cge:Meas_Ref ObjectId="240132"/>
    <cge:TPSR_Ref TObjectID="40177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240143">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2071.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40186" ObjectName="SW-CX_FHS.CX_FHS_04360SW"/>
     <cge:Meas_Ref ObjectId="240143"/>
    <cge:TPSR_Ref TObjectID="40186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2029.286780 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40184" ObjectName="SW-CX_FHS.CX_FHS_043XC"/>
     <cge:Meas_Ref ObjectId="240142"/>
    <cge:TPSR_Ref TObjectID="40184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2029.286780 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40185" ObjectName="SW-CX_FHS.CX_FHS_043XC1"/>
     <cge:Meas_Ref ObjectId="240142"/>
    <cge:TPSR_Ref TObjectID="40185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1931.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40182" ObjectName="SW-CX_FHS.CX_FHS_04260SW"/>
     <cge:Meas_Ref ObjectId="240138"/>
    <cge:TPSR_Ref TObjectID="40182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.689765 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40180" ObjectName="SW-CX_FHS.CX_FHS_042XC"/>
     <cge:Meas_Ref ObjectId="240137"/>
    <cge:TPSR_Ref TObjectID="40180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.689765 -84.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40181" ObjectName="SW-CX_FHS.CX_FHS_042XC1"/>
     <cge:Meas_Ref ObjectId="240137"/>
    <cge:TPSR_Ref TObjectID="40181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240177">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2533.892324 -158.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40209" ObjectName="SW-CX_FHS.CX_FHS_0461XC"/>
     <cge:Meas_Ref ObjectId="240177"/>
    <cge:TPSR_Ref TObjectID="40209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240177">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2533.892324 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40210" ObjectName="SW-CX_FHS.CX_FHS_0461XC1"/>
     <cge:Meas_Ref ObjectId="240177"/>
    <cge:TPSR_Ref TObjectID="40210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240154">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2401.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40194" ObjectName="SW-CX_FHS.CX_FHS_04560SW"/>
     <cge:Meas_Ref ObjectId="240154"/>
    <cge:TPSR_Ref TObjectID="40194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240153">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2352.189765 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40192" ObjectName="SW-CX_FHS.CX_FHS_045XC"/>
     <cge:Meas_Ref ObjectId="240153"/>
    <cge:TPSR_Ref TObjectID="40192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240156">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2312.000000 55.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40196" ObjectName="SW-CX_FHS.CX_FHS_04567SW"/>
     <cge:Meas_Ref ObjectId="240156"/>
    <cge:TPSR_Ref TObjectID="40196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240155">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.189765 54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40195" ObjectName="SW-CX_FHS.CX_FHS_0456SW"/>
     <cge:Meas_Ref ObjectId="240155"/>
    <cge:TPSR_Ref TObjectID="40195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240153">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2352.189765 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40193" ObjectName="SW-CX_FHS.CX_FHS_045XC1"/>
     <cge:Meas_Ref ObjectId="240153"/>
    <cge:TPSR_Ref TObjectID="40193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.708955 -214.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40145" ObjectName="SW-CX_FHS.CX_FHS_002XC"/>
     <cge:Meas_Ref ObjectId="240080"/>
    <cge:TPSR_Ref TObjectID="40145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.708955 -279.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40146" ObjectName="SW-CX_FHS.CX_FHS_002XC1"/>
     <cge:Meas_Ref ObjectId="240080"/>
    <cge:TPSR_Ref TObjectID="40146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240070">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.594799 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40137" ObjectName="SW-CX_FHS.CX_FHS_301XC1"/>
     <cge:Meas_Ref ObjectId="240070"/>
    <cge:TPSR_Ref TObjectID="40137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240070">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.594799 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40136" ObjectName="SW-CX_FHS.CX_FHS_301XC"/>
     <cge:Meas_Ref ObjectId="240070"/>
    <cge:TPSR_Ref TObjectID="40136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240088">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1506.000000 -726.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40149" ObjectName="SW-CX_FHS.CX_FHS_371XC"/>
     <cge:Meas_Ref ObjectId="240088"/>
    <cge:TPSR_Ref TObjectID="40149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240088">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1506.000000 -794.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40150" ObjectName="SW-CX_FHS.CX_FHS_371XC1"/>
     <cge:Meas_Ref ObjectId="240088"/>
    <cge:TPSR_Ref TObjectID="40150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240078">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.708955 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40143" ObjectName="SW-CX_FHS.CX_FHS_302XC1"/>
     <cge:Meas_Ref ObjectId="240078"/>
    <cge:TPSR_Ref TObjectID="40143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240078">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.708955 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40142" ObjectName="SW-CX_FHS.CX_FHS_302XC"/>
     <cge:Meas_Ref ObjectId="240078"/>
    <cge:TPSR_Ref TObjectID="40142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240128">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1190.000000 -38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40174" ObjectName="SW-CX_FHS.CX_FHS_03660SW"/>
     <cge:Meas_Ref ObjectId="240128"/>
    <cge:TPSR_Ref TObjectID="40174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.577072 -148.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40172" ObjectName="SW-CX_FHS.CX_FHS_036XC"/>
     <cge:Meas_Ref ObjectId="240127"/>
    <cge:TPSR_Ref TObjectID="40172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.577072 -83.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40173" ObjectName="SW-CX_FHS.CX_FHS_036XC1"/>
     <cge:Meas_Ref ObjectId="240127"/>
    <cge:TPSR_Ref TObjectID="40173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40156" ObjectName="SW-CX_FHS.CX_FHS_03160SW"/>
     <cge:Meas_Ref ObjectId="240107"/>
    <cge:TPSR_Ref TObjectID="40156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.449580 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40154" ObjectName="SW-CX_FHS.CX_FHS_031XC"/>
     <cge:Meas_Ref ObjectId="240106"/>
    <cge:TPSR_Ref TObjectID="40154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 424.000000 55.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40158" ObjectName="SW-CX_FHS.CX_FHS_0367SW"/>
     <cge:Meas_Ref ObjectId="240109"/>
    <cge:TPSR_Ref TObjectID="40158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240108">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 471.449580 54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40157" ObjectName="SW-CX_FHS.CX_FHS_0316SW"/>
     <cge:Meas_Ref ObjectId="240108"/>
    <cge:TPSR_Ref TObjectID="40157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.449580 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40155" ObjectName="SW-CX_FHS.CX_FHS_031XC1"/>
     <cge:Meas_Ref ObjectId="240106"/>
    <cge:TPSR_Ref TObjectID="40155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240166">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.892324 -629.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40201" ObjectName="SW-CX_FHS.CX_FHS_3901XC"/>
     <cge:Meas_Ref ObjectId="240166"/>
    <cge:TPSR_Ref TObjectID="40201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240166">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.892324 -582.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40202" ObjectName="SW-CX_FHS.CX_FHS_3901XC1"/>
     <cge:Meas_Ref ObjectId="240166"/>
    <cge:TPSR_Ref TObjectID="40202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40166" ObjectName="SW-CX_FHS.CX_FHS_03360SW"/>
     <cge:Meas_Ref ObjectId="240118"/>
    <cge:TPSR_Ref TObjectID="40166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240117">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 767.947280 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40164" ObjectName="SW-CX_FHS.CX_FHS_033XC"/>
     <cge:Meas_Ref ObjectId="240117"/>
    <cge:TPSR_Ref TObjectID="40164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240117">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 767.947280 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40165" ObjectName="SW-CX_FHS.CX_FHS_033XC1"/>
     <cge:Meas_Ref ObjectId="240117"/>
    <cge:TPSR_Ref TObjectID="40165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 963.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40170" ObjectName="SW-CX_FHS.CX_FHS_03460SW"/>
     <cge:Meas_Ref ObjectId="240123"/>
    <cge:TPSR_Ref TObjectID="40170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 923.859443 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40168" ObjectName="SW-CX_FHS.CX_FHS_034XC"/>
     <cge:Meas_Ref ObjectId="240122"/>
    <cge:TPSR_Ref TObjectID="40168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 923.859443 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40169" ObjectName="SW-CX_FHS.CX_FHS_034XC1"/>
     <cge:Meas_Ref ObjectId="240122"/>
    <cge:TPSR_Ref TObjectID="40169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240148">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2232.000000 -41.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40190" ObjectName="SW-CX_FHS.CX_FHS_04460SW"/>
     <cge:Meas_Ref ObjectId="240148"/>
    <cge:TPSR_Ref TObjectID="40190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240147">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2190.286780 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40188" ObjectName="SW-CX_FHS.CX_FHS_044XC"/>
     <cge:Meas_Ref ObjectId="240147"/>
    <cge:TPSR_Ref TObjectID="40188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240147">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2190.286780 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40189" ObjectName="SW-CX_FHS.CX_FHS_044XC1"/>
     <cge:Meas_Ref ObjectId="240147"/>
    <cge:TPSR_Ref TObjectID="40189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240176">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.224717 -162.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40207" ObjectName="SW-CX_FHS.CX_FHS_0351XC"/>
     <cge:Meas_Ref ObjectId="240176"/>
    <cge:TPSR_Ref TObjectID="40207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240176">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.224717 -92.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40208" ObjectName="SW-CX_FHS.CX_FHS_0351XC1"/>
     <cge:Meas_Ref ObjectId="240176"/>
    <cge:TPSR_Ref TObjectID="40208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240167">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1347.733420 -111.500000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40204" ObjectName="SW-CX_FHS.CX_FHS_0901XC1"/>
     <cge:Meas_Ref ObjectId="240167"/>
    <cge:TPSR_Ref TObjectID="40204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240167">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1347.733420 -158.500000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40203" ObjectName="SW-CX_FHS.CX_FHS_0901XC"/>
     <cge:Meas_Ref ObjectId="240167"/>
    <cge:TPSR_Ref TObjectID="40203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240168">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1647.107676 -129.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40206" ObjectName="SW-CX_FHS.CX_FHS_0902XC1"/>
     <cge:Meas_Ref ObjectId="240168"/>
    <cge:TPSR_Ref TObjectID="40206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-240168">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1647.107676 -176.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40205" ObjectName="SW-CX_FHS.CX_FHS_0902XC"/>
     <cge:Meas_Ref ObjectId="240168"/>
    <cge:TPSR_Ref TObjectID="40205"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 -1111.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.763970 109.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.000000 111.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.577072 107.277778)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1745.118337 109.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1896.689765 114.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2034.286780 108.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2540.000000 113.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.947280 107.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 928.859443 107.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2195.286780 109.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1b25770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-648 1165,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40136@0" ObjectIDZND0="40214@0" Pin0InfoVect0LinkObjId="g_1b5e9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-648 1165,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a69fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1569,-971 1583,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40151@1" ObjectIDZND0="g_1b991d0@0" Pin0InfoVect0LinkObjId="g_1b991d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240089_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-971 1583,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1addf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1570,-1043 1583,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40152@1" ObjectIDZND0="g_1b72690@0" Pin0InfoVect0LinkObjId="g_1b72690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240090_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1570,-1043 1583,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b5e9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-653 1441,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40201@0" ObjectIDZND0="40214@0" Pin0InfoVect0LinkObjId="g_1b25770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-653 1441,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb6780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-276 1165,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40138@1" ObjectIDZND0="40140@1" Pin0InfoVect0LinkObjId="SW-240072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240071_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-276 1165,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb6970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-239 1165,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40139@1" ObjectIDZND0="40138@0" Pin0InfoVect0LinkObjId="SW-240071_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240072_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-239 1165,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb6b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-222 1165,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40139@0" ObjectIDZND0="40215@0" Pin0InfoVect0LinkObjId="g_1b6cab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-222 1165,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a00540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-146 628,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40159@1" ObjectIDZND0="40160@1" Pin0InfoVect0LinkObjId="SW-240112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240111_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,-146 628,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a00730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-109 628,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40161@1" ObjectIDZND0="40159@0" Pin0InfoVect0LinkObjId="SW-240111_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240112_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,-109 628,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b80bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-36 669,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b80440@0" ObjectIDZND0="40162@0" Pin0InfoVect0LinkObjId="SW-240113_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b80440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="669,-36 669,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b6cab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-174 628,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40160@0" ObjectIDZND0="40215@0" Pin0InfoVect0LinkObjId="g_1bb6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,-174 628,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1d7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-92 628,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40161@0" ObjectIDZND0="g_1a00920@1" Pin0InfoVect0LinkObjId="g_1a00920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,-92 628,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a51cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1538,-152 1538,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40197@1" ObjectIDZND0="40198@1" Pin0InfoVect0LinkObjId="SW-240159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240158_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1538,-152 1538,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a51eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1538,-115 1538,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40199@1" ObjectIDZND0="40197@0" Pin0InfoVect0LinkObjId="SW-240158_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240159_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1538,-115 1538,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a520a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1538,-180 1538,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40198@0" ObjectIDZND0="40216@0" Pin0InfoVect0LinkObjId="g_1b0e9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240159_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1538,-180 1538,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a52290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1383,-173 1383,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40200@0" ObjectIDZND0="40215@0" Pin0InfoVect0LinkObjId="g_1bb6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1383,-173 1383,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a52480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1383,-105 1383,-81 1538,-81 1538,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40200@0" ObjectIDZND0="40199@0" Pin0InfoVect0LinkObjId="SW-240159_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1383,-105 1383,-81 1538,-81 1538,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b58be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1383,-156 1383,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40200@1" ObjectIDZND0="40200@1" Pin0InfoVect0LinkObjId="SW-240160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240160_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1383,-156 1383,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b5b250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1750,-148 1750,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40175@1" ObjectIDZND0="40176@1" Pin0InfoVect0LinkObjId="SW-240132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240131_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1750,-148 1750,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b5b470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1750,-111 1750,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40177@1" ObjectIDZND0="40175@0" Pin0InfoVect0LinkObjId="SW-240131_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240132_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1750,-111 1750,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a701f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1787,-38 1787,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a6f900@0" ObjectIDZND0="40178@0" Pin0InfoVect0LinkObjId="SW-240133_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a6f900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1787,-38 1787,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b126c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1720,-83 1787,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a70410@0" ObjectIDZND0="40178@1" Pin0InfoVect0LinkObjId="SW-240133_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a70410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1720,-83 1787,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b128e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1750,-94 1750,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40177@0" ObjectIDZND0="g_1b5b690@1" Pin0InfoVect0LinkObjId="g_1b5b690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1750,-94 1750,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b0dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2080,-38 2080,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b0d350@0" ObjectIDZND0="40186@0" Pin0InfoVect0LinkObjId="SW-240143_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b0d350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2080,-38 2080,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b0e9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2039,-176 2039,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40184@0" ObjectIDZND0="40216@0" Pin0InfoVect0LinkObjId="g_1a520a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2039,-176 2039,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b31b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,-83 2080,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b0de60@0" ObjectIDZND0="40186@1" Pin0InfoVect0LinkObjId="SW-240143_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b0de60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2013,-83 2080,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b31da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2039,-94 2039,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40185@0" ObjectIDZND0="g_1a54440@1" Pin0InfoVect0LinkObjId="g_1a54440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2039,-94 2039,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af8c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2039,-148 2039,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40183@1" ObjectIDZND0="40184@1" Pin0InfoVect0LinkObjId="SW-240142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240141_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2039,-148 2039,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af8ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2039,-111 2039,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40185@1" ObjectIDZND0="40183@0" Pin0InfoVect0LinkObjId="SW-240141_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240142_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2039,-111 2039,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1acc100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1940,-35 1940,-43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1acb760@0" ObjectIDZND0="40182@0" Pin0InfoVect0LinkObjId="SW-240138_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acb760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1940,-35 1940,-43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1acd010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-173 1902,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40180@0" ObjectIDZND0="40216@0" Pin0InfoVect0LinkObjId="g_1a520a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240137_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-173 1902,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b825f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-80 1940,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1acc360@0" ObjectIDZND0="40182@1" Pin0InfoVect0LinkObjId="SW-240138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acc360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-80 1940,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b82830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-91 1902,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40181@0" ObjectIDZND0="g_1af9150@1" Pin0InfoVect0LinkObjId="g_1af9150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240137_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-91 1902,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a44bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-145 1902,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40179@1" ObjectIDZND0="40180@1" Pin0InfoVect0LinkObjId="SW-240137_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240136_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-145 1902,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a44e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-108 1902,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40181@1" ObjectIDZND0="40179@0" Pin0InfoVect0LinkObjId="SW-240136_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240137_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-108 1902,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af52c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-147 2362,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40191@1" ObjectIDZND0="40192@1" Pin0InfoVect0LinkObjId="SW-240153_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240152_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-147 2362,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af5520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-110 2362,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40193@1" ObjectIDZND0="40191@0" Pin0InfoVect0LinkObjId="SW-240152_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240153_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-110 2362,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1afded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2410,-38 2410,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1afd4f0@0" ObjectIDZND0="40194@0" Pin0InfoVect0LinkObjId="SW-240154_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1afd4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2410,-38 2410,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1afe130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-14 2362,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1af5780@0" ObjectIDZND0="40195@1" Pin0InfoVect0LinkObjId="SW-240155_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1af5780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-14 2362,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1afe390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-175 2362,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40192@0" ObjectIDZND0="40216@0" Pin0InfoVect0LinkObjId="g_1a520a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-175 2362,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b138b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2345,5 2345,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b56d60@0" ObjectIDZND0="40196@1" Pin0InfoVect0LinkObjId="SW-240156_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b56d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2345,5 2345,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b160e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,-93 2362,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40193@0" ObjectIDZND0="g_1af5780@1" Pin0InfoVect0LinkObjId="g_1af5780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,-93 2362,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad4c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-275 1902,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40144@1" ObjectIDZND0="40146@1" Pin0InfoVect0LinkObjId="SW-240080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-275 1902,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad4ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-238 1902,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40145@1" ObjectIDZND0="40144@0" Pin0InfoVect0LinkObjId="SW-240079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-238 1902,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b08ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-732 1516,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40149@0" ObjectIDZND0="40214@0" Pin0InfoVect0LinkObjId="g_1b25770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-732 1516,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b09120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-857 1583,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1a6dac0@0" ObjectIDND1="g_1b368f0@0" ObjectIDND2="40150@x" ObjectIDZND0="g_1b6c240@0" Pin0InfoVect0LinkObjId="g_1b6c240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a6dac0_0" Pin1InfoVect1LinkObjId="g_1b368f0_0" Pin1InfoVect2LinkObjId="SW-240088_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-857 1583,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b09380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-869 1516,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_1a6dac0@0" ObjectIDZND0="g_1b6c240@0" ObjectIDZND1="g_1b368f0@0" ObjectIDZND2="40150@x" Pin0InfoVect0LinkObjId="g_1b6c240_0" Pin0InfoVect1LinkObjId="g_1b368f0_0" Pin0InfoVect2LinkObjId="SW-240088_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a6dac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-869 1516,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b095e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1522,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1522,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b09840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-572 1392,-572 1392,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="40202@x" ObjectIDND1="g_1b5ebe0@0" ObjectIDZND0="g_1b5f110@0" Pin0InfoVect0LinkObjId="g_1b5f110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240166_0" Pin1InfoVect1LinkObjId="g_1b5ebe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-572 1392,-572 1392,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b09aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-588 1441,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="40202@0" ObjectIDZND0="g_1b5f110@0" ObjectIDZND1="g_1b5ebe0@0" Pin0InfoVect0LinkObjId="g_1b5f110_0" Pin0InfoVect1LinkObjId="g_1b5ebe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-588 1441,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b09d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-572 1441,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1b5f110@0" ObjectIDND1="40202@x" ObjectIDZND0="g_1b5ebe0@0" Pin0InfoVect0LinkObjId="g_1b5ebe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b5f110_0" Pin1InfoVect1LinkObjId="SW-240166_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-572 1441,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1adff60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-522 1441,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1b5ebe0@1" ObjectIDZND0="g_1b09f60@0" Pin0InfoVect0LinkObjId="g_1b09f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b5ebe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-522 1441,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b01b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-617 1165,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40217@1" ObjectIDZND0="40136@1" Pin0InfoVect0LinkObjId="SW-240070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240069_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-617 1165,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b01db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-580 1165,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40137@1" ObjectIDZND0="40217@0" Pin0InfoVect0LinkObjId="SW-240069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-580 1165,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6a340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-787 1516,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40147@1" ObjectIDZND0="40150@1" Pin0InfoVect0LinkObjId="SW-240088_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240086_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-787 1516,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6a5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-750 1516,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40149@1" ObjectIDZND0="40147@0" Pin0InfoVect0LinkObjId="SW-240086_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-750 1516,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a6d600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a6d860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6e4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-971 1516,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="40148@x" ObjectIDND1="40151@x" ObjectIDZND0="g_1a6dac0@1" Pin0InfoVect0LinkObjId="g_1a6dac0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240087_0" Pin1InfoVect1LinkObjId="SW-240089_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-971 1516,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6e740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-985 1516,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="40148@0" ObjectIDZND0="g_1a6dac0@0" ObjectIDZND1="40151@x" Pin0InfoVect0LinkObjId="g_1a6dac0_0" Pin0InfoVect1LinkObjId="SW-240089_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240087_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-985 1516,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6e9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-971 1533,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="40148@x" ObjectIDND1="g_1a6dac0@0" ObjectIDZND0="40151@0" Pin0InfoVect0LinkObjId="SW-240089_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240087_0" Pin1InfoVect1LinkObjId="g_1a6dac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-971 1533,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a6ec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-563 1165,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40137@0" ObjectIDZND0="g_1a99c90@1" Pin0InfoVect0LinkObjId="g_1a99c90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-563 1165,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b184f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-479 1165,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1a99c90@0" ObjectIDZND0="40211@1" Pin0InfoVect0LinkObjId="g_1a80ff0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a99c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-479 1165,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b40f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-617 1902,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40141@1" ObjectIDZND0="40142@1" Pin0InfoVect0LinkObjId="SW-240078_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-617 1902,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b411b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-580 1902,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40143@1" ObjectIDZND0="40141@0" Pin0InfoVect0LinkObjId="SW-240077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240078_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-580 1902,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b44250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b444b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-680 1902,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40214@0" ObjectIDZND0="40142@0" Pin0InfoVect0LinkObjId="SW-240078_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b25770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-680 1902,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b44710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1861,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1861,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b44970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-481 1902,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1a9a6b0@0" ObjectIDZND0="40212@1" Pin0InfoVect0LinkObjId="g_1a81ed0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a9a6b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-481 1902,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b44bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-563 1902,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40143@0" ObjectIDZND0="g_1a9a6b0@1" Pin0InfoVect0LinkObjId="g_1a9a6b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-563 1902,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1f830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,88 628,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1a00920@0" Pin0InfoVect0LinkObjId="g_1a00920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b368f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,88 628,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b206a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1059,67 1059,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="load" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1b368f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1059,67 1059,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b20900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-186 1056,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40207@0" ObjectIDZND0="40215@0" Pin0InfoVect0LinkObjId="g_1bb6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-186 1056,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b20b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="602,-81 669,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b80da0@0" ObjectIDZND0="40162@1" Pin0InfoVect0LinkObjId="SW-240113_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b80da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="602,-81 669,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b22ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1164,-144 1164,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40171@1" ObjectIDZND0="40172@1" Pin0InfoVect0LinkObjId="SW-240127_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-144 1164,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b23100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1164,-107 1164,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40173@1" ObjectIDZND0="40171@0" Pin0InfoVect0LinkObjId="SW-240126_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240127_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-107 1164,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a1b360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1199,-34 1199,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a1a910@0" ObjectIDZND0="40174@0" Pin0InfoVect0LinkObjId="SW-240128_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a1a910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1199,-34 1199,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a1c2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1164,-172 1164,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40172@0" ObjectIDZND0="40215@0" Pin0InfoVect0LinkObjId="g_1bb6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-172 1164,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8dd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1164,-90 1164,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40173@0" ObjectIDZND0="g_1a92cf0@1" Pin0InfoVect0LinkObjId="g_1a92cf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-90 1164,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a91c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1132,-79 1199,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a1b5c0@0" ObjectIDZND0="40174@1" Pin0InfoVect0LinkObjId="SW-240128_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a1b5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1132,-79 1199,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a92a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1750,-15 1750,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1b5b690@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1b368f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b5b690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1750,-15 1750,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a93770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1164,-30 1164,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1a92cf0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1b368f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a92cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1164,-30 1164,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa9140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-12 1902,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1af9150@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1b368f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1af9150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-12 1902,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa9fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2039,-15 2039,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1a54440@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1b368f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a54440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2039,-15 2039,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1aacac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2545,109 2545,61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2545,109 2545,61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aacd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2508,-61 2544,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1b62730@0" ObjectIDZND0="40210@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-240177_0" Pin0InfoVect1LinkObjId="g_1b368f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b62730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2508,-61 2544,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aacf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-95 2544,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="40210@0" ObjectIDZND0="g_1b62730@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1b62730_0" Pin0InfoVect1LinkObjId="g_1b368f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240177_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-95 2544,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aad1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-61 2544,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_1b62730@0" ObjectIDND1="40210@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1b368f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b62730_0" Pin1InfoVect1LinkObjId="SW-240177_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-61 2544,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a01df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,49 2362,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="40195@0" ObjectIDZND0="40314@0" Pin0InfoVect0LinkObjId="CB-CX_FHS.CX_FHS_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240155_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,49 2362,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a02050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2400,58 2346,58 2345,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a73780@0" ObjectIDZND0="40196@0" Pin0InfoVect0LinkObjId="SW-240156_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a73780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2400,58 2346,58 2345,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a022b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2321,50 2321,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1a01750@0" Pin0InfoVect0LinkObjId="g_1a01750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2321,50 2321,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a02510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2324,-80 2410,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c0ff70@0" ObjectIDZND0="40194@1" Pin0InfoVect0LinkObjId="SW-240154_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c0ff70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2324,-80 2410,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a04850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-147 480,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40153@1" ObjectIDZND0="40154@1" Pin0InfoVect0LinkObjId="SW-240106_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240105_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,-147 480,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a04ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-110 480,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40155@1" ObjectIDZND0="40153@0" Pin0InfoVect0LinkObjId="SW-240105_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240106_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,-110 480,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a07f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="522,-38 522,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a07510@0" ObjectIDZND0="40156@0" Pin0InfoVect0LinkObjId="SW-240107_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a07510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="522,-38 522,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a081c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-175 480,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40154@0" ObjectIDZND0="40215@0" Pin0InfoVect0LinkObjId="g_1bb6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,-175 480,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a110b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="457,5 457,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a0fb90@0" ObjectIDZND0="40158@1" Pin0InfoVect0LinkObjId="SW-240109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0fb90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="457,5 457,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a98890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,49 480,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="40157@0" ObjectIDZND0="40313@0" Pin0InfoVect0LinkObjId="CB-CX_FHS.CX_FHS_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240108_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,49 480,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a98af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="512,58 458,58 457,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1aa74d0@0" ObjectIDZND0="40158@0" Pin0InfoVect0LinkObjId="SW-240109_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa74d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="512,58 458,58 457,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a98d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-80 522,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1aa67a0@0" ObjectIDZND0="40156@1" Pin0InfoVect0LinkObjId="SW-240107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa67a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-80 522,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a99a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-93 480,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40155@0" ObjectIDZND0="g_1a98fb0@1" Pin0InfoVect0LinkObjId="g_1a98fb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,-93 480,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a9b0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-221 1902,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40145@0" ObjectIDZND0="40216@0" Pin0InfoVect0LinkObjId="g_1a520a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-221 1902,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a9b330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1750,-176 1750,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40176@0" ObjectIDZND0="40216@0" Pin0InfoVect0LinkObjId="g_1a520a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1750,-176 1750,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a9cf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-1020 1516,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="40148@1" ObjectIDZND0="40152@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1aa4ea0@0" Pin0InfoVect0LinkObjId="SW-240090_0" Pin0InfoVect1LinkObjId="g_1b368f0_0" Pin0InfoVect2LinkObjId="g_1aa4ea0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240087_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-1020 1516,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a9d0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-1043 1534,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="40148@x" ObjectIDND1="0@x" ObjectIDND2="g_1aa4ea0@0" ObjectIDZND0="40152@0" Pin0InfoVect0LinkObjId="SW-240090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-240087_0" Pin1InfoVect1LinkObjId="g_1b368f0_0" Pin1InfoVect2LinkObjId="g_1aa4ea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-1043 1534,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a9e380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="433,50 433,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1a19000@0" Pin0InfoVect0LinkObjId="g_1a19000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="433,50 433,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa4330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-606 1441,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40202@1" ObjectIDZND0="40201@1" Pin0InfoVect0LinkObjId="SW-240166_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240166_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-606 1441,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa5960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-1116 1516,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1aa4ea0@0" ObjectIDZND1="40152@x" ObjectIDZND2="40148@x" Pin0InfoVect0LinkObjId="g_1aa4ea0_0" Pin0InfoVect1LinkObjId="SW-240090_0" Pin0InfoVect2LinkObjId="SW-240087_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b368f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-1116 1516,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa5bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-1078 1516,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_1aa4ea0@0" ObjectIDZND0="40152@x" ObjectIDZND1="40148@x" Pin0InfoVect0LinkObjId="SW-240090_0" Pin0InfoVect1LinkObjId="SW-240087_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b368f0_0" Pin1InfoVect1LinkObjId="g_1aa4ea0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-1078 1516,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa5e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-1078 1468,-1078 1468,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="40152@x" ObjectIDND2="40148@x" ObjectIDZND0="g_1aa4ea0@0" Pin0InfoVect0LinkObjId="g_1aa4ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b368f0_0" Pin1InfoVect1LinkObjId="SW-240090_0" Pin1InfoVect2LinkObjId="SW-240087_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-1078 1468,-1078 1468,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa6080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-857 1516,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_1a6dac0@0" ObjectIDND1="g_1b6c240@0" ObjectIDND2="g_1b368f0@0" ObjectIDZND0="40150@0" Pin0InfoVect0LinkObjId="SW-240088_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a6dac0_0" Pin1InfoVect1LinkObjId="g_1b6c240_0" Pin1InfoVect2LinkObjId="g_1b368f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-857 1516,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa62e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-857 1459,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1a6dac0@0" ObjectIDND1="g_1b6c240@0" ObjectIDND2="40150@x" ObjectIDZND0="g_1b368f0@0" Pin0InfoVect0LinkObjId="g_1b368f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a6dac0_0" Pin1InfoVect1LinkObjId="g_1b6c240_0" Pin1InfoVect2LinkObjId="SW-240088_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-857 1459,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa6540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,13 480,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40157@1" ObjectIDZND0="g_1a98fb0@0" Pin0InfoVect0LinkObjId="g_1a98fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240108_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,13 480,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d4bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-148 778,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40163@1" ObjectIDZND0="40164@1" Pin0InfoVect0LinkObjId="SW-240117_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240116_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-148 778,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d4e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-111 778,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40165@1" ObjectIDZND0="40163@0" Pin0InfoVect0LinkObjId="SW-240116_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240117_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-111 778,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d88f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-38 817,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19d7f00@0" ObjectIDZND0="40166@0" Pin0InfoVect0LinkObjId="SW-240118_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d7f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-38 817,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d97c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-176 778,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40164@0" ObjectIDZND0="40215@0" Pin0InfoVect0LinkObjId="g_1bb6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240117_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-176 778,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dc590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-94 778,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40165@0" ObjectIDZND0="g_19d5080@1" Pin0InfoVect0LinkObjId="g_19d5080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240117_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-94 778,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dff10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,86 778,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_19d5080@0" Pin0InfoVect0LinkObjId="g_19d5080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b368f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,86 778,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e0170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="750,-83 817,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_19d8b50@0" ObjectIDZND0="40166@1" Pin0InfoVect0LinkObjId="SW-240118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d8b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="750,-83 817,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e23f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,-148 934,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40167@1" ObjectIDZND0="40168@1" Pin0InfoVect0LinkObjId="SW-240122_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240121_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="934,-148 934,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e2650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,-111 934,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40169@1" ObjectIDZND0="40167@0" Pin0InfoVect0LinkObjId="SW-240121_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240122_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="934,-111 934,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e6120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="972,-38 972,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19e5730@0" ObjectIDZND0="40170@0" Pin0InfoVect0LinkObjId="SW-240123_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19e5730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="972,-38 972,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bfb3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,-176 934,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40168@0" ObjectIDZND0="40215@0" Pin0InfoVect0LinkObjId="g_1bb6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240122_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="934,-176 934,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bfdff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,-94 934,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40169@0" ObjectIDZND0="g_19e28b0@1" Pin0InfoVect0LinkObjId="g_19e28b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240122_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="934,-94 934,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c01970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,86 934,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_19e28b0@0" Pin0InfoVect0LinkObjId="g_19e28b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b368f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="934,86 934,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c01bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-83 972,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_19e6380@0" ObjectIDZND0="40170@1" Pin0InfoVect0LinkObjId="SW-240123_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19e6380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-83 972,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c061a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2241,-37 2241,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c057b0@0" ObjectIDZND0="40190@0" Pin0InfoVect0LinkObjId="SW-240148_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c057b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2241,-37 2241,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c07070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2200,-179 2200,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40188@0" ObjectIDZND0="40216@0" Pin0InfoVect0LinkObjId="g_1a520a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2200,-179 2200,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c09d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2174,-82 2241,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c06400@0" ObjectIDZND0="40190@1" Pin0InfoVect0LinkObjId="SW-240148_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c06400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2174,-82 2241,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c09f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2200,-93 2200,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40189@0" ObjectIDZND0="g_1c02a30@1" Pin0InfoVect0LinkObjId="g_1c02a30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2200,-93 2200,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c0e990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2200,-147 2200,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40187@1" ObjectIDZND0="40188@1" Pin0InfoVect0LinkObjId="SW-240147_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240146_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2200,-147 2200,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c0ebf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2200,-110 2200,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40189@1" ObjectIDZND0="40187@0" Pin0InfoVect0LinkObjId="SW-240146_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240147_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2200,-110 2200,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c0fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2200,-15 2200,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1c02a30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1b368f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c02a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2200,-15 2200,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a74ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-165 2544,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40209@1" ObjectIDZND0="g_1a743f0@0" Pin0InfoVect0LinkObjId="g_1a743f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240177_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-165 2544,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a74f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-112 2544,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40210@1" ObjectIDZND0="g_1a743f0@1" Pin0InfoVect0LinkObjId="g_1a743f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240177_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-112 2544,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a751a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2544,-182 2544,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40209@0" ObjectIDZND0="40216@0" Pin0InfoVect0LinkObjId="g_1a520a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240177_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2544,-182 2544,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7b820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-169 1056,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40207@1" ObjectIDZND0="g_1a7ae50@0" Pin0InfoVect0LinkObjId="g_1a7ae50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240176_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-169 1056,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7ba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-116 1056,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40208@1" ObjectIDZND0="g_1a7ae50@1" Pin0InfoVect0LinkObjId="g_1a7ae50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240176_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-116 1056,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-19 1056,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="40208@0" Pin0InfoVect0LinkObjId="SW-240176_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b368f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-19 1056,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a7f310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="487,177 487,183 462,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1a7ec30@0" Pin0InfoVect0LinkObjId="g_1a7ec30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="487,177 487,183 462,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a7f570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="451,183 433,183 433,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1a7ec30@1" ObjectIDZND0="g_1a19000@1" Pin0InfoVect0LinkObjId="g_1a19000_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a7ec30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="451,183 433,183 433,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a7feb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2362,177 2362,183 2346,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1a7f7d0@0" Pin0InfoVect0LinkObjId="g_1a7f7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2362,177 2362,183 2346,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a80110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2335,183 2321,183 2321,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1a7f7d0@1" ObjectIDZND0="g_1a01750@1" Pin0InfoVect0LinkObjId="g_1a01750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a7f7d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2335,183 2321,183 2321,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a80d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-304 1165,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40140@0" ObjectIDZND0="g_1a80370@0" Pin0InfoVect0LinkObjId="g_1a80370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-304 1165,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a80ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-372 1165,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1a80370@1" ObjectIDZND0="40211@0" Pin0InfoVect0LinkObjId="g_1b184f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a80370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-372 1165,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a81c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-303 1902,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40146@0" ObjectIDZND0="g_1a81250@0" Pin0InfoVect0LinkObjId="g_1a81250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-303 1902,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a81ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1902,-367 1902,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1a81250@1" ObjectIDZND0="40212@0" Pin0InfoVect0LinkObjId="g_1b44970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a81250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1902,-367 1902,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a86460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,37 1338,-4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1a838b0@0" ObjectIDZND0="g_1a82130@0" Pin0InfoVect0LinkObjId="g_1a82130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a838b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1338,37 1338,-4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a866c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,-64 1275,-64 1275,-49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="40204@x" ObjectIDND1="g_1a82130@0" ObjectIDZND0="g_1a82b00@0" Pin0InfoVect0LinkObjId="g_1a82b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240167_0" Pin1InfoVect1LinkObjId="g_1a82130_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-64 1275,-64 1275,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a86920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,-87 1338,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="40204@0" ObjectIDZND0="g_1a82b00@0" ObjectIDZND1="g_1a82130@0" Pin0InfoVect0LinkObjId="g_1a82b00_0" Pin0InfoVect1LinkObjId="g_1a82130_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240167_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-87 1338,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a86b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,-64 1338,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1a82b00@0" ObjectIDND1="40204@x" ObjectIDZND0="g_1a82130@1" Pin0InfoVect0LinkObjId="g_1a82130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a82b00_0" Pin1InfoVect1LinkObjId="SW-240167_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-64 1338,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a226a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,-134 1338,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40203@1" ObjectIDZND0="40204@1" Pin0InfoVect0LinkObjId="SW-240167_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240167_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-134 1338,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a22900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1338,-151 1338,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40203@0" ObjectIDZND0="40215@0" Pin0InfoVect0LinkObjId="g_1bb6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240167_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-151 1338,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a297a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1637,19 1637,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1a26bf0@0" ObjectIDZND0="g_1a256b0@0" Pin0InfoVect0LinkObjId="g_1a256b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a26bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1637,19 1637,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a29a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1637,-81 1574,-81 1574,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="40206@x" ObjectIDND1="g_1a256b0@0" ObjectIDZND0="g_1a25f00@0" Pin0InfoVect0LinkObjId="g_1a25f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240168_0" Pin1InfoVect1LinkObjId="g_1a256b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1637,-81 1574,-81 1574,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a29c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1637,-105 1637,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="40206@0" ObjectIDZND0="g_1a25f00@0" ObjectIDZND1="g_1a256b0@0" Pin0InfoVect0LinkObjId="g_1a25f00_0" Pin0InfoVect1LinkObjId="g_1a256b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1637,-105 1637,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a29ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1637,-81 1637,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="40206@x" ObjectIDND1="g_1a25f00@0" ObjectIDZND0="g_1a256b0@1" Pin0InfoVect0LinkObjId="g_1a256b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-240168_0" Pin1InfoVect1LinkObjId="g_1a25f00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1637,-81 1637,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2a120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1637,-205 1637,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="40216@0" ObjectIDZND0="40205@0" Pin0InfoVect0LinkObjId="SW-240168_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a520a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1637,-205 1637,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a30280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1637,-152 1637,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="40205@1" ObjectIDZND0="40206@1" Pin0InfoVect0LinkObjId="SW-240168_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-240168_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1637,-152 1637,-122 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-240048" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.500000 -911.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40117" ObjectName="DYN-CX_FHS"/>
     <cge:Meas_Ref ObjectId="240048"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a39650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1034.000000 722.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d40bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1018.000000 707.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d414b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 768.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d416f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 753.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d41930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 738.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d46560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1395.000000 775.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d471b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1409.000000 745.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d47710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1385.000000 760.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d480b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1773.000000 625.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d48310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1787.000000 595.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d48550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1763.000000 610.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d48880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1753.000000 284.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d48ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1767.000000 254.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d48d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1743.000000 269.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d49050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1006.000000 284.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d492b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 254.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d494f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.000000 269.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d49820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 585.000000 -162.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d49a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.000000 -192.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d49cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 575.000000 -177.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d49ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 741.000000 -162.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4a250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.000000 -192.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4a490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 731.000000 -177.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4a7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1128.000000 -161.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4aa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1142.000000 -191.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4ac60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 -176.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4af90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.000000 75.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4b1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1437.000000 45.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4b430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1413.000000 60.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4b760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1847.000000 -150.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4b9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1861.000000 -180.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4bc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1837.000000 -165.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4bf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2005.000000 -150.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4c190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2019.000000 -180.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4c3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1995.000000 -165.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4c700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2160.000000 -150.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4c960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2174.000000 -180.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4cba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2150.000000 -165.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4ced0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1673.000000 -150.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4d130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1687.000000 -180.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4d370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1663.000000 -165.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4e0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 963.000000 463.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4f480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 963.000000 478.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d50090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1692.000000 459.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d50310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1692.000000 474.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d51670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 456.000000 -240.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d518a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -225.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d51bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.000000 -238.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d51e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2314.000000 -223.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d53330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 247.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d53580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 232.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d537c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.000000 293.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d53a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.000000 278.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d53c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.000000 263.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d53f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.000000 245.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d541e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1509.000000 230.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d54420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 291.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d54660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 276.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d548a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 261.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d598a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 896.000000 -160.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d59ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.000000 -190.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d59d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 -175.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5a040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.000000 626.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5a2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1010.000000 596.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5a4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 986.000000 611.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b991d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1578.000000 -965.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b72690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1578.000000 -1037.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b80440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -18.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a6f900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b0d350" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2074.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acb760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1934.000000 -17.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1afd4f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2404.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b56d60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.000000 10.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b57760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2315.000000 19.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a1a910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1193.000000 -16.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a07510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a0fb90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 10.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a10620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 427.000000 19.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19d7f00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e5730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 966.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c057b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.000000 -19.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b84c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17f0150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17f0150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17f0150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17f0150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17f0150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17f0150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17f0150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_185bcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -1001.500000) translate(0,16)">凤凰山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_191c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -611.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b6bf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1411.000000 -1159.000000) translate(0,15)">35kV舍清线及凤凰山T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ac9b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.500000 -460.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a8a470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1292.000000 -702.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a8aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 706.000000 -235.000000) translate(0,15)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a8d1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2520.000000 123.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a45b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2004.000000 126.000000) translate(0,12)">备用三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a463e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1841.000000 125.000000) translate(0,12)">凤凰山五六号隧洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a47f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1690.000000 123.000000) translate(0,12)">凤凰山隧洞进口线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a488c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1025.000000 127.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a48b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 122.000000) translate(0,12)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a48f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 124.000000) translate(0,12)">龙川江倒虹吸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9b590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.000000 -782.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9c1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1541.000000 -993.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9c860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1523.000000 -1013.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9ccc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1531.000000 -1069.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9d2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1173.594799 -270.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9d600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1911.000000 -611.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9d840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1911.000000 -269.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9da80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.449580 -141.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9dcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.449580 23.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9df00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 529.000000 -69.000000) translate(0,12)">03160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9e140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 27.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9e570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 676.000000 -70.000000) translate(0,12)">03260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa4590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.000000 -630.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e03d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -70.000000) translate(0,12)">03360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c01e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 125.000000) translate(0,12)">凤凰山三四号隧洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c025a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 911.000000 125.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c0ee50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2187.000000 118.000000) translate(0,12)">备用四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a22b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1307.000000 89.000000) translate(0,12)">10kVI母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a30db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1595.000000 62.000000) translate(0,12)">10kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a33d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 637.000000 -140.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a34260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 787.000000 -142.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a344a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 943.000000 -142.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a346e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -72.000000) translate(0,12)">03460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a34920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -185.000000) translate(0,12)">0351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a34b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1173.000000 -138.000000) translate(0,12)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a34da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 -68.000000) translate(0,12)">03660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a34fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1288.000000 -150.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a35710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -172.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a35a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1547.000000 -146.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a35c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1649.000000 -146.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a35eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1759.000000 -142.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a360f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1794.000000 -72.000000) translate(0,12)">04160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a36330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1911.000000 -139.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a36570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1947.000000 -69.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a367b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2049.000000 -142.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a369f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2087.000000 -72.000000) translate(0,12)">04360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a36c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2210.000000 -141.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a36e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2248.000000 -71.000000) translate(0,12)">04460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a370b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2372.000000 -141.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a372f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2417.000000 -69.000000) translate(0,12)">04560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a37530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2369.000000 23.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a37770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2262.000000 24.000000) translate(0,12)">04567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a379b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2560.000000 -146.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a37bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -429.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a38110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1930.000000 -428.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a38390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 202.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a39100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2307.000000 198.000000) translate(0,12)">10kV2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d54ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 981.000000 -436.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d54ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 981.000000 -436.000000) translate(0,33)">SZ11-6300/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d54ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 981.000000 -436.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d54ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 981.000000 -436.000000) translate(0,69)">Yd11 Uk%=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d59100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 -429.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d59100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 -429.000000) translate(0,33)">SZ11-6300/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d59100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 -429.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d59100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 -429.000000) translate(0,69)">Yd11 Uk%=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5a720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1174.000000 -611.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d5c340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2060.000000 -233.000000) translate(0,15)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d5cab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 134.000000 -66.000000) translate(0,17)">孟慧凯：15912027237</text>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_FHS.CX_FHS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-680 2044,-680 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40214" ObjectName="BS-CX_FHS.CX_FHS_3IM"/>
    <cge:TPSR_Ref TObjectID="40214"/></metadata>
   <polyline fill="none" opacity="0" points="1043,-680 2044,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_FHS.CX_FHS_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="435,-205 1408,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40215" ObjectName="BS-CX_FHS.CX_FHS_9IM"/>
    <cge:TPSR_Ref TObjectID="40215"/></metadata>
   <polyline fill="none" opacity="0" points="435,-205 1408,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_FHS.CX_FHS_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1510,-205 2741,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="40216" ObjectName="BS-CX_FHS.CX_FHS_9IIM"/>
    <cge:TPSR_Ref TObjectID="40216"/></metadata>
   <polyline fill="none" opacity="0" points="1510,-205 2741,-205 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="40216" cx="1538" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40216" cx="1637" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40215" cx="1165" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40215" cx="1164" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40215" cx="480" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40215" cx="934" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40214" cx="1165" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40214" cx="1441" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40215" cx="1338" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40215" cx="1383" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40215" cx="1056" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40215" cx="778" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="40215" cx="628" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_FHS"/>
</svg>