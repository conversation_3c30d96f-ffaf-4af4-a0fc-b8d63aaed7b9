<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-126" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-270 -1029 2033 1139">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="currentTransformer:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="37" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="23" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="23" y1="32" y2="30"/>
    <circle cx="29" cy="7" r="7.5" stroke-width="1"/>
    <circle cx="34" cy="29" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="5" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="35" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="35" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="23" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="23" y1="20" y2="18"/>
    <circle cx="34" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="32" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="26" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="32" y1="5" y2="5"/>
    <circle cx="23" cy="29" r="7.5" stroke-width="1"/>
    <circle cx="23" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="2" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="3" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="35" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="35" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="41" x2="41" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="48" x2="48" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="48" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="22" x2="22" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="29" x2="29" y1="43" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="29" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="7" x2="7" y1="47" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="10" y1="46" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="13" y1="55" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="50" y2="50"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape177">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_3fc6fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_23d60b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape67_0">
    <circle cx="15" cy="72" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="74" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="74" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="74" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="20" y1="47" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="47" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="42"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,42 13,42 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="20,51 22,49 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="14" y1="51" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="129" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="16,101 10,114 23,114 16,101 16,102 16,101 "/>
    <circle cx="15" cy="50" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="44,74 44,29 16,20 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="35" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="66" y1="74" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape67_1"/>
   <symbol id="transformer2:shape68_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.335714" x1="16" x2="10" y1="81" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="66" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="35" y2="4"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="44,47 44,35 16,26 " stroke-width="1"/>
    <circle cx="15" cy="50" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,101 10,114 23,114 16,101 16,102 16,101 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="129" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="47" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="47" y2="52"/>
    <circle cx="15" cy="72" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.335714" x1="10" x2="21" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.335714" x1="16" x2="21" y1="81" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape68_1"/>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape69_0">
    <circle cx="31" cy="81" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="56" y2="51"/>
    <circle cx="31" cy="59" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="56" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="27" y2="27"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,56 6,56 6,27 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="31,13 25,26 37,26 31,13 31,14 31,13 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="44" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="56" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="7" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="80" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="80" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="80" y2="75"/>
   </symbol>
   <symbol id="transformer2:shape69_1"/>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="voltageTransformer:shape98">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="22" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="24" y2="24"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="35" y1="12" y2="12"/>
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1149" width="2043" x="-275" y="-1034"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4368d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 167.000000 -64.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c1860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 156.000000 -79.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c1a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.000000 -94.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4369dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.000000 472.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f73d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 457.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4369320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 442.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42baee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 71.000000 366.666667) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42bb150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 66.000000 353.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42bb390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 74.000000 408.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e03e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 74.000000 380.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e0620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 74.000000 394.333333) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fdf3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.000000 802.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f551d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 787.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f553a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 772.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="243,-85 286,-85 286,-57 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="239,-140 234,-150 244,-150 239,-140 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="239,-129 234,-119 244,-119 239,-129 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="481,-143 476,-153 486,-153 481,-143 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="481,-132 476,-122 486,-122 481,-132 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1492,-85 1535,-85 1535,-57 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1488,-140 1483,-150 1493,-150 1488,-140 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1488,-129 1483,-119 1493,-119 1488,-129 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="1339" y="-627"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="1378" y="-626"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="1310" y="-574"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="1336" y="-574"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="1544" y="-575"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="1570" y="-575"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="1530" y="-618"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="1491" y="-617"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-72324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16165" ObjectName="SW-CX_XT.CX_XT_3546SW"/>
     <cge:Meas_Ref ObjectId="72324"/>
    <cge:TPSR_Ref TObjectID="16165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72339">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16180" ObjectName="SW-CX_XT.CX_XT_3901SW"/>
     <cge:Meas_Ref ObjectId="72339"/>
    <cge:TPSR_Ref TObjectID="16180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72323">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16164" ObjectName="SW-CX_XT.CX_XT_3541SW"/>
     <cge:Meas_Ref ObjectId="72323"/>
    <cge:TPSR_Ref TObjectID="16164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72329">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16173" ObjectName="SW-CX_XT.CX_XT_3536SW"/>
     <cge:Meas_Ref ObjectId="72329"/>
    <cge:TPSR_Ref TObjectID="16173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72328">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16169" ObjectName="SW-CX_XT.CX_XT_3531SW"/>
     <cge:Meas_Ref ObjectId="72328"/>
    <cge:TPSR_Ref TObjectID="16169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16154" ObjectName="SW-CX_XT.CX_XT_3511SW"/>
     <cge:Meas_Ref ObjectId="72381"/>
    <cge:TPSR_Ref TObjectID="16154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.000000 -178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22961" ObjectName="SW-CX_XT.CX_XT_3513SW"/>
     <cge:Meas_Ref ObjectId="72382"/>
    <cge:TPSR_Ref TObjectID="22961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72366">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 262.000000 -45.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16157" ObjectName="SW-CX_XT.CX_XT_35167SW"/>
     <cge:Meas_Ref ObjectId="72366"/>
    <cge:TPSR_Ref TObjectID="16157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72318">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16159" ObjectName="SW-CX_XT.CX_XT_3521SW"/>
     <cge:Meas_Ref ObjectId="72318"/>
    <cge:TPSR_Ref TObjectID="16159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72319">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.000000 -178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16160" ObjectName="SW-CX_XT.CX_XT_3526SW"/>
     <cge:Meas_Ref ObjectId="72319"/>
    <cge:TPSR_Ref TObjectID="16160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 395.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72333">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -140.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22963" ObjectName="SW-CX_XT.CX_XT_3566SW"/>
     <cge:Meas_Ref ObjectId="72333"/>
    <cge:TPSR_Ref TObjectID="22963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72332">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16170" ObjectName="SW-CX_XT.CX_XT_3561SW"/>
     <cge:Meas_Ref ObjectId="72332"/>
    <cge:TPSR_Ref TObjectID="16170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72360">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1079.000000 -139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16177" ObjectName="SW-CX_XT.CX_XT_3556SW"/>
     <cge:Meas_Ref ObjectId="72360"/>
    <cge:TPSR_Ref TObjectID="16177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72359">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1079.000000 -285.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16176" ObjectName="SW-CX_XT.CX_XT_3551SW"/>
     <cge:Meas_Ref ObjectId="72359"/>
    <cge:TPSR_Ref TObjectID="16176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72316">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -366.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22952" ObjectName="SW-CX_XT.CX_XT_3011SW"/>
     <cge:Meas_Ref ObjectId="72316"/>
    <cge:TPSR_Ref TObjectID="22952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72350">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -416.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22954" ObjectName="SW-CX_XT.CX_XT_30117SW"/>
     <cge:Meas_Ref ObjectId="72350"/>
    <cge:TPSR_Ref TObjectID="22954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72349">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -496.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22953" ObjectName="SW-CX_XT.CX_XT_3016SW"/>
     <cge:Meas_Ref ObjectId="72349"/>
    <cge:TPSR_Ref TObjectID="22953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72314">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 -564.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22951" ObjectName="SW-CX_XT.CX_XT_1010SW"/>
     <cge:Meas_Ref ObjectId="72314"/>
    <cge:TPSR_Ref TObjectID="22951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72345">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -659.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22946" ObjectName="SW-CX_XT.CX_XT_15117SW"/>
     <cge:Meas_Ref ObjectId="72345"/>
    <cge:TPSR_Ref TObjectID="22946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72346">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -739.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22947" ObjectName="SW-CX_XT.CX_XT_15110SW"/>
     <cge:Meas_Ref ObjectId="72346"/>
    <cge:TPSR_Ref TObjectID="22947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72348">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -818.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22949" ObjectName="SW-CX_XT.CX_XT_15160SW"/>
     <cge:Meas_Ref ObjectId="72348"/>
    <cge:TPSR_Ref TObjectID="22949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72347">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -842.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22948" ObjectName="SW-CX_XT.CX_XT_1516SW"/>
     <cge:Meas_Ref ObjectId="72347"/>
    <cge:TPSR_Ref TObjectID="22948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72312">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 -898.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22950" ObjectName="SW-CX_XT.CX_XT_15167SW"/>
     <cge:Meas_Ref ObjectId="72312"/>
    <cge:TPSR_Ref TObjectID="22950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72378">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22955" ObjectName="SW-CX_XT.CX_XT_3571SW"/>
     <cge:Meas_Ref ObjectId="72378"/>
    <cge:TPSR_Ref TObjectID="22955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.000000 -178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22956" ObjectName="SW-CX_XT.CX_XT_3573SW"/>
     <cge:Meas_Ref ObjectId="72334"/>
    <cge:TPSR_Ref TObjectID="22956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72338">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 -45.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22960" ObjectName="SW-CX_XT.CX_XT_35767SW"/>
     <cge:Meas_Ref ObjectId="72338"/>
    <cge:TPSR_Ref TObjectID="22960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72383">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.000000 -283.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16156" ObjectName="SW-CX_XT.CX_XT_35117SW"/>
     <cge:Meas_Ref ObjectId="72383"/>
    <cge:TPSR_Ref TObjectID="16156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.000000 -165.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22962" ObjectName="SW-CX_XT.CX_XT_35137SW"/>
     <cge:Meas_Ref ObjectId="72384"/>
    <cge:TPSR_Ref TObjectID="22962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72320">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 499.000000 -283.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16161" ObjectName="SW-CX_XT.CX_XT_35217SW"/>
     <cge:Meas_Ref ObjectId="72320"/>
    <cge:TPSR_Ref TObjectID="16161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72321">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -165.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16162" ObjectName="SW-CX_XT.CX_XT_35267SW"/>
     <cge:Meas_Ref ObjectId="72321"/>
    <cge:TPSR_Ref TObjectID="16162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72330">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 738.000000 -270.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16171" ObjectName="SW-CX_XT.CX_XT_35317SW"/>
     <cge:Meas_Ref ObjectId="72330"/>
    <cge:TPSR_Ref TObjectID="16171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 738.000000 -122.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16174" ObjectName="SW-CX_XT.CX_XT_35367SW"/>
     <cge:Meas_Ref ObjectId="72331"/>
    <cge:TPSR_Ref TObjectID="16174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72325">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 930.000000 -271.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16166" ObjectName="SW-CX_XT.CX_XT_35417SW"/>
     <cge:Meas_Ref ObjectId="72325"/>
    <cge:TPSR_Ref TObjectID="16166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72326">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 931.000000 -123.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16167" ObjectName="SW-CX_XT.CX_XT_35467SW"/>
     <cge:Meas_Ref ObjectId="72326"/>
    <cge:TPSR_Ref TObjectID="16167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72361">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -268.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16178" ObjectName="SW-CX_XT.CX_XT_35517SW"/>
     <cge:Meas_Ref ObjectId="72361"/>
    <cge:TPSR_Ref TObjectID="16178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72362">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -120.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16179" ObjectName="SW-CX_XT.CX_XT_35567SW"/>
     <cge:Meas_Ref ObjectId="72362"/>
    <cge:TPSR_Ref TObjectID="16179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72374">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1298.000000 -269.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16172" ObjectName="SW-CX_XT.CX_XT_35617SW"/>
     <cge:Meas_Ref ObjectId="72374"/>
    <cge:TPSR_Ref TObjectID="16172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72375">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1298.000000 -121.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22964" ObjectName="SW-CX_XT.CX_XT_35667SW"/>
     <cge:Meas_Ref ObjectId="72375"/>
    <cge:TPSR_Ref TObjectID="22964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1506.000000 -283.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22957" ObjectName="SW-CX_XT.CX_XT_35717SW"/>
     <cge:Meas_Ref ObjectId="72335"/>
    <cge:TPSR_Ref TObjectID="22957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72336">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1506.000000 -165.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22958" ObjectName="SW-CX_XT.CX_XT_35737SW"/>
     <cge:Meas_Ref ObjectId="72336"/>
    <cge:TPSR_Ref TObjectID="22958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72340">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.000000 -446.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16181" ObjectName="SW-CX_XT.CX_XT_39017SW"/>
     <cge:Meas_Ref ObjectId="72340"/>
    <cge:TPSR_Ref TObjectID="16181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.000000 -58.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16155" ObjectName="SW-CX_XT.CX_XT_3516SW"/>
     <cge:Meas_Ref ObjectId="72385"/>
    <cge:TPSR_Ref TObjectID="16155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72337">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.000000 -63.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22959" ObjectName="SW-CX_XT.CX_XT_3576SW"/>
     <cge:Meas_Ref ObjectId="72337"/>
    <cge:TPSR_Ref TObjectID="22959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72344">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -680.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22945" ObjectName="SW-CX_XT.CX_XT_1511SW"/>
     <cge:Meas_Ref ObjectId="72344"/>
    <cge:TPSR_Ref TObjectID="22945"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3d66ab0">
    <use class="BV-35KV" transform="matrix(1.641026 -0.000000 0.000000 -1.696970 273.000000 -533.000000)" xlink:href="#voltageTransformer:shape98"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XT.CX_XT_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-354 1676,-354 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="16152" ObjectName="BS-CX_XT.CX_XT_3IM"/>
    <cge:TPSR_Ref TObjectID="16152"/></metadata>
   <polyline fill="none" opacity="0" points="171,-354 1676,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1267,-491 1413,-491 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1267,-491 1413,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1504,-493 1762,-493 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1504,-493 1762,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XT.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="753,-733 784,-733 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48132" ObjectName="BS-CX_XT.XM"/>
    <cge:TPSR_Ref TObjectID="48132"/></metadata>
   <polyline fill="none" opacity="0" points="753,-733 784,-733 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1301.000000 -725.000000)" xlink:href="#transformer2:shape67_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1301.000000 -725.000000)" xlink:href="#transformer2:shape67_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1562.000000 -725.000000)" xlink:href="#transformer2:shape68_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1562.000000 -725.000000)" xlink:href="#transformer2:shape68_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_XT.CX_XT_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="32435"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 730.000000 -562.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 730.000000 -562.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23030" ObjectName="TF-CX_XT.CX_XT_1T"/>
    <cge:TPSR_Ref TObjectID="23030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 449.000000 -8.000000)" xlink:href="#transformer2:shape69_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 449.000000 -8.000000)" xlink:href="#transformer2:shape69_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.580000 -0.000000 0.000000 -0.588889 225.000000 18.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.580000 -0.000000 0.000000 -0.588889 225.000000 18.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3bdb250">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -482.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_334c330">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 223.000000 57.000000)" xlink:href="#lightningRod:shape177"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e55d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -647.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e5b360">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1572.000000 -647.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ca230">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 689.000000 -478.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d083e0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 707.000000 -537.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d6d530">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.000000 -949.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_417f030">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 158.000000 -226.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_367cfa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -225.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e5320">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 427.000000 -14.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d74580">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 639.000000 -195.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f14410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -196.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_363d170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1007.000000 -193.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a652e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 -194.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_412dd20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1471.000000 -2.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d67e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 259.000000 -465.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c98810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.000000 -225.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -179.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79748" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -153.461538 -748.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79748" ObjectName="CX_XT:CX_XT_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79749" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -154.461538 -708.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79749" ObjectName="CX_XT:CX_XT_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-125295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -474.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22944"/>
     <cge:Term_Ref ObjectID="32294"/>
    <cge:TPSR_Ref TObjectID="22944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-125294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -474.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22944"/>
     <cge:Term_Ref ObjectID="32294"/>
    <cge:TPSR_Ref TObjectID="22944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-125296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -474.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22944"/>
     <cge:Term_Ref ObjectID="32294"/>
    <cge:TPSR_Ref TObjectID="22944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-72302" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -804.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72302" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16175"/>
     <cge:Term_Ref ObjectID="20547"/>
    <cge:TPSR_Ref TObjectID="16175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-72303" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -804.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16175"/>
     <cge:Term_Ref ObjectID="20547"/>
    <cge:TPSR_Ref TObjectID="16175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-72301" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -804.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16175"/>
     <cge:Term_Ref ObjectID="20547"/>
    <cge:TPSR_Ref TObjectID="16175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-72298" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 227.000000 65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16153"/>
     <cge:Term_Ref ObjectID="20503"/>
    <cge:TPSR_Ref TObjectID="16153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-72299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 227.000000 65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16153"/>
     <cge:Term_Ref ObjectID="20503"/>
    <cge:TPSR_Ref TObjectID="16153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-72297" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 227.000000 65.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16153"/>
     <cge:Term_Ref ObjectID="20503"/>
    <cge:TPSR_Ref TObjectID="16153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-72294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16158"/>
     <cge:Term_Ref ObjectID="20513"/>
    <cge:TPSR_Ref TObjectID="16158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-72295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16158"/>
     <cge:Term_Ref ObjectID="20513"/>
    <cge:TPSR_Ref TObjectID="16158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-72293" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 65.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16158"/>
     <cge:Term_Ref ObjectID="20513"/>
    <cge:TPSR_Ref TObjectID="16158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-72406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 708.000000 65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16168"/>
     <cge:Term_Ref ObjectID="20533"/>
    <cge:TPSR_Ref TObjectID="16168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-72407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 708.000000 65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16168"/>
     <cge:Term_Ref ObjectID="20533"/>
    <cge:TPSR_Ref TObjectID="16168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-72405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 708.000000 65.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16168"/>
     <cge:Term_Ref ObjectID="20533"/>
    <cge:TPSR_Ref TObjectID="16168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-72410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.000000 65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16163"/>
     <cge:Term_Ref ObjectID="20523"/>
    <cge:TPSR_Ref TObjectID="16163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-72411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.000000 65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16163"/>
     <cge:Term_Ref ObjectID="20523"/>
    <cge:TPSR_Ref TObjectID="16163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-72409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.000000 65.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16163"/>
     <cge:Term_Ref ObjectID="20523"/>
    <cge:TPSR_Ref TObjectID="16163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-72414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22941"/>
     <cge:Term_Ref ObjectID="32290"/>
    <cge:TPSR_Ref TObjectID="22941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-72415" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72415" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22941"/>
     <cge:Term_Ref ObjectID="32290"/>
    <cge:TPSR_Ref TObjectID="22941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-72413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 65.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22941"/>
     <cge:Term_Ref ObjectID="32290"/>
    <cge:TPSR_Ref TObjectID="22941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-72418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22942"/>
     <cge:Term_Ref ObjectID="32292"/>
    <cge:TPSR_Ref TObjectID="22942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-72419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22942"/>
     <cge:Term_Ref ObjectID="32292"/>
    <cge:TPSR_Ref TObjectID="22942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-72417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 65.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22942"/>
     <cge:Term_Ref ObjectID="32292"/>
    <cge:TPSR_Ref TObjectID="22942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-72422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22940"/>
     <cge:Term_Ref ObjectID="32288"/>
    <cge:TPSR_Ref TObjectID="22940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-72423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22940"/>
     <cge:Term_Ref ObjectID="32288"/>
    <cge:TPSR_Ref TObjectID="22940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-72421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 65.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22940"/>
     <cge:Term_Ref ObjectID="32288"/>
    <cge:TPSR_Ref TObjectID="22940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-72306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 129.000000 -410.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16152"/>
     <cge:Term_Ref ObjectID="20502"/>
    <cge:TPSR_Ref TObjectID="16152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-72307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 129.000000 -410.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16152"/>
     <cge:Term_Ref ObjectID="20502"/>
    <cge:TPSR_Ref TObjectID="16152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-72308" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 129.000000 -410.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16152"/>
     <cge:Term_Ref ObjectID="20502"/>
    <cge:TPSR_Ref TObjectID="16152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-72311" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 129.000000 -410.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72311" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16152"/>
     <cge:Term_Ref ObjectID="20502"/>
    <cge:TPSR_Ref TObjectID="16152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-72309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 129.000000 -410.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16152"/>
     <cge:Term_Ref ObjectID="20502"/>
    <cge:TPSR_Ref TObjectID="16152"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="-167" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="-167" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-216" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-216" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-7,-799 -10,-802 -10,-748 -7,-751 -7,-799" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-7,-799 -10,-802 139,-802 136,-799 -7,-799" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="-7,-751 -10,-748 139,-748 136,-751 -7,-751" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="136,-799 139,-802 139,-748 136,-751 136,-799" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="-7" y="-799"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-7" y="-799"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="-167" y="-940"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-216" y="-957"/></g>
   <g href="AVC秀田升压站.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-7" y="-799"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1317" x2="1339" y1="-621" y2="-621"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1363" x2="1395" y1="-619" y2="-619"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1402" x2="1422" y1="-619" y2="-619"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1422" x2="1422" y1="-624" y2="-615"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1424" x2="1424" y1="-623" y2="-617"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1426" x2="1426" y1="-622" y2="-617"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1309" x2="1352" y1="-518" y2="-518"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1309" x2="1298" y1="-518" y2="-536"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1303" x2="1359" y1="-529" y2="-529"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1343" x2="1343" y1="-554" y2="-541"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1577" x2="1554" y1="-612" y2="-612"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1530" x2="1498" y1="-610" y2="-610"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1491" x2="1471" y1="-610" y2="-610"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1471" x2="1471" y1="-615" y2="-606"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1469" x2="1469" y1="-614" y2="-608"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1467" x2="1467" y1="-613" y2="-608"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1317" x2="1549" y1="-592" y2="-592"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1549" x2="1549" y1="-592" y2="-590"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1577" x2="1343" y1="-586" y2="-586"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1343" x2="1343" y1="-586" y2="-574"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1317" x2="1317" y1="-554" y2="-541"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1551" x2="1551" y1="-555" y2="-542"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1577" x2="1577" y1="-555" y2="-542"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1549" x2="1549" y1="-582" y2="-575"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1352" x2="1363" y1="-518" y2="-536"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1542" x2="1585" y1="-522" y2="-522"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1542" x2="1531" y1="-522" y2="-540"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1536" x2="1592" y1="-533" y2="-533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1585" x2="1596" y1="-522" y2="-540"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="689" x2="689" y1="-629" y2="-608"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="689" x2="714" y1="-550" y2="-550"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="672,-550 689,-550 689,-572 " stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-72322">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -216.693356)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16163" ObjectName="SW-CX_XT.CX_XT_354BK"/>
     <cge:Meas_Ref ObjectId="72322"/>
    <cge:TPSR_Ref TObjectID="16163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72327">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 -215.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16168" ObjectName="SW-CX_XT.CX_XT_353BK"/>
     <cge:Meas_Ref ObjectId="72327"/>
    <cge:TPSR_Ref TObjectID="16168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.000000 -239.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16153" ObjectName="SW-CX_XT.CX_XT_351BK"/>
     <cge:Meas_Ref ObjectId="72380"/>
    <cge:TPSR_Ref TObjectID="16153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72356">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.000000 -239.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16158" ObjectName="SW-CX_XT.CX_XT_352BK"/>
     <cge:Meas_Ref ObjectId="72356"/>
    <cge:TPSR_Ref TObjectID="16158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 395.000000 14.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72367">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -214.693356)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22942" ObjectName="SW-CX_XT.CX_XT_356BK"/>
     <cge:Meas_Ref ObjectId="72367"/>
    <cge:TPSR_Ref TObjectID="22942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72358">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1079.000000 -213.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22941" ObjectName="SW-CX_XT.CX_XT_355BK"/>
     <cge:Meas_Ref ObjectId="72358"/>
    <cge:TPSR_Ref TObjectID="22941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72315">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22944" ObjectName="SW-CX_XT.CX_XT_301BK"/>
     <cge:Meas_Ref ObjectId="72315"/>
    <cge:TPSR_Ref TObjectID="22944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72343">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 -762.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16175" ObjectName="SW-CX_XT.CX_XT_151BK"/>
     <cge:Meas_Ref ObjectId="72343"/>
    <cge:TPSR_Ref TObjectID="16175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-72377">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.000000 -239.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22940" ObjectName="SW-CX_XT.CX_XT_357BK"/>
     <cge:Meas_Ref ObjectId="72377"/>
    <cge:TPSR_Ref TObjectID="22940"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_XT.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.000000 -42.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43343" ObjectName="SM-CX_XT.P1"/>
    <cge:TPSR_Ref TObjectID="43343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_XT.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -43.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43344" ObjectName="SM-CX_XT.P2"/>
    <cge:TPSR_Ref TObjectID="43344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_XT.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1083.000000 -40.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43345" ObjectName="SM-CX_XT.P3"/>
    <cge:TPSR_Ref TObjectID="43345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_XT.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 -41.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43346" ObjectName="SM-CX_XT.P4"/>
    <cge:TPSR_Ref TObjectID="43346"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3f189f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 -793.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e3110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1636.000000 -766.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e73030" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 666.000000 -522.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f772e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 398.000000 39.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3538160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -897.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fc1be0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.000000 -282.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fc2640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.000000 -164.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41ac600" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 554.000000 -282.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40c4b90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 554.000000 -164.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_447e490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 -269.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_447eec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 -121.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e46970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 985.000000 -270.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25de1d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 985.000000 -122.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25dec30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1161.000000 -267.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40ac5e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1161.000000 -119.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cc0370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1353.000000 -268.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e61e20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1353.000000 -120.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e62850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1561.000000 -282.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_435e3e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1561.000000 -164.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a81930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 376.000000 -445.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a823c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -817.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cc5000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -738.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f5dd00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -658.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f5e790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -415.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="16152" cx="304" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16152" cx="912" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16152" cx="720" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16152" cx="239" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16152" cx="481" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16152" cx="1280" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16152" cx="1088" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16152" cx="768" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16152" cx="1488" cy="-354" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48132" cx="770" cy="-733" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48132" cx="770" cy="-733" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1330" cy="-491" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1564" cy="-493" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35cb770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 261.000000 1.000000) translate(0,15)">1号补偿变动态</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35cb770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 261.000000 1.000000) translate(0,33)">无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_349e410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -22.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e51020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -553.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e51020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -553.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42c1520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 178.000000 -379.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_329bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3fdc990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -140.000000 -929.500000) translate(0,16)">秀田升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_426e0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -148.000000 7.000000) translate(0,17)">6715100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e9a1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 -4.000000) translate(0,15)">11-20号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3426050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 886.500000 14.000000) translate(0,15)">10MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_433b120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 680.000000 -21.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42c1150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -3.000000) translate(0,15)">1-10号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2341d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.500000 15.000000) translate(0,15)">10MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_410c950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 276.500000 37.000000) translate(0,15)">±6MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4369540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 263.000000 -22.000000) translate(0,12)">6000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3eefce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.500000 5.000000) translate(0,15)">1号站用变及小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3eefce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.500000 5.000000) translate(0,33)">电阻接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3fe5f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 -111.000000) translate(0,13)">1号接地站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3fe5f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 -111.000000) translate(0,29)">DKSC-1250/35-250/0.4GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3fe5f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 -111.000000) translate(0,45)">35±2x2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3fe5f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 -111.000000) translate(0,61)">Ud=6.5% Zn,yn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3fe5f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 -111.000000) translate(0,77)">YJLV22-26/35-3x70</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d07790" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1278.500000 -880.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4664d40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1280.000000 -482.000000) translate(0,15)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ceffb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1607.000000 -483.000000) translate(0,15)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf02f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1470.000000 -796.000000) translate(0,15)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf02f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1470.000000 -796.000000) translate(0,33)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4095bf0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1265.000000 -909.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40e0720" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1218.000000 -790.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_401a060" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1538.000000 -918.000000) translate(0,15)">10kV凹鮓线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_401a060" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1538.000000 -918.000000) translate(0,33)"> 跨榜支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fd2390" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1542.500000 -880.000000) translate(0,15)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f98540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1240.000000 -20.000000) translate(0,15)">4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_360e5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -2.000000) translate(0,15)">36-50号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3febb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.500000 16.000000) translate(0,15)">15MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cba720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 -19.000000) translate(0,15)">3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cacdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1039.000000 -1.000000) translate(0,15)">21-35号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25ef060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1062.500000 17.000000) translate(0,15)">15MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40fe4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1412.000000 4.000000) translate(0,15)">2号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23d65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 23.000000) translate(0,15)">±14MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3efdfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 -770.000000) translate(0,12)">15110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3efe470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 -690.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3efe6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -709.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3efe8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 710.000000 -849.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d45890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -929.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d45ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -872.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d45d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -791.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d45f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -326.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d46190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -208.000000) translate(0,12)">3513</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d463d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -40.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a37a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 248.000000 -269.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a39e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 614.000000 -594.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a3c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 717.000000 -447.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a3e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -396.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a40a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -526.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a42e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -460.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a35670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -317.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a358b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -171.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a35af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -245.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a35d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1495.000000 -326.000000) translate(0,12)">3571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a35f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1495.000000 -208.000000) translate(0,12)">3573</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a361b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -40.000000) translate(0,12)">35767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ff8c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1497.000000 -269.000000) translate(0,12)">357</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ff8ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 488.000000 -326.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ff90e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 488.000000 -208.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ff9320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 490.000000 -269.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ff9560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -318.000000) translate(0,12)">3541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ff97a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -172.000000) translate(0,12)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d53f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -246.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d5630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1095.000000 -315.000000) translate(0,12)">3551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d5870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1095.000000 -169.000000) translate(0,12)">3556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d5ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1097.000000 -243.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d5cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1287.000000 -316.000000) translate(0,12)">3561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fde980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1287.000000 -170.000000) translate(0,12)">3566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fdebc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1289.000000 -244.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fdee00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 311.000000 -421.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42268b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 280.000000 -312.000000) translate(0,12)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4226da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 279.000000 -192.000000) translate(0,12)">35137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46652d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.000000 -312.000000) translate(0,12)">35217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_46654d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -192.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4665710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 763.000000 -298.000000) translate(0,12)">35317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4665950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 762.000000 -150.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4665b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 957.000000 -298.000000) translate(0,12)">35417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4665dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 957.000000 -150.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40adcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 -295.000000) translate(0,12)">35517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40adf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1134.000000 -147.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40ae140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -296.000000) translate(0,12)">35617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40ae380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1324.000000 -148.000000) translate(0,12)">35667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40ae5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1535.000000 -310.000000) translate(0,12)">35717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40ae800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1534.000000 -192.000000) translate(0,12)">35737</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3537c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 329.000000 -477.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ade560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 197.000000 -85.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fad4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1446.000000 -90.000000) translate(0,12)">3576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3fad9a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 747.000000 -1029.000000) translate(0,16)">方秀线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fae270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 818.000000 -641.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fae270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 818.000000 -641.000000) translate(0,27)">SZ11-100000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fae270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 818.000000 -641.000000) translate(0,42)">121±8x1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fae270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 818.000000 -641.000000) translate(0,57)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fae270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 818.000000 -641.000000) translate(0,72)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3d02fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 20.000000 -784.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1549,-582 1548,-582 1548,-582 1547,-582 1547,-582 1546,-583 1546,-583 1545,-583 1545,-584 1545,-584 1544,-585 1544,-585 1544,-586 1544,-586 1544,-587 1544,-587 1545,-588 1545,-588 1545,-589 1546,-589 1546,-589 1547,-590 1547,-590 1548,-590 1548,-590 1549,-590 " stroke="rgb(60,120,255)" stroke-width="0.191496"/>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3df5e90">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 -886.000000)" xlink:href="#currentTransformer:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_25f5820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="380,-451 367,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a81930@0" ObjectIDZND0="16181@1" Pin0InfoVect0LinkObjId="SW-72340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a81930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="380,-451 367,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fc7840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-329 912,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16164@1" ObjectIDZND0="16152@0" Pin0InfoVect0LinkObjId="g_43f99e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72323_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-329 912,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33fbaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-276 971,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e46970@0" ObjectIDZND0="16166@1" Pin0InfoVect0LinkObjId="SW-72325_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e46970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-276 971,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34dfe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="888,-203 912,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_3f14410@0" ObjectIDZND0="16163@x" ObjectIDZND1="16165@x" Pin0InfoVect0LinkObjId="SW-72322_0" Pin0InfoVect1LinkObjId="SW-72324_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f14410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="888,-203 912,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3425500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-225 912,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16163@0" ObjectIDZND0="g_3f14410@0" ObjectIDZND1="16165@x" Pin0InfoVect0LinkObjId="g_3f14410_0" Pin0InfoVect1LinkObjId="SW-72324_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-225 912,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fec920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-203 912,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="16163@x" ObjectIDND1="g_3f14410@0" ObjectIDZND0="16165@1" Pin0InfoVect0LinkObjId="SW-72324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72322_0" Pin1InfoVect1LinkObjId="g_3f14410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-203 912,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40de720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="935,-276 912,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16166@0" ObjectIDZND0="16163@x" ObjectIDZND1="16164@x" Pin0InfoVect0LinkObjId="SW-72322_0" Pin0InfoVect1LinkObjId="SW-72323_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="935,-276 912,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fc7280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-252 912,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16163@1" ObjectIDZND0="16164@x" ObjectIDZND1="16166@x" Pin0InfoVect0LinkObjId="SW-72323_0" Pin0InfoVect1LinkObjId="SW-72325_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72322_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-252 912,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41186a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-276 912,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16163@x" ObjectIDND1="16166@x" ObjectIDZND0="16164@0" Pin0InfoVect0LinkObjId="SW-72323_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72322_0" Pin1InfoVect1LinkObjId="SW-72325_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-276 912,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43f9c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-128 971,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25de1d0@0" ObjectIDZND0="16167@1" Pin0InfoVect0LinkObjId="SW-72326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25de1d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-128 971,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4095360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="936,-128 912,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="16167@0" ObjectIDZND0="16165@x" ObjectIDZND1="43344@x" Pin0InfoVect0LinkObjId="SW-72324_0" Pin0InfoVect1LinkObjId="SM-CX_XT.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="936,-128 912,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_404f4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-147 912,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="16165@0" ObjectIDZND0="16167@x" ObjectIDZND1="43344@x" Pin0InfoVect0LinkObjId="SW-72326_0" Pin0InfoVect1LinkObjId="SM-CX_XT.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-147 912,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42c16f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-128 912,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="16165@x" ObjectIDND1="16167@x" ObjectIDZND0="43344@0" Pin0InfoVect0LinkObjId="SM-CX_XT.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72324_0" Pin1InfoVect1LinkObjId="SW-72326_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-128 912,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3351760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="797,-275 779,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_447e490@0" ObjectIDZND0="16171@1" Pin0InfoVect0LinkObjId="SW-72330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_447e490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-275 779,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43f99e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-328 720,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16169@1" ObjectIDZND0="16152@0" Pin0InfoVect0LinkObjId="g_3fc7840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72328_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,-328 720,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22fb810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="696,-202 720,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_3d74580@0" ObjectIDZND0="16168@x" ObjectIDZND1="16173@x" Pin0InfoVect0LinkObjId="SW-72327_0" Pin0InfoVect1LinkObjId="SW-72329_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d74580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="696,-202 720,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b89c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-224 720,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16168@0" ObjectIDZND0="g_3d74580@0" ObjectIDZND1="16173@x" Pin0InfoVect0LinkObjId="g_3d74580_0" Pin0InfoVect1LinkObjId="SW-72329_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72327_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="720,-224 720,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26417e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-202 720,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="16168@x" ObjectIDND1="g_3d74580@0" ObjectIDZND0="16173@1" Pin0InfoVect0LinkObjId="SW-72329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72327_0" Pin1InfoVect1LinkObjId="g_3d74580_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,-202 720,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e94350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-275 720,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16171@0" ObjectIDZND0="16168@x" ObjectIDZND1="16169@x" Pin0InfoVect0LinkObjId="SW-72327_0" Pin0InfoVect1LinkObjId="SW-72328_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="743,-275 720,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fa2b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-251 720,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16168@1" ObjectIDZND0="16169@x" ObjectIDZND1="16171@x" Pin0InfoVect0LinkObjId="SW-72328_0" Pin0InfoVect1LinkObjId="SW-72330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72327_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="720,-251 720,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4228ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-275 720,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16168@x" ObjectIDND1="16171@x" ObjectIDZND0="16169@0" Pin0InfoVect0LinkObjId="SW-72328_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72327_0" Pin1InfoVect1LinkObjId="SW-72330_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,-275 720,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42bb5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="797,-127 779,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_447eec0@0" ObjectIDZND0="16174@1" Pin0InfoVect0LinkObjId="SW-72331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_447eec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-127 779,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_329c330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-127 720,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" EndDevType1="switch" ObjectIDND0="16174@0" ObjectIDZND0="43343@x" ObjectIDZND1="16173@x" Pin0InfoVect0LinkObjId="SM-CX_XT.P1_0" Pin0InfoVect1LinkObjId="SW-72329_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="743,-127 720,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b8740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-146 720,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" EndDevType1="switch" ObjectIDND0="16173@0" ObjectIDZND0="43343@x" ObjectIDZND1="16174@x" Pin0InfoVect0LinkObjId="SM-CX_XT.P1_0" Pin0InfoVect1LinkObjId="SW-72331_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="720,-146 720,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e64090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-127 720,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="16173@x" ObjectIDND1="16174@x" ObjectIDZND0="43343@0" Pin0InfoVect0LinkObjId="SM-CX_XT.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72329_0" Pin1InfoVect1LinkObjId="SW-72331_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,-127 720,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3585f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,-288 298,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3fc1be0@0" ObjectIDZND0="16156@1" Pin0InfoVect0LinkObjId="SW-72383_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fc1be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,-288 298,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_329c770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-337 239,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16154@1" ObjectIDZND0="16152@0" Pin0InfoVect0LinkObjId="g_3fc7840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-337 239,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25daa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="262,-288 239,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16156@0" ObjectIDZND0="16153@x" ObjectIDZND1="16154@x" Pin0InfoVect0LinkObjId="SW-72380_0" Pin0InfoVect1LinkObjId="SW-72381_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="262,-288 239,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_433bac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,-170 298,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3fc2640@0" ObjectIDZND0="22962@1" Pin0InfoVect0LinkObjId="SW-72384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fc2640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,-170 298,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_433b660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-288 239,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16153@x" ObjectIDND1="16156@x" ObjectIDZND0="16154@0" Pin0InfoVect0LinkObjId="SW-72381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72380_0" Pin1InfoVect1LinkObjId="SW-72383_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-288 239,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4095570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-288 239,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16154@x" ObjectIDND1="16156@x" ObjectIDZND0="16153@1" Pin0InfoVect0LinkObjId="SW-72380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72381_0" Pin1InfoVect1LinkObjId="SW-72383_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-288 239,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_341ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-219 239,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="22961@1" ObjectIDZND0="16153@x" ObjectIDZND1="g_417f030@0" Pin0InfoVect0LinkObjId="SW-72380_0" Pin0InfoVect1LinkObjId="g_417f030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="239,-219 239,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d20e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-248 239,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16153@0" ObjectIDZND0="g_417f030@0" ObjectIDZND1="22961@x" Pin0InfoVect0LinkObjId="g_417f030_0" Pin0InfoVect1LinkObjId="SW-72382_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="239,-248 239,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f23aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-233 215,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16153@x" ObjectIDND1="22961@x" ObjectIDZND0="g_417f030@0" Pin0InfoVect0LinkObjId="g_417f030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72380_0" Pin1InfoVect1LinkObjId="SW-72382_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-233 215,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e9a640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-171 239,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22961@x" ObjectIDND1="22962@x" ObjectIDZND0="16155@1" Pin0InfoVect0LinkObjId="SW-72385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72382_0" Pin1InfoVect1LinkObjId="SW-72384_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-171 239,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4666190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="262,-170 239,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22962@0" ObjectIDZND0="22961@x" ObjectIDZND1="16155@x" Pin0InfoVect0LinkObjId="SW-72382_0" Pin0InfoVect1LinkObjId="SW-72385_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="262,-170 239,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ef68a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-170 239,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22962@x" ObjectIDND1="16155@x" ObjectIDZND0="22961@0" Pin0InfoVect0LinkObjId="SW-72382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72384_0" Pin1InfoVect1LinkObjId="SW-72385_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-170 239,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d4b630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-52 239,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="16157@x" ObjectIDND1="16155@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72366_0" Pin1InfoVect1LinkObjId="SW-72385_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-52 239,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ef5eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="267,-52 239,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="16157@0" ObjectIDZND0="0@x" ObjectIDZND1="16155@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-72385_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72366_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="267,-52 239,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4368b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-52 239,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="16157@x" ObjectIDND1="0@x" ObjectIDZND0="16155@0" Pin0InfoVect0LinkObjId="SW-72385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72366_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-52 239,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_41b3220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="240,27 240,16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_334c330@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_334c330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="240,27 240,16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40aeee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-288 540,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_41ac600@0" ObjectIDZND0="16161@1" Pin0InfoVect0LinkObjId="SW-72320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41ac600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-288 540,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4075960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-337 481,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16159@1" ObjectIDZND0="16152@0" Pin0InfoVect0LinkObjId="g_3fc7840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="481,-337 481,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3610d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="504,-288 481,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16161@0" ObjectIDZND0="16158@x" ObjectIDZND1="16159@x" Pin0InfoVect0LinkObjId="SW-72356_0" Pin0InfoVect1LinkObjId="SW-72318_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="504,-288 481,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_413a530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-170 539,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_40c4b90@0" ObjectIDZND0="16162@1" Pin0InfoVect0LinkObjId="SW-72321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40c4b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,-170 539,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a646c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-288 481,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16158@x" ObjectIDND1="16161@x" ObjectIDZND0="16159@0" Pin0InfoVect0LinkObjId="SW-72318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72356_0" Pin1InfoVect1LinkObjId="SW-72320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="481,-288 481,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5cd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-288 481,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16159@x" ObjectIDND1="16161@x" ObjectIDZND0="16158@1" Pin0InfoVect0LinkObjId="SW-72356_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72318_0" Pin1InfoVect1LinkObjId="SW-72320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="481,-288 481,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23417b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-219 481,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="16160@1" ObjectIDZND0="16158@x" ObjectIDZND1="g_367cfa0@0" Pin0InfoVect0LinkObjId="SW-72356_0" Pin0InfoVect1LinkObjId="g_367cfa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72319_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="481,-219 481,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2341a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-248 481,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16158@0" ObjectIDZND0="g_367cfa0@0" ObjectIDZND1="16160@x" Pin0InfoVect0LinkObjId="g_367cfa0_0" Pin0InfoVect1LinkObjId="SW-72319_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="481,-248 481,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e0b600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-233 502,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16158@x" ObjectIDND1="16160@x" ObjectIDZND0="g_367cfa0@0" Pin0InfoVect0LinkObjId="g_367cfa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72356_0" Pin1InfoVect1LinkObjId="SW-72319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="481,-233 502,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4258620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="404,21 404,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="breaker" ObjectIDND0="g_3f772e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f772e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="404,21 404,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4258880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="404,-21 404,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="404,-21 404,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4368f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-518 304,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3bdb250@1" ObjectIDZND0="g_3d66ab0@0" Pin0InfoVect0LinkObjId="g_3d66ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bdb250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-518 304,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4369110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-396 304,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16180@0" ObjectIDZND0="16152@0" Pin0InfoVect0LinkObjId="g_3fc7840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72339_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-396 304,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d1440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="331,-451 304,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="16181@0" ObjectIDZND0="g_3bdb250@0" ObjectIDZND1="g_23d67e0@0" ObjectIDZND2="16180@x" Pin0InfoVect0LinkObjId="g_3bdb250_0" Pin0InfoVect1LinkObjId="g_23d67e0_0" Pin0InfoVect2LinkObjId="SW-72339_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="331,-451 304,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d1670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-469 304,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_23d67e0@0" ObjectIDZND0="g_3bdb250@0" ObjectIDZND1="16180@x" ObjectIDZND2="16181@x" Pin0InfoVect0LinkObjId="g_3bdb250_0" Pin0InfoVect1LinkObjId="SW-72339_0" Pin0InfoVect2LinkObjId="SW-72340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23d67e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="266,-469 304,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d6cb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-474 304,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16180@x" ObjectIDND1="16181@x" ObjectIDND2="g_23d67e0@0" ObjectIDZND0="g_3bdb250@0" Pin0InfoVect0LinkObjId="g_3bdb250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-72339_0" Pin1InfoVect1LinkObjId="SW-72340_0" Pin1InfoVect2LinkObjId="g_23d67e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-474 304,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d6cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-432 304,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="16180@1" ObjectIDZND0="g_3bdb250@0" ObjectIDZND1="g_23d67e0@0" ObjectIDZND2="16181@x" Pin0InfoVect0LinkObjId="g_3bdb250_0" Pin0InfoVect1LinkObjId="g_23d67e0_0" Pin0InfoVect2LinkObjId="SW-72340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72339_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="304,-432 304,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dcbee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-474 304,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3bdb250@0" ObjectIDND1="g_23d67e0@0" ObjectIDZND0="16180@x" ObjectIDZND1="16181@x" Pin0InfoVect0LinkObjId="SW-72339_0" Pin0InfoVect1LinkObjId="SW-72340_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3bdb250_0" Pin1InfoVect1LinkObjId="g_23d67e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="304,-474 304,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_43f2600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-652 1317,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_25e55d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e55d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-652 1317,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_43f2830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-518 1330,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-518 1330,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4664b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-652 1577,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3e5b360@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e5b360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-652 1577,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3425b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-705 1317,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_25e55d0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e55d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-705 1317,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40f3d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1564,-522 1564,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1564,-522 1564,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e5b110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-705 1577,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3e5b360@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e5b360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-705 1577,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f18790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1367,-799 1379,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3f189f0@0" Pin0InfoVect0LinkObjId="g_3f189f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1367,-799 1379,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40e04c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1628,-772 1640,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_40e3110@0" Pin0InfoVect0LinkObjId="g_40e3110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-772 1640,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fce200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-327 1280,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16170@1" ObjectIDZND0="16152@0" Pin0InfoVect0LinkObjId="g_3fc7840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-327 1280,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34377e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-274 1339,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3cc0370@0" ObjectIDZND0="16172@1" Pin0InfoVect0LinkObjId="SW-72374_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cc0370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-274 1339,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a2400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1256,-201 1280,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2a652e0@0" ObjectIDZND0="22942@x" ObjectIDZND1="22963@x" Pin0InfoVect0LinkObjId="SW-72367_0" Pin0InfoVect1LinkObjId="SW-72333_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a652e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1256,-201 1280,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a2630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-223 1280,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22942@0" ObjectIDZND0="g_2a652e0@0" ObjectIDZND1="22963@x" Pin0InfoVect0LinkObjId="g_2a652e0_0" Pin0InfoVect1LinkObjId="SW-72333_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-223 1280,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a2890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-201 1280,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22942@x" ObjectIDND1="g_2a652e0@0" ObjectIDZND0="22963@1" Pin0InfoVect0LinkObjId="SW-72333_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72367_0" Pin1InfoVect1LinkObjId="g_2a652e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-201 1280,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3feb420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1303,-274 1280,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16172@0" ObjectIDZND0="22942@x" ObjectIDZND1="16170@x" Pin0InfoVect0LinkObjId="SW-72367_0" Pin0InfoVect1LinkObjId="SW-72332_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1303,-274 1280,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3feb680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-250 1280,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22942@1" ObjectIDZND0="16170@x" ObjectIDZND1="16172@x" Pin0InfoVect0LinkObjId="SW-72332_0" Pin0InfoVect1LinkObjId="SW-72374_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72367_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-250 1280,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3feb8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-274 1280,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22942@x" ObjectIDND1="16172@x" ObjectIDZND0="16170@0" Pin0InfoVect0LinkObjId="SW-72332_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72367_0" Pin1InfoVect1LinkObjId="SW-72374_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-274 1280,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42694c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-126 1339,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e61e20@0" ObjectIDZND0="22964@1" Pin0InfoVect0LinkObjId="SW-72375_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e61e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-126 1339,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4269720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1303,-126 1280,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="22964@0" ObjectIDZND0="22963@x" ObjectIDZND1="43346@x" Pin0InfoVect0LinkObjId="SW-72333_0" Pin0InfoVect1LinkObjId="SM-CX_XT.P4_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72375_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1303,-126 1280,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4269980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-145 1280,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="22963@0" ObjectIDZND0="22964@x" ObjectIDZND1="43346@x" Pin0InfoVect0LinkObjId="SW-72375_0" Pin0InfoVect1LinkObjId="SM-CX_XT.P4_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-145 1280,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360e390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-126 1280,-62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="22963@x" ObjectIDND1="22964@x" ObjectIDZND0="43346@0" Pin0InfoVect0LinkObjId="SM-CX_XT.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72333_0" Pin1InfoVect1LinkObjId="SW-72375_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-126 1280,-62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c2d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-273 1147,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25dec30@0" ObjectIDZND0="16178@1" Pin0InfoVect0LinkObjId="SW-72361_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25dec30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-273 1147,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c2f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-326 1088,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16176@1" ObjectIDZND0="16152@0" Pin0InfoVect0LinkObjId="g_3fc7840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72359_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-326 1088,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbadf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1064,-200 1088,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_363d170@0" ObjectIDZND0="22941@x" ObjectIDZND1="16177@x" Pin0InfoVect0LinkObjId="SW-72358_0" Pin0InfoVect1LinkObjId="SW-72360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_363d170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1064,-200 1088,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352af00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-222 1088,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22941@0" ObjectIDZND0="g_363d170@0" ObjectIDZND1="16177@x" Pin0InfoVect0LinkObjId="g_363d170_0" Pin0InfoVect1LinkObjId="SW-72360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72358_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-222 1088,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352b0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-200 1088,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22941@x" ObjectIDND1="g_363d170@0" ObjectIDZND0="16177@1" Pin0InfoVect0LinkObjId="SW-72360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72358_0" Pin1InfoVect1LinkObjId="g_363d170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-200 1088,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352b320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1111,-273 1088,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16178@0" ObjectIDZND0="22941@x" ObjectIDZND1="16176@x" Pin0InfoVect0LinkObjId="SW-72358_0" Pin0InfoVect1LinkObjId="SW-72359_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1111,-273 1088,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352b550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-249 1088,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22941@1" ObjectIDZND0="16176@x" ObjectIDZND1="16178@x" Pin0InfoVect0LinkObjId="SW-72359_0" Pin0InfoVect1LinkObjId="SW-72361_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72358_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-249 1088,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9a420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-273 1088,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22941@x" ObjectIDND1="16178@x" ObjectIDZND0="16176@0" Pin0InfoVect0LinkObjId="SW-72359_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72358_0" Pin1InfoVect1LinkObjId="SW-72361_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-273 1088,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9a680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-125 1147,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_40ac5e0@0" ObjectIDZND0="16179@1" Pin0InfoVect0LinkObjId="SW-72362_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40ac5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-125 1147,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9a8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1111,-125 1088,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="16179@0" ObjectIDZND0="16177@x" ObjectIDZND1="43345@x" Pin0InfoVect0LinkObjId="SW-72360_0" Pin0InfoVect1LinkObjId="SM-CX_XT.P3_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72362_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1111,-125 1088,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cac8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-144 1088,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="16177@0" ObjectIDZND0="16179@x" ObjectIDZND1="43345@x" Pin0InfoVect0LinkObjId="SW-72362_0" Pin0InfoVect1LinkObjId="SM-CX_XT.P3_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-144 1088,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cacb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-125 1088,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="16177@x" ObjectIDND1="16179@x" ObjectIDZND0="43345@0" Pin0InfoVect0LinkObjId="SM-CX_XT.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72360_0" Pin1InfoVect1LinkObjId="SW-72362_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-125 1088,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26434f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-354 768,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16152@0" ObjectIDZND0="22952@0" Pin0InfoVect0LinkObjId="SW-72316_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fc7840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-354 768,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c9bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-407 768,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22952@1" ObjectIDZND0="22954@x" ObjectIDZND1="22944@x" Pin0InfoVect0LinkObjId="SW-72350_0" Pin0InfoVect1LinkObjId="SW-72315_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72316_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="768,-407 768,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_411cee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-421 755,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22944@x" ObjectIDND1="22952@x" ObjectIDZND0="22954@1" Pin0InfoVect0LinkObjId="SW-72350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72315_0" Pin1InfoVect1LinkObjId="SW-72316_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-421 755,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2388c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-421 698,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22954@0" ObjectIDZND0="g_3f5e790@0" Pin0InfoVect0LinkObjId="g_3f5e790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-421 698,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a13bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-421 768,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22954@x" ObjectIDND1="22952@x" ObjectIDZND0="22944@0" Pin0InfoVect0LinkObjId="SW-72315_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72350_0" Pin1InfoVect1LinkObjId="SW-72316_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-421 768,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a13e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-485 746,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22944@x" ObjectIDND1="22953@x" ObjectIDZND0="g_22ca230@0" Pin0InfoVect0LinkObjId="g_22ca230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72315_0" Pin1InfoVect1LinkObjId="SW-72349_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-485 746,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c9fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-466 768,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22944@1" ObjectIDZND0="g_22ca230@0" ObjectIDZND1="22953@x" Pin0InfoVect0LinkObjId="g_22ca230_0" Pin0InfoVect1LinkObjId="SW-72349_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72315_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="768,-466 768,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f7b7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-485 768,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_22ca230@0" ObjectIDND1="22944@x" ObjectIDZND0="22953@0" Pin0InfoVect0LinkObjId="SW-72349_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22ca230_0" Pin1InfoVect1LinkObjId="SW-72315_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-485 768,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40cd3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="714,-629 657,-629 657,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_3d083e0@0" ObjectIDND1="23030@x" ObjectIDZND0="22951@1" Pin0InfoVect0LinkObjId="SW-72314_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d083e0_0" Pin1InfoVect1LinkObjId="g_22ed4d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="714,-629 657,-629 657,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d2db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="672,-540 672,-550 657,-550 657,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e73030@0" ObjectIDZND0="22951@0" Pin0InfoVect0LinkObjId="SW-72314_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e73030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="672,-540 672,-550 657,-550 657,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ed4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-537 768,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="22953@1" ObjectIDZND0="23030@0" Pin0InfoVect0LinkObjId="g_2a01f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72349_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-537 768,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a01f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="714,-595 714,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_3d083e0@0" ObjectIDZND0="23030@x" ObjectIDZND1="22951@x" Pin0InfoVect0LinkObjId="g_22ed4d0_0" Pin0InfoVect1LinkObjId="SW-72314_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d083e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="714,-595 714,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a02160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="714,-629 769,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_3d083e0@0" ObjectIDND1="22951@x" ObjectIDZND0="23030@x" Pin0InfoVect0LinkObjId="g_22ed4d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d083e0_0" Pin1InfoVect1LinkObjId="SW-72314_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="714,-629 769,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_41c5df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-664 698,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22946@0" ObjectIDZND0="g_3f5dd00@0" Pin0InfoVect0LinkObjId="g_3f5dd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72345_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-664 698,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_41c6050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-664 755,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23030@x" ObjectIDND1="22945@x" ObjectIDZND0="22946@1" Pin0InfoVect0LinkObjId="SW-72345_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22ed4d0_0" Pin1InfoVect1LinkObjId="SW-72344_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-664 755,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_425bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-648 768,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23030@1" ObjectIDZND0="22946@x" ObjectIDZND1="22945@x" Pin0InfoVect0LinkObjId="SW-72345_0" Pin0InfoVect1LinkObjId="SW-72344_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ed4d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="768,-648 768,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40b0970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-744 698,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22947@0" ObjectIDZND0="g_3cc5000@0" Pin0InfoVect0LinkObjId="g_3cc5000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72346_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-744 698,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40b0bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-744 755,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="16175@x" ObjectIDND1="48132@0" ObjectIDZND0="22947@1" Pin0InfoVect0LinkObjId="SW-72346_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72343_0" Pin1InfoVect1LinkObjId="g_3d035d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-744 755,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a78c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-823 698,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22949@0" ObjectIDZND0="g_2a823c0@0" Pin0InfoVect0LinkObjId="g_2a823c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72348_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-823 698,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a78ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-823 755,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16175@x" ObjectIDND1="22948@x" ObjectIDZND0="22949@1" Pin0InfoVect0LinkObjId="SW-72348_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72343_0" Pin1InfoVect1LinkObjId="SW-72347_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-823 755,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd3b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-664 768,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="22946@x" ObjectIDND1="23030@x" ObjectIDZND0="22945@0" Pin0InfoVect0LinkObjId="SW-72344_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72345_0" Pin1InfoVect1LinkObjId="g_22ed4d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-664 768,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd3d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,-733 768,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48132@0" ObjectIDZND0="22947@x" ObjectIDZND1="16175@x" Pin0InfoVect0LinkObjId="SW-72346_0" Pin0InfoVect1LinkObjId="SW-72343_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d035d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="770,-733 768,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fd3ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-823 768,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22949@x" ObjectIDND1="16175@x" ObjectIDZND0="22948@0" Pin0InfoVect0LinkObjId="SW-72347_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72348_0" Pin1InfoVect1LinkObjId="SW-72343_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-823 768,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3353380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-744 768,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="breaker" ObjectIDND0="22947@x" ObjectIDND1="48132@0" ObjectIDZND0="16175@0" Pin0InfoVect0LinkObjId="SW-72343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72346_0" Pin1InfoVect1LinkObjId="g_3d035d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-744 768,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33535e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-797 768,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16175@1" ObjectIDZND0="22949@x" ObjectIDZND1="22948@x" Pin0InfoVect0LinkObjId="SW-72348_0" Pin0InfoVect1LinkObjId="SW-72347_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72343_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="768,-797 768,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db3af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-883 768,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="currentTransformer" EndDevType2="lightningRod" ObjectIDND0="22948@1" ObjectIDZND0="22950@x" ObjectIDZND1="g_3df5e90@0" ObjectIDZND2="g_3d6d530@0" Pin0InfoVect0LinkObjId="SW-72312_0" Pin0InfoVect1LinkObjId="g_3df5e90_0" Pin0InfoVect2LinkObjId="g_3d6d530_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72347_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="768,-883 768,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_42b9f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-903 782,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3df5e90@0" ObjectIDND1="g_3d6d530@0" ObjectIDND2="31886@1" ObjectIDZND0="22950@0" Pin0InfoVect0LinkObjId="SW-72312_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3df5e90_0" Pin1InfoVect1LinkObjId="g_3d6d530_0" Pin1InfoVect2LinkObjId="g_3e2a860_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-903 782,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_42ba1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="818,-903 835,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22950@1" ObjectIDZND0="g_3538160@0" Pin0InfoVect0LinkObjId="g_3538160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72312_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="818,-903 835,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_42ba440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-936 747,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="currentTransformer" ObjectIDND0="22950@x" ObjectIDND1="22948@x" ObjectIDND2="g_3d6d530@0" ObjectIDZND0="g_3df5e90@0" Pin0InfoVect0LinkObjId="g_3df5e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-72312_0" Pin1InfoVect1LinkObjId="SW-72347_0" Pin1InfoVect2LinkObjId="g_3d6d530_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-936 747,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3df5c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-903 768,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="currentTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22950@x" ObjectIDND1="22948@x" ObjectIDZND0="g_3df5e90@0" ObjectIDZND1="g_3d6d530@0" ObjectIDZND2="31886@1" Pin0InfoVect0LinkObjId="g_3df5e90_0" Pin0InfoVect1LinkObjId="g_3d6d530_0" Pin0InfoVect2LinkObjId="g_3e2a860_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72312_0" Pin1InfoVect1LinkObjId="SW-72347_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="768,-903 768,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40e4a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-170 481,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="16160@x" ObjectIDND1="16162@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72319_0" Pin1InfoVect1LinkObjId="SW-72321_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="481,-170 481,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40bbcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="503,-170 481,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="16162@0" ObjectIDZND0="0@x" ObjectIDZND1="16160@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-72319_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="503,-170 481,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40bbf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-170 481,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="16162@x" ObjectIDZND0="16160@0" Pin0InfoVect0LinkObjId="SW-72319_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-72321_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="481,-170 481,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40bc190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-88 434,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_40e5320@0" Pin0InfoVect0LinkObjId="g_40e5320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="434,-88 434,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40bc3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="404,-70 404,-88 434,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="0@1" ObjectIDZND0="g_40e5320@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_40e5320_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="404,-70 404,-88 434,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22f1b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-88 455,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_40e5320@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40e5320_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="434,-88 455,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3de2df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1565,-288 1547,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e62850@0" ObjectIDZND0="22957@1" Pin0InfoVect0LinkObjId="SW-72335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e62850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1565,-288 1547,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267d7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-337 1488,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22955@1" ObjectIDZND0="16152@0" Pin0InfoVect0LinkObjId="g_3fc7840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-337 1488,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1511,-288 1488,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22957@0" ObjectIDZND0="22940@x" ObjectIDZND1="22955@x" Pin0InfoVect0LinkObjId="SW-72377_0" Pin0InfoVect1LinkObjId="SW-72378_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1511,-288 1488,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267dcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1565,-170 1547,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_435e3e0@0" ObjectIDZND0="22958@1" Pin0InfoVect0LinkObjId="SW-72336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_435e3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1565,-170 1547,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267df10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-288 1488,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22940@x" ObjectIDND1="22957@x" ObjectIDZND0="22955@0" Pin0InfoVect0LinkObjId="SW-72378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72377_0" Pin1InfoVect1LinkObjId="SW-72335_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-288 1488,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267e170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-288 1488,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22955@x" ObjectIDND1="22957@x" ObjectIDZND0="22940@1" Pin0InfoVect0LinkObjId="SW-72377_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72378_0" Pin1InfoVect1LinkObjId="SW-72335_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-288 1488,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9a3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-219 1488,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="22956@1" ObjectIDZND0="22940@x" ObjectIDZND1="g_3c98810@0" Pin0InfoVect0LinkObjId="SW-72377_0" Pin0InfoVect1LinkObjId="g_3c98810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72334_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-219 1488,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9a630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-248 1488,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22940@0" ObjectIDZND0="g_3c98810@0" ObjectIDZND1="22956@x" Pin0InfoVect0LinkObjId="g_3c98810_0" Pin0InfoVect1LinkObjId="SW-72334_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72377_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-248 1488,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-233 1529,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22940@x" ObjectIDND1="22956@x" ObjectIDZND0="g_3c98810@0" Pin0InfoVect0LinkObjId="g_3c98810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72377_0" Pin1InfoVect1LinkObjId="SW-72334_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-233 1529,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40ad3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-171 1488,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22956@x" ObjectIDND1="22958@x" ObjectIDZND0="22959@1" Pin0InfoVect0LinkObjId="SW-72337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72334_0" Pin1InfoVect1LinkObjId="SW-72336_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-171 1488,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40ad630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1511,-170 1488,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22958@0" ObjectIDZND0="22956@x" ObjectIDZND1="22959@x" Pin0InfoVect0LinkObjId="SW-72334_0" Pin0InfoVect1LinkObjId="SW-72337_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1511,-170 1488,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cad460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-170 1488,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22958@x" ObjectIDND1="22959@x" ObjectIDZND0="22956@0" Pin0InfoVect0LinkObjId="SW-72334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72336_0" Pin1InfoVect1LinkObjId="SW-72337_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-170 1488,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cad6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-52 1488,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22960@x" ObjectIDND1="22959@x" ObjectIDZND0="g_412dd20@0" Pin0InfoVect0LinkObjId="g_412dd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72338_0" Pin1InfoVect1LinkObjId="SW-72337_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-52 1488,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cad920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-52 1488,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22960@0" ObjectIDZND0="g_412dd20@0" ObjectIDZND1="22959@x" Pin0InfoVect0LinkObjId="g_412dd20_0" Pin0InfoVect1LinkObjId="SW-72337_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1516,-52 1488,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cadb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-52 1488,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22960@x" ObjectIDND1="g_412dd20@0" ObjectIDZND0="22959@0" Pin0InfoVect0LinkObjId="SW-72337_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-72338_0" Pin1InfoVect1LinkObjId="g_412dd20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-52 1488,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e2a860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-957 768,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="currentTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3d6d530@0" ObjectIDND1="g_3df5e90@0" ObjectIDND2="22950@x" ObjectIDZND0="31886@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d6d530_0" Pin1InfoVect1LinkObjId="g_3df5e90_0" Pin1InfoVect2LinkObjId="SW-72312_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-957 768,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34f3da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="787,-957 768,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3d6d530@0" ObjectIDZND0="g_3df5e90@0" ObjectIDZND1="22950@x" ObjectIDZND2="22948@x" Pin0InfoVect0LinkObjId="g_3df5e90_0" Pin0InfoVect1LinkObjId="SW-72312_0" Pin0InfoVect2LinkObjId="SW-72347_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d6d530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="787,-957 768,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34f4000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-957 768,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3d6d530@0" ObjectIDND1="31886@1" ObjectIDZND0="g_3df5e90@0" ObjectIDZND1="22950@x" ObjectIDZND2="22948@x" Pin0InfoVect0LinkObjId="g_3df5e90_0" Pin0InfoVect1LinkObjId="SW-72312_0" Pin0InfoVect2LinkObjId="SW-72347_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d6d530_0" Pin1InfoVect1LinkObjId="g_3e2a860_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="768,-957 768,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d035d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-721 770,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22945@1" ObjectIDZND0="48132@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-72344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="768,-721 770,-733 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-112 909,-112 912,-103 915,-111 915,-112 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-81 909,-81 912,-90 915,-82 915,-81 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-111 717,-111 720,-102 723,-110 723,-111 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-80 717,-80 720,-89 723,-81 723,-80 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1283,-110 1277,-110 1280,-101 1283,-109 1283,-110 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1283,-79 1277,-79 1280,-88 1283,-80 1283,-79 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1091,-109 1085,-109 1088,-100 1091,-108 1091,-109 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1091,-78 1085,-78 1088,-87 1091,-79 1091,-78 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="689,-583 684,-572 695,-572 689,-583 689,-582 689,-583 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="689,-597 684,-608 695,-608 689,-597 689,-598 689,-597 " stroke="rgb(170,85,127)"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-72275" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 -849.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16142" ObjectName="DYN-CX_XT"/>
     <cge:Meas_Ref ObjectId="72275"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_FS" endPointId="0" endStationName="CX_XT" flowDrawDirect="1" flowShape="0" id="AC-110kV.Fangxiu_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="768,-963 768,-994 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31886" ObjectName="AC-110kV.Fangxiu_line"/>
    <cge:TPSR_Ref TObjectID="31886_SS-126"/></metadata>
   <polyline fill="none" opacity="0" points="768,-963 768,-994 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XT"/>
</svg>