<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-187" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="1347 -1405 2072 1190">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="4" y2="4"/>
    <ellipse cx="14" cy="18" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="22" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="17" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="24" x2="24" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="24" y1="8" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="24" y1="10" y2="12"/>
    <ellipse cx="8" cy="10" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape125">
    <ellipse cx="14" cy="17" fillStyle="0" rx="9" ry="7" stroke-width="0.153636"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="20" x2="20" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="14" y1="16" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4efd000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4efddf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4efe7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4eff480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4f003a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4f01040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f01850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4f02140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4f03240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4f03240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f04740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f04740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f06320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f06320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_4f06fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f08c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4f09950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4f0a770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4f0b0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f0c770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f0d290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f0da10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4f0e1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f0f540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f0ff80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f10a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4f11430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4f127f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4f13110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_4f140f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4f14cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4f23260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4f16110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_4f16dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_4f17db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1200" width="2082" x="1342" y="-1410"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="1882" x2="1902" y1="-511" y2="-511"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="1902" x2="1902" y1="-491" y2="-511"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3360" x2="3360" y1="-428" y2="-448"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3340" x2="3360" y1="-448" y2="-448"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3366" x2="3354" y1="-428" y2="-428"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.228882" x1="3361" x2="3359" y1="-421" y2="-421"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.322998" x1="3363" x2="3357" y1="-424" y2="-424"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1908" x2="1896" y1="-491" y2="-491"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.228882" x1="1903" x2="1901" y1="-484" y2="-484"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.322998" x1="1905" x2="1899" y1="-487" y2="-487"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-130139">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2324.788423 -984.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23856" ObjectName="SW-NH_SQ.NH_SQ_301BK"/>
     <cge:Meas_Ref ObjectId="130139"/>
    <cge:TPSR_Ref TObjectID="23856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2323.788423 -775.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23857" ObjectName="SW-NH_SQ.NH_SQ_001BK"/>
     <cge:Meas_Ref ObjectId="130142"/>
    <cge:TPSR_Ref TObjectID="23857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130146">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3112.788423 -984.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23859" ObjectName="SW-NH_SQ.NH_SQ_302BK"/>
     <cge:Meas_Ref ObjectId="130146"/>
    <cge:TPSR_Ref TObjectID="23859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130149">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3111.788423 -775.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23861" ObjectName="SW-NH_SQ.NH_SQ_002BK"/>
     <cge:Meas_Ref ObjectId="130149"/>
    <cge:TPSR_Ref TObjectID="23861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130176">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3080.670659 -532.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23879" ObjectName="SW-NH_SQ.NH_SQ_085BK"/>
     <cge:Meas_Ref ObjectId="130176"/>
    <cge:TPSR_Ref TObjectID="23879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2090.670659 -539.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23869" ObjectName="SW-NH_SQ.NH_SQ_081BK"/>
     <cge:Meas_Ref ObjectId="130158"/>
    <cge:TPSR_Ref TObjectID="23869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130163">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2281.670659 -540.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23872" ObjectName="SW-NH_SQ.NH_SQ_082BK"/>
     <cge:Meas_Ref ObjectId="130163"/>
    <cge:TPSR_Ref TObjectID="23872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130168">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2472.670659 -540.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23875" ObjectName="SW-NH_SQ.NH_SQ_083BK"/>
     <cge:Meas_Ref ObjectId="130168"/>
    <cge:TPSR_Ref TObjectID="23875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130173">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2663.670659 -539.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23878" ObjectName="SW-NH_SQ.NH_SQ_084BK"/>
     <cge:Meas_Ref ObjectId="130173"/>
    <cge:TPSR_Ref TObjectID="23878"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NH_SQ.NH_SQ_9IM1">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1809,-676 2916,-676 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29741" ObjectName="BS-NH_SQ.NH_SQ_9IM1"/>
    <cge:TPSR_Ref TObjectID="29741"/></metadata>
   <polyline fill="none" opacity="0" points="1809,-676 2916,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_SQ.NH_SQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2111,-1141 3350,-1141 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23852" ObjectName="BS-NH_SQ.NH_SQ_3IM"/>
    <cge:TPSR_Ref TObjectID="23852"/></metadata>
   <polyline fill="none" opacity="0" points="2111,-1141 3350,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_SQ.NH_SQ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3012,-671 3414,-671 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23851" ObjectName="BS-NH_SQ.NH_SQ_9IIM"/>
    <cge:TPSR_Ref TObjectID="23851"/></metadata>
   <polyline fill="none" opacity="0" points="3012,-671 3414,-671 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-NH_SQ.NH_SQ_085Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3081.000000 -318.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33924" ObjectName="EC-NH_SQ.NH_SQ_085Ld"/>
    <cge:TPSR_Ref TObjectID="33924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_SQ.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2091.000000 -357.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33920" ObjectName="EC-NH_SQ.081Ld"/>
    <cge:TPSR_Ref TObjectID="33920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_SQ.NH_SQ_082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2282.000000 -358.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33921" ObjectName="EC-NH_SQ.NH_SQ_082Ld"/>
    <cge:TPSR_Ref TObjectID="33921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_SQ.NH_SQ_083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2473.000000 -358.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33922" ObjectName="EC-NH_SQ.NH_SQ_083Ld"/>
    <cge:TPSR_Ref TObjectID="33922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_SQ.NH_SQ_084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2664.000000 -300.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33923" ObjectName="EC-NH_SQ.NH_SQ_084Ld"/>
    <cge:TPSR_Ref TObjectID="33923"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_8647ef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2626.000000 -1237.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4783b20" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.227273 -1.000000 -0.000000 3274.893365 -480.032390)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ecc3b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.227273 -1.000000 -0.000000 2634.000000 -961.032390)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_38e5210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,-1243 2608,-1243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_8647ef0@0" ObjectIDZND0="23854@1" Pin0InfoVect0LinkObjId="SW-130136_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_8647ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2630,-1243 2608,-1243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28011f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2559,-1343 2559,-1308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38073@1" ObjectIDZND0="g_45ad1e0@0" ObjectIDZND1="g_4047c50@0" ObjectIDZND2="23854@x" Pin0InfoVect0LinkObjId="g_45ad1e0_0" Pin0InfoVect1LinkObjId="g_4047c50_0" Pin0InfoVect2LinkObjId="SW-130136_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2559,-1343 2559,-1308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_7a40b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3269,-474 3269,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4783b20@0" ObjectIDZND0="23864@1" Pin0InfoVect0LinkObjId="SW-130153_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4783b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3269,-474 3269,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2849350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2334,-992 2334,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23856@0" ObjectIDZND0="23884@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130139_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2334,-992 2334,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27943a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2333,-867 2333,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="23884@0" ObjectIDZND0="23857@1" Pin0InfoVect0LinkObjId="SW-130142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2849350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2333,-867 2333,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_477eef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2333,-783 2333,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23857@0" ObjectIDZND0="23858@1" Pin0InfoVect0LinkObjId="SW-130144_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2333,-783 2333,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39f63f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2334,-1064 2334,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23853@0" ObjectIDZND0="23856@1" Pin0InfoVect0LinkObjId="SW-130139_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2334,-1064 2334,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4408340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2333,-720 2333,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23858@0" ObjectIDZND0="29741@0" Pin0InfoVect0LinkObjId="g_4638f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130144_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2333,-720 2333,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2853bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2334,-1100 2334,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23853@1" ObjectIDZND0="23852@0" Pin0InfoVect0LinkObjId="g_84c3980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130135_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2334,-1100 2334,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_84c3980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2559,-1178 2559,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23855@0" ObjectIDZND0="23852@0" Pin0InfoVect0LinkObjId="g_2853bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2559,-1178 2559,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_8592bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3122,-992 3122,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23859@0" ObjectIDZND0="23891@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130146_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3122,-992 3122,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_7b805d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3121,-867 3121,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="23891@1" ObjectIDZND0="23861@1" Pin0InfoVect0LinkObjId="SW-130149_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_8592bb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3121,-867 3121,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2358bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3121,-783 3121,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23861@0" ObjectIDZND0="23862@1" Pin0InfoVect0LinkObjId="SW-130151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130149_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3121,-783 3121,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28024e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3122,-1064 3122,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23860@0" ObjectIDZND0="23859@1" Pin0InfoVect0LinkObjId="SW-130146_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130148_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3122,-1064 3122,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_81202e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3122,-1101 3122,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23860@1" ObjectIDZND0="23852@0" Pin0InfoVect0LinkObjId="g_2853bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130148_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3122,-1101 3122,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_282bb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2559,-1308 2591,-1308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_4047c50@0" ObjectIDND1="38073@1" ObjectIDND2="23854@x" ObjectIDZND0="g_45ad1e0@0" Pin0InfoVect0LinkObjId="g_45ad1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4047c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="SW-130136_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2559,-1308 2591,-1308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_7a40630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3332,-598 3332,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23863@0" ObjectIDZND0="g_476e880@0" ObjectIDZND1="g_2729680@0" ObjectIDZND2="23864@x" Pin0InfoVect0LinkObjId="g_476e880_0" Pin0InfoVect1LinkObjId="g_2729680_0" Pin0InfoVect2LinkObjId="SW-130153_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3332,-598 3332,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_7af6b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3332,-557 3393,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23863@x" ObjectIDND1="g_2729680@0" ObjectIDND2="23864@x" ObjectIDZND0="g_476e880@0" Pin0InfoVect0LinkObjId="g_476e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130152_0" Pin1InfoVect1LinkObjId="g_2729680_0" Pin1InfoVect2LinkObjId="SW-130153_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3332,-557 3393,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e39e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3332,-557 3332,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23863@x" ObjectIDND1="g_476e880@0" ObjectIDND2="23864@x" ObjectIDZND0="g_2729680@0" Pin0InfoVect0LinkObjId="g_2729680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130152_0" Pin1InfoVect1LinkObjId="g_476e880_0" Pin1InfoVect2LinkObjId="SW-130153_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3332,-557 3332,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3911340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3332,-490 3332,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2729680@1" ObjectIDZND0="g_28444c0@0" Pin0InfoVect0LinkObjId="g_28444c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2729680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3332,-490 3332,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39de5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2690,-1027 2751,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_472af50@0" ObjectIDND1="23866@x" ObjectIDND2="23865@x" ObjectIDZND0="g_46906a0@0" Pin0InfoVect0LinkObjId="g_46906a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_472af50_0" Pin1InfoVect1LinkObjId="SW-130155_0" Pin1InfoVect2LinkObjId="SW-130154_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2690,-1027 2751,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38f7360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2690,-1027 2690,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_46906a0@0" ObjectIDND1="23866@x" ObjectIDND2="23865@x" ObjectIDZND0="g_472af50@0" Pin0InfoVect0LinkObjId="g_472af50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_46906a0_0" Pin1InfoVect1LinkObjId="SW-130155_0" Pin1InfoVect2LinkObjId="SW-130154_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2690,-1027 2690,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_7d409a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2690,-960 2690,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_472af50@1" ObjectIDZND0="g_46b16b0@0" Pin0InfoVect0LinkObjId="g_46b16b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_472af50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2690,-960 2690,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3971500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2465,-1247 2465,-1255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="23905@1" ObjectIDZND0="g_4047c50@0" Pin0InfoVect0LinkObjId="g_4047c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2465,-1247 2465,-1255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38d2bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2465,-1300 2465,-1308 2559,-1308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_4047c50@1" ObjectIDZND0="g_45ad1e0@0" ObjectIDZND1="38073@1" ObjectIDZND2="23854@x" Pin0InfoVect0LinkObjId="g_45ad1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-130136_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4047c50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2465,-1300 2465,-1308 2559,-1308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38e3560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2543,-1122 2543,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_4013020@0" ObjectIDZND0="23852@0" Pin0InfoVect0LinkObjId="g_2853bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4013020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2543,-1122 2543,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e9900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3090,-601 3090,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23880@0" ObjectIDZND0="23879@1" Pin0InfoVect0LinkObjId="SW-130176_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130178_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3090,-601 3090,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38de900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3090,-540 3090,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23879@0" ObjectIDZND0="23881@1" Pin0InfoVect0LinkObjId="SW-130179_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3090,-540 3090,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ec860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3332,-671 3332,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23851@0" ObjectIDZND0="23863@1" Pin0InfoVect0LinkObjId="SW-130152_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_478f4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3332,-671 3332,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38d6ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2690,-1027 2690,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_46906a0@0" ObjectIDND1="g_472af50@0" ObjectIDND2="23866@x" ObjectIDZND0="23865@0" Pin0InfoVect0LinkObjId="SW-130154_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_46906a0_0" Pin1InfoVect1LinkObjId="g_472af50_0" Pin1InfoVect2LinkObjId="SW-130155_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2690,-1027 2690,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_87109a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2688,-1088 2688,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23865@1" ObjectIDZND0="23852@0" Pin0InfoVect0LinkObjId="g_2853bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130154_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2688,-1088 2688,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_85d1fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2628,-955 2628,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3ecc3b0@0" ObjectIDZND0="23866@0" Pin0InfoVect0LinkObjId="SW-130155_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ecc3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2628,-955 2628,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_79c2a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2628,-1015 2628,-1027 2690,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23866@1" ObjectIDZND0="g_46906a0@0" ObjectIDZND1="g_472af50@0" ObjectIDZND2="23865@x" Pin0InfoVect0LinkObjId="g_46906a0_0" Pin0InfoVect1LinkObjId="g_472af50_0" Pin0InfoVect2LinkObjId="SW-130154_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130155_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2628,-1015 2628,-1027 2690,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fbbd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3270,-541 3270,-557 3332,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23864@0" ObjectIDZND0="23863@x" ObjectIDZND1="g_476e880@0" ObjectIDZND2="g_2729680@0" Pin0InfoVect0LinkObjId="SW-130152_0" Pin0InfoVect1LinkObjId="g_476e880_0" Pin0InfoVect2LinkObjId="g_2729680_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3270,-541 3270,-557 3332,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27867e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1876,-589 1876,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_38f2480@0" ObjectIDND1="g_281e6c0@0" ObjectIDZND0="23900@0" Pin0InfoVect0LinkObjId="SW-130391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_38f2480_0" Pin1InfoVect1LinkObjId="g_281e6c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-589 1876,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e04e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1875,-530 1875,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_281e6c0@0" ObjectIDZND0="g_3fbbf80@0" Pin0InfoVect0LinkObjId="g_3fbbf80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_281e6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1875,-530 1875,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4638f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1876,-643 1876,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23900@1" ObjectIDZND0="29741@0" Pin0InfoVect0LinkObjId="g_4408340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-643 1876,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d0db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1924,-565 1924,-590 1875,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_38f2480@0" ObjectIDZND0="23900@x" ObjectIDZND1="g_281e6c0@0" Pin0InfoVect0LinkObjId="SW-130391_0" Pin0InfoVect1LinkObjId="g_281e6c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38f2480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1924,-565 1924,-590 1875,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ce3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1876,-590 1876,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_38f2480@0" ObjectIDND1="23900@x" ObjectIDZND0="g_281e6c0@1" Pin0InfoVect0LinkObjId="g_281e6c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_38f2480_0" Pin1InfoVect1LinkObjId="SW-130391_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-590 1876,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ba2e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-547 2100,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23869@0" ObjectIDZND0="23893@1" Pin0InfoVect0LinkObjId="SW-130379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-547 2100,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39f6680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-437 2100,-414 2100,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="23893@x" ObjectIDND1="g_3cb8310@0" ObjectIDZND0="33920@0" Pin0InfoVect0LinkObjId="EC-NH_SQ.081Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130379_0" Pin1InfoVect1LinkObjId="g_3cb8310_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-437 2100,-414 2100,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_84a52c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-471 2100,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="23893@0" ObjectIDZND0="33920@x" ObjectIDZND1="g_3cb8310@0" Pin0InfoVect0LinkObjId="EC-NH_SQ.081Ld_0" Pin0InfoVect1LinkObjId="g_3cb8310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-471 2100,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d196d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-437 2131,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33920@x" ObjectIDND1="23893@x" ObjectIDZND0="g_3cb8310@0" Pin0InfoVect0LinkObjId="g_3cb8310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_SQ.081Ld_0" Pin1InfoVect1LinkObjId="SW-130379_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-437 2131,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_478a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-574 2100,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23869@1" ObjectIDZND0="23892@0" Pin0InfoVect0LinkObjId="SW-130378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130158_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-574 2100,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40bb2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-644 2100,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23892@1" ObjectIDZND0="29741@0" Pin0InfoVect0LinkObjId="g_4408340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-644 2100,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e1c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2291,-548 2291,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23872@0" ObjectIDZND0="23895@1" Pin0InfoVect0LinkObjId="SW-130382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130163_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2291,-548 2291,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e1380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2291,-438 2291,-415 2291,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="23895@x" ObjectIDND1="g_38e2e80@0" ObjectIDZND0="33921@0" Pin0InfoVect0LinkObjId="EC-NH_SQ.NH_SQ_082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130382_0" Pin1InfoVect1LinkObjId="g_38e2e80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2291,-438 2291,-415 2291,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38def80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2291,-472 2291,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="23895@0" ObjectIDZND0="33921@x" ObjectIDZND1="g_38e2e80@0" Pin0InfoVect0LinkObjId="EC-NH_SQ.NH_SQ_082Ld_0" Pin0InfoVect1LinkObjId="g_38e2e80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2291,-472 2291,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e0840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2291,-438 2322,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33921@x" ObjectIDND1="23895@x" ObjectIDZND0="g_38e2e80@0" Pin0InfoVect0LinkObjId="g_38e2e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_SQ.NH_SQ_082Ld_0" Pin1InfoVect1LinkObjId="SW-130382_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2291,-438 2322,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38db2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2291,-575 2291,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23872@1" ObjectIDZND0="23894@0" Pin0InfoVect0LinkObjId="SW-130381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130163_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2291,-575 2291,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_872b750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2291,-645 2291,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23894@1" ObjectIDZND0="29741@0" Pin0InfoVect0LinkObjId="g_4408340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2291,-645 2291,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38cbd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2482,-548 2482,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23875@0" ObjectIDZND0="23897@1" Pin0InfoVect0LinkObjId="SW-130385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2482,-548 2482,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c6790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2482,-438 2482,-415 2482,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="23897@x" ObjectIDND1="g_38cb8d0@0" ObjectIDZND0="33922@0" Pin0InfoVect0LinkObjId="EC-NH_SQ.NH_SQ_083Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130385_0" Pin1InfoVect1LinkObjId="g_38cb8d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2482,-438 2482,-415 2482,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c7510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2482,-472 2482,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="23897@0" ObjectIDZND0="33922@x" ObjectIDZND1="g_38cb8d0@0" Pin0InfoVect0LinkObjId="EC-NH_SQ.NH_SQ_083Ld_0" Pin0InfoVect1LinkObjId="g_38cb8d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2482,-472 2482,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c8290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2482,-438 2513,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33922@x" ObjectIDND1="23897@x" ObjectIDZND0="g_38cb8d0@0" Pin0InfoVect0LinkObjId="g_38cb8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_SQ.NH_SQ_083Ld_0" Pin1InfoVect1LinkObjId="SW-130385_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2482,-438 2513,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c5e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2482,-575 2482,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23875@1" ObjectIDZND0="23896@0" Pin0InfoVect0LinkObjId="SW-130384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130168_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2482,-575 2482,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f38a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2482,-645 2482,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23896@1" ObjectIDZND0="29741@0" Pin0InfoVect0LinkObjId="g_4408340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2482,-645 2482,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_8643220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-547 2673,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23878@0" ObjectIDZND0="23899@1" Pin0InfoVect0LinkObjId="SW-130388_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-547 2673,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2780220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-471 2673,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="23899@0" ObjectIDZND0="g_8647350@0" ObjectIDZND1="33923@x" Pin0InfoVect0LinkObjId="g_8647350_0" Pin0InfoVect1LinkObjId="EC-NH_SQ.NH_SQ_084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-471 2673,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_855ae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-437 2704,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="23899@x" ObjectIDND1="33923@x" ObjectIDZND0="g_8647350@0" Pin0InfoVect0LinkObjId="g_8647350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130388_0" Pin1InfoVect1LinkObjId="EC-NH_SQ.NH_SQ_084Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-437 2704,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c6e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-574 2673,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23878@1" ObjectIDZND0="23898@0" Pin0InfoVect0LinkObjId="SW-130387_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130173_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-574 2673,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e7980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-644 2673,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23898@1" ObjectIDZND0="29741@0" Pin0InfoVect0LinkObjId="g_4408340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-644 2673,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_79ea870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3121,-671 3121,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23851@0" ObjectIDZND0="23862@0" Pin0InfoVect0LinkObjId="SW-130151_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_478f4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3121,-671 3121,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_478f4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3090,-641 3090,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23880@1" ObjectIDZND0="23851@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130178_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3090,-641 3090,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44200e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2572,-1243 2559,-1243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="23854@0" ObjectIDZND0="g_45ad1e0@0" ObjectIDZND1="g_4047c50@0" ObjectIDZND2="38073@1" Pin0InfoVect0LinkObjId="g_45ad1e0_0" Pin0InfoVect1LinkObjId="g_4047c50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130136_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2572,-1243 2559,-1243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40a95d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2559,-1309 2559,-1243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_45ad1e0@0" ObjectIDND1="g_4047c50@0" ObjectIDND2="38073@1" ObjectIDZND0="23854@x" ObjectIDZND1="23855@x" Pin0InfoVect0LinkObjId="SW-130136_0" Pin0InfoVect1LinkObjId="SW-130138_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_45ad1e0_0" Pin1InfoVect1LinkObjId="g_4047c50_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2559,-1309 2559,-1243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_404a4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2559,-1243 2559,-1214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23854@x" ObjectIDND1="g_45ad1e0@0" ObjectIDND2="g_4047c50@0" ObjectIDZND0="23855@1" Pin0InfoVect0LinkObjId="SW-130138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130136_0" Pin1InfoVect1LinkObjId="g_45ad1e0_0" Pin1InfoVect2LinkObjId="g_4047c50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2559,-1243 2559,-1214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_7a58010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-437 2673,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="23899@x" ObjectIDND1="g_8647350@0" ObjectIDZND0="33923@0" Pin0InfoVect0LinkObjId="EC-NH_SQ.NH_SQ_084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130388_0" Pin1InfoVect1LinkObjId="g_8647350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-437 2673,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e3cfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3121,-431 3090,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3ecbf70@0" ObjectIDZND0="23881@x" ObjectIDZND1="33924@x" Pin0InfoVect0LinkObjId="SW-130179_0" Pin0InfoVect1LinkObjId="EC-NH_SQ.NH_SQ_085Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ecbf70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3121,-431 3090,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_476a080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3090,-464 3090,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="23881@0" ObjectIDZND0="g_3ecbf70@0" ObjectIDZND1="33924@x" Pin0InfoVect0LinkObjId="g_3ecbf70_0" Pin0InfoVect1LinkObjId="EC-NH_SQ.NH_SQ_085Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130179_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3090,-464 3090,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_7ac5940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3090,-431 3090,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3ecbf70@0" ObjectIDND1="23881@x" ObjectIDZND0="33924@0" Pin0InfoVect0LinkObjId="EC-NH_SQ.NH_SQ_085Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ecbf70_0" Pin1InfoVect1LinkObjId="SW-130179_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3090,-431 3090,-345 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="23852" cx="2559" cy="-1141" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23852" cx="3122" cy="-1141" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23852" cx="2543" cy="-1141" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23851" cx="3332" cy="-671" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23852" cx="2688" cy="-1141" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23852" cx="2334" cy="-1141" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29741" cx="1876" cy="-676" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29741" cx="2100" cy="-676" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29741" cx="2291" cy="-676" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29741" cx="2482" cy="-676" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29741" cx="2673" cy="-676" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29741" cx="2333" cy="-676" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23851" cx="3121" cy="-671" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23851" cx="3090" cy="-671" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130044" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.000000 -1295.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23836" ObjectName="DYN-NH_SQ"/>
     <cge:Meas_Ref ObjectId="130044"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_79bee50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2313.000000 -1227.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2816040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2512.000000 -1405.000000) translate(0,15)">35kV沙桥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7bf2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7bf2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7bf2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7bf2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7bf2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7bf2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7bf2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7bf2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7bf2470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_7a1e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4583d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2067.000000 -1129.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27f44c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3066.000000 -294.000000) translate(0,15)">铁路专线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28424b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3293.000000 -407.000000) translate(0,15)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40a9f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2644.000000 -907.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fe7200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -1061.000000) translate(0,15)">35kV母线避雷器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3ff43a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 -1376.000000) translate(0,16)">沙桥变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df14d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2575.000000 -1203.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1be20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2701.000000 -1078.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e177b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1085.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3faa330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -735.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c45370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3342.000000 -627.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cd0af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.000000 -490.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1a0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3277.000000 -524.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de0a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2569.000000 -1006.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6b670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1009.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1aa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -801.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6d460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2588.000000 -1269.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3941c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3106.000000 -624.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4768700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1811.000000 -483.000000) translate(0,15)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_449e610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2266.000000 -330.000000) translate(0,15)">沙桥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_86b5240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2454.000000 -330.000000) translate(0,15)">小古山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38c4c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2684.000000 -329.000000) translate(0,15)">天申堂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_277a8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1883.000000 -632.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2785120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3321.000000 -693.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_280fd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2109.000000 -568.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2785360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -633.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2785ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -496.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2771fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2300.000000 -569.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ac3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2298.000000 -634.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_281a280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2298.000000 -497.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27a7350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2491.000000 -569.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2822e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2489.000000 -634.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27a6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2489.000000 -497.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2823510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2682.000000 -568.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27a5730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2680.000000 -633.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2823090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2680.000000 -496.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2822790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2345.000000 -1013.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28232d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2343.000000 -1090.000000) translate(0,12)">3012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e7aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2344.000000 -804.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28000b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2340.000000 -745.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_27c9af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1687.000000 -1376.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4795780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1686.000000 -1340.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_47959c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3103.000000 -560.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_47848a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.000000 -1006.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_472b150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2184.000000 -891.000000) translate(0,15)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_472b150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2184.000000 -891.000000) translate(0,33)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_460d190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2990.000000 -895.000000) translate(0,15)">S9-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_460d190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2990.000000 -895.000000) translate(0,33)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4420560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -702.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3feaa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2064.000000 -329.000000) translate(0,15)">木瓜村线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3fbc1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -407.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3fbc1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -407.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3fa7c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -386.500000) translate(0,16)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3df1290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -442.000000) translate(0,16)">7381231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc8940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2220.000000 -917.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1abe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3009.000000 -916.000000) translate(0,12)">2号主变</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3090,-654 3085,-644 3095,-644 3090,-654 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3090,-656 3085,-666 3095,-666 3090,-656 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_NH" endPointId="0" endStationName="NH_SQ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shaqiao" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2559,-1343 2559,-1383 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38073" ObjectName="AC-35kV.LN_shaqiao"/>
    <cge:TPSR_Ref TObjectID="38073_SS-187"/></metadata>
   <polyline fill="none" opacity="0" points="2559,-1343 2559,-1383 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_45ad1e0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2645.000000 -1315.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_476e880">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3400.226698 -503.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2729680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3322.893365 -485.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28444c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3318.893365 -428.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_46906a0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2758.333333 -973.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_472af50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2681.000000 -955.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_46b16b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2677.000000 -917.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4047c50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2460.000000 -1250.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4013020">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2550.333333 -1068.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ecbf70">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3128.003992 -377.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fbbf80">
    <use class="BV-10KV" transform="matrix(-0.857143 -0.000000 0.000000 -1.000000 1887.000000 -494.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38f2480">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1931.333333 -511.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_281e6c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1871.000000 -523.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cb8310">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2138.003992 -383.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38e2e80">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2329.003992 -384.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38cb8d0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2520.003992 -384.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_8647350">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2711.003992 -383.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130085" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2088.000000 -283.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130085" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23869"/>
     <cge:Term_Ref ObjectID="33661"/>
    <cge:TPSR_Ref TObjectID="23869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130086" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2088.000000 -283.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23869"/>
     <cge:Term_Ref ObjectID="33661"/>
    <cge:TPSR_Ref TObjectID="23869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130082" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2088.000000 -283.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23869"/>
     <cge:Term_Ref ObjectID="33661"/>
    <cge:TPSR_Ref TObjectID="23869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.000000 -281.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23872"/>
     <cge:Term_Ref ObjectID="33667"/>
    <cge:TPSR_Ref TObjectID="23872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.000000 -281.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23872"/>
     <cge:Term_Ref ObjectID="33667"/>
    <cge:TPSR_Ref TObjectID="23872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.000000 -281.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23872"/>
     <cge:Term_Ref ObjectID="33667"/>
    <cge:TPSR_Ref TObjectID="23872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2487.000000 -282.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23875"/>
     <cge:Term_Ref ObjectID="33673"/>
    <cge:TPSR_Ref TObjectID="23875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2487.000000 -282.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23875"/>
     <cge:Term_Ref ObjectID="33673"/>
    <cge:TPSR_Ref TObjectID="23875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2487.000000 -282.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23875"/>
     <cge:Term_Ref ObjectID="33673"/>
    <cge:TPSR_Ref TObjectID="23875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2682.000000 -282.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23878"/>
     <cge:Term_Ref ObjectID="33679"/>
    <cge:TPSR_Ref TObjectID="23878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130104" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2682.000000 -282.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23878"/>
     <cge:Term_Ref ObjectID="33679"/>
    <cge:TPSR_Ref TObjectID="23878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2682.000000 -282.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23878"/>
     <cge:Term_Ref ObjectID="33679"/>
    <cge:TPSR_Ref TObjectID="23878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-130109" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3226.000000 -573.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23879"/>
     <cge:Term_Ref ObjectID="33681"/>
    <cge:TPSR_Ref TObjectID="23879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-130110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3226.000000 -573.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23879"/>
     <cge:Term_Ref ObjectID="33681"/>
    <cge:TPSR_Ref TObjectID="23879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-130106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3226.000000 -573.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23879"/>
     <cge:Term_Ref ObjectID="33681"/>
    <cge:TPSR_Ref TObjectID="23879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2471.000000 -1025.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23856"/>
     <cge:Term_Ref ObjectID="33635"/>
    <cge:TPSR_Ref TObjectID="23856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130062" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2471.000000 -1025.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23856"/>
     <cge:Term_Ref ObjectID="33635"/>
    <cge:TPSR_Ref TObjectID="23856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130058" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2471.000000 -1025.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130058" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23856"/>
     <cge:Term_Ref ObjectID="33635"/>
    <cge:TPSR_Ref TObjectID="23856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130067" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2469.000000 -819.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23857"/>
     <cge:Term_Ref ObjectID="33637"/>
    <cge:TPSR_Ref TObjectID="23857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2469.000000 -819.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23857"/>
     <cge:Term_Ref ObjectID="33637"/>
    <cge:TPSR_Ref TObjectID="23857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2469.000000 -819.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130064" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23857"/>
     <cge:Term_Ref ObjectID="33637"/>
    <cge:TPSR_Ref TObjectID="23857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -1025.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23859"/>
     <cge:Term_Ref ObjectID="33641"/>
    <cge:TPSR_Ref TObjectID="23859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130074" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -1025.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23859"/>
     <cge:Term_Ref ObjectID="33641"/>
    <cge:TPSR_Ref TObjectID="23859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130070" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -1025.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23859"/>
     <cge:Term_Ref ObjectID="33641"/>
    <cge:TPSR_Ref TObjectID="23859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -814.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23861"/>
     <cge:Term_Ref ObjectID="33645"/>
    <cge:TPSR_Ref TObjectID="23861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -814.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23861"/>
     <cge:Term_Ref ObjectID="33645"/>
    <cge:TPSR_Ref TObjectID="23861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -814.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23861"/>
     <cge:Term_Ref ObjectID="33645"/>
    <cge:TPSR_Ref TObjectID="23861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-130127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2126.000000 -1225.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23852"/>
     <cge:Term_Ref ObjectID="33624"/>
    <cge:TPSR_Ref TObjectID="23852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-130128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2126.000000 -1225.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23852"/>
     <cge:Term_Ref ObjectID="33624"/>
    <cge:TPSR_Ref TObjectID="23852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-130129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2126.000000 -1225.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23852"/>
     <cge:Term_Ref ObjectID="33624"/>
    <cge:TPSR_Ref TObjectID="23852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-130133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2126.000000 -1225.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23852"/>
     <cge:Term_Ref ObjectID="33624"/>
    <cge:TPSR_Ref TObjectID="23852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-130130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2126.000000 -1225.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23852"/>
     <cge:Term_Ref ObjectID="33624"/>
    <cge:TPSR_Ref TObjectID="23852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-130119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3020.000000 -762.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23851"/>
     <cge:Term_Ref ObjectID="33623"/>
    <cge:TPSR_Ref TObjectID="23851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-130120" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3020.000000 -762.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23851"/>
     <cge:Term_Ref ObjectID="33623"/>
    <cge:TPSR_Ref TObjectID="23851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-130121" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3020.000000 -762.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23851"/>
     <cge:Term_Ref ObjectID="33623"/>
    <cge:TPSR_Ref TObjectID="23851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-130125" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3020.000000 -762.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130125" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23851"/>
     <cge:Term_Ref ObjectID="33623"/>
    <cge:TPSR_Ref TObjectID="23851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-130122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3020.000000 -762.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23851"/>
     <cge:Term_Ref ObjectID="33623"/>
    <cge:TPSR_Ref TObjectID="23851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-130112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -805.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29741"/>
     <cge:Term_Ref ObjectID="14846"/>
    <cge:TPSR_Ref TObjectID="29741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-130113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -805.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29741"/>
     <cge:Term_Ref ObjectID="14846"/>
    <cge:TPSR_Ref TObjectID="29741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-130114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -805.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29741"/>
     <cge:Term_Ref ObjectID="14846"/>
    <cge:TPSR_Ref TObjectID="29741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-130118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -805.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29741"/>
     <cge:Term_Ref ObjectID="14846"/>
    <cge:TPSR_Ref TObjectID="29741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-130115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -805.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29741"/>
     <cge:Term_Ref ObjectID="14846"/>
    <cge:TPSR_Ref TObjectID="29741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-130116" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -805.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29741"/>
     <cge:Term_Ref ObjectID="14846"/>
    <cge:TPSR_Ref TObjectID="29741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-130117" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -805.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130117" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29741"/>
     <cge:Term_Ref ObjectID="14846"/>
    <cge:TPSR_Ref TObjectID="29741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-256012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2441.000000 -916.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="256012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23884"/>
     <cge:Term_Ref ObjectID="33687"/>
    <cge:TPSR_Ref TObjectID="23884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-130134" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2441.000000 -916.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23884"/>
     <cge:Term_Ref ObjectID="33687"/>
    <cge:TPSR_Ref TObjectID="23884"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/></g>
   <g href="cx_配调_配网接线图35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1413" y="-1404"/></g>
   <g href="35kV沙桥变10kV木瓜村线081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2109" y="-568"/></g>
   <g href="35kV沙桥变10kV沙桥线082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2300" y="-569"/></g>
   <g href="35kV沙桥变10kV小古山线083间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2491" y="-569"/></g>
   <g href="35kV沙桥变10kV天申堂线084间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2682" y="-568"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1676" y="-1385"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1676" y="-1347"/></g>
   <g href="35kV沙桥变10kV铁路专线085间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3103" y="-560"/></g>
   <g href="35kV沙桥变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="80" x="1366" y="-1006"/></g>
   <g href="35kV沙桥变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2220" y="-917"/></g>
   <g href="35kV沙桥变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="3009" y="-916"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a94010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2063.000000 1225.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39546d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2063.000000 1211.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_390aaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2063.000000 1197.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39004a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2069.000000 1182.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3905760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2055.000000 1167.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38f12a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2953.000000 762.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38eec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2953.000000 748.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38efe80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2953.000000 734.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38cc1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2959.000000 719.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38edee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2945.000000 704.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38eab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2405.000000 1025.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38e4300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2394.000000 1010.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38de240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2419.000000 995.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38da340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2405.000000 819.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38dbe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2394.000000 804.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38d9cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2419.000000 789.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38dae80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3188.000000 1025.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38dc780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3177.000000 1010.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ded80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3202.000000 995.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38e0f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3188.000000 814.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38cbb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3177.000000 799.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38d5450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3202.000000 784.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_280f490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2024.000000 270.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c6c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2049.000000 255.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c69f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2035.000000 285.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27fd560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1747.000000 777.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27fe7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1753.000000 762.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28446c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 747.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2844900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 732.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2841bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 717.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2843430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1747.000000 805.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2841df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1747.000000 791.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fe3be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2223.000000 267.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fe0f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2248.000000 252.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fc09a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2234.000000 282.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fc2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2422.000000 267.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fc3de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2447.000000 252.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fdc720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2433.000000 282.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fc3720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2618.000000 269.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fcae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2643.000000 254.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fc2940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2629.000000 284.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fc3b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3153.000000 560.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fbab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3178.000000 545.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb9330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3164.000000 575.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ad8560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2366.000000 901.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39081e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2366.000000 916.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-NH_SQ.NH_SQ_ZYB1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33709"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2480.000000 -1252.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2480.000000 -1252.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23905" ObjectName="TF-NH_SQ.NH_SQ_ZYB1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_SQ.NH_SQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33627"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3096.000000 -952.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3096.000000 -952.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23891" ObjectName="TF-NH_SQ.NH_SQ_2T"/>
    <cge:TPSR_Ref TObjectID="23891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_SQ.NH_SQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33689"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2302.000000 -862.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2302.000000 -862.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23884" ObjectName="TF-NH_SQ.NH_SQ_1T"/>
    <cge:TPSR_Ref TObjectID="23884"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="1460" y="-1386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1412" y="-1403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1413" y="-1404"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1413" y="-1404"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2109" y="-568"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2109" y="-568"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2300" y="-569"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2300" y="-569"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2491" y="-569"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2491" y="-569"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2682" y="-568"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2682" y="-568"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1676" y="-1385"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1676" y="-1385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1676" y="-1347"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1676" y="-1347"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3103" y="-560"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3103" y="-560"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="80" x="1366" y="-1006"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="80" x="1366" y="-1006"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2220" y="-917"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2220" y="-917"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="3009" y="-916"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="3009" y="-916"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-130136">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2581.000000 -1217.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23854" ObjectName="SW-NH_SQ.NH_SQ_3711SW"/>
     <cge:Meas_Ref ObjectId="130136"/>
    <cge:TPSR_Ref TObjectID="23854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130138">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2550.000000 -1173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23855" ObjectName="SW-NH_SQ.NH_SQ_3011SW"/>
     <cge:Meas_Ref ObjectId="130138"/>
    <cge:TPSR_Ref TObjectID="23855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130135">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2325.000000 -1059.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23853" ObjectName="SW-NH_SQ.NH_SQ_3012SW"/>
     <cge:Meas_Ref ObjectId="130135"/>
    <cge:TPSR_Ref TObjectID="23853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130144">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2324.000000 -715.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23858" ObjectName="SW-NH_SQ.NH_SQ_0011SW"/>
     <cge:Meas_Ref ObjectId="130144"/>
    <cge:TPSR_Ref TObjectID="23858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130148">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3113.000000 -1060.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23860" ObjectName="SW-NH_SQ.NH_SQ_3021SW"/>
     <cge:Meas_Ref ObjectId="130148"/>
    <cge:TPSR_Ref TObjectID="23860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3112.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23862" ObjectName="SW-NH_SQ.NH_SQ_0021SW"/>
     <cge:Meas_Ref ObjectId="130151"/>
    <cge:TPSR_Ref TObjectID="23862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130152">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3322.893365 -593.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23863" ObjectName="SW-NH_SQ.NH_SQ_0902SW"/>
     <cge:Meas_Ref ObjectId="130152"/>
    <cge:TPSR_Ref TObjectID="23863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130154">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2680.000000 -1047.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23865" ObjectName="SW-NH_SQ.NH_SQ_3901SW"/>
     <cge:Meas_Ref ObjectId="130154"/>
    <cge:TPSR_Ref TObjectID="23865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130178">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3081.000000 -595.857143)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23880" ObjectName="SW-NH_SQ.NH_SQ_0851SW"/>
     <cge:Meas_Ref ObjectId="130178"/>
    <cge:TPSR_Ref TObjectID="23880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130179">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3081.000000 -459.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23881" ObjectName="SW-NH_SQ.NH_SQ_0856SW"/>
     <cge:Meas_Ref ObjectId="130179"/>
    <cge:TPSR_Ref TObjectID="23881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130155">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2619.000000 -974.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23866" ObjectName="SW-NH_SQ.NH_SQ_39017SW"/>
     <cge:Meas_Ref ObjectId="130155"/>
    <cge:TPSR_Ref TObjectID="23866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130153">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.227273 -1.000000 -0.000000 3295.893365 -530.486935)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23864" ObjectName="SW-NH_SQ.NH_SQ_09027SW"/>
     <cge:Meas_Ref ObjectId="130153"/>
    <cge:TPSR_Ref TObjectID="23864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1867.000000 -602.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23900" ObjectName="SW-NH_SQ.NH_SQ_0901SW"/>
     <cge:Meas_Ref ObjectId="130391"/>
    <cge:TPSR_Ref TObjectID="23900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2091.000000 -603.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23892" ObjectName="SW-NH_SQ.NH_SQ_0811SW"/>
     <cge:Meas_Ref ObjectId="130378"/>
    <cge:TPSR_Ref TObjectID="23892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2091.000000 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23893" ObjectName="SW-NH_SQ.NH_SQ_0816SW"/>
     <cge:Meas_Ref ObjectId="130379"/>
    <cge:TPSR_Ref TObjectID="23893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130381">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2282.000000 -604.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23894" ObjectName="SW-NH_SQ.NH_SQ_8021SW"/>
     <cge:Meas_Ref ObjectId="130381"/>
    <cge:TPSR_Ref TObjectID="23894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130382">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2282.000000 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23895" ObjectName="SW-NH_SQ.NH_SQ_0826SW"/>
     <cge:Meas_Ref ObjectId="130382"/>
    <cge:TPSR_Ref TObjectID="23895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130384">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2473.000000 -604.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23896" ObjectName="SW-NH_SQ.NH_SQ_0831SW"/>
     <cge:Meas_Ref ObjectId="130384"/>
    <cge:TPSR_Ref TObjectID="23896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130385">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2473.000000 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23897" ObjectName="SW-NH_SQ.NH_SQ_0836SW"/>
     <cge:Meas_Ref ObjectId="130385"/>
    <cge:TPSR_Ref TObjectID="23897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2664.000000 -603.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23898" ObjectName="SW-NH_SQ.NH_SQ_0841SW"/>
     <cge:Meas_Ref ObjectId="130387"/>
    <cge:TPSR_Ref TObjectID="23898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130388">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2664.000000 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23899" ObjectName="SW-NH_SQ.NH_SQ_0846SW"/>
     <cge:Meas_Ref ObjectId="130388"/>
    <cge:TPSR_Ref TObjectID="23899"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1448.500000 -1327.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208957" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 -1232.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208957" ObjectName="NH_SQ:NH_SQ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208957" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 -1193.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208957" ObjectName="NH_SQ:NH_SQ_sumP"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="NH_SQ"/>
</svg>