<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="786686" id="thSvg" viewBox="-22 -960 2552 1224">
 
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_282d750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_283e420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_283ed60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_283fcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2840f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2841b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2842740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2843170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28439e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28443c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2844fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2845920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2846220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2846dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2847770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2848080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2849aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_284a510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_284add0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_284b7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_284c9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_284d320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_284de10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28531f0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2853f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_284fb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2851060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_285f8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2855f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1234" width="2562" x="-27" y="-965"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="45" x2="624" y1="-577" y2="-577"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-555" y2="-555"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-534" y2="-534"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-511" y2="-511"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-489" y2="-489"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-468" y2="-468"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-446" y2="-446"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-423" y2="-423"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="44" y1="-662" y2="-292"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="81" x2="81" y1="-640" y2="-292"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="205" x2="205" y1="-640" y2="-292"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="266" x2="266" y1="-641" y2="-292"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="446" x2="446" y1="-640" y2="-292"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-640" y2="-640"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="520" x2="520" y1="-292" y2="-640"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-402" y2="-402"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-662" y2="-662"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="332" x2="332" y1="-639" y2="-292"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-599" y2="-599"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-379" y2="-379"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-292" y2="-292"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-313" y2="-313"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="44" x2="624" y1="-336" y2="-336"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="45" x2="624" y1="-357" y2="-357"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="624" x2="624" y1="-292" y2="-662"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-556" y2="-556"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-535" y2="-535"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-512" y2="-512"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-490" y2="-490"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-469" y2="-469"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-447" y2="-447"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-424" y2="-424"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="653" y1="-663" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="690" x2="690" y1="-640" y2="213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="814" x2="814" y1="-641" y2="211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="875" x2="875" y1="-645" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1055" x2="1055" y1="-641" y2="213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-641" y2="-641"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1129" x2="1129" y1="212" y2="-640"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-403" y2="-403"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-663" y2="-663"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="941" x2="941" y1="-639" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-600" y2="-600"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-380" y2="-380"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-293" y2="-293"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-314" y2="-314"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-337" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="654" x2="1233" y1="-358" y2="-358"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1233" x2="1233" y1="212" y2="-662"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="654" x2="1233" y1="-578" y2="-578"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-206" y2="-206"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-183" y2="-183"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-161" y2="-161"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-140" y2="-140"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-118" y2="-118"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-95" y2="-95"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-74" y2="-74"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-271" y2="-271"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-51" y2="-51"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="36" y2="36"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="15" y2="15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-8" y2="-8"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="654" x2="1233" y1="-29" y2="-29"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="654" x2="1233" y1="-249" y2="-249"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="-227" y2="-227"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="82" y2="82"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="103" y2="103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="126" y2="126"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="213" y2="213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="192" y2="192"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="169" y2="169"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="654" x2="1233" y1="148" y2="148"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="653" x2="1233" y1="59" y2="59"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-535" y2="-535"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-512" y2="-512"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-490" y2="-490"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-469" y2="-469"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-447" y2="-447"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-424" y2="-424"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1259" y1="-663" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1296" x2="1296" y1="-640" y2="213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1420" x2="1420" y1="-641" y2="211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1481" x2="1481" y1="-645" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1661" x2="1661" y1="-641" y2="213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-641" y2="-641"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1735" x2="1735" y1="212" y2="-640"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-403" y2="-403"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-663" y2="-663"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1547" x2="1547" y1="-639" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-600" y2="-600"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-380" y2="-380"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-293" y2="-293"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-314" y2="-314"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-337" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1260" x2="1839" y1="-358" y2="-358"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1839" x2="1839" y1="212" y2="-662"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1260" x2="1839" y1="-578" y2="-578"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-206" y2="-206"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-183" y2="-183"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-161" y2="-161"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-140" y2="-140"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-118" y2="-118"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-95" y2="-95"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-74" y2="-74"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-271" y2="-271"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-51" y2="-51"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="36" y2="36"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="15" y2="15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-8" y2="-8"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1260" x2="1839" y1="-29" y2="-29"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-227" y2="-227"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="82" y2="82"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="103" y2="103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="126" y2="126"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="213" y2="213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="192" y2="192"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="169" y2="169"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1260" x2="1839" y1="148" y2="148"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="59" y2="59"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="1259" x2="1839" y1="-556" y2="-556"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/jt3.png" imageHeight="128" imageWidth="128">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="42" x="2450" y="-757"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="42" x="2450" y="-757"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/fhzy.png" imageHeight="83" imageWidth="84">
    <a>
     
     <rect fill="none" height="36" qtmmishow="hidden" width="42" x="2450" y="-862"/>
    </a>
   <metadata/><rect fill="white" height="36" opacity="0" stroke="white" transform="" width="42" x="2450" y="-862"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/btnd_02.png" UpImage="image/btnd_01.png" imageHeight="33" imageWidth="134">
    <a>
     
     <rect fill="none" height="34" qtmmishow="hidden" width="207" x="233" y="-731"/>
    </a>
   <metadata/><rect fill="white" height="34" opacity="0" stroke="white" transform="" width="207" x="233" y="-731"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/btnd_02.png" UpImage="image/btnd_01.png" imageHeight="33" imageWidth="134">
    <a>
     
     <rect fill="none" height="34" qtmmishow="hidden" width="203" x="27" y="-731"/>
    </a>
   <metadata/><rect fill="white" height="34" opacity="0" stroke="white" transform="" width="203" x="27" y="-731"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/btnd_02.png" UpImage="image/btnd_01.png" imageHeight="33" imageWidth="134">
    <a>
     
     <rect fill="none" height="34" qtmmishow="hidden" width="350" x="437" y="-728"/>
    </a>
   <metadata/><rect fill="white" height="34" opacity="0" stroke="white" transform="" width="350" x="437" y="-728"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_地调_变压器负载率监视.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="42" x="2450" y="-757"/></g>
   <g href="cx_index.svg" style="fill-opacity:0"><rect height="36" qtmmishow="hidden" width="42" x="2450" y="-862"/></g>
   <g href="cx_地调_重载主变潮流监控.svg" style="fill-opacity:0"><rect height="34" qtmmishow="hidden" width="207" x="233" y="-731"/></g>
   <g href="cx_地调_重载线路潮流监控.svg" style="fill-opacity:0"><rect height="34" qtmmishow="hidden" width="203" x="27" y="-731"/></g>
   <g href="cx_地调_110kV及部分220kV线路限流表.svg" style="fill-opacity:0"><rect height="34" qtmmishow="hidden" width="350" x="437" y="-728"/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6627" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -571.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6627" ObjectName="CX_ZX:CX_ZX_287BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6561" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -550.600000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6561" ObjectName="CX_ZX:CX_ZX_283BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-190495" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -527.400000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190495" ObjectName="CX_LuC:CX_LuC_275BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-190485" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -505.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190485" ObjectName="CX_LuC:CX_LuC_274BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187570" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -484.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187570" ObjectName="CX_LuC:CX_LuC_271BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187573" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -462.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187573" ObjectName="CX_LuC:CX_LuC_269BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187564" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -439.600000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187564" ObjectName="CX_LuC:CX_LuC_262BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12941" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -593.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12941" ObjectName="CX_XJH:CX_XJH_221BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12821" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -396.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12821" ObjectName="CX_XJH:CX_XJH_223BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187567" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -418.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187567" ObjectName="CX_LuC:CX_LuC_267BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -373.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -308.200000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -329.400000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -351.600000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6560" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -550.600000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6560" ObjectName="CX_ZX:CX_ZX_283BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-190498" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -527.400000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190498" ObjectName="CX_LuC:CX_LuC_275BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-190488" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -505.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="190488" ObjectName="CX_LuC:CX_LuC_274BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187572" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -484.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187572" ObjectName="CX_LuC:CX_LuC_271BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187575" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -462.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187575" ObjectName="CX_LuC:CX_LuC_269BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187566" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -439.600000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187566" ObjectName="CX_LuC:CX_LuC_262BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12935" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -593.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12935" ObjectName="CX_XJH:CX_XJH_221BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-187569" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -418.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187569" ObjectName="CX_LuC:CX_LuC_267BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12815" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -396.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12815" ObjectName="CX_XJH:CX_XJH_223BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -373.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -308.200000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -329.400000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -351.600000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6626" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -571.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6626" ObjectName="CX_ZX:CX_ZX_287BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -572.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-13057" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -551.600000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="13057" ObjectName="CX_XJH:CX_XJH_132BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-30798" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -528.400000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30798" ObjectName="CX_DJ:CX_DJ_162BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12950" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -506.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12950" ObjectName="CX_XJH:CX_XJH_123BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12923" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -594.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12923" ObjectName="CX_XJH:CX_XJH_124BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6564" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -374.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6564" ObjectName="CX_ZX:CX_ZX_187BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-13051" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -551.600000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="13051" ObjectName="CX_XJH:CX_XJH_132BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-30792" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -528.400000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30792" ObjectName="CX_DJ:CX_DJ_162BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12944" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -506.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12944" ObjectName="CX_XJH:CX_XJH_123BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12917" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -594.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12917" ObjectName="CX_XJH:CX_XJH_124BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6563" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -374.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6563" ObjectName="CX_ZX:CX_ZX_187BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -572.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-16671" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -484.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="16671" ObjectName="CX_LT:CX_LT_182BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-16674" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -484.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="16674" ObjectName="CX_LT:CX_LT_182BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-13000" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -463.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="13000" ObjectName="CX_XJH:CX_XJH_133BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -441.200000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -441.200000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-13006" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -463.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="13006" ObjectName="CX_XJH:CX_XJH_133BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12989" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -419.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12989" ObjectName="CX_XJH:CX_XJH_126BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12983" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -419.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12983" ObjectName="CX_XJH:CX_XJH_126BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6579" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -396.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6579" ObjectName="CX_ZX:CX_ZX_186BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6578" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -396.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6578" ObjectName="CX_ZX:CX_ZX_186BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6567" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -353.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6567" ObjectName="CX_ZX:CX_ZX_188BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6566" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -353.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6566" ObjectName="CX_ZX:CX_ZX_188BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6576" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -331.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6576" ObjectName="CX_ZX:CX_ZX_184BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6575" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -331.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6575" ObjectName="CX_ZX:CX_ZX_184BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-18990" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -286.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18990" ObjectName="CX_YR:CX_YR_161BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-58539" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -308.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58539" ObjectName="CX_YR:CX_YR_164BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-58535" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -308.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58535" ObjectName="CX_YR:CX_YR_164BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-18993" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -286.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18993" ObjectName="CX_YR:CX_YR_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115171" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -265.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115171" ObjectName="CX_FS:CX_FS_167BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115168" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -265.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115168" ObjectName="CX_FS:CX_FS_167BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115179" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -242.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115179" ObjectName="CX_FS:CX_FS_168BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115176" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -242.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115176" ObjectName="CX_FS:CX_FS_168BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3746" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -221.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3746" ObjectName="CX_YM:CX_YM_133BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3743" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -221.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3743" ObjectName="CX_YM:CX_YM_133BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -199.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -199.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -177.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -177.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-82404" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -155.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82404" ObjectName="CX_XQ:CX_XQ_121BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-82407" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -155.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82407" ObjectName="CX_XQ:CX_XQ_121BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3778" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -133.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3778" ObjectName="CX_YM:CX_YM_139BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3775" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -133.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3775" ObjectName="CX_YM:CX_YM_139BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -111.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -111.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3770" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -89.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3770" ObjectName="CX_YM:CX_YM_138BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3767" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -89.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3767" ObjectName="CX_YM:CX_YM_138BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -66.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -66.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3738" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -44.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3738" ObjectName="CX_YM:CX_YM_132BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3735" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -44.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3735" ObjectName="CX_YM:CX_YM_132BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -23.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -23.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1610" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 -1.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1610" ObjectName="CX_SS:CX_SS_171BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1609" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 -1.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1609" ObjectName="CX_SS:CX_SS_171BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1606" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 19.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1606" ObjectName="CX_SS:CX_SS_172BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1605" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 19.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1605" ObjectName="CX_SS:CX_SS_172BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-70692" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 41.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70692" ObjectName="CX_SB:CX_SB_154BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-70687" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 41.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70687" ObjectName="CX_SB:CX_SB_154BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1863" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 66.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1863" ObjectName="CX_LF:CX_LF_144BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1860" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 66.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1860" ObjectName="CX_LF:CX_LF_144BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-21872" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 86.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21872" ObjectName="CX_XJ:CX_XJ_148BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-21869" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 86.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21869" ObjectName="CX_XJ:CX_XJ_148BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1858" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 111.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1858" ObjectName="CX_LF:CX_LF_145BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1850" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 111.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1850" ObjectName="CX_LF:CX_LF_148BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1838" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 133.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1838" ObjectName="CX_LF:CX_LF_147BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1835" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 133.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1835" ObjectName="CX_LF:CX_LF_147BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 154.200000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 154.200000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12932" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 175.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12932" ObjectName="CX_XJH:CX_XJH_127BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12926" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 175.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12926" ObjectName="CX_XJH:CX_XJH_127BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12914" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1200.000000 197.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12914" ObjectName="CX_XJH:CX_XJH_129BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12908" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1017.000000 197.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12908" ObjectName="CX_XJH:CX_XJH_129BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12905" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -594.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12905" ObjectName="CX_XJH:CX_XJH_128BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-12899" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -594.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12899" ObjectName="CX_XJH:CX_XJH_128BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6570" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -572.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6570" ObjectName="CX_ZX:CX_ZX_189BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6569" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -572.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6569" ObjectName="CX_ZX:CX_ZX_189BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6561" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -550.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6561" ObjectName="CX_ZX:CX_ZX_283BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6572" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -550.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6572" ObjectName="CX_ZX:CX_ZX_182BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6690" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -529.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6690" ObjectName="CX_ZX:CX_ZX_181BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-6689" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -529.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6689" ObjectName="CX_ZX:CX_ZX_181BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-4343" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -507.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="4343" ObjectName="CX_YA:CX_YA_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-4346" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -507.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="4346" ObjectName="CX_YA:CX_YA_161BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-26084" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -485.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26084" ObjectName="CX_DY:CX_DY_154BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-26087" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -485.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26087" ObjectName="CX_DY:CX_DY_154BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3754" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -463.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3754" ObjectName="CX_YM:CX_YM_135BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3751" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -463.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3751" ObjectName="CX_YM:CX_YM_135BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3722" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -440.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3722" ObjectName="CX_YM:CX_YM_129BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3719" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -440.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3719" ObjectName="CX_YM:CX_YM_129BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3730" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -419.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3730" ObjectName="CX_YM:CX_YM_128BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-3727" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -419.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3727" ObjectName="CX_YM:CX_YM_128BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1858" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -395.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1858" ObjectName="CX_LF:CX_LF_145BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1855" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -395.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1855" ObjectName="CX_LF:CX_LF_145BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1838" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -373.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1838" ObjectName="CX_LF:CX_LF_147BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1835" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -373.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1835" ObjectName="CX_LF:CX_LF_147BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1943" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -352.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1943" ObjectName="CX_LF:CX_LF_143BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1940" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -352.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1940" ObjectName="CX_LF:CX_LF_143BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -329.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -329.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -306.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -306.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1999" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -287.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1999" ObjectName="CX_LF:CX_LF_165BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1996" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -287.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1996" ObjectName="CX_LF:CX_LF_165BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -254.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -254.800000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-2004" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -222.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="2004" ObjectName="CX_LF:CX_LF_149BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-2007" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -222.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="2007" ObjectName="CX_LF:CX_LF_149BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1937" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -199.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1937" ObjectName="CX_LF:CX_LF_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1934" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -199.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1934" ObjectName="CX_LF:CX_LF_161BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5465" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -176.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5465" ObjectName="CX_YZ:CX_YZ_157BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5461" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -176.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5461" ObjectName="CX_YZ:CX_YZ_157BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5447" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -155.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5447" ObjectName="CX_YZ:CX_YZ_151BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-5443" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -155.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5443" ObjectName="CX_YZ:CX_YZ_151BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1598" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -132.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1598" ObjectName="CX_SS:CX_SS_183BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1597" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -132.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1597" ObjectName="CX_SS:CX_SS_183BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1602" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -111.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1602" ObjectName="CX_SS:CX_SS_182BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-1601" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -111.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1601" ObjectName="CX_SS:CX_SS_182BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -89.200000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -89.200000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-57935" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -65.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57935" ObjectName="CX_SS:CX_SS_174BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-57932" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -65.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57932" ObjectName="CX_SS:CX_SS_174BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-57923" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -44.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57923" ObjectName="CX_SS:CX_SS_173BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-57919" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -44.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57919" ObjectName="CX_SS:CX_SS_173BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-72522" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -23.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72522" ObjectName="CX_TX:CX_TX_161BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-72518" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -23.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72518" ObjectName="CX_TX:CX_TX_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62683" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 -2.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62683" ObjectName="CX_SS:CX_SS_176BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62680" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 -2.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62680" ObjectName="CX_SS:CX_SS_176BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88029" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 43.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88029" ObjectName="CX_SZ:CX_SZ_122BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-96546" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 64.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96546" ObjectName="CX_SS:CX_SS_179BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-96543" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 64.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96543" ObjectName="CX_SS:CX_SS_179BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-88026" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 43.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88026" ObjectName="CX_SZ:CX_SZ_122BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-96553" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 89.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96553" ObjectName="CX_SS:CX_SS_181BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115187" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 110.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115187" ObjectName="CX_FS:CX_FS_169BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115184" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 110.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115184" ObjectName="CX_FS:CX_FS_169BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115195" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 133.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115195" ObjectName="CX_FS:CX_FS_171BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115211" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 154.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115211" ObjectName="CX_FS:CX_FS_173BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115208" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 154.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115208" ObjectName="CX_FS:CX_FS_173BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-115192" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 133.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115192" ObjectName="CX_FS:CX_FS_171BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-96550" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 89.800000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96550" ObjectName="CX_SS:CX_SS_181BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-181715" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1806.000000 20.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181715" ObjectName="CX_SZ:CX_SZ_121BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-181718" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1622.000000 20.200000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181718" ObjectName="CX_SZ:CX_SZ_121BK_Ia"/>
    </metadata>
   </g>
  </g><g id="Image_Layer">
   
   
   
   
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="16" graphid="g_1ff87f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 2448.700000 -821.000000) translate(0,13)">返回主页</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="16" graphid="g_1fe61a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 2455.200000 -719.000000) translate(0,13)">返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="Kaiti_GB2312" font-size="24" graphid="g_2217b90" transform="matrix(1.000000 0.000000 0.000000 1.000000 37.000000 -724.000000) translate(0,20)">重载线路潮流监控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="Kaiti_GB2312" font-size="24" graphid="g_21d9520" transform="matrix(1.000000 0.000000 0.000000 1.000000 244.000000 -724.000000) translate(0,20)">重载主变潮流监控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21da0f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 109.059505 -626.820367) translate(0,15)">线路名称</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21db0b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 47.000343 -626.836862) translate(0,15)">序号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21db680" transform="matrix(0.979920 0.000000 0.000000 0.832895 56.559505 -594.820367) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21db970" transform="matrix(0.979920 0.000000 0.000000 0.832895 56.559505 -572.820367) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbbb0" transform="matrix(0.979920 0.000000 0.000000 0.832895 56.559505 -551.820367) translate(0,15)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbdf0" transform="matrix(0.979920 0.000000 0.000000 0.832895 56.059505 -528.820367) translate(0,15)">4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc030" transform="matrix(0.979920 0.000000 0.000000 0.832895 56.559505 -506.820367) translate(0,15)">5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc270" transform="matrix(0.979920 0.000000 0.000000 0.832895 56.559505 -485.820367) translate(0,15)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc4b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 56.559505 -463.820367) translate(0,15)">7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc6f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 56.559505 -440.820367) translate(0,15)">8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc930" transform="matrix(0.979920 0.000000 0.000000 0.832895 56.559505 -419.820367) translate(0,15)">9</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2207d30" transform="matrix(0.979920 0.000000 0.000000 0.832895 51.559505 -397.820367) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2207f70" transform="matrix(0.979920 0.000000 0.000000 0.832895 51.559505 -374.820367) translate(0,15)">11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22081b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 93.319334 -594.836862) translate(0,15)">谢楚北牵线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22089d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 286.000000 -660.000000) translate(0,15)">220kV线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2208da0" transform="matrix(1.000000 0.000000 0.000000 1.000000 209.000000 -630.000000) translate(0,12)">理论载</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2208da0" transform="matrix(1.000000 0.000000 0.000000 1.000000 209.000000 -630.000000) translate(0,27)">流量（A）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2262680" transform="matrix(1.000000 0.000000 0.000000 1.000000 53.000000 -309.000000) translate(0,12)">14</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2376450" transform="matrix(1.000000 0.000000 0.000000 1.000000 53.000000 -330.000000) translate(0,12)">13</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2376690" transform="matrix(1.000000 0.000000 0.000000 1.000000 53.500000 -353.000000) translate(0,12)">12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2376d50" transform="matrix(1.000000 0.000000 0.000000 1.000000 451.000000 -631.000000) translate(0,12)">理论输送</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2376d50" transform="matrix(1.000000 0.000000 0.000000 1.000000 451.000000 -631.000000) translate(0,27)">容量 (MVA)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2377570" transform="matrix(1.000000 0.000000 0.000000 1.000000 527.000000 -633.000000) translate(0,12)">实际输</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2377570" transform="matrix(1.000000 0.000000 0.000000 1.000000 527.000000 -633.000000) translate(0,27)">送功率（MW）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2377e60" transform="matrix(1.000000 0.000000 0.000000 1.000000 278.000000 -631.000000) translate(0,12)">流量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2377e60" transform="matrix(1.000000 0.000000 0.000000 1.000000 278.000000 -631.000000) translate(0,27)">告警值</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23788c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -593.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2378d20" transform="matrix(0.979920 0.000000 0.000000 0.832895 451.319334 -593.836862) translate(0,15)">266.73</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2379170" transform="matrix(0.979920 0.000000 0.000000 0.832895 274.319334 -592.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23793f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 361.000000 -633.000000) translate(0,12)">实际载</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23793f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 361.000000 -633.000000) translate(0,27)">流量（A）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22018d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 336.000000 -594.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21beac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 526.000000 -593.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21bed40" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -570.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bef80" transform="matrix(1.000000 0.000000 0.000000 1.000000 336.000000 -571.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21bf1c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 93.319334 -571.836862) translate(0,15)">紫楚北牵线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bf400" transform="matrix(1.000000 0.000000 0.000000 1.000000 527.000000 -571.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21bf640" transform="matrix(0.979920 0.000000 0.000000 0.832895 275.319334 -570.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21bf880" transform="matrix(0.979920 0.000000 0.000000 0.832895 451.319334 -571.836862) translate(0,15)">266.73</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21bfac0" transform="matrix(0.979920 0.000000 0.000000 0.832895 451.319334 -528.836862) translate(0,15)">266.73</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21bfd00" transform="matrix(1.000000 0.000000 0.000000 1.000000 528.000000 -528.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21c0540" transform="matrix(0.979920 0.000000 0.000000 0.832895 275.319334 -549.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c07c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 527.000000 -550.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21c0a00" transform="matrix(0.979920 0.000000 0.000000 0.832895 451.319334 -550.836862) translate(0,15)">266.73</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c0c40" transform="matrix(1.000000 0.000000 0.000000 1.000000 337.000000 -551.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c0e80" transform="matrix(1.000000 0.000000 0.000000 1.000000 337.000000 -528.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21c10c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -527.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21c1300" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -550.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21c1540" transform="matrix(0.979920 0.000000 0.000000 0.832895 94.319334 -551.836862) translate(0,15)">紫南牵线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21c1a70" transform="matrix(0.979920 0.000000 0.000000 0.832895 94.319334 -528.836862) translate(0,15)">鹿普牵线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21c22f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 276.319334 -527.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d1b40" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -505.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d1d80" transform="matrix(0.979920 0.000000 0.000000 0.832895 94.319334 -506.836862) translate(0,15)">鹿南牵线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d1fc0" transform="matrix(0.979920 0.000000 0.000000 0.832895 451.319334 -506.836862) translate(0,15)">266.73</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d2200" transform="matrix(1.000000 0.000000 0.000000 1.000000 528.000000 -506.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d2440" transform="matrix(0.979920 0.000000 0.000000 0.832895 276.319334 -505.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d2680" transform="matrix(1.000000 0.000000 0.000000 1.000000 337.000000 -506.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d28c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 337.000000 -485.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d2b00" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -484.836862) translate(0,15)">1600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d2d40" transform="matrix(0.979920 0.000000 0.000000 0.832895 94.319334 -485.836862) translate(0,15)">鹿紫Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d2f80" transform="matrix(0.979920 0.000000 0.000000 0.832895 276.319334 -484.836862) translate(0,15)">985.6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d31c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 453.319334 -485.836862) translate(0,15)">609.66</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d3400" transform="matrix(1.000000 0.000000 0.000000 1.000000 528.000000 -485.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d3640" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -461.836862) translate(0,15)">1600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d3880" transform="matrix(0.979920 0.000000 0.000000 0.832895 275.319334 -461.836862) translate(0,15)">985.6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d3ac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 336.000000 -462.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d3d00" transform="matrix(0.979920 0.000000 0.000000 0.832895 452.319334 -462.836862) translate(0,15)">609.66</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d3f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 527.000000 -462.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d4180" transform="matrix(0.979920 0.000000 0.000000 0.832895 93.319334 -462.836862) translate(0,15)">鹿紫Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d43c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 336.000000 -440.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d4600" transform="matrix(0.979920 0.000000 0.000000 0.832895 452.319334 -440.836862) translate(0,15)">609.66</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d4840" transform="matrix(1.000000 0.000000 0.000000 1.000000 527.000000 -440.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d4a80" transform="matrix(0.979920 0.000000 0.000000 0.832895 93.319334 -440.836862) translate(0,15)">鹿保线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d4f30" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -439.836862) translate(0,15)">1600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d5170" transform="matrix(0.979920 0.000000 0.000000 0.832895 275.319334 -439.836862) translate(0,15)">985.6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d53b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -418.836862) translate(0,15)">800</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21d55f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 276.319334 -418.836862) translate(0,15)">492.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d5830" transform="matrix(1.000000 0.000000 0.000000 1.000000 337.000000 -419.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2246770" transform="matrix(0.979920 0.000000 0.000000 0.832895 453.319334 -419.836862) translate(0,15)">304.83</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22469b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 528.000000 -419.500000) translate(0,12)">鹿城侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2246bf0" transform="matrix(0.979920 0.000000 0.000000 0.832895 94.319334 -419.836862) translate(0,15)">鹿平线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2246e30" transform="matrix(0.979920 0.000000 0.000000 0.832895 95.319334 -396.836862) translate(0,15)">谢紫Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2247070" transform="matrix(0.979920 0.000000 0.000000 0.832895 217.319334 -395.836862) translate(0,15)">800</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22472b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 277.319334 -395.836862) translate(0,15)">492.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22474f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 338.000000 -396.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2247730" transform="matrix(1.000000 0.000000 0.000000 1.000000 529.000000 -396.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2247970" transform="matrix(0.979920 0.000000 0.000000 0.832895 454.319334 -396.836862) translate(0,15)">304.83</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2248190" transform="matrix(0.979920 0.000000 0.000000 0.832895 718.059505 -627.820367) translate(0,15)">线路名称</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2249120" transform="matrix(0.979920 0.000000 0.000000 0.832895 656.000343 -627.836862) translate(0,15)">序号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22496f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -595.820367) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22499e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -573.820367) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2249c20" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -552.820367) translate(0,15)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2249e60" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.059505 -529.820367) translate(0,15)">4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_224a0a0" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -507.820367) translate(0,15)">5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_224a2e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -486.820367) translate(0,15)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_224a520" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -464.820367) translate(0,15)">7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_224a760" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -441.820367) translate(0,15)">8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22bdba0" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -420.820367) translate(0,15)">9</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22bdde0" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -398.820367) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22be020" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -375.820367) translate(0,15)">11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22be260" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -595.836862) translate(0,15)">楚牟线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22be860" transform="matrix(1.000000 0.000000 0.000000 1.000000 895.000000 -661.000000) translate(0,15)">110kV线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bec10" transform="matrix(1.000000 0.000000 0.000000 1.000000 818.000000 -631.000000) translate(0,12)">理论载</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bec10" transform="matrix(1.000000 0.000000 0.000000 1.000000 818.000000 -631.000000) translate(0,27)">流量（A）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22306c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 -310.000000) translate(0,12)">14</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2230900" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 -331.000000) translate(0,12)">13</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2230b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.500000 -354.000000) translate(0,12)">12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2231200" transform="matrix(1.000000 0.000000 0.000000 1.000000 1060.000000 -632.000000) translate(0,12)">理论输送</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2231200" transform="matrix(1.000000 0.000000 0.000000 1.000000 1060.000000 -632.000000) translate(0,27)">容量 (MVA)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2231600" transform="matrix(1.000000 0.000000 0.000000 1.000000 1136.000000 -634.000000) translate(0,12)">实际输</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2231600" transform="matrix(1.000000 0.000000 0.000000 1.000000 1136.000000 -634.000000) translate(0,27)">送功率（MW）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2231840" transform="matrix(1.000000 0.000000 0.000000 1.000000 887.000000 -632.000000) translate(0,12)">流量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2231840" transform="matrix(1.000000 0.000000 0.000000 1.000000 887.000000 -632.000000) translate(0,27)">告警值</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2231a80" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -594.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2231cc0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -594.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2231f00" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -593.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2232140" transform="matrix(1.000000 0.000000 0.000000 1.000000 970.000000 -634.000000) translate(0,12)">实际载</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2232140" transform="matrix(1.000000 0.000000 0.000000 1.000000 970.000000 -634.000000) translate(0,27)">流量（A）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256c5d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -595.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256c8c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -594.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256cb00" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -571.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256cd40" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -572.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256cf80" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -572.836862) translate(0,15)">楚牟T广线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256d1c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -572.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256d400" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -571.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256d640" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -572.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256d880" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -529.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256dac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -529.500000) translate(0,12)">东郊侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256dd00" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -550.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256df40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -551.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256e180" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -551.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256e3c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -552.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256e600" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -529.500000) translate(0,12)">东郊侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256e840" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -528.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256ea80" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -551.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256ecc0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -552.836862) translate(0,15)">谢白线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256ef00" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -529.836862) translate(0,15)">东郊T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256f140" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -528.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256f380" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -506.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256f5c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -507.836862) translate(0,15)">谢金Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256f800" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -507.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256fa40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -507.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_256fc80" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -506.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256fec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -507.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2580270" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -484.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_263e920" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -485.836862) translate(0,15)">龙潭Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25cf140" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -485.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25bb750" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -485.500000) translate(0,12)">龙潭侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25b6f90" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -484.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2603c70" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -485.500000) translate(0,12)">龙潭侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a7530" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -463.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25a7760" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -464.836862) translate(0,15)">谢烟龙金线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d5740" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -464.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d59b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -464.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d3870" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -463.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d3ae0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -464.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2634a80" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -441.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2634ce0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -442.836862) translate(0,15)">谢金T广线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d7660" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -442.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d78d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -442.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e1290" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -441.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e1500" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -442.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26743a0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -419.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26748d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -420.836862) translate(0,15)">谢烟双线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2674b20" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -420.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2674d90" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -420.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2674fe0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -419.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2675250" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -420.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26935c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -397.836862) translate(0,15)">紫白西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2693a50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -397.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2693c80" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -397.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2693ed0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -396.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2694140" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -397.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2694bc0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -396.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26bf6b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -376.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2260d30" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -376.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26bfff0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -375.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c0260" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -376.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c0ce0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -375.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c0f40" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -376.836862) translate(0,15)">紫西东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26d2c10" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -353.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d30d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -354.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26d3b50" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -353.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26d3db0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -354.836862) translate(0,15)">紫东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26d4000" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -354.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d4270" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -354.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2716100" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -332.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2716e50" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -331.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27170b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -332.836862) translate(0,15)">紫姚南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2717300" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -332.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2717570" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -332.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27177c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -331.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2806b90" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -309.836862) translate(0,15)">永干万线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2806d00" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -309.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2806f20" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -309.500000) translate(0,12)">永仁侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2807170" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -308.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28073e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -309.500000) translate(0,12)">永仁侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2807f70" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -308.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2808150" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -288.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28083a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -288.500000) translate(0,12)">永仁侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28085f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -287.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2808860" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -288.500000) translate(0,12)">永仁侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2808ab0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -287.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2808d20" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -288.836862) translate(0,15)">永万的线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c808d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -265.820367) translate(0,15)">16</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c80e00" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -244.820367) translate(0,15)">17</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c81030" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.059505 -221.820367) translate(0,15)">18</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c81480" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -199.820367) translate(0,15)">19</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c816b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -178.820367) translate(0,15)">20</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c818e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -156.820367) translate(0,15)">21</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c81b10" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -133.820367) translate(0,15)">22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c81d40" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -112.820367) translate(0,15)">23</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c11690" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -90.820367) translate(0,15)">24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c11a60" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -67.820367) translate(0,15)">25</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c11c90" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 -2.000000) translate(0,12)">28</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c11ec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 -23.000000) translate(0,12)">27</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c82a00" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.500000 -46.000000) translate(0,12)">26</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c82c30" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 -287.820367) translate(0,15)">15</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cc1500" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 40.179633) translate(0,15)">30</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cc1980" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 62.179633) translate(0,15)">31</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cc1b70" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 85.179633) translate(0,15)">32</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc1da0" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 151.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc1fd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 130.000000) translate(0,12)">34</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cc2200" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.500000 107.000000) translate(0,12)">33</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cc2430" transform="matrix(0.979920 0.000000 0.000000 0.832895 664.559505 19.179633) translate(0,15)">29</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cec800" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 176.000000) translate(0,12)">36</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cecd20" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 197.000000) translate(0,12)">37</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_302f450" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -267.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c81ef0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -267.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c824a0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -266.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c82710" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -267.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3030190" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -266.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3030400" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -267.836862) translate(0,15)">方元大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_304f380" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -244.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_304f750" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -243.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_304f9c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -244.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_304fc10" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -243.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_304fe80" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -244.836862) translate(0,15)">方永Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3050900" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -244.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28883b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -223.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2579e10" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -222.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28791d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -223.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_286a410" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -222.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28c8550" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -223.836862) translate(0,15)">元牟线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28d0170" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -223.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2869530" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -200.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd4650" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -201.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f908c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -200.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d575c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -201.836862) translate(0,15)">元牟T羊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d2eb30" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -201.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f8ada0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -201.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3188f00" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -179.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16350" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -179.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d16920" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -178.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16b90" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -179.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d16de0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -178.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3189d80" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -179.836862) translate(0,15)">紫新甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31af340" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -156.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31af940" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -157.500000) translate(0,12)">新桥侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31afb50" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -156.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31afd80" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -157.836862) translate(0,15)">新桥T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31b06e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -157.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31b0940" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -134.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31890f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -134.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3189630" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -135.836862) translate(0,15)">月沙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3189c10" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -135.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b22c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -157.500000) translate(0,12)">新桥侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d8550" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -112.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d8a40" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -113.836862) translate(0,15)">月沙T元线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d94c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -113.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d9720" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -90.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d9980" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -91.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d9bd0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -90.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31d9f50" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -91.836862) translate(0,15)">大湾子线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31b18f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -91.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31dae90" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -91.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31db320" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -112.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f7270" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -68.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f74d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -45.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7730" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -46.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f7980" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -45.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f7bf0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -46.836862) translate(0,15)">羊臼河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31da100" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -46.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31da680" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -46.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31da8d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -68.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31dab30" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -67.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f93b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -68.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f9600" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -67.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31f9870" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -68.836862) translate(0,15)">大湾T元线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3259280" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -113.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32596c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -136.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3259920" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -113.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3259b80" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -136.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3071220" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -25.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3071480" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -25.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30716d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -24.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3071940" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -25.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3071b90" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -24.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3071e00" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -25.836862) translate(0,15)">羊臼T甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a9de0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 -3.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_309a850" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 -2.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30956f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 -3.836862) translate(0,15)">狮牡禄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30a6790" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 -3.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ac6b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -3.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30a7b70" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 -2.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3161420" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 18.163138) translate(0,15)">狮禄崇Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3161a10" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 18.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3161c80" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 18.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3161ed0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 19.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3162140" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 18.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3162b30" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 19.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3162ea0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 39.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a2f90" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 39.500000) translate(0,12)">双柏侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30a3450" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 40.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a36c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 39.500000) translate(0,12)">双柏侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30a3910" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 40.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30a3b80" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 39.163138) translate(0,15)">双鄂大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_312ed80" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 64.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_312f1f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 65.163138) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_312f440" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 64.163138) translate(0,15)">禄腰德线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_312fec0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 64.163138) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3130120" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 64.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3130370" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 65.163138) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d676d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 84.500000) translate(0,12)">西郊侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3163270" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 85.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3163850" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 84.500000) translate(0,12)">西郊侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3163aa0" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 85.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3163d10" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 84.163138) translate(0,15)">西虎杜嘉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d68f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 109.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d69190" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 110.163138) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d693d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 109.163138) translate(0,15)">禄金Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d69e50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 109.163138) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6a0b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 109.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d6a300" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 110.163138) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d6a570" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 84.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d95af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 131.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d95c80" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 132.163138) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d678d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 131.163138) translate(0,15)">禄牵Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d682d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 131.163138) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d68530" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 131.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d96fd0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 132.163138) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dccc60" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 153.163138) translate(0,15)">380</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dcd190" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 152.163138) translate(0,15)">勐武线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dcdc10" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 152.163138) translate(0,15)">72.40</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dce0c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 153.163138) translate(0,15)">234.08</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2df78d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 174.163138) translate(0,15)">谢东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2df7df0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 174.163138) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df8060" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 174.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2df7bc0" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 175.163138) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d962a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 174.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d96d20" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 175.163138) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e3be30" transform="matrix(0.979920 0.000000 0.000000 0.832895 829.319334 197.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e3c310" transform="matrix(0.979920 0.000000 0.000000 0.832895 702.319334 196.163138) translate(0,15)">谢西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e3c560" transform="matrix(0.979920 0.000000 0.000000 0.832895 1066.319334 196.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3c7d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 196.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e3ca20" transform="matrix(0.979920 0.000000 0.000000 0.832895 880.319334 197.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3cc90" transform="matrix(1.000000 0.000000 0.000000 1.000000 948.000000 196.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2df8230" transform="matrix(0.979920 0.000000 0.000000 0.832895 1324.059505 -627.820367) translate(0,15)">线路名称</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_375f9c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1262.000343 -627.836862) translate(0,15)">序号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3760100" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -595.820367) translate(0,15)">38</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3760420" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -573.820367) translate(0,15)">39</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3760680" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -552.820367) translate(0,15)">40</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3760830" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.059505 -529.820367) translate(0,15)">41</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3760cd0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -507.820367) translate(0,15)">42</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3760f30" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -486.820367) translate(0,15)">43</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3773d70" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -464.820367) translate(0,15)">44</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3773fd0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -441.820367) translate(0,15)">45</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_375ff70" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -420.820367) translate(0,15)">46</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3774440" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -398.820367) translate(0,15)">47</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3774670" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -375.820367) translate(0,15)">48</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37748d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -594.836862) translate(0,15)">谢沙冶线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3774c40" transform="matrix(1.000000 0.000000 0.000000 1.000000 1501.000000 -661.000000) translate(0,15)">110kV线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3775320" transform="matrix(1.000000 0.000000 0.000000 1.000000 1424.000000 -631.000000) translate(0,12)">理论载</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3775320" transform="matrix(1.000000 0.000000 0.000000 1.000000 1424.000000 -631.000000) translate(0,27)">流量（A）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3775800" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.000000 -310.000000) translate(0,12)">51</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3777660" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.000000 -331.000000) translate(0,12)">50</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3777890" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.500000 -354.000000) translate(0,12)">49</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3777ed0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1666.000000 -632.000000) translate(0,12)">理论输送</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3777ed0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1666.000000 -632.000000) translate(0,27)">容量 (MVA)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3777350" transform="matrix(1.000000 0.000000 0.000000 1.000000 1742.000000 -634.000000) translate(0,12)">实际输</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3777350" transform="matrix(1.000000 0.000000 0.000000 1.000000 1742.000000 -634.000000) translate(0,27)">送功率（MW）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37787e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1493.000000 -632.000000) translate(0,12)">流量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37787e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1493.000000 -632.000000) translate(0,27)">告警值</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3778a40" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -594.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37782a0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -594.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3778df0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -593.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3779000" transform="matrix(1.000000 0.000000 0.000000 1.000000 1576.000000 -634.000000) translate(0,12)">实际载</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3779000" transform="matrix(1.000000 0.000000 0.000000 1.000000 1576.000000 -634.000000) translate(0,27)">流量（A）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_377dbf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -595.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_377b9c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -594.500000) translate(0,12)">谢家河侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_378cce0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -254.820367) translate(0,15)">53</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_378cf00" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -221.820367) translate(0,15)">54</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_378f0f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.059505 -198.820367) translate(0,15)">55</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_378d220" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -176.820367) translate(0,15)">56</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_378ff30" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -155.820367) translate(0,15)">57</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3790190" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -133.820367) translate(0,15)">58</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3794c40" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -110.820367) translate(0,15)">59</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3794e40" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -89.820367) translate(0,15)">60</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37950a0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -67.820367) translate(0,15)">61</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3795300" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -44.820367) translate(0,15)">62</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3795560" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.000000 21.000000) translate(0,12)">65</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_378f2a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.000000 -0.000000) translate(0,12)">64</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_378f590" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.500000 -23.000000) translate(0,12)">63</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_378f7f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 -287.820367) translate(0,15)">52</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_378fa50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 63.179633) translate(0,15)">67</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_378fcb0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 85.179633) translate(0,15)">68</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3792470" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 108.179633) translate(0,15)">69</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3792620" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.000000 174.000000) translate(0,12)">72</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37929a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.000000 153.000000) translate(0,12)">71</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ef410" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.500000 130.000000) translate(0,12)">70</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3792b70" transform="matrix(0.979920 0.000000 0.000000 0.832895 1270.559505 42.179633) translate(0,15)">66</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3792dd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1270.000000 199.000000) translate(0,12)">73</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_368d7e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -573.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368dc20" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -573.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_368de70" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -572.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368e0e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -573.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_368eb60" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -572.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_368edc0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -572.836862) translate(0,15)">紫沙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ba88f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -550.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ba8b50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -550.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ba8db0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -549.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ba9420" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -551.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ba9670" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -550.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ba98d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -550.836862) translate(0,15)">紫南云线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bdbd70" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -530.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3bdc220" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -529.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bdc490" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -530.500000) translate(0,12)">紫溪侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3bdcf10" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -529.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3bdd170" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -529.836862) translate(0,15)">紫凤线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3bdd3c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -530.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3bffe10" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -507.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c00010" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -507.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c00270" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -506.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c008e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -508.500000) translate(0,12)">姚安侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c00b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -507.500000) translate(0,12)">姚安侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c00d90" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -507.836862) translate(0,15)">姚大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3274b50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -485.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3274fe0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -485.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3275200" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -484.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3275870" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -486.500000) translate(0,12)">大姚侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3275ac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -485.500000) translate(0,12)">大姚侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3275d20" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -485.836862) translate(0,15)">大六线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32ab750" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -463.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32abbf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -464.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32ac5f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -463.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32ac850" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -463.836862) translate(0,15)">元大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32acaa0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -464.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32acd10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -464.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d7ac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -442.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32d8020" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -441.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32d8290" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -441.836862) translate(0,15)">元黄哨Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32d8d10" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -442.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d8f70" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -442.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32d91c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -441.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3305550" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -420.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33059d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -420.836862) translate(0,15)">元黄哨Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3306430" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -421.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3306690" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -421.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33068e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -420.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3306b50" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -421.500000) translate(0,12)">元谋侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3321d00" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -396.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33223b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -396.836862) translate(0,15)">禄金Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3322e30" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -397.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3323090" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -397.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33232e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -396.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3323550" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -397.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_334dbf0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -374.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_334e280" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -374.836862) translate(0,15)">禄牵Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_334ec80" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -375.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_334eee0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -375.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_334f130" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -374.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_334f3a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -375.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3390e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -353.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33912a0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -352.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33914d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -353.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3391f50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -352.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33921b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -352.836862) translate(0,15)">禄勤老线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3392400" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -353.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33dc3e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -329.836862) translate(0,15)">勤牵Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33dd1e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -331.836862) translate(0,15)">84.78</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dd440" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -331.500000) translate(0,12)">勤丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33dd690" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -330.836862) translate(0,15)">274.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dd900" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -331.500000) translate(0,12)">勤丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33ddb50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -330.836862) translate(0,15)">445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3417f00" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -307.836862) translate(0,15)">勤牵Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3418b90" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -308.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3418df0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -308.500000) translate(0,12)">勤丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3419040" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -307.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34192b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -308.500000) translate(0,12)">勤丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3419500" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -307.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3467af0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -289.836862) translate(0,15)">133.36</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3467d50" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -289.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3467fa0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -288.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468210" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -289.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3468460" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -288.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34686d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -288.836862) translate(0,15)">禄上Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c6a120" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -256.836862) translate(0,15)">133.36</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6a340" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -256.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c6a590" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -255.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c6a800" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -256.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c6aa50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -255.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c6acc0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -259.836862) translate(0,15)">禄上Ⅰ回线T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3c6acc0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -259.836862) translate(0,33)">隆基支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4018e70" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -224.836862) translate(0,15)">133.36</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4019070" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -224.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4019280" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -223.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40194b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -224.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4019700" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -223.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4019970" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -223.836862) translate(0,15)">禄上Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_403ee50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -199.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403f040" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -200.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_403f800" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -199.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_403fa20" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -199.836862) translate(0,15)">禄洪线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_403fc30" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -200.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403fe60" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -200.500000) translate(0,12)">禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fe9c60" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -177.500000) translate(0,12)">腰站侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fea900" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -176.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3feab60" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -176.836862) translate(0,15)">腰洪指线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3feadb0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -177.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3feb020" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -177.500000) translate(0,12)">腰站侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3feb270" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -176.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_404ec90" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -155.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_404eeb0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -155.836862) translate(0,15)">腰勤线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_404f0c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -156.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404f2f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -156.500000) translate(0,12)">腰站侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_404f540" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -155.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404f7b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -156.500000) translate(0,12)">腰站侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40d9570" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -133.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40d92f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -133.836862) translate(0,15)">狮武Ⅰ线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40d9fd0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -134.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40da230" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -134.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40da480" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -133.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40da6f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -134.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ea0660" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -112.836862) translate(0,15)">狮武Ⅱ线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ea1580" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -113.836862) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea17e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -113.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ea1a30" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -112.836862) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea1ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -113.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ea1ef0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -112.836862) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_407d2f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -89.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4069550" transform="matrix(0.979920 0.000000 0.000000 0.832895 1301.319334 -90.836862) translate(0,15)">一、二级联络线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40696c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -90.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_406be60" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -89.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40aee50" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -67.836862) translate(0,15)">133.36</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3decbb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -67.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3decd20" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -66.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40b8b20" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -67.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40b8c90" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -66.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40a1620" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -66.836862) translate(0,15)">狮果Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_424a300" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -46.836862) translate(0,15)">133.36</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4251fd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -46.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42635b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -45.836862) translate(0,15)">431.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4268e00" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -46.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4241750" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -45.836862) translate(0,15)">700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_425ed10" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -45.836862) translate(0,15)">狮果Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42f7330" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -23.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42f7550" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -23.836862) translate(0,15)">田心T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42f77a0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -24.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42f7a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -24.500000) translate(0,12)">田心侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42f7c60" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -23.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42f7ed0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -24.500000) translate(0,12)">田心侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c1de0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 -2.836862) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c1950" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 -2.836862) translate(0,15)">狮田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c20c0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 -3.836862) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c2290" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 -3.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c24a0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 -2.836862) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c26d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 -3.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37e0e40" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 43.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37e1490" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 43.163138) translate(0,15)">舍资Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37e16a0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 42.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37e1c20" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 43.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37e2470" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 64.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37e27e0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 64.163138) translate(0,15)">狮西Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37e2a30" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 63.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e2ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 63.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37e2ef0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 64.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e3160" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 63.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_380fb60" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 89.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_380f8d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 89.163138) translate(0,15)">狮西Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38001d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 88.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3810360" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 88.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38105b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 89.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3810820" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 88.500000) translate(0,12)">狮山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3810e90" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 110.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3811200" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 110.163138) translate(0,15)">方莲Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3811450" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 109.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38116c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 109.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3811910" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 110.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3811b80" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 109.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3812570" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 133.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3812960" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 133.163138) translate(0,15)">方莲Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_380fd10" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 132.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3812fd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 132.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3813220" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 133.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3813490" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 132.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3813b00" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 154.163138) translate(0,15)">610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3813d60" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 154.163138) translate(0,15)">方六线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38127d0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 153.163138) translate(0,15)">116.22</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38141c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 153.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3814410" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 154.163138) translate(0,15)">375.76</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3814680" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 153.500000) translate(0,12)">方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3893c40" transform="matrix(0.979920 0.000000 0.000000 0.832895 1671.319334 18.163138) translate(0,15)">98.12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3893ea0" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 18.500000) translate(0,12)">舍资侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38940f0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1490.319334 19.163138) translate(0,15)">317.24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3894360" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 18.500000) translate(0,12)">舍资侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38945b0" transform="matrix(0.979920 0.000000 0.000000 0.832895 1435.319334 19.163138) translate(0,15)">515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3894820" transform="matrix(0.979920 0.000000 0.000000 0.832895 1311.319334 20.163138) translate(0,15)">舍资Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3919530" transform="matrix(1.000000 0.000000 0.000000 1.000000 1743.000000 40.500000) translate(0,12)">舍资侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3919e50" transform="matrix(1.000000 0.000000 0.000000 1.000000 1556.000000 40.500000) translate(0,12)">舍资侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="Kaiti_GB2312" font-size="24" graphid="g_6dcd420" transform="matrix(1.000000 0.000000 0.000000 1.000000 458.000000 -721.000000) translate(0,20)">110kV及部分220kV线路限流表</text>
  </g><g areaN="0" fileType="0" fixScaleFlag="0" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>