<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-26" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3055 -1220 2602 1183">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape27">
    <polyline points="22,19 34,19 34,6 32,7 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="37" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="33" x2="36" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="30" x2="39" y1="6" y2="6"/>
    <ellipse cx="8" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="18" cy="19" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="25" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="6" x2="8" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="9" x2="9" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="21" x2="18" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="18" x2="18" y1="17" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="21" x2="17" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="12" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="12" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape103">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="23" x2="22" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.84362" x1="1" x2="22" y1="46" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="22" x2="15" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="10" x2="10" y1="35" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.675211" x1="11" x2="11" y1="5" y2="16"/>
    <polyline arcFlag="1" points="11,28 11,28 11,28 11,28 12,28 12,28 12,27 12,27 13,27 13,27 13,26 13,26 13,26 13,25 13,25 13,24 13,24 13,24 13,23 12,23 12,23 12,23 12,22 11,22 11,22 11,22 " stroke-width="0.0340106"/>
    <polyline arcFlag="1" points="10,34 11,34 11,34 11,34 11,34 12,34 12,34 12,33 12,33 12,33 12,32 13,32 13,32 13,31 13,31 13,31 12,30 12,30 12,30 12,29 12,29 12,29 11,29 11,28 11,28 11,28 " stroke-width="0.0340106"/>
    <polyline arcFlag="1" points="11,22 11,22 11,22 11,22 12,22 12,21 12,21 12,21 13,21 13,20 13,20 13,20 13,19 13,19 13,19 13,18 13,18 13,17 13,17 12,17 12,17 12,16 12,16 11,16 11,16 11,16 " stroke-width="0.0340106"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.532624" x1="9" x2="12" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="8" x2="13" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="4" x2="17" y1="46" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape22">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="55" x2="60" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="46" x2="51" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="38" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="30" x2="35" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="86" x2="72" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="18" y1="9" y2="9"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape24_0">
    <ellipse cx="69" cy="46" fillStyle="0" rx="25.5" ry="26" stroke-width="0.535277"/>
    <polyline points="71,96 86,96 " stroke-width="1"/>
    <polyline points="86,96 77,81 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="79" x2="72" y1="47" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="88" x2="79" y1="41" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="79" x2="79" y1="57" y2="47"/>
   </symbol>
   <symbol id="transformer:shape24_1">
    <polyline points="86,96 1,48 " stroke-width="1"/>
    <ellipse cx="40" cy="65" fillStyle="0" rx="25.5" ry="26.5" stroke-width="0.524983"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="41" x2="35" y1="71" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="48" x2="41" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="41" x2="41" y1="80" y2="71"/>
   </symbol>
   <symbol id="transformer:shape24-2">
    <ellipse cx="41" cy="30" fillStyle="0" rx="25.5" ry="26.5" stroke-width="0.535277"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="40" x2="48" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="31" x2="48" y1="16" y2="16"/>
   </symbol>
   <symbol id="voltageTransformer:shape22">
    <polyline points="41,18 8,18 8,43 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="5" x2="11" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="0" x2="15" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.618687" x1="6" x2="10" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="38" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.17171" x1="22" x2="31" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21568" x1="27" x2="22" y1="20" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21447" x1="27" x2="31" y1="20" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="15" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="15" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="61" x2="57" y1="22" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="57" x2="57" y1="26" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="52" x2="56" y1="22" y2="26"/>
    <ellipse cx="27" cy="26" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="42" cy="35" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="55" cy="26" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="41" cy="20" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="35" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="35" y2="38"/>
   </symbol>
   <symbol id="voltageTransformer:shape36">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline points="19,9 35,9 35,22 " stroke-width="0.8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_367de80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f81df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_367fb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3680a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3681d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3682910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36834c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3683ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_367ed40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_367ed40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36870d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36870d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3688f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3688f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3689f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_368bba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_368c7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_368d6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_368dfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_368f770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3690470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3690d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36914f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36923d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3692be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3693720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_36940e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3695580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_36960f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3697120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3697d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36a6120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_369e1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_369f8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3699f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1193" width="2612" x="3050" y="-1225"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f08220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 1169.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f084f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 1154.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f08730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 1139.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f08b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 1176.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f08e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 1161.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f09050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 1146.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f09470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 845.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f09730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 830.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f09970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 815.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f09d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 1167.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0a050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 1152.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0a290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 1137.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0a6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 1165.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0a970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.000000 1150.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0abb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4541.000000 1135.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0afd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 783.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 768.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0b4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 753.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0b8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 839.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0bbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 824.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0bdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4613.000000 809.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0c210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 602.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0c4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 587.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f0c710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 572.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403a370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 827.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403a9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 812.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403abe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 797.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403ae20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3578.000000 781.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403b060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 841.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403b2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 765.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403b5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 943.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403b850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 928.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403ba90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.000000 913.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403bcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 897.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403bf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 957.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403c150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.000000 881.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403c480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4953.000000 1120.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403c700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4953.000000 1105.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403c940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 1090.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403cb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4945.000000 1074.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403cdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4953.000000 1134.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_403d000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4961.000000 1058.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4054e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5426.000000 985.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40550c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5415.000000 970.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4055300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5440.000000 955.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4055c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5440.000000 810.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4055e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5429.000000 795.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40560a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5454.000000 780.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fae420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5422.000000 628.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fae6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5411.000000 613.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fae8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5436.000000 598.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3faf2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5420.000000 359.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3faf560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5409.000000 344.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3faf7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5434.000000 329.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb07d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5416.000000 251.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb0a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5405.000000 236.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb0c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5430.000000 221.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb47b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4608.000000 772.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb4ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4608.000000 787.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.142857 -0.000000 0.000000 -1.647059 -1762.285714 508.117647)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4979" x2="5007" y1="725" y2="708"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5007" x2="4980" y1="724" y2="708"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4979" x2="5007" y1="725" y2="708"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3079" y="-1219"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3078" y="-1099"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-637"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-26566">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 -865.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4272" ObjectName="SW-CX_DY.CX_DY_1121SW"/>
     <cge:Meas_Ref ObjectId="26566"/>
    <cge:TPSR_Ref TObjectID="4272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26568">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 -868.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4274" ObjectName="SW-CX_DY.CX_DY_1122SW"/>
     <cge:Meas_Ref ObjectId="26568"/>
    <cge:TPSR_Ref TObjectID="4274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26569">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4275" ObjectName="SW-CX_DY.CX_DY_11227SW"/>
     <cge:Meas_Ref ObjectId="26569"/>
    <cge:TPSR_Ref TObjectID="4275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26567">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4273" ObjectName="SW-CX_DY.CX_DY_11217SW"/>
     <cge:Meas_Ref ObjectId="26567"/>
    <cge:TPSR_Ref TObjectID="4273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26185">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4217" ObjectName="SW-CX_DY.CX_DY_1511SW"/>
     <cge:Meas_Ref ObjectId="26185"/>
    <cge:TPSR_Ref TObjectID="4217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26186">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4218" ObjectName="SW-CX_DY.CX_DY_1516SW"/>
     <cge:Meas_Ref ObjectId="26186"/>
    <cge:TPSR_Ref TObjectID="4218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26189">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4221" ObjectName="SW-CX_DY.CX_DY_15167SW"/>
     <cge:Meas_Ref ObjectId="26189"/>
    <cge:TPSR_Ref TObjectID="4221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26188">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 -970.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4220" ObjectName="SW-CX_DY.CX_DY_15160SW"/>
     <cge:Meas_Ref ObjectId="26188"/>
    <cge:TPSR_Ref TObjectID="4220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26205">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4223" ObjectName="SW-CX_DY.CX_DY_1521SW"/>
     <cge:Meas_Ref ObjectId="26205"/>
    <cge:TPSR_Ref TObjectID="4223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26207">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4225" ObjectName="SW-CX_DY.CX_DY_1526SW"/>
     <cge:Meas_Ref ObjectId="26207"/>
    <cge:TPSR_Ref TObjectID="4225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26209">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3980.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4227" ObjectName="SW-CX_DY.CX_DY_15267SW"/>
     <cge:Meas_Ref ObjectId="26209"/>
    <cge:TPSR_Ref TObjectID="4227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26206">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4224" ObjectName="SW-CX_DY.CX_DY_15217SW"/>
     <cge:Meas_Ref ObjectId="26206"/>
    <cge:TPSR_Ref TObjectID="4224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26208">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 -970.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4226" ObjectName="SW-CX_DY.CX_DY_15260SW"/>
     <cge:Meas_Ref ObjectId="26208"/>
    <cge:TPSR_Ref TObjectID="4226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26187">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4219" ObjectName="SW-CX_DY.CX_DY_15117SW"/>
     <cge:Meas_Ref ObjectId="26187"/>
    <cge:TPSR_Ref TObjectID="4219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26212">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -740.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4230" ObjectName="SW-CX_DY.CX_DY_19017SW"/>
     <cge:Meas_Ref ObjectId="26212"/>
    <cge:TPSR_Ref TObjectID="4230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26211">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -814.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4229" ObjectName="SW-CX_DY.CX_DY_19010SW"/>
     <cge:Meas_Ref ObjectId="26211"/>
    <cge:TPSR_Ref TObjectID="4229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26210">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 -755.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4228" ObjectName="SW-CX_DY.CX_DY_1901SW"/>
     <cge:Meas_Ref ObjectId="26210"/>
    <cge:TPSR_Ref TObjectID="4228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26257">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4239" ObjectName="SW-CX_DY.CX_DY_1542SW"/>
     <cge:Meas_Ref ObjectId="26257"/>
    <cge:TPSR_Ref TObjectID="4239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26258">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4240" ObjectName="SW-CX_DY.CX_DY_1546SW"/>
     <cge:Meas_Ref ObjectId="26258"/>
    <cge:TPSR_Ref TObjectID="4240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26261">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4243" ObjectName="SW-CX_DY.CX_DY_15467SW"/>
     <cge:Meas_Ref ObjectId="26261"/>
    <cge:TPSR_Ref TObjectID="4243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26259">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4667.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4241" ObjectName="SW-CX_DY.CX_DY_15427SW"/>
     <cge:Meas_Ref ObjectId="26259"/>
    <cge:TPSR_Ref TObjectID="4241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26260">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4667.000000 -970.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4242" ObjectName="SW-CX_DY.CX_DY_15460SW"/>
     <cge:Meas_Ref ObjectId="26260"/>
    <cge:TPSR_Ref TObjectID="4242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26237">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -864.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4233" ObjectName="SW-CX_DY.CX_DY_1532SW"/>
     <cge:Meas_Ref ObjectId="26237"/>
    <cge:TPSR_Ref TObjectID="4233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26238">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -985.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4234" ObjectName="SW-CX_DY.CX_DY_1536SW"/>
     <cge:Meas_Ref ObjectId="26238"/>
    <cge:TPSR_Ref TObjectID="4234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26241">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.000000 -1036.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4237" ObjectName="SW-CX_DY.CX_DY_15367SW"/>
     <cge:Meas_Ref ObjectId="26241"/>
    <cge:TPSR_Ref TObjectID="4237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26239">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -917.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4235" ObjectName="SW-CX_DY.CX_DY_15327SW"/>
     <cge:Meas_Ref ObjectId="26239"/>
    <cge:TPSR_Ref TObjectID="4235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26240">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -971.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4236" ObjectName="SW-CX_DY.CX_DY_15360SW"/>
     <cge:Meas_Ref ObjectId="26240"/>
    <cge:TPSR_Ref TObjectID="4236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26288">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -804.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4245" ObjectName="SW-CX_DY.CX_DY_1011SW"/>
     <cge:Meas_Ref ObjectId="26288"/>
    <cge:TPSR_Ref TObjectID="4245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26290">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -681.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4247" ObjectName="SW-CX_DY.CX_DY_1016SW"/>
     <cge:Meas_Ref ObjectId="26290"/>
    <cge:TPSR_Ref TObjectID="4247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26289">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4246" ObjectName="SW-CX_DY.CX_DY_10117SW"/>
     <cge:Meas_Ref ObjectId="26289"/>
    <cge:TPSR_Ref TObjectID="4246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26291">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 -730.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4248" ObjectName="SW-CX_DY.CX_DY_10160SW"/>
     <cge:Meas_Ref ObjectId="26291"/>
    <cge:TPSR_Ref TObjectID="4248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26426">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -791.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4258" ObjectName="SW-CX_DY.CX_DY_1022SW"/>
     <cge:Meas_Ref ObjectId="26426"/>
    <cge:TPSR_Ref TObjectID="4258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26428">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4260" ObjectName="SW-CX_DY.CX_DY_1026SW"/>
     <cge:Meas_Ref ObjectId="26428"/>
    <cge:TPSR_Ref TObjectID="4260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26427">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -779.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4259" ObjectName="SW-CX_DY.CX_DY_10217SW"/>
     <cge:Meas_Ref ObjectId="26427"/>
    <cge:TPSR_Ref TObjectID="4259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26429">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -731.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4261" ObjectName="SW-CX_DY.CX_DY_10260SW"/>
     <cge:Meas_Ref ObjectId="26429"/>
    <cge:TPSR_Ref TObjectID="4261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26440">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4342.000000 -493.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4267" ObjectName="SW-CX_DY.CX_DY_3020SW"/>
     <cge:Meas_Ref ObjectId="26440"/>
    <cge:TPSR_Ref TObjectID="4267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 -607.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4266" ObjectName="SW-CX_DY.CX_DY_3026SW"/>
     <cge:Meas_Ref ObjectId="26439"/>
    <cge:TPSR_Ref TObjectID="4266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4828.000000 -607.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4265" ObjectName="SW-CX_DY.CX_DY_3022SW"/>
     <cge:Meas_Ref ObjectId="26438"/>
    <cge:TPSR_Ref TObjectID="4265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155592">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5034.000000 -780.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26248" ObjectName="SW-DY_ZX.DY_ZX_3411SW"/>
     <cge:Meas_Ref ObjectId="155592"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155593">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5147.000000 -780.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26249" ObjectName="SW-DY_ZX.DY_ZX_3416SW"/>
     <cge:Meas_Ref ObjectId="155593"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155701">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 5029.000000 -229.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26268" ObjectName="SW-DY_ZX.DY_ZX_3451SW"/>
     <cge:Meas_Ref ObjectId="155701"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155702">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 5142.000000 -229.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26269" ObjectName="SW-DY_ZX.DY_ZX_3456SW"/>
     <cge:Meas_Ref ObjectId="155702"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5211.000000 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26261" ObjectName="SW-DY_ZX.DY_ZX_3439SW"/>
     <cge:Meas_Ref ObjectId="155666"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5016.000000 -347.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26273" ObjectName="SW-DY_ZX.DY_ZX_3461SW"/>
     <cge:Meas_Ref ObjectId="155720"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5138.000000 -347.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26274" ObjectName="SW-DY_ZX.DY_ZX_3466SW"/>
     <cge:Meas_Ref ObjectId="155721"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155594">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5211.000000 -806.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26250" ObjectName="SW-DY_ZX.DY_ZX_34167SW"/>
     <cge:Meas_Ref ObjectId="155594"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5027.000000 -603.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26252" ObjectName="SW-DY_ZX.DY_ZX_3421SW"/>
     <cge:Meas_Ref ObjectId="155612"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5172.000000 -603.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26253" ObjectName="SW-DY_ZX.DY_ZX_3426SW"/>
     <cge:Meas_Ref ObjectId="155613"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155614">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5273.000000 -567.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26254" ObjectName="SW-DY_ZX.DY_ZX_3429SW"/>
     <cge:Meas_Ref ObjectId="155614"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155664">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5001.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26259" ObjectName="SW-DY_ZX.DY_ZX_3431SW"/>
     <cge:Meas_Ref ObjectId="155664"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5147.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26260" ObjectName="SW-DY_ZX.DY_ZX_3436SW"/>
     <cge:Meas_Ref ObjectId="155665"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 5209.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26271" ObjectName="SW-DY_ZX.DY_ZX_3459SW"/>
     <cge:Meas_Ref ObjectId="155704"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155703">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 5206.000000 -255.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26270" ObjectName="SW-DY_ZX.DY_ZX_34567SW"/>
     <cge:Meas_Ref ObjectId="155703"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155734">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4909.000000 -955.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26275" ObjectName="SW-DY_ZX.DY_ZX_3901SW"/>
     <cge:Meas_Ref ObjectId="155734"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.000000 -969.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26276" ObjectName="SW-DY_ZX.DY_ZX_39017SW"/>
     <cge:Meas_Ref ObjectId="155735"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26677">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -739.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4306" ObjectName="SW-CX_DY.CX_DY_19027SW"/>
     <cge:Meas_Ref ObjectId="26677"/>
    <cge:TPSR_Ref TObjectID="4306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26676">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -813.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4305" ObjectName="SW-CX_DY.CX_DY_19020SW"/>
     <cge:Meas_Ref ObjectId="26676"/>
    <cge:TPSR_Ref TObjectID="4305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26675">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 -754.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4304" ObjectName="SW-CX_DY.CX_DY_1902SW"/>
     <cge:Meas_Ref ObjectId="26675"/>
    <cge:TPSR_Ref TObjectID="4304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26430">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -670.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4262" ObjectName="SW-CX_DY.CX_DY_10267SW"/>
     <cge:Meas_Ref ObjectId="26430"/>
    <cge:TPSR_Ref TObjectID="4262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155935">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -0.891304 4920.000000 -433.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26326" ObjectName="SW-DY_ZX.DY_ZX_3121SW"/>
     <cge:Meas_Ref ObjectId="155935"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155617">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5252.000000 -627.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26257" ObjectName="SW-DY_ZX.DY_ZX_34267SW"/>
     <cge:Meas_Ref ObjectId="155617"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155615">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5088.000000 -661.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26255" ObjectName="SW-DY_ZX.DY_ZX_34217SW"/>
     <cge:Meas_Ref ObjectId="155615"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155616">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5166.000000 -650.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26256" ObjectName="SW-DY_ZX.DY_ZX_34260SW"/>
     <cge:Meas_Ref ObjectId="155616"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26434">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4381.000000 -577.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4263" ObjectName="SW-CX_DY.CX_DY_1020SW"/>
     <cge:Meas_Ref ObjectId="26434"/>
    <cge:TPSR_Ref TObjectID="4263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-305435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -553.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47300" ObjectName="SW-CX_DY.CX_DY_30217SW"/>
     <cge:Meas_Ref ObjectId="305435"/>
    <cge:TPSR_Ref TObjectID="47300"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3f169d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4819.500000 -918.500000)" xlink:href="#voltageTransformer:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f1e9e0">
    <use class="BV-110KV" transform="matrix(1.250000 -0.000000 0.000000 1.928571 3703.000000 -712.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f20ea0">
    <use class="BV-110KV" transform="matrix(1.250000 -0.000000 0.000000 1.928571 4764.000000 -707.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanda_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3737,-1139 3737,-1185 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11550" ObjectName="AC-110kV.yuanda_line"/>
    <cge:TPSR_Ref TObjectID="11550_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="3737,-1139 3737,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaodaTdayao_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3967,-1138 3967,-1183 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9192" ObjectName="AC-110kV.yaodaTdayao_line"/>
    <cge:TPSR_Ref TObjectID="9192_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="3967,-1138 3967,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YPJ" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuda_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4374,-1134 4374,-1180 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11716" ObjectName="AC-110kV.yuda_line"/>
    <cge:TPSR_Ref TObjectID="11716_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="4374,-1134 4374,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DY" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-110kV.daliu_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4655,-1141 4655,-1188 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11715" ObjectName="AC-110kV.daliu_line"/>
    <cge:TPSR_Ref TObjectID="11715_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="4655,-1141 4655,-1188 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3edb460">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3843.500000 -1102.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f717a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3872.000000 -1065.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f8de50">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4073.500000 -1102.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f8ee20">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -1065.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f3e990">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3772.500000 -708.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f5a030">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4761.500000 -1102.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f5b040">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4790.000000 -1065.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4056630">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4480.500000 -1103.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4057640">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4509.000000 -1066.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40053d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4441.000000 -569.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4005f80">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -581.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f66630">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -431.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4016b80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5318.000000 -554.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ec50f0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5307.500000 -966.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ec5bd0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5364.000000 -969.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ed0ba0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5246.500000 -768.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ed1420">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5292.000000 -771.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3efd360">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5178.500000 -567.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3efdbe0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5226.000000 -570.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f00cd0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5360.500000 -581.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f01550">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5406.000000 -584.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f02370">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5258.000000 -934.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f7b070">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5302.500000 -216.246531)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f7bb50">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5359.000000 -218.746531)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f81c30">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4892.000000 -924.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f829e0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4884.500000 -969.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f861a0">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4833.500000 -707.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f4b510">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4164.000000 -495.000000)" xlink:href="#lightningRod:shape103"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f19440">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5201.000000 -740.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f1ab70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5262.000000 -181.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f1c040">
    <use class="BV-35KV" transform="matrix(0.943820 -0.000000 0.000000 -0.764706 5241.000000 -226.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb5c20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.000000 -603.000000)" xlink:href="#lightningRod:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-26111" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4663.000000 -770.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26111" ObjectName="CX_DY:CX_DY_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3188.500000 -1139.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62641" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3218.538462 -968.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62641" ObjectName="CX_DY:CX_DY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79712" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3217.538462 -929.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79712" ObjectName="CX_DY:CX_DY_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208330" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3212.538462 -1045.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208330" ObjectName="CX_DY:CX_DY_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239628" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3215.538462 -1006.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239628" ObjectName="CX_DY:CX_DY_sumP2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-26060" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3807.000000 -1144.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26060" ObjectName="CX_DY:CX_DY_151BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-26068" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -1145.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26068" ObjectName="CX_DY:CX_DY_152BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-56788" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.000000 -1141.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56788" ObjectName="CX_DY:CX_DY_153BK_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-26083" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.000000 -1146.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26083" ObjectName="CX_DY:CX_DY_154BK_U"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -1169.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26064" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4216"/>
     <cge:Term_Ref ObjectID="6072"/>
    <cge:TPSR_Ref TObjectID="4216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -1169.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4216"/>
     <cge:Term_Ref ObjectID="6072"/>
    <cge:TPSR_Ref TObjectID="4216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -1169.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4216"/>
     <cge:Term_Ref ObjectID="6072"/>
    <cge:TPSR_Ref TObjectID="4216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -845.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4271"/>
     <cge:Term_Ref ObjectID="6182"/>
    <cge:TPSR_Ref TObjectID="4271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -845.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4271"/>
     <cge:Term_Ref ObjectID="6182"/>
    <cge:TPSR_Ref TObjectID="4271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -845.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4271"/>
     <cge:Term_Ref ObjectID="6182"/>
    <cge:TPSR_Ref TObjectID="4271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3931.000000 -1176.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4222"/>
     <cge:Term_Ref ObjectID="6084"/>
    <cge:TPSR_Ref TObjectID="4222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3931.000000 -1176.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4222"/>
     <cge:Term_Ref ObjectID="6084"/>
    <cge:TPSR_Ref TObjectID="4222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3931.000000 -1176.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4222"/>
     <cge:Term_Ref ObjectID="6084"/>
    <cge:TPSR_Ref TObjectID="4222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4588.000000 -1165.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4238"/>
     <cge:Term_Ref ObjectID="6116"/>
    <cge:TPSR_Ref TObjectID="4238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4588.000000 -1165.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4238"/>
     <cge:Term_Ref ObjectID="6116"/>
    <cge:TPSR_Ref TObjectID="4238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4588.000000 -1165.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4238"/>
     <cge:Term_Ref ObjectID="6116"/>
    <cge:TPSR_Ref TObjectID="4238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4336.000000 -1167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4232"/>
     <cge:Term_Ref ObjectID="6104"/>
    <cge:TPSR_Ref TObjectID="4232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4336.000000 -1167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4232"/>
     <cge:Term_Ref ObjectID="6104"/>
    <cge:TPSR_Ref TObjectID="4232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4336.000000 -1167.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4232"/>
     <cge:Term_Ref ObjectID="6104"/>
    <cge:TPSR_Ref TObjectID="4232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -783.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4244"/>
     <cge:Term_Ref ObjectID="6128"/>
    <cge:TPSR_Ref TObjectID="4244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -783.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4244"/>
     <cge:Term_Ref ObjectID="6128"/>
    <cge:TPSR_Ref TObjectID="4244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -783.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4244"/>
     <cge:Term_Ref ObjectID="6128"/>
    <cge:TPSR_Ref TObjectID="4244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26109" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -840.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4257"/>
     <cge:Term_Ref ObjectID="6154"/>
    <cge:TPSR_Ref TObjectID="4257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -840.239382) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4257"/>
     <cge:Term_Ref ObjectID="6154"/>
    <cge:TPSR_Ref TObjectID="4257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -840.239382) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4257"/>
     <cge:Term_Ref ObjectID="6154"/>
    <cge:TPSR_Ref TObjectID="4257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-62472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4755.000000 -602.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4264"/>
     <cge:Term_Ref ObjectID="6168"/>
    <cge:TPSR_Ref TObjectID="4264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-62473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4755.000000 -602.239382) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4264"/>
     <cge:Term_Ref ObjectID="6168"/>
    <cge:TPSR_Ref TObjectID="4264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-62469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4755.000000 -602.239382) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4264"/>
     <cge:Term_Ref ObjectID="6168"/>
    <cge:TPSR_Ref TObjectID="4264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-26113" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4694.000000 -786.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4313"/>
     <cge:Term_Ref ObjectID="6275"/>
    <cge:TPSR_Ref TObjectID="4313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -958.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -958.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -958.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -958.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -958.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -958.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3647.000000 -840.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3647.000000 -840.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3647.000000 -840.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3647.000000 -840.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3647.000000 -840.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3647.000000 -840.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-155480" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -1133.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-155481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -1133.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-155482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -1133.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-155486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -1133.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-155483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -1133.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-155487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5013.000000 -1133.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26243"/>
     <cge:Term_Ref ObjectID="37043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5492.000000 -988.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5492.000000 -988.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5492.000000 -988.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26258"/>
     <cge:Term_Ref ObjectID="37070"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5505.000000 -810.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5505.000000 -810.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5505.000000 -810.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26247"/>
     <cge:Term_Ref ObjectID="37048"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5480.000000 -627.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5480.000000 -627.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5480.000000 -627.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26251"/>
     <cge:Term_Ref ObjectID="37056"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5479.000000 -361.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5479.000000 -361.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5479.000000 -361.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26272"/>
     <cge:Term_Ref ObjectID="37098"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-155471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5477.000000 -251.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-155472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5477.000000 -251.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-155468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5477.000000 -251.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="155468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26267"/>
     <cge:Term_Ref ObjectID="37088"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3200" y="-1198"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3200" y="-1198"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3152" y="-1215"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3152" y="-1215"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3374,-1207 3371,-1210 3371,-1148 3374,-1151 3374,-1207" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3374,-1207 3371,-1210 3430,-1210 3427,-1207 3374,-1207" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3374,-1151 3371,-1148 3430,-1148 3427,-1151 3374,-1151" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3427,-1207 3430,-1210 3430,-1148 3427,-1151 3427,-1207" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="56" stroke="rgb(255,255,255)" width="53" x="3374" y="-1207"/>
     <rect fill="none" height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="3374" y="-1207"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="22" x="4623" y="-1149"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="22" x="4623" y="-1149"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4398" y="-683"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4398" y="-683"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3745" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3745" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3975" y="-956"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3975" y="-956"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="24" x="4189" y="-949"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="24" x="4189" y="-949"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4383" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4383" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4664" y="-956"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4664" y="-956"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="31" qtmmishow="hidden" width="101" x="3153" y="-790"/>
    </a>
   <metadata/><rect fill="white" height="31" opacity="0" stroke="white" transform="" width="101" x="3153" y="-790"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3454" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3454" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3454" y="-1212"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3454" y="-1212"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="31" qtmmishow="hidden" width="101" x="3151" y="-719"/>
    </a>
   <metadata/><rect fill="white" height="31" opacity="0" stroke="white" transform="" width="101" x="3151" y="-719"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3200" y="-1198"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3152" y="-1215"/></g>
   <g href="AVC大姚站.svg" style="fill-opacity:0"><rect height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="3374" y="-1207"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="22" x="4623" y="-1149"/></g>
   <g href="110kV大姚变110kV2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4398" y="-683"/></g>
   <g href="110kV大姚变110kV元大线151断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3745" y="-957"/></g>
   <g href="110kV大姚变110kV姚大线152断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3975" y="-956"/></g>
   <g href="110kV大姚变110kV分段112断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="24" x="4189" y="-949"/></g>
   <g href="110kV大姚变110kV渔大线153断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4383" y="-957"/></g>
   <g href="110kV大姚变110kV大六线154断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4664" y="-956"/></g>
   <g href="110kV大姚变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="31" qtmmishow="hidden" width="101" x="3153" y="-790"/></g>
   <g href="cx_配调_配网接线图110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3454" y="-1177"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3454" y="-1212"/></g>
   <g href="110kV大姚变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="31" qtmmishow="hidden" width="101" x="3151" y="-719"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5428" x2="5433" y1="-605" y2="-601"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5651" x2="5656" y1="-478" y2="-478"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DY.CX_DY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="6265"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -565.000000)" xlink:href="#transformer:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="6267"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -565.000000)" xlink:href="#transformer:shape24_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="6269"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -565.000000)" xlink:href="#transformer:shape24-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4312" ObjectName="TF-CX_DY.CX_DY_2T"/>
    <cge:TPSR_Ref TObjectID="4312"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3edc470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.000000 -989.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4012860" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.000000 -989.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ed9630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.000000 -969.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3eda050" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.000000 -1034.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f8af80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 -915.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f8ba10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 -969.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f8c4a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 -1034.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f2ed20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.000000 -915.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f2fc70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3814.000000 -751.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f3d0c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3814.000000 -825.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f57700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4717.000000 -915.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f58190" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4717.000000 -969.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f58c20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4717.000000 -1034.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f6cb50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -916.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f6d5e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -970.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f6e070" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -1035.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ebd260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4006.000000 -793.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ec0ba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4006.000000 -741.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f5d970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4553.000000 -790.239382)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f60c80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4553.000000 -742.239382)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ed0110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5259.000000 -817.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f780b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5254.000000 -264.746531)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f811e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4893.000000 -1044.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f83980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4875.000000 -750.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f848d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4875.000000 -824.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f4e110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4553.000000 -681.239382)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4040650" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5300.000000 -638.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_404e6a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5136.000000 -672.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4052ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5214.000000 -661.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fbc850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.000000 -520.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="3751" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4812" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="3967" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="4147" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4262" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="3737" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4655" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4374" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="3944" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4490" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4977" cy="-785" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26327" cx="4977" cy="-233" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4977" cy="-960" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4977" cy="-485" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26327" cx="4977" cy="-417" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26327" cx="4977" cy="-352" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4977" cy="-983" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4977" cy="-608" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26243" cx="4977" cy="-554" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f449f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3745.000000 -957.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f45020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -893.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f45260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -940.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f454a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -998.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f456e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -1063.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f45920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3741.000000 -1015.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f45b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -956.000000) translate(0,12)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f45da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3970.000000 -1014.000000) translate(0,12)">1526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f45fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3969.000000 -891.000000) translate(0,12)">1521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f46220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3983.000000 -1066.000000) translate(0,12)">15267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f46460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.000000 -998.000000) translate(0,12)">15260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f466a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3983.000000 -940.000000) translate(0,12)">15217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f468e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3632.000000 -879.000000) translate(0,12)">110kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f46b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4826.000000 -875.000000) translate(0,12)">110kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f46d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4189.000000 -949.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f46fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -895.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f471e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -897.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f47420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -972.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f47660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -971.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f478a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4383.000000 -957.000000) translate(0,12)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f47ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -894.000000) translate(0,12)">1532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f47d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4390.000000 -942.000000) translate(0,12)">15327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f47f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -997.000000) translate(0,12)">15360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f481a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -1015.000000) translate(0,12)">1536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f483e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4390.000000 -1062.000000) translate(0,12)">15367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f48620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -956.000000) translate(0,12)">154</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f48860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.000000 -893.000000) translate(0,12)">1542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f48aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.000000 -1014.000000) translate(0,12)">1546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f48ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -1062.000000) translate(0,12)">15467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f48f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -997.000000) translate(0,12)">15460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f49160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.500000 -941.000000) translate(0,12)">15427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f493a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4819.000000 -784.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f495e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4828.000000 -844.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f49820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4828.000000 -770.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f49a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -785.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f49ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3767.000000 -841.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f49ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3767.000000 -767.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4a120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3952.000000 -767.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4a360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -834.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3966.000000 -810.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4a7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3965.500000 -754.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4aa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -709.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_3f4ac60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4924.000000 -1035.000000) translate(0,15)">35kV中心变Ⅰ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_3f4aeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -196.000000) translate(0,15)">35kV中心变Ⅱ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4b0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4348.000000 -524.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4cf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4499.000000 -768.239382) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4d430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4492.000000 -822.239382) translate(0,12)">1022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4d670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4495.000000 -716.239382) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.500000 -804.239382) translate(0,12)">10227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4daf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.000000 -755.239382) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f50f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.000000 -697.239382) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f513f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 -612.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f51630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4736.000000 -632.239382) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f51870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4834.000000 -633.239382) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f51ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -634.239382) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f51cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4916.000000 -982.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0cd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -1122.000000) translate(0,15)">元</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0cd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -1122.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0cd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -1122.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -1120.000000) translate(0,15)">姚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -1120.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -1120.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -1117.000000) translate(0,15)">渔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -1117.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -1117.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -1117.000000) translate(0,15)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -1117.000000) translate(0,33)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -1117.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 -730.000000) translate(0,15)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 -730.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 -730.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 -730.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 -730.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0d8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5321.000000 -1006.000000) translate(0,15)">大石河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0db00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5321.000000 -810.000000) translate(0,15)">中北Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0dd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5342.000000 -636.000000) translate(0,15)">中仓线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0df80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5084.000000 -539.000000) translate(0,15)">35kV中心变1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0ec20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5321.000000 -380.000000) translate(0,15)">中北I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0efe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5310.000000 -256.000000) translate(0,15)">六中杨石线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0f220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -730.000000) translate(0,15)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0f220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -730.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0f220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -730.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0f220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -730.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0f220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -730.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f0f470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -433.000000) translate(0,15)">1号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,59)">片区有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3f0fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3f108b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3240.500000 -1187.500000) translate(0,16)">大姚变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f15560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4383.000000 -957.000000) translate(0,12)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f16780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4838.000000 -918.000000) translate(0,12)">35kVI母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4030b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3382.000000 -1190.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_4043e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3155.000000 -787.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40445d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -812.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40445d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -812.000000) translate(0,27)">SFSZ11-40000/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40445d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -812.000000) translate(0,42)">110±8×1.25%/38.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40445d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -812.000000) translate(0,57)">±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40445d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -812.000000) translate(0,72)">40/40/40MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40445d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -812.000000) translate(0,87)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40445d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -812.000000) translate(0,102)">Uk1-2%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40445d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -812.000000) translate(0,117)">Uk1-3%=17.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40445d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -812.000000) translate(0,132)">Uk2-3%=6.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_4044a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3212.000000 -248.000000) translate(0,17)">4913</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,16)">1、全站停电检修前应挂“全站停电检修”牌，复电后</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,36)">方可摘除“全站停电检修”牌。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,76)">2、各类间隔工作时，停电完成后应在相应间隔挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,96)">“禁止合闸，有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,136)">3、线路工作时，停电完成后应在相应间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,156)">挂“禁止合闸，线路有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,196)">4、现场工作影响对应间隔四遥信息正确性的，应挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,216)">“禁止刷新”牌，工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,256)">5、现场开展相应间隔四遥信息核对前，应挂“调试一”牌，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_4045110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3099.000000 -563.000000) translate(0,276)">核对工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4045820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -1169.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4045cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -1204.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40474e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.000000 -1144.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4047b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -1145.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40481c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -1141.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4048830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4667.000000 -1146.000000) translate(0,12)">Ux(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4048b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3055.000000 -202.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_4048e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3209.000000 -212.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_4048e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3209.000000 -212.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4049050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4938.000000 -461.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4049280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4840.000000 -1004.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40494c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -1008.000000) translate(0,12)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4049700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -1009.000000) translate(0,12)">3431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4049940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5154.000000 -1009.000000) translate(0,12)">3436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4049b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5104.000000 -810.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4049dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5042.000000 -811.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404a000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5154.000000 -813.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404a240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5214.000000 -840.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404a480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5034.000000 -634.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404a6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5096.000000 -633.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404a900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -634.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404ab40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5272.000000 -556.000000) translate(0,12)">3429</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404ad80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5255.000000 -658.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404afc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5094.000000 -377.000000) translate(0,12)">346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404b200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5023.000000 -378.000000) translate(0,12)">3461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5144.000000 -382.000000) translate(0,12)">3466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404b680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5098.000000 -268.000000) translate(0,12)">345</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -259.000000) translate(0,12)">3451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404bb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5149.000000 -259.000000) translate(0,12)">3456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404bd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5209.000000 -285.000000) translate(0,12)">34567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404bf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5213.000000 -199.000000) translate(0,12)">3459</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_404f130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5086.000000 -697.000000) translate(0,12)">34217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4053540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -682.000000) translate(0,12)">34260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_40547a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4916.000000 -1161.000000) translate(0,15)">中心变35kV母线数据</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3fb3f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3153.000000 -715.000000) translate(0,20)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb53f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5216.000000 -949.000000) translate(0,12)">3439</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fb59e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4545.000000 -603.000000) translate(0,15)">35kV临供电缆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fb6d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -674.000000) translate(0,15)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fb6d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -674.000000) translate(0,33)">主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fb6d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -674.000000) translate(0,51)">拆除</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fb6d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -674.000000) translate(0,69)">解脱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fb6d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -674.000000) translate(0,87)">点</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb9840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4407.000000 -683.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fbe4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4814.000000 -583.000000) translate(0,12)">30217</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4634" cy="-1138" fill="rgb(0,0,0)" fillStyle="1" r="10" stroke="rgb(255,255,255)" stroke-width="1.75238"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DY.CX_DY_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3678,-855 4175,-855 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4212" ObjectName="BS-CX_DY.CX_DY_1IM"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   <polyline fill="none" opacity="0" points="3678,-855 4175,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DY.CX_DY_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-855 4869,-855 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4213" ObjectName="BS-CX_DY.CX_DY_1IIM"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   <polyline fill="none" opacity="0" points="4237,-855 4869,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_ZX.DY_ZX_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-200 4977,-432 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26327" ObjectName="BS-DY_ZX.DY_ZX_3ⅡM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4977,-200 4977,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_ZX.DY_ZX_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-471 4977,-1015 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26243" ObjectName="BS-DY_ZX.DY_ZX_3ⅠM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4977,-471 4977,-1015 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-26" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3383.000000 -1107.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26" ObjectName="DYN-CX_DY"/>
     <cge:Meas_Ref ObjectId="26"/>
    </metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-26562">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4180.000000 -917.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4271" ObjectName="SW-CX_DY.CX_DY_112BK"/>
     <cge:Meas_Ref ObjectId="26562"/>
    <cge:TPSR_Ref TObjectID="4271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26183">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -927.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4216" ObjectName="SW-CX_DY.CX_DY_151BK"/>
     <cge:Meas_Ref ObjectId="26183"/>
    <cge:TPSR_Ref TObjectID="4216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26202">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -927.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4222" ObjectName="SW-CX_DY.CX_DY_152BK"/>
     <cge:Meas_Ref ObjectId="26202"/>
    <cge:TPSR_Ref TObjectID="4222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26255">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -927.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4238" ObjectName="SW-CX_DY.CX_DY_154BK"/>
     <cge:Meas_Ref ObjectId="26255"/>
    <cge:TPSR_Ref TObjectID="4238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26286">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -737.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4244" ObjectName="SW-CX_DY.CX_DY_101BK"/>
     <cge:Meas_Ref ObjectId="26286"/>
    <cge:TPSR_Ref TObjectID="4244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26422">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -738.239382)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4257" ObjectName="SW-CX_DY.CX_DY_102BK"/>
     <cge:Meas_Ref ObjectId="26422"/>
    <cge:TPSR_Ref TObjectID="4257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4726.000000 -602.239382)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4264" ObjectName="SW-CX_DY.CX_DY_302BK"/>
     <cge:Meas_Ref ObjectId="26435"/>
    <cge:TPSR_Ref TObjectID="4264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5084.000000 -362.345005)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26272" ObjectName="SW-DY_ZX.DY_ZX_346BK"/>
     <cge:Meas_Ref ObjectId="155719"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155663">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5093.000000 -993.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26258" ObjectName="SW-DY_ZX.DY_ZX_343BK"/>
     <cge:Meas_Ref ObjectId="155663"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5093.000000 -795.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26247" ObjectName="SW-DY_ZX.DY_ZX_341BK"/>
     <cge:Meas_Ref ObjectId="155591"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155611">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5086.000000 -618.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26251" ObjectName="SW-DY_ZX.DY_ZX_342BK"/>
     <cge:Meas_Ref ObjectId="155611"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-155700">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5088.000000 -243.746531)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26267" ObjectName="SW-DY_ZX.DY_ZX_345BK"/>
     <cge:Meas_Ref ObjectId="155700"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26236">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -928.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4232" ObjectName="SW-CX_DY.CX_DY_153BK"/>
     <cge:Meas_Ref ObjectId="26236"/>
    <cge:TPSR_Ref TObjectID="4232"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_3edcff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4147,-855 4147,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDZND0="4272@0" Pin0InfoVect0LinkObjId="SW-26566_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4147,-855 4147,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fa4db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-855 4262,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDZND0="4274@0" Pin0InfoVect0LinkObjId="SW-26568_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f54c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-855 4262,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fa5010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4147,-903 4147,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4272@1" ObjectIDZND0="4271@x" ObjectIDZND1="4273@x" Pin0InfoVect0LinkObjId="SW-26562_0" Pin0InfoVect1LinkObjId="SW-26567_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26566_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4147,-903 4147,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fa5270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-909 4262,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4274@1" ObjectIDZND0="4271@x" ObjectIDZND1="4275@x" Pin0InfoVect0LinkObjId="SW-26562_0" Pin0InfoVect1LinkObjId="SW-26569_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26568_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-909 4262,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3eb73a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4147,-927 4189,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4273@x" ObjectIDND1="4272@x" ObjectIDZND0="4271@1" Pin0InfoVect0LinkObjId="SW-26562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26567_0" Pin1InfoVect1LinkObjId="SW-26566_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4147,-927 4189,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3eb7600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4216,-927 4262,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4271@0" ObjectIDZND0="4274@x" ObjectIDZND1="4275@x" Pin0InfoVect0LinkObjId="SW-26568_0" Pin0InfoVect1LinkObjId="SW-26569_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4216,-927 4262,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3eb7860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4147,-927 4147,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4271@x" ObjectIDND1="4272@x" ObjectIDZND0="4273@0" Pin0InfoVect0LinkObjId="SW-26567_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26562_0" Pin1InfoVect1LinkObjId="SW-26566_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4147,-927 4147,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3eb7ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4147,-983 4147,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4273@1" ObjectIDZND0="g_3edc470@0" Pin0InfoVect0LinkObjId="g_3edc470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26567_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4147,-983 4147,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3edbfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-927 4262,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4271@x" ObjectIDND1="4274@x" ObjectIDZND0="4275@0" Pin0InfoVect0LinkObjId="SW-26569_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26562_0" Pin1InfoVect1LinkObjId="SW-26568_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-927 4262,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3edc210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-983 4262,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4275@1" ObjectIDZND0="g_4012860@0" Pin0InfoVect0LinkObjId="g_4012860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26569_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-983 4262,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef4f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-905 3737,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4217@1" ObjectIDZND0="4216@x" ObjectIDZND1="4219@x" Pin0InfoVect0LinkObjId="SW-26183_0" Pin0InfoVect1LinkObjId="SW-26187_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26185_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-905 3737,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef51e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-921 3737,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4219@x" ObjectIDND1="4217@x" ObjectIDZND0="4216@0" Pin0InfoVect0LinkObjId="SW-26183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26187_0" Pin1InfoVect1LinkObjId="SW-26185_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-921 3737,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef5440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-962 3737,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4216@1" ObjectIDZND0="4218@x" ObjectIDZND1="4220@x" Pin0InfoVect0LinkObjId="SW-26186_0" Pin0InfoVect1LinkObjId="SW-26188_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-962 3737,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef56a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-975 3737,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4216@x" ObjectIDND1="4220@x" ObjectIDZND0="4218@0" Pin0InfoVect0LinkObjId="SW-26186_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26183_0" Pin1InfoVect1LinkObjId="SW-26188_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-975 3737,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ed8f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-975 3754,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4216@x" ObjectIDND1="4218@x" ObjectIDZND0="4220@0" Pin0InfoVect0LinkObjId="SW-26188_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26183_0" Pin1InfoVect1LinkObjId="SW-26186_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-975 3754,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ed9170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-975 3804,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4220@1" ObjectIDZND0="g_3ed9630@0" Pin0InfoVect0LinkObjId="g_3ed9630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26188_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-975 3804,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ed93d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3791,-1040 3804,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4221@1" ObjectIDZND0="g_3eda050@0" Pin0InfoVect0LinkObjId="g_3eda050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26189_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3791,-1040 3804,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3edaae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1025 3737,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4218@1" ObjectIDZND0="g_3f717a0@0" ObjectIDZND1="g_3edb460@0" ObjectIDZND2="11550@1" Pin0InfoVect0LinkObjId="g_3f717a0_0" Pin0InfoVect1LinkObjId="g_3edb460_0" Pin0InfoVect2LinkObjId="g_3f1cf80_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26186_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1025 3737,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3edad40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1040 3755,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3f717a0@0" ObjectIDND1="g_3edb460@0" ObjectIDND2="11550@1" ObjectIDZND0="4221@0" Pin0InfoVect0LinkObjId="SW-26189_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f717a0_0" Pin1InfoVect1LinkObjId="g_3edb460_0" Pin1InfoVect2LinkObjId="g_3f1cf80_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1040 3755,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3edafa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3850,-1108 3850,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f717a0@0" ObjectIDND1="4218@x" ObjectIDND2="4221@x" ObjectIDZND0="g_3edb460@0" Pin0InfoVect0LinkObjId="g_3edb460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f717a0_0" Pin1InfoVect1LinkObjId="SW-26186_0" Pin1InfoVect2LinkObjId="SW-26189_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3850,-1108 3850,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3edb200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3850,-1108 3880,-1108 3880,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3edb460@0" ObjectIDND1="4218@x" ObjectIDND2="4221@x" ObjectIDZND0="g_3f717a0@0" Pin0InfoVect0LinkObjId="g_3f717a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3edb460_0" Pin1InfoVect1LinkObjId="SW-26186_0" Pin1InfoVect2LinkObjId="SW-26189_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3850,-1108 3880,-1108 3880,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f71540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-855 3737,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDZND0="4217@0" Pin0InfoVect0LinkObjId="SW-26185_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-855 3737,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef1790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-905 3967,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4223@1" ObjectIDZND0="4222@x" ObjectIDZND1="4224@x" Pin0InfoVect0LinkObjId="SW-26202_0" Pin0InfoVect1LinkObjId="SW-26206_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26205_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-905 3967,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef19f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-921 3967,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4224@x" ObjectIDND1="4223@x" ObjectIDZND0="4222@0" Pin0InfoVect0LinkObjId="SW-26202_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26206_0" Pin1InfoVect1LinkObjId="SW-26205_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-921 3967,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef1c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-962 3967,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4222@1" ObjectIDZND0="4225@x" ObjectIDZND1="4226@x" Pin0InfoVect0LinkObjId="SW-26207_0" Pin0InfoVect1LinkObjId="SW-26208_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26202_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-962 3967,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef1eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-975 3967,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4222@x" ObjectIDND1="4226@x" ObjectIDZND0="4225@0" Pin0InfoVect0LinkObjId="SW-26207_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26202_0" Pin1InfoVect1LinkObjId="SW-26208_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-975 3967,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8a3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-921 3984,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4222@x" ObjectIDND1="4223@x" ObjectIDZND0="4224@0" Pin0InfoVect0LinkObjId="SW-26206_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26202_0" Pin1InfoVect1LinkObjId="SW-26205_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-921 3984,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8a600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-921 4034,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4224@1" ObjectIDZND0="g_3f8af80@0" Pin0InfoVect0LinkObjId="g_3f8af80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4020,-921 4034,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8a860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-975 3984,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4222@x" ObjectIDND1="4225@x" ObjectIDZND0="4226@0" Pin0InfoVect0LinkObjId="SW-26208_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26202_0" Pin1InfoVect1LinkObjId="SW-26207_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-975 3984,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8aac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-975 4034,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4226@1" ObjectIDZND0="g_3f8ba10@0" Pin0InfoVect0LinkObjId="g_3f8ba10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4020,-975 4034,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-1040 4034,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4227@1" ObjectIDZND0="g_3f8c4a0@0" Pin0InfoVect0LinkObjId="g_3f8c4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-1040 4034,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8d4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-1025 3967,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4225@1" ObjectIDZND0="4227@x" ObjectIDZND1="g_3f8ee20@0" ObjectIDZND2="g_3f8de50@0" Pin0InfoVect0LinkObjId="SW-26209_0" Pin0InfoVect1LinkObjId="g_3f8ee20_0" Pin0InfoVect2LinkObjId="g_3f8de50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26207_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-1025 3967,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8d730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-1040 3985,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="4225@x" ObjectIDND1="g_3f8ee20@0" ObjectIDND2="g_3f8de50@0" ObjectIDZND0="4227@0" Pin0InfoVect0LinkObjId="SW-26209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26207_0" Pin1InfoVect1LinkObjId="g_3f8ee20_0" Pin1InfoVect2LinkObjId="g_3f8de50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-1040 3985,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8d990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-1108 4080,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f8ee20@0" ObjectIDND1="4225@x" ObjectIDND2="4227@x" ObjectIDZND0="g_3f8de50@0" Pin0InfoVect0LinkObjId="g_3f8de50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f8ee20_0" Pin1InfoVect1LinkObjId="SW-26207_0" Pin1InfoVect2LinkObjId="SW-26209_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-1108 4080,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8dbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-1108 4110,-1108 4110,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f8de50@0" ObjectIDND1="4225@x" ObjectIDND2="4227@x" ObjectIDZND0="g_3f8ee20@0" Pin0InfoVect0LinkObjId="g_3f8ee20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f8de50_0" Pin1InfoVect1LinkObjId="SW-26207_0" Pin1InfoVect2LinkObjId="SW-26209_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-1108 4110,-1108 4110,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f8ebc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-855 3967,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDZND0="4223@0" Pin0InfoVect0LinkObjId="SW-26205_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-855 3967,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f2e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-921 3754,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4216@x" ObjectIDND1="4217@x" ObjectIDZND0="4219@0" Pin0InfoVect0LinkObjId="SW-26187_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26183_0" Pin1InfoVect1LinkObjId="SW-26185_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-921 3754,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f2eac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-921 3804,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4219@1" ObjectIDZND0="g_3f2ed20@0" Pin0InfoVect0LinkObjId="g_3f2ed20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26187_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-921 3804,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f2f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3752,-745 3769,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3f3e990@0" ObjectIDND1="g_3f1e9e0@0" ObjectIDND2="4228@x" ObjectIDZND0="4230@0" Pin0InfoVect0LinkObjId="SW-26212_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f3e990_0" Pin1InfoVect1LinkObjId="g_3f1e9e0_0" Pin1InfoVect2LinkObjId="SW-26210_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3752,-745 3769,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f2fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-745 3819,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4230@1" ObjectIDZND0="g_3f2fc70@0" Pin0InfoVect0LinkObjId="g_3f2fc70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26212_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-745 3819,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3cc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3752,-819 3769,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDND1="4228@x" ObjectIDZND0="4229@0" Pin0InfoVect0LinkObjId="SW-26211_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-26210_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3752,-819 3769,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3ce60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-819 3819,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4229@1" ObjectIDZND0="g_3f3d0c0@0" Pin0InfoVect0LinkObjId="g_3f3d0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26211_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-819 3819,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3db50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-760 3751,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="4228@0" ObjectIDZND0="g_3f3e990@0" ObjectIDZND1="g_3f1e9e0@0" ObjectIDZND2="4230@x" Pin0InfoVect0LinkObjId="g_3f3e990_0" Pin0InfoVect1LinkObjId="g_3f1e9e0_0" Pin0InfoVect2LinkObjId="SW-26212_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-760 3751,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3ddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-855 3751,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4212@0" ObjectIDZND0="4228@x" ObjectIDZND1="4229@x" Pin0InfoVect0LinkObjId="SW-26210_0" Pin0InfoVect1LinkObjId="SW-26211_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-855 3751,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3e010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-819 3751,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDND1="4229@x" ObjectIDZND0="4228@1" Pin0InfoVect0LinkObjId="SW-26210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-26211_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-819 3751,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3e270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-721 3727,-721 3727,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3f3e990@0" ObjectIDND1="4228@x" ObjectIDND2="4230@x" ObjectIDZND0="g_3f1e9e0@0" Pin0InfoVect0LinkObjId="g_3f1e9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f3e990_0" Pin1InfoVect1LinkObjId="SW-26210_0" Pin1InfoVect2LinkObjId="SW-26212_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-721 3727,-721 3727,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3e4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-704 3779,-721 3751,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3f3e990@0" ObjectIDZND0="g_3f1e9e0@0" ObjectIDZND1="4228@x" ObjectIDZND2="4230@x" Pin0InfoVect0LinkObjId="g_3f1e9e0_0" Pin0InfoVect1LinkObjId="SW-26210_0" Pin0InfoVect2LinkObjId="SW-26212_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f3e990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-704 3779,-721 3751,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f3e730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-721 3751,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3f3e990@0" ObjectIDND1="g_3f1e9e0@0" ObjectIDZND0="4228@x" ObjectIDZND1="4230@x" Pin0InfoVect0LinkObjId="SW-26210_0" Pin0InfoVect1LinkObjId="SW-26212_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f3e990_0" Pin1InfoVect1LinkObjId="g_3f1e9e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3751,-721 3751,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ee8c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-904 4655,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4239@1" ObjectIDZND0="4238@x" ObjectIDZND1="4241@x" Pin0InfoVect0LinkObjId="SW-26255_0" Pin0InfoVect1LinkObjId="SW-26259_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26257_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-904 4655,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ee8ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-921 4655,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4241@x" ObjectIDND1="4239@x" ObjectIDZND0="4238@0" Pin0InfoVect0LinkObjId="SW-26255_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26259_0" Pin1InfoVect1LinkObjId="SW-26257_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-921 4655,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ee9130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-962 4655,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4238@1" ObjectIDZND0="4240@x" ObjectIDZND1="4242@x" Pin0InfoVect0LinkObjId="SW-26258_0" Pin0InfoVect1LinkObjId="SW-26260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26255_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-962 4655,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ee9390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-975 4655,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4238@x" ObjectIDND1="4242@x" ObjectIDZND0="4240@0" Pin0InfoVect0LinkObjId="SW-26258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26255_0" Pin1InfoVect1LinkObjId="SW-26260_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-975 4655,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f56b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-921 4672,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4238@x" ObjectIDND1="4239@x" ObjectIDZND0="4241@0" Pin0InfoVect0LinkObjId="SW-26259_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26255_0" Pin1InfoVect1LinkObjId="SW-26257_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-921 4672,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f56d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4708,-921 4722,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4241@1" ObjectIDZND0="g_3f57700@0" Pin0InfoVect0LinkObjId="g_3f57700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26259_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4708,-921 4722,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f56fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-975 4672,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4238@x" ObjectIDND1="4240@x" ObjectIDZND0="4242@0" Pin0InfoVect0LinkObjId="SW-26260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26255_0" Pin1InfoVect1LinkObjId="SW-26258_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-975 4672,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f57240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4708,-975 4722,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4242@1" ObjectIDZND0="g_3f58190@0" Pin0InfoVect0LinkObjId="g_3f58190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4708,-975 4722,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f574a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-1040 4722,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4243@1" ObjectIDZND0="g_3f58c20@0" Pin0InfoVect0LinkObjId="g_3f58c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26261_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-1040 4722,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f596b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-1025 4655,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4240@1" ObjectIDZND0="g_3f5b040@0" ObjectIDZND1="g_3f5a030@0" ObjectIDZND2="11715@1" Pin0InfoVect0LinkObjId="g_3f5b040_0" Pin0InfoVect1LinkObjId="g_3f5a030_0" Pin0InfoVect2LinkObjId="g_3f1e060_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26258_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-1025 4655,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f59910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-1040 4673,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3f5b040@0" ObjectIDND1="g_3f5a030@0" ObjectIDND2="11715@1" ObjectIDZND0="4243@0" Pin0InfoVect0LinkObjId="SW-26261_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f5b040_0" Pin1InfoVect1LinkObjId="g_3f5a030_0" Pin1InfoVect2LinkObjId="g_3f1e060_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-1040 4673,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f59b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-1108 4768,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f5b040@0" ObjectIDND1="4240@x" ObjectIDND2="4243@x" ObjectIDZND0="g_3f5a030@0" Pin0InfoVect0LinkObjId="g_3f5a030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f5b040_0" Pin1InfoVect1LinkObjId="SW-26258_0" Pin1InfoVect2LinkObjId="SW-26261_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-1108 4768,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f59dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-1108 4798,-1108 4798,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f5a030@0" ObjectIDND1="4240@x" ObjectIDND2="4243@x" ObjectIDZND0="g_3f5b040@0" Pin0InfoVect0LinkObjId="g_3f5b040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f5a030_0" Pin1InfoVect1LinkObjId="SW-26258_0" Pin1InfoVect2LinkObjId="SW-26261_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-1108 4798,-1108 4798,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f5ade0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-855 4655,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDZND0="4239@0" Pin0InfoVect0LinkObjId="SW-26257_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f54c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-855 4655,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f348a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-905 4374,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4233@1" ObjectIDZND0="4232@x" ObjectIDZND1="4235@x" Pin0InfoVect0LinkObjId="SW-26236_0" Pin0InfoVect1LinkObjId="SW-26239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26237_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-905 4374,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f34b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-922 4374,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4235@x" ObjectIDND1="4233@x" ObjectIDZND0="4232@0" Pin0InfoVect0LinkObjId="SW-26236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26239_0" Pin1InfoVect1LinkObjId="SW-26237_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-922 4374,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f34d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-963 4374,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4232@1" ObjectIDZND0="4234@x" ObjectIDZND1="4236@x" Pin0InfoVect0LinkObjId="SW-26238_0" Pin0InfoVect1LinkObjId="SW-26240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-963 4374,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f34fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-976 4374,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4232@x" ObjectIDND1="4236@x" ObjectIDZND0="4234@0" Pin0InfoVect0LinkObjId="SW-26238_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26236_0" Pin1InfoVect1LinkObjId="SW-26240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-976 4374,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f6bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-922 4391,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4232@x" ObjectIDND1="4233@x" ObjectIDZND0="4235@0" Pin0InfoVect0LinkObjId="SW-26239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26236_0" Pin1InfoVect1LinkObjId="SW-26237_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-922 4391,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f6c1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-922 4441,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4235@1" ObjectIDZND0="g_3f6cb50@0" Pin0InfoVect0LinkObjId="g_3f6cb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-922 4441,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f6c430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-976 4391,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4232@x" ObjectIDND1="4234@x" ObjectIDZND0="4236@0" Pin0InfoVect0LinkObjId="SW-26240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26236_0" Pin1InfoVect1LinkObjId="SW-26238_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-976 4391,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f6c690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4427,-976 4441,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4236@1" ObjectIDZND0="g_3f6d5e0@0" Pin0InfoVect0LinkObjId="g_3f6d5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4427,-976 4441,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f6c8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-1041 4441,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4237@1" ObjectIDZND0="g_3f6e070@0" Pin0InfoVect0LinkObjId="g_3f6e070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-1041 4441,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f6eb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-1026 4374,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4234@1" ObjectIDZND0="g_4057640@0" ObjectIDZND1="g_4056630@0" ObjectIDZND2="11716@1" Pin0InfoVect0LinkObjId="g_4057640_0" Pin0InfoVect1LinkObjId="g_4056630_0" Pin0InfoVect2LinkObjId="g_3f1d940_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26238_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-1026 4374,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f6ed60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-1041 4392,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_4057640@0" ObjectIDND1="g_4056630@0" ObjectIDND2="11716@1" ObjectIDZND0="4237@0" Pin0InfoVect0LinkObjId="SW-26241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4057640_0" Pin1InfoVect1LinkObjId="g_4056630_0" Pin1InfoVect2LinkObjId="g_3f1d940_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-1041 4392,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f6efc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4487,-1109 4487,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_4057640@0" ObjectIDND1="4234@x" ObjectIDND2="4237@x" ObjectIDZND0="g_4056630@0" Pin0InfoVect0LinkObjId="g_4056630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4057640_0" Pin1InfoVect1LinkObjId="SW-26238_0" Pin1InfoVect2LinkObjId="SW-26241_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4487,-1109 4487,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40563d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4487,-1109 4517,-1109 4517,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_4056630@0" ObjectIDND1="4234@x" ObjectIDND2="4237@x" ObjectIDZND0="g_4057640@0" Pin0InfoVect0LinkObjId="g_4057640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4056630_0" Pin1InfoVect1LinkObjId="SW-26238_0" Pin1InfoVect2LinkObjId="SW-26241_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4487,-1109 4517,-1109 4517,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40573e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-855 4374,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDZND0="4233@0" Pin0InfoVect0LinkObjId="SW-26237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f54c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-855 4374,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_405bcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-855 3944,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDZND0="4245@1" Pin0InfoVect0LinkObjId="SW-26288_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-855 3944,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ebcb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-671 3944,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="4247@0" Pin0InfoVect0LinkObjId="SW-26290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-671 3944,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ebcda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-787 3961,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4244@x" ObjectIDND1="4245@x" ObjectIDZND0="4246@0" Pin0InfoVect0LinkObjId="SW-26289_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26286_0" Pin1InfoVect1LinkObjId="SW-26288_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-787 3961,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ebd000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-787 4011,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4246@1" ObjectIDZND0="g_3ebd260@0" Pin0InfoVect0LinkObjId="g_3ebd260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26289_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-787 4011,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec0220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-772 3944,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4244@1" ObjectIDZND0="4245@x" ObjectIDZND1="4246@x" Pin0InfoVect0LinkObjId="SW-26288_0" Pin0InfoVect1LinkObjId="SW-26289_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-772 3944,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec0480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-787 3944,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4244@x" ObjectIDND1="4246@x" ObjectIDZND0="4245@0" Pin0InfoVect0LinkObjId="SW-26288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26286_0" Pin1InfoVect1LinkObjId="SW-26289_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-787 3944,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec06e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-735 3961,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4244@x" ObjectIDND1="4247@x" ObjectIDZND0="4248@0" Pin0InfoVect0LinkObjId="SW-26291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26286_0" Pin1InfoVect1LinkObjId="SW-26290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-735 3961,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ec0940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-735 4011,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4248@1" ObjectIDZND0="g_3ec0ba0@0" Pin0InfoVect0LinkObjId="g_3ec0ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-735 4011,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4004f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-722 3944,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4247@1" ObjectIDZND0="4244@x" ObjectIDZND1="4248@x" Pin0InfoVect0LinkObjId="SW-26286_0" Pin0InfoVect1LinkObjId="SW-26291_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-722 3944,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4005170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-735 3944,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4247@x" ObjectIDND1="4248@x" ObjectIDZND0="4244@0" Pin0InfoVect0LinkObjId="SW-26286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26290_0" Pin1InfoVect1LinkObjId="SW-26291_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-735 3944,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f5d4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-784 4508,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4257@x" ObjectIDND1="4258@x" ObjectIDZND0="4259@0" Pin0InfoVect0LinkObjId="SW-26427_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26422_0" Pin1InfoVect1LinkObjId="SW-26426_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-784 4508,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f5d710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4544,-784 4558,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4259@1" ObjectIDZND0="g_3f5d970@0" Pin0InfoVect0LinkObjId="g_3f5d970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26427_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4544,-784 4558,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f607c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-736 4508,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4257@x" ObjectIDND1="4260@x" ObjectIDZND0="4261@0" Pin0InfoVect0LinkObjId="SW-26429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26422_0" Pin1InfoVect1LinkObjId="SW-26428_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-736 4508,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f60a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4544,-736 4558,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4261@1" ObjectIDZND0="g_3f60c80@0" Pin0InfoVect0LinkObjId="g_3f60c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4544,-736 4558,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f66170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-498 4531,-498 4531,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="4267@1" ObjectIDZND0="4312@x" Pin0InfoVect0LinkObjId="g_4031150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-498 4531,-498 4531,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f663d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-498 4275,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f4b510@0" ObjectIDND1="4267@x" ObjectIDZND0="g_3f66630@0" Pin0InfoVect0LinkObjId="g_3f66630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f4b510_0" Pin1InfoVect1LinkObjId="SW-26440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-498 4275,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f91180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-498 4175,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f66630@0" ObjectIDND1="4267@x" ObjectIDZND0="g_3f4b510@0" Pin0InfoVect0LinkObjId="g_3f4b510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f66630_0" Pin1InfoVect1LinkObjId="SW-26440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-498 4175,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f97c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4707,-612 4735,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4266@1" ObjectIDZND0="4264@1" Pin0InfoVect0LinkObjId="SW-26435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26439_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4707,-612 4735,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4016200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-352 5021,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26327@0" ObjectIDZND0="26273@0" Pin0InfoVect0LinkObjId="SW-155720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-352 5021,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4016460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5057,-352 5093,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26273@1" ObjectIDZND0="26272@1" Pin0InfoVect0LinkObjId="SW-155719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5057,-352 5093,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40166c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-352 5143,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26272@0" ObjectIDZND0="26274@0" Pin0InfoVect0LinkObjId="SW-155721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-352 5143,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4016920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5179,-352 5382,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="26274@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5179,-352 5382,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4017930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-417 4930,-417 4930,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26327@0" ObjectIDZND0="26326@0" Pin0InfoVect0LinkObjId="SW-155935_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-417 4930,-417 4930,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4017b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-469 4930,-486 4979,-486 4977,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26326@1" ObjectIDZND0="26243@0" Pin0InfoVect0LinkObjId="g_3f03120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155935_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-469 4930,-486 4979,-486 4977,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_401e670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-983 5006,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26243@0" ObjectIDZND0="26259@0" Pin0InfoVect0LinkObjId="SW-155664_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4017b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-983 5006,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_401e8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-983 5152,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26258@0" ObjectIDZND0="26260@0" Pin0InfoVect0LinkObjId="SW-155665_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155663_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-983 5152,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_401eb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5188,-983 5202,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="26260@1" ObjectIDZND0="26261@x" Pin0InfoVect0LinkObjId="SW-155666_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5188,-983 5202,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_401ed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5202,-983 5397,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="26261@x" ObjectIDND1="26260@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155666_0" Pin1InfoVect1LinkObjId="SW-155665_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5202,-983 5397,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_401eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5202,-983 5202,-957 5216,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="26260@x" ObjectIDZND0="26261@0" Pin0InfoVect0LinkObjId="SW-155666_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5202,-983 5202,-957 5216,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec5970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5302,-957 5328,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3ec50f0@0" ObjectIDZND0="g_3ec5bd0@0" Pin0InfoVect0LinkObjId="g_3ec5bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ec50f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5302,-957 5328,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eccb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-785 5039,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26243@0" ObjectIDZND0="26248@0" Pin0InfoVect0LinkObjId="SW-155592_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4017b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-785 5039,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eccda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-785 5102,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26248@1" ObjectIDZND0="26247@1" Pin0InfoVect0LinkObjId="SW-155591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155592_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-785 5102,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ecd000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-785 5152,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26247@0" ObjectIDZND0="26249@0" Pin0InfoVect0LinkObjId="SW-155593_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-785 5152,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ecd260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5188,-785 5202,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26249@1" ObjectIDZND0="g_3ed0ba0@0" ObjectIDZND1="g_3f19440@0" ObjectIDZND2="26250@x" Pin0InfoVect0LinkObjId="g_3ed0ba0_0" Pin0InfoVect1LinkObjId="g_3f19440_0" Pin0InfoVect2LinkObjId="SW-155594_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155593_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5188,-785 5202,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ecd4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5202,-785 5397,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_3ed0ba0@0" ObjectIDND1="g_3f19440@0" ObjectIDND2="26249@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ed0ba0_0" Pin1InfoVect1LinkObjId="g_3f19440_0" Pin1InfoVect2LinkObjId="SW-155593_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5202,-785 5397,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ecfc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5202,-785 5202,-811 5216,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3ed0ba0@0" ObjectIDND1="g_3f19440@0" ObjectIDND2="26249@x" ObjectIDZND0="26250@0" Pin0InfoVect0LinkObjId="SW-155594_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ed0ba0_0" Pin1InfoVect1LinkObjId="g_3f19440_0" Pin1InfoVect2LinkObjId="SW-155593_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5202,-785 5202,-811 5216,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ecfeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5252,-811 5264,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26250@1" ObjectIDZND0="g_3ed0110@0" Pin0InfoVect0LinkObjId="g_3ed0110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155594_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5252,-811 5264,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3efd100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-608 5031,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26243@0" ObjectIDZND0="26252@0" Pin0InfoVect0LinkObjId="SW-155612_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4017b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-608 5031,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3efe2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5190,-558 5173,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3efdbe0@0" ObjectIDZND0="g_3efd360@0" Pin0InfoVect0LinkObjId="g_3efd360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3efdbe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5190,-558 5173,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3efe540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5142,-558 5131,-558 5131,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3efd360@1" ObjectIDZND0="26251@x" ObjectIDZND1="26256@x" ObjectIDZND2="26253@x" Pin0InfoVect0LinkObjId="SW-155611_0" Pin0InfoVect1LinkObjId="SW-155616_0" Pin0InfoVect2LinkObjId="SW-155613_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3efd360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5142,-558 5131,-558 5131,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f01c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5370,-572 5355,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3f01550@0" ObjectIDZND0="g_3f00cd0@0" Pin0InfoVect0LinkObjId="g_3f00cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f01550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5370,-572 5355,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f01eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5324,-572 5314,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3f00cd0@1" ObjectIDZND0="26254@1" Pin0InfoVect0LinkObjId="SW-155614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f00cd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5324,-572 5314,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f02110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5256,-759 5241,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3ed1420@0" ObjectIDZND0="g_3ed0ba0@0" Pin0InfoVect0LinkObjId="g_3ed0ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ed1420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5256,-759 5241,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f03120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-960 4977,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26275@1" ObjectIDZND0="26243@0" Pin0InfoVect0LinkObjId="g_4017b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155734_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-960 4977,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f756c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5124,-233 5147,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26267@0" ObjectIDZND0="26269@0" Pin0InfoVect0LinkObjId="SW-155702_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5124,-233 5147,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f75920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5183,-233 5197,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="26269@1" ObjectIDZND0="g_3f1c040@0" ObjectIDZND1="26271@x" ObjectIDZND2="26270@x" Pin0InfoVect0LinkObjId="g_3f1c040_0" Pin0InfoVect1LinkObjId="SW-155704_0" Pin0InfoVect2LinkObjId="SW-155703_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155702_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5183,-233 5197,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f7b8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5297,-207 5323,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3f7b070@0" ObjectIDZND0="g_3f7bb50@0" Pin0InfoVect0LinkObjId="g_3f7bb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f7b070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5297,-207 5323,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f80f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-1010 4899,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26276@1" ObjectIDZND0="g_3f811e0@0" Pin0InfoVect0LinkObjId="g_3f811e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155735_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-1010 4899,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f83260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4813,-960 4848,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3f169d0@0" ObjectIDZND0="g_3f829e0@1" Pin0InfoVect0LinkObjId="g_3f829e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f169d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4813,-960 4848,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f834c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4813,-744 4830,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3f861a0@0" ObjectIDND1="g_3f20ea0@0" ObjectIDND2="4304@x" ObjectIDZND0="4306@0" Pin0InfoVect0LinkObjId="SW-26677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f861a0_0" Pin1InfoVect1LinkObjId="g_3f20ea0_0" Pin1InfoVect2LinkObjId="SW-26675_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4813,-744 4830,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f83720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4866,-744 4880,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4306@1" ObjectIDZND0="g_3f83980@0" Pin0InfoVect0LinkObjId="g_3f83980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4866,-744 4880,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f84410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4813,-818 4830,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDND1="4304@x" ObjectIDZND0="4305@0" Pin0InfoVect0LinkObjId="SW-26676_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f54c60_0" Pin1InfoVect1LinkObjId="SW-26675_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4813,-818 4830,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f84670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4866,-818 4880,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4305@1" ObjectIDZND0="g_3f848d0@0" Pin0InfoVect0LinkObjId="g_3f848d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4866,-818 4880,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f85360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-759 4812,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="4304@0" ObjectIDZND0="g_3f861a0@0" ObjectIDZND1="g_3f20ea0@0" ObjectIDZND2="4306@x" Pin0InfoVect0LinkObjId="g_3f861a0_0" Pin0InfoVect1LinkObjId="g_3f20ea0_0" Pin0InfoVect2LinkObjId="SW-26677_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-759 4812,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f855c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-855 4812,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4213@0" ObjectIDZND0="4304@x" ObjectIDZND1="4305@x" Pin0InfoVect0LinkObjId="SW-26675_0" Pin0InfoVect1LinkObjId="SW-26676_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f54c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-855 4812,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f85820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-818 4812,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDND1="4305@x" ObjectIDZND0="4304@1" Pin0InfoVect0LinkObjId="SW-26675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f54c60_0" Pin1InfoVect1LinkObjId="SW-26676_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-818 4812,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f85a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-720 4788,-720 4788,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3f861a0@0" ObjectIDND1="4304@x" ObjectIDND2="4306@x" ObjectIDZND0="g_3f20ea0@0" Pin0InfoVect0LinkObjId="g_3f20ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f861a0_0" Pin1InfoVect1LinkObjId="SW-26675_0" Pin1InfoVect2LinkObjId="SW-26677_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-720 4788,-720 4788,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f85ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-703 4840,-720 4812,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3f861a0@0" ObjectIDZND0="g_3f20ea0@0" ObjectIDZND1="4304@x" ObjectIDZND2="4306@x" Pin0InfoVect0LinkObjId="g_3f20ea0_0" Pin0InfoVect1LinkObjId="SW-26675_0" Pin0InfoVect2LinkObjId="SW-26677_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f861a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-703 4840,-720 4812,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f85f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-720 4812,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3f861a0@0" ObjectIDND1="g_3f20ea0@0" ObjectIDZND0="4304@x" ObjectIDZND1="4306@x" Pin0InfoVect0LinkObjId="SW-26675_0" Pin0InfoVect1LinkObjId="SW-26677_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f861a0_0" Pin1InfoVect1LinkObjId="g_3f20ea0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-720 4812,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f4b320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-498 4347,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3f66630@0" ObjectIDND1="g_3f4b510@0" ObjectIDZND0="4267@0" Pin0InfoVect0LinkObjId="SW-26440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f66630_0" Pin1InfoVect1LinkObjId="g_3f4b510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-498 4347,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f4ca80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-498 4175,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="g_3f4b510@0" ObjectIDZND1="g_3f66630@0" ObjectIDZND2="4267@x" Pin0InfoVect0LinkObjId="g_3f4b510_0" Pin0InfoVect1LinkObjId="g_3f66630_0" Pin0InfoVect2LinkObjId="SW-26440_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-498 4175,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f4cce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-498 4175,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f66630@0" ObjectIDND1="4267@x" ObjectIDZND0="g_3f4b510@0" Pin0InfoVect0LinkObjId="g_3f4b510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f66630_0" Pin1InfoVect1LinkObjId="SW-26440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-498 4175,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f4dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-675 4508,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="4260@x" ObjectIDND1="4312@x" ObjectIDZND0="4262@0" Pin0InfoVect0LinkObjId="SW-26430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26428_0" Pin1InfoVect1LinkObjId="g_3f66170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-675 4508,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f4df20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4544,-675 4558,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4262@1" ObjectIDZND0="g_3f4e110@0" Pin0InfoVect0LinkObjId="g_3f4e110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4544,-675 4558,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f54c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-832 4490,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4258@1" ObjectIDZND0="4213@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-832 4490,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f54e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-784 4490,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4257@x" ObjectIDND1="4259@x" ObjectIDZND0="4258@0" Pin0InfoVect0LinkObjId="SW-26426_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26422_0" Pin1InfoVect1LinkObjId="SW-26427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-784 4490,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f55040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-784 4490,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4258@x" ObjectIDND1="4259@x" ObjectIDZND0="4257@1" Pin0InfoVect0LinkObjId="SW-26422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26426_0" Pin1InfoVect1LinkObjId="SW-26427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-784 4490,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f55230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-736 4490,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4260@x" ObjectIDND1="4261@x" ObjectIDZND0="4257@0" Pin0InfoVect0LinkObjId="SW-26422_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26428_0" Pin1InfoVect1LinkObjId="SW-26429_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-736 4490,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f55460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-724 4490,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4260@1" ObjectIDZND0="4257@x" ObjectIDZND1="4261@x" Pin0InfoVect0LinkObjId="SW-26422_0" Pin0InfoVect1LinkObjId="SW-26429_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26428_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-724 4490,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-233 5034,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26327@0" ObjectIDZND0="26268@0" Pin0InfoVect0LinkObjId="SW-155701_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-233 5034,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0e3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5070,-233 5097,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26268@1" ObjectIDZND0="26267@1" Pin0InfoVect0LinkObjId="SW-155700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155701_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5070,-233 5097,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5197,-233 5197,-259 5211,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3f1c040@0" ObjectIDND1="26269@x" ObjectIDND2="26271@x" ObjectIDZND0="26270@0" Pin0InfoVect0LinkObjId="SW-155703_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f1c040_0" Pin1InfoVect1LinkObjId="SW-155702_0" Pin1InfoVect2LinkObjId="SW-155704_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5197,-233 5197,-259 5211,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0e7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5247,-259 5259,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26270@1" ObjectIDZND0="g_3f780b0@0" Pin0InfoVect0LinkObjId="g_3f780b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155703_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5247,-259 5259,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0e9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5197,-233 5197,-207 5214,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3f1c040@0" ObjectIDND1="26269@x" ObjectIDND2="26270@x" ObjectIDZND0="26271@0" Pin0InfoVect0LinkObjId="SW-155704_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f1c040_0" Pin1InfoVect1LinkObjId="SW-155702_0" Pin1InfoVect2LinkObjId="SW-155703_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5197,-233 5197,-207 5214,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f12dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-960 4899,-960 4899,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="26275@0" ObjectIDZND0="26276@0" Pin0InfoVect0LinkObjId="SW-155735_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-960 4899,-960 4899,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f13030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-930 4899,-930 4899,-960 4879,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3f81c30@0" ObjectIDZND0="g_3f829e0@0" Pin0InfoVect0LinkObjId="g_3f829e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f81c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-930 4899,-930 4899,-960 4879,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f132a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-628 4390,-635 4420,-635 4420,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="4263@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-628 4390,-635 4420,-635 4420,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f18ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5262,-927 5262,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3f02370@0" ObjectIDZND0="g_3ec50f0@0" ObjectIDZND1="26261@x" Pin0InfoVect0LinkObjId="g_3ec50f0_0" Pin0InfoVect1LinkObjId="SW-155666_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f02370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5262,-927 5262,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f18d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5252,-957 5262,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26261@1" ObjectIDZND0="g_3f02370@0" ObjectIDZND1="g_3ec50f0@0" Pin0InfoVect0LinkObjId="g_3f02370_0" Pin0InfoVect1LinkObjId="g_3ec50f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5252,-957 5262,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f18f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5262,-957 5271,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f02370@0" ObjectIDND1="26261@x" ObjectIDZND0="g_3ec50f0@1" Pin0InfoVect0LinkObjId="g_3ec50f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f02370_0" Pin1InfoVect1LinkObjId="SW-155666_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5262,-957 5271,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f191e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5271,-608 5271,-572 5278,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26257@x" ObjectIDND1="26253@x" ObjectIDZND0="26254@0" Pin0InfoVect0LinkObjId="SW-155614_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155617_0" Pin1InfoVect1LinkObjId="SW-155613_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5271,-608 5271,-572 5278,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1a1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5202,-759 5202,-733 5205,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3ed0ba0@0" ObjectIDND1="26249@x" ObjectIDND2="26250@x" ObjectIDZND0="g_3f19440@0" Pin0InfoVect0LinkObjId="g_3f19440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ed0ba0_0" Pin1InfoVect1LinkObjId="SW-155593_0" Pin1InfoVect2LinkObjId="SW-155594_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5202,-759 5202,-733 5205,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1a450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5210,-759 5202,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3ed0ba0@1" ObjectIDZND0="g_3f19440@0" ObjectIDZND1="26249@x" ObjectIDZND2="26250@x" Pin0InfoVect0LinkObjId="g_3f19440_0" Pin0InfoVect1LinkObjId="SW-155593_0" Pin0InfoVect2LinkObjId="SW-155594_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ed0ba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5210,-759 5202,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1a6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5202,-759 5202,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3ed0ba0@0" ObjectIDND1="g_3f19440@0" ObjectIDZND0="26249@x" ObjectIDZND1="26250@x" Pin0InfoVect0LinkObjId="SW-155593_0" Pin0InfoVect1LinkObjId="SW-155594_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ed0ba0_0" Pin1InfoVect1LinkObjId="g_3f19440_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5202,-759 5202,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1a910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-608 5131,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="26251@0" ObjectIDZND0="g_3efd360@0" ObjectIDZND1="26256@x" ObjectIDZND2="26253@x" Pin0InfoVect0LinkObjId="g_3efd360_0" Pin0InfoVect1LinkObjId="SW-155616_0" Pin0InfoVect2LinkObjId="SW-155613_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155611_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-608 5131,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1b920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-174 5258,-174 5258,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3f1ab70@0" ObjectIDZND0="g_3f7b070@0" ObjectIDZND1="26271@x" Pin0InfoVect0LinkObjId="g_3f7b070_0" Pin0InfoVect1LinkObjId="SW-155704_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f1ab70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-174 5258,-174 5258,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1bb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5250,-207 5258,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26271@1" ObjectIDZND0="g_3f1ab70@0" ObjectIDZND1="g_3f7b070@0" Pin0InfoVect0LinkObjId="g_3f1ab70_0" Pin0InfoVect1LinkObjId="g_3f7b070_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155704_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5250,-207 5258,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1bde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5258,-207 5266,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f1ab70@0" ObjectIDND1="26271@x" ObjectIDZND0="g_3f7b070@1" Pin0InfoVect0LinkObjId="g_3f7b070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f1ab70_0" Pin1InfoVect1LinkObjId="SW-155704_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5258,-207 5266,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5197,-233 5245,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="26269@x" ObjectIDND1="26271@x" ObjectIDND2="26270@x" ObjectIDZND0="g_3f1c040@1" Pin0InfoVect0LinkObjId="g_3f1c040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155702_0" Pin1InfoVect1LinkObjId="SW-155704_0" Pin1InfoVect2LinkObjId="SW-155703_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5197,-233 5245,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5320,-233 5383,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3f1c040@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f1c040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5320,-233 5383,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1cf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1108 3737,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4218@x" ObjectIDND1="4221@x" ObjectIDND2="g_3f717a0@0" ObjectIDZND0="11550@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26186_0" Pin1InfoVect1LinkObjId="SW-26189_0" Pin1InfoVect2LinkObjId="g_3f717a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1108 3737,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1d1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1040 3737,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4218@x" ObjectIDND1="4221@x" ObjectIDZND0="g_3f717a0@0" ObjectIDZND1="g_3edb460@0" ObjectIDZND2="11550@1" Pin0InfoVect0LinkObjId="g_3f717a0_0" Pin0InfoVect1LinkObjId="g_3edb460_0" Pin0InfoVect2LinkObjId="g_3f1cf80_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26186_0" Pin1InfoVect1LinkObjId="SW-26189_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1040 3737,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1108 3850,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4218@x" ObjectIDND1="4221@x" ObjectIDND2="11550@1" ObjectIDZND0="g_3f717a0@0" ObjectIDZND1="g_3edb460@0" Pin0InfoVect0LinkObjId="g_3f717a0_0" Pin0InfoVect1LinkObjId="g_3edb460_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26186_0" Pin1InfoVect1LinkObjId="SW-26189_0" Pin1InfoVect2LinkObjId="g_3f1cf80_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1108 3850,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1d940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-1108 4374,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4234@x" ObjectIDND1="4237@x" ObjectIDND2="g_4057640@0" ObjectIDZND0="11716@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26238_0" Pin1InfoVect1LinkObjId="SW-26241_0" Pin1InfoVect2LinkObjId="g_4057640_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-1108 4374,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1dba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-1041 4374,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4234@x" ObjectIDND1="4237@x" ObjectIDZND0="g_4057640@0" ObjectIDZND1="g_4056630@0" ObjectIDZND2="11716@1" Pin0InfoVect0LinkObjId="g_4057640_0" Pin0InfoVect1LinkObjId="g_4056630_0" Pin0InfoVect2LinkObjId="g_3f1d940_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26238_0" Pin1InfoVect1LinkObjId="SW-26241_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-1041 4374,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1de00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-1109 4487,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4234@x" ObjectIDND1="4237@x" ObjectIDND2="11716@1" ObjectIDZND0="g_4057640@0" ObjectIDZND1="g_4056630@0" Pin0InfoVect0LinkObjId="g_4057640_0" Pin0InfoVect1LinkObjId="g_4056630_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26238_0" Pin1InfoVect1LinkObjId="SW-26241_0" Pin1InfoVect2LinkObjId="g_3f1d940_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-1109 4487,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1e060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-1109 4655,-1142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4240@x" ObjectIDND1="4243@x" ObjectIDND2="g_3f5b040@0" ObjectIDZND0="11715@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26258_0" Pin1InfoVect1LinkObjId="SW-26261_0" Pin1InfoVect2LinkObjId="g_3f5b040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-1109 4655,-1142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1e2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-1040 4655,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4240@x" ObjectIDND1="4243@x" ObjectIDZND0="g_3f5b040@0" ObjectIDZND1="g_3f5a030@0" ObjectIDZND2="11715@1" Pin0InfoVect0LinkObjId="g_3f5b040_0" Pin0InfoVect1LinkObjId="g_3f5a030_0" Pin0InfoVect2LinkObjId="g_3f1e060_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26258_0" Pin1InfoVect1LinkObjId="SW-26261_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-1040 4655,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1e520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-1108 4768,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4240@x" ObjectIDND1="4243@x" ObjectIDND2="11715@1" ObjectIDZND0="g_3f5b040@0" ObjectIDZND1="g_3f5a030@0" Pin0InfoVect0LinkObjId="g_3f5b040_0" Pin0InfoVect1LinkObjId="g_3f5a030_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26258_0" Pin1InfoVect1LinkObjId="SW-26261_0" Pin1InfoVect2LinkObjId="g_3f1e060_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-1108 4768,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1e780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-572 5313,-547 5322,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_4016b80@0" Pin0InfoVect0LinkObjId="g_4016b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-572 5313,-547 5322,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f23360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-635 4447,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="g_4005f80@0" ObjectIDZND0="g_40053d0@0" ObjectIDZND1="4312@x" Pin0InfoVect0LinkObjId="g_40053d0_0" Pin0InfoVect1LinkObjId="g_3f66170_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4005f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-635 4447,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f235c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4447,-635 4447,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="g_4005f80@0" ObjectIDND1="4312@x" ObjectIDZND0="g_40053d0@0" Pin0InfoVect0LinkObjId="g_40053d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4005f80_0" Pin1InfoVect1LinkObjId="g_3f66170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4447,-635 4447,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4031150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4447,-635 4493,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer" ObjectIDND0="g_4005f80@0" ObjectIDND1="g_40053d0@0" ObjectIDZND0="4312@x" Pin0InfoVect0LinkObjId="g_3f66170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4005f80_0" Pin1InfoVect1LinkObjId="g_40053d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4447,-635 4493,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4031ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-688 4490,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="4260@0" ObjectIDZND0="4262@x" ObjectIDZND1="4312@x" Pin0InfoVect0LinkObjId="SW-26430_0" Pin0InfoVect1LinkObjId="g_3f66170_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-688 4490,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4031cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-655 4490,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4312@1" ObjectIDZND0="4260@x" ObjectIDZND1="4262@x" Pin0InfoVect0LinkObjId="SW-26428_0" Pin0InfoVect1LinkObjId="SW-26430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f66170_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-655 4490,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4039290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-1108 3967,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4225@x" ObjectIDND1="4227@x" ObjectIDND2="g_3f8ee20@0" ObjectIDZND0="9192@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26207_0" Pin1InfoVect1LinkObjId="SW-26209_0" Pin1InfoVect2LinkObjId="g_3f8ee20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-1108 3967,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4039d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-1040 3967,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4225@x" ObjectIDND1="4227@x" ObjectIDZND0="g_3f8ee20@0" ObjectIDZND1="g_3f8de50@0" ObjectIDZND2="9192@1" Pin0InfoVect0LinkObjId="g_3f8ee20_0" Pin0InfoVect1LinkObjId="g_3f8de50_0" Pin0InfoVect2LinkObjId="g_4039290_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26207_0" Pin1InfoVect1LinkObjId="SW-26209_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-1040 3967,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4039fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-1108 4080,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4225@x" ObjectIDND1="4227@x" ObjectIDND2="9192@1" ObjectIDZND0="g_3f8ee20@0" ObjectIDZND1="g_3f8de50@0" Pin0InfoVect0LinkObjId="g_3f8ee20_0" Pin0InfoVect1LinkObjId="g_3f8de50_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26207_0" Pin1InfoVect1LinkObjId="SW-26209_0" Pin1InfoVect2LinkObjId="g_4039290_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-1108 4080,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_403dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-983 5102,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26259@1" ObjectIDZND0="26258@1" Pin0InfoVect0LinkObjId="SW-155663_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155664_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-983 5102,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4040190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5243,-606 5243,-632 5257,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26254@x" ObjectIDND1="26253@x" ObjectIDZND0="26257@0" Pin0InfoVect0LinkObjId="SW-155617_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155614_0" Pin1InfoVect1LinkObjId="SW-155613_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5243,-606 5243,-632 5257,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40403f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5293,-632 5305,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26257@1" ObjectIDZND0="g_4040650@0" Pin0InfoVect0LinkObjId="g_4040650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155617_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5293,-632 5305,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40410e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5213,-608 5243,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26253@1" ObjectIDZND0="26257@x" ObjectIDZND1="26254@x" Pin0InfoVect0LinkObjId="SW-155617_0" Pin0InfoVect1LinkObjId="SW-155614_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155613_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5213,-608 5243,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4041bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5390,-608 5271,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="26254@x" ObjectIDZND1="26257@x" ObjectIDZND2="26253@x" Pin0InfoVect0LinkObjId="SW-155614_0" Pin0InfoVect1LinkObjId="SW-155617_0" Pin0InfoVect2LinkObjId="SW-155613_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5390,-608 5271,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4041e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5271,-608 5243,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26254@x" ObjectIDZND0="26257@x" ObjectIDZND1="26253@x" Pin0InfoVect0LinkObjId="SW-155617_0" Pin0InfoVect1LinkObjId="SW-155613_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155614_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5271,-608 5243,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_404e440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-666 5141,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26255@1" ObjectIDZND0="g_404e6a0@0" Pin0InfoVect0LinkObjId="g_404e6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155615_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-666 5141,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_404f620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5093,-666 5080,-666 5080,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26255@0" ObjectIDZND0="26252@x" ObjectIDZND1="26251@x" Pin0InfoVect0LinkObjId="SW-155612_0" Pin0InfoVect1LinkObjId="SW-155611_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155615_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5093,-666 5080,-666 5080,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_404ff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5068,-608 5080,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26252@1" ObjectIDZND0="26255@x" ObjectIDZND1="26251@x" Pin0InfoVect0LinkObjId="SW-155615_0" Pin0InfoVect1LinkObjId="SW-155611_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5068,-608 5080,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4050180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5080,-608 5095,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26255@x" ObjectIDND1="26252@x" ObjectIDZND0="26251@1" Pin0InfoVect0LinkObjId="SW-155611_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155615_0" Pin1InfoVect1LinkObjId="SW-155612_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5080,-608 5095,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4052850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-655 5219,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26256@1" ObjectIDZND0="g_4052ab0@0" Pin0InfoVect0LinkObjId="g_4052ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155616_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-655 5219,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4053a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5171,-655 5155,-655 5155,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26256@0" ObjectIDZND0="26251@x" ObjectIDZND1="g_3efd360@0" ObjectIDZND2="26253@x" Pin0InfoVect0LinkObjId="SW-155611_0" Pin0InfoVect1LinkObjId="g_3efd360_0" Pin0InfoVect2LinkObjId="SW-155613_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-155616_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5171,-655 5155,-655 5155,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40543a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5131,-608 5155,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26251@x" ObjectIDND1="g_3efd360@0" ObjectIDZND0="26256@x" ObjectIDZND1="26253@x" Pin0InfoVect0LinkObjId="SW-155616_0" Pin0InfoVect1LinkObjId="SW-155613_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-155611_0" Pin1InfoVect1LinkObjId="g_3efd360_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5131,-608 5155,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4054590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5155,-608 5177,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26256@x" ObjectIDND1="26251@x" ObjectIDND2="g_3efd360@0" ObjectIDZND0="26253@0" Pin0InfoVect0LinkObjId="SW-155613_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-155616_0" Pin1InfoVect1LinkObjId="SW-155611_0" Pin1InfoVect2LinkObjId="g_3efd360_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5155,-608 5177,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fb7350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4671,-612 4650,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4266@0" ObjectIDZND0="g_3fb5c20@1" Pin0InfoVect0LinkObjId="g_3fb5c20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4671,-612 4650,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fb7540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4571,-612 4547,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" ObjectIDND0="g_3fb5c20@0" ObjectIDZND0="4312@0" Pin0InfoVect0LinkObjId="g_3f66170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fb5c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4571,-612 4547,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fb7730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-554 4903,-554 4903,-607 4903,-612 4869,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26243@0" ObjectIDZND0="4265@1" Pin0InfoVect0LinkObjId="SW-26438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4017b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-554 4903,-554 4903,-607 4903,-612 4869,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fbd2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-538 4807,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3fbc850@0" ObjectIDZND0="47300@0" Pin0InfoVect0LinkObjId="SW-305435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fbc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-538 4807,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fbd500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-594 4807,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47300@1" ObjectIDZND0="4264@x" ObjectIDZND1="4265@x" Pin0InfoVect0LinkObjId="SW-26435_0" Pin0InfoVect1LinkObjId="SW-26438_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-305435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-594 4807,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fbdff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4762,-612 4807,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4264@0" ObjectIDZND0="47300@x" ObjectIDZND1="4265@x" Pin0InfoVect0LinkObjId="SW-305435_0" Pin0InfoVect1LinkObjId="SW-26438_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4762,-612 4807,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fbe250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-612 4833,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="47300@x" ObjectIDND1="4264@x" ObjectIDZND0="4265@0" Pin0InfoVect0LinkObjId="SW-26438_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-305435_0" Pin1InfoVect1LinkObjId="SW-26435_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-612 4833,-612 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DY"/>
</svg>