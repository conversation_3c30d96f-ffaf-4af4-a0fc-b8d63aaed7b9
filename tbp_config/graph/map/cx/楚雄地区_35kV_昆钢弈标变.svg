<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-42" aopId="786686" id="thSvg" viewBox="3115 -1210 2173 1404">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="16" stroke-width="0.5" width="33" x="5" y="1"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="16" stroke-width="0.5" width="33" x="6" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="16" stroke-width="0.5" width="32" x="6" y="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="6,2 38,18 " stroke-width="0.509653"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="38,2 6,18 " stroke-width="0.5"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <rect height="16" stroke-width="0.5" width="32" x="6" y="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="6,2 38,18 " stroke-width="0.509653"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="38,2 6,18 " stroke-width="0.5"/>
   </symbol>
   <symbol id="capacitor:shape14">
    <polyline arcFlag="1" fill="none" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="50" x2="47" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="41" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <polyline arcFlag="1" fill="none" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <polyline arcFlag="1" fill="none" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" fill="none" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape111">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="53" x2="60" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="47" x2="65" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="54" x2="58" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="56" y1="28" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="56" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="94" y2="65"/>
    <rect height="31" stroke-width="1" width="16" x="5" y="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="59" y2="105"/>
    <circle cx="13" cy="46" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="26" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="22" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="50" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="42" y2="46"/>
    <ellipse cx="13" cy="27" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="lightningRod:shape95">
    <ellipse cx="20" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="34" x2="29" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="34" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="31" x2="29" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="21" x2="21" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="9" x2="0" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="6" x2="3" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="7" x2="2" y1="4" y2="4"/>
    <polyline fill="none" points="21,19 5,19 5,6 " stroke-width="1"/>
    <ellipse cx="31" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="20" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="31" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline fill="none" points="64,100 1,37 " stroke-width="1"/>
    <polyline fill="none" points="58,100 64,100 " stroke-width="1"/>
    <polyline fill="none" points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="voltageTransformer:shape36">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline fill="none" points="19,9 35,9 35,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b2090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b2970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b32b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b4500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b57f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b6490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b7030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b7a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b82a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b8c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b9870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21ba080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21ba660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bb000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bb880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21bbfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bd760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21be460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bed20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21bf710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c08f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c1270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c1d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c70e0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c7e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c3a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c4f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21d44c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21c9ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1414" width="2183" x="3110" y="-1215"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4327" x2="4339" y1="-1156" y2="-1144"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,215,0)" stroke-width="1" x1="4327" x2="4340" y1="-1143" y2="-1156"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4272" x2="4284" y1="-850" y2="-838"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,215,0)" stroke-width="1" x1="4272" x2="4285" y1="-837" y2="-850"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1076"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1196"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46895">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 -839.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8342" ObjectName="SW-CX_KGYB.CX_KGYB_3711SW"/>
     <cge:Meas_Ref ObjectId="46895"/>
    <cge:TPSR_Ref TObjectID="8342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46892">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 -951.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8339" ObjectName="SW-CX_KGYB.CX_KGYB_3716SW"/>
     <cge:Meas_Ref ObjectId="46892"/>
    <cge:TPSR_Ref TObjectID="8339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46896">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4080.000000 -824.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8343" ObjectName="SW-CX_KGYB.CX_KGYB_3901SW"/>
     <cge:Meas_Ref ObjectId="46896"/>
    <cge:TPSR_Ref TObjectID="8343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56948">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -743.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10516" ObjectName="SW-CX_KGYB.CX_KGYB_3011SW"/>
     <cge:Meas_Ref ObjectId="56948"/>
    <cge:TPSR_Ref TObjectID="10516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46996">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -654.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8382" ObjectName="SW-CX_KGYB.CX_KGYB_3016SW"/>
     <cge:Meas_Ref ObjectId="46996"/>
    <cge:TPSR_Ref TObjectID="8382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46897">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 -741.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8344" ObjectName="SW-CX_KGYB.CX_KGYB_3031SW"/>
     <cge:Meas_Ref ObjectId="46897"/>
    <cge:TPSR_Ref TObjectID="8344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3538.000000 -40.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3538.000000 158.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46894">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 -817.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8341" ObjectName="SW-CX_KGYB.CX_KGYB_37117SW"/>
     <cge:Meas_Ref ObjectId="46894"/>
    <cge:TPSR_Ref TObjectID="8341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47029">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3767.000000 -395.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8387" ObjectName="SW-CX_KGYB.CX_KGYB_001XC"/>
     <cge:Meas_Ref ObjectId="47029"/>
    <cge:TPSR_Ref TObjectID="8387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47029">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3767.000000 -321.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8388" ObjectName="SW-CX_KGYB.CX_KGYB_001XC1"/>
     <cge:Meas_Ref ObjectId="47029"/>
    <cge:TPSR_Ref TObjectID="8388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 -272.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8375" ObjectName="SW-CX_KGYB.CX_KGYB_071XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 -198.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8376" ObjectName="SW-CX_KGYB.CX_KGYB_071XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3637.000000 -322.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3579.000000 -165.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8377" ObjectName="SW-CX_KGYB.CX_KGYB_07117SW"/>
     <cge:Meas_Ref ObjectId="46968"/>
    <cge:TPSR_Ref TObjectID="8377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46910">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3725.000000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8350" ObjectName="SW-CX_KGYB.CX_KGYB_07267SW"/>
     <cge:Meas_Ref ObjectId="46910"/>
    <cge:TPSR_Ref TObjectID="8350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3696.000000 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8348" ObjectName="SW-CX_KGYB.CX_KGYB_072XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3696.000000 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8349" ObjectName="SW-CX_KGYB.CX_KGYB_072XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3859.090909 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8353" ObjectName="SW-CX_KGYB.CX_KGYB_07367SW"/>
     <cge:Meas_Ref ObjectId="46917"/>
    <cge:TPSR_Ref TObjectID="8353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46924">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.181818 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8356" ObjectName="SW-CX_KGYB.CX_KGYB_07467SW"/>
     <cge:Meas_Ref ObjectId="46924"/>
    <cge:TPSR_Ref TObjectID="8356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.181818 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8354" ObjectName="SW-CX_KGYB.CX_KGYB_074XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.181818 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8355" ObjectName="SW-CX_KGYB.CX_KGYB_074XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46931">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4127.272727 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8359" ObjectName="SW-CX_KGYB.CX_KGYB_07567SW"/>
     <cge:Meas_Ref ObjectId="46931"/>
    <cge:TPSR_Ref TObjectID="8359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.272727 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8357" ObjectName="SW-CX_KGYB.CX_KGYB_075XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.272727 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8358" ObjectName="SW-CX_KGYB.CX_KGYB_075XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4261.363636 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8385" ObjectName="SW-CX_KGYB.CX_KGYB_07667SW"/>
     <cge:Meas_Ref ObjectId="47005"/>
    <cge:TPSR_Ref TObjectID="8385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.363636 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8383" ObjectName="SW-CX_KGYB.CX_KGYB_076XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.363636 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8384" ObjectName="SW-CX_KGYB.CX_KGYB_076XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46937">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4395.454545 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8362" ObjectName="SW-CX_KGYB.CX_KGYB_07767SW"/>
     <cge:Meas_Ref ObjectId="46937"/>
    <cge:TPSR_Ref TObjectID="8362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4366.454545 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8360" ObjectName="SW-CX_KGYB.CX_KGYB_077XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4366.454545 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8361" ObjectName="SW-CX_KGYB.CX_KGYB_077XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46943">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.545455 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8365" ObjectName="SW-CX_KGYB.CX_KGYB_07867SW"/>
     <cge:Meas_Ref ObjectId="46943"/>
    <cge:TPSR_Ref TObjectID="8365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4500.545455 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8363" ObjectName="SW-CX_KGYB.CX_KGYB_078XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4500.545455 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8364" ObjectName="SW-CX_KGYB.CX_KGYB_078XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.636364 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8368" ObjectName="SW-CX_KGYB.CX_KGYB_07967SW"/>
     <cge:Meas_Ref ObjectId="46949"/>
    <cge:TPSR_Ref TObjectID="8368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46948">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.636364 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8366" ObjectName="SW-CX_KGYB.CX_KGYB_079XC"/>
     <cge:Meas_Ref ObjectId="46948"/>
    <cge:TPSR_Ref TObjectID="8366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46948">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.636364 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8367" ObjectName="SW-CX_KGYB.CX_KGYB_079XC1"/>
     <cge:Meas_Ref ObjectId="46948"/>
    <cge:TPSR_Ref TObjectID="8367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46955">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.727273 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8371" ObjectName="SW-CX_KGYB.CX_KGYB_08167SW"/>
     <cge:Meas_Ref ObjectId="46955"/>
    <cge:TPSR_Ref TObjectID="8371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46954">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4768.727273 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8369" ObjectName="SW-CX_KGYB.CX_KGYB_081XC"/>
     <cge:Meas_Ref ObjectId="46954"/>
    <cge:TPSR_Ref TObjectID="8369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46954">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4768.727273 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8370" ObjectName="SW-CX_KGYB.CX_KGYB_081XC1"/>
     <cge:Meas_Ref ObjectId="46954"/>
    <cge:TPSR_Ref TObjectID="8370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4931.818182 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8374" ObjectName="SW-CX_KGYB.CX_KGYB_08267SW"/>
     <cge:Meas_Ref ObjectId="46962"/>
    <cge:TPSR_Ref TObjectID="8374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46961">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4902.818182 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8372" ObjectName="SW-CX_KGYB.CX_KGYB_082XC"/>
     <cge:Meas_Ref ObjectId="46961"/>
    <cge:TPSR_Ref TObjectID="8372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46961">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4902.818182 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8373" ObjectName="SW-CX_KGYB.CX_KGYB_082XC1"/>
     <cge:Meas_Ref ObjectId="46961"/>
    <cge:TPSR_Ref TObjectID="8373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46903">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5065.909091 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8347" ObjectName="SW-CX_KGYB.CX_KGYB_08367SW"/>
     <cge:Meas_Ref ObjectId="46903"/>
    <cge:TPSR_Ref TObjectID="8347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46902">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5036.909091 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8345" ObjectName="SW-CX_KGYB.CX_KGYB_083XC"/>
     <cge:Meas_Ref ObjectId="46902"/>
    <cge:TPSR_Ref TObjectID="8345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46902">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5036.909091 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8346" ObjectName="SW-CX_KGYB.CX_KGYB_083XC1"/>
     <cge:Meas_Ref ObjectId="46902"/>
    <cge:TPSR_Ref TObjectID="8346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46975">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5200.000000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8380" ObjectName="SW-CX_KGYB.CX_KGYB_08467SW"/>
     <cge:Meas_Ref ObjectId="46975"/>
    <cge:TPSR_Ref TObjectID="8380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46974">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5171.000000 -273.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8378" ObjectName="SW-CX_KGYB.CX_KGYB_084XC"/>
     <cge:Meas_Ref ObjectId="46974"/>
    <cge:TPSR_Ref TObjectID="8378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46974">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5171.000000 -199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8379" ObjectName="SW-CX_KGYB.CX_KGYB_084XC1"/>
     <cge:Meas_Ref ObjectId="46974"/>
    <cge:TPSR_Ref TObjectID="8379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -200.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8352" ObjectName="SW-CX_KGYB.CX_KGYB_073XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8351" ObjectName="SW-CX_KGYB.CX_KGYB_073XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-9094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5139.000000 -1026.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1399" ObjectName="SW-CX_SG.CX_SG_3122SW"/>
     <cge:Meas_Ref ObjectId="9094"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-9093">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5139.000000 -912.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1398" ObjectName="SW-CX_SG.CX_SG_3121SW"/>
     <cge:Meas_Ref ObjectId="9093"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-9385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5015.000000 -819.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1461" ObjectName="SW-CX_SG.CX_SG_3626SW"/>
     <cge:Meas_Ref ObjectId="9385"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-9384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5142.000000 -819.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1460" ObjectName="SW-CX_SG.CX_SG_3621SW"/>
     <cge:Meas_Ref ObjectId="9384"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-9399">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5012.000000 -1123.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1464" ObjectName="SW-CX_SG.CX_SG_3826SW"/>
     <cge:Meas_Ref ObjectId="9399"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-9398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5139.000000 -1123.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1463" ObjectName="SW-CX_SG.CX_SG_3822SW"/>
     <cge:Meas_Ref ObjectId="9398"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3544.000000 -68.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_KGYB.CX_KGYB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-814 4256,-814 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8337" ObjectName="BS-CX_KGYB.CX_KGYB_3IM"/>
    <cge:TPSR_Ref TObjectID="8337"/></metadata>
   <polyline fill="none" opacity="0" points="3596,-814 4256,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SG.CX_SG_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5200,-1023 5200,-1173 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="1340" ObjectName="BS-CX_SG.CX_SG_3IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5200,-1023 5200,-1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SG.CX_SG_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5200,-823 5200,-973 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="1339" ObjectName="BS-CX_SG.CX_SG_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5200,-823 5200,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_KGYB.CX_KGYB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3504,-310 5256,-310 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8338" ObjectName="BS-CX_KGYB.CX_KGYB_9IM"/>
    <cge:TPSR_Ref TObjectID="8338"/></metadata>
   <polyline fill="none" opacity="0" points="3504,-310 5256,-310 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -492.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -492.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1dae110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3588.000000 -1029.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_228f340">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -1049.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c7f50">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4944.000000 -1144.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b41a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4947.000000 -840.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eb3720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4897.000000 -788.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2abd110">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4871.000000 -888.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c31d30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4701.000000 -804.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2434b00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -949.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218bbe0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4007.500000 -1000.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2215950">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 -596.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b70810">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3862.500000 -593.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb2eb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.857143 3772.000000 -435.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1857aa0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3863.500000 -355.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c1f9c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4083.000000 -632.000000)" xlink:href="#lightningRod:shape111"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d66c40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.024390 3638.000000 -386.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2948180">
    <use class="BV-0KV" transform="matrix(-0.000000 1.024390 1.000000 0.000000 3574.500000 -435.682927)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2206450">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.024390 3666.500000 -467.463415)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d52730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3548.000000 -104.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac7da0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3527.500000 -131.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dd94e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.000000 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18eee30">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3682.500000 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d682c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.090909 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23b7220">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3816.590909 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20141a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.181818 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f09110">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3950.681818 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22756c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4103.272727 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23aefb0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4084.772727 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b4c70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.363636 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2076890">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4218.863636 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0cab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4371.454545 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1edd290">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4352.954545 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9c0d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.545455 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d24590">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4487.045455 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f7f920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.636364 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ecd420">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4621.136364 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2219cf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4773.727273 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_b3ef340">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4755.227273 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2687680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4907.818182 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2692350">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4889.318182 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d4340">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5041.909091 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26e08b0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5023.409091 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eec340">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5176.000000 -103.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b709e0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5157.500000 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a91120">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4856.000000 -1203.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18375f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 -1064.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26725d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 -1063.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3233.000000 -1114.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78574" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3252.538462 -1012.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78574" ObjectName="CX_KGYB:CX_KGYB_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46824" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3695.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8340"/>
     <cge:Term_Ref ObjectID="11726"/>
    <cge:TPSR_Ref TObjectID="8340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46866" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3695.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8340"/>
     <cge:Term_Ref ObjectID="11726"/>
    <cge:TPSR_Ref TObjectID="8340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46819" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3695.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46819" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8340"/>
     <cge:Term_Ref ObjectID="11726"/>
    <cge:TPSR_Ref TObjectID="8340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46858" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3690.000000 -757.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8381"/>
     <cge:Term_Ref ObjectID="11808"/>
    <cge:TPSR_Ref TObjectID="8381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46878" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3690.000000 -757.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8381"/>
     <cge:Term_Ref ObjectID="11808"/>
    <cge:TPSR_Ref TObjectID="8381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46857" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3690.000000 -757.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8381"/>
     <cge:Term_Ref ObjectID="11808"/>
    <cge:TPSR_Ref TObjectID="8381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46877" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3637.000000 -102.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8760"/>
     <cge:Term_Ref ObjectID="12377"/>
    <cge:TPSR_Ref TObjectID="8760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46855" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3637.000000 -102.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46855" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8760"/>
     <cge:Term_Ref ObjectID="12377"/>
    <cge:TPSR_Ref TObjectID="8760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46830" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3783.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8764"/>
     <cge:Term_Ref ObjectID="12385"/>
    <cge:TPSR_Ref TObjectID="8764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46868" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3783.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8764"/>
     <cge:Term_Ref ObjectID="12385"/>
    <cge:TPSR_Ref TObjectID="8764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46828" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3783.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8764"/>
     <cge:Term_Ref ObjectID="12385"/>
    <cge:TPSR_Ref TObjectID="8764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46833" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3918.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8769"/>
     <cge:Term_Ref ObjectID="12395"/>
    <cge:TPSR_Ref TObjectID="8769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46869" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3918.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8769"/>
     <cge:Term_Ref ObjectID="12395"/>
    <cge:TPSR_Ref TObjectID="8769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46831" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3918.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8769"/>
     <cge:Term_Ref ObjectID="12395"/>
    <cge:TPSR_Ref TObjectID="8769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46836" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46836" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8761"/>
     <cge:Term_Ref ObjectID="12379"/>
    <cge:TPSR_Ref TObjectID="8761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46870" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8761"/>
     <cge:Term_Ref ObjectID="12379"/>
    <cge:TPSR_Ref TObjectID="8761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46834" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8761"/>
     <cge:Term_Ref ObjectID="12379"/>
    <cge:TPSR_Ref TObjectID="8761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46839" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4188.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46839" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8772"/>
     <cge:Term_Ref ObjectID="12401"/>
    <cge:TPSR_Ref TObjectID="8772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46871" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4188.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8772"/>
     <cge:Term_Ref ObjectID="12401"/>
    <cge:TPSR_Ref TObjectID="8772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46837" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4188.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46837" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8772"/>
     <cge:Term_Ref ObjectID="12401"/>
    <cge:TPSR_Ref TObjectID="8772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4321.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8768"/>
     <cge:Term_Ref ObjectID="12393"/>
    <cge:TPSR_Ref TObjectID="8768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46879" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4321.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8768"/>
     <cge:Term_Ref ObjectID="12393"/>
    <cge:TPSR_Ref TObjectID="8768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46860" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4321.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8768"/>
     <cge:Term_Ref ObjectID="12393"/>
    <cge:TPSR_Ref TObjectID="8768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46842" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4453.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8771"/>
     <cge:Term_Ref ObjectID="12399"/>
    <cge:TPSR_Ref TObjectID="8771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46872" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4453.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8771"/>
     <cge:Term_Ref ObjectID="12399"/>
    <cge:TPSR_Ref TObjectID="8771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46840" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4453.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8771"/>
     <cge:Term_Ref ObjectID="12399"/>
    <cge:TPSR_Ref TObjectID="8771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46845" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4587.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46845" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8763"/>
     <cge:Term_Ref ObjectID="12383"/>
    <cge:TPSR_Ref TObjectID="8763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46873" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4587.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8763"/>
     <cge:Term_Ref ObjectID="12383"/>
    <cge:TPSR_Ref TObjectID="8763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46843" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4587.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8763"/>
     <cge:Term_Ref ObjectID="12383"/>
    <cge:TPSR_Ref TObjectID="8763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46848" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4721.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8762"/>
     <cge:Term_Ref ObjectID="12381"/>
    <cge:TPSR_Ref TObjectID="8762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46874" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4721.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8762"/>
     <cge:Term_Ref ObjectID="12381"/>
    <cge:TPSR_Ref TObjectID="8762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46846" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4721.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8762"/>
     <cge:Term_Ref ObjectID="12381"/>
    <cge:TPSR_Ref TObjectID="8762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46851" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4855.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8766"/>
     <cge:Term_Ref ObjectID="12389"/>
    <cge:TPSR_Ref TObjectID="8766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46875" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4855.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8766"/>
     <cge:Term_Ref ObjectID="12389"/>
    <cge:TPSR_Ref TObjectID="8766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46849" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4855.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8766"/>
     <cge:Term_Ref ObjectID="12389"/>
    <cge:TPSR_Ref TObjectID="8766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46854" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4989.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46854" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8767"/>
     <cge:Term_Ref ObjectID="12391"/>
    <cge:TPSR_Ref TObjectID="8767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46876" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4989.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8767"/>
     <cge:Term_Ref ObjectID="12391"/>
    <cge:TPSR_Ref TObjectID="8767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46852" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4989.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8767"/>
     <cge:Term_Ref ObjectID="12391"/>
    <cge:TPSR_Ref TObjectID="8767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46827" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5123.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8765"/>
     <cge:Term_Ref ObjectID="12387"/>
    <cge:TPSR_Ref TObjectID="8765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46867" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5123.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8765"/>
     <cge:Term_Ref ObjectID="12387"/>
    <cge:TPSR_Ref TObjectID="8765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46825" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5123.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8765"/>
     <cge:Term_Ref ObjectID="12387"/>
    <cge:TPSR_Ref TObjectID="8765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47032" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5257.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8770"/>
     <cge:Term_Ref ObjectID="12397"/>
    <cge:TPSR_Ref TObjectID="8770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47033" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5257.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47033" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8770"/>
     <cge:Term_Ref ObjectID="12397"/>
    <cge:TPSR_Ref TObjectID="8770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47030" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5257.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8770"/>
     <cge:Term_Ref ObjectID="12397"/>
    <cge:TPSR_Ref TObjectID="8770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46864" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3960.000000 -394.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8386"/>
     <cge:Term_Ref ObjectID="11818"/>
    <cge:TPSR_Ref TObjectID="8386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46880" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3960.000000 -394.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8386"/>
     <cge:Term_Ref ObjectID="11818"/>
    <cge:TPSR_Ref TObjectID="8386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46863" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3960.000000 -394.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8386"/>
     <cge:Term_Ref ObjectID="11818"/>
    <cge:TPSR_Ref TObjectID="8386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-46881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3593.000000 -894.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8337"/>
     <cge:Term_Ref ObjectID="11722"/>
    <cge:TPSR_Ref TObjectID="8337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-46882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3593.000000 -894.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8337"/>
     <cge:Term_Ref ObjectID="11722"/>
    <cge:TPSR_Ref TObjectID="8337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-46883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3593.000000 -894.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8337"/>
     <cge:Term_Ref ObjectID="11722"/>
    <cge:TPSR_Ref TObjectID="8337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-46884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3593.000000 -894.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8337"/>
     <cge:Term_Ref ObjectID="11722"/>
    <cge:TPSR_Ref TObjectID="8337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-46891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3580.000000 -349.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8338"/>
     <cge:Term_Ref ObjectID="11723"/>
    <cge:TPSR_Ref TObjectID="8338"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3245" y="-1173"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3245" y="-1173"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3196" y="-1190"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3196" y="-1190"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3245" y="-1173"/></g>
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3196" y="-1190"/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4092,-507 4101,-507 4098,-497 4092,-507 " stroke="rgb(255,255,0)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_16e9df0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -1001.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1f5cc50" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3543.000000 140.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2966c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3616.500000 -43.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18c7620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3682.000000 -835.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef1a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3613.000000 -181.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23b17d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3730.000000 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf64c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.090909 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2548e80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.181818 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2074db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.272727 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2003b90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.363636 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218c460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.454545 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b61fe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.545455 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18d2130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.636364 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18dd3c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.727273 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a2ae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.818182 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22890a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5070.909091 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_246b030" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5205.000000 -101.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="8337" cx="3775" cy="-814" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="1340" cx="5200" cy="-1149" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="1340" cx="5199" cy="-1052" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="1339" cx="5199" cy="-938" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="1339" cx="5199" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8337" cx="4095" cy="-814" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8337" cx="3777" cy="-814" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8337" cx="4096" cy="-814" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="3777" cy="-310" fill="rgb(50,205,50)" r="6" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="3553" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="3706" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="3974" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="4108" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="4242" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="4376" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="4511" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="4645" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="4779" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="4913" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="5047" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="5181" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8338" cx="3840" cy="-310" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25fa8d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4358.000000 -1174.000000) translate(0,15)">N6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2946820" transform="matrix(1.000000 0.000000 0.000000 1.000000 4299.000000 -867.000000) translate(0,15)">N6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16c6c70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4534.000000 -1170.000000) translate(0,15)">35kV沙桃II回T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30126b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4594.000000 -1140.000000) translate(0,15)">LGJ-185/25</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24dc570" transform="matrix(1.000000 0.000000 0.000000 1.000000 4484.000000 -870.000000) translate(0,15)">LGJ-185/25</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2137140" transform="matrix(1.000000 0.000000 0.000000 1.000000 4477.000000 -836.000000) translate(0,15)">35kV沙桃I回T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b44570" transform="matrix(1.000000 0.000000 0.000000 1.000000 4815.000000 -887.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_246ae30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4804.000000 -1210.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29e92e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4850.000000 -1145.000000) translate(0,15)">YJV22-3*185</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22872b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4862.000000 -832.000000) translate(0,15)">YJV22-3*185</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29f3a50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3893.000000 -1150.000000) translate(0,15)">35kV昆钢弈标支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cf0e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3896.000000 -1131.000000) translate(0,15)">LGJ-185/25</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f7eeb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3788.000000 -1087.000000) translate(0,15)">YJV22-3*150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25eee10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.000000 -1007.000000) translate(0,15)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25eee10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.000000 -1007.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22b2f90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3516.000000 175.000000) translate(0,15)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2017620" transform="matrix(1.000000 0.000000 0.000000 1.024390 3515.000000 -471.341463) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2017620" transform="matrix(1.000000 0.000000 0.000000 1.024390 3515.000000 -471.341463) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fd80a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3642.000000 14.000000) translate(0,15)">混凝土搅拌站变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2440ff0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3782.000000 14.000000) translate(0,15)">水泥矿渣磨变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ba42a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3929.000000 14.000000) translate(0,15)">包装变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16ec800" transform="matrix(1.000000 0.000000 0.000000 1.000000 4003.000000 14.000000) translate(0,15)">选粉机收尘器风机电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18b5e40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4220.000000 14.000000) translate(0,15)">磨主电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_221f960" transform="matrix(1.000000 0.000000 0.000000 1.000000 4341.000000 14.000000) translate(0,15)">循环风机电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18b4340" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 14.000000) translate(0,15)">辊压机定辊电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ecd8a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4731.000000 14.000000) translate(0,15)">立磨收尘器风机电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2286220" transform="matrix(1.000000 0.000000 0.000000 1.000000 4900.000000 12.000000) translate(0,15)">立磨主电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1aa1c80" transform="matrix(1.000000 0.000000 0.000000 1.000000 5021.000000 14.000000) translate(0,15)">空压站变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2388e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5168.000000 14.000000) translate(0,15)">箱变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1aca960" transform="matrix(1.000000 0.000000 0.000000 1.000000 4464.000000 14.000000) translate(0,15)">辊压机动辊电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2258a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5216.000000 -1157.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2258a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5216.000000 -1157.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2258a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5216.000000 -1157.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2258a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5216.000000 -1157.000000) translate(0,69)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2258a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5216.000000 -1157.000000) translate(0,87)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2258a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5216.000000 -1157.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e420c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4063.000000 -473.000000) translate(0,15)">1#站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22233e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4103.000000 -788.000000) translate(0,12)">3031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16e95b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4102.000000 -871.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16e97b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3784.000000 -946.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2660040" transform="matrix(1.000000 0.000000 0.000000 1.000000 3782.000000 -998.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2660250" transform="matrix(1.000000 0.000000 0.000000 1.000000 3782.000000 -886.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2598b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3721.000000 -869.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2598d30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3786.000000 -747.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18e0750" transform="matrix(1.000000 0.000000 0.000000 1.000000 3784.000000 -701.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18e0960" transform="matrix(1.000000 0.000000 0.000000 1.000000 3786.000000 -380.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259e0d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3568.000000 -217.000000) translate(0,12)">07117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259e2d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3747.000000 -166.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259f1f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3881.000000 -166.000000) translate(0,12)">07367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259f400" transform="matrix(1.000000 0.000000 0.000000 1.000000 4015.000000 -166.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7d040" transform="matrix(1.000000 0.000000 0.000000 1.000000 4149.000000 -166.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7d240" transform="matrix(1.000000 0.000000 0.000000 1.000000 4283.000000 -166.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_258a400" transform="matrix(1.000000 0.000000 0.000000 1.000000 4417.000000 -166.000000) translate(0,12)">07767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_258a610" transform="matrix(1.000000 0.000000 0.000000 1.000000 4552.000000 -166.000000) translate(0,12)">07867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207fb80" transform="matrix(1.000000 0.000000 0.000000 1.000000 4686.000000 -166.000000) translate(0,12)">07967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207fd80" transform="matrix(1.000000 0.000000 0.000000 1.000000 4820.000000 -166.000000) translate(0,12)">08167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256f820" transform="matrix(1.000000 0.000000 0.000000 1.000000 4954.000000 -166.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256fa30" transform="matrix(1.000000 0.000000 0.000000 1.000000 5088.000000 -166.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac68b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5222.000000 -166.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6ab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3612.000000 -838.000000) translate(0,12)">I段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad8370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3805.000000 -552.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad85a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3567.000000 -261.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f83500" transform="matrix(1.000000 0.000000 0.000000 1.000000 3715.000000 -258.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f83710" transform="matrix(1.000000 0.000000 0.000000 1.000000 3849.000000 -259.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ea5e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3984.000000 -258.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ea830" transform="matrix(1.000000 0.000000 0.000000 1.000000 4118.000000 -258.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dfec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4252.000000 -258.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e0110" transform="matrix(1.000000 0.000000 0.000000 1.000000 4386.000000 -258.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b00af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4520.000000 -258.000000) translate(0,12)">078</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b00d00" transform="matrix(1.000000 0.000000 0.000000 1.000000 4654.000000 -258.000000) translate(0,12)">079</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da8620" transform="matrix(1.000000 0.000000 0.000000 1.000000 4788.000000 -258.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1da8870" transform="matrix(1.000000 0.000000 0.000000 1.000000 4922.000000 -258.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24234c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5056.000000 -258.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2423710" transform="matrix(1.000000 0.000000 0.000000 1.000000 5190.000000 -258.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -575.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1054.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1054.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1054.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1054.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1054.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1054.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b37b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1054.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_206fa90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3271.000000 -1162.500000) translate(0,16)">昆钢奕标变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218daa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5238.000000 -1040.000000) translate(0,12)">110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218daa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5238.000000 -1040.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218daa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5238.000000 -1040.000000) translate(0,42)">沙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218daa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5238.000000 -1040.000000) translate(0,57)">沟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218daa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5238.000000 -1040.000000) translate(0,72)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2486370" transform="matrix(1.000000 0.000000 0.000000 1.000000 5210.000000 -946.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2486370" transform="matrix(1.000000 0.000000 0.000000 1.000000 5210.000000 -946.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2486370" transform="matrix(1.000000 0.000000 0.000000 1.000000 5210.000000 -946.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2486370" transform="matrix(1.000000 0.000000 0.000000 1.000000 5210.000000 -946.000000) translate(0,69)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2486370" transform="matrix(1.000000 0.000000 0.000000 1.000000 5210.000000 -946.000000) translate(0,87)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2486370" transform="matrix(1.000000 0.000000 0.000000 1.000000 5210.000000 -946.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486580" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -1003.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2570680" transform="matrix(1.000000 0.000000 0.000000 1.000000 5132.000000 -1078.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25708c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5132.000000 -964.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f06b00" transform="matrix(1.000000 0.000000 0.000000 1.000000 5131.000000 -1175.000000) translate(0,12)">3822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f06d10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5072.000000 -1174.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_258bdb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5004.000000 -1175.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" graphid="g_24b1430" transform="matrix(1.000000 0.000000 0.000000 1.000000 5135.000000 -871.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" graphid="g_299b930" transform="matrix(1.000000 0.000000 0.000000 1.000000 5072.000000 -870.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" graphid="g_299bb50" transform="matrix(1.000000 0.000000 0.000000 1.000000 5007.000000 -871.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b67030" transform="matrix(1.000000 0.000000 0.000000 1.000000 3784.000000 -790.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2169e80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3790.000000 -454.000000) translate(0,15)">YJV-1*300</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_216a090" transform="matrix(1.000000 0.000000 0.000000 1.000000 3682.000000 -644.000000) translate(0,15)">YJV-3*150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24ca1d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3587.000000 -601.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24ca1d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3587.000000 -601.000000) translate(0,33)">SF11-10000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24ca1d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3587.000000 -601.000000) translate(0,51)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24ca1d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3587.000000 -601.000000) translate(0,69)">10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24ca1d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3587.000000 -601.000000) translate(0,87)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24ca1d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3587.000000 -601.000000) translate(0,105)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2694280" transform="matrix(1.000000 0.000000 0.000000 1.000000 3510.000000 -85.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259cfb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3575.000000 -48.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_259d1c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3559.000000 112.000000) translate(0,12)">07100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2595180" transform="matrix(1.000000 0.000000 0.000000 1.000000 4252.000000 -258.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24df2d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3510.000000 -331.000000) translate(0,12)">I段</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abddc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3633.000000 959.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aca710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3622.000000 944.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aca920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 929.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256e5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3633.000000 757.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b37160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3622.000000 742.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b373b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 727.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5c160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3569.000000 100.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_187dd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 85.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_187dff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3723.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad5330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3737.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad5540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2597270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25968f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2596b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3844.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dba20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2670410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2670610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3981.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e179a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d023a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d025e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aee890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b7cf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b7d0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e4f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4397.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2341eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4411.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23420f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_201afc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff9ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffa130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4521.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2590bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a5820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4679.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a5a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e2130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2485030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2485270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5a4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e59e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4942.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e5c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18696f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5062.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b0c2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5076.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b0c4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd5af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5201.000000 67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18f70e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5215.000000 37.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18f7320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5190.000000 52.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22cdbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 394.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_265f400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 364.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_265f610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 379.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acf930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3506.000000 896.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acfb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3498.000000 849.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2964ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3506.000000 866.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2964f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3506.000000 881.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5b050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3491.000000 349.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_KGYB.CX_KGYB_1drq">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3525.000000 80.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10520" ObjectName="CB-CX_KGYB.CX_KGYB_1drq"/>
    <cge:TPSR_Ref TObjectID="10520"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-37311" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3418.000000 -1082.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5893" ObjectName="DYN-CX_KGYB"/>
     <cge:Meas_Ref ObjectId="37311"/>
    </metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46893">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3766.000000 -917.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8340" ObjectName="SW-CX_KGYB.CX_KGYB_371BK"/>
     <cge:Meas_Ref ObjectId="46893"/>
    <cge:TPSR_Ref TObjectID="8340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-9100">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5114.000000 -974.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1400" ObjectName="SW-CX_SG.CX_SG_312BK"/>
     <cge:Meas_Ref ObjectId="9100"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-9396">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5063.000000 -1139.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1462" ObjectName="SW-CX_SG.CX_SG_382BK"/>
     <cge:Meas_Ref ObjectId="9396"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-9382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5063.000000 -835.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1459" ObjectName="SW-CX_SG.CX_SG_362BK"/>
     <cge:Meas_Ref ObjectId="9382"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46995">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3768.000000 -718.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8381" ObjectName="SW-CX_KGYB.CX_KGYB_301BK"/>
     <cge:Meas_Ref ObjectId="46995"/>
    <cge:TPSR_Ref TObjectID="8381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47028">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3768.000000 -351.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8386" ObjectName="SW-CX_KGYB.CX_KGYB_001BK"/>
     <cge:Meas_Ref ObjectId="47028"/>
    <cge:TPSR_Ref TObjectID="8386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46909">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3697.000000 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8764" ObjectName="SW-CX_KGYB.CX_KGYB_072BK"/>
     <cge:Meas_Ref ObjectId="46909"/>
    <cge:TPSR_Ref TObjectID="8764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46923">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.181818 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8761" ObjectName="SW-CX_KGYB.CX_KGYB_074BK"/>
     <cge:Meas_Ref ObjectId="46923"/>
    <cge:TPSR_Ref TObjectID="8761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46930">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.272727 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8772" ObjectName="SW-CX_KGYB.CX_KGYB_075BK"/>
     <cge:Meas_Ref ObjectId="46930"/>
    <cge:TPSR_Ref TObjectID="8772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46936">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4367.454545 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8771" ObjectName="SW-CX_KGYB.CX_KGYB_077BK"/>
     <cge:Meas_Ref ObjectId="46936"/>
    <cge:TPSR_Ref TObjectID="8771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46942">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4501.545455 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8763" ObjectName="SW-CX_KGYB.CX_KGYB_078BK"/>
     <cge:Meas_Ref ObjectId="46942"/>
    <cge:TPSR_Ref TObjectID="8763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46948">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.636364 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8762" ObjectName="SW-CX_KGYB.CX_KGYB_079BK"/>
     <cge:Meas_Ref ObjectId="46948"/>
    <cge:TPSR_Ref TObjectID="8762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46954">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.727273 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8766" ObjectName="SW-CX_KGYB.CX_KGYB_081BK"/>
     <cge:Meas_Ref ObjectId="46954"/>
    <cge:TPSR_Ref TObjectID="8766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46961">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.818182 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8767" ObjectName="SW-CX_KGYB.CX_KGYB_082BK"/>
     <cge:Meas_Ref ObjectId="46961"/>
    <cge:TPSR_Ref TObjectID="8767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46902">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5037.909091 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8765" ObjectName="SW-CX_KGYB.CX_KGYB_083BK"/>
     <cge:Meas_Ref ObjectId="46902"/>
    <cge:TPSR_Ref TObjectID="8765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46974">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5172.000000 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8770" ObjectName="SW-CX_KGYB.CX_KGYB_084BK"/>
     <cge:Meas_Ref ObjectId="46974"/>
    <cge:TPSR_Ref TObjectID="8770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8769" ObjectName="SW-CX_KGYB.CX_KGYB_073BK"/>
     <cge:Meas_Ref ObjectId="46916"/>
    <cge:TPSR_Ref TObjectID="8769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -543.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46967">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3544.000000 -228.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8760" ObjectName="SW-CX_KGYB.CX_KGYB_071BK"/>
     <cge:Meas_Ref ObjectId="46967"/>
    <cge:TPSR_Ref TObjectID="8760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47004">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.454545 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8768" ObjectName="SW-CX_KGYB.CX_KGYB_076BK"/>
     <cge:Meas_Ref ObjectId="47004"/>
    <cge:TPSR_Ref TObjectID="8768"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_b3f7280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-814 3775,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8337@0" ObjectIDZND0="8342@x" ObjectIDZND1="8341@x" Pin0InfoVect0LinkObjId="SW-46895_0" Pin0InfoVect1LinkObjId="SW-46894_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a34250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-814 3775,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ec8d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-843 3775,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8337@0" ObjectIDND1="8341@x" ObjectIDZND0="8342@0" Pin0InfoVect0LinkObjId="SW-46895_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a34250_0" Pin1InfoVect1LinkObjId="SW-46894_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-843 3775,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24f2c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-897 3775,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8342@1" ObjectIDZND0="8340@0" Pin0InfoVect0LinkObjId="SW-46893_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46895_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-897 3775,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a7c1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-952 3775,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8340@1" ObjectIDZND0="8339@0" Pin0InfoVect0LinkObjId="SW-46892_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46893_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-952 3775,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a35e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-1035 3648,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8339@x" ObjectIDND1="g_228f340@0" ObjectIDZND0="g_1dae110@0" Pin0InfoVect0LinkObjId="g_1dae110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46892_0" Pin1InfoVect1LinkObjId="g_228f340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-1035 3648,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d90e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-1009 3775,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8339@1" ObjectIDZND0="g_1dae110@0" ObjectIDZND1="g_228f340@0" Pin0InfoVect0LinkObjId="g_1dae110_0" Pin0InfoVect1LinkObjId="g_228f340_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46892_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-1009 3775,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_227f510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-1035 3775,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1dae110@0" ObjectIDND1="8339@x" ObjectIDZND0="g_228f340@1" Pin0InfoVect0LinkObjId="g_228f340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dae110_0" Pin1InfoVect1LinkObjId="SW-46892_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-1035 3775,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a0580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4942,-845 4974,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_24b41a0@1" ObjectIDZND0="g_2abd110@0" ObjectIDZND1="g_1eb3720@0" ObjectIDZND2="1461@x" Pin0InfoVect0LinkObjId="g_2abd110_0" Pin0InfoVect1LinkObjId="g_1eb3720_0" Pin0InfoVect2LinkObjId="SW-9385_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24b41a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4942,-845 4974,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f4520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-845 4974,-878 4943,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_24b41a0@0" ObjectIDND1="g_1eb3720@0" ObjectIDND2="1461@x" ObjectIDZND0="g_2abd110@0" Pin0InfoVect0LinkObjId="g_2abd110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24b41a0_0" Pin1InfoVect1LinkObjId="g_1eb3720_0" Pin1InfoVect2LinkObjId="SW-9385_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-845 4974,-878 4943,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7e850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-845 4829,-810 4760,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1c31d30@0" Pin0InfoVect0LinkObjId="g_1c31d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-845 4829,-810 4760,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2076af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4282,-845 4889,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_24b41a0@0" Pin0InfoVect0LinkObjId="g_24b41a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4282,-845 4889,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2672d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-845 4975,-845 4975,-795 4956,-795 4956,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_24b41a0@0" ObjectIDND1="g_2abd110@0" ObjectIDND2="1461@x" ObjectIDZND0="g_1eb3720@0" Pin0InfoVect0LinkObjId="g_1eb3720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24b41a0_0" Pin1InfoVect1LinkObjId="g_2abd110_0" Pin1InfoVect2LinkObjId="SW-9385_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-845 4975,-845 4975,-795 4956,-795 4956,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2075b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-985 4095,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2434b00@1" ObjectIDZND0="g_16e9df0@0" Pin0InfoVect0LinkObjId="g_16e9df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2434b00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-985 4095,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2201f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-941 4014,-924 4095,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_218bbe0@0" ObjectIDZND0="g_2434b00@0" ObjectIDZND1="8343@x" Pin0InfoVect0LinkObjId="g_2434b00_0" Pin0InfoVect1LinkObjId="SW-46896_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218bbe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-941 4014,-924 4095,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25803d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-924 4095,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_218bbe0@0" ObjectIDND1="8343@x" ObjectIDZND0="g_2434b00@0" Pin0InfoVect0LinkObjId="g_2434b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_218bbe0_0" Pin1InfoVect1LinkObjId="SW-46896_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-924 4095,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2409380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-814 4095,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8337@0" ObjectIDZND0="8343@0" Pin0InfoVect0LinkObjId="SW-46896_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a34250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-814 4095,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19da720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-882 4095,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8343@1" ObjectIDZND0="g_218bbe0@0" ObjectIDZND1="g_2434b00@0" Pin0InfoVect0LinkObjId="g_218bbe0_0" Pin0InfoVect1LinkObjId="g_2434b00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46896_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-882 4095,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d23290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-666 3856,-666 3856,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8382@x" ObjectIDND1="g_2215950@0" ObjectIDZND0="g_1b70810@0" Pin0InfoVect0LinkObjId="g_1b70810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46996_0" Pin1InfoVect1LinkObjId="g_2215950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-666 3856,-666 3856,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2455990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-430 3857,-430 3857,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8387@x" ObjectIDND1="g_1bb2eb0@0" ObjectIDZND0="g_1857aa0@0" Pin0InfoVect0LinkObjId="g_1857aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47029_0" Pin1InfoVect1LinkObjId="g_1bb2eb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-430 3857,-430 3857,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b69e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-814 4096,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8337@0" ObjectIDZND0="8344@1" Pin0InfoVect0LinkObjId="SW-46897_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a34250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-814 4096,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a34250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-801 3777,-814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10516@1" ObjectIDZND0="8337@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56948_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-801 3777,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d10ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-753 3777,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8381@1" ObjectIDZND0="10516@0" Pin0InfoVect0LinkObjId="SW-56948_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46995_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-753 3777,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac20b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-712 3777,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8382@1" ObjectIDZND0="8381@0" Pin0InfoVect0LinkObjId="SW-46995_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-712 3777,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a834a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-666 3777,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b70810@0" ObjectIDND1="g_2215950@0" ObjectIDZND0="8382@0" Pin0InfoVect0LinkObjId="SW-46996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b70810_0" Pin1InfoVect1LinkObjId="g_2215950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-666 3777,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fc2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-666 3777,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1b70810@0" ObjectIDND1="8382@x" ObjectIDZND0="g_2215950@0" Pin0InfoVect0LinkObjId="g_2215950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b70810_0" Pin1InfoVect1LinkObjId="SW-46996_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-666 3777,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e1f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-601 3777,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2215950@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2215950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-601 3777,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23935a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-737 4096,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c1f9c0@1" ObjectIDZND0="8344@0" Pin0InfoVect0LinkObjId="SW-46897_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c1f9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-737 4096,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e0a670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-424 3646,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d66c40@1" ObjectIDZND0="g_2206450@0" Pin0InfoVect0LinkObjId="g_2206450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d66c40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-424 3646,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21bf140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3581,-375 3647,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2948180@0" ObjectIDZND0="g_1d66c40@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1d66c40_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2948180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3581,-375 3647,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1de5550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-375 3647,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2948180@0" ObjectIDND1="0@x" ObjectIDZND0="g_1d66c40@0" Pin0InfoVect0LinkObjId="g_1d66c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2948180_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-375 3647,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e09f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3521,-191 3553,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ac7da0@0" ObjectIDZND0="g_1d52730@0" ObjectIDZND1="8376@x" ObjectIDZND2="8377@x" Pin0InfoVect0LinkObjId="g_1d52730_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-46968_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac7da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3521,-191 3553,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c22140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-162 3553,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d52730@0" ObjectIDZND0="g_2ac7da0@0" ObjectIDZND1="8376@x" ObjectIDZND2="8377@x" Pin0InfoVect0LinkObjId="g_2ac7da0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-46968_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d52730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-162 3553,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_267e630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-109 3553,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d52730@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d52730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-109 3553,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15bcad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,136 3553,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1f5cc50@0" Pin0InfoVect0LinkObjId="g_1f5cc50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,136 3553,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a7a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-843 3759,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8337@0" ObjectIDND1="8342@x" ObjectIDZND0="8341@1" Pin0InfoVect0LinkObjId="SW-46894_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a34250_0" Pin1InfoVect1LinkObjId="SW-46895_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-843 3759,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d5daf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3723,-843 3707,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8341@0" ObjectIDZND0="g_18c7620@0" Pin0InfoVect0LinkObjId="g_18c7620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46894_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3723,-843 3707,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2200690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-419 3777,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8387@0" ObjectIDZND0="g_1857aa0@0" ObjectIDZND1="g_1bb2eb0@0" Pin0InfoVect0LinkObjId="g_1857aa0_0" Pin0InfoVect1LinkObjId="g_1bb2eb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47029_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-419 3777,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e0460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-310 3777,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8338@0" ObjectIDZND0="8388@0" Pin0InfoVect0LinkObjId="SW-47029_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f0c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-310 3777,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2067290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-345 3777,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8388@1" ObjectIDZND0="8386@0" Pin0InfoVect0LinkObjId="SW-47028_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47029_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-345 3777,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_214dbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-386 3777,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8386@1" ObjectIDZND0="8387@1" Pin0InfoVect0LinkObjId="SW-47029_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47028_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-386 3777,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f0c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-296 3553,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8375@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_240f080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-296 3553,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2761d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-191 3553,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2ac7da0@0" ObjectIDND1="g_1d52730@0" ObjectIDND2="8377@x" ObjectIDZND0="8376@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ac7da0_0" Pin1InfoVect1LinkObjId="g_1d52730_0" Pin1InfoVect2LinkObjId="SW-46968_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-191 3553,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2077b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-222 3553,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8376@1" ObjectIDZND0="8760@0" Pin0InfoVect0LinkObjId="SW-46967_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-222 3553,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_b3f48e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-263 3553,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8760@1" ObjectIDZND0="8375@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46967_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-263 3553,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2292e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-310 3647,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8338@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f0c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-310 3647,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2683cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-346 3647,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2948180@0" ObjectIDZND1="g_1d66c40@0" Pin0InfoVect0LinkObjId="g_2948180_0" Pin0InfoVect1LinkObjId="g_1d66c40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-346 3647,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26799b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-191 3570,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2ac7da0@0" ObjectIDND1="g_1d52730@0" ObjectIDND2="8376@x" ObjectIDZND0="8377@0" Pin0InfoVect0LinkObjId="SW-46968_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ac7da0_0" Pin1InfoVect1LinkObjId="g_1d52730_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-191 3570,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b2f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3606,-191 3618,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8377@1" ObjectIDZND0="g_1ef1a40@0" Pin0InfoVect0LinkObjId="g_1ef1a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3606,-191 3618,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a79730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-189 3706,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_18eee30@0" ObjectIDZND0="g_1dd94e0@0" ObjectIDZND1="8350@x" ObjectIDZND2="8349@x" Pin0InfoVect0LinkObjId="g_1dd94e0_0" Pin0InfoVect1LinkObjId="SW-46910_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18eee30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-189 3706,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2022410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-189 3706,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_18eee30@0" ObjectIDND1="8350@x" ObjectIDND2="8349@x" ObjectIDZND0="g_1dd94e0@0" Pin0InfoVect0LinkObjId="g_1dd94e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18eee30_0" Pin1InfoVect1LinkObjId="SW-46910_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-189 3706,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1abcfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-108 3706,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1dd94e0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dd94e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-108 3706,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_268de30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3707,-188 3706,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1dd94e0@0" ObjectIDND1="g_18eee30@0" ObjectIDND2="8350@x" ObjectIDZND0="g_1dd94e0@0" ObjectIDZND1="g_18eee30@0" ObjectIDZND2="8350@x" Pin0InfoVect0LinkObjId="g_1dd94e0_0" Pin0InfoVect1LinkObjId="g_18eee30_0" Pin0InfoVect2LinkObjId="SW-46910_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1dd94e0_0" Pin1InfoVect1LinkObjId="g_18eee30_0" Pin1InfoVect2LinkObjId="SW-46910_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3707,-188 3706,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac2f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-127 3740,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_23b17d0@0" ObjectIDZND0="8350@0" Pin0InfoVect0LinkObjId="SW-46910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23b17d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-127 3740,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d58290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-177 3740,-189 3706,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8350@1" ObjectIDZND0="g_1dd94e0@0" ObjectIDZND1="g_18eee30@0" ObjectIDZND2="8349@x" Pin0InfoVect0LinkObjId="g_1dd94e0_0" Pin0InfoVect1LinkObjId="g_18eee30_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-177 3740,-189 3706,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_240f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-297 3706,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8348@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-297 3706,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18a5670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-191 3706,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1dd94e0@0" ObjectIDND1="g_18eee30@0" ObjectIDND2="8350@x" ObjectIDZND0="8349@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1dd94e0_0" Pin1InfoVect1LinkObjId="g_18eee30_0" Pin1InfoVect2LinkObjId="SW-46910_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-191 3706,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dad900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-223 3706,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8349@1" ObjectIDZND0="8764@0" Pin0InfoVect0LinkObjId="SW-46909_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-223 3706,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f1010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-264 3706,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8764@1" ObjectIDZND0="8348@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46909_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-264 3706,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1e5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-189 3840,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_23b7220@0" ObjectIDZND0="g_1d682c0@0" ObjectIDZND1="8353@x" ObjectIDZND2="g_1d682c0@0" Pin0InfoVect0LinkObjId="g_1d682c0_0" Pin0InfoVect1LinkObjId="SW-46917_0" Pin0InfoVect2LinkObjId="g_1d682c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23b7220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-189 3840,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eb2630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-189 3840,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_23b7220@0" ObjectIDND1="8353@x" ObjectIDND2="g_1d682c0@0" ObjectIDZND0="g_1d682c0@0" Pin0InfoVect0LinkObjId="g_1d682c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23b7220_0" Pin1InfoVect1LinkObjId="SW-46917_0" Pin1InfoVect2LinkObjId="g_1d682c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-189 3840,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2931de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-108 3840,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1d682c0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d682c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-108 3840,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22877e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-188 3840,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1d682c0@0" ObjectIDND1="g_23b7220@0" ObjectIDND2="8353@x" ObjectIDZND0="g_1d682c0@0" ObjectIDZND1="g_23b7220@0" ObjectIDZND2="8353@x" Pin0InfoVect0LinkObjId="g_1d682c0_0" Pin0InfoVect1LinkObjId="g_23b7220_0" Pin0InfoVect2LinkObjId="SW-46917_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d682c0_0" Pin1InfoVect1LinkObjId="g_23b7220_0" Pin1InfoVect2LinkObjId="SW-46917_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-188 3840,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f7fb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-127 3874,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bf64c0@0" ObjectIDZND0="8353@0" Pin0InfoVect0LinkObjId="SW-46917_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bf64c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-127 3874,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b72620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-177 3874,-189 3840,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8353@1" ObjectIDZND0="g_1d682c0@0" ObjectIDZND1="g_23b7220@0" ObjectIDZND2="g_1d682c0@0" Pin0InfoVect0LinkObjId="g_1d682c0_0" Pin0InfoVect1LinkObjId="g_23b7220_0" Pin0InfoVect2LinkObjId="g_1d682c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46917_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-177 3874,-189 3840,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18bd580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-189 3974,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1f09110@0" ObjectIDZND0="g_20141a0@0" ObjectIDZND1="8356@x" ObjectIDZND2="8355@x" Pin0InfoVect0LinkObjId="g_20141a0_0" Pin0InfoVect1LinkObjId="SW-46924_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f09110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-189 3974,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257c430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3974,-189 3974,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1f09110@0" ObjectIDND1="8356@x" ObjectIDND2="8355@x" ObjectIDZND0="g_20141a0@0" Pin0InfoVect0LinkObjId="g_20141a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f09110_0" Pin1InfoVect1LinkObjId="SW-46924_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3974,-189 3974,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2276440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3974,-108 3974,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_20141a0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20141a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3974,-108 3974,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2089570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-188 3974,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_20141a0@0" ObjectIDND1="g_1f09110@0" ObjectIDND2="8356@x" ObjectIDZND0="g_20141a0@0" ObjectIDZND1="g_1f09110@0" ObjectIDZND2="8356@x" Pin0InfoVect0LinkObjId="g_20141a0_0" Pin0InfoVect1LinkObjId="g_1f09110_0" Pin0InfoVect2LinkObjId="SW-46924_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20141a0_0" Pin1InfoVect1LinkObjId="g_1f09110_0" Pin1InfoVect2LinkObjId="SW-46924_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-188 3974,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16e89a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4008,-127 4008,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2548e80@0" ObjectIDZND0="8356@0" Pin0InfoVect0LinkObjId="SW-46924_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2548e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4008,-127 4008,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_268ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4008,-177 4008,-189 3974,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8356@1" ObjectIDZND0="g_20141a0@0" ObjectIDZND1="g_1f09110@0" ObjectIDZND2="8355@x" Pin0InfoVect0LinkObjId="g_20141a0_0" Pin0InfoVect1LinkObjId="g_1f09110_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46924_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4008,-177 4008,-189 3974,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_261d560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3974,-297 3974,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8354@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3974,-297 3974,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eb86c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3974,-191 3974,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_20141a0@0" ObjectIDND1="g_1f09110@0" ObjectIDND2="8356@x" ObjectIDZND0="8355@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20141a0_0" Pin1InfoVect1LinkObjId="g_1f09110_0" Pin1InfoVect2LinkObjId="SW-46924_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3974,-191 3974,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff75f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3974,-223 3974,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8355@1" ObjectIDZND0="8761@0" Pin0InfoVect0LinkObjId="SW-46923_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3974,-223 3974,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1e920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3974,-264 3974,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8761@1" ObjectIDZND0="8354@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46923_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3974,-264 3974,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c14760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-189 4108,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_23aefb0@0" ObjectIDZND0="g_22756c0@0" ObjectIDZND1="8359@x" ObjectIDZND2="8358@x" Pin0InfoVect0LinkObjId="g_22756c0_0" Pin0InfoVect1LinkObjId="SW-46931_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23aefb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-189 4108,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2948600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-189 4108,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_23aefb0@0" ObjectIDND1="8359@x" ObjectIDND2="8358@x" ObjectIDZND0="g_22756c0@0" Pin0InfoVect0LinkObjId="g_22756c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23aefb0_0" Pin1InfoVect1LinkObjId="SW-46931_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-189 4108,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2277ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-108 4108,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_22756c0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22756c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-108 4108,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b45740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-188 4108,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_22756c0@0" ObjectIDND1="g_23aefb0@0" ObjectIDND2="8359@x" ObjectIDZND0="g_22756c0@0" ObjectIDZND1="g_23aefb0@0" ObjectIDZND2="8359@x" Pin0InfoVect0LinkObjId="g_22756c0_0" Pin0InfoVect1LinkObjId="g_23aefb0_0" Pin0InfoVect2LinkObjId="SW-46931_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22756c0_0" Pin1InfoVect1LinkObjId="g_23aefb0_0" Pin1InfoVect2LinkObjId="SW-46931_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-188 4108,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_186fc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-127 4142,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2074db0@0" ObjectIDZND0="8359@0" Pin0InfoVect0LinkObjId="SW-46931_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2074db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-127 4142,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20c02d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-177 4142,-189 4108,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8359@1" ObjectIDZND0="g_22756c0@0" ObjectIDZND1="g_23aefb0@0" ObjectIDZND2="8358@x" Pin0InfoVect0LinkObjId="g_22756c0_0" Pin0InfoVect1LinkObjId="g_23aefb0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46931_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-177 4142,-189 4108,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23aa330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-297 4108,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8357@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-297 4108,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29171c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-191 4108,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_22756c0@0" ObjectIDND1="g_23aefb0@0" ObjectIDND2="8359@x" ObjectIDZND0="8358@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22756c0_0" Pin1InfoVect1LinkObjId="g_23aefb0_0" Pin1InfoVect2LinkObjId="SW-46931_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-191 4108,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2548c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-223 4108,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8358@1" ObjectIDZND0="8772@0" Pin0InfoVect0LinkObjId="SW-46930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-223 4108,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f54500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-264 4108,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8772@1" ObjectIDZND0="8357@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-264 4108,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19daa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-189 4242,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2076890@0" ObjectIDZND0="g_20b4c70@0" ObjectIDZND1="8385@x" ObjectIDZND2="8384@x" Pin0InfoVect0LinkObjId="g_20b4c70_0" Pin0InfoVect1LinkObjId="SW-47005_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2076890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-189 4242,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1848710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-189 4242,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2076890@0" ObjectIDND1="8385@x" ObjectIDND2="8384@x" ObjectIDZND0="g_20b4c70@0" Pin0InfoVect0LinkObjId="g_20b4c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2076890_0" Pin1InfoVect1LinkObjId="SW-47005_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-189 4242,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2414940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-108 4242,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_20b4c70@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b4c70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-108 4242,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25910a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4243,-188 4242,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_20b4c70@0" ObjectIDND1="g_2076890@0" ObjectIDND2="8385@x" ObjectIDZND0="g_20b4c70@0" ObjectIDZND1="g_2076890@0" ObjectIDZND2="8385@x" Pin0InfoVect0LinkObjId="g_20b4c70_0" Pin0InfoVect1LinkObjId="g_2076890_0" Pin0InfoVect2LinkObjId="SW-47005_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20b4c70_0" Pin1InfoVect1LinkObjId="g_2076890_0" Pin1InfoVect2LinkObjId="SW-47005_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4243,-188 4242,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23fb7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4276,-127 4276,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2003b90@0" ObjectIDZND0="8385@0" Pin0InfoVect0LinkObjId="SW-47005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2003b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4276,-127 4276,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a95300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4276,-177 4276,-189 4242,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8385@1" ObjectIDZND0="g_20b4c70@0" ObjectIDZND1="g_2076890@0" ObjectIDZND2="8384@x" Pin0InfoVect0LinkObjId="g_20b4c70_0" Pin0InfoVect1LinkObjId="g_2076890_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4276,-177 4276,-189 4242,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f4e9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-297 4242,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8383@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-297 4242,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee8e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-191 4242,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_20b4c70@0" ObjectIDND1="g_2076890@0" ObjectIDND2="8385@x" ObjectIDZND0="8384@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20b4c70_0" Pin1InfoVect1LinkObjId="g_2076890_0" Pin1InfoVect2LinkObjId="SW-47005_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-191 4242,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-223 4242,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8384@1" ObjectIDZND0="8768@0" Pin0InfoVect0LinkObjId="SW-47004_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-223 4242,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab40f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-264 4242,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8768@1" ObjectIDZND0="8383@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-264 4242,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ec290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-189 4376,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1edd290@0" ObjectIDZND0="g_2a0cab0@0" ObjectIDZND1="8362@x" ObjectIDZND2="8361@x" Pin0InfoVect0LinkObjId="g_2a0cab0_0" Pin0InfoVect1LinkObjId="SW-46937_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1edd290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-189 4376,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c30790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4376,-189 4376,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1edd290@0" ObjectIDND1="8362@x" ObjectIDND2="8361@x" ObjectIDZND0="g_2a0cab0@0" Pin0InfoVect0LinkObjId="g_2a0cab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1edd290_0" Pin1InfoVect1LinkObjId="SW-46937_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4376,-189 4376,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_212f2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4376,-108 4376,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2a0cab0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a0cab0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4376,-108 4376,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2072bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-188 4376,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2a0cab0@0" ObjectIDND1="g_1edd290@0" ObjectIDND2="8362@x" ObjectIDZND0="g_2a0cab0@0" ObjectIDZND1="g_1edd290@0" ObjectIDZND2="8362@x" Pin0InfoVect0LinkObjId="g_2a0cab0_0" Pin0InfoVect1LinkObjId="g_1edd290_0" Pin0InfoVect2LinkObjId="SW-46937_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a0cab0_0" Pin1InfoVect1LinkObjId="g_1edd290_0" Pin1InfoVect2LinkObjId="SW-46937_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-188 4376,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1878c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-127 4410,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_218c460@0" ObjectIDZND0="8362@0" Pin0InfoVect0LinkObjId="SW-46937_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218c460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4410,-127 4410,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19cc680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-177 4410,-189 4376,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8362@1" ObjectIDZND0="g_2a0cab0@0" ObjectIDZND1="g_1edd290@0" ObjectIDZND2="8361@x" Pin0InfoVect0LinkObjId="g_2a0cab0_0" Pin0InfoVect1LinkObjId="g_1edd290_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46937_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4410,-177 4410,-189 4376,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_211f390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4376,-297 4376,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8360@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4376,-297 4376,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee9080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4376,-191 4376,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2a0cab0@0" ObjectIDND1="g_1edd290@0" ObjectIDND2="8362@x" ObjectIDZND0="8361@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a0cab0_0" Pin1InfoVect1LinkObjId="g_1edd290_0" Pin1InfoVect2LinkObjId="SW-46937_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4376,-191 4376,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2022ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4376,-223 4376,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8361@1" ObjectIDZND0="8771@0" Pin0InfoVect0LinkObjId="SW-46936_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4376,-223 4376,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18d53a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4376,-264 4376,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8771@1" ObjectIDZND0="8360@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46936_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4376,-264 4376,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2591930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4481,-189 4511,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d24590@0" ObjectIDZND0="g_1a9c0d0@0" ObjectIDZND1="8365@x" ObjectIDZND2="8364@x" Pin0InfoVect0LinkObjId="g_1a9c0d0_0" Pin0InfoVect1LinkObjId="SW-46943_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d24590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4481,-189 4511,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e39c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-189 4511,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d24590@0" ObjectIDND1="8365@x" ObjectIDND2="8364@x" ObjectIDZND0="g_1a9c0d0@0" Pin0InfoVect0LinkObjId="g_1a9c0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d24590_0" Pin1InfoVect1LinkObjId="SW-46943_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-189 4511,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b64e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-108 4511,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1a9c0d0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a9c0d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-108 4511,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e161a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-188 4511,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1a9c0d0@0" ObjectIDND1="g_1d24590@0" ObjectIDND2="8365@x" ObjectIDZND0="g_1a9c0d0@0" ObjectIDZND1="g_1d24590@0" ObjectIDZND2="8365@x" Pin0InfoVect0LinkObjId="g_1a9c0d0_0" Pin0InfoVect1LinkObjId="g_1d24590_0" Pin0InfoVect2LinkObjId="SW-46943_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a9c0d0_0" Pin1InfoVect1LinkObjId="g_1d24590_0" Pin1InfoVect2LinkObjId="SW-46943_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-188 4511,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18bde80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-127 4545,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b61fe0@0" ObjectIDZND0="8365@0" Pin0InfoVect0LinkObjId="SW-46943_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b61fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-127 4545,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cfab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-177 4545,-189 4511,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8365@1" ObjectIDZND0="g_1a9c0d0@0" ObjectIDZND1="g_1d24590@0" ObjectIDZND2="8364@x" Pin0InfoVect0LinkObjId="g_1a9c0d0_0" Pin0InfoVect1LinkObjId="g_1d24590_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46943_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-177 4545,-189 4511,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18b3ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-297 4511,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8363@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-297 4511,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24610a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-191 4511,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1a9c0d0@0" ObjectIDND1="g_1d24590@0" ObjectIDND2="8365@x" ObjectIDZND0="8364@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a9c0d0_0" Pin1InfoVect1LinkObjId="g_1d24590_0" Pin1InfoVect2LinkObjId="SW-46943_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-191 4511,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297b710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-223 4511,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8364@1" ObjectIDZND0="8763@0" Pin0InfoVect0LinkObjId="SW-46942_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-223 4511,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2276660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-264 4511,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8763@1" ObjectIDZND0="8363@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46942_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-264 4511,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2421350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4615,-189 4645,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1ecd420@0" ObjectIDZND0="g_1f7f920@0" ObjectIDZND1="8368@x" ObjectIDZND2="8367@x" Pin0InfoVect0LinkObjId="g_1f7f920_0" Pin0InfoVect1LinkObjId="SW-46949_0" Pin0InfoVect2LinkObjId="SW-46948_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ecd420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4615,-189 4645,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e1ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4645,-189 4645,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1ecd420@0" ObjectIDND1="8368@x" ObjectIDND2="8367@x" ObjectIDZND0="g_1f7f920@0" Pin0InfoVect0LinkObjId="g_1f7f920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ecd420_0" Pin1InfoVect1LinkObjId="SW-46949_0" Pin1InfoVect2LinkObjId="SW-46948_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4645,-189 4645,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2441b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4645,-108 4645,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1f7f920@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f7f920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4645,-108 4645,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ecdf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-188 4645,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1f7f920@0" ObjectIDND1="g_1ecd420@0" ObjectIDND2="8368@x" ObjectIDZND0="g_1f7f920@0" ObjectIDZND1="g_1ecd420@0" ObjectIDZND2="8368@x" Pin0InfoVect0LinkObjId="g_1f7f920_0" Pin0InfoVect1LinkObjId="g_1ecd420_0" Pin0InfoVect2LinkObjId="SW-46949_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f7f920_0" Pin1InfoVect1LinkObjId="g_1ecd420_0" Pin1InfoVect2LinkObjId="SW-46949_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-188 4645,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2397f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4679,-127 4679,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_18d2130@0" ObjectIDZND0="8368@0" Pin0InfoVect0LinkObjId="SW-46949_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18d2130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4679,-127 4679,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff6a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4679,-177 4679,-189 4645,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8368@1" ObjectIDZND0="g_1f7f920@0" ObjectIDZND1="g_1ecd420@0" ObjectIDZND2="8367@x" Pin0InfoVect0LinkObjId="g_1f7f920_0" Pin0InfoVect1LinkObjId="g_1ecd420_0" Pin0InfoVect2LinkObjId="SW-46948_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46949_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4679,-177 4679,-189 4645,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4645,-297 4645,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8366@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46948_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4645,-297 4645,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f72b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4645,-191 4645,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1f7f920@0" ObjectIDND1="g_1ecd420@0" ObjectIDND2="8368@x" ObjectIDZND0="8367@0" Pin0InfoVect0LinkObjId="SW-46948_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f7f920_0" Pin1InfoVect1LinkObjId="g_1ecd420_0" Pin1InfoVect2LinkObjId="SW-46949_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4645,-191 4645,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c309d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4645,-223 4645,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8367@1" ObjectIDZND0="8762@0" Pin0InfoVect0LinkObjId="SW-46948_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46948_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4645,-223 4645,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22761c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4645,-264 4645,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8762@1" ObjectIDZND0="8366@1" Pin0InfoVect0LinkObjId="SW-46948_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46948_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4645,-264 4645,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae6c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4749,-189 4779,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_b3ef340@0" ObjectIDZND0="g_2219cf0@0" ObjectIDZND1="8371@x" ObjectIDZND2="8370@x" Pin0InfoVect0LinkObjId="g_2219cf0_0" Pin0InfoVect1LinkObjId="SW-46955_0" Pin0InfoVect2LinkObjId="SW-46954_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_b3ef340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4749,-189 4779,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_b3ef7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-189 4779,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_b3ef340@0" ObjectIDND1="8371@x" ObjectIDND2="8370@x" ObjectIDZND0="g_2219cf0@0" Pin0InfoVect0LinkObjId="g_2219cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_b3ef340_0" Pin1InfoVect1LinkObjId="SW-46955_0" Pin1InfoVect2LinkObjId="SW-46954_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-189 4779,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_b3ee800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-108 4779,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2219cf0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2219cf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-108 4779,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_b3ee140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-188 4779,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2219cf0@0" ObjectIDND1="g_b3ef340@0" ObjectIDND2="8371@x" ObjectIDZND0="g_2219cf0@0" ObjectIDZND1="g_b3ef340@0" ObjectIDZND2="8371@x" Pin0InfoVect0LinkObjId="g_2219cf0_0" Pin0InfoVect1LinkObjId="g_b3ef340_0" Pin0InfoVect2LinkObjId="SW-46955_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2219cf0_0" Pin1InfoVect1LinkObjId="g_b3ef340_0" Pin1InfoVect2LinkObjId="SW-46955_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-188 4779,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2127e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4813,-127 4813,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_18dd3c0@0" ObjectIDZND0="8371@0" Pin0InfoVect0LinkObjId="SW-46955_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18dd3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4813,-127 4813,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2168d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4813,-177 4813,-189 4779,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8371@1" ObjectIDZND0="g_2219cf0@0" ObjectIDZND1="g_b3ef340@0" ObjectIDZND2="8370@x" Pin0InfoVect0LinkObjId="g_2219cf0_0" Pin0InfoVect1LinkObjId="g_b3ef340_0" Pin0InfoVect2LinkObjId="SW-46954_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46955_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4813,-177 4813,-189 4779,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19eaea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-297 4779,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8369@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46954_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-297 4779,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2690690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-191 4779,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2219cf0@0" ObjectIDND1="g_b3ef340@0" ObjectIDND2="8371@x" ObjectIDZND0="8370@0" Pin0InfoVect0LinkObjId="SW-46954_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2219cf0_0" Pin1InfoVect1LinkObjId="g_b3ef340_0" Pin1InfoVect2LinkObjId="SW-46955_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-191 4779,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ebc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-223 4779,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8370@1" ObjectIDZND0="8766@0" Pin0InfoVect0LinkObjId="SW-46954_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46954_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-223 4779,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25853f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-264 4779,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8766@1" ObjectIDZND0="8369@1" Pin0InfoVect0LinkObjId="SW-46954_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46954_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-264 4779,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_256c430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4883,-189 4913,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2692350@0" ObjectIDZND0="g_2687680@0" ObjectIDZND1="8374@x" ObjectIDZND2="8373@x" Pin0InfoVect0LinkObjId="g_2687680_0" Pin0InfoVect1LinkObjId="SW-46962_0" Pin0InfoVect2LinkObjId="SW-46961_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2692350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4883,-189 4913,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2696870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-189 4913,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2692350@0" ObjectIDND1="8374@x" ObjectIDND2="8373@x" ObjectIDZND0="g_2687680@0" Pin0InfoVect0LinkObjId="g_2687680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2692350_0" Pin1InfoVect1LinkObjId="SW-46962_0" Pin1InfoVect2LinkObjId="SW-46961_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-189 4913,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1838c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-108 4913,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2687680@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2687680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-108 4913,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26900c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-188 4913,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2687680@0" ObjectIDND1="g_2692350@0" ObjectIDND2="8374@x" ObjectIDZND0="g_2687680@0" ObjectIDZND1="g_2692350@0" ObjectIDZND2="8374@x" Pin0InfoVect0LinkObjId="g_2687680_0" Pin0InfoVect1LinkObjId="g_2692350_0" Pin0InfoVect2LinkObjId="SW-46962_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2687680_0" Pin1InfoVect1LinkObjId="g_2692350_0" Pin1InfoVect2LinkObjId="SW-46962_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-188 4913,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2689910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-127 4947,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25a2ae0@0" ObjectIDZND0="8374@0" Pin0InfoVect0LinkObjId="SW-46962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a2ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-127 4947,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_267d020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-177 4947,-189 4913,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8374@1" ObjectIDZND0="g_2687680@0" ObjectIDZND1="g_2692350@0" ObjectIDZND2="8373@x" Pin0InfoVect0LinkObjId="g_2687680_0" Pin0InfoVect1LinkObjId="g_2692350_0" Pin0InfoVect2LinkObjId="SW-46961_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-177 4947,-189 4913,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2691ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-297 4913,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8372@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46961_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-297 4913,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2595d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-191 4913,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2687680@0" ObjectIDND1="g_2692350@0" ObjectIDND2="8374@x" ObjectIDZND0="8373@0" Pin0InfoVect0LinkObjId="SW-46961_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2687680_0" Pin1InfoVect1LinkObjId="g_2692350_0" Pin1InfoVect2LinkObjId="SW-46962_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-191 4913,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26743c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-223 4913,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8373@1" ObjectIDZND0="8767@0" Pin0InfoVect0LinkObjId="SW-46961_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46961_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-223 4913,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a7afb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-264 4913,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8767@1" ObjectIDZND0="8372@1" Pin0InfoVect0LinkObjId="SW-46961_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46961_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-264 4913,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2582ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-189 5047,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_26e08b0@0" ObjectIDZND0="g_22d4340@0" ObjectIDZND1="8347@x" ObjectIDZND2="8346@x" Pin0InfoVect0LinkObjId="g_22d4340_0" Pin0InfoVect1LinkObjId="SW-46903_0" Pin0InfoVect2LinkObjId="SW-46902_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e08b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-189 5047,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2575f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-189 5047,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_26e08b0@0" ObjectIDND1="8347@x" ObjectIDND2="8346@x" ObjectIDZND0="g_22d4340@0" Pin0InfoVect0LinkObjId="g_22d4340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26e08b0_0" Pin1InfoVect1LinkObjId="SW-46903_0" Pin1InfoVect2LinkObjId="SW-46902_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-189 5047,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_256fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-108 5047,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_22d4340@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22d4340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-108 5047,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa1f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-188 5047,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_22d4340@0" ObjectIDND1="g_26e08b0@0" ObjectIDND2="8347@x" ObjectIDZND0="g_22d4340@0" ObjectIDZND1="g_26e08b0@0" ObjectIDZND2="8347@x" Pin0InfoVect0LinkObjId="g_22d4340_0" Pin0InfoVect1LinkObjId="g_26e08b0_0" Pin0InfoVect2LinkObjId="SW-46903_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22d4340_0" Pin1InfoVect1LinkObjId="g_26e08b0_0" Pin1InfoVect2LinkObjId="SW-46903_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-188 5047,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a78ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-127 5081,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22890a0@0" ObjectIDZND0="8347@0" Pin0InfoVect0LinkObjId="SW-46903_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22890a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-127 5081,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b4d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-177 5081,-189 5047,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8347@1" ObjectIDZND0="g_22d4340@0" ObjectIDZND1="g_26e08b0@0" ObjectIDZND2="8346@x" Pin0InfoVect0LinkObjId="g_22d4340_0" Pin0InfoVect1LinkObjId="g_26e08b0_0" Pin0InfoVect2LinkObjId="SW-46902_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46903_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-177 5081,-189 5047,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23b0eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-297 5047,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8345@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46902_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-297 5047,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a76320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-191 5047,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_22d4340@0" ObjectIDND1="g_26e08b0@0" ObjectIDND2="8347@x" ObjectIDZND0="8346@0" Pin0InfoVect0LinkObjId="SW-46902_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22d4340_0" Pin1InfoVect1LinkObjId="g_26e08b0_0" Pin1InfoVect2LinkObjId="SW-46903_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-191 5047,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b0060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-223 5047,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8346@1" ObjectIDZND0="8765@0" Pin0InfoVect0LinkObjId="SW-46902_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46902_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-223 5047,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1f590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5047,-264 5047,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8765@1" ObjectIDZND0="8345@1" Pin0InfoVect0LinkObjId="SW-46902_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46902_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5047,-264 5047,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29778c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5151,-189 5181,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1b709e0@0" ObjectIDZND0="g_1eec340@0" ObjectIDZND1="8380@x" ObjectIDZND2="8379@x" Pin0InfoVect0LinkObjId="g_1eec340_0" Pin0InfoVect1LinkObjId="SW-46975_0" Pin0InfoVect2LinkObjId="SW-46974_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b709e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5151,-189 5181,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f48210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-189 5181,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1b709e0@0" ObjectIDND1="8380@x" ObjectIDND2="8379@x" ObjectIDZND0="g_1eec340@0" Pin0InfoVect0LinkObjId="g_1eec340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b709e0_0" Pin1InfoVect1LinkObjId="SW-46975_0" Pin1InfoVect2LinkObjId="SW-46974_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-189 5181,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b43730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-108 5181,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1eec340@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eec340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-108 5181,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2931100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5182,-188 5181,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1eec340@0" ObjectIDND1="g_1b709e0@0" ObjectIDND2="8380@x" ObjectIDZND0="g_1eec340@0" ObjectIDZND1="g_1b709e0@0" ObjectIDZND2="8380@x" Pin0InfoVect0LinkObjId="g_1eec340_0" Pin0InfoVect1LinkObjId="g_1b709e0_0" Pin0InfoVect2LinkObjId="SW-46975_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1eec340_0" Pin1InfoVect1LinkObjId="g_1b709e0_0" Pin1InfoVect2LinkObjId="SW-46975_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5182,-188 5181,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae3b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5215,-127 5215,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_246b030@0" ObjectIDZND0="8380@0" Pin0InfoVect0LinkObjId="SW-46975_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_246b030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5215,-127 5215,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_300fa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5215,-177 5215,-189 5181,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8380@1" ObjectIDZND0="g_1eec340@0" ObjectIDZND1="g_1b709e0@0" ObjectIDZND2="8379@x" Pin0InfoVect0LinkObjId="g_1eec340_0" Pin0InfoVect1LinkObjId="g_1b709e0_0" Pin0InfoVect2LinkObjId="SW-46974_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46975_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5215,-177 5215,-189 5181,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21a7530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-297 5181,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8378@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46974_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-297 5181,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_184e5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-191 5181,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1eec340@0" ObjectIDND1="g_1b709e0@0" ObjectIDND2="8380@x" ObjectIDZND0="8379@0" Pin0InfoVect0LinkObjId="SW-46974_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1eec340_0" Pin1InfoVect1LinkObjId="g_1b709e0_0" Pin1InfoVect2LinkObjId="SW-46975_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-191 5181,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2599890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-223 5181,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8379@1" ObjectIDZND0="8770@0" Pin0InfoVect0LinkObjId="SW-46974_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46974_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-223 5181,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2430400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5181,-264 5181,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8770@1" ObjectIDZND0="8378@1" Pin0InfoVect0LinkObjId="SW-46974_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46974_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5181,-264 5181,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c97c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-191 3840,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d682c0@0" ObjectIDND1="g_23b7220@0" ObjectIDND2="8353@x" ObjectIDZND0="8352@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d682c0_0" Pin1InfoVect1LinkObjId="g_23b7220_0" Pin1InfoVect2LinkObjId="SW-46917_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-191 3840,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a99a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-298 3840,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8351@0" ObjectIDZND0="8338@0" Pin0InfoVect0LinkObjId="g_29f0c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-298 3840,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18cc520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-224 3840,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8352@1" ObjectIDZND0="8769@0" Pin0InfoVect0LinkObjId="SW-46916_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-224 3840,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_186a300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-265 3840,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8769@1" ObjectIDZND0="8351@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-265 3840,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25870a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-1052 5166,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1340@0" ObjectIDZND0="1399@1" Pin0InfoVect0LinkObjId="SW-9094_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-1052 5166,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a1430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-1052 5104,-1052 5104,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1399@0" ObjectIDZND0="1400@1" Pin0InfoVect0LinkObjId="SW-9100_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9094_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-1052 5104,-1052 5104,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24617a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-938 5166,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1339@0" ObjectIDZND0="1398@1" Pin0InfoVect0LinkObjId="SW-9093_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-938 5166,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ae2ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-938 5104,-938 5104,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1398@0" ObjectIDZND0="1400@0" Pin0InfoVect0LinkObjId="SW-9100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9093_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-938 5104,-938 5104,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16e6780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5068,-845 5042,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1459@0" ObjectIDZND0="1461@1" Pin0InfoVect0LinkObjId="SW-9385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5068,-845 5042,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf48c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-845 4974,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="1461@0" ObjectIDZND0="g_24b41a0@0" ObjectIDZND1="g_2abd110@0" ObjectIDZND2="g_1eb3720@0" Pin0InfoVect0LinkObjId="g_24b41a0_0" Pin0InfoVect1LinkObjId="g_2abd110_0" Pin0InfoVect2LinkObjId="g_1eb3720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-845 4974,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23a9fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-845 5169,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1339@0" ObjectIDZND0="1460@1" Pin0InfoVect0LinkObjId="SW-9384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-845 5169,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eeb1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5133,-845 5102,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1460@0" ObjectIDZND0="1459@1" Pin0InfoVect0LinkObjId="SW-9382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5133,-845 5102,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2027840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5200,-1149 5166,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1340@0" ObjectIDZND0="1463@1" Pin0InfoVect0LinkObjId="SW-9398_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5200,-1149 5166,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1abdb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-1149 5102,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1463@0" ObjectIDZND0="1462@1" Pin0InfoVect0LinkObjId="SW-9396_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-1149 5102,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c274a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5068,-1149 5039,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1462@0" ObjectIDZND0="1464@1" Pin0InfoVect0LinkObjId="SW-9399_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9396_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5068,-1149 5039,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_299ad10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4375,-1149 4375,-1134 3775,-1134 3775,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_26725d0@0" ObjectIDND1="g_1a91120@0" ObjectIDND2="1464@x" ObjectIDZND0="g_228f340@0" Pin0InfoVect0LinkObjId="g_228f340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26725d0_0" Pin1InfoVect1LinkObjId="g_1a91120_0" Pin1InfoVect2LinkObjId="SW-9399_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4375,-1149 4375,-1134 3775,-1134 3775,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2573960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4375,-1149 4335,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="g_228f340@0" ObjectIDND1="g_26725d0@0" ObjectIDND2="g_1a91120@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_228f340_0" Pin1InfoVect1LinkObjId="g_26725d0_0" Pin1InfoVect2LinkObjId="g_1a91120_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4375,-1149 4335,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b66dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-647 4096,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1c1f9c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c1f9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-647 4096,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21a1d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-551 4096,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-551 4096,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20fd8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-53 3621,-53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2966c10@0" Pin0InfoVect0LinkObjId="g_2966c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-53 3621,-53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d4e8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-53 3553,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="10520@0" Pin0InfoVect0LinkObjId="CB-CX_KGYB.CX_KGYB_1drq_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-53 3553,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f769c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3566,-53 3553,-53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="10520@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="CB-CX_KGYB.CX_KGYB_1drq_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3566,-53 3553,-53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f76bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,-53 3553,-62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10520@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_KGYB.CX_KGYB_1drq_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3553,-53 3553,-62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2694050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3553,100 3553,78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3553,100 3553,78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2414ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-497 3777,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1bb2eb0@0" Pin0InfoVect0LinkObjId="g_1bb2eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-497 3777,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2414d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3777,-439 3777,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1bb2eb0@1" ObjectIDZND0="g_1857aa0@0" ObjectIDZND1="8387@x" Pin0InfoVect0LinkObjId="g_1857aa0_0" Pin0InfoVect1LinkObjId="SW-47029_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bb2eb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3777,-439 3777,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21e69a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-1193 4962,-1193 4962,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_1a91120@0" ObjectIDZND0="1464@x" ObjectIDZND1="g_18375f0@0" ObjectIDZND2="g_26725d0@0" Pin0InfoVect0LinkObjId="SW-9399_0" Pin0InfoVect1LinkObjId="g_18375f0_0" Pin0InfoVect2LinkObjId="g_26725d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a91120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-1193 4962,-1193 4962,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2671e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5003,-1149 4962,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="1464@0" ObjectIDZND0="g_1a91120@0" ObjectIDZND1="g_18375f0@0" ObjectIDZND2="g_26725d0@0" Pin0InfoVect0LinkObjId="g_1a91120_0" Pin0InfoVect1LinkObjId="g_18375f0_0" Pin0InfoVect2LinkObjId="g_26725d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-9399_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5003,-1149 4962,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe9ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4962,-1149 4962,-1070 4942,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1a91120@0" ObjectIDND1="1464@x" ObjectIDND2="g_26725d0@0" ObjectIDZND0="g_18375f0@0" Pin0InfoVect0LinkObjId="g_18375f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a91120_0" Pin1InfoVect1LinkObjId="SW-9399_0" Pin1InfoVect2LinkObjId="g_26725d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4962,-1149 4962,-1070 4942,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b00690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4815,-1069 4828,-1069 4828,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26725d0@0" ObjectIDZND0="g_1a91120@0" ObjectIDZND1="1464@x" ObjectIDZND2="g_18375f0@0" Pin0InfoVect0LinkObjId="g_1a91120_0" Pin0InfoVect1LinkObjId="SW-9399_0" Pin0InfoVect2LinkObjId="g_18375f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26725d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4815,-1069 4828,-1069 4828,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f53470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4962,-1149 4828,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_1a91120@0" ObjectIDND1="1464@x" ObjectIDND2="g_18375f0@0" ObjectIDZND0="g_26725d0@0" ObjectIDZND1="g_15c7f50@0" ObjectIDZND2="g_228f340@0" Pin0InfoVect0LinkObjId="g_26725d0_0" Pin0InfoVect1LinkObjId="g_15c7f50_0" Pin0InfoVect2LinkObjId="g_228f340_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a91120_0" Pin1InfoVect1LinkObjId="SW-9399_0" Pin1InfoVect2LinkObjId="g_18375f0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4962,-1149 4828,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2577980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-1149 4617,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_26725d0@0" ObjectIDND1="g_1a91120@0" ObjectIDND2="1464@x" ObjectIDZND0="g_15c7f50@0" ObjectIDZND1="g_228f340@0" Pin0InfoVect0LinkObjId="g_15c7f50_0" Pin0InfoVect1LinkObjId="g_228f340_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26725d0_0" Pin1InfoVect1LinkObjId="g_1a91120_0" Pin1InfoVect2LinkObjId="SW-9399_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-1149 4617,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b5fcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-1149 4617,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_15c7f50@0" ObjectIDZND0="g_26725d0@0" ObjectIDZND1="g_1a91120@0" ObjectIDZND2="1464@x" Pin0InfoVect0LinkObjId="g_26725d0_0" Pin0InfoVect1LinkObjId="g_1a91120_0" Pin0InfoVect2LinkObjId="SW-9399_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c7f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-1149 4617,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe8a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4375,-1149 4617,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_228f340@0" ObjectIDZND0="g_26725d0@0" ObjectIDZND1="g_1a91120@0" ObjectIDZND2="1464@x" Pin0InfoVect0LinkObjId="g_26725d0_0" Pin0InfoVect1LinkObjId="g_1a91120_0" Pin0InfoVect2LinkObjId="SW-9399_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_228f340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4375,-1149 4617,-1149 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_KGYB"/>
</svg>