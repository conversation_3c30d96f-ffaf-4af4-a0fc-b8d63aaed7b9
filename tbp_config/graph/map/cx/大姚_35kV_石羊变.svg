<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-234" aopId="3934726" id="thSvg" product="E8000V2" version="1.0" viewBox="-754 -1325 2384 1219">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape189">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="13" y1="21" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="13,64 38,64 38,35 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="13" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="32" x2="44" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="41" x2="35" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="39" x2="37" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="0"/>
    <circle cx="13" cy="66" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="80" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="84" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="88" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="60" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="64" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="68" y2="64"/>
    <ellipse cx="13" cy="82" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="voltageTransformer:shape112">
    <circle cx="32" cy="16" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="5" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="9" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="32" x2="35" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="29" x2="32" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="32" x2="32" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="23" y2="20"/>
    <circle cx="8" cy="16" fillStyle="0" r="7.5" stroke-width="0.536731"/>
    <circle cx="20" cy="9" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="20" cy="23" fillStyle="0" r="8" stroke-width="0.570276"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape63">
    <ellipse cx="19" cy="18" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="21" x2="21" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="18" x2="21" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="21" x2="23" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="20" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="17" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="22" y1="19" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="8" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="5" x2="8" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="10" y1="19" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="6" x2="9" y1="6" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.103806" x1="6" x2="9" y1="9" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="9" x2="9" y1="5" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="24" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="52" y2="52"/>
    <ellipse cx="8" cy="8" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="19" cy="8" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="8" cy="17" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <rect height="13" stroke-width="1" width="7" x="16" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="42" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="25" y1="33" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="31" y1="39" y2="39"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22d9970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22dab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22db600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22dc1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22dd430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22ddf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22deb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22df600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1b5c130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1b5c130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e29b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e29b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e4380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e4380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_22e5170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e6c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22e78c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22e8730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22e8e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22ea700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22eaef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22eb550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22ebce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22ecdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22ed7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22ee290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22eec50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22ef3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f0eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f1e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22f2b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22f9040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f9b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_22f3a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_22f4ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1229" width="2394" x="-759" y="-1330"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1628" x2="1628" y1="-541" y2="-528"/>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1275,-296 1287,-287 1275,-278 1275,-296 " stroke="rgb(0,255,0)" stroke-width="2"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1361,-366 1370,-378 1379,-366 1361,-366 " stroke="rgb(0,255,0)" stroke-width="2"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-184061">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.241796 -1010.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27986" ObjectName="SW-DY_SY.DY_SY_331BK"/>
     <cge:Meas_Ref ObjectId="184061"/>
    <cge:TPSR_Ref TObjectID="27986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184106">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.241796 -754.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27996" ObjectName="SW-DY_SY.DY_SY_301BK"/>
     <cge:Meas_Ref ObjectId="184106"/>
    <cge:TPSR_Ref TObjectID="27996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184116">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.241796 -592.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27999" ObjectName="SW-DY_SY.DY_SY_001BK"/>
     <cge:Meas_Ref ObjectId="184116"/>
    <cge:TPSR_Ref TObjectID="27999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184259">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.212271 -358.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28004" ObjectName="SW-DY_SY.DY_SY_033BK"/>
     <cge:Meas_Ref ObjectId="184259"/>
    <cge:TPSR_Ref TObjectID="28004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184289">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.212271 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28007" ObjectName="SW-DY_SY.DY_SY_034BK"/>
     <cge:Meas_Ref ObjectId="184289"/>
    <cge:TPSR_Ref TObjectID="28007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 84.212271 -344.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28010" ObjectName="SW-DY_SY.DY_SY_035BK"/>
     <cge:Meas_Ref ObjectId="184323"/>
    <cge:TPSR_Ref TObjectID="28010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.212271 -355.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28016" ObjectName="SW-DY_SY.DY_SY_038BK"/>
     <cge:Meas_Ref ObjectId="184391"/>
    <cge:TPSR_Ref TObjectID="28016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184357">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.212271 -362.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28013" ObjectName="SW-DY_SY.DY_SY_036BK"/>
     <cge:Meas_Ref ObjectId="184357"/>
    <cge:TPSR_Ref TObjectID="28013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235987">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.241796 -1012.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39390" ObjectName="SW-DY_SY.DY_SY_332BK"/>
     <cge:Meas_Ref ObjectId="235987"/>
    <cge:TPSR_Ref TObjectID="39390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235989">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.241796 -772.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39394" ObjectName="SW-DY_SY.DY_SY_302BK"/>
     <cge:Meas_Ref ObjectId="235989"/>
    <cge:TPSR_Ref TObjectID="39394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235988">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.241796 -563.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39397" ObjectName="SW-DY_SY.DY_SY_002BK"/>
     <cge:Meas_Ref ObjectId="235988"/>
    <cge:TPSR_Ref TObjectID="39397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184156">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 616.000000 -579.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39400" ObjectName="SW-DY_SY.DY_SY_012BK"/>
     <cge:Meas_Ref ObjectId="184156"/>
    <cge:TPSR_Ref TObjectID="39400"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c210b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.000000 -1108.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c4c820">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -38.000000 -690.000000)" xlink:href="#voltageTransformer:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bdeaf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.000000 -1214.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a3d950">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1235.000000 -1182.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b49480">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1305.000000 -693.000000)" xlink:href="#voltageTransformer:shape63"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_SY" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shiyangT" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="266,-1249 266,-1288 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38091" ObjectName="AC-35kV.LN_shiyangT"/>
    <cge:TPSR_Ref TObjectID="38091_SS-234"/></metadata>
   <polyline fill="none" opacity="0" points="266,-1249 266,-1288 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" lineStyle="2" points="-152,-498 -152,-225 " stroke-dasharray="10 5 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="-152,-498 -152,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" lineStyle="2" points="-56,-498 -56,-225 " stroke-dasharray="10 5 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="-56,-498 -56,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" lineStyle="2" points="1118,-500 1118,-227 " stroke-dasharray="10 5 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1118,-500 1118,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" lineStyle="2" points="1451,-497 1451,-224 " stroke-dasharray="10 5 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1451,-497 1451,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_SY" endPointId="0" endStationName="CX_DGY" flowDrawDirect="1" flowShape="0" id="AC-35kV.gushi_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1200,-1254 1200,-1293 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43360" ObjectName="AC-35kV.gushi_line"/>
    <cge:TPSR_Ref TObjectID="43360_SS-234"/></metadata>
   <polyline fill="none" opacity="0" points="1200,-1254 1200,-1293 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 -202.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 -202.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_SY.DY_SY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="39698"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 152.000000 -641.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 152.000000 -641.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28019" ObjectName="TF-DY_SY.DY_SY_1T"/>
    <cge:TPSR_Ref TObjectID="28019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_SY.DY_SY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="59231"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.000000 -603.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.000000 -603.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39406" ObjectName="TF-DY_SY.DY_SY_2T"/>
    <cge:TPSR_Ref TObjectID="39406"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ba0000">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 642.000000 -1049.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ba0860">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 605.000000 -1059.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c3d7e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 352.676442 -204.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b57970">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 855.676442 -202.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1baeb80">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 121.676442 -190.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c17750">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1296.676442 -202.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ba6a10">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1019.676442 -208.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1befc80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -75.000000 -595.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ba4b30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 157.000000 -1012.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bdd1a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.000000 -1204.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a3cba0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 -1169.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a433e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1190.000000 -1193.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b07b60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1066.000000 -724.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b08740">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1117.000000 -709.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b47e40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1262.000000 -595.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -657.000000 -1196.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237619" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -576.000000 -1022.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237619" ObjectName="DY_SY:DY_SYsum_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237620" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -575.000000 -978.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237620" ObjectName="DY_SY:DY_SYsum_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237619" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -577.000000 -1101.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237619" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-183981" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -575.000000 -1057.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183981" ObjectName="DY_SY:DY_SY_301BK_P"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184029" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 91.000000 -154.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184029" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28010"/>
     <cge:Term_Ref ObjectID="39678"/>
    <cge:TPSR_Ref TObjectID="28010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 91.000000 -154.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28010"/>
     <cge:Term_Ref ObjectID="39678"/>
    <cge:TPSR_Ref TObjectID="28010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184026" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 91.000000 -154.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28010"/>
     <cge:Term_Ref ObjectID="39678"/>
    <cge:TPSR_Ref TObjectID="28010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184017" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -154.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184017" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28004"/>
     <cge:Term_Ref ObjectID="39666"/>
    <cge:TPSR_Ref TObjectID="28004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -154.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28004"/>
     <cge:Term_Ref ObjectID="39666"/>
    <cge:TPSR_Ref TObjectID="28004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184014" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -154.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28004"/>
     <cge:Term_Ref ObjectID="39666"/>
    <cge:TPSR_Ref TObjectID="28004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184023" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -151.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184023" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28007"/>
     <cge:Term_Ref ObjectID="39672"/>
    <cge:TPSR_Ref TObjectID="28007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184024" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -151.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184024" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28007"/>
     <cge:Term_Ref ObjectID="39672"/>
    <cge:TPSR_Ref TObjectID="28007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184020" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -151.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184020" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28007"/>
     <cge:Term_Ref ObjectID="39672"/>
    <cge:TPSR_Ref TObjectID="28007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184035" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -151.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184035" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28013"/>
     <cge:Term_Ref ObjectID="39684"/>
    <cge:TPSR_Ref TObjectID="28013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184036" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -151.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184036" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28013"/>
     <cge:Term_Ref ObjectID="39684"/>
    <cge:TPSR_Ref TObjectID="28013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -151.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28013"/>
     <cge:Term_Ref ObjectID="39684"/>
    <cge:TPSR_Ref TObjectID="28013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-184041" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1274.000000 -151.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28016"/>
     <cge:Term_Ref ObjectID="39690"/>
    <cge:TPSR_Ref TObjectID="28016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-184042" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1274.000000 -151.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184042" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28016"/>
     <cge:Term_Ref ObjectID="39690"/>
    <cge:TPSR_Ref TObjectID="28016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-184038" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1274.000000 -151.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28016"/>
     <cge:Term_Ref ObjectID="39690"/>
    <cge:TPSR_Ref TObjectID="28016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236041" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -602.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39397"/>
     <cge:Term_Ref ObjectID="59218"/>
    <cge:TPSR_Ref TObjectID="39397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -602.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39397"/>
     <cge:Term_Ref ObjectID="59218"/>
    <cge:TPSR_Ref TObjectID="39397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-236044" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -602.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39397"/>
     <cge:Term_Ref ObjectID="59218"/>
    <cge:TPSR_Ref TObjectID="39397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183987" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -631.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27999"/>
     <cge:Term_Ref ObjectID="39656"/>
    <cge:TPSR_Ref TObjectID="27999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183988" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -631.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27999"/>
     <cge:Term_Ref ObjectID="39656"/>
    <cge:TPSR_Ref TObjectID="27999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -631.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27999"/>
     <cge:Term_Ref ObjectID="39656"/>
    <cge:TPSR_Ref TObjectID="27999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236024" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 -673.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236024" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39400"/>
     <cge:Term_Ref ObjectID="59222"/>
    <cge:TPSR_Ref TObjectID="39400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236023" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 -673.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236023" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39400"/>
     <cge:Term_Ref ObjectID="59222"/>
    <cge:TPSR_Ref TObjectID="39400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-236027" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 -673.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236027" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39400"/>
     <cge:Term_Ref ObjectID="59222"/>
    <cge:TPSR_Ref TObjectID="39400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -811.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27996"/>
     <cge:Term_Ref ObjectID="39650"/>
    <cge:TPSR_Ref TObjectID="27996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -811.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27996"/>
     <cge:Term_Ref ObjectID="39650"/>
    <cge:TPSR_Ref TObjectID="27996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -811.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27996"/>
     <cge:Term_Ref ObjectID="39650"/>
    <cge:TPSR_Ref TObjectID="27996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236081" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -803.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39394"/>
     <cge:Term_Ref ObjectID="59212"/>
    <cge:TPSR_Ref TObjectID="39394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236074" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -803.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39394"/>
     <cge:Term_Ref ObjectID="59212"/>
    <cge:TPSR_Ref TObjectID="39394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-236075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -803.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39394"/>
     <cge:Term_Ref ObjectID="59212"/>
    <cge:TPSR_Ref TObjectID="39394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.000000 -1052.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39390"/>
     <cge:Term_Ref ObjectID="59204"/>
    <cge:TPSR_Ref TObjectID="39390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.000000 -1052.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39390"/>
     <cge:Term_Ref ObjectID="59204"/>
    <cge:TPSR_Ref TObjectID="39390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-236065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.000000 -1052.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39390"/>
     <cge:Term_Ref ObjectID="59204"/>
    <cge:TPSR_Ref TObjectID="39390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 425.000000 -1048.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27986"/>
     <cge:Term_Ref ObjectID="39630"/>
    <cge:TPSR_Ref TObjectID="27986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 425.000000 -1048.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27986"/>
     <cge:Term_Ref ObjectID="39630"/>
    <cge:TPSR_Ref TObjectID="27986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 425.000000 -1048.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27986"/>
     <cge:Term_Ref ObjectID="39630"/>
    <cge:TPSR_Ref TObjectID="27986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-183992" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -1030.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27983"/>
     <cge:Term_Ref ObjectID="39626"/>
    <cge:TPSR_Ref TObjectID="27983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-183993" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -1030.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27983"/>
     <cge:Term_Ref ObjectID="39626"/>
    <cge:TPSR_Ref TObjectID="27983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-183994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -1030.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27983"/>
     <cge:Term_Ref ObjectID="39626"/>
    <cge:TPSR_Ref TObjectID="27983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-183998" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -1030.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27983"/>
     <cge:Term_Ref ObjectID="39626"/>
    <cge:TPSR_Ref TObjectID="27983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-183995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -1030.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27983"/>
     <cge:Term_Ref ObjectID="39626"/>
    <cge:TPSR_Ref TObjectID="27983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-183999" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -1030.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27983"/>
     <cge:Term_Ref ObjectID="39626"/>
    <cge:TPSR_Ref TObjectID="27983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-236034" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -631.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236034" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39404"/>
     <cge:Term_Ref ObjectID="59228"/>
    <cge:TPSR_Ref TObjectID="39404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-236033" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -631.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236033" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39404"/>
     <cge:Term_Ref ObjectID="59228"/>
    <cge:TPSR_Ref TObjectID="39404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-236032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -631.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39404"/>
     <cge:Term_Ref ObjectID="59228"/>
    <cge:TPSR_Ref TObjectID="39404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-236031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -631.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39404"/>
     <cge:Term_Ref ObjectID="59228"/>
    <cge:TPSR_Ref TObjectID="39404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-236030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -631.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39404"/>
     <cge:Term_Ref ObjectID="59228"/>
    <cge:TPSR_Ref TObjectID="39404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-236285" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -631.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39404"/>
     <cge:Term_Ref ObjectID="59228"/>
    <cge:TPSR_Ref TObjectID="39404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-184000" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -180.000000 -648.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184000" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27984"/>
     <cge:Term_Ref ObjectID="39627"/>
    <cge:TPSR_Ref TObjectID="27984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-184001" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -180.000000 -648.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184001" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27984"/>
     <cge:Term_Ref ObjectID="39627"/>
    <cge:TPSR_Ref TObjectID="27984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-184002" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -180.000000 -648.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27984"/>
     <cge:Term_Ref ObjectID="39627"/>
    <cge:TPSR_Ref TObjectID="27984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-184006" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -180.000000 -648.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27984"/>
     <cge:Term_Ref ObjectID="39627"/>
    <cge:TPSR_Ref TObjectID="27984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-184003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -180.000000 -648.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27984"/>
     <cge:Term_Ref ObjectID="39627"/>
    <cge:TPSR_Ref TObjectID="27984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-184007" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -180.000000 -648.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="184007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27984"/>
     <cge:Term_Ref ObjectID="39627"/>
    <cge:TPSR_Ref TObjectID="27984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-183991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 357.000000 -711.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28019"/>
     <cge:Term_Ref ObjectID="39696"/>
    <cge:TPSR_Ref TObjectID="28019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-183990" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 357.000000 -711.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28019"/>
     <cge:Term_Ref ObjectID="39696"/>
    <cge:TPSR_Ref TObjectID="28019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-236091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 967.000000 -685.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39406"/>
     <cge:Term_Ref ObjectID="59229"/>
    <cge:TPSR_Ref TObjectID="39406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-236035" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 967.000000 -685.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236035" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39406"/>
     <cge:Term_Ref ObjectID="59229"/>
    <cge:TPSR_Ref TObjectID="39406"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-605" y="-1254"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-654" y="-1271"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-454" y="-1234"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-454" y="-1234"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-454" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-454" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-448" y="-1188"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-448" y="-1188"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="276" y="-1039"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="276" y="-1039"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1210" y="-1041"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1210" y="-1041"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="221" y="-696"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="221" y="-696"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1126" y="-689"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1126" y="-689"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="103" y="-373"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="103" y="-373"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="334" y="-387"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="334" y="-387"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="837" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="837" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1001" y="-391"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1001" y="-391"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1278" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1278" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="80" x="-701" y="-889"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="80" x="-701" y="-889"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="626" y="-613"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="626" y="-613"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-454" y="-1234"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-454" y="-1269"/></g>
   <g href="AVC石羊站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-448" y="-1188"/></g>
   <g href="35kV石羊变35kV石羊变T接线331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="276" y="-1039"/></g>
   <g href="35kV石羊变35kV古石线332断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1210" y="-1041"/></g>
   <g href="35kV石羊变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="221" y="-696"/></g>
   <g href="35kV石羊变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1126" y="-689"/></g>
   <g href="35kV石羊变10kV石西线035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="103" y="-373"/></g>
   <g href="35kV石羊变10kV城区Ⅰ回线033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="334" y="-387"/></g>
   <g href="35kV石羊变10kV石东线034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="837" y="-385"/></g>
   <g href="35kV石羊变10kV石南线036间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1001" y="-391"/></g>
   <g href="35kV石羊变10kV备用二线038间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1278" y="-385"/></g>
   <g href="35kV石羊变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="80" x="-701" y="-889"/></g>
   <g href="35kV石羊变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="626" y="-613"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="277,-1216 277,-1206 285,-1211 277,-1216 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="306,-1206 306,-1216 298,-1211 306,-1206 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-184063">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.241796 -1081.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27987" ObjectName="SW-DY_SY.DY_SY_3316SW"/>
     <cge:Meas_Ref ObjectId="184063"/>
    <cge:TPSR_Ref TObjectID="27987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.241796 -1061.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27990" ObjectName="SW-DY_SY.DY_SY_33160SW"/>
     <cge:Meas_Ref ObjectId="184066"/>
    <cge:TPSR_Ref TObjectID="27990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184064">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.241796 -918.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27988" ObjectName="SW-DY_SY.DY_SY_3311SW"/>
     <cge:Meas_Ref ObjectId="184064"/>
    <cge:TPSR_Ref TObjectID="27988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 294.241796 -978.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27991" ObjectName="SW-DY_SY.DY_SY_33117SW"/>
     <cge:Meas_Ref ObjectId="184067"/>
    <cge:TPSR_Ref TObjectID="27991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184065">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.241796 -1139.085366)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27989" ObjectName="SW-DY_SY.DY_SY_33167SW"/>
     <cge:Meas_Ref ObjectId="184065"/>
    <cge:TPSR_Ref TObjectID="27989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 164.971879 -1121.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184098">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 640.241796 -960.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27992" ObjectName="SW-DY_SY.DY_SY_3901SW"/>
     <cge:Meas_Ref ObjectId="184098"/>
    <cge:TPSR_Ref TObjectID="27992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184100">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.241796 -931.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27994" ObjectName="SW-DY_SY.DY_SY_39010SW"/>
     <cge:Meas_Ref ObjectId="184100"/>
    <cge:TPSR_Ref TObjectID="27994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184099">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 679.241796 -1015.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27993" ObjectName="SW-DY_SY.DY_SY_39017SW"/>
     <cge:Meas_Ref ObjectId="184099"/>
    <cge:TPSR_Ref TObjectID="27993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184108">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.241796 -836.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27997" ObjectName="SW-DY_SY.DY_SY_3011SW"/>
     <cge:Meas_Ref ObjectId="184108"/>
    <cge:TPSR_Ref TObjectID="27997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184109">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 224.241796 -811.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27998" ObjectName="SW-DY_SY.DY_SY_30117SW"/>
     <cge:Meas_Ref ObjectId="184109"/>
    <cge:TPSR_Ref TObjectID="27998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.241796 -530.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28000" ObjectName="SW-DY_SY.DY_SY_0011SW"/>
     <cge:Meas_Ref ObjectId="184118"/>
    <cge:TPSR_Ref TObjectID="28000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.761290 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28005" ObjectName="SW-DY_SY.DY_SY_0331SW"/>
     <cge:Meas_Ref ObjectId="184261"/>
    <cge:TPSR_Ref TObjectID="28005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.761290 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28006" ObjectName="SW-DY_SY.DY_SY_0336SW"/>
     <cge:Meas_Ref ObjectId="184262"/>
    <cge:TPSR_Ref TObjectID="28006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184291">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.761290 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28008" ObjectName="SW-DY_SY.DY_SY_0342SW"/>
     <cge:Meas_Ref ObjectId="184291"/>
    <cge:TPSR_Ref TObjectID="28008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184292">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.761290 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28009" ObjectName="SW-DY_SY.DY_SY_0346SW"/>
     <cge:Meas_Ref ObjectId="184292"/>
    <cge:TPSR_Ref TObjectID="28009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 83.761290 -414.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28011" ObjectName="SW-DY_SY.DY_SY_0351SW"/>
     <cge:Meas_Ref ObjectId="184325"/>
    <cge:TPSR_Ref TObjectID="28011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184326">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 83.761290 -272.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28012" ObjectName="SW-DY_SY.DY_SY_0356SW"/>
     <cge:Meas_Ref ObjectId="184326"/>
    <cge:TPSR_Ref TObjectID="28012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184393">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.761290 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28017" ObjectName="SW-DY_SY.DY_SY_0382SW"/>
     <cge:Meas_Ref ObjectId="184393"/>
    <cge:TPSR_Ref TObjectID="28017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184394">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.761290 -299.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28018" ObjectName="SW-DY_SY.DY_SY_0386SW"/>
     <cge:Meas_Ref ObjectId="184394"/>
    <cge:TPSR_Ref TObjectID="28018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 981.761290 -432.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28014" ObjectName="SW-DY_SY.DY_SY_0362SW"/>
     <cge:Meas_Ref ObjectId="184359"/>
    <cge:TPSR_Ref TObjectID="28014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 981.761290 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28015" ObjectName="SW-DY_SY.DY_SY_0366SW"/>
     <cge:Meas_Ref ObjectId="184360"/>
    <cge:TPSR_Ref TObjectID="28015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.971879 -309.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184101">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -27.238710 -533.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27995" ObjectName="SW-DY_SY.DY_SY_0901SW"/>
     <cge:Meas_Ref ObjectId="184101"/>
    <cge:TPSR_Ref TObjectID="27995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -23.028121 -625.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235973">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.241796 -1083.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39387" ObjectName="SW-DY_SY.DY_SY_3326SW"/>
     <cge:Meas_Ref ObjectId="235973"/>
    <cge:TPSR_Ref TObjectID="39387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235972">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1233.241796 -1063.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39389" ObjectName="SW-DY_SY.DY_SY_33260SW"/>
     <cge:Meas_Ref ObjectId="235972"/>
    <cge:TPSR_Ref TObjectID="39389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235975">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.241796 -920.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39391" ObjectName="SW-DY_SY.DY_SY_3321SW"/>
     <cge:Meas_Ref ObjectId="235975"/>
    <cge:TPSR_Ref TObjectID="39391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235974">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.241796 -977.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39392" ObjectName="SW-DY_SY.DY_SY_33217SW"/>
     <cge:Meas_Ref ObjectId="235974"/>
    <cge:TPSR_Ref TObjectID="39392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235962">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.241796 -1141.085366)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39388" ObjectName="SW-DY_SY.DY_SY_33267SW"/>
     <cge:Meas_Ref ObjectId="235962"/>
    <cge:TPSR_Ref TObjectID="39388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235977">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.241796 -845.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39395" ObjectName="SW-DY_SY.DY_SY_3021SW"/>
     <cge:Meas_Ref ObjectId="235977"/>
    <cge:TPSR_Ref TObjectID="39395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235985">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.241796 -512.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39398" ObjectName="SW-DY_SY.DY_SY_0022SW"/>
     <cge:Meas_Ref ObjectId="235985"/>
    <cge:TPSR_Ref TObjectID="39398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235976">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1108.241796 -823.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39396" ObjectName="SW-DY_SY.DY_SY_30217SW"/>
     <cge:Meas_Ref ObjectId="235976"/>
    <cge:TPSR_Ref TObjectID="39396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235979">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.761290 -533.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39393" ObjectName="SW-DY_SY.DY_SY_0902SW"/>
     <cge:Meas_Ref ObjectId="235979"/>
    <cge:TPSR_Ref TObjectID="39393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1313.971879 -625.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184157">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 -584.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39402" ObjectName="SW-DY_SY.DY_SY_0121SW"/>
     <cge:Meas_Ref ObjectId="184157"/>
    <cge:TPSR_Ref TObjectID="39402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-184158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 701.000000 -584.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39401" ObjectName="SW-DY_SY.DY_SY_0122SW"/>
     <cge:Meas_Ref ObjectId="184158"/>
    <cge:TPSR_Ref TObjectID="39401"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_SY.033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.761290 -203.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37969" ObjectName="EC-DY_SY.033Ld"/>
    <cge:TPSR_Ref TObjectID="37969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_SY.034Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.761290 -201.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37970" ObjectName="EC-DY_SY.034Ld"/>
    <cge:TPSR_Ref TObjectID="37970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_SY.035Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 83.761290 -197.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37971" ObjectName="EC-DY_SY.035Ld"/>
    <cge:TPSR_Ref TObjectID="37971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_SY.038Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.761290 -201.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44519" ObjectName="EC-DY_SY.038Ld"/>
    <cge:TPSR_Ref TObjectID="44519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_SY.036Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 981.761290 -207.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37972" ObjectName="EC-DY_SY.036Ld"/>
    <cge:TPSR_Ref TObjectID="37972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -157.000000 -204.000000)" xlink:href="#load:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -61.000000 -204.000000)" xlink:href="#load:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1113.000000 -206.000000)" xlink:href="#load:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 -203.000000)" xlink:href="#load:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1ba0670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="649,-1110 649,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1c210b0@0" ObjectIDZND0="g_1ba0000@1" Pin0InfoVect0LinkObjId="g_1ba0000_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c210b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="649,-1110 649,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b9c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-1063 612,-1041 649,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1ba0860@0" ObjectIDZND0="g_1ba0000@0" ObjectIDZND1="27992@x" ObjectIDZND2="27993@x" Pin0InfoVect0LinkObjId="g_1ba0000_0" Pin0InfoVect1LinkObjId="SW-184098_0" Pin0InfoVect2LinkObjId="SW-184099_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ba0860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="612,-1063 612,-1041 649,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b9c300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="649,-1041 649,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1ba0860@0" ObjectIDND1="27992@x" ObjectIDND2="27993@x" ObjectIDZND0="g_1ba0000@0" Pin0InfoVect0LinkObjId="g_1ba0000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ba0860_0" Pin1InfoVect1LinkObjId="SW-184098_0" Pin1InfoVect2LinkObjId="SW-184099_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="649,-1041 649,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf9b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="649,-1041 649,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ba0860@0" ObjectIDND1="g_1ba0000@0" ObjectIDZND0="27992@x" ObjectIDZND1="27993@x" Pin0InfoVect0LinkObjId="SW-184098_0" Pin0InfoVect1LinkObjId="SW-184099_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ba0860_0" Pin1InfoVect1LinkObjId="g_1ba0000_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="649,-1041 649,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf9cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="649,-1020 649,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1ba0860@0" ObjectIDND1="g_1ba0000@0" ObjectIDND2="27993@x" ObjectIDZND0="27992@1" Pin0InfoVect0LinkObjId="SW-184098_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ba0860_0" Pin1InfoVect1LinkObjId="g_1ba0000_0" Pin1InfoVect2LinkObjId="SW-184099_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="649,-1020 649,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfa540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="649,-965 649,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="27992@0" ObjectIDZND0="27983@0" ObjectIDZND1="27994@x" Pin0InfoVect0LinkObjId="g_1bfa730_0" Pin0InfoVect1LinkObjId="SW-184100_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184098_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="649,-965 649,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfa730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="649,-936 649,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="27992@x" ObjectIDND1="27994@x" ObjectIDZND0="27983@0" Pin0InfoVect0LinkObjId="g_1bfa540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-184098_0" Pin1InfoVect1LinkObjId="SW-184100_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="649,-936 649,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd29c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-841 191,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27997@0" ObjectIDZND0="27996@x" ObjectIDZND1="27998@x" Pin0InfoVect0LinkObjId="SW-184106_0" Pin0InfoVect1LinkObjId="SW-184109_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184108_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="191,-841 191,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd2bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-816 191,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27997@x" ObjectIDND1="27998@x" ObjectIDZND0="27996@1" Pin0InfoVect0LinkObjId="SW-184106_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-184108_0" Pin1InfoVect1LinkObjId="SW-184109_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-816 191,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd2da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-762 191,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="27996@0" ObjectIDZND0="28019@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-762 191,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd2f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-647 191,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="28019@0" ObjectIDZND0="27999@1" Pin0InfoVect0LinkObjId="SW-184116_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd2da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-647 191,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c57c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-600 191,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27999@0" ObjectIDZND0="28000@1" Pin0InfoVect0LinkObjId="SW-184118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184116_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-600 191,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c3e350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-433 324,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28005@0" ObjectIDZND0="28004@1" Pin0InfoVect0LinkObjId="SW-184259_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-433 324,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcbe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-258 360,-273 324,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1c3d7e0@0" ObjectIDZND0="28006@x" ObjectIDZND1="37969@x" Pin0InfoVect0LinkObjId="SW-184262_0" Pin0InfoVect1LinkObjId="EC-DY_SY.033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c3d7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="360,-258 360,-273 324,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcc090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-291 324,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28006@0" ObjectIDZND0="g_1c3d7e0@0" ObjectIDZND1="37969@x" Pin0InfoVect0LinkObjId="g_1c3d7e0_0" Pin0InfoVect1LinkObjId="EC-DY_SY.033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184262_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="324,-291 324,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcc2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-273 324,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1c3d7e0@0" ObjectIDND1="28006@x" ObjectIDZND0="37969@0" Pin0InfoVect0LinkObjId="EC-DY_SY.033Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c3d7e0_0" Pin1InfoVect1LinkObjId="SW-184262_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-273 324,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcc4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-366 324,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28004@0" ObjectIDZND0="28006@1" Pin0InfoVect0LinkObjId="SW-184262_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184259_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-366 324,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcc6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-469 324,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28005@1" ObjectIDZND0="27984@0" Pin0InfoVect0LinkObjId="g_1c06110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184261_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-469 324,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b584e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="827,-431 827,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28008@0" ObjectIDZND0="28007@1" Pin0InfoVect0LinkObjId="SW-184289_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="827,-431 827,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc7000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-256 863,-271 827,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1b57970@0" ObjectIDZND0="28009@x" ObjectIDZND1="37970@x" Pin0InfoVect0LinkObjId="SW-184292_0" Pin0InfoVect1LinkObjId="EC-DY_SY.034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b57970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="863,-256 863,-271 827,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc7220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="827,-289 827,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28009@0" ObjectIDZND0="g_1b57970@0" ObjectIDZND1="37970@x" Pin0InfoVect0LinkObjId="g_1b57970_0" Pin0InfoVect1LinkObjId="EC-DY_SY.034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="827,-289 827,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc7440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="827,-271 827,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1b57970@0" ObjectIDND1="28009@x" ObjectIDZND0="37970@0" Pin0InfoVect0LinkObjId="EC-DY_SY.034Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b57970_0" Pin1InfoVect1LinkObjId="SW-184292_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="827,-271 827,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc7660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="827,-364 827,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28007@0" ObjectIDZND0="28009@1" Pin0InfoVect0LinkObjId="SW-184292_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="827,-364 827,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1baf6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="93,-419 93,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28011@0" ObjectIDZND0="28010@1" Pin0InfoVect0LinkObjId="SW-184323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="93,-419 93,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c05890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="129,-244 129,-259 93,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1baeb80@0" ObjectIDZND0="28012@x" ObjectIDZND1="37971@x" Pin0InfoVect0LinkObjId="SW-184326_0" Pin0InfoVect1LinkObjId="EC-DY_SY.035Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1baeb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="129,-244 129,-259 93,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c05ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="93,-277 93,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28012@0" ObjectIDZND0="g_1baeb80@0" ObjectIDZND1="37971@x" Pin0InfoVect0LinkObjId="g_1baeb80_0" Pin0InfoVect1LinkObjId="EC-DY_SY.035Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="93,-277 93,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c05cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="93,-259 93,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1baeb80@0" ObjectIDND1="28012@x" ObjectIDZND0="37971@0" Pin0InfoVect0LinkObjId="EC-DY_SY.035Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1baeb80_0" Pin1InfoVect1LinkObjId="SW-184326_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="93,-259 93,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c05ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="93,-352 93,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28010@0" ObjectIDZND0="28012@1" Pin0InfoVect0LinkObjId="SW-184326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="93,-352 93,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c06110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="93,-455 93,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28011@1" ObjectIDZND0="27984@0" Pin0InfoVect0LinkObjId="g_1bcc6f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="93,-455 93,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c182c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1268,-431 1268,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28017@0" ObjectIDZND0="28016@1" Pin0InfoVect0LinkObjId="SW-184391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1268,-431 1268,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba5ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-256 1304,-271 1268,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1c17750@0" ObjectIDZND0="44519@x" ObjectIDZND1="28018@x" Pin0InfoVect0LinkObjId="EC-DY_SY.038Ld_0" Pin0InfoVect1LinkObjId="SW-184394_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c17750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-256 1304,-271 1268,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba5d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1268,-271 1268,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1c17750@0" ObjectIDND1="28018@x" ObjectIDZND0="44519@0" Pin0InfoVect0LinkObjId="EC-DY_SY.038Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c17750_0" Pin1InfoVect1LinkObjId="SW-184394_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1268,-271 1268,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba5f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1268,-467 1268,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28017@1" ObjectIDZND0="39404@0" Pin0InfoVect0LinkObjId="g_1ba7bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1268,-467 1268,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba6dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="991,-437 991,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28014@0" ObjectIDZND0="28013@1" Pin0InfoVect0LinkObjId="SW-184357_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="991,-437 991,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba7970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="991,-370 991,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28013@0" ObjectIDZND0="28015@1" Pin0InfoVect0LinkObjId="SW-184360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184357_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="991,-370 991,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba7bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="991,-473 991,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28014@1" ObjectIDZND0="39404@0" Pin0InfoVect0LinkObjId="g_1ba5f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184359_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="991,-473 991,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba7e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="828,-467 828,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28008@1" ObjectIDZND0="39404@0" Pin0InfoVect0LinkObjId="g_1ba5f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="828,-467 828,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba8090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1027,-262 991,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1ba6a10@0" ObjectIDZND0="37972@x" ObjectIDZND1="28015@x" Pin0InfoVect0LinkObjId="EC-DY_SY.036Ld_0" Pin0InfoVect1LinkObjId="SW-184360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ba6a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1027,-262 991,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba8b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="991,-262 991,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1ba6a10@0" ObjectIDND1="28015@x" ObjectIDZND0="37972@0" Pin0InfoVect0LinkObjId="EC-DY_SY.036Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ba6a10_0" Pin1InfoVect1LinkObjId="SW-184360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="991,-262 991,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c4e910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-18,-675 -18,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_1c4c820@0" Pin0InfoVect0LinkObjId="g_1c4c820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c210b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-18,-675 -18,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bee0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-877 191,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27997@1" ObjectIDZND0="27983@0" Pin0InfoVect0LinkObjId="g_1bfa540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184108_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-877 191,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bee340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-535 191,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28000@0" ObjectIDZND0="27984@0" Pin0InfoVect0LinkObjId="g_1bcc6f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184118_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-535 191,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1beecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-18,-538 -18,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27995@0" ObjectIDZND0="27984@0" Pin0InfoVect0LinkObjId="g_1bcc6f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-18,-538 -18,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1beeec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-18,-590 -69,-590 -69,-600 -67,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="27995@x" ObjectIDZND0="g_1befc80@0" Pin0InfoVect0LinkObjId="g_1befc80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c210b0_0" Pin1InfoVect1LinkObjId="SW-184101_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-18,-590 -69,-590 -69,-600 -67,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bef830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-18,-630 -18,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="27995@x" ObjectIDZND1="g_1befc80@0" Pin0InfoVect0LinkObjId="SW-184101_0" Pin0InfoVect1LinkObjId="g_1befc80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c210b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-18,-630 -18,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1befa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-18,-590 -18,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1befc80@0" ObjectIDZND0="27995@1" Pin0InfoVect0LinkObjId="SW-184101_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c210b0_0" Pin1InfoVect1LinkObjId="g_1befc80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-18,-590 -18,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b68380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-908 266,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27983@0" ObjectIDZND0="27988@0" Pin0InfoVect0LinkObjId="SW-184064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfa540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-908 266,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b68570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="373,-983 335,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c33c60@0" ObjectIDZND0="27991@0" Pin0InfoVect0LinkObjId="SW-184067_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c33c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="373,-983 335,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b68780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="299,-983 266,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27991@1" ObjectIDZND0="27988@x" ObjectIDZND1="27986@x" Pin0InfoVect0LinkObjId="SW-184064_0" Pin0InfoVect1LinkObjId="SW-184061_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184067_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="299,-983 266,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b689b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="373,-1066 340,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c22d50@0" ObjectIDZND0="27990@0" Pin0InfoVect0LinkObjId="SW-184066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c22d50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="373,-1066 340,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b68be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-1066 266,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27990@1" ObjectIDZND0="27986@x" ObjectIDZND1="27987@x" Pin0InfoVect0LinkObjId="SW-184061_0" Pin0InfoVect1LinkObjId="SW-184063_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="304,-1066 266,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b69620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-1045 266,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27986@1" ObjectIDZND0="27990@x" ObjectIDZND1="27987@x" Pin0InfoVect0LinkObjId="SW-184066_0" Pin0InfoVect1LinkObjId="SW-184063_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184061_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="266,-1045 266,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b69880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-1086 266,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27987@0" ObjectIDZND0="27990@x" ObjectIDZND1="27986@x" Pin0InfoVect0LinkObjId="SW-184066_0" Pin0InfoVect1LinkObjId="SW-184061_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184063_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="266,-1086 266,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6a370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-1144 266,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="27989@x" ObjectIDND1="g_1bdd1a0@0" ObjectIDND2="38091@1" ObjectIDZND0="27987@1" Pin0InfoVect0LinkObjId="SW-184063_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-184065_0" Pin1InfoVect1LinkObjId="g_1bdd1a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-1144 266,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6a5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,-1144 336,-1144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b04100@0" ObjectIDZND0="27989@0" Pin0InfoVect0LinkObjId="SW-184065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b04100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="371,-1144 336,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6a830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="300,-1144 266,-1144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="27989@1" ObjectIDZND0="27987@x" ObjectIDZND1="g_1bdd1a0@0" ObjectIDZND2="38091@1" Pin0InfoVect0LinkObjId="SW-184063_0" Pin0InfoVect1LinkObjId="g_1bdd1a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="300,-1144 266,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdc6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-1188 266,-1144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1bdd1a0@0" ObjectIDND1="38091@1" ObjectIDND2="g_1bdeaf0@0" ObjectIDZND0="27987@x" ObjectIDZND1="27989@x" Pin0InfoVect0LinkObjId="SW-184063_0" Pin0InfoVect1LinkObjId="SW-184065_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bdd1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_1bdeaf0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="266,-1188 266,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdc950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-1211 238,-1211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="27987@x" ObjectIDND1="27989@x" ObjectIDND2="0@x" ObjectIDZND0="g_1bdd1a0@0" Pin0InfoVect0LinkObjId="g_1bdd1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-184063_0" Pin1InfoVect1LinkObjId="SW-184065_0" Pin1InfoVect2LinkObjId="g_1c210b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-1211 238,-1211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bde890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-1188 266,-1211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="voltageTransformer" ObjectIDND0="27987@x" ObjectIDND1="27989@x" ObjectIDND2="0@x" ObjectIDZND0="g_1bdd1a0@0" ObjectIDZND1="38091@1" ObjectIDZND2="g_1bdeaf0@0" Pin0InfoVect0LinkObjId="g_1bdd1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_1bdeaf0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-184063_0" Pin1InfoVect1LinkObjId="SW-184065_0" Pin1InfoVect2LinkObjId="g_1c210b0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="266,-1188 266,-1211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbb0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-908 1200,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27983@0" ObjectIDZND0="39391@0" Pin0InfoVect0LinkObjId="SW-235975_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfa540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-908 1200,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbb320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1307,-982 1269,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bb70f0@0" ObjectIDZND0="39392@0" Pin0InfoVect0LinkObjId="SW-235974_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bb70f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1307,-982 1269,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bbb580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1307,-1068 1274,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b771b0@0" ObjectIDZND0="39389@0" Pin0InfoVect0LinkObjId="SW-235972_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b771b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1307,-1068 1274,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-1068 1200,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="39389@1" ObjectIDZND0="39390@x" ObjectIDZND1="39387@x" Pin0InfoVect0LinkObjId="SW-235987_0" Pin0InfoVect1LinkObjId="SW-235973_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235972_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-1068 1200,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3bd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1047 1200,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39390@1" ObjectIDZND0="39389@x" ObjectIDZND1="39387@x" Pin0InfoVect0LinkObjId="SW-235972_0" Pin0InfoVect1LinkObjId="SW-235973_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235987_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1047 1200,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3bfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1088 1200,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="39387@0" ObjectIDZND0="39390@x" ObjectIDZND1="39389@x" Pin0InfoVect0LinkObjId="SW-235987_0" Pin0InfoVect1LinkObjId="SW-235972_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235973_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1088 1200,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3c220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1146 1200,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="39388@x" ObjectIDND1="g_1a433e0@0" ObjectIDND2="g_1a3d950@0" ObjectIDZND0="39387@1" Pin0InfoVect0LinkObjId="SW-235973_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235962_0" Pin1InfoVect1LinkObjId="g_1a433e0_0" Pin1InfoVect2LinkObjId="g_1a3d950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1146 1200,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3c480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1305,-1146 1270,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bba630@0" ObjectIDZND0="39388@0" Pin0InfoVect0LinkObjId="SW-235962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bba630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1305,-1146 1270,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3c6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1234,-1146 1200,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="39388@1" ObjectIDZND0="39387@x" ObjectIDZND1="g_1a433e0@0" ObjectIDZND2="g_1a3d950@0" Pin0InfoVect0LinkObjId="SW-235973_0" Pin0InfoVect1LinkObjId="g_1a433e0_0" Pin0InfoVect2LinkObjId="g_1a3d950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1234,-1146 1200,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3c940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1176 1171,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="39388@x" ObjectIDND1="39387@x" ObjectIDND2="g_1a433e0@0" ObjectIDZND0="g_1a3cba0@0" Pin0InfoVect0LinkObjId="g_1a3cba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235962_0" Pin1InfoVect1LinkObjId="SW-235973_0" Pin1InfoVect2LinkObjId="g_1a433e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1176 1171,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a3f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1243,-1187 1243,-1176 1200,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1a3d950@0" ObjectIDZND0="39388@x" ObjectIDZND1="39387@x" ObjectIDZND2="g_1a433e0@0" Pin0InfoVect0LinkObjId="SW-235962_0" Pin0InfoVect1LinkObjId="SW-235973_0" Pin0InfoVect2LinkObjId="g_1a433e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a3d950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1243,-1187 1243,-1176 1200,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a409d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1233,-982 1200,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="39392@1" ObjectIDZND0="39391@x" ObjectIDZND1="39390@x" Pin0InfoVect0LinkObjId="SW-235975_0" Pin0InfoVect1LinkObjId="SW-235987_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235974_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1233,-982 1200,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a414c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-959 266,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27988@1" ObjectIDZND0="27991@x" ObjectIDZND1="27986@x" Pin0InfoVect0LinkObjId="SW-184067_0" Pin0InfoVect1LinkObjId="SW-184061_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184064_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="266,-959 266,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a41fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-961 1200,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="39391@1" ObjectIDZND0="39392@x" ObjectIDZND1="39390@x" Pin0InfoVect0LinkObjId="SW-235974_0" Pin0InfoVect1LinkObjId="SW-235987_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235975_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-961 1200,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a42210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-1018 266,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27986@0" ObjectIDZND0="27991@x" ObjectIDZND1="27988@x" Pin0InfoVect0LinkObjId="SW-184067_0" Pin0InfoVect1LinkObjId="SW-184064_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="266,-1018 266,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a42470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1020 1200,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39390@0" ObjectIDZND0="39392@x" ObjectIDZND1="39391@x" Pin0InfoVect0LinkObjId="SW-235974_0" Pin0InfoVect1LinkObjId="SW-235975_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235987_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1020 1200,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a43180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1176 1200,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1a433e0@0" ObjectIDND1="g_1a3d950@0" ObjectIDND2="g_1a3cba0@0" ObjectIDZND0="39388@x" ObjectIDZND1="39387@x" Pin0InfoVect0LinkObjId="SW-235962_0" Pin0InfoVect1LinkObjId="SW-235973_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a433e0_0" Pin1InfoVect1LinkObjId="g_1a3d950_0" Pin1InfoVect2LinkObjId="g_1a3cba0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1176 1200,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a44000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1251 1200,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" ObjectIDND0="43360@1" ObjectIDZND0="g_1a433e0@1" Pin0InfoVect0LinkObjId="g_1a433e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1251 1200,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a44260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1198 1200,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1a433e0@0" ObjectIDZND0="39388@x" ObjectIDZND1="39387@x" ObjectIDZND2="g_1a3d950@0" Pin0InfoVect0LinkObjId="SW-235962_0" Pin0InfoVect1LinkObjId="SW-235973_0" Pin0InfoVect2LinkObjId="g_1a3d950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a433e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1198 1200,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a444c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="756,-1020 720,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a5ec40@0" ObjectIDZND0="27993@0" Pin0InfoVect0LinkObjId="SW-184099_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a5ec40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="756,-1020 720,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a44720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="684,-1020 649,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27993@1" ObjectIDZND0="g_1ba0860@0" ObjectIDZND1="g_1ba0000@0" ObjectIDZND2="27992@x" Pin0InfoVect0LinkObjId="g_1ba0860_0" Pin0InfoVect1LinkObjId="g_1ba0000_0" Pin0InfoVect2LinkObjId="SW-184098_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184099_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="684,-1020 649,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a44a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="756,-936 722,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a5c650@0" ObjectIDZND0="27994@0" Pin0InfoVect0LinkObjId="SW-184100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a5c650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="756,-936 722,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a44ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="686,-936 649,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="27994@1" ObjectIDZND0="27992@x" ObjectIDZND1="27983@0" Pin0InfoVect0LinkObjId="SW-184098_0" Pin0InfoVect1LinkObjId="g_1bfa540_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="686,-936 649,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a44f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="299,-816 265,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bd1bf0@0" ObjectIDZND0="27998@0" Pin0InfoVect0LinkObjId="SW-184109_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd1bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="299,-816 265,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a451a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="229,-816 192,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27998@1" ObjectIDZND0="27997@x" ObjectIDZND1="27996@x" Pin0InfoVect0LinkObjId="SW-184108_0" Pin0InfoVect1LinkObjId="SW-184106_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184109_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="229,-816 192,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a4e4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,-828 1076,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="39396@1" ObjectIDZND0="39394@x" ObjectIDZND1="39395@x" Pin0InfoVect0LinkObjId="SW-235989_0" Pin0InfoVect1LinkObjId="SW-235977_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235976_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1113,-828 1076,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b073e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1183,-828 1149,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b06950@0" ObjectIDZND0="39396@0" Pin0InfoVect0LinkObjId="SW-235976_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b06950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1183,-828 1149,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b08510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-717 1121,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_1b07b60@0" ObjectIDND1="39406@x" ObjectIDZND0="g_1b08740@0" Pin0InfoVect0LinkObjId="g_1b08740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b07b60_0" Pin1InfoVect1LinkObjId="g_1b09940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-717 1121,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b09480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-500 1076,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39404@0" ObjectIDZND0="39398@0" Pin0InfoVect0LinkObjId="SW-235985_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ba5f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-500 1076,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b096e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-553 1076,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39398@1" ObjectIDZND0="39397@0" Pin0InfoVect0LinkObjId="SW-235988_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235985_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-553 1076,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b09940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-598 1076,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="39397@1" ObjectIDZND0="39406@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235988_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-598 1076,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b0a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-700 1076,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="39406@1" ObjectIDZND0="g_1b08740@0" ObjectIDZND1="g_1b07b60@0" Pin0InfoVect0LinkObjId="g_1b08740_0" Pin0InfoVect1LinkObjId="g_1b07b60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b09940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-700 1076,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b0a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-717 1076,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_1b08740@0" ObjectIDND1="39406@x" ObjectIDZND0="g_1b07b60@0" Pin0InfoVect0LinkObjId="g_1b07b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b08740_0" Pin1InfoVect1LinkObjId="g_1b09940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-717 1076,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b0a8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-768 1076,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1b07b60@1" ObjectIDZND0="39394@0" Pin0InfoVect0LinkObjId="SW-235989_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b07b60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-768 1076,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b0b3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-807 1076,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39394@1" ObjectIDZND0="39396@x" ObjectIDZND1="39395@x" Pin0InfoVect0LinkObjId="SW-235976_0" Pin0InfoVect1LinkObjId="SW-235977_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-807 1076,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b0b640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-828 1076,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="39396@x" ObjectIDND1="39394@x" ObjectIDZND0="39395@0" Pin0InfoVect0LinkObjId="SW-235977_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235976_0" Pin1InfoVect1LinkObjId="SW-235989_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-828 1076,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b0b8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-886 1076,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39395@1" ObjectIDZND0="27983@0" Pin0InfoVect0LinkObjId="g_1bfa540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235977_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-886 1076,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b0e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1319,-675 1319,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_1b49480@0" Pin0InfoVect0LinkObjId="g_1b49480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c210b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1319,-675 1319,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b474b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1319,-538 1319,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39393@0" ObjectIDZND0="39404@0" Pin0InfoVect0LinkObjId="g_1ba5f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235979_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1319,-538 1319,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b47710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1319,-590 1268,-590 1268,-600 1270,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="39393@x" ObjectIDZND0="g_1b47e40@0" Pin0InfoVect0LinkObjId="g_1b47e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c210b0_0" Pin1InfoVect1LinkObjId="SW-235979_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1319,-590 1268,-590 1268,-600 1270,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b47980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1319,-627 1319,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="39393@x" ObjectIDZND1="g_1b47e40@0" Pin0InfoVect0LinkObjId="SW-235979_0" Pin0InfoVect1LinkObjId="g_1b47e40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c210b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1319,-627 1319,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b47be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1319,-590 1319,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1b47e40@0" ObjectIDZND0="39393@1" Pin0InfoVect0LinkObjId="SW-235979_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c210b0_0" Pin1InfoVect1LinkObjId="g_1b47e40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1319,-590 1319,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac42e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="528,-500 528,-589 549,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27984@0" ObjectIDZND0="39402@0" Pin0InfoVect0LinkObjId="SW-184157_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bcc6f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="528,-500 528,-589 549,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac4540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="585,-589 625,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39402@1" ObjectIDZND0="39400@1" Pin0InfoVect0LinkObjId="SW-184156_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184157_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="585,-589 625,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac47a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="652,-589 706,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39400@0" ObjectIDZND0="39401@0" Pin0InfoVect0LinkObjId="SW-184158_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184156_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="652,-589 706,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac4a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-589 769,-589 769,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39401@1" ObjectIDZND0="39404@0" Pin0InfoVect0LinkObjId="g_1ba5f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184158_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="742,-589 769,-589 769,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac7050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="991,-295 991,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28015@0" ObjectIDZND0="g_1ba6a10@0" ObjectIDZND1="37972@x" Pin0InfoVect0LinkObjId="g_1ba6a10_0" Pin0InfoVect1LinkObjId="EC-DY_SY.036Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="991,-295 991,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac8530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1268,-340 1268,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28018@1" ObjectIDZND0="28016@0" Pin0InfoVect0LinkObjId="SW-184391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-184394_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1268,-340 1268,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1aca5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-295 1371,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1c210b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c210b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-295 1371,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1aca810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-359 1371,-383 1338,-383 1338,-286 1283,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c210b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-359 1371,-383 1338,-383 1338,-286 1283,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a50960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="170,-1107 170,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1ba4b30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1c210b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ba4b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="170,-1107 170,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a50b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="170,-1171 170,-1188 266,-1188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="27987@x" ObjectIDZND1="27989@x" ObjectIDZND2="g_1bdd1a0@0" Pin0InfoVect0LinkObjId="SW-184063_0" Pin0InfoVect1LinkObjId="SW-184065_0" Pin0InfoVect2LinkObjId="g_1bdd1a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c210b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="170,-1171 170,-1188 266,-1188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b3a960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1268,-271 1268,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="44519@x" ObjectIDND1="g_1c17750@0" ObjectIDZND0="28018@0" Pin0InfoVect0LinkObjId="SW-184394_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-DY_SY.038Ld_0" Pin1InfoVect1LinkObjId="g_1c17750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1268,-271 1268,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b3add0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-1249 266,-1211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38091@1" ObjectIDZND0="g_1bdd1a0@0" ObjectIDZND1="27987@x" ObjectIDZND2="27989@x" Pin0InfoVect0LinkObjId="g_1bdd1a0_0" Pin0InfoVect1LinkObjId="SW-184063_0" Pin0InfoVect2LinkObjId="SW-184065_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="266,-1249 266,-1211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b3b050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="323,-1220 323,-1211 266,-1211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1bdeaf0@0" ObjectIDZND0="g_1bdd1a0@0" ObjectIDZND1="27987@x" ObjectIDZND2="27989@x" Pin0InfoVect0LinkObjId="g_1bdd1a0_0" Pin0InfoVect1LinkObjId="SW-184063_0" Pin0InfoVect2LinkObjId="SW-184065_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bdeaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="323,-1220 323,-1211 266,-1211 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153551" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -518.500000 -1130.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26042" ObjectName="DYN-DY_SY"/>
     <cge:Meas_Ref ObjectId="153551"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be20f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.000000 715.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be2c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 282.500000 693.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b33ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 986.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b344f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1359.000000 1030.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b34ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1367.000000 955.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b35650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1359.000000 1015.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b35bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1351.000000 972.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b35e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1359.000000 1001.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b36990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1437.000000 588.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b36c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 632.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b36e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1439.000000 557.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b370a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 617.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b372e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.000000 574.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b37520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 603.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b37df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -235.000000 605.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b38080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -241.000000 649.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b382c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.000000 574.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b38500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -241.000000 634.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b38740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -249.000000 591.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b38980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -241.000000 620.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b39260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 922.000000 688.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b39490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 892.500000 666.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1c22520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.000000 1018.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1b9f4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 363.500000 1033.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aec650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 1048.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aecfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 1021.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aed260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1044.500000 1036.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aed4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 1051.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aed7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 949.000000 771.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aeda30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 925.500000 786.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aedc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 801.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aedfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 949.000000 571.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aee200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 925.500000 586.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aee440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 601.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aee770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 74.000000 780.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aee9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 50.500000 795.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aeec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 62.000000 810.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aeef40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 74.000000 602.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aef1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 50.500000 617.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aef3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 62.000000 632.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aef710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 593.000000 642.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aef970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.500000 657.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aefbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 672.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -337.000000 894.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1aefee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.000000 1018.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af0180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 363.500000 1033.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af03c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 1048.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -99.000000 894.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af06f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.000000 1018.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af0990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 363.500000 1033.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af0bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 1048.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 404.000000 897.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af0f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.000000 1018.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af11a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 363.500000 1033.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af13e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 1048.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 897.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af1710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.000000 1018.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af19b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 363.500000 1033.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af1bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 1048.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.000000 897.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af1f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.000000 1018.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af21c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 363.500000 1033.500000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1af2400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 1048.500000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1af6bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1235.000000 1320.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="1242" cy="1313" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_SY.DY_SY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-908 1456,-908 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27983" ObjectName="BS-DY_SY.DY_SY_3IM"/>
    <cge:TPSR_Ref TObjectID="27983"/></metadata>
   <polyline fill="none" opacity="0" points="-132,-908 1456,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_SY.DY_SY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-250,-500 593,-500 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27984" ObjectName="BS-DY_SY.DY_SY_9IM"/>
    <cge:TPSR_Ref TObjectID="27984"/></metadata>
   <polyline fill="none" opacity="0" points="-250,-500 593,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_SY.DY_SY_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="708,-500 1556,-500 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39404" ObjectName="BS-DY_SY.DY_SY_9IIM"/>
    <cge:TPSR_Ref TObjectID="39404"/></metadata>
   <polyline fill="none" opacity="0" points="708,-500 1556,-500 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27983" cx="191" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27983" cx="649" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27983" cx="1200" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27984" cx="324" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27984" cx="93" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27984" cx="191" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27984" cx="-18" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39404" cx="1076" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39404" cx="1319" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39404" cx="769" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39404" cx="828" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39404" cx="991" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39404" cx="1268" cy="-500" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-448" y="-1187"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="20" graphid="g_1c7a140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -559.000000 -1243.500000) translate(0,16)">石羊变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a2c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a2c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a2c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a2c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a2c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a2c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a2c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a2c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a2c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1996fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,374)">联系方式：0878-6148330</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1690e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 213.096525 -1310.000000) translate(0,12)">35kV大石河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_186c800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 104.096525 -1010.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1859a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 670.096525 -1119.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bff1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -57.000000 -751.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bee5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 806.000000 -930.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1beea90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -524.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be3de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 38.000000 -717.000000) translate(0,12)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be3de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 38.000000 -717.000000) translate(0,27)">35±3*2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be3de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 38.000000 -717.000000) translate(0,42)">Yd11,7.18%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1ba28d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -443.000000 -1226.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1ba4480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -443.000000 -1261.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1b674f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -431.500000 -1176.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be0dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1156.096525 -1316.000000) translate(0,12)">35kV古石线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b07640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -645.000000) translate(0,12)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b07640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -645.000000) translate(0,27)">35±3*2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b07640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -645.000000) translate(0,42)">Yd11,6.88%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b0bb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -767.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acaa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -201.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acaf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -181.000000) translate(0,12)">备用三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 276.000000 -1039.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a50060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -1009.000000) translate(0,12)">33117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a502a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 302.000000 -1092.000000) translate(0,12)">33160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a504e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 -1111.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a50720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 298.000000 -1170.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a50d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 -948.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a51060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.000000 -1046.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a51530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -990.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a517b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -962.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a519f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -1041.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a51c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -1113.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a51e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -950.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a520b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -1008.000000) translate(0,12)">33217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a522f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -1094.000000) translate(0,12)">33260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a52530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1240.000000 -1170.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a52770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -866.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a529b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 227.000000 -842.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a52bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 201.000000 -783.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a52e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 201.000000 -621.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a53070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -560.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a532b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 -801.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a534f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -875.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a53730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1111.000000 -854.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a53970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 -592.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a53bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -542.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a53df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -11.000000 -563.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a54030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -563.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a54270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 -613.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a544b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -615.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a546f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 708.000000 -615.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a54930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1468.000000 -525.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a54b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -170.000000 -191.500000) translate(0,12)">预留1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a54b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -170.000000 -191.500000) translate(0,27)">电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a55f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -78.000000 -184.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a56480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1100.000000 -188.500000) translate(0,12)">预留2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a56480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1100.000000 -188.500000) translate(0,27)">电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a568e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 103.000000 -373.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a56b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 100.000000 -302.000000) translate(0,12)">0356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a56d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 100.000000 -444.000000) translate(0,12)">0351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a56fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 334.000000 -387.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a571e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 331.000000 -458.000000) translate(0,12)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a57420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 331.000000 -316.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a57660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 837.000000 -385.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a57950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -314.000000) translate(0,12)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a57d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 835.000000 -456.000000) translate(0,12)">0342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a57f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -391.000000) translate(0,12)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a581b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 998.000000 -462.000000) translate(0,12)">0362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a583f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 998.000000 -320.000000) translate(0,12)">0366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a58630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -384.000000) translate(0,12)">038</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a58870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1275.000000 -456.000000) translate(0,12)">0382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a58ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1275.000000 -329.000000) translate(0,12)">0386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b3cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 220.000000 -697.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b3eb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1127.000000 -690.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b3efd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -889.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b40db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -754.000000 -274.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1c5cf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -600.000000 -284.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1c5cf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -600.000000 -284.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af2e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 72.000000 -187.000000) translate(0,12)">石西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af33b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 286.000000 -189.000000) translate(0,12)">城区Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af3f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 804.000000 -181.000000) translate(0,12)">石东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af44b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 973.000000 -181.000000) translate(0,12)">石南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1af5110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -181.000000) translate(0,12)">10kV杨家箐线</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c22d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 368.241796 -1060.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c33c60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 368.241796 -977.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b04100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.241796 -1138.085366)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a5c650" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.241796 -930.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a5ec40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.241796 -1014.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd1bf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 294.241796 -810.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b771b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.241796 -1062.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb70f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.241796 -976.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bba630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1300.241796 -1140.085366)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b06950" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1178.241796 -822.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_SY"/>
</svg>