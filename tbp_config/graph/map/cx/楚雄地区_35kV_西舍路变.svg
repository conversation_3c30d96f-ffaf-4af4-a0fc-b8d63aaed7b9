<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-66" aopId="786946" id="thSvg" product="E8000V2" version="1.0" viewBox="3119 -1294 2025 1330">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="67" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <polyline arcFlag="1" points="44,21 45,21 46,21 46,21 47,22 47,22 48,23 48,23 49,24 49,24 49,25 49,26 50,27 50,28 50,28 49,29 49,30 49,31 49,31 48,32 48,32 47,33 47,33 46,34 46,34 45,34 44,34 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,34 45,34 46,34 46,34 47,35 47,35 48,36 48,36 49,37 49,37 49,38 49,39 50,40 50,41 50,41 49,42 49,43 49,44 49,44 48,45 48,45 47,46 47,46 46,47 46,47 45,47 44,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,47 45,47 46,47 46,47 47,48 47,48 48,49 48,49 49,50 49,50 49,51 49,52 50,53 50,54 50,54 49,55 49,56 49,57 49,57 48,58 48,58 47,59 47,59 46,60 46,60 45,60 44,60 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="13" y2="13"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="32"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="21" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="21" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="29" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="88" y2="88"/>
    <polyline points="25,101 27,101 29,100 30,100 32,99 33,98 35,97 36,95 37,93 37,92 38,90 38,88 38,86 37,84 37,83 36,81 35,80 33,78 32,77 30,76 29,76 27,75 25,75 23,75 21,76 20,76 18,77 17,78 15,80 14,81 13,83 13,84 12,86 12,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="67" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="68" y2="68"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape74">
    <circle cx="39" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <circle cx="30" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="71" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="31" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="24" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="8" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,17 39,15 45,15 43,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape85">
    <circle cx="8" cy="17" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="20" y2="20"/>
    <rect height="27" stroke-width="0.416667" width="14" x="1" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="73" y2="25"/>
    <circle cx="8" cy="8" fillStyle="0" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape166">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="73" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="12" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="12" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="8" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="26" y2="24"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="49"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,62 9,40 9,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="64" y2="64"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="10" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape179">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="5" y2="13"/>
    <rect height="4" stroke-width="1" width="19" x="16" y="25"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="37,16 15,38 15,50 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="18" y1="14" y2="14"/>
    <circle cx="16" cy="66" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="65" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="59" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="93" y2="118"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="124" y2="124"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="121" y2="121"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="119" y2="119"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,87 40,87 40,116 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="14,130 20,117 7,117 14,130 14,129 14,130 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="102" y2="136"/>
    <circle cx="16" cy="88" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="87" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="17" y1="87" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="87" y2="82"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape29_0">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape29_1">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="36" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.13333"/>
    <polyline points="58,100 64,100 " stroke-width="1.13333"/>
    <polyline points="64,100 64,93 " stroke-width="1.13333"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_120f490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_12b1750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_12b2130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_11c2080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_11c30a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_11c3b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_147ec00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_147f500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_12a5080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_12a5080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a07610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a07610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_121cd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_121cd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_121db50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_121f400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_11c5c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_11c68b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_11c6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14a82c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14a5860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14a60a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14a67e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14a74f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_11966e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1197110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1197a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1198550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1211150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_12121b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1212d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19fc140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19fcee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19fe000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1a1a910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1340" width="2035" x="3114" y="-1299"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3749" x2="3748" y1="24" y2="24"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.000000 -702.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6658" ObjectName="SW-CX_XSL.CX_XSL_301BK"/>
     <cge:Meas_Ref ObjectId="40666"/>
    <cge:TPSR_Ref TObjectID="6658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6660" ObjectName="SW-CX_XSL.CX_XSL_001BK"/>
     <cge:Meas_Ref ObjectId="40669"/>
    <cge:TPSR_Ref TObjectID="6660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40635">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -336.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6639" ObjectName="SW-CX_XSL.CX_XSL_071BK"/>
     <cge:Meas_Ref ObjectId="40635"/>
    <cge:TPSR_Ref TObjectID="6639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40650">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 -332.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6648" ObjectName="SW-CX_XSL.CX_XSL_074BK"/>
     <cge:Meas_Ref ObjectId="40650"/>
    <cge:TPSR_Ref TObjectID="6648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40655">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 -331.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6651" ObjectName="SW-CX_XSL.CX_XSL_075BK"/>
     <cge:Meas_Ref ObjectId="40655"/>
    <cge:TPSR_Ref TObjectID="6651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40660">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -331.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6654" ObjectName="SW-CX_XSL.CX_XSL_076BK"/>
     <cge:Meas_Ref ObjectId="40660"/>
    <cge:TPSR_Ref TObjectID="6654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40645">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 -339.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6645" ObjectName="SW-CX_XSL.CX_XSL_073BK"/>
     <cge:Meas_Ref ObjectId="40645"/>
    <cge:TPSR_Ref TObjectID="6645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40640">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6642" ObjectName="SW-CX_XSL.CX_XSL_072BK"/>
     <cge:Meas_Ref ObjectId="40640"/>
    <cge:TPSR_Ref TObjectID="6642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215019">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -925.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31981" ObjectName="SW-CX_XSL.CX_XSL_371BK"/>
     <cge:Meas_Ref ObjectId="215019"/>
    <cge:TPSR_Ref TObjectID="31981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215049">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -924.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31986" ObjectName="SW-CX_XSL.CX_XSL_372BK"/>
     <cge:Meas_Ref ObjectId="215049"/>
    <cge:TPSR_Ref TObjectID="31986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-282790">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -707.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44654" ObjectName="SW-CX_XSL.CX_XSL_302BK"/>
     <cge:Meas_Ref ObjectId="282790"/>
    <cge:TPSR_Ref TObjectID="44654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-282810">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -548.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44655" ObjectName="SW-CX_XSL.CX_XSL_002BK"/>
     <cge:Meas_Ref ObjectId="282810"/>
    <cge:TPSR_Ref TObjectID="44655"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XSL.CX_XSL_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-443 5104,-443 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6637" ObjectName="BS-CX_XSL.CX_XSL_9IM"/>
    <cge:TPSR_Ref TObjectID="6637"/></metadata>
   <polyline fill="none" opacity="0" points="3854,-443 5104,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XSL.CX_XSL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4041,-853 4969,-853 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6638" ObjectName="BS-CX_XSL.CX_XSL_3IM"/>
    <cge:TPSR_Ref TObjectID="6638"/></metadata>
   <polyline fill="none" opacity="0" points="4041,-853 4969,-853 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_XSL.CX_XSL_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4866.000000 -139.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40533" ObjectName="CB-CX_XSL.CX_XSL_1C"/>
    <cge:TPSR_Ref TObjectID="40533"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -978.000000)" xlink:href="#transformer2:shape29_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -978.000000)" xlink:href="#transformer2:shape29_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -977.000000)" xlink:href="#transformer2:shape29_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -977.000000)" xlink:href="#transformer2:shape29_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_XSL.CX_XSL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9476"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.090909 -0.000000 0.000000 -0.882353 4225.000000 -597.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -0.882353 4225.000000 -597.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6667" ObjectName="TF-CX_XSL.CX_XSL_1T"/>
    <cge:TPSR_Ref TObjectID="6667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_XSL.CX_XSL_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="22156"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.090909 -0.000000 0.000000 -0.882353 4614.000000 -602.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.090909 -0.000000 0.000000 -0.882353 4614.000000 -602.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="44653" ObjectName="TF-CX_XSL.CX_XSL_2T"/>
    <cge:TPSR_Ref TObjectID="44653"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20985f0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5052.000000 -231.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14bf840">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 5071.000000 -191.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122b780">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5032.000000 -292.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c8c70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4729.000000 -1085.000000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a34aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4563.000000 -188.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a630e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4249.000000 -199.000000)" xlink:href="#lightningRod:shape166"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_125aa40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.000000 -1110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12bf100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -1109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4a610">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -190.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_124c110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -195.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a1f050">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4381.000000 -195.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1256e70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_120ccc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 -193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1252e00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.000000 -991.000000)" xlink:href="#lightningRod:shape179"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-41243" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4385.000000 -643.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41243" ObjectName="CX_XSL:CX_XSL_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3229.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-41235" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -1022.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41235" ObjectName="CX_XSL:CX_XSL_1T_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-309737" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -980.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309737" ObjectName="CX_XSL:CX_XSL_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-309738" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -942.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309738" ObjectName="CX_XSL:CX_XSL_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41242" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -575.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6660"/>
     <cge:Term_Ref ObjectID="9460"/>
    <cge:TPSR_Ref TObjectID="6660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41241" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -575.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6660"/>
     <cge:Term_Ref ObjectID="9460"/>
    <cge:TPSR_Ref TObjectID="6660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41237" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -575.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6660"/>
     <cge:Term_Ref ObjectID="9460"/>
    <cge:TPSR_Ref TObjectID="6660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41229" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -87.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6639"/>
     <cge:Term_Ref ObjectID="9418"/>
    <cge:TPSR_Ref TObjectID="6639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41228" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -87.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6639"/>
     <cge:Term_Ref ObjectID="9418"/>
    <cge:TPSR_Ref TObjectID="6639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41225" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -87.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41225" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6639"/>
     <cge:Term_Ref ObjectID="9418"/>
    <cge:TPSR_Ref TObjectID="6639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41209" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -89.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6642"/>
     <cge:Term_Ref ObjectID="9424"/>
    <cge:TPSR_Ref TObjectID="6642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41208" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -89.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6642"/>
     <cge:Term_Ref ObjectID="9424"/>
    <cge:TPSR_Ref TObjectID="6642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41205" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -89.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6642"/>
     <cge:Term_Ref ObjectID="9424"/>
    <cge:TPSR_Ref TObjectID="6642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41224" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4435.000000 -87.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6645"/>
     <cge:Term_Ref ObjectID="9430"/>
    <cge:TPSR_Ref TObjectID="6645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41223" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4435.000000 -87.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41223" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6645"/>
     <cge:Term_Ref ObjectID="9430"/>
    <cge:TPSR_Ref TObjectID="6645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41220" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4435.000000 -87.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41220" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6645"/>
     <cge:Term_Ref ObjectID="9430"/>
    <cge:TPSR_Ref TObjectID="6645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41234" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -81.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6648"/>
     <cge:Term_Ref ObjectID="9436"/>
    <cge:TPSR_Ref TObjectID="6648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41233" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -81.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6648"/>
     <cge:Term_Ref ObjectID="9436"/>
    <cge:TPSR_Ref TObjectID="6648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41230" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -81.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6648"/>
     <cge:Term_Ref ObjectID="9436"/>
    <cge:TPSR_Ref TObjectID="6648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41219" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4804.000000 -77.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41219" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6651"/>
     <cge:Term_Ref ObjectID="9442"/>
    <cge:TPSR_Ref TObjectID="6651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41218" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4804.000000 -77.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6651"/>
     <cge:Term_Ref ObjectID="9442"/>
    <cge:TPSR_Ref TObjectID="6651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41215" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4804.000000 -77.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6651"/>
     <cge:Term_Ref ObjectID="9442"/>
    <cge:TPSR_Ref TObjectID="6651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-41236" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4424.000000 -661.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6667"/>
     <cge:Term_Ref ObjectID="9477"/>
    <cge:TPSR_Ref TObjectID="6667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-41245" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4050.000000 -918.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6638"/>
     <cge:Term_Ref ObjectID="9417"/>
    <cge:TPSR_Ref TObjectID="6638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-41247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4050.000000 -918.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6638"/>
     <cge:Term_Ref ObjectID="9417"/>
    <cge:TPSR_Ref TObjectID="6638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-41248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4050.000000 -918.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6638"/>
     <cge:Term_Ref ObjectID="9417"/>
    <cge:TPSR_Ref TObjectID="6638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-135054" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4050.000000 -918.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6638"/>
     <cge:Term_Ref ObjectID="9417"/>
    <cge:TPSR_Ref TObjectID="6638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-41211" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -513.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41211" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6637"/>
     <cge:Term_Ref ObjectID="9416"/>
    <cge:TPSR_Ref TObjectID="6637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-41213" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -513.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6637"/>
     <cge:Term_Ref ObjectID="9416"/>
    <cge:TPSR_Ref TObjectID="6637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-41214" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -513.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6637"/>
     <cge:Term_Ref ObjectID="9416"/>
    <cge:TPSR_Ref TObjectID="6637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-41210" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -513.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6637"/>
     <cge:Term_Ref ObjectID="9416"/>
    <cge:TPSR_Ref TObjectID="6637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -762.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6658"/>
     <cge:Term_Ref ObjectID="9456"/>
    <cge:TPSR_Ref TObjectID="6658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -762.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6658"/>
     <cge:Term_Ref ObjectID="9456"/>
    <cge:TPSR_Ref TObjectID="6658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -762.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6658"/>
     <cge:Term_Ref ObjectID="9456"/>
    <cge:TPSR_Ref TObjectID="6658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-58413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -762.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6658"/>
     <cge:Term_Ref ObjectID="9456"/>
    <cge:TPSR_Ref TObjectID="6658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -1281.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31981"/>
     <cge:Term_Ref ObjectID="46193"/>
    <cge:TPSR_Ref TObjectID="31981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -1281.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31981"/>
     <cge:Term_Ref ObjectID="46193"/>
    <cge:TPSR_Ref TObjectID="31981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -1281.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31981"/>
     <cge:Term_Ref ObjectID="46193"/>
    <cge:TPSR_Ref TObjectID="31981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1294.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31986"/>
     <cge:Term_Ref ObjectID="46203"/>
    <cge:TPSR_Ref TObjectID="31986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1294.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31986"/>
     <cge:Term_Ref ObjectID="46203"/>
    <cge:TPSR_Ref TObjectID="31986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1294.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31986"/>
     <cge:Term_Ref ObjectID="46203"/>
    <cge:TPSR_Ref TObjectID="31986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4978.000000 -65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6654"/>
     <cge:Term_Ref ObjectID="9448"/>
    <cge:TPSR_Ref TObjectID="6654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41201" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4978.000000 -65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6654"/>
     <cge:Term_Ref ObjectID="9448"/>
    <cge:TPSR_Ref TObjectID="6654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-102054" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4788.000000 -724.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44653"/>
     <cge:Term_Ref ObjectID="22154"/>
    <cge:TPSR_Ref TObjectID="44653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-102055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4788.000000 -724.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44653"/>
     <cge:Term_Ref ObjectID="22154"/>
    <cge:TPSR_Ref TObjectID="44653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-282781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -759.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="282781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44654"/>
     <cge:Term_Ref ObjectID="22158"/>
    <cge:TPSR_Ref TObjectID="44654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-282782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -759.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="282782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44654"/>
     <cge:Term_Ref ObjectID="22158"/>
    <cge:TPSR_Ref TObjectID="44654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-282778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -759.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="282778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44654"/>
     <cge:Term_Ref ObjectID="22158"/>
    <cge:TPSR_Ref TObjectID="44654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-282783" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -759.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="282783" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44654"/>
     <cge:Term_Ref ObjectID="22158"/>
    <cge:TPSR_Ref TObjectID="44654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-282787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -572.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="282787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44655"/>
     <cge:Term_Ref ObjectID="22160"/>
    <cge:TPSR_Ref TObjectID="44655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-282788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -572.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="282788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44655"/>
     <cge:Term_Ref ObjectID="22160"/>
    <cge:TPSR_Ref TObjectID="44655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-282784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -572.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="282784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44655"/>
     <cge:Term_Ref ObjectID="22160"/>
    <cge:TPSR_Ref TObjectID="44655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-282789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -572.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="282789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44655"/>
     <cge:Term_Ref ObjectID="22160"/>
    <cge:TPSR_Ref TObjectID="44655"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3241" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3241" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3193" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3193" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4005" y="-365"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4005" y="-365"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4196" y="-367"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4196" y="-367"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4369" y="-368"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4369" y="-368"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4724" y="-360"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4724" y="-360"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4900" y="-360"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4900" y="-360"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4548" y="-361"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4548" y="-361"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="30" qtmmishow="hidden" width="93" x="3148" y="-832"/>
    </a>
   <metadata/><rect fill="white" height="30" opacity="0" stroke="white" transform="" width="93" x="3148" y="-832"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3403" y="-1159"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3403" y="-1159"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3403" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3403" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4247" y="-953"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4247" y="-953"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4515" y="-954"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4515" y="-954"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3547" y="-1185"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3547" y="-1185"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="4149" y="-643"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="4149" y="-643"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3144" y="-773"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3144" y="-773"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="4708" y="-676"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="4708" y="-676"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3241" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3193" y="-1194"/></g>
   <g href="35kV西舍路变10kV西依线071断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4005" y="-365"/></g>
   <g href="35kV西舍路变10kV大自雄线072断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4196" y="-367"/></g>
   <g href="35kV西舍路变10kV西洋线073断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4369" y="-368"/></g>
   <g href="35kV西舍路变10kV西舍路集镇线075断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4724" y="-360"/></g>
   <g href="35kV西舍路变1号电容器组076断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4900" y="-360"/></g>
   <g href="35kV西舍路变10kV闸上线074断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4548" y="-361"/></g>
   <g href="35kV西舍路变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="30" qtmmishow="hidden" width="93" x="3148" y="-832"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3403" y="-1159"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3403" y="-1194"/></g>
   <g href="35kV西舍路变35kV杜新舍线371断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4247" y="-953"/></g>
   <g href="35kV西舍路变35kV保西线372断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4515" y="-954"/></g>
   <g href="AVC西舍路站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3547" y="-1185"/></g>
   <g href="35kV西舍路变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="4149" y="-643"/></g>
   <g href="35kV西舍路变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3144" y="-773"/></g>
   <g href="35kV西舍路变2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="4708" y="-676"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3120" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3547" y="-1185"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XSL" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_duxinsheTxsl" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4238,-1226 4238,-1191 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34591" ObjectName="AC-35kV.LN_duxinsheTxsl"/>
    <cge:TPSR_Ref TObjectID="34591_SS-66"/></metadata>
   <polyline fill="none" opacity="0" points="4238,-1226 4238,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XSL" endPointId="0" endStationName="CX_BDH" flowDrawDirect="1" flowShape="0" id="AC-35kV.baoxi_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4506,-1188 4506,-1227 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9166" ObjectName="AC-35kV.baoxi_line"/>
    <cge:TPSR_Ref TObjectID="9166_SS-66"/></metadata>
   <polyline fill="none" opacity="0" points="4506,-1188 4506,-1227 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_14b9610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 4962.000000 -258.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1229100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 4335.000000 -755.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ab630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4320.000000 -975.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1216a80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 -1059.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ae320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4587.000000 -915.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1486790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.000000 -974.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a359e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4587.000000 -1058.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d67e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 -948.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a3fc50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 -963.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a23a30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 4722.000000 -760.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6638" cx="4760" cy="-853" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6638" cx="4266" cy="-853" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6638" cx="4238" cy="-853" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6638" cx="4506" cy="-853" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6638" cx="4905" cy="-853" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6637" cx="3995" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6637" cx="4186" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6637" cx="4538" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6637" cx="4715" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6637" cx="4891" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6637" cx="4266" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6637" cx="4361" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6637" cx="5058" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6638" cx="4655" cy="-853" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6637" cx="4655" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ff1a60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4673.000000 -1114.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fa92e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3962.000000 -38.000000) translate(0,15)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fa92e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3962.000000 -38.000000) translate(0,33)">依</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fa92e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3962.000000 -38.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14d7d10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4157.000000 -57.000000) translate(0,15)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14d7d10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4157.000000 -57.000000) translate(0,33)">自</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14d7d10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4157.000000 -57.000000) translate(0,51)">雄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14d7d10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4157.000000 -57.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1919bc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4336.000000 -37.000000) translate(0,15)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1919bc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4336.000000 -37.000000) translate(0,33)">洋</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1919bc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4336.000000 -37.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a22030" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4512.000000 -19.000000) translate(0,15)">闸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a22030" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4512.000000 -19.000000) translate(0,33)">上</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a22030" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4512.000000 -19.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2030780" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4675.000000 -86.000000) translate(0,15)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2030780" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4675.000000 -86.000000) translate(0,33)">舍</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2030780" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4675.000000 -86.000000) translate(0,51)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2030780" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4675.000000 -86.000000) translate(0,69)">集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2030780" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4675.000000 -86.000000) translate(0,87)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2030780" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4675.000000 -86.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1251b40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4982.000000 -173.000000) translate(0,15)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fe830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -916.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1223710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4700.000000 -995.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4b6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4279.000000 -730.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11cd560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4277.000000 -817.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11fd670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -876.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12571b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -466.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a5f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4275.000000 -558.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151c570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4273.000000 -496.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14acc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4005.000000 -365.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a2cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4003.000000 -318.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a5f130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4003.000000 -411.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14c60e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 -367.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14945e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4194.000000 -320.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1214de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4194.000000 -413.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a39ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -368.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3f3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4367.000000 -414.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ef0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4367.000000 -321.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1492430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -361.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a24040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4546.000000 -407.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a5a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4546.000000 -314.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1224ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -360.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d8b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4722.000000 -313.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f4c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4722.000000 -406.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11bc760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -406.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12145e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4910.000000 -293.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14c9eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -313.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c4fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4900.000000 -360.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_14887a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3281.500000 -1166.500000) translate(0,16)">西舍路变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_13c4240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f60d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4298.000000 -790.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14931a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -196.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1a49bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3246.000000 -238.000000) translate(0,17)">3814336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1a49bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3246.000000 -238.000000) translate(0,38)">4781</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11991d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -619.000000) translate(0,12)">SZ9-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11991d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -619.000000) translate(0,27)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11991d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -619.000000) translate(0,42)">Ⅰ+7.5%  37.625</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11991d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -619.000000) translate(0,57)">Ⅱ+5%     36.750</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11991d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -619.000000) translate(0,72)">Ⅲ+2.5%  35.870</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a9080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 -367.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_11af4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -826.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_14a1da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3414.000000 -1151.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_12518f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3414.000000 -1186.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1493590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4210.000000 -1220.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1493590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4210.000000 -1220.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1493590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4210.000000 -1220.000000) translate(0,51)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1493590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4210.000000 -1220.000000) translate(0,69)">新</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1493590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4210.000000 -1220.000000) translate(0,87)">舍</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1493590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4210.000000 -1220.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13b7ac0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4472.000000 -1237.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13b7ac0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4472.000000 -1237.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13b7ac0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4472.000000 -1237.000000) translate(0,51)">保</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13b7ac0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4472.000000 -1237.000000) translate(0,69)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13b7ac0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4472.000000 -1237.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14bb650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4965.000000 -1131.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14bb650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4965.000000 -1131.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14bb650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4965.000000 -1131.000000) translate(0,51)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14bb650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4965.000000 -1131.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14bb650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4965.000000 -1131.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14bb650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4965.000000 -1131.000000) translate(0,105)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14bb650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4965.000000 -1131.000000) translate(0,123)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a146e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -1007.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a14940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -1025.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a14b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -1091.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a62aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4513.000000 -898.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a62ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4534.000000 -1006.000000) translate(0,12)">37260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a62f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4513.000000 -1024.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_12523d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4533.000000 -1090.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_12525e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4935.000000 -947.000000) translate(0,12)">37317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a55650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.000000 -953.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1485890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4515.000000 -954.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_148ec50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -899.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_19f7670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -1025.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_19f7b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -1007.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1215160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -1091.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_14823f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4513.000000 -1024.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_119bf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4533.000000 -1090.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_119c1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4534.000000 -1006.000000) translate(0,12)">37260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_119c3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4513.000000 -898.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a293f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -940.000000) translate(0,12)">37217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a29630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -917.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19f5910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3119.000000 -179.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19f5910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3119.000000 -179.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_14bd680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3246.000000 -189.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_14bd680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3246.000000 -189.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_14bd680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3246.000000 -189.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a092b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4850.000000 -129.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_14a1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3563.500000 -1170.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a1c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -643.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1a0e220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -772.000000) translate(0,20)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19efd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5073.000000 -365.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.000000 -644.000000) translate(0,12)">SZ20-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.000000 -644.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.000000 -644.000000) translate(0,42)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.000000 -644.000000) translate(0,57)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.000000 -644.000000) translate(0,72)">Ud=7.0%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a571f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -736.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a20b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -823.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a20d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -795.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a20f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -577.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a211d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -491.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a21410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4708.000000 -676.000000) translate(0,12)">2号主变</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c5150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 575.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d2380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4109.000000 560.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 545.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b7540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 87.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14bbc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 72.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11c8bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 57.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0ab10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 88.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a600d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.000000 73.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d5ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 58.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14ab180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 85.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b9160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 70.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4388.000000 55.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a17970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 81.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b8200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 66.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a4090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 51.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fc440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4743.000000 77.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d2c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4732.000000 62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1217270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4757.000000 47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 146.000000 290.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a15cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4762.000000 357.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12bed80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 342.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f62c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 469.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1494250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 513.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1258f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 483.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2aac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 498.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f50e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 919.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 889.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1484a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 904.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a9c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 874.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a49de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 643.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2a5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 658.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14c5390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 747.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14c9190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 732.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14c9370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 716.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a22f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 762.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4d460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.000000 1281.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c67e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 1266.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c6a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 1251.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c6e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4453.000000 1292.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f5490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 1277.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f56d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4467.000000 1262.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a1e450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4702.000000 709.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a1e6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4702.000000 724.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 482.000000 2.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a02920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 747.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a02b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 732.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0bd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 716.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0bf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 762.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 187.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0c2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 747.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0c560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 732.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0c7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 716.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a0c9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 762.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a0a220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.000000 675.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="4778" cy="668" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_XSL.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -16.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34510" ObjectName="EC-CX_XSL.071Ld"/>
    <cge:TPSR_Ref TObjectID="34510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_XSL.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -8.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34511" ObjectName="EC-CX_XSL.072Ld"/>
    <cge:TPSR_Ref TObjectID="34511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_XSL.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 -8.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34512" ObjectName="EC-CX_XSL.073Ld"/>
    <cge:TPSR_Ref TObjectID="34512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_XSL.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 12.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34513" ObjectName="EC-CX_XSL.074Ld"/>
    <cge:TPSR_Ref TObjectID="34513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_XSL.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 -7.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34514" ObjectName="EC-CX_XSL.075Ld"/>
    <cge:TPSR_Ref TObjectID="34514"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37335" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3439.000000 -1088.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5917" ObjectName="DYN-CX_XSL"/>
     <cge:Meas_Ref ObjectId="37335"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40670">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.000000 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6661" ObjectName="SW-CX_XSL.CX_XSL_0011SW"/>
     <cge:Meas_Ref ObjectId="40670"/>
    <cge:TPSR_Ref TObjectID="6661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40681">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4751.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6663" ObjectName="SW-CX_XSL.CX_XSL_3901SW"/>
     <cge:Meas_Ref ObjectId="40681"/>
    <cge:TPSR_Ref TObjectID="6663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58502">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4283.000000 -759.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10675" ObjectName="SW-CX_XSL.CX_XSL_30117SW"/>
     <cge:Meas_Ref ObjectId="58502"/>
    <cge:TPSR_Ref TObjectID="10675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40667">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6659" ObjectName="SW-CX_XSL.CX_XSL_3011SW"/>
     <cge:Meas_Ref ObjectId="40667"/>
    <cge:TPSR_Ref TObjectID="6659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40636">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6640" ObjectName="SW-CX_XSL.CX_XSL_0711SW"/>
     <cge:Meas_Ref ObjectId="40636"/>
    <cge:TPSR_Ref TObjectID="6640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40637">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6641" ObjectName="SW-CX_XSL.CX_XSL_0716SW"/>
     <cge:Meas_Ref ObjectId="40637"/>
    <cge:TPSR_Ref TObjectID="6641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40641">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6643" ObjectName="SW-CX_XSL.CX_XSL_0721SW"/>
     <cge:Meas_Ref ObjectId="40641"/>
    <cge:TPSR_Ref TObjectID="6643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6644" ObjectName="SW-CX_XSL.CX_XSL_0726SW"/>
     <cge:Meas_Ref ObjectId="40642"/>
    <cge:TPSR_Ref TObjectID="6644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 -376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6649" ObjectName="SW-CX_XSL.CX_XSL_0741SW"/>
     <cge:Meas_Ref ObjectId="40651"/>
    <cge:TPSR_Ref TObjectID="6649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6650" ObjectName="SW-CX_XSL.CX_XSL_0746SW"/>
     <cge:Meas_Ref ObjectId="40652"/>
    <cge:TPSR_Ref TObjectID="6650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 -376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6652" ObjectName="SW-CX_XSL.CX_XSL_0751SW"/>
     <cge:Meas_Ref ObjectId="40656"/>
    <cge:TPSR_Ref TObjectID="6652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40657">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6653" ObjectName="SW-CX_XSL.CX_XSL_0756SW"/>
     <cge:Meas_Ref ObjectId="40657"/>
    <cge:TPSR_Ref TObjectID="6653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5067.000000 -384.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6665" ObjectName="SW-CX_XSL.CX_XSL_0901SW"/>
     <cge:Meas_Ref ObjectId="40683"/>
    <cge:TPSR_Ref TObjectID="6665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40661">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6655" ObjectName="SW-CX_XSL.CX_XSL_0761SW"/>
     <cge:Meas_Ref ObjectId="40661"/>
    <cge:TPSR_Ref TObjectID="6655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6656" ObjectName="SW-CX_XSL.CX_XSL_0766SW"/>
     <cge:Meas_Ref ObjectId="40662"/>
    <cge:TPSR_Ref TObjectID="6656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4908.000000 -262.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6657" ObjectName="SW-CX_XSL.CX_XSL_07667SW"/>
     <cge:Meas_Ref ObjectId="40665"/>
    <cge:TPSR_Ref TObjectID="6657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40682">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 -964.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6664" ObjectName="SW-CX_XSL.CX_XSL_39017SW"/>
     <cge:Meas_Ref ObjectId="40682"/>
    <cge:TPSR_Ref TObjectID="6664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40646">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 -384.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6646" ObjectName="SW-CX_XSL.CX_XSL_0731SW"/>
     <cge:Meas_Ref ObjectId="40646"/>
    <cge:TPSR_Ref TObjectID="6646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6647" ObjectName="SW-CX_XSL.CX_XSL_0736SW"/>
     <cge:Meas_Ref ObjectId="40647"/>
    <cge:TPSR_Ref TObjectID="6647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -1035.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4451.000000 -1034.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215023">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -869.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6662" ObjectName="SW-CX_XSL.CX_XSL_3711SW"/>
     <cge:Meas_Ref ObjectId="215023"/>
    <cge:TPSR_Ref TObjectID="6662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215058">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -1059.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31984" ObjectName="SW-CX_XSL.CX_XSL_37267SW"/>
     <cge:Meas_Ref ObjectId="215058"/>
    <cge:TPSR_Ref TObjectID="31984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215021">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -995.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31978" ObjectName="SW-CX_XSL.CX_XSL_3716SW"/>
     <cge:Meas_Ref ObjectId="215021"/>
    <cge:TPSR_Ref TObjectID="31978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215022">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 -976.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31979" ObjectName="SW-CX_XSL.CX_XSL_37160SW"/>
     <cge:Meas_Ref ObjectId="215022"/>
    <cge:TPSR_Ref TObjectID="31979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215024">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -1060.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31980" ObjectName="SW-CX_XSL.CX_XSL_37167SW"/>
     <cge:Meas_Ref ObjectId="215024"/>
    <cge:TPSR_Ref TObjectID="31980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215052">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -868.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31982" ObjectName="SW-CX_XSL.CX_XSL_3721SW"/>
     <cge:Meas_Ref ObjectId="215052"/>
    <cge:TPSR_Ref TObjectID="31982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215054">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -994.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31985" ObjectName="SW-CX_XSL.CX_XSL_3726SW"/>
     <cge:Meas_Ref ObjectId="215054"/>
    <cge:TPSR_Ref TObjectID="31985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215089">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4896.000000 -891.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31988" ObjectName="SW-CX_XSL.CX_XSL_3731SW"/>
     <cge:Meas_Ref ObjectId="215089"/>
    <cge:TPSR_Ref TObjectID="31988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215090">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4931.000000 -949.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31989" ObjectName="SW-CX_XSL.CX_XSL_37317SW"/>
     <cge:Meas_Ref ObjectId="215090"/>
    <cge:TPSR_Ref TObjectID="31989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215053">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31987" ObjectName="SW-CX_XSL.CX_XSL_37217SW"/>
     <cge:Meas_Ref ObjectId="215053"/>
    <cge:TPSR_Ref TObjectID="31987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215055">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 -975.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31983" ObjectName="SW-CX_XSL.CX_XSL_37260SW"/>
     <cge:Meas_Ref ObjectId="215055"/>
    <cge:TPSR_Ref TObjectID="31983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-282791">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -793.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44657" ObjectName="SW-CX_XSL.CX_XSL_3021SW"/>
     <cge:Meas_Ref ObjectId="282791"/>
    <cge:TPSR_Ref TObjectID="44657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-282792">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 -764.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44658" ObjectName="SW-CX_XSL.CX_XSL_30217SW"/>
     <cge:Meas_Ref ObjectId="282792"/>
    <cge:TPSR_Ref TObjectID="44658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-282811">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -461.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44656" ObjectName="SW-CX_XSL.CX_XSL_0021SW"/>
     <cge:Meas_Ref ObjectId="282811"/>
    <cge:TPSR_Ref TObjectID="44656"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_119aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-969 4737,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6663@x" ObjectIDND1="g_13c8c70@0" ObjectIDZND0="6664@1" Pin0InfoVect0LinkObjId="SW-40682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40681_0" Pin1InfoVect1LinkObjId="g_13c8c70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-969 4737,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e9d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4701,-969 4681,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6664@0" ObjectIDZND0="g_1a3fc50@0" Pin0InfoVect0LinkObjId="g_1a3fc50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4701,-969 4681,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-764 4342,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10675@1" ObjectIDZND0="g_1229100@0" Pin0InfoVect0LinkObjId="g_1229100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58502_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-764 4342,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12c4ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4288,-764 4266,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10675@0" ObjectIDZND0="6659@x" ObjectIDZND1="6658@x" Pin0InfoVect0LinkObjId="SW-40667_0" Pin0InfoVect1LinkObjId="SW-40666_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58502_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4288,-764 4266,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a22970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-443 3995,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6637@0" ObjectIDZND0="6640@1" Pin0InfoVect0LinkObjId="SW-40636_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1195a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-443 3995,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b6310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-386 3995,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6640@0" ObjectIDZND0="6639@1" Pin0InfoVect0LinkObjId="SW-40635_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40636_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-386 3995,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1200790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-344 3995,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6639@0" ObjectIDZND0="6641@1" Pin0InfoVect0LinkObjId="SW-40637_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40635_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-344 3995,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ebf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-443 4186,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6637@0" ObjectIDZND0="6643@1" Pin0InfoVect0LinkObjId="SW-40641_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1195a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-443 4186,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d3540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-388 4186,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6643@0" ObjectIDZND0="6642@1" Pin0InfoVect0LinkObjId="SW-40640_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40641_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-388 4186,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a63d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-346 4186,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6642@0" ObjectIDZND0="6644@1" Pin0InfoVect0LinkObjId="SW-40642_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-346 4186,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129f9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4538,-443 4538,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6637@0" ObjectIDZND0="6649@1" Pin0InfoVect0LinkObjId="SW-40651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1195a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4538,-443 4538,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a0cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4538,-381 4538,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6649@0" ObjectIDZND0="6648@1" Pin0InfoVect0LinkObjId="SW-40650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4538,-381 4538,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1200570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4538,-340 4538,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6648@0" ObjectIDZND0="6650@1" Pin0InfoVect0LinkObjId="SW-40652_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4538,-340 4538,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4538,-276 4571,-276 4571,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="6650@x" ObjectIDND1="g_1256e70@0" ObjectIDND2="34513@x" ObjectIDZND0="g_1a34aa0@0" Pin0InfoVect0LinkObjId="g_1a34aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40652_0" Pin1InfoVect1LinkObjId="g_1256e70_0" Pin1InfoVect2LinkObjId="EC-CX_XSL.074Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4538,-276 4571,-276 4571,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_151c8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4538,-289 4538,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="6650@0" ObjectIDZND0="g_1a34aa0@0" ObjectIDZND1="g_1256e70@0" ObjectIDZND2="34513@x" Pin0InfoVect0LinkObjId="g_1a34aa0_0" Pin0InfoVect1LinkObjId="g_1256e70_0" Pin0InfoVect2LinkObjId="EC-CX_XSL.074Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40652_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4538,-289 4538,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ca550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-443 4715,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6637@0" ObjectIDZND0="6652@1" Pin0InfoVect0LinkObjId="SW-40656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1195a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-443 4715,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f8f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-381 4715,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6652@0" ObjectIDZND0="6651@1" Pin0InfoVect0LinkObjId="SW-40655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-381 4715,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b02f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-339 4715,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6651@0" ObjectIDZND0="6653@1" Pin0InfoVect0LinkObjId="SW-40657_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-339 4715,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a2eb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-853 4760,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6638@0" ObjectIDZND0="6663@0" Pin0InfoVect0LinkObjId="SW-40681_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11f22f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-853 4760,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a16040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-928 4760,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6663@1" ObjectIDZND0="6664@x" ObjectIDZND1="g_13c8c70@0" Pin0InfoVect0LinkObjId="SW-40682_0" Pin0InfoVect1LinkObjId="g_13c8c70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-928 4760,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f7680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-443 4891,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6637@0" ObjectIDZND0="6655@1" Pin0InfoVect0LinkObjId="SW-40661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1195a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-443 4891,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b9c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-381 4891,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6655@0" ObjectIDZND0="6654@1" Pin0InfoVect0LinkObjId="SW-40660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-381 4891,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1487580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-339 4891,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6654@0" ObjectIDZND0="6656@1" Pin0InfoVect0LinkObjId="SW-40662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-339 4891,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125bc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-267 4913,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="40533@x" ObjectIDND1="6656@x" ObjectIDZND0="6657@0" Pin0InfoVect0LinkObjId="SW-40665_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_XSL.CX_XSL_1C_0" Pin1InfoVect1LinkObjId="SW-40662_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-267 4913,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a68490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4949,-267 4969,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6657@1" ObjectIDZND0="g_14b9610@0" Pin0InfoVect0LinkObjId="g_14b9610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4949,-267 4969,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a056f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-288 4891,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="6656@0" ObjectIDZND0="40533@x" ObjectIDZND1="6657@x" Pin0InfoVect0LinkObjId="CB-CX_XSL.CX_XSL_1C_0" Pin0InfoVect1LinkObjId="SW-40665_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-288 4891,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14964a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-248 4891,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40533@0" ObjectIDZND0="6656@x" ObjectIDZND1="6657@x" Pin0InfoVect0LinkObjId="SW-40662_0" Pin0InfoVect1LinkObjId="SW-40665_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_XSL.CX_XSL_1C_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-248 4891,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19eb3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-303 5025,-303 5025,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6665@x" ObjectIDND1="g_20985f0@0" ObjectIDZND0="g_122b780@0" Pin0InfoVect0LinkObjId="g_122b780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40683_0" Pin1InfoVect1LinkObjId="g_20985f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-303 5025,-303 5025,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122af70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-343 5058,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6665@1" ObjectIDZND0="g_122b780@0" ObjectIDZND1="g_20985f0@0" Pin0InfoVect0LinkObjId="g_122b780_0" Pin0InfoVect1LinkObjId="g_20985f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40683_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-343 5058,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1222d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-303 5058,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_122b780@0" ObjectIDND1="6665@x" ObjectIDZND0="g_20985f0@0" Pin0InfoVect0LinkObjId="g_20985f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_122b780_0" Pin1InfoVect1LinkObjId="SW-40683_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-303 5058,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148a3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5057,-236 5057,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_20985f0@1" ObjectIDZND0="g_14bf840@0" Pin0InfoVect0LinkObjId="g_14bf840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20985f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5057,-236 5057,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c0560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-564 4266,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6660@1" ObjectIDZND0="6667@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40669_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-564 4266,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c8f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-443 4266,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6637@0" ObjectIDZND0="6661@0" Pin0InfoVect0LinkObjId="SW-40670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1195a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-443 4266,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14bcdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-507 4266,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6661@1" ObjectIDZND0="6660@0" Pin0InfoVect0LinkObjId="SW-40669_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-507 4266,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a2af60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-678 4266,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="6667@0" ObjectIDZND0="6658@0" Pin0InfoVect0LinkObjId="SW-40666_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12c0560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-678 4266,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11a4ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-737 4266,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6658@1" ObjectIDZND0="6659@x" ObjectIDZND1="10675@x" Pin0InfoVect0LinkObjId="SW-40667_0" Pin0InfoVect1LinkObjId="SW-58502_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-737 4266,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14afe50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-764 4266,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6658@x" ObjectIDND1="10675@x" ObjectIDZND0="6659@0" Pin0InfoVect0LinkObjId="SW-40667_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40666_0" Pin1InfoVect1LinkObjId="SW-58502_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-764 4266,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11f22f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-829 4266,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6659@1" ObjectIDZND0="6638@0" Pin0InfoVect0LinkObjId="g_12bf8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40667_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-829 4266,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11bd070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-443 4361,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6637@0" ObjectIDZND0="6646@1" Pin0InfoVect0LinkObjId="SW-40646_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1195a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-443 4361,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b7ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-389 4361,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6646@0" ObjectIDZND0="6645@1" Pin0InfoVect0LinkObjId="SW-40645_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40646_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-389 4361,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f7350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-347 4361,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6645@0" ObjectIDZND0="6647@1" Pin0InfoVect0LinkObjId="SW-40647_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40645_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-347 4361,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1492d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-272 4259,-281 4186,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_1a630e0@0" ObjectIDZND0="g_124c110@0" ObjectIDZND1="34511@x" ObjectIDZND2="6644@x" Pin0InfoVect0LinkObjId="g_124c110_0" Pin0InfoVect1LinkObjId="EC-CX_XSL.072Ld_0" Pin0InfoVect2LinkObjId="SW-40642_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a630e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-272 4259,-281 4186,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12bf8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-874 4238,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6662@0" ObjectIDZND0="6638@0" Pin0InfoVect0LinkObjId="g_11f22f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215023_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-874 4238,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ec6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-981 4269,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31981@x" ObjectIDND1="31978@x" ObjectIDZND0="31979@0" Pin0InfoVect0LinkObjId="SW-215022_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215019_0" Pin1InfoVect1LinkObjId="SW-215021_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-981 4269,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ec950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-981 4324,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31979@1" ObjectIDZND0="g_14ab630@0" Pin0InfoVect0LinkObjId="g_14ab630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215022_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-981 4324,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11c8460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1000 4238,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31978@0" ObjectIDZND0="31981@x" ObjectIDZND1="31979@x" Pin0InfoVect0LinkObjId="SW-215019_0" Pin0InfoVect1LinkObjId="SW-215022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215021_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1000 4238,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1216820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-981 4238,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="31978@x" ObjectIDND1="31979@x" ObjectIDZND0="31981@1" Pin0InfoVect0LinkObjId="SW-215019_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215021_0" Pin1InfoVect1LinkObjId="SW-215022_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-981 4238,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d4010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-1065 4268,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="31978@x" ObjectIDND1="0@x" ObjectIDND2="34591@1" ObjectIDZND0="31980@0" Pin0InfoVect0LinkObjId="SW-215024_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-215021_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="g_1a626c0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-1065 4268,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12bd670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-1065 4323,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31980@1" ObjectIDZND0="g_1216a80@0" Pin0InfoVect0LinkObjId="g_1216a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-1065 4323,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12bd8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1065 4238,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="34591@1" ObjectIDND2="g_125aa40@0" ObjectIDZND0="31978@1" Pin0InfoVect0LinkObjId="SW-215021_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="g_1a626c0_1" Pin1InfoVect2LinkObjId="g_125aa40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1065 4238,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14bac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1173 4286,-1173 4286,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="34591@1" ObjectIDND1="0@x" ObjectIDND2="31978@x" ObjectIDZND0="g_125aa40@0" Pin0InfoVect0LinkObjId="g_125aa40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a626c0_1" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="SW-215021_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1173 4286,-1173 4286,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14bae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-1111 4188,-1111 4188,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="31978@x" ObjectIDND1="31980@x" ObjectIDND2="34591@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-215021_0" Pin1InfoVect1LinkObjId="SW-215024_0" Pin1InfoVect2LinkObjId="g_1a626c0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-1111 4188,-1111 4188,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11bb970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1192 4238,-1173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34591@1" ObjectIDZND0="g_125aa40@0" ObjectIDZND1="0@x" ObjectIDZND2="31978@x" Pin0InfoVect0LinkObjId="g_125aa40_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="SW-215021_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a626c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1192 4238,-1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_11bbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4188,-1040 4188,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4188,-1040 4188,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11ae0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-873 4506,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31982@0" ObjectIDZND0="6638@0" Pin0InfoVect0LinkObjId="g_11f22f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215052_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-873 4506,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c2a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-921 4536,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31986@x" ObjectIDND1="31982@x" ObjectIDZND0="31987@0" Pin0InfoVect0LinkObjId="SW-215053_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215049_0" Pin1InfoVect1LinkObjId="SW-215052_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-921 4536,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12bdd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4572,-921 4591,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31987@1" ObjectIDZND0="g_11ae320@0" Pin0InfoVect0LinkObjId="g_11ae320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215053_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4572,-921 4591,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12bdfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-932 4506,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31986@0" ObjectIDZND0="31987@x" ObjectIDZND1="31982@x" Pin0InfoVect0LinkObjId="SW-215053_0" Pin0InfoVect1LinkObjId="SW-215052_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215049_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-932 4506,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1486550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-921 4506,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31986@x" ObjectIDND1="31987@x" ObjectIDZND0="31982@1" Pin0InfoVect0LinkObjId="SW-215052_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215049_0" Pin1InfoVect1LinkObjId="SW-215053_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-921 4506,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a55040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-980 4537,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="31985@x" ObjectIDND1="31986@x" ObjectIDZND0="31983@0" Pin0InfoVect0LinkObjId="SW-215055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215054_0" Pin1InfoVect1LinkObjId="SW-215049_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-980 4537,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f65e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4573,-980 4592,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31983@1" ObjectIDZND0="g_1486790@0" Pin0InfoVect0LinkObjId="g_1486790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4573,-980 4592,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f6810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-999 4506,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="31985@0" ObjectIDZND0="31983@x" ObjectIDZND1="31986@x" Pin0InfoVect0LinkObjId="SW-215055_0" Pin0InfoVect1LinkObjId="SW-215049_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215054_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-999 4506,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a35780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-980 4506,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="31985@x" ObjectIDND1="31983@x" ObjectIDZND0="31986@1" Pin0InfoVect0LinkObjId="SW-215049_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215054_0" Pin1InfoVect1LinkObjId="SW-215055_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-980 4506,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11b09c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-1064 4536,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="31985@x" ObjectIDND1="0@x" ObjectIDND2="g_12bf100@0" ObjectIDZND0="31984@0" Pin0InfoVect0LinkObjId="SW-215058_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-215054_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="g_12bf100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-1064 4536,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11aa030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4572,-1064 4591,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31984@1" ObjectIDZND0="g_1a359e0@0" Pin0InfoVect0LinkObjId="g_1a359e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4572,-1064 4591,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11aa260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1064 4506,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="31984@x" ObjectIDND1="0@x" ObjectIDND2="g_12bf100@0" ObjectIDZND0="31985@1" Pin0InfoVect0LinkObjId="SW-215054_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-215058_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="g_12bf100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1064 4506,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e5240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1172 4554,-1172 4554,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="31984@x" ObjectIDND2="31985@x" ObjectIDZND0="g_12bf100@0" Pin0InfoVect0LinkObjId="g_12bf100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="SW-215058_0" Pin1InfoVect2LinkObjId="SW-215054_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1172 4554,-1172 4554,-1163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e54a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1110 4456,-1110 4456,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="31984@x" ObjectIDND1="31985@x" ObjectIDND2="g_12bf100@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-215058_0" Pin1InfoVect1LinkObjId="SW-215054_0" Pin1InfoVect2LinkObjId="g_12bf100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1110 4456,-1110 4456,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11ce330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1110 4506,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_12bf100@0" ObjectIDND2="9166@1" ObjectIDZND0="31984@x" ObjectIDZND1="31985@x" Pin0InfoVect0LinkObjId="SW-215058_0" Pin0InfoVect1LinkObjId="SW-215054_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="g_12bf100_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1110 4506,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11ce590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1190 4506,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="9166@1" ObjectIDZND0="g_12bf100@0" ObjectIDZND1="0@x" ObjectIDZND2="31984@x" Pin0InfoVect0LinkObjId="g_12bf100_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="SW-215058_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1190 4506,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11adb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-1110 4506,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="31984@x" ObjectIDND2="31985@x" ObjectIDZND0="g_12bf100@0" ObjectIDZND1="9166@1" Pin0InfoVect0LinkObjId="g_12bf100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="SW-215058_0" Pin1InfoVect2LinkObjId="SW-215054_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-1110 4506,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_11adda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4456,-1039 4456,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4456,-1039 4456,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a57db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-933 4238,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31981@0" ObjectIDZND0="6662@1" Pin0InfoVect0LinkObjId="SW-215023_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215019_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-933 4238,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d65f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4972,-954 4991,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31989@1" ObjectIDZND0="g_14d67e0@0" Pin0InfoVect0LinkObjId="g_14d67e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215090_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4972,-954 4991,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a624d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1065 4238,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="31978@x" ObjectIDND1="31980@x" ObjectIDZND0="0@x" ObjectIDZND1="34591@1" ObjectIDZND2="g_125aa40@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_1a626c0_1" Pin0InfoVect2LinkObjId="g_125aa40_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215021_0" Pin1InfoVect1LinkObjId="SW-215024_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1065 4238,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a626c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1111 4238,-1173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="31978@x" ObjectIDND2="31980@x" ObjectIDZND0="34591@1" ObjectIDZND1="g_125aa40@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="g_125aa40_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="SW-215021_0" Pin1InfoVect2LinkObjId="SW-215024_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1111 4238,-1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_120ca60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-266 4514,-276 4538,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_1256e70@0" ObjectIDZND0="g_1a34aa0@0" ObjectIDZND1="6650@x" ObjectIDZND2="34513@x" Pin0InfoVect0LinkObjId="g_1a34aa0_0" Pin0InfoVect1LinkObjId="SW-40652_0" Pin0InfoVect2LinkObjId="EC-CX_XSL.074Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1256e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-266 4514,-276 4538,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d7ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-969 4760,-969 4760,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6664@x" ObjectIDND1="6663@x" ObjectIDZND0="g_13c8c70@0" Pin0InfoVect0LinkObjId="g_13c8c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40682_0" Pin1InfoVect1LinkObjId="SW-40681_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-969 4760,-969 4760,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a41e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4905,-896 4905,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31988@0" ObjectIDZND0="6638@0" Pin0InfoVect0LinkObjId="g_11f22f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215089_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4905,-896 4905,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a42660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4936,-954 4905,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="31989@0" ObjectIDZND0="31988@x" ObjectIDZND1="g_1252e00@0" Pin0InfoVect0LinkObjId="SW-215089_0" Pin0InfoVect1LinkObjId="g_1252e00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4936,-954 4905,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d4de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4905,-932 4905,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="31988@1" ObjectIDZND0="31989@x" ObjectIDZND1="g_1252e00@0" Pin0InfoVect0LinkObjId="SW-215090_0" Pin0InfoVect1LinkObjId="g_1252e00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215089_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4905,-932 4905,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d5040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4905,-954 4905,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="31989@x" ObjectIDND1="31988@x" ObjectIDZND0="g_1252e00@0" Pin0InfoVect0LinkObjId="g_1252e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215090_0" Pin1InfoVect1LinkObjId="SW-215089_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4905,-954 4905,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1195a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-379 5058,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6665@0" ObjectIDZND0="6637@0" Pin0InfoVect0LinkObjId="g_1a56f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40683_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-379 5058,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c84b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-834 4655,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44657@1" ObjectIDZND0="6638@0" Pin0InfoVect0LinkObjId="g_11f22f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-282791_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-834 4655,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a16f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4677,-769 4655,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44658@0" ObjectIDZND0="44657@x" ObjectIDZND1="44654@x" Pin0InfoVect0LinkObjId="SW-282791_0" Pin0InfoVect1LinkObjId="SW-282790_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-282792_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4677,-769 4655,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a23310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-798 4655,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44657@0" ObjectIDZND0="44658@x" ObjectIDZND1="44654@x" Pin0InfoVect0LinkObjId="SW-282792_0" Pin0InfoVect1LinkObjId="SW-282790_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-282791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-798 4655,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a23570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-769 4655,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44658@x" ObjectIDND1="44657@x" ObjectIDZND0="44654@1" Pin0InfoVect0LinkObjId="SW-282790_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-282792_0" Pin1InfoVect1LinkObjId="SW-282791_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-769 4655,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a237d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-715 4655,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="44654@0" ObjectIDZND0="44653@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-282790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-715 4655,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a56b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-769 4729,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44658@1" ObjectIDZND0="g_1a23a30@0" Pin0InfoVect0LinkObjId="g_1a23a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-282792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-769 4729,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a56d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-606 4655,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="44653@1" ObjectIDZND0="44655@1" Pin0InfoVect0LinkObjId="SW-282810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a237d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-606 4655,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a56f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-466 4655,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44656@0" ObjectIDZND0="6637@0" Pin0InfoVect0LinkObjId="g_1195a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-282811_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-466 4655,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f8630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-254 3995,-43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1a4a610@0" ObjectIDND1="6641@x" ObjectIDZND0="34510@0" Pin0InfoVect0LinkObjId="EC-CX_XSL.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a4a610_0" Pin1InfoVect1LinkObjId="SW-40637_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-254 3995,-43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14879d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4022,-244 4022,-254 3995,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1a4a610@0" ObjectIDZND0="6641@x" ObjectIDZND1="34510@x" Pin0InfoVect0LinkObjId="SW-40637_0" Pin0InfoVect1LinkObjId="EC-CX_XSL.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a4a610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4022,-244 4022,-254 3995,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1487c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-254 3995,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1a4a610@0" ObjectIDND1="34510@x" ObjectIDZND0="6641@0" Pin0InfoVect0LinkObjId="SW-40637_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a4a610_0" Pin1InfoVect1LinkObjId="EC-CX_XSL.071Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-254 3995,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1487e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-259 4186,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_124c110@0" ObjectIDND1="6644@x" ObjectIDND2="g_1a630e0@0" ObjectIDZND0="34511@0" Pin0InfoVect0LinkObjId="EC-CX_XSL.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_124c110_0" Pin1InfoVect1LinkObjId="SW-40642_0" Pin1InfoVect2LinkObjId="g_1a630e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-259 4186,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a4bcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-249 4213,-259 4186,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_124c110@0" ObjectIDZND0="34511@x" ObjectIDZND1="6644@x" ObjectIDZND2="g_1a630e0@0" Pin0InfoVect0LinkObjId="EC-CX_XSL.072Ld_0" Pin0InfoVect1LinkObjId="SW-40642_0" Pin0InfoVect2LinkObjId="g_1a630e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_124c110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-249 4213,-259 4186,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a4bef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-259 4361,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1a1f050@0" ObjectIDND1="6647@x" ObjectIDZND0="34512@0" Pin0InfoVect0LinkObjId="EC-CX_XSL.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a1f050_0" Pin1InfoVect1LinkObjId="SW-40647_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-259 4361,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a362c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4388,-249 4388,-259 4361,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1a1f050@0" ObjectIDZND0="6647@x" ObjectIDZND1="34512@x" Pin0InfoVect0LinkObjId="SW-40647_0" Pin0InfoVect1LinkObjId="EC-CX_XSL.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a1f050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4388,-249 4388,-259 4361,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a36500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-259 4361,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1a1f050@0" ObjectIDND1="34512@x" ObjectIDZND0="6647@0" Pin0InfoVect0LinkObjId="SW-40647_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a1f050_0" Pin1InfoVect1LinkObjId="EC-CX_XSL.073Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-259 4361,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a36760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4538,-276 4538,-16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_1a34aa0@0" ObjectIDND1="6650@x" ObjectIDND2="g_1256e70@0" ObjectIDZND0="34513@0" Pin0InfoVect0LinkObjId="EC-CX_XSL.074Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a34aa0_0" Pin1InfoVect1LinkObjId="SW-40652_0" Pin1InfoVect2LinkObjId="g_1256e70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4538,-276 4538,-16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a369c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-257 4715,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_120ccc0@0" ObjectIDND1="6653@x" ObjectIDZND0="34514@0" Pin0InfoVect0LinkObjId="EC-CX_XSL.075Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_120ccc0_0" Pin1InfoVect1LinkObjId="SW-40657_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-257 4715,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-247 4742,-257 4715,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_120ccc0@0" ObjectIDZND0="6653@x" ObjectIDZND1="34514@x" Pin0InfoVect0LinkObjId="SW-40657_0" Pin0InfoVect1LinkObjId="EC-CX_XSL.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_120ccc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-247 4742,-257 4715,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-257 4715,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_120ccc0@0" ObjectIDND1="34514@x" ObjectIDZND0="6653@0" Pin0InfoVect0LinkObjId="SW-40657_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_120ccc0_0" Pin1InfoVect1LinkObjId="EC-CX_XSL.075Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-257 4715,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2bd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-554 4655,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44655@0" ObjectIDZND0="44656@1" Pin0InfoVect0LinkObjId="SW-282811_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-282810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-554 4655,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a09c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-259 4186,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_124c110@0" ObjectIDND1="34511@x" ObjectIDZND0="6644@x" ObjectIDZND1="g_1a630e0@0" Pin0InfoVect0LinkObjId="SW-40642_0" Pin0InfoVect1LinkObjId="g_1a630e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_124c110_0" Pin1InfoVect1LinkObjId="EC-CX_XSL.072Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-259 4186,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a09ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-281 4186,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_124c110@0" ObjectIDND1="34511@x" ObjectIDND2="g_1a630e0@0" ObjectIDZND0="6644@0" Pin0InfoVect0LinkObjId="SW-40642_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_124c110_0" Pin1InfoVect1LinkObjId="EC-CX_XSL.072Ld_0" Pin1InfoVect2LinkObjId="g_1a630e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-281 4186,-295 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XSL"/>
</svg>