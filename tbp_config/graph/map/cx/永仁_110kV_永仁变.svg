<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-8" aopId="3941118" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1236 2155 1314">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape15">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="50" x2="47" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="41" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="capacitor:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="52" x2="52" y1="45" y2="16"/>
    <polyline arcFlag="1" points="25,101 23,101 21,100 20,100 18,99 17,98 15,97 14,95 13,93 13,92 12,90 12,88 12,86 13,84 13,83 14,81 15,80 17,78 18,77 20,76 21,76 23,75 25,75 27,75 29,76 30,76 32,77 33,78 35,80 36,81 37,83 37,84 38,86 38,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="38" x2="26" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="100" y2="108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="54" y2="47"/>
    <polyline arcFlag="1" points="43,15 44,15 45,15 45,15 46,15 46,16 47,16 47,17 48,17 48,18 48,18 48,19 49,20 49,20 49,21 48,22 48,22 48,23 48,23 47,24 47,24 46,25 46,25 45,25 45,26 44,26 43,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,25 44,25 45,25 45,25 46,26 46,26 47,26 47,27 48,27 48,28 48,29 48,29 49,30 49,31 49,31 48,32 48,33 48,33 48,34 47,34 47,35 46,35 46,35 45,36 45,36 44,36 43,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,36 44,36 45,36 45,37 46,37 46,37 47,38 47,38 48,39 48,39 48,40 48,40 49,41 49,42 49,42 48,43 48,44 48,44 48,45 47,45 47,46 46,46 46,47 45,47 45,47 44,47 43,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="26" x2="26" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="8" y2="8"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="23"/>
    <rect height="23" stroke-width="0.398039" width="12" x="20" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="8" x2="8" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="43" x2="43" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="16" y2="24"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="0" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="9" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="7" y2="5"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="28" stroke-width="1" width="14" x="0" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="51" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="52" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="4" y2="39"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="35" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.337605" x1="7" x2="7" y1="5" y2="16"/>
    <polyline arcFlag="1" points="7,28 7,28 7,28 7,28 8,28 8,28 8,27 8,27 9,27 9,27 9,26 9,26 9,26 9,25 9,25 9,24 9,24 9,24 9,23 8,23 8,23 8,23 8,22 7,22 7,22 7,22 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="6,34 7,34 7,34 7,34 7,34 8,34 8,34 8,33 8,33 8,33 8,32 9,32 9,32 9,31 9,31 9,31 8,30 8,30 8,30 8,29 8,29 8,29 7,29 7,28 7,28 7,28 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="7,22 7,22 7,22 7,22 8,22 8,21 8,21 8,21 9,21 9,20 9,20 9,20 9,19 9,19 9,19 9,18 9,18 9,17 9,17 8,17 8,17 8,16 8,16 7,16 7,16 7,16 " stroke-width="0.0170053"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.266312" x1="5" x2="8" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="4" x2="9" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="0" x2="13" y1="46" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape107">
    <circle cx="7" cy="18" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="26" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="11" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="11" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="12" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="1" y1="39" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="6" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="4" x2="10" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.167832" x1="7" x2="7" y1="38" y2="32"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="4" x2="13" y1="40" y2="40"/>
    <circle cx="24" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="34" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="13" x2="13" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="38" x2="20" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="47" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape111">
    <rect height="16" stroke-width="0.5" width="32" x="4" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="6" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape46">
    <circle cx="18" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="10" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="lightningRod:shape95">
    <ellipse cx="20" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="34" x2="29" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="34" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="31" x2="29" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="21" x2="21" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="9" x2="0" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="6" x2="3" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="7" x2="2" y1="4" y2="4"/>
    <polyline points="21,19 5,19 5,6 " stroke-width="1"/>
    <ellipse cx="31" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="20" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="31" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <rect height="29" stroke-width="2" width="15" x="1" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="8" x2="11" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="9" x2="6" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.8" x1="8" x2="8" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="11" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="1" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="51" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="36" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="9" y2="9"/>
    <rect height="16" stroke-width="2" width="31" x="6" y="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape1_0">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape1_1">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape49">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="27" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="27" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="34" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="13" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="13" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="20" y1="8" y2="8"/>
    <circle cx="29" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2542150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2577db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ded9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24f5120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30a8e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30a6680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30e4700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30e51a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30e6820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30e6820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bb5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bb5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bd050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bd050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_31be070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bfce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31c0950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e3f100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e3f850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e410a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e41ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e423b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e42b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e43bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e44570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e45060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e45a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31c95a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31ca520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31cb620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31cc2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31da660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31d2a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31cd3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31ce760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1324" width="2165" x="3112" y="-1241"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e3fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 1196.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36eceb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3588.000000 1181.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39dec00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3613.000000 1166.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3689740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.000000 1074.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31daa70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4988.000000 1059.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31dac80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 1044.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36affc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.000000 831.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3693750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4988.000000 816.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3693940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 801.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3724400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4861.000000 768.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3690730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.000000 753.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3690930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 738.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36913d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.000000 718.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ac620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4988.000000 703.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ac7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 688.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31eddc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.000000 524.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dedd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4988.000000 509.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dedf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 494.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e85890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 705.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2510790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4345.000000 690.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25109a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 675.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebfb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 515.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3e700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4372.000000 500.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3e900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4397.000000 485.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c76f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 470.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39dc990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.000000 455.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39dcbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 440.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cf6290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 470.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38d9ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 455.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38d9de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 440.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3771c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3461.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30bd880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3450.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30bdaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3475.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e877c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f67b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3580.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f69f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3605.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_350ff70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373a8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3844.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373ab10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ad570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4289.000000 237.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32139d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.000000 222.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3213ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 207.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3394290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39cd880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39cda90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3277a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3698f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3699140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cf2d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4627.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3789d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3789fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e0700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4747.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32aa860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32aaaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4761.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_363f450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4869.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e43e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4858.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e4620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31caa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 818.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_378b300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4207.000000 803.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_378b540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 788.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e22400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -23.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ae940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -38.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e7390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 -4.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e7690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4949.000000 -19.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ac9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5167.000000 -4.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32acc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5192.000000 -19.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3686110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3655.000000 659.000000) translate(0,12)">档位（档）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_257bbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3655.000000 644.000000) translate(0,12)">油温（℃）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_257bf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3625.000000 629.000000) translate(0,12)">绕组温度（℃）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380cd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4235.000000 617.000000) translate(0,12)">档位（档）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a8140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4235.000000 602.000000) translate(0,12)">油温（℃）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a8380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4205.000000 587.000000) translate(0,12)">绕组温度（℃）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250d730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 770.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2505e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 755.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25060c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3890.000000 740.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3292340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364df50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23efbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324a260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3540.000000 830.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324a590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3534.000000 845.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324a7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3534.000000 873.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324a9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3534.000000 860.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324abf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3526.000000 815.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324adc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 801.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324b170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3429.000000 423.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324b3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3423.000000 438.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324b600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3423.000000 466.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324b840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3423.000000 453.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324ba80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3415.000000 408.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324bcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3431.000000 394.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324bff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5188.000000 408.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324c270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5182.000000 423.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324c4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5182.000000 451.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324c6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5182.000000 438.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324c930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5174.000000 393.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324cb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5190.000000 379.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324cea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4628.000000 1013.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324d120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 1028.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324d360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 1056.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324d5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 1043.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324d7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 998.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324da20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 984.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324dd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4628.000000 678.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324dfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 693.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324e210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 721.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324e450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 708.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324e690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 663.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324e8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 649.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-19328">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.000000 -257.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2992" ObjectName="SW-CX_YR.CX_YR_012BK"/>
     <cge:Meas_Ref ObjectId="19328"/>
    <cge:TPSR_Ref TObjectID="2992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19313">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2977" ObjectName="SW-CX_YR.CX_YR_081BK"/>
     <cge:Meas_Ref ObjectId="19313"/>
    <cge:TPSR_Ref TObjectID="2977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19317">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.166667 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2981" ObjectName="SW-CX_YR.CX_YR_082BK"/>
     <cge:Meas_Ref ObjectId="19317"/>
    <cge:TPSR_Ref TObjectID="2981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.333333 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2984" ObjectName="SW-CX_YR.CX_YR_083BK"/>
     <cge:Meas_Ref ObjectId="19320"/>
    <cge:TPSR_Ref TObjectID="2984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4784.500000 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2987" ObjectName="SW-CX_YR.CX_YR_084BK"/>
     <cge:Meas_Ref ObjectId="19323"/>
    <cge:TPSR_Ref TObjectID="2987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19326">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4902.666667 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2990" ObjectName="SW-CX_YR.CX_YR_085BK"/>
     <cge:Meas_Ref ObjectId="19326"/>
    <cge:TPSR_Ref TObjectID="2990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19239">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.833333 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2908" ObjectName="SW-CX_YR.CX_YR_086BK"/>
     <cge:Meas_Ref ObjectId="19239"/>
    <cge:TPSR_Ref TObjectID="2908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19238">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5151.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2907" ObjectName="SW-CX_YR.CX_YR_087BK"/>
     <cge:Meas_Ref ObjectId="19238"/>
    <cge:TPSR_Ref TObjectID="2907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19334">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 -727.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2997" ObjectName="SW-CX_YR.CX_YR_101BK"/>
     <cge:Meas_Ref ObjectId="19334"/>
    <cge:TPSR_Ref TObjectID="2997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19344">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3795.000000 -426.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3006" ObjectName="SW-CX_YR.CX_YR_001BK"/>
     <cge:Meas_Ref ObjectId="19344"/>
    <cge:TPSR_Ref TObjectID="3006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19252">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 -424.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2919" ObjectName="SW-CX_YR.CX_YR_002BK"/>
     <cge:Meas_Ref ObjectId="19252"/>
    <cge:TPSR_Ref TObjectID="2919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19248">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -618.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2915" ObjectName="SW-CX_YR.CX_YR_302BK"/>
     <cge:Meas_Ref ObjectId="19248"/>
    <cge:TPSR_Ref TObjectID="2915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19341">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 -516.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3003" ObjectName="SW-CX_YR.CX_YR_301BK"/>
     <cge:Meas_Ref ObjectId="19341"/>
    <cge:TPSR_Ref TObjectID="3003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19299">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3508.000000 -239.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2963" ObjectName="SW-CX_YR.CX_YR_061BK"/>
     <cge:Meas_Ref ObjectId="19299"/>
    <cge:TPSR_Ref TObjectID="2963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3637.600000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2967" ObjectName="SW-CX_YR.CX_YR_062BK"/>
     <cge:Meas_Ref ObjectId="19303"/>
    <cge:TPSR_Ref TObjectID="2967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.200000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2971" ObjectName="SW-CX_YR.CX_YR_063BK"/>
     <cge:Meas_Ref ObjectId="19307"/>
    <cge:TPSR_Ref TObjectID="2971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19311">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.800000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2975" ObjectName="SW-CX_YR.CX_YR_064BK"/>
     <cge:Meas_Ref ObjectId="19311"/>
    <cge:TPSR_Ref TObjectID="2975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19229">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2898" ObjectName="SW-CX_YR.CX_YR_066BK"/>
     <cge:Meas_Ref ObjectId="19229"/>
    <cge:TPSR_Ref TObjectID="2898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19256">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.000000 -948.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2922" ObjectName="SW-CX_YR.CX_YR_161BK"/>
     <cge:Meas_Ref ObjectId="19256"/>
    <cge:TPSR_Ref TObjectID="2922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19263">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.666667 -948.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2928" ObjectName="SW-CX_YR.CX_YR_162BK"/>
     <cge:Meas_Ref ObjectId="19263"/>
    <cge:TPSR_Ref TObjectID="2928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19270">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.333333 -948.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2934" ObjectName="SW-CX_YR.CX_YR_163BK"/>
     <cge:Meas_Ref ObjectId="19270"/>
    <cge:TPSR_Ref TObjectID="2934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19286">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4790.000000 -988.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2950" ObjectName="SW-CX_YR.CX_YR_362BK"/>
     <cge:Meas_Ref ObjectId="19286"/>
    <cge:TPSR_Ref TObjectID="2950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19283">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 -829.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2947" ObjectName="SW-CX_YR.CX_YR_361BK"/>
     <cge:Meas_Ref ObjectId="19283"/>
    <cge:TPSR_Ref TObjectID="2947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -726.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2959" ObjectName="SW-CX_YR.CX_YR_312BK"/>
     <cge:Meas_Ref ObjectId="19295"/>
    <cge:TPSR_Ref TObjectID="2959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19240">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -773.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2909" ObjectName="SW-CX_YR.CX_YR_102BK"/>
     <cge:Meas_Ref ObjectId="19240"/>
    <cge:TPSR_Ref TObjectID="2909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19289">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 -543.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2953" ObjectName="SW-CX_YR.CX_YR_363BK"/>
     <cge:Meas_Ref ObjectId="19289"/>
    <cge:TPSR_Ref TObjectID="2953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19292">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4790.000000 -639.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2956" ObjectName="SW-CX_YR.CX_YR_364BK"/>
     <cge:Meas_Ref ObjectId="19292"/>
    <cge:TPSR_Ref TObjectID="2956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58511">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -947.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11082" ObjectName="SW-CX_YR.CX_YR_164BK"/>
     <cge:Meas_Ref ObjectId="58511"/>
    <cge:TPSR_Ref TObjectID="11082"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YR.CX_YR_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="4403"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -536.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="4405"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -536.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="4407"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -536.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="3020" ObjectName="TF-CX_YR.CX_YR_1T"/>
    <cge:TPSR_Ref TObjectID="3020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YR.CX_YR_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="4396"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -581.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="4398"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -581.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="4400"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -581.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="3019" ObjectName="TF-CX_YR.CX_YR_2T"/>
    <cge:TPSR_Ref TObjectID="3019"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YR.CX_YR_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3500,-349 4311,-349 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2892" ObjectName="BS-CX_YR.CX_YR_9IM"/>
    <cge:TPSR_Ref TObjectID="2892"/></metadata>
   <polyline fill="none" opacity="0" points="3500,-349 4311,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YR.CX_YR_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-349 5205,-349 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2893" ObjectName="BS-CX_YR.CX_YR_9IIM"/>
    <cge:TPSR_Ref TObjectID="2893"/></metadata>
   <polyline fill="none" opacity="0" points="4373,-349 5205,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YR.CX_YR_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3474,-887 4479,-887 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2890" ObjectName="BS-CX_YR.CX_YR_1IM"/>
    <cge:TPSR_Ref TObjectID="2890"/></metadata>
   <polyline fill="none" opacity="0" points="3474,-887 4479,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YR.CX_YR_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-1055 4716,-776 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2891" ObjectName="BS-CX_YR.CX_YR_3IM"/>
    <cge:TPSR_Ref TObjectID="2891"/></metadata>
   <polyline fill="none" opacity="0" points="4716,-1055 4716,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YR.CX_YR_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-716 4717,-437 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3021" ObjectName="BS-CX_YR.CX_YR_3IIM"/>
    <cge:TPSR_Ref TObjectID="3021"/></metadata>
   <polyline fill="none" opacity="0" points="4717,-716 4717,-437 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_YR.CX_YR_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 11.000000)" xlink:href="#capacitor:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10513" ObjectName="CB-CX_YR.CX_YR_Cb1"/>
    <cge:TPSR_Ref TObjectID="10513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_YR.CX_YR_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5018.000000 -11.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10512" ObjectName="CB-CX_YR.CX_YR_Cb2"/>
    <cge:TPSR_Ref TObjectID="10512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_YR.CX_YR_Cb3">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.000000 -14.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10472" ObjectName="CB-CX_YR.CX_YR_Cb3"/>
    <cge:TPSR_Ref TObjectID="10472"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YR.CX_YR_0651_1TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16742"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.640000 -0.000000 0.000000 -0.711111 4034.000000 -97.000000)" xlink:href="#transformer2:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.640000 -0.000000 0.000000 -0.711111 4034.000000 -97.000000)" xlink:href="#transformer2:shape1_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="11914" ObjectName="TF-CX_YR.CX_YR_0651_1TZyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5017.000000 -566.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5017.000000 -566.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_38ebbd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 -182.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39e2290">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4441.000000 -116.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37401a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.166667 -182.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a5210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.166667 -116.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e1ef30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.333333 -184.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e25510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4681.333333 -116.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35afde0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4789.500000 -184.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d0cc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.500000 -116.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32174d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.666667 -184.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25596b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.666667 -116.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_248c250">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5036.833333 -189.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e83f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5155.000000 -189.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e300b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3721.000000 -533.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23fad20">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 -537.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dc04e0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 -617.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d74b60">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 -620.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33456e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -637.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36caf20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -531.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2439ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4080.000000 -559.000000)" xlink:href="#lightningRod:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d95610">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 -559.000000)" xlink:href="#lightningRod:shape107"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_350eee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 -177.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dbc870">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 -116.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3755c00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3641.600000 -178.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b0800">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3655.600000 -117.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b56b10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3779.200000 -181.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c800e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.200000 -117.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32a0400">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3904.800000 -178.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3209a60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.800000 -117.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2501810">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.400000 -239.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_341e660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.400000 -172.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df4070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -189.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38ec390">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -1065.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3692ee0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -1133.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3672f10">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3768.000000 -1065.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c2e050">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3769.000000 -1133.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f7910">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3936.000000 -1065.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f6920">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3937.000000 -1133.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37a8060">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 -1078.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a3c70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4382.000000 -1100.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d66aa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 -1012.000000)" xlink:href="#lightningRod:shape111"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_251e4b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4962.000000 -1013.000000)" xlink:href="#lightningRod:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b31b40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4827.000000 -880.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e2abe0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4869.500000 -914.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3698020">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -970.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36be100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -812.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d85a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 -474.000000)" xlink:href="#lightningRod:shape111"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31baeb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4831.000000 -439.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b3a10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3548.000000 -413.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7e8f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3603.000000 -509.000000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3331ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3614.000000 -433.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3394670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 -411.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37bc720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4581.000000 -507.000000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c10b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 -431.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30cfe90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 -571.000000)" xlink:href="#lightningRod:shape111"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e1c00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4916.000000 -522.000000)" xlink:href="#lightningRod:shape111"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3687120">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -523.000000)" xlink:href="#lightningRod:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36664f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5015.000000 -526.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3634160">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4931.000000 -621.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33974f0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5037.000000 -644.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31bc000">
    <use class="BV-35KV" transform="matrix(0.529412 -0.000000 0.000000 0.854545 4038.000000 -608.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_368c580">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -1064.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32a58a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4248.000000 -1133.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3245a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4901.000000 -666.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-19014" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -600.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19014" ObjectName="CX_YR:CX_YR_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -585.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-19187" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -642.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19187" ObjectName="CX_YR:CX_YR_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -627.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3215.000000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78569" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3288.538462 -987.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78569" ObjectName="CX_YR:CX_YR_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79753" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3288.538462 -945.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79753" ObjectName="CX_YR:CX_YR_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-18993" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3657.000000 -1196.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2922"/>
     <cge:Term_Ref ObjectID="4206"/>
    <cge:TPSR_Ref TObjectID="2922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-18994" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3657.000000 -1196.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2922"/>
     <cge:Term_Ref ObjectID="4206"/>
    <cge:TPSR_Ref TObjectID="2922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-18990" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3657.000000 -1196.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2922"/>
     <cge:Term_Ref ObjectID="4206"/>
    <cge:TPSR_Ref TObjectID="2922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19002" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -1198.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2928"/>
     <cge:Term_Ref ObjectID="4218"/>
    <cge:TPSR_Ref TObjectID="2928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19003" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -1198.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2928"/>
     <cge:Term_Ref ObjectID="4218"/>
    <cge:TPSR_Ref TObjectID="2928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-18998" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -1198.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2928"/>
     <cge:Term_Ref ObjectID="4218"/>
    <cge:TPSR_Ref TObjectID="2928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19168" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -237.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2992"/>
     <cge:Term_Ref ObjectID="4346"/>
    <cge:TPSR_Ref TObjectID="2992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19169" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -237.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2992"/>
     <cge:Term_Ref ObjectID="4346"/>
    <cge:TPSR_Ref TObjectID="2992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19164" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -237.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2992"/>
     <cge:Term_Ref ObjectID="4346"/>
    <cge:TPSR_Ref TObjectID="2992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19133" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4433.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2977"/>
     <cge:Term_Ref ObjectID="4316"/>
    <cge:TPSR_Ref TObjectID="2977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19134" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4433.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2977"/>
     <cge:Term_Ref ObjectID="4316"/>
    <cge:TPSR_Ref TObjectID="2977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19129" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4433.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2977"/>
     <cge:Term_Ref ObjectID="4316"/>
    <cge:TPSR_Ref TObjectID="2977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19140" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2981"/>
     <cge:Term_Ref ObjectID="4324"/>
    <cge:TPSR_Ref TObjectID="2981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19141" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2981"/>
     <cge:Term_Ref ObjectID="4324"/>
    <cge:TPSR_Ref TObjectID="2981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19136" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2981"/>
     <cge:Term_Ref ObjectID="4324"/>
    <cge:TPSR_Ref TObjectID="2981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19147" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4682.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2984"/>
     <cge:Term_Ref ObjectID="4330"/>
    <cge:TPSR_Ref TObjectID="2984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19148" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4682.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2984"/>
     <cge:Term_Ref ObjectID="4330"/>
    <cge:TPSR_Ref TObjectID="2984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19143" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4682.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2984"/>
     <cge:Term_Ref ObjectID="4330"/>
    <cge:TPSR_Ref TObjectID="2984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19154" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4804.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2987"/>
     <cge:Term_Ref ObjectID="4336"/>
    <cge:TPSR_Ref TObjectID="2987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19155" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4804.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2987"/>
     <cge:Term_Ref ObjectID="4336"/>
    <cge:TPSR_Ref TObjectID="2987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19150" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4804.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2987"/>
     <cge:Term_Ref ObjectID="4336"/>
    <cge:TPSR_Ref TObjectID="2987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19161" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2990"/>
     <cge:Term_Ref ObjectID="4342"/>
    <cge:TPSR_Ref TObjectID="2990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19162" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2990"/>
     <cge:Term_Ref ObjectID="4342"/>
    <cge:TPSR_Ref TObjectID="2990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19157" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2990"/>
     <cge:Term_Ref ObjectID="4342"/>
    <cge:TPSR_Ref TObjectID="2990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19031" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -470.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2919"/>
     <cge:Term_Ref ObjectID="4200"/>
    <cge:TPSR_Ref TObjectID="2919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19032" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -470.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2919"/>
     <cge:Term_Ref ObjectID="4200"/>
    <cge:TPSR_Ref TObjectID="2919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19028" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -470.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19028" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2919"/>
     <cge:Term_Ref ObjectID="4200"/>
    <cge:TPSR_Ref TObjectID="2919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19025" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4416.000000 -705.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19025" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2915"/>
     <cge:Term_Ref ObjectID="4192"/>
    <cge:TPSR_Ref TObjectID="2915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19026" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4416.000000 -705.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2915"/>
     <cge:Term_Ref ObjectID="4192"/>
    <cge:TPSR_Ref TObjectID="2915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19022" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4416.000000 -705.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2915"/>
     <cge:Term_Ref ObjectID="4192"/>
    <cge:TPSR_Ref TObjectID="2915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19198" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4443.000000 -514.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3003"/>
     <cge:Term_Ref ObjectID="4368"/>
    <cge:TPSR_Ref TObjectID="3003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19199" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4443.000000 -514.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19199" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3003"/>
     <cge:Term_Ref ObjectID="4368"/>
    <cge:TPSR_Ref TObjectID="3003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19195" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4443.000000 -514.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19195" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3003"/>
     <cge:Term_Ref ObjectID="4368"/>
    <cge:TPSR_Ref TObjectID="3003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19126" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2975"/>
     <cge:Term_Ref ObjectID="4312"/>
    <cge:TPSR_Ref TObjectID="2975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19127" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2975"/>
     <cge:Term_Ref ObjectID="4312"/>
    <cge:TPSR_Ref TObjectID="2975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19122" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2975"/>
     <cge:Term_Ref ObjectID="4312"/>
    <cge:TPSR_Ref TObjectID="2975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19010" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -1195.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2934"/>
     <cge:Term_Ref ObjectID="4230"/>
    <cge:TPSR_Ref TObjectID="2934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19011" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -1195.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19011" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2934"/>
     <cge:Term_Ref ObjectID="4230"/>
    <cge:TPSR_Ref TObjectID="2934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19006" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -1195.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2934"/>
     <cge:Term_Ref ObjectID="4230"/>
    <cge:TPSR_Ref TObjectID="2934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19077" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -1074.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2950"/>
     <cge:Term_Ref ObjectID="4262"/>
    <cge:TPSR_Ref TObjectID="2950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19078" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -1074.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19078" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2950"/>
     <cge:Term_Ref ObjectID="4262"/>
    <cge:TPSR_Ref TObjectID="2950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19073" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -1074.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2950"/>
     <cge:Term_Ref ObjectID="4262"/>
    <cge:TPSR_Ref TObjectID="2950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19070" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -831.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2947"/>
     <cge:Term_Ref ObjectID="4256"/>
    <cge:TPSR_Ref TObjectID="2947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19071" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -831.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19071" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2947"/>
     <cge:Term_Ref ObjectID="4256"/>
    <cge:TPSR_Ref TObjectID="2947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19066" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -831.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19066" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2947"/>
     <cge:Term_Ref ObjectID="4256"/>
    <cge:TPSR_Ref TObjectID="2947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19098" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4919.000000 -768.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2959"/>
     <cge:Term_Ref ObjectID="4280"/>
    <cge:TPSR_Ref TObjectID="2959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19099" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4919.000000 -768.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2959"/>
     <cge:Term_Ref ObjectID="4280"/>
    <cge:TPSR_Ref TObjectID="2959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19094" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4919.000000 -768.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2959"/>
     <cge:Term_Ref ObjectID="4280"/>
    <cge:TPSR_Ref TObjectID="2959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19019" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4279.000000 -818.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2909"/>
     <cge:Term_Ref ObjectID="4180"/>
    <cge:TPSR_Ref TObjectID="2909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19020" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4279.000000 -818.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19020" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2909"/>
     <cge:Term_Ref ObjectID="4180"/>
    <cge:TPSR_Ref TObjectID="2909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19016" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4279.000000 -818.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19016" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2909"/>
     <cge:Term_Ref ObjectID="4180"/>
    <cge:TPSR_Ref TObjectID="2909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19091" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.500000 -718.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2956"/>
     <cge:Term_Ref ObjectID="4274"/>
    <cge:TPSR_Ref TObjectID="2956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19092" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.500000 -718.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2956"/>
     <cge:Term_Ref ObjectID="4274"/>
    <cge:TPSR_Ref TObjectID="2956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19087" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.500000 -718.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2956"/>
     <cge:Term_Ref ObjectID="4274"/>
    <cge:TPSR_Ref TObjectID="2956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19192" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -770.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19192" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2997"/>
     <cge:Term_Ref ObjectID="4356"/>
    <cge:TPSR_Ref TObjectID="2997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19193" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -770.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19193" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2997"/>
     <cge:Term_Ref ObjectID="4356"/>
    <cge:TPSR_Ref TObjectID="2997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19189" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -770.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2997"/>
     <cge:Term_Ref ObjectID="4356"/>
    <cge:TPSR_Ref TObjectID="2997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19204" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.000000 -470.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3006"/>
     <cge:Term_Ref ObjectID="4374"/>
    <cge:TPSR_Ref TObjectID="3006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19205" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.000000 -470.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3006"/>
     <cge:Term_Ref ObjectID="4374"/>
    <cge:TPSR_Ref TObjectID="3006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19201" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.000000 -470.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3006"/>
     <cge:Term_Ref ObjectID="4374"/>
    <cge:TPSR_Ref TObjectID="3006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19105" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3523.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2963"/>
     <cge:Term_Ref ObjectID="4288"/>
    <cge:TPSR_Ref TObjectID="2963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19106" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3523.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2963"/>
     <cge:Term_Ref ObjectID="4288"/>
    <cge:TPSR_Ref TObjectID="2963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19101" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3523.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2963"/>
     <cge:Term_Ref ObjectID="4288"/>
    <cge:TPSR_Ref TObjectID="2963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19112" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3652.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2967"/>
     <cge:Term_Ref ObjectID="4296"/>
    <cge:TPSR_Ref TObjectID="2967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19113" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3652.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2967"/>
     <cge:Term_Ref ObjectID="4296"/>
    <cge:TPSR_Ref TObjectID="2967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19108" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3652.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2967"/>
     <cge:Term_Ref ObjectID="4296"/>
    <cge:TPSR_Ref TObjectID="2967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-19119" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2971"/>
     <cge:Term_Ref ObjectID="4304"/>
    <cge:TPSR_Ref TObjectID="2971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19120" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2971"/>
     <cge:Term_Ref ObjectID="4304"/>
    <cge:TPSR_Ref TObjectID="2971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19115" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2971"/>
     <cge:Term_Ref ObjectID="4304"/>
    <cge:TPSR_Ref TObjectID="2971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19186" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 26.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19186" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2898"/>
     <cge:Term_Ref ObjectID="4158"/>
    <cge:TPSR_Ref TObjectID="2898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19185" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 26.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2898"/>
     <cge:Term_Ref ObjectID="4158"/>
    <cge:TPSR_Ref TObjectID="2898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19393" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2908"/>
     <cge:Term_Ref ObjectID="4178"/>
    <cge:TPSR_Ref TObjectID="2908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19392" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2908"/>
     <cge:Term_Ref ObjectID="4178"/>
    <cge:TPSR_Ref TObjectID="2908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-19395" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5232.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2907"/>
     <cge:Term_Ref ObjectID="4176"/>
    <cge:TPSR_Ref TObjectID="2907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-19394" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5232.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2907"/>
     <cge:Term_Ref ObjectID="4176"/>
    <cge:TPSR_Ref TObjectID="2907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-19034" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3591.000000 -876.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19034" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2890"/>
     <cge:Term_Ref ObjectID="4146"/>
    <cge:TPSR_Ref TObjectID="2890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-19035" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3591.000000 -876.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19035" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2890"/>
     <cge:Term_Ref ObjectID="4146"/>
    <cge:TPSR_Ref TObjectID="2890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-19036" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3591.000000 -876.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19036" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2890"/>
     <cge:Term_Ref ObjectID="4146"/>
    <cge:TPSR_Ref TObjectID="2890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-19037" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3591.000000 -876.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2890"/>
     <cge:Term_Ref ObjectID="4146"/>
    <cge:TPSR_Ref TObjectID="2890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-19038" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3591.000000 -876.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2890"/>
     <cge:Term_Ref ObjectID="4146"/>
    <cge:TPSR_Ref TObjectID="2890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-19041" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3591.000000 -876.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2890"/>
     <cge:Term_Ref ObjectID="4146"/>
    <cge:TPSR_Ref TObjectID="2890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-19058" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5241.000000 -454.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19058" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2893"/>
     <cge:Term_Ref ObjectID="4149"/>
    <cge:TPSR_Ref TObjectID="2893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-19059" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5241.000000 -454.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2893"/>
     <cge:Term_Ref ObjectID="4149"/>
    <cge:TPSR_Ref TObjectID="2893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-19060" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5241.000000 -454.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2893"/>
     <cge:Term_Ref ObjectID="4149"/>
    <cge:TPSR_Ref TObjectID="2893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-19061" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5241.000000 -454.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2893"/>
     <cge:Term_Ref ObjectID="4149"/>
    <cge:TPSR_Ref TObjectID="2893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-19062" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5241.000000 -454.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2893"/>
     <cge:Term_Ref ObjectID="4149"/>
    <cge:TPSR_Ref TObjectID="2893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-19065" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5241.000000 -454.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2893"/>
     <cge:Term_Ref ObjectID="4149"/>
    <cge:TPSR_Ref TObjectID="2893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-19384" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -721.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3021"/>
     <cge:Term_Ref ObjectID="4409"/>
    <cge:TPSR_Ref TObjectID="3021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-19385" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -721.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3021"/>
     <cge:Term_Ref ObjectID="4409"/>
    <cge:TPSR_Ref TObjectID="3021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-19386" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -721.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3021"/>
     <cge:Term_Ref ObjectID="4409"/>
    <cge:TPSR_Ref TObjectID="3021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-19387" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -721.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3021"/>
     <cge:Term_Ref ObjectID="4409"/>
    <cge:TPSR_Ref TObjectID="3021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-19388" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -721.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3021"/>
     <cge:Term_Ref ObjectID="4409"/>
    <cge:TPSR_Ref TObjectID="3021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-19391" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -721.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3021"/>
     <cge:Term_Ref ObjectID="4409"/>
    <cge:TPSR_Ref TObjectID="3021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-19015" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -618.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3019"/>
     <cge:Term_Ref ObjectID="4397"/>
    <cge:TPSR_Ref TObjectID="3019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-19188" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -659.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3020"/>
     <cge:Term_Ref ObjectID="4404"/>
    <cge:TPSR_Ref TObjectID="3020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-19050" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3480.000000 -465.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19050" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2892"/>
     <cge:Term_Ref ObjectID="4148"/>
    <cge:TPSR_Ref TObjectID="2892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-19051" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3480.000000 -465.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2892"/>
     <cge:Term_Ref ObjectID="4148"/>
    <cge:TPSR_Ref TObjectID="2892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-19052" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3480.000000 -465.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19052" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2892"/>
     <cge:Term_Ref ObjectID="4148"/>
    <cge:TPSR_Ref TObjectID="2892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-19053" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3480.000000 -465.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19053" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2892"/>
     <cge:Term_Ref ObjectID="4148"/>
    <cge:TPSR_Ref TObjectID="2892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-19054" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3480.000000 -465.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2892"/>
     <cge:Term_Ref ObjectID="4148"/>
    <cge:TPSR_Ref TObjectID="2892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-19057" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3480.000000 -465.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2892"/>
     <cge:Term_Ref ObjectID="4148"/>
    <cge:TPSR_Ref TObjectID="2892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-19042" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -1058.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19042" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2891"/>
     <cge:Term_Ref ObjectID="4147"/>
    <cge:TPSR_Ref TObjectID="2891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-19043" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -1058.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2891"/>
     <cge:Term_Ref ObjectID="4147"/>
    <cge:TPSR_Ref TObjectID="2891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-19044" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -1058.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2891"/>
     <cge:Term_Ref ObjectID="4147"/>
    <cge:TPSR_Ref TObjectID="2891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-19045" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -1058.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2891"/>
     <cge:Term_Ref ObjectID="4147"/>
    <cge:TPSR_Ref TObjectID="2891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-19046" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -1058.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2891"/>
     <cge:Term_Ref ObjectID="4147"/>
    <cge:TPSR_Ref TObjectID="2891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-19049" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -1058.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19049" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2891"/>
     <cge:Term_Ref ObjectID="4147"/>
    <cge:TPSR_Ref TObjectID="2891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58539" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4240.000000 -1196.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11082"/>
     <cge:Term_Ref ObjectID="10009"/>
    <cge:TPSR_Ref TObjectID="11082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58540" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4240.000000 -1196.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11082"/>
     <cge:Term_Ref ObjectID="10009"/>
    <cge:TPSR_Ref TObjectID="11082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58535" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4240.000000 -1196.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11082"/>
     <cge:Term_Ref ObjectID="10009"/>
    <cge:TPSR_Ref TObjectID="11082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-19084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -524.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2953"/>
     <cge:Term_Ref ObjectID="4268"/>
    <cge:TPSR_Ref TObjectID="2953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-19085" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -524.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19085" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2953"/>
     <cge:Term_Ref ObjectID="4268"/>
    <cge:TPSR_Ref TObjectID="2953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-19080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -524.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2953"/>
     <cge:Term_Ref ObjectID="4268"/>
    <cge:TPSR_Ref TObjectID="2953"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3235" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3186" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="22" x="3925" y="-1209"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="22" x="3925" y="-1209"/></g>
  </g><g id="MotifButton_Layer">
   <g href="yr_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/></g>
   <g href="yr_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="22" x="3925" y="-1209"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_39da2e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4910.500000 -467.500000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37c1e00">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4910.500000 -908.500000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3244820">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4948.000000 -667.000000)" xlink:href="#voltageTransformer:shape49"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_085Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.000000 -86.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11903" ObjectName="EC-CX_YR.CX_YR_085Ld"/>
    <cge:TPSR_Ref TObjectID="11903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -86.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11907" ObjectName="EC-CX_YR.CX_YR_084Ld"/>
    <cge:TPSR_Ref TObjectID="11907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -86.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11904" ObjectName="EC-CX_YR.CX_YR_083Ld"/>
    <cge:TPSR_Ref TObjectID="11904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -86.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11905" ObjectName="EC-CX_YR.CX_YR_081Ld"/>
    <cge:TPSR_Ref TObjectID="11905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.000000 -86.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11906" ObjectName="EC-CX_YR.CX_YR_082Ld"/>
    <cge:TPSR_Ref TObjectID="11906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -86.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11912" ObjectName="EC-CX_YR.CX_YR_064Ld"/>
    <cge:TPSR_Ref TObjectID="11912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3509.000000 -85.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11909" ObjectName="EC-CX_YR.CX_YR_061Ld"/>
    <cge:TPSR_Ref TObjectID="11909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -85.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11910" ObjectName="EC-CX_YR.CX_YR_062Ld"/>
    <cge:TPSR_Ref TObjectID="11910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 -86.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11911" ObjectName="EC-CX_YR.CX_YR_063Ld"/>
    <cge:TPSR_Ref TObjectID="11911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_362Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5082.000000 -988.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3015" ObjectName="EC-CX_YR.CX_YR_362Ld"/>
    <cge:TPSR_Ref TObjectID="3015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_364Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5083.000000 -640.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3017" ObjectName="EC-CX_YR.CX_YR_364Ld"/>
    <cge:TPSR_Ref TObjectID="3017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YR.CX_YR_363Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5086.000000 -544.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3016" ObjectName="EC-CX_YR.CX_YR_363Ld"/>
    <cge:TPSR_Ref TObjectID="3016"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_37a2770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-335 4433,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2976@0" ObjectIDZND0="2893@0" Pin0InfoVect0LinkObjId="g_329f550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19312_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-335 4433,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329f550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-334 4559,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2979@0" ObjectIDZND0="2893@0" Pin0InfoVect0LinkObjId="g_37a2770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-334 4559,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-336 4673,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2982@0" ObjectIDZND0="2893@0" Pin0InfoVect0LinkObjId="g_37a2770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-336 4673,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-335 4913,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2988@0" ObjectIDZND0="2893@0" Pin0InfoVect0LinkObjId="g_37a2770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-335 4913,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5043,-335 5043,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2899@0" ObjectIDZND0="2893@0" Pin0InfoVect0LinkObjId="g_37a2770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5043,-335 5043,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5043,-286 5043,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2908@1" ObjectIDZND0="2899@1" Pin0InfoVect0LinkObjId="SW-19230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5043,-286 5043,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e2ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-147 5102,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2901@1" ObjectIDZND0="g_2d8c320@0" Pin0InfoVect0LinkObjId="g_2d8c320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-147 5102,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e2f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-335 5160,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2903@0" ObjectIDZND0="2893@0" Pin0InfoVect0LinkObjId="g_37a2770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-335 5160,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e3180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-146 5170,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="2904@x" ObjectIDND1="10472@x" ObjectIDZND0="2905@0" Pin0InfoVect0LinkObjId="SW-19236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19235_0" Pin1InfoVect1LinkObjId="CB-CX_YR.CX_YR_Cb3_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-146 5170,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e33e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-286 5160,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2907@1" ObjectIDZND0="2903@1" Pin0InfoVect0LinkObjId="SW-19234_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19238_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-286 5160,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e3640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-146 5223,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2905@1" ObjectIDZND0="g_2607a40@0" Pin0InfoVect0LinkObjId="g_2607a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-146 5223,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da7620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-709 3859,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2df43a0@0" ObjectIDZND0="3001@1" Pin0InfoVect0LinkObjId="SW-19339_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2df43a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-709 3859,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da7880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-709 3805,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="3001@0" ObjectIDZND0="3000@x" ObjectIDZND1="2997@x" Pin0InfoVect0LinkObjId="SW-19338_0" Pin0InfoVect1LinkObjId="SW-19334_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19339_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-709 3805,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da7ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-603 3727,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="3020@x" ObjectIDND1="g_23fad20@0" ObjectIDND2="3002@x" ObjectIDZND0="g_2e300b0@0" Pin0InfoVect0LinkObjId="g_2e300b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e72620_0" Pin1InfoVect1LinkObjId="g_23fad20_0" Pin1InfoVect2LinkObjId="SW-19340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-603 3727,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da7d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3750,-603 3750,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="3020@x" ObjectIDND1="g_2e300b0@0" ObjectIDND2="3002@x" ObjectIDZND0="g_23fad20@0" Pin0InfoVect0LinkObjId="g_23fad20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e72620_0" Pin1InfoVect1LinkObjId="g_2e300b0_0" Pin1InfoVect2LinkObjId="SW-19340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3750,-603 3750,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3da7fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-607 3750,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3020@x" ObjectIDZND0="g_23fad20@0" ObjectIDZND1="g_2e300b0@0" ObjectIDZND2="3002@x" Pin0InfoVect0LinkObjId="g_23fad20_0" Pin0InfoVect1LinkObjId="g_2e300b0_0" Pin0InfoVect2LinkObjId="SW-19340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e72620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-607 3750,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e72190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3750,-603 3727,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3020@x" ObjectIDND1="g_23fad20@0" ObjectIDZND0="g_2e300b0@0" ObjectIDZND1="3002@x" Pin0InfoVect0LinkObjId="g_2e300b0_0" Pin0InfoVect1LinkObjId="SW-19340_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e72620_0" Pin1InfoVect1LinkObjId="g_23fad20_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3750,-603 3727,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e723c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3699,-544 3699,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e1d630@0" ObjectIDZND0="3002@1" Pin0InfoVect0LinkObjId="SW-19340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1d630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3699,-544 3699,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e72620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3699,-593 3699,-603 3727,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="3002@0" ObjectIDZND0="3020@x" ObjectIDZND1="g_23fad20@0" ObjectIDZND2="g_2e300b0@0" Pin0InfoVect0LinkObjId="g_3778220_0" Pin0InfoVect1LinkObjId="g_23fad20_0" Pin0InfoVect2LinkObjId="g_2e300b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3699,-593 3699,-603 3727,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e72880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-690 4051,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="3019@x" ObjectIDND1="g_2dc04e0@0" ObjectIDND2="2910@x" ObjectIDZND0="g_2d74b60@0" Pin0InfoVect0LinkObjId="g_2d74b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36fb390_0" Pin1InfoVect1LinkObjId="g_2dc04e0_0" Pin1InfoVect2LinkObjId="SW-19242_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-690 4051,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e72ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-652 4051,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3019@x" ObjectIDZND0="g_2d74b60@0" ObjectIDZND1="g_2dc04e0@0" ObjectIDZND2="2910@x" Pin0InfoVect0LinkObjId="g_2d74b60_0" Pin0InfoVect1LinkObjId="g_2dc04e0_0" Pin0InfoVect2LinkObjId="SW-19242_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36fb390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-652 4051,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36faed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-538 4127,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3019@x" ObjectIDND1="2921@x" ObjectIDZND0="g_36caf20@0" Pin0InfoVect0LinkObjId="g_36caf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36fb390_0" Pin1InfoVect1LinkObjId="SW-19254_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-538 4127,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36fb130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-586 4142,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3019@2" ObjectIDZND0="g_36caf20@0" ObjectIDZND1="2921@x" Pin0InfoVect0LinkObjId="g_36caf20_0" Pin0InfoVect1LinkObjId="SW-19254_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36fb390_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-586 4142,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36fb390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-506 4142,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="2921@0" ObjectIDZND0="3019@x" ObjectIDZND1="g_36caf20@0" Pin0InfoVect0LinkObjId="g_328cac0_0" Pin0InfoVect1LinkObjId="g_36caf20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19254_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-506 4142,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36fb5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-339 3517,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2960@0" ObjectIDZND0="2892@0" Pin0InfoVect0LinkObjId="g_39613b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-339 3517,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36fb850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-289 3530,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2963@x" ObjectIDND1="2960@x" ObjectIDZND0="2961@0" Pin0InfoVect0LinkObjId="SW-19297_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19299_0" Pin1InfoVect1LinkObjId="SW-19296_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-289 3530,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3292ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3566,-289 3581,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2961@1" ObjectIDZND0="g_332d610@0" Pin0InfoVect0LinkObjId="g_332d610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19297_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3566,-289 3581,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3293150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-235 3517,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_350eee0@0" ObjectIDZND0="2963@0" Pin0InfoVect0LinkObjId="SW-19299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_350eee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-235 3517,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32933b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-171 3517,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2962@0" ObjectIDZND0="g_350eee0@1" Pin0InfoVect0LinkObjId="g_350eee0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-171 3517,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3293610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-288 3660,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2967@x" ObjectIDND1="2964@x" ObjectIDZND0="2965@0" Pin0InfoVect0LinkObjId="SW-19301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19303_0" Pin1InfoVect1LinkObjId="SW-19300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-288 3660,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3293870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3693,-288 3711,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2965@1" ObjectIDZND0="g_24e6480@0" Pin0InfoVect0LinkObjId="g_24e6480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3693,-288 3711,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3960ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-236 3647,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3755c00@0" ObjectIDZND0="2967@0" Pin0InfoVect0LinkObjId="SW-19303_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3755c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-236 3647,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3961150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-172 3647,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2966@0" ObjectIDZND0="g_3755c00@1" Pin0InfoVect0LinkObjId="g_3755c00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-172 3647,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39613b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-334 3647,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2964@0" ObjectIDZND0="2892@0" Pin0InfoVect0LinkObjId="g_36fb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-334 3647,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3961610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-337 3784,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2968@0" ObjectIDZND0="2892@0" Pin0InfoVect0LinkObjId="g_36fb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-337 3784,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3961870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-287 3784,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2971@x" ObjectIDND1="2969@x" ObjectIDZND0="2968@1" Pin0InfoVect0LinkObjId="SW-19304_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19307_0" Pin1InfoVect1LinkObjId="SW-19305_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-287 3784,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3962470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-289 3795,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2971@x" ObjectIDND1="2968@x" ObjectIDZND0="2969@0" Pin0InfoVect0LinkObjId="SW-19305_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19307_0" Pin1InfoVect1LinkObjId="SW-19304_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-289 3795,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39626d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3832,-289 3847,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2969@1" ObjectIDZND0="g_3da9210@0" Pin0InfoVect0LinkObjId="g_3da9210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19305_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3832,-289 3847,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3962930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-275 3784,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2971@1" ObjectIDZND0="2969@x" ObjectIDZND1="2968@x" Pin0InfoVect0LinkObjId="SW-19305_0" Pin0InfoVect1LinkObjId="SW-19304_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-275 3784,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3962b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-335 3910,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2972@0" ObjectIDZND0="2892@0" Pin0InfoVect0LinkObjId="g_36fb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-335 3910,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3962df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-288 3910,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2975@x" ObjectIDND1="2973@x" ObjectIDZND0="2972@1" Pin0InfoVect0LinkObjId="SW-19308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19311_0" Pin1InfoVect1LinkObjId="SW-19309_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-288 3910,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37a4cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-290 3919,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2975@x" ObjectIDND1="2972@x" ObjectIDZND0="2973@0" Pin0InfoVect0LinkObjId="SW-19309_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19311_0" Pin1InfoVect1LinkObjId="SW-19308_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-290 3919,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37a4f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-290 3969,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2973@1" ObjectIDZND0="g_241cac0@0" Pin0InfoVect0LinkObjId="g_241cac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19309_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-290 3969,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37a5190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-275 3910,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2975@1" ObjectIDZND0="2973@x" ObjectIDZND1="2972@x" Pin0InfoVect0LinkObjId="SW-19309_0" Pin0InfoVect1LinkObjId="SW-19308_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19311_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-275 3910,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37a53f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-236 3910,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_32a0400@0" ObjectIDZND0="2975@0" Pin0InfoVect0LinkObjId="SW-19311_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a0400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-236 3910,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37a5650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-172 3910,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2974@0" ObjectIDZND0="g_32a0400@1" Pin0InfoVect0LinkObjId="g_32a0400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-172 3910,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32adb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-337 4050,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3010@0" ObjectIDZND0="2892@0" Pin0InfoVect0LinkObjId="g_36fb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-337 4050,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32addc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-287 4050,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2501810@0" ObjectIDND1="3011@x" ObjectIDZND0="3010@1" Pin0InfoVect0LinkObjId="SW-19349_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2501810_0" Pin1InfoVect1LinkObjId="SW-19350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-287 4050,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ae020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-289 4063,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2501810@0" ObjectIDND1="3010@x" ObjectIDZND0="3011@0" Pin0InfoVect0LinkObjId="SW-19350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2501810_0" Pin1InfoVect1LinkObjId="SW-19349_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-289 4063,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ae280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-289 4113,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3011@1" ObjectIDZND0="g_2508120@0" Pin0InfoVect0LinkObjId="g_2508120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-289 4113,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ae4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-230 4050,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_341e660@0" ObjectIDZND0="g_2501810@0" Pin0InfoVect0LinkObjId="g_2501810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_341e660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-230 4050,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36e6410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-275 4050,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2501810@1" ObjectIDZND0="3011@x" ObjectIDZND1="3010@x" Pin0InfoVect0LinkObjId="SW-19350_0" Pin0InfoVect1LinkObjId="SW-19349_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2501810_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-275 4050,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36e6670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-338 4186,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2894@0" ObjectIDZND0="2892@0" Pin0InfoVect0LinkObjId="g_36fb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19225_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-338 4186,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36e68d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-256 4186,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="2898@0" ObjectIDZND0="g_2df4070@0" Pin0InfoVect0LinkObjId="g_2df4070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19229_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-256 4186,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36e6b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-886 3582,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2890@0" ObjectIDZND0="2923@1" Pin0InfoVect0LinkObjId="SW-19257_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c0c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-886 3582,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36e6d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-936 3582,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="2923@0" ObjectIDZND0="2922@x" ObjectIDZND1="2924@x" Pin0InfoVect0LinkObjId="SW-19256_0" Pin0InfoVect1LinkObjId="SW-19258_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19257_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-936 3582,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-946 3582,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="2923@x" ObjectIDND1="2924@x" ObjectIDZND0="2922@0" Pin0InfoVect0LinkObjId="SW-19256_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19257_0" Pin1InfoVect1LinkObjId="SW-19258_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-946 3582,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3635,-995 3648,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2926@1" ObjectIDZND0="g_3684580@0" Pin0InfoVect0LinkObjId="g_3684580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3635,-995 3648,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-948 3599,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2922@x" ObjectIDND1="2923@x" ObjectIDZND0="2924@0" Pin0InfoVect0LinkObjId="SW-19258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19256_0" Pin1InfoVect1LinkObjId="SW-19257_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-948 3599,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3635,-948 3648,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2924@1" ObjectIDZND0="g_36b8310@0" Pin0InfoVect0LinkObjId="g_36b8310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19258_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3635,-948 3648,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3669180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-1053 3815,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2933@1" ObjectIDZND0="g_3703590@0" Pin0InfoVect0LinkObjId="g_3703590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-1053 3815,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_39dbd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-996 3770,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2931@x" ObjectIDND1="2928@x" ObjectIDZND0="2932@0" Pin0InfoVect0LinkObjId="SW-19267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19266_0" Pin1InfoVect1LinkObjId="SW-19263_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-996 3770,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_39dbf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-996 3815,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2932@1" ObjectIDZND0="g_37a4a40@0" Pin0InfoVect0LinkObjId="g_37a4a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-996 3815,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_39dc1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-983 3749,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2928@1" ObjectIDZND0="2932@x" ObjectIDZND1="2931@x" Pin0InfoVect0LinkObjId="SW-19267_0" Pin0InfoVect1LinkObjId="SW-19266_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-983 3749,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_39dc420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-996 3749,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2932@x" ObjectIDND1="2928@x" ObjectIDZND0="2931@1" Pin0InfoVect0LinkObjId="SW-19266_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19267_0" Pin1InfoVect1LinkObjId="SW-19263_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-996 3749,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_39dc680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3800,-950 3815,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2930@1" ObjectIDZND0="g_364adf0@0" Pin0InfoVect0LinkObjId="g_364adf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19265_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3800,-950 3815,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37a0090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-949 3917,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="2936@x" ObjectIDND1="2935@x" ObjectIDZND0="2934@0" Pin0InfoVect0LinkObjId="SW-19270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19272_0" Pin1InfoVect1LinkObjId="SW-19271_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-949 3917,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37a02f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3970,-1053 3983,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2939@1" ObjectIDZND0="g_24418a0@0" Pin0InfoVect0LinkObjId="g_24418a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19275_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3970,-1053 3983,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37a0550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3970,-995 3983,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2938@1" ObjectIDZND0="g_2580fa0@0" Pin0InfoVect0LinkObjId="g_2580fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19274_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3970,-995 3983,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37a07b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-950 3935,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2934@x" ObjectIDND1="2935@x" ObjectIDZND0="2936@0" Pin0InfoVect0LinkObjId="SW-19272_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19270_0" Pin1InfoVect1LinkObjId="SW-19271_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-950 3935,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37a0a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3970,-950 3983,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2936@1" ObjectIDZND0="g_3525c30@0" Pin0InfoVect0LinkObjId="g_3525c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19272_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3970,-950 3983,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37739b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4388,-1085 4363,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_37a8060@0" ObjectIDZND0="g_36a3c70@0" ObjectIDZND1="2942@x" ObjectIDZND2="2940@x" Pin0InfoVect0LinkObjId="g_36a3c70_0" Pin0InfoVect1LinkObjId="SW-19278_0" Pin0InfoVect2LinkObjId="SW-19276_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a8060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4388,-1085 4363,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3773c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-887 4806,-887 4806,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b31b40@0" ObjectIDZND0="g_2e2abe0@0" ObjectIDZND1="2943@x" Pin0InfoVect0LinkObjId="g_2e2abe0_0" Pin0InfoVect1LinkObjId="SW-19279_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b31b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-887 4806,-887 4806,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3773e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4806,-923 4833,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b31b40@0" ObjectIDND1="2943@x" ObjectIDZND0="g_2e2abe0@1" Pin0InfoVect0LinkObjId="g_2e2abe0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b31b40_0" Pin1InfoVect1LinkObjId="SW-19279_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4806,-923 4833,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37740d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4864,-923 4888,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2e2abe0@0" ObjectIDZND0="g_37c1e00@0" Pin0InfoVect0LinkObjId="g_37c1e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e2abe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4864,-923 4888,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3774330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-446 4810,-446 4810,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_31baeb0@0" ObjectIDZND0="g_2d85a30@0" ObjectIDZND1="2944@x" Pin0InfoVect0LinkObjId="g_2d85a30_0" Pin0InfoVect1LinkObjId="SW-19280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31baeb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-446 4810,-446 4810,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37778a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-482 4857,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_39da2e0@0" ObjectIDZND0="g_2d85a30@1" Pin0InfoVect0LinkObjId="g_2d85a30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39da2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-482 4857,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3777b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-482 4810,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2d85a30@0" ObjectIDZND0="g_31baeb0@0" ObjectIDZND1="2944@x" Pin0InfoVect0LinkObjId="g_31baeb0_0" Pin0InfoVect1LinkObjId="SW-19280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d85a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-482 4810,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3777d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-248 3784,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="2971@0" ObjectIDZND0="g_2b56b10@0" Pin0InfoVect0LinkObjId="g_2b56b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-248 3784,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3777fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-186 3784,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b56b10@1" ObjectIDZND0="2970@0" Pin0InfoVect0LinkObjId="SW-19306_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b56b10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-186 3784,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3778220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-649 3805,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="3000@1" ObjectIDZND0="3020@1" Pin0InfoVect0LinkObjId="g_2e72620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19338_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-649 3805,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38eaed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-827 4195,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36c8c60@0" ObjectIDZND0="2912@1" Pin0InfoVect0LinkObjId="SW-19245_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36c8c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-827 4195,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38eb130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-827 4143,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="2912@0" ObjectIDZND0="2911@x" ObjectIDZND1="2909@x" Pin0InfoVect0LinkObjId="SW-19244_0" Pin0InfoVect1LinkObjId="SW-19240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19245_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-827 4143,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38eb390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-779 3858,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_370bb70@0" ObjectIDZND0="2999@1" Pin0InfoVect0LinkObjId="SW-19337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_370bb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-779 3858,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38eb5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-779 3805,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="2999@0" ObjectIDZND0="2998@x" ObjectIDZND1="2997@x" Pin0InfoVect0LinkObjId="SW-19336_0" Pin0InfoVect1LinkObjId="SW-19334_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-779 3805,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38eb850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-696 4195,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_248ebb0@0" ObjectIDZND0="2918@1" Pin0InfoVect0LinkObjId="SW-19251_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_248ebb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-696 4195,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36e00a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-769 4195,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_31beb40@0" ObjectIDZND0="2914@1" Pin0InfoVect0LinkObjId="SW-19247_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31beb40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-769 4195,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36e0300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-459 4142,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2919@1" ObjectIDZND0="2921@1" Pin0InfoVect0LinkObjId="SW-19254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19252_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-459 4142,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36e0560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4501,-348 4501,-371 4142,-371 4142,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2893@0" ObjectIDZND0="2920@1" Pin0InfoVect0LinkObjId="SW-19253_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a2770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4501,-348 4501,-371 4142,-371 4142,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36e07b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-779 3805,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2999@x" ObjectIDND1="2997@x" ObjectIDZND0="2998@1" Pin0InfoVect0LinkObjId="SW-19336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19337_0" Pin1InfoVect1LinkObjId="SW-19334_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-779 3805,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36e09f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-779 3805,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="2998@x" ObjectIDND1="2999@x" ObjectIDZND0="2997@1" Pin0InfoVect0LinkObjId="SW-19334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19336_0" Pin1InfoVect1LinkObjId="SW-19337_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-779 3805,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24a8480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-683 3805,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="3000@0" ObjectIDZND0="3001@x" ObjectIDZND1="2997@x" Pin0InfoVect0LinkObjId="SW-19339_0" Pin0InfoVect1LinkObjId="SW-19334_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-683 3805,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24a86e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-735 3805,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2997@0" ObjectIDZND0="3000@x" ObjectIDZND1="3001@x" Pin0InfoVect0LinkObjId="SW-19338_0" Pin0InfoVect1LinkObjId="SW-19339_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-735 3805,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24a8940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-824 4143,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2912@x" ObjectIDND1="2909@x" ObjectIDZND0="2911@1" Pin0InfoVect0LinkObjId="SW-19244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19245_0" Pin1InfoVect1LinkObjId="SW-19240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-824 4143,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24a8ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-769 4143,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="2914@0" ObjectIDZND0="2909@x" ObjectIDZND1="2913@x" Pin0InfoVect0LinkObjId="SW-19240_0" Pin0InfoVect1LinkObjId="SW-19246_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19247_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-769 4143,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24a8e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-824 4143,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="2911@x" ObjectIDND1="2912@x" ObjectIDZND0="2909@1" Pin0InfoVect0LinkObjId="SW-19240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19244_0" Pin1InfoVect1LinkObjId="SW-19245_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-824 4143,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_39d4450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-781 4143,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2909@0" ObjectIDZND0="2914@x" ObjectIDZND1="2913@x" Pin0InfoVect0LinkObjId="SW-19247_0" Pin0InfoVect1LinkObjId="SW-19246_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-781 4143,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_39d46b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-766 4143,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2909@x" ObjectIDND1="2914@x" ObjectIDZND0="2913@0" Pin0InfoVect0LinkObjId="SW-19246_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19240_0" Pin1InfoVect1LinkObjId="SW-19247_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-766 4143,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d4910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-348 3623,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2892@0" ObjectIDZND0="2995@1" Pin0InfoVect0LinkObjId="SW-19331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36fb5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-348 3623,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d4b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-420 3638,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2995@x" ObjectIDND1="g_3331ff0@0" ObjectIDND2="g_32b3a10@0" ObjectIDZND0="2996@0" Pin0InfoVect0LinkObjId="SW-19332_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19331_0" Pin1InfoVect1LinkObjId="g_3331ff0_0" Pin1InfoVect2LinkObjId="g_32b3a10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-420 3638,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d4dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3670,-420 3686,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2996@1" ObjectIDZND0="g_31bf180@0" Pin0InfoVect0LinkObjId="g_31bf180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3670,-420 3686,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3788340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-402 3623,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="2995@0" ObjectIDZND0="g_3331ff0@0" ObjectIDZND1="g_32b3a10@0" ObjectIDZND2="2996@x" Pin0InfoVect0LinkObjId="g_3331ff0_0" Pin0InfoVect1LinkObjId="g_32b3a10_0" Pin0InfoVect2LinkObjId="SW-19332_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-402 3623,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37885a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-420 3608,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="2995@x" ObjectIDND1="g_3331ff0@0" ObjectIDND2="2996@x" ObjectIDZND0="g_32b3a10@0" Pin0InfoVect0LinkObjId="g_32b3a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19331_0" Pin1InfoVect1LinkObjId="g_3331ff0_0" Pin1InfoVect2LinkObjId="SW-19332_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-420 3608,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3788800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-420 3623,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="2995@x" ObjectIDND1="g_32b3a10@0" ObjectIDND2="2996@x" ObjectIDZND0="g_3331ff0@0" Pin0InfoVect0LinkObjId="g_3331ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19331_0" Pin1InfoVect1LinkObjId="g_32b3a10_0" Pin1InfoVect2LinkObjId="SW-19332_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-420 3623,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3788a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-469 3623,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3331ff0@1" ObjectIDZND0="g_2c7e8f0@0" Pin0InfoVect0LinkObjId="g_2c7e8f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3331ff0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-469 3623,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3788cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-348 4601,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2893@0" ObjectIDZND0="2993@1" Pin0InfoVect0LinkObjId="SW-19329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a2770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-348 4601,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37bf780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-418 4616,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2993@x" ObjectIDND1="g_34c10b0@0" ObjectIDND2="g_3394670@0" ObjectIDZND0="2994@0" Pin0InfoVect0LinkObjId="SW-19330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19329_0" Pin1InfoVect1LinkObjId="g_34c10b0_0" Pin1InfoVect2LinkObjId="g_3394670_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-418 4616,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37bf9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-418 4664,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2994@1" ObjectIDZND0="g_37891e0@0" Pin0InfoVect0LinkObjId="g_37891e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-418 4664,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37bfc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-402 4601,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="2993@0" ObjectIDZND0="g_34c10b0@0" ObjectIDZND1="g_3394670@0" ObjectIDZND2="2994@x" Pin0InfoVect0LinkObjId="g_34c10b0_0" Pin0InfoVect1LinkObjId="g_3394670_0" Pin0InfoVect2LinkObjId="SW-19330_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-402 4601,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37bfea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-418 4586,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="2993@x" ObjectIDND1="g_34c10b0@0" ObjectIDND2="2994@x" ObjectIDZND0="g_3394670@0" Pin0InfoVect0LinkObjId="g_3394670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19329_0" Pin1InfoVect1LinkObjId="g_34c10b0_0" Pin1InfoVect2LinkObjId="SW-19330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-418 4586,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37c0100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-418 4601,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="2993@x" ObjectIDND1="g_3394670@0" ObjectIDND2="2994@x" ObjectIDZND0="g_34c10b0@0" Pin0InfoVect0LinkObjId="g_34c10b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19329_0" Pin1InfoVect1LinkObjId="g_3394670_0" Pin1InfoVect2LinkObjId="SW-19330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-418 4601,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3da68a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-467 4601,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_34c10b0@1" ObjectIDZND0="g_37bc720@0" Pin0InfoVect0LinkObjId="g_37bc720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c10b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-467 4601,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3da6b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-649 4912,-628 4935,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_33974f0@0" ObjectIDND1="2955@x" ObjectIDND2="g_3245a30@0" ObjectIDZND0="g_3634160@0" Pin0InfoVect0LinkObjId="g_3634160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33974f0_0" Pin1InfoVect1LinkObjId="SW-19291_0" Pin1InfoVect2LinkObjId="g_3245a30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-649 4912,-628 4935,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3da6d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5056,-649 5032,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="3017@0" ObjectIDZND0="g_33974f0@1" Pin0InfoVect0LinkObjId="g_33974f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YR.CX_YR_364Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5056,-649 5032,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3da6fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-998 5055,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_2d66aa0@0" ObjectIDND1="2949@x" ObjectIDND2="g_3698020@0" ObjectIDZND0="3015@0" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_362Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d66aa0_0" Pin1InfoVect1LinkObjId="SW-19285_0" Pin1InfoVect2LinkObjId="g_3698020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-998 5055,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3da7220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-977 4902,-977 4902,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3698020@0" ObjectIDZND0="3015@x" ObjectIDZND1="g_2d66aa0@0" ObjectIDZND2="2949@x" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_362Ld_0" Pin0InfoVect1LinkObjId="g_2d66aa0_0" Pin0InfoVect2LinkObjId="SW-19285_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3698020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-977 4902,-977 4902,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39cbc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-997 4902,-1021 4919,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3015@x" ObjectIDND1="2949@x" ObjectIDND2="g_3698020@0" ObjectIDZND0="g_2d66aa0@0" Pin0InfoVect0LinkObjId="g_2d66aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_YR.CX_YR_362Ld_0" Pin1InfoVect1LinkObjId="SW-19285_0" Pin1InfoVect2LinkObjId="g_3698020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-997 4902,-1021 4919,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39cbea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4950,-1020 4967,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d66aa0@1" ObjectIDZND0="g_251e4b0@0" Pin0InfoVect0LinkObjId="g_251e4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d66aa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4950,-1020 4967,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39cc100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-530 4964,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_36e1c00@1" ObjectIDZND0="g_3687120@0" Pin0InfoVect0LinkObjId="g_3687120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36e1c00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-530 4964,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39cc360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-177 4050,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_341e660@1" ObjectIDZND0="11914@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_341e660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-177 4050,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39cc5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-213 4259,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38a4d50@0" ObjectIDZND0="3013@1" Pin0InfoVect0LinkObjId="SW-19379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a4d50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-213 4259,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33af490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-267 4259,-281 4287,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3013@0" ObjectIDZND0="2992@x" ObjectIDZND1="3012@x" Pin0InfoVect0LinkObjId="SW-19328_0" Pin0InfoVect1LinkObjId="SW-19378_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-267 4259,-281 4287,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33af6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4327,-267 4287,-267 4287,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2992@1" ObjectIDZND0="3013@x" ObjectIDZND1="3012@x" Pin0InfoVect0LinkObjId="SW-19379_0" Pin0InfoVect1LinkObjId="SW-19378_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19328_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4327,-267 4287,-267 4287,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33af950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4287,-281 4287,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2992@x" ObjectIDND1="3013@x" ObjectIDZND0="3012@1" Pin0InfoVect0LinkObjId="SW-19378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19328_0" Pin1InfoVect1LinkObjId="SW-19379_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4287,-281 4287,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33afbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4287,-336 4287,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3012@0" ObjectIDZND0="2892@0" Pin0InfoVect0LinkObjId="g_36fb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4287,-336 4287,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33afe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-348 4405,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2893@0" ObjectIDZND0="2991@0" Pin0InfoVect0LinkObjId="SW-19327_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a2770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-348 4405,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379f2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-299 4405,-267 4354,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2991@1" ObjectIDZND0="2992@0" Pin0InfoVect0LinkObjId="SW-19328_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19327_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-299 4405,-267 4354,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4559,-283 4559,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2981@1" ObjectIDZND0="2979@1" Pin0InfoVect0LinkObjId="SW-19315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19317_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4559,-283 4559,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379f7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-283 4433,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2977@1" ObjectIDZND0="2976@1" Pin0InfoVect0LinkObjId="SW-19312_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19313_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-283 4433,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379fa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-256 4432,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="2977@0" ObjectIDZND0="g_38ebbd0@0" Pin0InfoVect0LinkObjId="g_38ebbd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19313_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-256 4432,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-256 4558,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="2981@0" ObjectIDZND0="g_37401a0@0" Pin0InfoVect0LinkObjId="g_37401a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19317_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-256 4558,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a8d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-302 4673,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2982@1" ObjectIDZND0="2984@1" Pin0InfoVect0LinkObjId="SW-19320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-302 4673,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a8f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-302 4794,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2985@1" ObjectIDZND0="2987@1" Pin0InfoVect0LinkObjId="SW-19323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19321_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-302 4794,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a91c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-302 4913,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2988@1" ObjectIDZND0="2990@1" Pin0InfoVect0LinkObjId="SW-19326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19324_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-302 4913,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a9420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-257 4912,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="2990@0" ObjectIDZND0="g_32174d0@0" Pin0InfoVect0LinkObjId="g_32174d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-257 4912,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a9680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-257 4673,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="2984@0" ObjectIDZND0="g_2e1ef30@0" Pin0InfoVect0LinkObjId="g_2e1ef30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-257 4673,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3802e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-257 4794,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="2987@0" ObjectIDZND0="g_35afde0@0" Pin0InfoVect0LinkObjId="g_35afde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-257 4794,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38030e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-13 5042,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="2902@0" Pin0InfoVect0LinkObjId="SW-19233_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-13 5042,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3803340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5043,42 5042,61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2902@1" ObjectIDZND0="g_39e2a60@0" Pin0InfoVect0LinkObjId="g_39e2a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19233_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5043,42 5042,61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38035a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-16 5160,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="2906@0" Pin0InfoVect0LinkObjId="SW-19237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-16 5160,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3803800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,46 5160,61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2906@1" ObjectIDZND0="g_32acef0@0" Pin0InfoVect0LinkObjId="g_32acef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19237_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,46 5160,61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3714c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-113 3910,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11912@0" ObjectIDZND0="g_3209a60@0" ObjectIDZND1="2974@x" Pin0InfoVect0LinkObjId="g_3209a60_0" Pin0InfoVect1LinkObjId="SW-19310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YR.CX_YR_064Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-113 3910,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3714e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3923,-124 3910,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3209a60@0" ObjectIDZND0="11912@x" ObjectIDZND1="2974@x" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_064Ld_0" Pin0InfoVect1LinkObjId="SW-19310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3209a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3923,-124 3910,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37150c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-123 3910,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_3209a60@0" ObjectIDND1="11912@x" ObjectIDZND0="2974@1" Pin0InfoVect0LinkObjId="SW-19310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3209a60_0" Pin1InfoVect1LinkObjId="EC-CX_YR.CX_YR_064Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-123 3910,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3715320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-113 3784,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11911@0" ObjectIDZND0="g_2c800e0@0" ObjectIDZND1="2970@x" Pin0InfoVect0LinkObjId="g_2c800e0_0" Pin0InfoVect1LinkObjId="SW-19306_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YR.CX_YR_063Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-113 3784,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3715580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-124 3784,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2c800e0@0" ObjectIDZND0="11911@x" ObjectIDZND1="2970@x" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_063Ld_0" Pin0InfoVect1LinkObjId="SW-19306_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c800e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-124 3784,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3290c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-123 3784,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2c800e0@0" ObjectIDND1="11911@x" ObjectIDZND0="2970@1" Pin0InfoVect0LinkObjId="SW-19306_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c800e0_0" Pin1InfoVect1LinkObjId="EC-CX_YR.CX_YR_063Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-123 3784,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3290e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-112 3647,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11910@0" ObjectIDZND0="g_32b0800@0" ObjectIDZND1="2966@x" Pin0InfoVect0LinkObjId="g_32b0800_0" Pin0InfoVect1LinkObjId="SW-19302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YR.CX_YR_062Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-112 3647,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32910c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3660,-124 3647,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_32b0800@0" ObjectIDZND0="11910@x" ObjectIDZND1="2966@x" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_062Ld_0" Pin0InfoVect1LinkObjId="SW-19302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b0800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3660,-124 3647,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3291320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-123 3647,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_32b0800@0" ObjectIDND1="11910@x" ObjectIDZND0="2966@1" Pin0InfoVect0LinkObjId="SW-19302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32b0800_0" Pin1InfoVect1LinkObjId="EC-CX_YR.CX_YR_062Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-123 3647,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3291580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-112 3518,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11909@0" ObjectIDZND0="g_2dbc870@0" ObjectIDZND1="2962@x" Pin0InfoVect0LinkObjId="g_2dbc870_0" Pin0InfoVect1LinkObjId="SW-19298_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YR.CX_YR_061Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-112 3518,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f8700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3531,-123 3518,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2dbc870@0" ObjectIDZND0="11909@x" ObjectIDZND1="2962@x" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_061Ld_0" Pin0InfoVect1LinkObjId="SW-19298_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dbc870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3531,-123 3518,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f8960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-122 3518,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2dbc870@0" ObjectIDND1="11909@x" ObjectIDZND0="2962@1" Pin0InfoVect0LinkObjId="SW-19298_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2dbc870_0" Pin1InfoVect1LinkObjId="EC-CX_YR.CX_YR_061Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-122 3518,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23f8bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4387,-1140 4363,-1140 4363,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_36a3c70@0" ObjectIDZND0="g_37a8060@0" ObjectIDZND1="2942@x" ObjectIDZND2="2940@x" Pin0InfoVect0LinkObjId="g_37a8060_0" Pin0InfoVect1LinkObjId="SW-19278_0" Pin0InfoVect2LinkObjId="SW-19276_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36a3c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4387,-1140 4363,-1140 4363,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f8e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-564 3994,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2d95610@0" ObjectIDZND0="g_31bc000@0" ObjectIDZND1="g_2439ea0@0" ObjectIDZND2="9801@x" Pin0InfoVect0LinkObjId="g_31bc000_0" Pin0InfoVect1LinkObjId="g_2439ea0_0" Pin0InfoVect2LinkObjId="SW-19243_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d95610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-564 3994,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f9080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-564 4042,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_31bc000@0" ObjectIDZND0="g_2d95610@0" ObjectIDZND1="9800@x" ObjectIDZND2="g_2439ea0@0" Pin0InfoVect0LinkObjId="g_2d95610_0" Pin0InfoVect1LinkObjId="SW-19335_0" Pin0InfoVect2LinkObjId="g_2439ea0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31bc000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-564 4042,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37c0540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-551 4042,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2d95610@0" ObjectIDND1="9800@x" ObjectIDZND0="g_31bc000@0" ObjectIDZND1="g_2439ea0@0" ObjectIDZND2="9801@x" Pin0InfoVect0LinkObjId="g_31bc000_0" Pin0InfoVect1LinkObjId="g_2439ea0_0" Pin0InfoVect2LinkObjId="SW-19243_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d95610_0" Pin1InfoVect1LinkObjId="SW-19335_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-551 4042,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37c07a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-156 5160,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="2904@1" ObjectIDZND0="2905@x" ObjectIDZND1="10472@x" Pin0InfoVect0LinkObjId="SW-19236_0" Pin0InfoVect1LinkObjId="CB-CX_YR.CX_YR_Cb3_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-156 5160,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37c0a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-144 5160,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="2904@x" ObjectIDND1="2905@x" ObjectIDZND0="10472@0" Pin0InfoVect0LinkObjId="CB-CX_YR.CX_YR_Cb3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19235_0" Pin1InfoVect1LinkObjId="SW-19236_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-144 5160,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37c0c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-904 3749,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2929@1" ObjectIDZND0="2890@0" Pin0InfoVect0LinkObjId="g_37c0ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-904 3749,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37c0ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-834 3805,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2998@0" ObjectIDZND0="2890@0" Pin0InfoVect0LinkObjId="g_37c0c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-834 3805,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37c1120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1104 4252,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="18019@1" ObjectIDND1="g_32a58a0@0" ObjectIDND2="11087@x" ObjectIDZND0="g_368c580@0" Pin0InfoVect0LinkObjId="g_368c580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_393f0f0_1" Pin1InfoVect1LinkObjId="g_32a58a0_0" Pin1InfoVect2LinkObjId="SW-58515_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1104 4252,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3daf530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4284,-992 4298,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11086@1" ObjectIDZND0="g_256fcc0@0" Pin0InfoVect0LinkObjId="g_256fcc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4284,-992 4298,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3daf760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4284,-946 4298,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11084@1" ObjectIDZND0="g_36f07e0@0" Pin0InfoVect0LinkObjId="g_36f07e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4284,-946 4298,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3daf9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-194 4186,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2df4070@1" ObjectIDZND0="2896@0" Pin0InfoVect0LinkObjId="SW-19227_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2df4070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-194 4186,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dafc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-259 5042,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="2908@0" ObjectIDZND0="g_248c250@0" Pin0InfoVect0LinkObjId="g_248c250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19239_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-259 5042,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dafe80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-259 5160,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="2907@0" ObjectIDZND0="g_36e83f0@0" Pin0InfoVect0LinkObjId="g_36e83f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19238_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-259 5160,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3db00e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-194 5160,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_36e83f0@1" ObjectIDZND0="2904@0" Pin0InfoVect0LinkObjId="SW-19235_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36e83f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-194 5160,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_393ea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4950,-579 4970,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_30cfe90@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30cfe90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4950,-579 4970,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_393ec30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4906,-839 5004,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="powerLine" ObjectIDND0="2946@x" ObjectIDND1="g_36be100@0" ObjectIDZND0="37837@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19282_0" Pin1InfoVect1LinkObjId="g_36be100_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4906,-839 5004,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_393ee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4906,-839 4906,-819 4929,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="2946@x" ObjectIDND1="37837@1" ObjectIDZND0="g_36be100@0" Pin0InfoVect0LinkObjId="g_36be100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19282_0" Pin1InfoVect1LinkObjId="g_393ec30_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4906,-839 4906,-819 4929,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_393f0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1139 4228,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_368c580@0" ObjectIDND1="11087@x" ObjectIDND2="2925@x" ObjectIDZND0="18019@1" Pin0InfoVect0LinkObjId="g_393f350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_368c580_0" Pin1InfoVect1LinkObjId="SW-58515_0" Pin1InfoVect2LinkObjId="SW-58516_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1139 4228,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_393f350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1104 4228,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="g_368c580@0" ObjectIDND1="11087@x" ObjectIDND2="2925@x" ObjectIDZND0="18019@1" ObjectIDZND1="g_32a58a0@0" Pin0InfoVect0LinkObjId="g_393f0f0_1" Pin0InfoVect1LinkObjId="g_32a58a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_368c580_0" Pin1InfoVect1LinkObjId="SW-58515_0" Pin1InfoVect2LinkObjId="SW-58516_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1104 4228,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_393f5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1140 4252,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_368c580@0" ObjectIDND1="11087@x" ObjectIDND2="2925@x" ObjectIDZND0="g_32a58a0@0" Pin0InfoVect0LinkObjId="g_32a58a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_368c580_0" Pin1InfoVect1LinkObjId="SW-58515_0" Pin1InfoVect2LinkObjId="SW-58516_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1140 4252,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e897a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-125 4198,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="2896@x" ObjectIDND1="10513@x" ObjectIDZND0="2897@0" Pin0InfoVect0LinkObjId="SW-19228_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19227_0" Pin1InfoVect1LinkObjId="CB-CX_YR.CX_YR_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-125 4198,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e899d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-147 4186,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="2896@1" ObjectIDZND0="2897@x" ObjectIDZND1="10513@x" Pin0InfoVect0LinkObjId="SW-19228_0" Pin0InfoVect1LinkObjId="CB-CX_YR.CX_YR_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19227_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-147 4186,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e89c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-123 4186,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="2896@x" ObjectIDND1="2897@x" ObjectIDZND0="10513@0" Pin0InfoVect0LinkObjId="CB-CX_YR.CX_YR_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19227_0" Pin1InfoVect1LinkObjId="SW-19228_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-123 4186,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e89e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4233,-125 4252,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2897@1" ObjectIDZND0="g_2419500@0" Pin0InfoVect0LinkObjId="g_2419500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19228_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4233,-125 4252,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e8a0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3606,-1105 3582,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_38ec390@0" ObjectIDZND0="g_3692ee0@0" ObjectIDZND1="11714@1" ObjectIDZND2="11085@x" Pin0InfoVect0LinkObjId="g_3692ee0_0" Pin0InfoVect1LinkObjId="g_37b2e70_1" Pin0InfoVect2LinkObjId="SW-19259_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38ec390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3606,-1105 3582,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e8a350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3606,-1140 3582,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3692ee0@0" ObjectIDZND0="g_38ec390@0" ObjectIDZND1="11085@x" ObjectIDZND2="2927@x" Pin0InfoVect0LinkObjId="g_38ec390_0" Pin0InfoVect1LinkObjId="SW-19259_0" Pin0InfoVect2LinkObjId="SW-19261_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3692ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3606,-1140 3582,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37b2c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-1105 3582,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_38ec390@0" ObjectIDND1="11085@x" ObjectIDND2="2927@x" ObjectIDZND0="g_3692ee0@0" ObjectIDZND1="11714@1" Pin0InfoVect0LinkObjId="g_3692ee0_0" Pin0InfoVect1LinkObjId="g_37b2e70_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38ec390_0" Pin1InfoVect1LinkObjId="SW-19259_0" Pin1InfoVect2LinkObjId="SW-19261_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-1105 3582,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37b2e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-1140 3582,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_38ec390@0" ObjectIDND1="11085@x" ObjectIDND2="2927@x" ObjectIDZND0="11714@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38ec390_0" Pin1InfoVect1LinkObjId="SW-19259_0" Pin1InfoVect2LinkObjId="SW-19261_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-1140 3582,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37b30d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3773,-1105 3749,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_3672f10@0" ObjectIDZND0="g_2c2e050@0" ObjectIDZND1="31883@1" ObjectIDZND2="2931@x" Pin0InfoVect0LinkObjId="g_2c2e050_0" Pin0InfoVect1LinkObjId="g_3cf7eb0_1" Pin0InfoVect2LinkObjId="SW-19266_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3672f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3773,-1105 3749,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37b3330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-1053 3749,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="2931@x" ObjectIDND1="2933@x" ObjectIDZND0="g_3672f10@0" ObjectIDZND1="g_2c2e050@0" ObjectIDZND2="31883@1" Pin0InfoVect0LinkObjId="g_3672f10_0" Pin0InfoVect1LinkObjId="g_2c2e050_0" Pin0InfoVect2LinkObjId="g_3cf7eb0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19266_0" Pin1InfoVect1LinkObjId="SW-19268_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-1053 3749,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37b3590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3773,-1140 3749,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2c2e050@0" ObjectIDZND0="g_3672f10@0" ObjectIDZND1="2931@x" ObjectIDZND2="2933@x" Pin0InfoVect0LinkObjId="g_3672f10_0" Pin0InfoVect1LinkObjId="SW-19266_0" Pin0InfoVect2LinkObjId="SW-19268_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c2e050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3773,-1140 3749,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37b37f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-1105 3749,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_3672f10@0" ObjectIDND1="2931@x" ObjectIDND2="2933@x" ObjectIDZND0="g_2c2e050@0" ObjectIDZND1="31883@1" Pin0InfoVect0LinkObjId="g_2c2e050_0" Pin0InfoVect1LinkObjId="g_3cf7eb0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3672f10_0" Pin1InfoVect1LinkObjId="SW-19266_0" Pin1InfoVect2LinkObjId="SW-19268_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-1105 3749,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cf7eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-1140 3749,-1171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3672f10@0" ObjectIDND1="2931@x" ObjectIDND2="2933@x" ObjectIDZND0="31883@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3672f10_0" Pin1InfoVect1LinkObjId="SW-19266_0" Pin1InfoVect2LinkObjId="SW-19268_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-1140 3749,-1171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cf8110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,-1140 3917,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_36f6920@0" ObjectIDZND0="g_23f7910@0" ObjectIDZND1="2939@x" ObjectIDZND2="2937@x" Pin0InfoVect0LinkObjId="g_23f7910_0" Pin0InfoVect1LinkObjId="SW-19275_0" Pin0InfoVect2LinkObjId="SW-19273_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36f6920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3941,-1140 3917,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cf8370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-1140 3917,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_36f6920@0" ObjectIDND1="g_23f7910@0" ObjectIDND2="2939@x" ObjectIDZND0="31884@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36f6920_0" Pin1InfoVect1LinkObjId="g_23f7910_0" Pin1InfoVect2LinkObjId="SW-19275_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-1140 3917,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cf85d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,-1105 3917,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_23f7910@0" ObjectIDZND0="g_36f6920@0" ObjectIDZND1="31884@1" ObjectIDZND2="2939@x" Pin0InfoVect0LinkObjId="g_36f6920_0" Pin0InfoVect1LinkObjId="g_3cf8370_1" Pin0InfoVect2LinkObjId="SW-19275_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23f7910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3941,-1105 3917,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cf8830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-1105 3917,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_23f7910@0" ObjectIDND1="2939@x" ObjectIDND2="2937@x" ObjectIDZND0="g_36f6920@0" ObjectIDZND1="31884@1" Pin0InfoVect0LinkObjId="g_36f6920_0" Pin0InfoVect1LinkObjId="g_3cf8370_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23f7910_0" Pin1InfoVect1LinkObjId="SW-19275_0" Pin1InfoVect2LinkObjId="SW-19273_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-1105 3917,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cf8a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-1053 3917,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="2939@0" ObjectIDZND0="g_36f6920@0" ObjectIDZND1="31884@1" ObjectIDZND2="g_23f7910@0" Pin0InfoVect0LinkObjId="g_36f6920_0" Pin0InfoVect1LinkObjId="g_3cf8370_1" Pin0InfoVect2LinkObjId="g_23f7910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19275_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-1053 3917,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e75b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-335 4794,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2985@0" ObjectIDZND0="2893@0" Pin0InfoVect0LinkObjId="g_37a2770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-335 4794,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e75da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-1005 3582,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="11085@1" ObjectIDZND0="2922@x" ObjectIDZND1="2926@x" Pin0InfoVect0LinkObjId="SW-19256_0" Pin0InfoVect1LinkObjId="SW-19260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19259_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-1005 3582,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e76000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-983 3582,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2922@1" ObjectIDZND0="11085@x" ObjectIDZND1="2926@x" Pin0InfoVect0LinkObjId="SW-19259_0" Pin0InfoVect1LinkObjId="SW-19260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19256_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-983 3582,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e76260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-995 3599,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2922@x" ObjectIDND1="11085@x" ObjectIDZND0="2926@0" Pin0InfoVect0LinkObjId="SW-19260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19256_0" Pin1InfoVect1LinkObjId="SW-19259_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-995 3599,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e764c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-950 3749,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="2930@0" ObjectIDZND0="2929@x" ObjectIDZND1="2928@x" Pin0InfoVect0LinkObjId="SW-19264_0" Pin0InfoVect1LinkObjId="SW-19263_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19265_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-950 3749,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e76720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-939 3749,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="2929@0" ObjectIDZND0="2930@x" ObjectIDZND1="2928@x" Pin0InfoVect0LinkObjId="SW-19265_0" Pin0InfoVect1LinkObjId="SW-19263_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-939 3749,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_328c3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-948 3749,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="2929@x" ObjectIDND1="2930@x" ObjectIDZND0="2928@0" Pin0InfoVect0LinkObjId="SW-19263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19264_0" Pin1InfoVect1LinkObjId="SW-19265_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-948 3749,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_328c600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-696 4143,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="2918@0" ObjectIDZND0="2913@x" ObjectIDZND1="3019@x" Pin0InfoVect0LinkObjId="SW-19246_0" Pin0InfoVect1LinkObjId="g_36fb390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19251_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-696 4143,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_328c860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-714 4143,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="2913@1" ObjectIDZND0="2918@x" ObjectIDZND1="3019@x" Pin0InfoVect0LinkObjId="SW-19251_0" Pin0InfoVect1LinkObjId="g_36fb390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19246_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-714 4143,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_328cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-694 4143,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="2913@x" ObjectIDND1="2918@x" ObjectIDZND0="3019@1" Pin0InfoVect0LinkObjId="g_36fb390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19246_0" Pin1InfoVect1LinkObjId="SW-19251_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-694 4143,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_328cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-275 3647,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2967@1" ObjectIDZND0="2964@x" ObjectIDZND1="2965@x" Pin0InfoVect0LinkObjId="SW-19300_0" Pin0InfoVect1LinkObjId="SW-19301_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19303_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-275 3647,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_328cf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3647,-288 3647,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2967@x" ObjectIDND1="2965@x" ObjectIDZND0="2964@1" Pin0InfoVect0LinkObjId="SW-19300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19303_0" Pin1InfoVect1LinkObjId="SW-19301_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3647,-288 3647,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3807b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-274 3517,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2963@1" ObjectIDZND0="2960@x" ObjectIDZND1="2961@x" Pin0InfoVect0LinkObjId="SW-19296_0" Pin0InfoVect1LinkObjId="SW-19297_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-274 3517,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3807d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-289 3517,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2963@x" ObjectIDND1="2961@x" ObjectIDZND0="2960@1" Pin0InfoVect0LinkObjId="SW-19296_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19299_0" Pin1InfoVect1LinkObjId="SW-19297_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-289 3517,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3807fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4571,-123 4558,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_24a5210@0" ObjectIDZND0="11906@x" ObjectIDZND1="2980@x" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_082Ld_0" Pin0InfoVect1LinkObjId="SW-19316_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24a5210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4571,-123 4558,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3808240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-123 4558,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_24a5210@0" ObjectIDND1="2980@x" ObjectIDZND0="11906@0" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24a5210_0" Pin1InfoVect1LinkObjId="SW-19316_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-123 4558,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38084a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-187 4558,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_37401a0@1" ObjectIDZND0="2980@0" Pin0InfoVect0LinkObjId="SW-19316_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37401a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-187 4558,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3808700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4558,-137 4558,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="2980@1" ObjectIDZND0="11906@x" ObjectIDZND1="g_24a5210@0" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_082Ld_0" Pin0InfoVect1LinkObjId="g_24a5210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19316_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4558,-137 4558,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3785230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-187 4432,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_38ebbd0@1" ObjectIDZND0="2978@0" Pin0InfoVect0LinkObjId="SW-19314_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38ebbd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-187 4432,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3785490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-135 4432,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="2978@1" ObjectIDZND0="g_39e2290@0" ObjectIDZND1="11905@x" Pin0InfoVect0LinkObjId="g_39e2290_0" Pin0InfoVect1LinkObjId="EC-CX_YR.CX_YR_081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-135 4432,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37856f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-123 4432,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_39e2290@0" ObjectIDZND0="2978@x" ObjectIDZND1="11905@x" Pin0InfoVect0LinkObjId="SW-19314_0" Pin0InfoVect1LinkObjId="EC-CX_YR.CX_YR_081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39e2290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-123 4432,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3785950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-123 4432,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_39e2290@0" ObjectIDND1="2978@x" ObjectIDZND0="11905@0" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_081Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_39e2290_0" Pin1InfoVect1LinkObjId="SW-19314_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-123 4432,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3785bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-189 4673,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2e1ef30@1" ObjectIDZND0="2983@0" Pin0InfoVect0LinkObjId="SW-19319_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1ef30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-189 4673,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3785e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-138 4673,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="2983@1" ObjectIDZND0="g_2e25510@0" ObjectIDZND1="11904@x" Pin0InfoVect0LinkObjId="g_2e25510_0" Pin0InfoVect1LinkObjId="EC-CX_YR.CX_YR_083Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19319_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-138 4673,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e0200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4686,-123 4673,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e25510@0" ObjectIDZND0="2983@x" ObjectIDZND1="11904@x" Pin0InfoVect0LinkObjId="SW-19319_0" Pin0InfoVect1LinkObjId="EC-CX_YR.CX_YR_083Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e25510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4686,-123 4673,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e0460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-123 4673,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2e25510@0" ObjectIDND1="2983@x" ObjectIDZND0="11904@0" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_083Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e25510_0" Pin1InfoVect1LinkObjId="SW-19319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-123 4673,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e06c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-189 4794,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_35afde0@1" ObjectIDZND0="2986@0" Pin0InfoVect0LinkObjId="SW-19322_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35afde0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-189 4794,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e0920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-137 4794,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="2986@1" ObjectIDZND0="g_30d0cc0@0" ObjectIDZND1="11907@x" Pin0InfoVect0LinkObjId="g_30d0cc0_0" Pin0InfoVect1LinkObjId="EC-CX_YR.CX_YR_084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19322_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-137 4794,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e0b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-124 4794,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_30d0cc0@0" ObjectIDZND0="2986@x" ObjectIDZND1="11907@x" Pin0InfoVect0LinkObjId="SW-19322_0" Pin0InfoVect1LinkObjId="EC-CX_YR.CX_YR_084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30d0cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-124 4794,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e0de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-124 4794,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_30d0cc0@0" ObjectIDND1="2986@x" ObjectIDZND0="11907@0" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30d0cc0_0" Pin1InfoVect1LinkObjId="SW-19322_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-124 4794,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379c860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-189 4912,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_32174d0@1" ObjectIDZND0="2989@0" Pin0InfoVect0LinkObjId="SW-19325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32174d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-189 4912,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-136 4912,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="2989@1" ObjectIDZND0="11903@x" ObjectIDZND1="g_25596b0@0" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_085Ld_0" Pin0InfoVect1LinkObjId="g_25596b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-136 4912,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-113 4912,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="11903@0" ObjectIDZND0="2989@x" ObjectIDZND1="g_25596b0@0" Pin0InfoVect0LinkObjId="SW-19325_0" Pin0InfoVect1LinkObjId="g_25596b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YR.CX_YR_085Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-113 4912,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379cf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-123 4925,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11903@x" ObjectIDND1="2989@x" ObjectIDZND0="g_25596b0@0" Pin0InfoVect0LinkObjId="g_25596b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_YR.CX_YR_085Ld_0" Pin1InfoVect1LinkObjId="SW-19325_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-123 4925,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379d1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-194 5042,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_248c250@1" ObjectIDZND0="2900@0" Pin0InfoVect0LinkObjId="SW-19231_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_248c250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-194 5042,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-158 5042,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="2900@1" ObjectIDZND0="10512@x" ObjectIDZND1="2901@x" Pin0InfoVect0LinkObjId="CB-CX_YR.CX_YR_Cb2_0" Pin0InfoVect1LinkObjId="SW-19232_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19231_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-158 5042,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37d23b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-123 5042,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10512@0" ObjectIDZND0="2900@x" ObjectIDZND1="2901@x" Pin0InfoVect0LinkObjId="SW-19231_0" Pin0InfoVect1LinkObjId="SW-19232_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_YR.CX_YR_Cb2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-123 5042,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37d2610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-147 5051,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10512@x" ObjectIDND1="2900@x" ObjectIDZND0="2901@0" Pin0InfoVect0LinkObjId="SW-19232_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_YR.CX_YR_Cb2_0" Pin1InfoVect1LinkObjId="SW-19231_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-147 5051,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37d2870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-876 4143,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2911@0" ObjectIDZND0="2890@0" Pin0InfoVect0LinkObjId="g_37c0c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-876 4143,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d2ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-628 4257,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3019@0" ObjectIDZND0="g_33456e0@0" ObjectIDZND1="2917@x" Pin0InfoVect0LinkObjId="g_33456e0_0" Pin0InfoVect1LinkObjId="SW-19250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36fb390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-628 4257,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d2d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-628 4257,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3019@x" ObjectIDND1="2917@x" ObjectIDZND0="g_33456e0@0" Pin0InfoVect0LinkObjId="g_33456e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36fb390_0" Pin1InfoVect1LinkObjId="SW-19250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-628 4257,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_37d2f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4426,-1043 4412,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_37a1290@0" ObjectIDZND0="2942@1" Pin0InfoVect0LinkObjId="SW-19278_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a1290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4426,-1043 4412,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3212290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-886 4363,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2890@0" ObjectIDZND0="2940@x" ObjectIDZND1="2941@x" Pin0InfoVect0LinkObjId="SW-19276_0" Pin0InfoVect1LinkObjId="SW-19277_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c0c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-886 4363,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32124f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-978 4363,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="2940@1" ObjectIDZND0="2890@0" ObjectIDZND1="2941@x" Pin0InfoVect0LinkObjId="g_37c0c60_0" Pin0InfoVect1LinkObjId="SW-19277_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19276_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-978 4363,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3212750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-942 4374,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="2940@x" ObjectIDND1="2890@0" ObjectIDZND0="2941@0" Pin0InfoVect0LinkObjId="SW-19277_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19276_0" Pin1InfoVect1LinkObjId="g_37c0c60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-942 4374,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32129b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4410,-942 4428,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2941@1" ObjectIDZND0="g_32983a0@0" Pin0InfoVect0LinkObjId="g_32983a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4410,-942 4428,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3212c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4087,-564 4087,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2439ea0@0" ObjectIDZND0="g_2d95610@0" ObjectIDZND1="9800@x" ObjectIDZND2="g_31bc000@0" Pin0InfoVect0LinkObjId="g_2d95610_0" Pin0InfoVect1LinkObjId="SW-19335_0" Pin0InfoVect2LinkObjId="g_31bc000_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2439ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4087,-564 4087,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3212e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4087,-551 4042,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2439ea0@0" ObjectIDND1="9801@x" ObjectIDZND0="g_2d95610@0" ObjectIDZND1="9800@x" ObjectIDZND2="g_31bc000@0" Pin0InfoVect0LinkObjId="g_2d95610_0" Pin0InfoVect1LinkObjId="SW-19335_0" Pin0InfoVect2LinkObjId="g_31bc000_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2439ea0_0" Pin1InfoVect1LinkObjId="SW-19243_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4087,-551 4042,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db7960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-1043 4363,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="2942@0" ObjectIDZND0="g_36a3c70@0" ObjectIDZND1="g_37a8060@0" ObjectIDZND2="2940@x" Pin0InfoVect0LinkObjId="g_36a3c70_0" Pin0InfoVect1LinkObjId="g_37a8060_0" Pin0InfoVect2LinkObjId="SW-19276_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19278_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-1043 4363,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db7bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-1084 4363,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36a3c70@0" ObjectIDND1="g_37a8060@0" ObjectIDZND0="2942@x" ObjectIDZND1="2940@x" Pin0InfoVect0LinkObjId="SW-19278_0" Pin0InfoVect1LinkObjId="SW-19276_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36a3c70_0" Pin1InfoVect1LinkObjId="g_37a8060_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-1084 4363,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db7e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-1041 4363,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_36a3c70@0" ObjectIDND1="g_37a8060@0" ObjectIDND2="2942@x" ObjectIDZND0="2940@0" Pin0InfoVect0LinkObjId="SW-19276_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36a3c70_0" Pin1InfoVect1LinkObjId="g_37a8060_0" Pin1InfoVect2LinkObjId="SW-19278_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4363,-1041 4363,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db8080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4282,-1051 4296,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11087@1" ObjectIDZND0="g_376e5c0@0" Pin0InfoVect0LinkObjId="g_376e5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58515_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4282,-1051 4296,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db82e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-1039 3582,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="11085@0" ObjectIDZND0="g_3692ee0@0" ObjectIDZND1="11714@1" ObjectIDZND2="g_38ec390@0" Pin0InfoVect0LinkObjId="g_3692ee0_0" Pin0InfoVect1LinkObjId="g_37b2e70_1" Pin0InfoVect2LinkObjId="g_38ec390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19259_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-1039 3582,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3db8540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-1052 3582,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="11085@x" ObjectIDND1="2927@x" ObjectIDZND0="g_3692ee0@0" ObjectIDZND1="11714@1" ObjectIDZND2="g_38ec390@0" Pin0InfoVect0LinkObjId="g_3692ee0_0" Pin0InfoVect1LinkObjId="g_37b2e70_1" Pin0InfoVect2LinkObjId="g_38ec390_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19259_0" Pin1InfoVect1LinkObjId="SW-19261_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3582,-1052 3582,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e7a2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-1052 3632,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_37791f0@0" ObjectIDZND0="2927@1" Pin0InfoVect0LinkObjId="SW-19261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37791f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-1052 3632,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e7a540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-1052 3582,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="2927@0" ObjectIDZND0="g_3692ee0@0" ObjectIDZND1="11714@1" ObjectIDZND2="g_38ec390@0" Pin0InfoVect0LinkObjId="g_3692ee0_0" Pin0InfoVect1LinkObjId="g_37b2e70_1" Pin0InfoVect2LinkObjId="g_38ec390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-1052 3582,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3781bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-955 4227,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="11082@0" ObjectIDZND0="11084@x" ObjectIDZND1="11083@x" Pin0InfoVect0LinkObjId="SW-58513_0" Pin0InfoVect1LinkObjId="SW-58512_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-955 4227,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3781e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-946 4248,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="11082@x" ObjectIDND1="11083@x" ObjectIDZND0="11084@0" Pin0InfoVect0LinkObjId="SW-58513_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58511_0" Pin1InfoVect1LinkObjId="SW-58512_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-946 4248,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3782900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1104 4228,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18019@1" ObjectIDND1="g_32a58a0@0" ObjectIDND2="g_368c580@0" ObjectIDZND0="11087@x" ObjectIDZND1="2925@x" Pin0InfoVect0LinkObjId="SW-58515_0" Pin0InfoVect1LinkObjId="SW-58516_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_393f0f0_1" Pin1InfoVect1LinkObjId="g_32a58a0_0" Pin1InfoVect2LinkObjId="g_368c580_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1104 4228,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3782b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1051 4246,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="18019@1" ObjectIDND1="g_32a58a0@0" ObjectIDND2="g_368c580@0" ObjectIDZND0="11087@0" Pin0InfoVect0LinkObjId="SW-58515_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_393f0f0_1" Pin1InfoVect1LinkObjId="g_32a58a0_0" Pin1InfoVect2LinkObjId="g_368c580_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1051 4246,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3783600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-982 4228,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="11082@1" ObjectIDZND0="11086@x" ObjectIDZND1="2925@x" Pin0InfoVect0LinkObjId="SW-58514_0" Pin0InfoVect1LinkObjId="SW-58516_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-982 4228,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3783860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-992 4248,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="11082@x" ObjectIDND1="2925@x" ObjectIDZND0="11086@0" Pin0InfoVect0LinkObjId="SW-58514_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58511_0" Pin1InfoVect1LinkObjId="SW-58516_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-992 4248,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3783ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1051 4228,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="18019@1" ObjectIDND1="g_32a58a0@0" ObjectIDND2="g_368c580@0" ObjectIDZND0="2925@0" Pin0InfoVect0LinkObjId="SW-58516_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_393f0f0_1" Pin1InfoVect1LinkObjId="g_32a58a0_0" Pin1InfoVect2LinkObjId="g_368c580_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1051 4228,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3783d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1002 4228,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="2925@1" ObjectIDZND0="11082@x" ObjectIDZND1="11086@x" Pin0InfoVect0LinkObjId="SW-58511_0" Pin0InfoVect1LinkObjId="SW-58514_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58516_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1002 4228,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3214b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-690 4028,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3019@x" ObjectIDND1="g_2d74b60@0" ObjectIDZND0="g_2dc04e0@0" ObjectIDZND1="2910@x" Pin0InfoVect0LinkObjId="g_2dc04e0_0" Pin0InfoVect1LinkObjId="SW-19242_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36fb390_0" Pin1InfoVect1LinkObjId="g_2d74b60_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-690 4028,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3214df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4028,-690 4028,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="3019@x" ObjectIDND1="g_2d74b60@0" ObjectIDND2="2910@x" ObjectIDZND0="g_2dc04e0@0" Pin0InfoVect0LinkObjId="g_2dc04e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36fb390_0" Pin1InfoVect1LinkObjId="g_2d74b60_0" Pin1InfoVect2LinkObjId="SW-19242_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4028,-690 4028,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ddde70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4006,-623 4006,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36bdd60@0" ObjectIDZND0="2910@1" Pin0InfoVect0LinkObjId="SW-19242_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36bdd60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4006,-623 4006,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dde0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4006,-674 4006,-690 4028,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2910@0" ObjectIDZND0="3019@x" ObjectIDZND1="g_2d74b60@0" ObjectIDZND2="g_2dc04e0@0" Pin0InfoVect0LinkObjId="g_36fb390_0" Pin0InfoVect1LinkObjId="g_2d74b60_0" Pin0InfoVect2LinkObjId="g_2dc04e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4006,-674 4006,-690 4028,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ddeba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-985 3917,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2934@1" ObjectIDZND0="2938@x" ObjectIDZND1="2937@x" Pin0InfoVect0LinkObjId="SW-19274_0" Pin0InfoVect1LinkObjId="SW-19273_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-985 3917,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ddee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-995 3934,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2937@x" ObjectIDND1="2934@x" ObjectIDZND0="2938@0" Pin0InfoVect0LinkObjId="SW-19274_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19273_0" Pin1InfoVect1LinkObjId="SW-19270_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-995 3934,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3285b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-1105 3917,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36f6920@0" ObjectIDND1="31884@1" ObjectIDND2="g_23f7910@0" ObjectIDZND0="2939@x" ObjectIDZND1="2937@x" Pin0InfoVect0LinkObjId="SW-19275_0" Pin0InfoVect1LinkObjId="SW-19273_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36f6920_0" Pin1InfoVect1LinkObjId="g_3cf8370_1" Pin1InfoVect2LinkObjId="g_23f7910_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-1105 3917,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3285d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-994 3917,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2938@x" ObjectIDND1="2934@x" ObjectIDZND0="2937@1" Pin0InfoVect0LinkObjId="SW-19273_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19274_0" Pin1InfoVect1LinkObjId="SW-19270_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-994 3917,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_327f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-1042 3917,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="2937@0" ObjectIDZND0="2939@x" ObjectIDZND1="g_36f6920@0" ObjectIDZND2="31884@1" Pin0InfoVect0LinkObjId="SW-19275_0" Pin0InfoVect1LinkObjId="g_36f6920_0" Pin0InfoVect2LinkObjId="g_3cf8370_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19273_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-1042 3917,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_327faf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-886 3917,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2890@0" ObjectIDZND0="2935@1" Pin0InfoVect0LinkObjId="SW-19271_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c0c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-886 3917,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_327fd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-935 3917,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="2935@0" ObjectIDZND0="2934@x" ObjectIDZND1="2936@x" Pin0InfoVect0LinkObjId="SW-19270_0" Pin0InfoVect1LinkObjId="SW-19272_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-935 3917,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3280d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-283 4186,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2898@1" ObjectIDZND0="2894@x" ObjectIDZND1="2895@x" Pin0InfoVect0LinkObjId="SW-19225_0" Pin0InfoVect1LinkObjId="SW-19226_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19229_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-283 4186,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3280f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-294 4186,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2898@x" ObjectIDND1="2895@x" ObjectIDZND0="2894@1" Pin0InfoVect0LinkObjId="SW-19225_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19229_0" Pin1InfoVect1LinkObjId="SW-19226_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-294 4186,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ffd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-294 4198,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2898@x" ObjectIDND1="2894@x" ObjectIDZND0="2895@0" Pin0InfoVect0LinkObjId="SW-19226_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19229_0" Pin1InfoVect1LinkObjId="SW-19225_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-294 4198,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31fff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4234,-294 4250,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2895@1" ObjectIDZND0="g_36f0380@0" Pin0InfoVect0LinkObjId="g_36f0380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19226_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4234,-294 4250,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3202520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4087,-551 4150,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2439ea0@0" ObjectIDND1="g_2d95610@0" ObjectIDND2="9800@x" ObjectIDZND0="9801@0" Pin0InfoVect0LinkObjId="SW-19243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2439ea0_0" Pin1InfoVect1LinkObjId="g_2d95610_0" Pin1InfoVect2LinkObjId="SW-19335_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4087,-551 4150,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3202780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-551 4196,-551 4196,-588 4182,-588 4182,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="9801@1" ObjectIDZND0="3019@x" Pin0InfoVect0LinkObjId="g_36fb390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-551 4196,-551 4196,-588 4182,-588 4182,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_452d400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-998 4777,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2950@1" ObjectIDZND0="2948@1" Pin0InfoVect0LinkObjId="SW-19284_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-998 4777,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_452d660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-998 4715,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2948@0" ObjectIDZND0="2891@0" Pin0InfoVect0LinkObjId="g_327e4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19284_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-998 4715,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_452fc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-998 4847,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2950@0" ObjectIDZND0="2949@0" Pin0InfoVect0LinkObjId="SW-19285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19286_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-998 4847,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_452fea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4883,-998 4902,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2949@1" ObjectIDZND0="3015@x" ObjectIDZND1="g_2d66aa0@0" ObjectIDZND2="g_3698020@0" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_362Ld_0" Pin0InfoVect1LinkObjId="g_2d66aa0_0" Pin0InfoVect2LinkObjId="g_3698020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4883,-998 4902,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31ea8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-923 4744,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2891@0" ObjectIDZND0="2943@0" Pin0InfoVect0LinkObjId="SW-19279_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_452d660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-923 4744,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31eab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-923 4806,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2943@1" ObjectIDZND0="g_2e2abe0@0" ObjectIDZND1="g_2b31b40@0" Pin0InfoVect0LinkObjId="g_2e2abe0_0" Pin0InfoVect1LinkObjId="g_2b31b40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-923 4806,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31fcdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4819,-839 4845,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2947@0" ObjectIDZND0="2946@0" Pin0InfoVect0LinkObjId="SW-19282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19283_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4819,-839 4845,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31fd010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-839 4906,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="2946@1" ObjectIDZND0="g_36be100@0" ObjectIDZND1="37837@1" Pin0InfoVect0LinkObjId="g_36be100_0" Pin0InfoVect1LinkObjId="g_393ec30_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-839 4906,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31fd270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-839 4743,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2891@0" ObjectIDZND0="2945@0" Pin0InfoVect0LinkObjId="SW-19281_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_452d660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-839 4743,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31fd4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-839 4795,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2945@1" ObjectIDZND0="2947@1" Pin0InfoVect0LinkObjId="SW-19283_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19281_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-839 4795,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_327e240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-761 4807,-800 4779,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2959@1" ObjectIDZND0="2958@1" Pin0InfoVect0LinkObjId="SW-19294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-761 4807,-800 4779,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_327e4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-800 4715,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2958@0" ObjectIDZND0="2891@0" Pin0InfoVect0LinkObjId="g_452d660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-800 4715,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_327e700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-734 4807,-689 4779,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2959@0" ObjectIDZND0="2957@1" Pin0InfoVect0LinkObjId="SW-19293_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-734 4807,-689 4779,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_327e960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-689 4716,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2957@0" ObjectIDZND0="3021@0" Pin0InfoVect0LinkObjId="g_323d690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-689 4716,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_323d1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-649 4847,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2956@0" ObjectIDZND0="2955@0" Pin0InfoVect0LinkObjId="SW-19291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-649 4847,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_323d430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-649 4777,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2956@1" ObjectIDZND0="2954@1" Pin0InfoVect0LinkObjId="SW-19290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-649 4777,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_323d690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-649 4716,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2954@0" ObjectIDZND0="3021@0" Pin0InfoVect0LinkObjId="g_327e960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-649 4716,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31c4210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-553 4851,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2953@0" ObjectIDZND0="2952@0" Pin0InfoVect0LinkObjId="SW-19288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-553 4851,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31c4470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-553 4745,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3021@0" ObjectIDZND0="2951@0" Pin0InfoVect0LinkObjId="SW-19287_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_327e960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-553 4745,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31c46d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-553 4805,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2951@1" ObjectIDZND0="2953@1" Pin0InfoVect0LinkObjId="SW-19289_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19287_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-553 4805,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31c6c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-482 4746,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3021@0" ObjectIDZND0="2944@0" Pin0InfoVect0LinkObjId="SW-19280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_327e960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-482 4746,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31c6ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-482 4810,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2944@1" ObjectIDZND0="g_31baeb0@0" ObjectIDZND1="g_2d85a30@0" Pin0InfoVect0LinkObjId="g_31baeb0_0" Pin0InfoVect1LinkObjId="g_2d85a30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-482 4810,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc8930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-526 4474,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3003@0" ObjectIDZND0="3004@0" Pin0InfoVect0LinkObjId="SW-19342_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4423,-526 4474,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc8b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4510,-526 4585,-526 4585,-950 4715,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3004@1" ObjectIDZND0="2891@0" Pin0InfoVect0LinkObjId="g_452d660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4510,-526 4585,-526 4585,-950 4715,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc8de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-526 4338,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3003@1" ObjectIDZND0="3005@1" Pin0InfoVect0LinkObjId="SW-19343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19341_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-526 4338,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc9040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4302,-526 3949,-526 3949,-583 3860,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="3005@0" ObjectIDZND0="3020@0" Pin0InfoVect0LinkObjId="g_2e72620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4302,-526 3949,-526 3949,-583 3860,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbcdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-628 4466,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2915@0" ObjectIDZND0="2916@0" Pin0InfoVect0LinkObjId="SW-19249_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19248_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-628 4466,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbd020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4502,-628 4716,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2916@1" ObjectIDZND0="3021@0" Pin0InfoVect0LinkObjId="g_327e960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4502,-628 4716,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbd280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-628 4308,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3019@x" ObjectIDND1="g_33456e0@0" ObjectIDZND0="2917@0" Pin0InfoVect0LinkObjId="SW-19250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36fb390_0" Pin1InfoVect1LinkObjId="g_33456e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-628 4308,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbd4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4344,-628 4394,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2917@1" ObjectIDZND0="2915@1" Pin0InfoVect0LinkObjId="SW-19248_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4344,-628 4394,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbfa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-551 3930,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d95610@0" ObjectIDND1="g_31bc000@0" ObjectIDND2="g_2439ea0@0" ObjectIDZND0="9800@1" Pin0InfoVect0LinkObjId="SW-19335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d95610_0" Pin1InfoVect1LinkObjId="g_31bc000_0" Pin1InfoVect2LinkObjId="g_2439ea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-551 3930,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbfcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-551 3844,-551 3844,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="9800@0" ObjectIDZND0="3020@x" Pin0InfoVect0LinkObjId="g_2e72620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-551 3844,-551 3844,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc4f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3804,-434 3804,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3006@0" ObjectIDZND0="3007@0" Pin0InfoVect0LinkObjId="SW-19345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19344_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3804,-434 3804,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dc5180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3804,-371 3804,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3007@1" ObjectIDZND0="2892@0" Pin0InfoVect0LinkObjId="g_36fb5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19345_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3804,-371 3804,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ee6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3804,-541 3804,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="3020@2" ObjectIDZND0="3008@0" Pin0InfoVect0LinkObjId="SW-19346_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e72620_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3804,-541 3804,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ee910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3804,-478 3804,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3008@1" ObjectIDZND0="3006@1" Pin0InfoVect0LinkObjId="SW-19344_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19346_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3804,-478 3804,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_31f13b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-946 4227,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="11082@x" ObjectIDND1="11084@x" ObjectIDZND0="11083@0" Pin0InfoVect0LinkObjId="SW-58512_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58511_0" Pin1InfoVect1LinkObjId="SW-58513_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-946 4227,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_31f1610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-896 4227,-888 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11083@1" ObjectIDZND0="2890@0" Pin0InfoVect0LinkObjId="g_37c0c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58512_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-896 4227,-888 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3243630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4979,-649 4912,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_33974f0@0" ObjectIDZND0="g_3634160@0" ObjectIDZND1="2955@x" ObjectIDZND2="g_3245a30@0" Pin0InfoVect0LinkObjId="g_3634160_0" Pin0InfoVect1LinkObjId="SW-19291_0" Pin0InfoVect2LinkObjId="g_3245a30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33974f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4979,-649 4912,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3243890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-675 4898,-675 4898,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3245a30@0" ObjectIDZND0="2955@x" ObjectIDZND1="g_3634160@0" ObjectIDZND2="g_33974f0@0" Pin0InfoVect0LinkObjId="SW-19291_0" Pin0InfoVect1LinkObjId="g_3634160_0" Pin0InfoVect2LinkObjId="g_33974f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3245a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-675 4898,-675 4898,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3244360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4883,-649 4898,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2955@1" ObjectIDZND0="g_3634160@0" ObjectIDZND1="g_33974f0@0" ObjectIDZND2="g_3245a30@0" Pin0InfoVect0LinkObjId="g_3634160_0" Pin0InfoVect1LinkObjId="g_33974f0_0" Pin0InfoVect2LinkObjId="g_3245a30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4883,-649 4898,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32445c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-649 4912,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2955@x" ObjectIDND1="g_3245a30@0" ObjectIDZND0="g_3634160@0" ObjectIDZND1="g_33974f0@0" Pin0InfoVect0LinkObjId="g_3634160_0" Pin0InfoVect1LinkObjId="g_33974f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19291_0" Pin1InfoVect1LinkObjId="g_3245a30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-649 4912,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32457d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-675 4953,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3245a30@1" ObjectIDZND0="g_3244820@0" Pin0InfoVect0LinkObjId="g_3244820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3245a30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-675 4953,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32469d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4142,-414 4142,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2920@0" ObjectIDZND0="2919@0" Pin0InfoVect0LinkObjId="SW-19252_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19253_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4142,-414 4142,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32476d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5019,-532 5006,-532 5006,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_36664f0@0" ObjectIDZND0="3016@x" ObjectIDZND1="g_36e1c00@0" ObjectIDZND2="2952@x" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_363Ld_0" Pin0InfoVect1LinkObjId="g_36e1c00_0" Pin0InfoVect2LinkObjId="SW-19288_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36664f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5019,-532 5006,-532 5006,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3248040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-553 5059,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_36664f0@0" ObjectIDND1="g_36e1c00@0" ObjectIDND2="2952@x" ObjectIDZND0="3016@0" Pin0InfoVect0LinkObjId="EC-CX_YR.CX_YR_363Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36664f0_0" Pin1InfoVect1LinkObjId="g_36e1c00_0" Pin1InfoVect2LinkObjId="SW-19288_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-553 5059,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3248230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4919,-579 4900,-579 4900,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_30cfe90@0" ObjectIDZND0="2952@x" ObjectIDZND1="g_36e1c00@0" ObjectIDZND2="g_36664f0@0" Pin0InfoVect0LinkObjId="SW-19288_0" Pin0InfoVect1LinkObjId="g_36e1c00_0" Pin0InfoVect2LinkObjId="g_36664f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30cfe90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4919,-579 4900,-579 4900,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3248c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-553 4900,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2952@1" ObjectIDZND0="g_30cfe90@0" ObjectIDZND1="g_36e1c00@0" ObjectIDZND2="g_36664f0@0" Pin0InfoVect0LinkObjId="g_30cfe90_0" Pin0InfoVect1LinkObjId="g_36e1c00_0" Pin0InfoVect2LinkObjId="g_36664f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-553 4900,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3248ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-530 4912,-530 4912,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_36e1c00@0" ObjectIDZND0="2952@x" ObjectIDZND1="g_30cfe90@0" ObjectIDZND2="g_36664f0@0" Pin0InfoVect0LinkObjId="SW-19288_0" Pin0InfoVect1LinkObjId="g_30cfe90_0" Pin0InfoVect2LinkObjId="g_36664f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36e1c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-530 4912,-530 4912,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3249960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-553 4912,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="2952@x" ObjectIDND1="g_30cfe90@0" ObjectIDZND0="g_36e1c00@0" ObjectIDZND1="g_36664f0@0" ObjectIDZND2="3016@x" Pin0InfoVect0LinkObjId="g_36e1c00_0" Pin0InfoVect1LinkObjId="g_36664f0_0" Pin0InfoVect2LinkObjId="EC-CX_YR.CX_YR_363Ld_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19288_0" Pin1InfoVect1LinkObjId="g_30cfe90_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-553 4912,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3249bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-553 5006,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_36e1c00@0" ObjectIDND1="2952@x" ObjectIDND2="g_30cfe90@0" ObjectIDZND0="g_36664f0@0" ObjectIDZND1="3016@x" Pin0InfoVect0LinkObjId="g_36664f0_0" Pin0InfoVect1LinkObjId="EC-CX_YR.CX_YR_363Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36e1c00_0" Pin1InfoVect1LinkObjId="SW-19288_0" Pin1InfoVect2LinkObjId="g_30cfe90_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-553 5006,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3250aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-1040 3749,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2931@0" ObjectIDZND0="2933@x" ObjectIDZND1="g_3672f10@0" ObjectIDZND2="g_2c2e050@0" Pin0InfoVect0LinkObjId="SW-19268_0" Pin0InfoVect1LinkObjId="g_3672f10_0" Pin0InfoVect2LinkObjId="g_2c2e050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19266_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-1040 3749,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3250d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-1053 3766,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2931@x" ObjectIDND1="g_3672f10@0" ObjectIDND2="g_2c2e050@0" ObjectIDZND0="2933@0" Pin0InfoVect0LinkObjId="SW-19268_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19266_0" Pin1InfoVect1LinkObjId="g_3672f10_0" Pin1InfoVect2LinkObjId="g_2c2e050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-1053 3766,-1053 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-8" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3417.000000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8" ObjectName="DYN-CX_YR"/>
     <cge:Meas_Ref ObjectId="8"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="3936" cy="-1198" fill="rgb(0,0,0)" fillStyle="1" r="10" stroke="rgb(255,255,255)" stroke-width="1.75238"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_FS" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.fangyongⅠhui_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3917,-1211 3917,-1152 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31884" ObjectName="AC-110kV.fangyongⅠhui_line"/>
    <cge:TPSR_Ref TObjectID="31884_SS-8"/></metadata>
   <polyline fill="none" opacity="0" points="3917,-1211 3917,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YR" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongganwanTyr_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4228,-1164 4228,-1201 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18019" ObjectName="AC-110kV.yongganwanTyr_line"/>
    <cge:TPSR_Ref TObjectID="18019_SS-8"/></metadata>
   <polyline fill="none" opacity="0" points="4228,-1164 4228,-1201 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YR" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongwandiTyr_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3582,-1208 3582,-1165 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11714" ObjectName="AC-110kV.yongwandiTyr_line"/>
    <cge:TPSR_Ref TObjectID="11714_SS-8"/></metadata>
   <polyline fill="none" opacity="0" points="3582,-1208 3582,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_FS" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.fangyongⅡhui_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3749,-1216 3749,-1171 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31883" ObjectName="AC-110kV.fangyongⅡhui_line"/>
    <cge:TPSR_Ref TObjectID="31883_SS-8"/></metadata>
   <polyline fill="none" opacity="0" points="3749,-1216 3749,-1171 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YR" endPointId="0" endStationName="YR_WD" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_weidi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5002,-839 5037,-839 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37837" ObjectName="AC-35kV.LN_weidi"/>
    <cge:TPSR_Ref TObjectID="37837_SS-8"/></metadata>
   <polyline fill="none" opacity="0" points="5002,-839 5037,-839 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="4433" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="4559" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="4673" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="4913" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="5043" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="5160" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2892" cx="3517" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2892" cx="3647" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2892" cx="3784" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2892" cx="3910" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2892" cx="4050" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2892" cx="4186" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2890" cx="3582" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="4501" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2892" cx="3623" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="4601" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2892" cx="4287" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="4405" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2890" cx="3749" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2890" cx="3805" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2893" cx="4794" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2890" cx="4143" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2890" cx="4363" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2890" cx="3917" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2891" cx="4715" cy="-998" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2891" cx="4715" cy="-840" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3021" cx="4716" cy="-689" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2891" cx="4715" cy="-800" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3021" cx="4716" cy="-649" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3021" cx="4717" cy="-553" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2891" cx="4715" cy="-950" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3021" cx="4716" cy="-628" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2892" cx="3804" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="2890" cx="4227" cy="-886" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-19312">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2976" ObjectName="SW-CX_YR.CX_YR_0812SW"/>
     <cge:Meas_Ref ObjectId="19312"/>
    <cge:TPSR_Ref TObjectID="2976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2978" ObjectName="SW-CX_YR.CX_YR_0816SW"/>
     <cge:Meas_Ref ObjectId="19314"/>
    <cge:TPSR_Ref TObjectID="2978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 -293.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2979" ObjectName="SW-CX_YR.CX_YR_0822SW"/>
     <cge:Meas_Ref ObjectId="19315"/>
    <cge:TPSR_Ref TObjectID="2979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19316">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2980" ObjectName="SW-CX_YR.CX_YR_0826SW"/>
     <cge:Meas_Ref ObjectId="19316"/>
    <cge:TPSR_Ref TObjectID="2980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2982" ObjectName="SW-CX_YR.CX_YR_0832SW"/>
     <cge:Meas_Ref ObjectId="19318"/>
    <cge:TPSR_Ref TObjectID="2982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19319">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2983" ObjectName="SW-CX_YR.CX_YR_0836SW"/>
     <cge:Meas_Ref ObjectId="19319"/>
    <cge:TPSR_Ref TObjectID="2983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19321">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2985" ObjectName="SW-CX_YR.CX_YR_0842SW"/>
     <cge:Meas_Ref ObjectId="19321"/>
    <cge:TPSR_Ref TObjectID="2985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2986" ObjectName="SW-CX_YR.CX_YR_0846SW"/>
     <cge:Meas_Ref ObjectId="19322"/>
    <cge:TPSR_Ref TObjectID="2986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2988" ObjectName="SW-CX_YR.CX_YR_0852SW"/>
     <cge:Meas_Ref ObjectId="19324"/>
    <cge:TPSR_Ref TObjectID="2988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2989" ObjectName="SW-CX_YR.CX_YR_0856SW"/>
     <cge:Meas_Ref ObjectId="19325"/>
    <cge:TPSR_Ref TObjectID="2989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19230">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5034.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2899" ObjectName="SW-CX_YR.CX_YR_0862SW"/>
     <cge:Meas_Ref ObjectId="19230"/>
    <cge:TPSR_Ref TObjectID="2899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19232">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 5046.000000 -142.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2901" ObjectName="SW-CX_YR.CX_YR_08667SW"/>
     <cge:Meas_Ref ObjectId="19232"/>
    <cge:TPSR_Ref TObjectID="2901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19231">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.804348 5033.000000 -151.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2900" ObjectName="SW-CX_YR.CX_YR_0866SW"/>
     <cge:Meas_Ref ObjectId="19231"/>
    <cge:TPSR_Ref TObjectID="2900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19234">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5151.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2903" ObjectName="SW-CX_YR.CX_YR_0872SW"/>
     <cge:Meas_Ref ObjectId="19234"/>
    <cge:TPSR_Ref TObjectID="2903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19236">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 5165.000000 -141.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2905" ObjectName="SW-CX_YR.CX_YR_08767SW"/>
     <cge:Meas_Ref ObjectId="19236"/>
    <cge:TPSR_Ref TObjectID="2905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19235">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.739130 5151.000000 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2904" ObjectName="SW-CX_YR.CX_YR_0876SW"/>
     <cge:Meas_Ref ObjectId="19235"/>
    <cge:TPSR_Ref TObjectID="2904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19254">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2921" ObjectName="SW-CX_YR.CX_YR_0026SW"/>
     <cge:Meas_Ref ObjectId="19254"/>
    <cge:TPSR_Ref TObjectID="2921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19253">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 -376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2920" ObjectName="SW-CX_YR.CX_YR_0022SW"/>
     <cge:Meas_Ref ObjectId="19253"/>
    <cge:TPSR_Ref TObjectID="2920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19296">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3508.000000 -298.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2960" ObjectName="SW-CX_YR.CX_YR_0611SW"/>
     <cge:Meas_Ref ObjectId="19296"/>
    <cge:TPSR_Ref TObjectID="2960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19298">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3508.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2962" ObjectName="SW-CX_YR.CX_YR_0616SW"/>
     <cge:Meas_Ref ObjectId="19298"/>
    <cge:TPSR_Ref TObjectID="2962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2964" ObjectName="SW-CX_YR.CX_YR_0621SW"/>
     <cge:Meas_Ref ObjectId="19300"/>
    <cge:TPSR_Ref TObjectID="2964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2966" ObjectName="SW-CX_YR.CX_YR_0626SW"/>
     <cge:Meas_Ref ObjectId="19302"/>
    <cge:TPSR_Ref TObjectID="2966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2968" ObjectName="SW-CX_YR.CX_YR_0631SW"/>
     <cge:Meas_Ref ObjectId="19304"/>
    <cge:TPSR_Ref TObjectID="2968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2970" ObjectName="SW-CX_YR.CX_YR_0636SW"/>
     <cge:Meas_Ref ObjectId="19306"/>
    <cge:TPSR_Ref TObjectID="2970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2972" ObjectName="SW-CX_YR.CX_YR_0641SW"/>
     <cge:Meas_Ref ObjectId="19308"/>
    <cge:TPSR_Ref TObjectID="2972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19310">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2974" ObjectName="SW-CX_YR.CX_YR_0646SW"/>
     <cge:Meas_Ref ObjectId="19310"/>
    <cge:TPSR_Ref TObjectID="2974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19349">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3010" ObjectName="SW-CX_YR.CX_YR_0651SW"/>
     <cge:Meas_Ref ObjectId="19349"/>
    <cge:TPSR_Ref TObjectID="3010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19225">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -297.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2894" ObjectName="SW-CX_YR.CX_YR_0661SW"/>
     <cge:Meas_Ref ObjectId="19225"/>
    <cge:TPSR_Ref TObjectID="2894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19227">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2896" ObjectName="SW-CX_YR.CX_YR_0666SW"/>
     <cge:Meas_Ref ObjectId="19227"/>
    <cge:TPSR_Ref TObjectID="2896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19329">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2993" ObjectName="SW-CX_YR.CX_YR_0902SW"/>
     <cge:Meas_Ref ObjectId="19329"/>
    <cge:TPSR_Ref TObjectID="2993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19233">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2902" ObjectName="SW-CX_YR.CX_YR_08600SW"/>
     <cge:Meas_Ref ObjectId="19233"/>
    <cge:TPSR_Ref TObjectID="2902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19237">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5151.000000 51.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2906" ObjectName="SW-CX_YR.CX_YR_08700SW"/>
     <cge:Meas_Ref ObjectId="19237"/>
    <cge:TPSR_Ref TObjectID="2906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19228">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 4193.000000 -120.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2897" ObjectName="SW-CX_YR.CX_YR_06667SW"/>
     <cge:Meas_Ref ObjectId="19228"/>
    <cge:TPSR_Ref TObjectID="2897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.978261 4278.000000 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3012" ObjectName="SW-CX_YR.CX_YR_0121SW"/>
     <cge:Meas_Ref ObjectId="19378"/>
    <cge:TPSR_Ref TObjectID="3012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19327">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.978261 4396.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2991" ObjectName="SW-CX_YR.CX_YR_0122SW"/>
     <cge:Meas_Ref ObjectId="19327"/>
    <cge:TPSR_Ref TObjectID="2991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.978261 4250.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3013" ObjectName="SW-CX_YR.CX_YR_01217SW"/>
     <cge:Meas_Ref ObjectId="19379"/>
    <cge:TPSR_Ref TObjectID="3013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19340">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -1.000000 3689.000000 -553.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3002" ObjectName="SW-CX_YR.CX_YR_1010SW"/>
     <cge:Meas_Ref ObjectId="19340"/>
    <cge:TPSR_Ref TObjectID="3002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19276">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -1.000000 4353.000000 -971.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2940" ObjectName="SW-CX_YR.CX_YR_1901SW"/>
     <cge:Meas_Ref ObjectId="19276"/>
    <cge:TPSR_Ref TObjectID="2940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -1.000000 3613.000000 -363.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2995" ObjectName="SW-CX_YR.CX_YR_0901SW"/>
     <cge:Meas_Ref ObjectId="19331"/>
    <cge:TPSR_Ref TObjectID="2995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19261">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 -1047.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2927" ObjectName="SW-CX_YR.CX_YR_16167SW"/>
     <cge:Meas_Ref ObjectId="19261"/>
    <cge:TPSR_Ref TObjectID="2927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19260">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 -990.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2926" ObjectName="SW-CX_YR.CX_YR_16160SW"/>
     <cge:Meas_Ref ObjectId="19260"/>
    <cge:TPSR_Ref TObjectID="2926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19258">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 -943.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2924" ObjectName="SW-CX_YR.CX_YR_16117SW"/>
     <cge:Meas_Ref ObjectId="19258"/>
    <cge:TPSR_Ref TObjectID="2924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19268">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3761.000000 -1048.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2933" ObjectName="SW-CX_YR.CX_YR_16267SW"/>
     <cge:Meas_Ref ObjectId="19268"/>
    <cge:TPSR_Ref TObjectID="2933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19267">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3761.000000 -991.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2932" ObjectName="SW-CX_YR.CX_YR_16260SW"/>
     <cge:Meas_Ref ObjectId="19267"/>
    <cge:TPSR_Ref TObjectID="2932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19265">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3759.000000 -945.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2930" ObjectName="SW-CX_YR.CX_YR_16217SW"/>
     <cge:Meas_Ref ObjectId="19265"/>
    <cge:TPSR_Ref TObjectID="2930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19272">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -945.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2936" ObjectName="SW-CX_YR.CX_YR_16317SW"/>
     <cge:Meas_Ref ObjectId="19272"/>
    <cge:TPSR_Ref TObjectID="2936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19274">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3928.000000 -990.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2938" ObjectName="SW-CX_YR.CX_YR_16360SW"/>
     <cge:Meas_Ref ObjectId="19274"/>
    <cge:TPSR_Ref TObjectID="2938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19275">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 -1048.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2939" ObjectName="SW-CX_YR.CX_YR_16367SW"/>
     <cge:Meas_Ref ObjectId="19275"/>
    <cge:TPSR_Ref TObjectID="2939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19259">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.000000 -998.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11085" ObjectName="SW-CX_YR.CX_YR_1616SW"/>
     <cge:Meas_Ref ObjectId="19259"/>
    <cge:TPSR_Ref TObjectID="11085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19257">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.000000 -895.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2923" ObjectName="SW-CX_YR.CX_YR_1611SW"/>
     <cge:Meas_Ref ObjectId="19257"/>
    <cge:TPSR_Ref TObjectID="2923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19266">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -999.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2931" ObjectName="SW-CX_YR.CX_YR_1626SW"/>
     <cge:Meas_Ref ObjectId="19266"/>
    <cge:TPSR_Ref TObjectID="2931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19264">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -898.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2929" ObjectName="SW-CX_YR.CX_YR_1621SW"/>
     <cge:Meas_Ref ObjectId="19264"/>
    <cge:TPSR_Ref TObjectID="2929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19337">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 -774.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2999" ObjectName="SW-CX_YR.CX_YR_10117SW"/>
     <cge:Meas_Ref ObjectId="19337"/>
    <cge:TPSR_Ref TObjectID="2999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19339">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 -704.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3001" ObjectName="SW-CX_YR.CX_YR_10160SW"/>
     <cge:Meas_Ref ObjectId="19339"/>
    <cge:TPSR_Ref TObjectID="3001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19245">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -822.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2912" ObjectName="SW-CX_YR.CX_YR_10217SW"/>
     <cge:Meas_Ref ObjectId="19245"/>
    <cge:TPSR_Ref TObjectID="2912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19247">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -764.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2914" ObjectName="SW-CX_YR.CX_YR_10260SW"/>
     <cge:Meas_Ref ObjectId="19247"/>
    <cge:TPSR_Ref TObjectID="2914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19251">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -691.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2918" ObjectName="SW-CX_YR.CX_YR_10267SW"/>
     <cge:Meas_Ref ObjectId="19251"/>
    <cge:TPSR_Ref TObjectID="2918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19336">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 -793.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2998" ObjectName="SW-CX_YR.CX_YR_1011SW"/>
     <cge:Meas_Ref ObjectId="19336"/>
    <cge:TPSR_Ref TObjectID="2998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19338">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 -642.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3000" ObjectName="SW-CX_YR.CX_YR_1016SW"/>
     <cge:Meas_Ref ObjectId="19338"/>
    <cge:TPSR_Ref TObjectID="3000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19244">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -835.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2911" ObjectName="SW-CX_YR.CX_YR_1021SW"/>
     <cge:Meas_Ref ObjectId="19244"/>
    <cge:TPSR_Ref TObjectID="2911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19246">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2913" ObjectName="SW-CX_YR.CX_YR_1026SW"/>
     <cge:Meas_Ref ObjectId="19246"/>
    <cge:TPSR_Ref TObjectID="2913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19278">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4372.000000 -1038.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2942" ObjectName="SW-CX_YR.CX_YR_19017SW"/>
     <cge:Meas_Ref ObjectId="19278"/>
    <cge:TPSR_Ref TObjectID="2942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19277">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -937.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2941" ObjectName="SW-CX_YR.CX_YR_19010SW"/>
     <cge:Meas_Ref ObjectId="19277"/>
    <cge:TPSR_Ref TObjectID="2941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58513">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -941.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11084" ObjectName="SW-CX_YR.CX_YR_16417SW"/>
     <cge:Meas_Ref ObjectId="58513"/>
    <cge:TPSR_Ref TObjectID="11084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58514">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -987.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11086" ObjectName="SW-CX_YR.CX_YR_16460SW"/>
     <cge:Meas_Ref ObjectId="58514"/>
    <cge:TPSR_Ref TObjectID="11086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58515">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4241.000000 -1046.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11087" ObjectName="SW-CX_YR.CX_YR_16467SW"/>
     <cge:Meas_Ref ObjectId="58515"/>
    <cge:TPSR_Ref TObjectID="11087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19332">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 -415.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2996" ObjectName="SW-CX_YR.CX_YR_09017SW"/>
     <cge:Meas_Ref ObjectId="19332"/>
    <cge:TPSR_Ref TObjectID="2996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19330">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.000000 -413.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2994" ObjectName="SW-CX_YR.CX_YR_09027SW"/>
     <cge:Meas_Ref ObjectId="19330"/>
    <cge:TPSR_Ref TObjectID="2994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58516">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -997.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2925" ObjectName="SW-CX_YR.CX_YR_1646SW"/>
     <cge:Meas_Ref ObjectId="58516"/>
    <cge:TPSR_Ref TObjectID="2925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19242">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 -633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2910" ObjectName="SW-CX_YR.CX_YR_1020SW"/>
     <cge:Meas_Ref ObjectId="19242"/>
    <cge:TPSR_Ref TObjectID="2910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19273">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 -1001.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2937" ObjectName="SW-CX_YR.CX_YR_1636SW"/>
     <cge:Meas_Ref ObjectId="19273"/>
    <cge:TPSR_Ref TObjectID="2937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19271">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 -894.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2935" ObjectName="SW-CX_YR.CX_YR_1631SW"/>
     <cge:Meas_Ref ObjectId="19271"/>
    <cge:TPSR_Ref TObjectID="2935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19226">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -289.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2895" ObjectName="SW-CX_YR.CX_YR_06617SW"/>
     <cge:Meas_Ref ObjectId="19226"/>
    <cge:TPSR_Ref TObjectID="2895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4145.000000 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9801" ObjectName="SW-CX_YR.CX_YR_3020SW"/>
     <cge:Meas_Ref ObjectId="19243"/>
    <cge:TPSR_Ref TObjectID="9801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 -993.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2948" ObjectName="SW-CX_YR.CX_YR_3621SW"/>
     <cge:Meas_Ref ObjectId="19284"/>
    <cge:TPSR_Ref TObjectID="2948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19285">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -993.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2949" ObjectName="SW-CX_YR.CX_YR_3626SW"/>
     <cge:Meas_Ref ObjectId="19285"/>
    <cge:TPSR_Ref TObjectID="2949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 -918.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2943" ObjectName="SW-CX_YR.CX_YR_3901SW"/>
     <cge:Meas_Ref ObjectId="19279"/>
    <cge:TPSR_Ref TObjectID="2943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19281">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -834.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2945" ObjectName="SW-CX_YR.CX_YR_3611SW"/>
     <cge:Meas_Ref ObjectId="19281"/>
    <cge:TPSR_Ref TObjectID="2945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19282">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4840.000000 -834.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2946" ObjectName="SW-CX_YR.CX_YR_3616SW"/>
     <cge:Meas_Ref ObjectId="19282"/>
    <cge:TPSR_Ref TObjectID="2946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -795.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2958" ObjectName="SW-CX_YR.CX_YR_3121SW"/>
     <cge:Meas_Ref ObjectId="19294"/>
    <cge:TPSR_Ref TObjectID="2958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -684.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2957" ObjectName="SW-CX_YR.CX_YR_3122SW"/>
     <cge:Meas_Ref ObjectId="19293"/>
    <cge:TPSR_Ref TObjectID="2957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19290">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 -644.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2954" ObjectName="SW-CX_YR.CX_YR_3642SW"/>
     <cge:Meas_Ref ObjectId="19290"/>
    <cge:TPSR_Ref TObjectID="2954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19291">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -644.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2955" ObjectName="SW-CX_YR.CX_YR_3646SW"/>
     <cge:Meas_Ref ObjectId="19291"/>
    <cge:TPSR_Ref TObjectID="2955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19287">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4740.000000 -548.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2951" ObjectName="SW-CX_YR.CX_YR_3632SW"/>
     <cge:Meas_Ref ObjectId="19287"/>
    <cge:TPSR_Ref TObjectID="2951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19288">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -548.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2952" ObjectName="SW-CX_YR.CX_YR_3636SW"/>
     <cge:Meas_Ref ObjectId="19288"/>
    <cge:TPSR_Ref TObjectID="2952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19280">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.000000 -477.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2944" ObjectName="SW-CX_YR.CX_YR_3902SW"/>
     <cge:Meas_Ref ObjectId="19280"/>
    <cge:TPSR_Ref TObjectID="2944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19343">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 -521.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3005" ObjectName="SW-CX_YR.CX_YR_3016SW"/>
     <cge:Meas_Ref ObjectId="19343"/>
    <cge:TPSR_Ref TObjectID="3005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19342">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4469.000000 -521.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3004" ObjectName="SW-CX_YR.CX_YR_3011SW"/>
     <cge:Meas_Ref ObjectId="19342"/>
    <cge:TPSR_Ref TObjectID="3004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19250">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -623.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2917" ObjectName="SW-CX_YR.CX_YR_3026SW"/>
     <cge:Meas_Ref ObjectId="19250"/>
    <cge:TPSR_Ref TObjectID="2917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19249">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4461.000000 -623.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2916" ObjectName="SW-CX_YR.CX_YR_3022SW"/>
     <cge:Meas_Ref ObjectId="19249"/>
    <cge:TPSR_Ref TObjectID="2916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9800" ObjectName="SW-CX_YR.CX_YR_3010SW"/>
     <cge:Meas_Ref ObjectId="19335"/>
    <cge:TPSR_Ref TObjectID="9800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19346">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3795.000000 -473.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3008" ObjectName="SW-CX_YR.CX_YR_0016SW"/>
     <cge:Meas_Ref ObjectId="19346"/>
    <cge:TPSR_Ref TObjectID="3008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3795.000000 -366.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3007" ObjectName="SW-CX_YR.CX_YR_0011SW"/>
     <cge:Meas_Ref ObjectId="19345"/>
    <cge:TPSR_Ref TObjectID="3007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58512">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -891.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11083" ObjectName="SW-CX_YR.CX_YR_1641SW"/>
     <cge:Meas_Ref ObjectId="58512"/>
    <cge:TPSR_Ref TObjectID="11083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19297">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3525.000000 -284.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2961" ObjectName="SW-CX_YR.CX_YR_06117SW"/>
     <cge:Meas_Ref ObjectId="19297"/>
    <cge:TPSR_Ref TObjectID="2961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -283.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2965" ObjectName="SW-CX_YR.CX_YR_06217SW"/>
     <cge:Meas_Ref ObjectId="19301"/>
    <cge:TPSR_Ref TObjectID="2965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19305">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3791.000000 -284.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2969" ObjectName="SW-CX_YR.CX_YR_06317SW"/>
     <cge:Meas_Ref ObjectId="19305"/>
    <cge:TPSR_Ref TObjectID="2969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19309">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3914.000000 -285.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2973" ObjectName="SW-CX_YR.CX_YR_06417SW"/>
     <cge:Meas_Ref ObjectId="19309"/>
    <cge:TPSR_Ref TObjectID="2973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19350">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -284.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3011" ObjectName="SW-CX_YR.CX_YR_06517SW"/>
     <cge:Meas_Ref ObjectId="19350"/>
    <cge:TPSR_Ref TObjectID="3011"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e7c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e7c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e7c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e7c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e7c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e7c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e7c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2faa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbc4d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4398.142857 -153.800000) translate(0,15)">野</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbc4d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4398.142857 -153.800000) translate(0,33)">猪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbc4d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4398.142857 -153.800000) translate(0,51)">箐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbc4d0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4398.142857 -153.800000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_338b580" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4527.309524 -167.800000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_338b580" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4527.309524 -167.800000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_338b580" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4527.309524 -167.800000) translate(0,51)">Ⅴ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_338b580" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4527.309524 -167.800000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_338b580" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4527.309524 -167.800000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23fb3c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4643.476190 -153.800000) translate(0,15)">沙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23fb3c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4643.476190 -153.800000) translate(0,33)">松</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23fb3c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4643.476190 -153.800000) translate(0,51)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23fb3c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4643.476190 -153.800000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39663c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4766.642857 -136.800000) translate(0,15)">麦</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39663c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4766.642857 -136.800000) translate(0,33)">拉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39663c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4766.642857 -136.800000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b65590" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4885.809524 -170.800000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b65590" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4885.809524 -170.800000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b65590" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4885.809524 -170.800000) translate(0,51)">IV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b65590" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4885.809524 -170.800000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b65590" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4885.809524 -170.800000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_373d3a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4993.976190 -94.800000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_373d3a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4993.976190 -94.800000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_373d3a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4993.976190 -94.800000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_373d3a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4993.976190 -94.800000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_373d3a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4993.976190 -94.800000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_373d3a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4993.976190 -94.800000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e75770" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5114.142857 -94.800000) translate(0,15)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e75770" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5114.142857 -94.800000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e75770" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5114.142857 -94.800000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e75770" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5114.142857 -94.800000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e75770" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5114.142857 -94.800000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e75770" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5114.142857 -94.800000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2707a50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3489.142857 -170.800000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2707a50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3489.142857 -170.800000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2707a50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3489.142857 -170.800000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2707a50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3489.142857 -170.800000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2707a50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3489.142857 -170.800000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2de6780" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3618.742857 -170.800000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2de6780" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3618.742857 -170.800000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2de6780" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3618.742857 -170.800000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2de6780" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3618.742857 -170.800000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2de6780" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3618.742857 -170.800000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3810e40" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4015.542857 -178.800000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3810e40" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4015.542857 -178.800000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3810e40" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4015.542857 -178.800000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3810e40" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4015.542857 -178.800000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3810e40" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4015.542857 -178.800000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_370d190" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4133.142857 -94.800000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_370d190" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4133.142857 -94.800000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_370d190" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4133.142857 -94.800000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_370d190" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4133.142857 -94.800000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_370d190" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4133.142857 -94.800000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_370d190" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4133.142857 -94.800000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39d1a80" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3549.142857 -1227.800000) translate(0,15)">永万的线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25854f0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3729.389610 -1231.300000) translate(0,15)">方永Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36f5b50" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3891.623377 -1229.800000) translate(0,15)">方永Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3667a00" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4360.870130 -1167.000000) translate(0,15)">110kV电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e7c500" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5048.142857 -861.800000) translate(0,15)">维的线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38e9480" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5054.142857 -1025.800000) translate(0,15)">永物T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ddd220" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4911.142857 -911.800000) translate(0,15)">35kVI段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36a8e10" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4922.142857 -466.800000) translate(0,15)">35kVII段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23f01c0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3563.742857 -530.800000) translate(0,15)">10kVI段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3712720" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4521.742857 -522.800000) translate(0,15)">10kVI段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c0b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3544.000000 -978.000000) translate(0,12)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2419e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3536.000000 -926.000000) translate(0,12)">1611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e2bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3598.000000 -939.000000) translate(0,12)">16117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ac780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3597.500000 -985.000000) translate(0,12)">16160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35fcce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.500000 -1047.000000) translate(0,12)">16167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a2660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3534.000000 -1030.000000) translate(0,12)">1616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_371f230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3712.000000 -977.000000) translate(0,12)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d5730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3704.000000 -926.000000) translate(0,12)">1621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3757f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -940.000000) translate(0,12)">16217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37580d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3703.000000 -1030.000000) translate(0,12)">1626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c0d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -1043.000000) translate(0,12)">16267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c0eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -986.000000) translate(0,12)">16260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31e3390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3880.000000 -977.000000) translate(0,12)">163</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_338ed90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3872.000000 -926.000000) translate(0,12)">1631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_369f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3932.000000 -939.000000) translate(0,12)">16317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a1c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3931.500000 -985.000000) translate(0,12)">16360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b2bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3931.500000 -1042.000000) translate(0,12)">16367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3312d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -1002.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3390170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -964.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36252f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4374.000000 -1064.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_372c330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -756.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31e7ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3812.000000 -823.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7b520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3821.000000 -805.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c12e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3812.000000 -671.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e00fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3821.000000 -733.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c1aa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3652.000000 -582.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36321d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3813.000000 -455.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3696a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3811.000000 -395.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a7e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3811.000000 -515.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3671200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3857.000000 -616.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31bc400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4174.000000 -670.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3277030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -862.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -847.000000) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36bd9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -789.000000) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3687f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -737.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3625c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -721.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3566f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4000.000000 -611.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_370c210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -453.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3511740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -405.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3505250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -499.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,51)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,69)">性</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,87)">点</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,105)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,123)">压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,141)">互</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,159)">感</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3cf93a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3964.742857 -722.800000) translate(0,177)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3804490" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4063.742857 -655.800000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3804490" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4063.742857 -655.800000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3804490" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4063.742857 -655.800000) translate(0,51)">消</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3804490" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4063.742857 -655.800000) translate(0,69)">弧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3804490" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4063.742857 -655.800000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3804490" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4063.742857 -655.800000) translate(0,105)">圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3804680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3630.000000 -392.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3696260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3634.000000 -446.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3696430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -390.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36da190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -444.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3754490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3478.000000 -911.000000) translate(0,15)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37d6820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3501.000000 -370.000000) translate(0,15)">10kV I母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4726.000000 -453.000000) translate(0,15)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ba0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4800.000000 -864.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36e4760" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5053.142857 -578.800000) translate(0,15)">永龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33b1f70" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5022.142857 -597.800000) translate(0,15)">35kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30be450" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 5046.142857 -673.800000) translate(0,15)">永方盛线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c3b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -866.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f6c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4851.000000 -866.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cf55f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -949.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33880d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4800.000000 -1021.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3209390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4740.000000 -1025.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d8ddb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4848.000000 -1023.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3657d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -755.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33363c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -825.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -715.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3769b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4800.000000 -673.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f7520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -675.000000) translate(0,12)">3642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373ea30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4848.000000 -675.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362e6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -579.000000) translate(0,12)">3632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b39e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4846.000000 -579.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36877d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -507.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36effc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4394.000000 -653.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c9800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4395.000000 -550.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d2750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -552.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a0ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4305.000000 -552.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_376fb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4466.000000 -655.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c8f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4306.000000 -655.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_369d540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3527.000000 -268.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b35e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -323.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31bd8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3529.000000 -310.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368ea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -160.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3684b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3656.000000 -269.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3700210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -324.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cb1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3658.000000 -311.000000) translate(0,12)">06217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3807680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -161.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b3990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -269.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3628ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.000000 -324.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ad370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3795.000000 -310.000000) translate(0,12)">06317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ca660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.000000 -161.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a64fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -269.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f1fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -324.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3754830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -310.000000) translate(0,12)">06417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368e690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -161.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3213550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4057.000000 -324.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c17310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -310.000000) translate(0,12)">06517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36fd960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -277.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bb920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -325.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d8190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -315.000000) translate(0,12)">06617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cf6720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -165.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cf5a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4328.000000 -291.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c0f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -256.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3756dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4294.000000 -323.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36599f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4361.000000 -321.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3711b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -277.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37527b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4439.000000 -324.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3697170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4439.000000 -161.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b8630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -277.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c4960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -161.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3717ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -324.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36faaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -278.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_366af30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -324.000000) translate(0,12)">0832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_370c870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -161.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_371f9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4803.000000 -278.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3687b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -324.000000) translate(0,12)">0842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b98e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -161.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b9b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4921.000000 -278.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4919.000000 -324.000000) translate(0,12)">0852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b1bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4919.000000 -161.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c0170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5051.000000 -280.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c0370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.000000 -325.000000) translate(0,12)">0862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373ad20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5047.000000 -183.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5055.000000 -165.000000) translate(0,12)">08667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a9ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5169.000000 -280.000000) translate(0,12)">087</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32863e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -325.000000) translate(0,12)">0872</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32865e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5166.000000 -180.000000) translate(0,12)">0876</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e33da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5168.000000 -141.000000) translate(0,12)">08767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e33fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5107.000000 18.000000) translate(0,12)">08700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36aa600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.000000 17.000000) translate(0,12)">08600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23efe30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -802.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3704710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 -1167.500000) translate(0,16)">永仁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37aad90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3752.342857 -170.800000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37aad90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3752.342857 -170.800000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37aad90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3752.342857 -170.800000) translate(0,51)">III</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37aad90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3752.342857 -170.800000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37aad90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3752.342857 -170.800000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,245,238)" font-family="SimSun" font-size="15" graphid="g_31cbc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3902.000000 -577.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,245,238)" font-family="SimSun" font-size="15" graphid="g_31bbde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -577.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,245,238)" font-family="SimSun" font-size="15" graphid="g_36f7fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -122.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ddf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4192.000000 -977.000000) translate(0,12)">164</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39de430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -925.000000) translate(0,12)">1641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a8890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4246.000000 -938.000000) translate(0,12)">16417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a8aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -984.000000) translate(0,12)">16460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a8cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -1044.000000) translate(0,12)">16467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a2f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -1029.000000) translate(0,12)">1646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32a3170" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4200.142857 -1235.800000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32a3170" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4200.142857 -1235.800000) translate(0,33)">干</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32a3170" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4200.142857 -1235.800000) translate(0,51)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32a3170" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4200.142857 -1235.800000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329b380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5072.000000 -372.000000) translate(0,15)">10kV II母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329b5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -1057.000000) translate(0,15)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32a0ab0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3881.142857 -153.800000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32a0ab0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3881.142857 -153.800000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32a0ab0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3881.142857 -153.800000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32a0ab0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3881.142857 -153.800000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_370e700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -1030.000000) translate(0,12)">1636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32463a0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4956.142857 -699.800000) translate(0,15)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3247060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3286.000000 -247.000000) translate(0,12)">4985</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324f040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4806.000000 -577.000000) translate(0,12)">363</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_38a4d50" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4265.500000 -195.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d8c320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.833333 -141.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2607a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5218.000000 -140.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df43a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 -703.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e1d630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3693.000000 -526.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bdd60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4000.000000 -605.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_332d610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3575.937500 -283.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24e6480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.600000 -281.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3da9210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.200000 -283.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_241cac0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3963.800000 -283.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2508120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.400000 -283.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f0380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -288.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3684580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 -989.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36b8310" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 -942.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3703590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.666667 -1047.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37a4a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.666667 -990.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_364adf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.666667 -944.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24418a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.333333 -1047.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2580fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.333333 -989.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3525c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.333333 -944.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36c8c60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -821.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_370bb70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3866.000000 -773.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_248ebb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -690.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31beb40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -763.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31bf180" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.400000 -414.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37891e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4659.400000 -412.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39e2a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5036.000000 78.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32acef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5154.000000 78.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2419500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -119.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_376e5c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -1045.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256fcc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 -986.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f07e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 -940.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37a1290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -1037.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32983a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -936.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37791f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3643.000000 -1046.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YR"/>
</svg>