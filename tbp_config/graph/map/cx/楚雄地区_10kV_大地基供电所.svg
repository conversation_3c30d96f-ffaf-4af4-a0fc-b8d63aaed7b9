<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="0" id="thSvg" viewBox="-2 -2972 4209 2974">
 
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    
   </symbol>
   <symbol id="Tag:shape27">
    
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(127,127,127);fill:none}
.BKBV-0KV { stroke:rgb(127,127,127);fill:rgb(127,127,127)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(0,72,216);fill:none}
.BKBV-10KV { stroke:rgb(0,72,216);fill:rgb(0,72,216)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(0,255,0);fill:none}
.BKBV-20KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(213,0,0);fill:none}
.BKBV-110KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-220KV { stroke:rgb(255,0,255);fill:none}
.BKBV-220KV { stroke:rgb(255,0,255);fill:rgb(255,0,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(255,0,0);fill:none}
.BKBV-500KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-750KV { stroke:rgb(255,0,0);fill:none}
.BKBV-750KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="2984" width="4219" x="-7" y="-2977"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="0" fill="none" points="3138,-1899 3139,-1899 3140,-1899 3141,-1899 3142,-1899 3143,-1900 3144,-1900 3144,-1901 3145,-1902 3145,-1902 3146,-1903 3146,-1904 3147,-1905 3147,-1906 3147,-1907 3147,-1908 3146,-1909 3146,-1910 3145,-1911 3145,-1911 3144,-1912 3144,-1913 3143,-1913 3142,-1914 3141,-1914 3140,-1914 3139,-1914 3138,-1914 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3138,-1884 3139,-1884 3140,-1884 3141,-1884 3142,-1884 3143,-1885 3144,-1885 3144,-1886 3145,-1887 3145,-1887 3146,-1888 3146,-1889 3147,-1890 3147,-1891 3147,-1892 3147,-1893 3146,-1894 3146,-1895 3145,-1896 3145,-1896 3144,-1897 3144,-1898 3143,-1898 3142,-1899 3141,-1899 3140,-1899 3139,-1899 3138,-1899 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3330,-1899 3331,-1899 3332,-1899 3333,-1899 3334,-1899 3335,-1900 3336,-1900 3336,-1901 3337,-1902 3337,-1902 3338,-1903 3338,-1904 3339,-1905 3339,-1906 3339,-1907 3339,-1908 3338,-1909 3338,-1910 3337,-1911 3337,-1911 3336,-1912 3336,-1913 3335,-1913 3334,-1914 3333,-1914 3332,-1914 3331,-1914 3330,-1914 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3330,-1884 3331,-1884 3332,-1884 3333,-1884 3334,-1884 3335,-1885 3336,-1885 3336,-1886 3337,-1887 3337,-1887 3338,-1888 3338,-1889 3339,-1890 3339,-1891 3339,-1892 3339,-1893 3338,-1894 3338,-1895 3337,-1896 3337,-1896 3336,-1897 3336,-1898 3335,-1898 3334,-1899 3333,-1899 3332,-1899 3331,-1899 3330,-1899 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3904,-1754 3905,-1754 3906,-1754 3907,-1755 3908,-1755 3908,-1755 3909,-1756 3910,-1757 3910,-1758 3911,-1758 3911,-1759 3911,-1760 3911,-1761 3911,-1762 3911,-1763 3911,-1764 3911,-1765 3910,-1766 3910,-1766 3909,-1767 3908,-1768 3908,-1768 3907,-1768 3906,-1769 3905,-1769 3904,-1769 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3904,-1739 3905,-1739 3906,-1739 3907,-1740 3908,-1740 3908,-1740 3909,-1741 3910,-1742 3910,-1743 3911,-1743 3911,-1744 3911,-1745 3911,-1746 3911,-1747 3911,-1748 3911,-1749 3911,-1750 3910,-1751 3910,-1751 3909,-1752 3908,-1753 3908,-1753 3907,-1753 3906,-1754 3905,-1754 3904,-1754 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3783,-1149 3783,-1148 3783,-1147 3783,-1146 3784,-1145 3784,-1145 3785,-1144 3785,-1143 3786,-1143 3787,-1142 3788,-1142 3789,-1141 3790,-1141 3791,-1141 3791,-1141 3792,-1141 3793,-1142 3794,-1142 3795,-1143 3796,-1143 3796,-1144 3797,-1145 3797,-1145 3798,-1146 3798,-1147 3798,-1148 3798,-1149 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="3798,-1149 3798,-1148 3798,-1147 3798,-1146 3799,-1145 3799,-1145 3800,-1144 3800,-1143 3801,-1143 3802,-1142 3803,-1142 3804,-1141 3805,-1141 3806,-1141 3806,-1141 3807,-1141 3808,-1142 3809,-1142 3810,-1143 3811,-1143 3811,-1144 3812,-1145 3812,-1145 3813,-1146 3813,-1147 3813,-1148 3813,-1149 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2727,-1472 2727,-1471 2727,-1470 2728,-1469 2728,-1468 2728,-1468 2729,-1467 2730,-1466 2731,-1466 2731,-1465 2732,-1465 2733,-1465 2734,-1465 2735,-1465 2736,-1465 2737,-1465 2738,-1465 2739,-1466 2739,-1466 2740,-1467 2741,-1468 2741,-1468 2741,-1469 2742,-1470 2742,-1471 2742,-1472 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2742,-1472 2742,-1471 2742,-1470 2743,-1469 2743,-1468 2743,-1468 2744,-1467 2745,-1466 2746,-1466 2746,-1465 2747,-1465 2748,-1465 2749,-1465 2750,-1465 2751,-1465 2752,-1465 2753,-1465 2754,-1466 2754,-1466 2755,-1467 2756,-1468 2756,-1468 2756,-1469 2757,-1470 2757,-1471 2757,-1472 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2745,-570 2745,-569 2746,-568 2746,-567 2746,-567 2747,-566 2748,-565 2749,-565 2749,-564 2750,-564 2751,-564 2752,-564 2753,-564 2754,-564 2755,-564 2756,-564 2756,-565 2757,-565 2758,-566 2759,-567 2759,-567 2759,-568 2760,-569 2760,-570 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2760,-570 2760,-569 2761,-568 2761,-567 2761,-567 2762,-566 2763,-565 2764,-565 2764,-564 2765,-564 2766,-564 2767,-564 2768,-564 2769,-564 2770,-564 2771,-564 2771,-565 2772,-565 2773,-566 2774,-567 2774,-567 2774,-568 2775,-569 2775,-570 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2557,-1077 2558,-1077 2559,-1077 2560,-1077 2561,-1077 2562,-1078 2563,-1078 2563,-1079 2564,-1080 2564,-1080 2565,-1081 2565,-1082 2566,-1083 2566,-1084 2566,-1085 2566,-1086 2565,-1087 2565,-1088 2564,-1089 2564,-1089 2563,-1090 2563,-1091 2562,-1091 2561,-1092 2560,-1092 2559,-1092 2558,-1092 2557,-1092 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2557,-1062 2558,-1062 2559,-1062 2560,-1062 2561,-1062 2562,-1063 2563,-1063 2563,-1064 2564,-1065 2564,-1065 2565,-1066 2565,-1067 2566,-1068 2566,-1069 2566,-1070 2566,-1071 2565,-1072 2565,-1073 2564,-1074 2564,-1074 2563,-1075 2563,-1076 2562,-1076 2561,-1077 2560,-1077 2559,-1077 2558,-1077 2557,-1077 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2319,-1013 2320,-1013 2321,-1013 2322,-1013 2323,-1014 2323,-1014 2324,-1015 2325,-1015 2325,-1016 2326,-1017 2326,-1018 2327,-1019 2327,-1020 2327,-1021 2327,-1021 2327,-1022 2326,-1023 2326,-1024 2325,-1025 2325,-1026 2324,-1026 2323,-1027 2323,-1027 2322,-1028 2321,-1028 2320,-1028 2319,-1028 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2319,-998 2320,-998 2321,-998 2322,-998 2323,-999 2323,-999 2324,-1000 2325,-1000 2325,-1001 2326,-1002 2326,-1003 2327,-1004 2327,-1005 2327,-1006 2327,-1006 2327,-1007 2326,-1008 2326,-1009 2325,-1010 2325,-1011 2324,-1011 2323,-1012 2323,-1012 2322,-1013 2321,-1013 2320,-1013 2319,-1013 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2233,-1083 2234,-1083 2235,-1083 2236,-1084 2237,-1084 2237,-1084 2238,-1085 2239,-1086 2239,-1087 2240,-1087 2240,-1088 2240,-1089 2240,-1090 2240,-1091 2240,-1092 2240,-1093 2240,-1094 2239,-1095 2239,-1095 2238,-1096 2237,-1097 2237,-1097 2236,-1097 2235,-1098 2234,-1098 2233,-1098 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2233,-1068 2234,-1068 2235,-1068 2236,-1069 2237,-1069 2237,-1069 2238,-1070 2239,-1071 2239,-1072 2240,-1072 2240,-1073 2240,-1074 2240,-1075 2240,-1076 2240,-1077 2240,-1078 2240,-1079 2239,-1080 2239,-1080 2238,-1081 2237,-1082 2237,-1082 2236,-1082 2235,-1083 2234,-1083 2233,-1083 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2233,-1289 2234,-1289 2235,-1289 2236,-1289 2237,-1289 2238,-1290 2239,-1290 2239,-1291 2240,-1292 2240,-1292 2241,-1293 2241,-1294 2242,-1295 2242,-1296 2242,-1297 2242,-1298 2241,-1299 2241,-1300 2240,-1301 2240,-1301 2239,-1302 2239,-1303 2238,-1303 2237,-1304 2236,-1304 2235,-1304 2234,-1304 2233,-1304 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2233,-1274 2234,-1274 2235,-1274 2236,-1274 2237,-1274 2238,-1275 2239,-1275 2239,-1276 2240,-1277 2240,-1277 2241,-1278 2241,-1279 2242,-1280 2242,-1281 2242,-1282 2242,-1283 2241,-1284 2241,-1285 2240,-1286 2240,-1286 2239,-1287 2239,-1288 2238,-1288 2237,-1289 2236,-1289 2235,-1289 2234,-1289 2233,-1289 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2009,-755 2009,-754 2009,-753 2009,-752 2009,-751 2010,-750 2010,-750 2011,-749 2012,-748 2012,-748 2013,-747 2014,-747 2015,-746 2016,-746 2017,-746 2018,-746 2019,-747 2020,-747 2021,-748 2021,-748 2022,-749 2023,-750 2023,-750 2024,-751 2024,-752 2024,-753 2024,-754 2024,-755 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2024,-755 2024,-754 2024,-753 2024,-752 2024,-751 2025,-750 2025,-750 2026,-749 2027,-748 2027,-748 2028,-747 2029,-747 2030,-746 2031,-746 2032,-746 2033,-746 2034,-747 2035,-747 2036,-748 2036,-748 2037,-749 2038,-750 2038,-750 2039,-751 2039,-752 2039,-753 2039,-754 2039,-755 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="1714,-1125 1715,-1125 1716,-1126 1717,-1126 1717,-1126 1718,-1127 1719,-1128 1719,-1129 1720,-1129 1720,-1130 1720,-1131 1720,-1132 1720,-1133 1720,-1134 1720,-1135 1720,-1136 1719,-1136 1719,-1137 1718,-1138 1717,-1139 1717,-1139 1716,-1139 1715,-1140 1714,-1140 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1714,-1110 1715,-1110 1716,-1111 1717,-1111 1717,-1111 1718,-1112 1719,-1113 1719,-1114 1720,-1114 1720,-1115 1720,-1116 1720,-1117 1720,-1118 1720,-1119 1720,-1120 1720,-1121 1719,-1121 1719,-1122 1718,-1123 1717,-1124 1717,-1124 1716,-1124 1715,-1125 1714,-1125 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1497,-1619 1498,-1619 1499,-1619 1500,-1619 1501,-1619 1502,-1620 1503,-1620 1503,-1621 1504,-1622 1504,-1622 1505,-1623 1505,-1624 1506,-1625 1506,-1626 1506,-1627 1506,-1628 1505,-1629 1505,-1630 1504,-1631 1504,-1631 1503,-1632 1503,-1633 1502,-1633 1501,-1634 1500,-1634 1499,-1634 1498,-1634 1497,-1634 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="1497,-1604 1498,-1604 1499,-1604 1500,-1604 1501,-1604 1502,-1605 1503,-1605 1503,-1606 1504,-1607 1504,-1607 1505,-1608 1505,-1609 1506,-1610 1506,-1611 1506,-1612 1506,-1613 1505,-1614 1505,-1615 1504,-1616 1504,-1616 1503,-1617 1503,-1618 1502,-1618 1501,-1619 1500,-1619 1499,-1619 1498,-1619 1497,-1619 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="1497,-1736 1498,-1736 1499,-1736 1500,-1736 1501,-1736 1502,-1737 1503,-1737 1503,-1738 1504,-1739 1504,-1739 1505,-1740 1505,-1741 1506,-1742 1506,-1743 1506,-1744 1506,-1745 1505,-1746 1505,-1747 1504,-1748 1504,-1748 1503,-1749 1503,-1750 1502,-1750 1501,-1751 1500,-1751 1499,-1751 1498,-1751 1497,-1751 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="1497,-1721 1498,-1721 1499,-1721 1500,-1721 1501,-1721 1502,-1722 1503,-1722 1503,-1723 1504,-1724 1504,-1724 1505,-1725 1505,-1726 1506,-1727 1506,-1728 1506,-1729 1506,-1730 1505,-1731 1505,-1732 1504,-1733 1504,-1733 1503,-1734 1503,-1735 1502,-1735 1501,-1736 1500,-1736 1499,-1736 1498,-1736 1497,-1736 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="991,-712 992,-712 993,-712 994,-712 995,-712 996,-713 997,-713 997,-714 998,-715 998,-715 999,-716 999,-717 1000,-718 1000,-719 1000,-720 1000,-721 999,-722 999,-723 998,-724 998,-724 997,-725 997,-726 996,-726 995,-727 994,-727 993,-727 992,-727 991,-727 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="991,-697 992,-697 993,-697 994,-697 995,-697 996,-698 997,-698 997,-699 998,-700 998,-700 999,-701 999,-702 1000,-703 1000,-704 1000,-705 1000,-706 999,-707 999,-708 998,-709 998,-709 997,-710 997,-711 996,-711 995,-712 994,-712 993,-712 992,-712 991,-712 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="1994,-412 1994,-411 1994,-410 1994,-409 1995,-408 1995,-408 1996,-407 1996,-406 1997,-406 1998,-405 1999,-405 2000,-404 2001,-404 2002,-404 2002,-404 2003,-404 2004,-405 2005,-405 2006,-406 2007,-406 2007,-407 2008,-408 2008,-408 2009,-409 2009,-410 2009,-411 2009,-412 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2009,-412 2009,-411 2009,-410 2009,-409 2010,-408 2010,-408 2011,-407 2011,-406 2012,-406 2013,-405 2014,-405 2015,-404 2016,-404 2017,-404 2017,-404 2018,-404 2019,-405 2020,-405 2021,-406 2022,-406 2022,-407 2023,-408 2023,-408 2024,-409 2024,-410 2024,-411 2024,-412 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="833,-2629 834,-2629 835,-2629 836,-2629 837,-2630 837,-2630 838,-2631 839,-2631 839,-2632 840,-2633 840,-2634 841,-2635 841,-2636 841,-2637 841,-2637 841,-2638 840,-2639 840,-2640 839,-2641 839,-2642 838,-2642 837,-2643 837,-2643 836,-2644 835,-2644 834,-2644 833,-2644 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="833,-2614 834,-2614 835,-2614 836,-2614 837,-2615 837,-2615 838,-2616 839,-2616 839,-2617 840,-2618 840,-2619 841,-2620 841,-2621 841,-2622 841,-2622 841,-2623 840,-2624 840,-2625 839,-2626 839,-2627 838,-2627 837,-2628 837,-2628 836,-2629 835,-2629 834,-2629 833,-2629 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="911,-2374 911,-2373 912,-2372 912,-2371 912,-2371 913,-2370 914,-2369 915,-2369 915,-2368 916,-2368 917,-2368 918,-2368 919,-2368 920,-2368 921,-2368 922,-2368 922,-2369 923,-2369 924,-2370 925,-2371 925,-2371 925,-2372 926,-2373 926,-2374 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="926,-2374 926,-2373 927,-2372 927,-2371 927,-2371 928,-2370 929,-2369 930,-2369 930,-2368 931,-2368 932,-2368 933,-2368 934,-2368 935,-2368 936,-2368 937,-2368 937,-2369 938,-2369 939,-2370 940,-2371 940,-2371 940,-2372 941,-2373 941,-2374 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="899,-2053 899,-2052 899,-2051 899,-2050 900,-2049 900,-2049 901,-2048 901,-2047 902,-2047 903,-2046 904,-2046 905,-2045 906,-2045 907,-2045 907,-2045 908,-2045 909,-2046 910,-2046 911,-2047 912,-2047 912,-2048 913,-2049 913,-2049 914,-2050 914,-2051 914,-2052 914,-2053 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="914,-2053 914,-2052 914,-2051 914,-2050 915,-2049 915,-2049 916,-2048 916,-2047 917,-2047 918,-2046 919,-2046 920,-2045 921,-2045 922,-2045 922,-2045 923,-2045 924,-2046 925,-2046 926,-2047 927,-2047 927,-2048 928,-2049 928,-2049 929,-2050 929,-2051 929,-2052 929,-2053 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="833,-1768 834,-1768 835,-1768 836,-1768 837,-1769 837,-1769 838,-1770 839,-1770 839,-1771 840,-1772 840,-1773 841,-1774 841,-1775 841,-1776 841,-1776 841,-1777 840,-1778 840,-1779 839,-1780 839,-1781 838,-1781 837,-1782 837,-1782 836,-1783 835,-1783 834,-1783 833,-1783 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="833,-1753 834,-1753 835,-1753 836,-1753 837,-1754 837,-1754 838,-1755 839,-1755 839,-1756 840,-1757 840,-1758 841,-1759 841,-1760 841,-1761 841,-1761 841,-1762 840,-1763 840,-1764 839,-1765 839,-1766 838,-1766 837,-1767 837,-1767 836,-1768 835,-1768 834,-1768 833,-1768 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="515,-2231 515,-2230 515,-2229 515,-2228 515,-2227 516,-2226 516,-2226 517,-2225 518,-2224 518,-2224 519,-2223 520,-2223 521,-2222 522,-2222 523,-2222 524,-2222 525,-2223 526,-2223 527,-2224 527,-2224 528,-2225 529,-2226 529,-2226 530,-2227 530,-2228 530,-2229 530,-2230 530,-2231 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="530,-2231 530,-2230 530,-2229 530,-2228 530,-2227 531,-2226 531,-2226 532,-2225 533,-2224 533,-2224 534,-2223 535,-2223 536,-2222 537,-2222 538,-2222 539,-2222 540,-2223 541,-2223 542,-2224 542,-2224 543,-2225 544,-2226 544,-2226 545,-2227 545,-2228 545,-2229 545,-2230 545,-2231 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="449,-1930 450,-1930 451,-1930 452,-1931 453,-1931 453,-1931 454,-1932 455,-1933 455,-1934 456,-1934 456,-1935 456,-1936 456,-1937 456,-1938 456,-1939 456,-1940 456,-1941 455,-1942 455,-1942 454,-1943 453,-1944 453,-1944 452,-1944 451,-1945 450,-1945 449,-1945 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="449,-1914 450,-1914 451,-1914 452,-1914 453,-1914 454,-1915 455,-1915 455,-1916 456,-1917 456,-1917 457,-1918 457,-1919 458,-1920 458,-1921 458,-1922 458,-1923 457,-1924 457,-1925 456,-1926 456,-1926 455,-1927 455,-1928 454,-1928 453,-1929 452,-1929 451,-1929 450,-1929 449,-1929 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="508,-564 508,-563 508,-562 508,-561 509,-560 509,-560 510,-559 510,-558 511,-558 512,-557 513,-557 514,-556 515,-556 516,-556 516,-556 517,-556 518,-557 519,-557 520,-558 521,-558 521,-559 522,-560 522,-560 523,-561 523,-562 523,-563 523,-564 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="523,-564 523,-563 523,-562 523,-561 524,-560 524,-560 525,-559 525,-558 526,-558 527,-557 528,-557 529,-556 530,-556 531,-556 531,-556 532,-556 533,-557 534,-557 535,-558 536,-558 536,-559 537,-560 537,-560 538,-561 538,-562 538,-563 538,-564 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1723,-453 1724,-453 1725,-454 1726,-454 1726,-454 1727,-455 1728,-456 1728,-457 1729,-457 1729,-458 1729,-459 1729,-460 1729,-461 1729,-462 1729,-463 1729,-464 1728,-464 1728,-465 1727,-466 1726,-467 1726,-467 1725,-467 1724,-468 1723,-468 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1723,-438 1724,-438 1725,-439 1726,-439 1726,-439 1727,-440 1728,-441 1728,-442 1729,-442 1729,-443 1729,-444 1729,-445 1729,-446 1729,-447 1729,-448 1729,-449 1728,-449 1728,-450 1727,-451 1726,-452 1726,-452 1725,-452 1724,-453 1723,-453 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2699,-2093 2699,-2092 2699,-2091 2700,-2090 2700,-2089 2700,-2089 2701,-2088 2702,-2087 2703,-2087 2703,-2086 2704,-2086 2705,-2086 2706,-2086 2707,-2086 2708,-2086 2709,-2086 2710,-2086 2711,-2087 2711,-2087 2712,-2088 2713,-2089 2713,-2089 2713,-2090 2714,-2091 2714,-2092 2714,-2093 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2714,-2093 2714,-2092 2714,-2091 2715,-2090 2715,-2089 2715,-2089 2716,-2088 2717,-2087 2718,-2087 2718,-2086 2719,-2086 2720,-2086 2721,-2086 2722,-2086 2723,-2086 2724,-2086 2725,-2086 2726,-2087 2726,-2087 2727,-2088 2728,-2089 2728,-2089 2728,-2090 2729,-2091 2729,-2092 2729,-2093 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2637,-1955 2637,-1954 2638,-1953 2638,-1952 2638,-1952 2639,-1951 2640,-1950 2641,-1950 2641,-1949 2642,-1949 2643,-1949 2644,-1949 2645,-1949 2646,-1949 2647,-1949 2648,-1949 2648,-1950 2649,-1950 2650,-1951 2651,-1952 2651,-1952 2651,-1953 2652,-1954 2652,-1955 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2652,-1955 2652,-1954 2653,-1953 2653,-1952 2653,-1952 2654,-1951 2655,-1950 2656,-1950 2656,-1949 2657,-1949 2658,-1949 2659,-1949 2660,-1949 2661,-1949 2662,-1949 2663,-1949 2663,-1950 2664,-1950 2665,-1951 2666,-1952 2666,-1952 2666,-1953 2667,-1954 2667,-1955 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2641,-1651 2641,-1650 2641,-1649 2641,-1648 2641,-1647 2642,-1646 2642,-1646 2643,-1645 2644,-1644 2644,-1644 2645,-1643 2646,-1643 2647,-1642 2648,-1642 2649,-1642 2650,-1642 2651,-1643 2652,-1643 2653,-1644 2653,-1644 2654,-1645 2655,-1646 2655,-1646 2656,-1647 2656,-1648 2656,-1649 2656,-1650 2656,-1651 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2656,-1651 2656,-1650 2656,-1649 2656,-1648 2656,-1647 2657,-1646 2657,-1646 2658,-1645 2659,-1644 2659,-1644 2660,-1643 2661,-1643 2662,-1642 2663,-1642 2664,-1642 2665,-1642 2666,-1643 2667,-1643 2668,-1644 2668,-1644 2669,-1645 2670,-1646 2670,-1646 2671,-1647 2671,-1648 2671,-1649 2671,-1650 2671,-1651 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2696,-799 2696,-798 2696,-797 2697,-796 2697,-795 2697,-795 2698,-794 2699,-793 2700,-793 2700,-792 2701,-792 2702,-792 2703,-792 2704,-792 2705,-792 2706,-792 2707,-792 2708,-793 2708,-793 2709,-794 2710,-795 2710,-795 2710,-796 2711,-797 2711,-798 2711,-799 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2711,-799 2711,-798 2711,-797 2712,-796 2712,-795 2712,-795 2713,-794 2714,-793 2715,-793 2715,-792 2716,-792 2717,-792 2718,-792 2719,-792 2720,-792 2721,-792 2722,-792 2723,-793 2723,-793 2724,-794 2725,-795 2725,-795 2725,-796 2726,-797 2726,-798 2726,-799 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2696,-692 2696,-691 2696,-690 2697,-689 2697,-688 2697,-688 2698,-687 2699,-686 2700,-686 2700,-685 2701,-685 2702,-685 2703,-685 2704,-685 2705,-685 2706,-685 2707,-685 2708,-686 2708,-686 2709,-687 2710,-688 2710,-688 2710,-689 2711,-690 2711,-691 2711,-692 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2711,-692 2711,-691 2711,-690 2712,-689 2712,-688 2712,-688 2713,-687 2714,-686 2715,-686 2715,-685 2716,-685 2717,-685 2718,-685 2719,-685 2720,-685 2721,-685 2722,-685 2723,-686 2723,-686 2724,-687 2725,-688 2725,-688 2725,-689 2726,-690 2726,-691 2726,-692 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2181,-1726 2181,-1725 2181,-1724 2182,-1723 2182,-1722 2182,-1722 2183,-1721 2184,-1720 2185,-1720 2185,-1719 2186,-1719 2187,-1719 2188,-1719 2189,-1719 2190,-1719 2191,-1719 2192,-1719 2193,-1720 2193,-1720 2194,-1721 2195,-1722 2195,-1722 2195,-1723 2196,-1724 2196,-1725 2196,-1726 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2196,-1726 2196,-1725 2196,-1724 2197,-1723 2197,-1722 2197,-1722 2198,-1721 2199,-1720 2200,-1720 2200,-1719 2201,-1719 2202,-1719 2203,-1719 2204,-1719 2205,-1719 2206,-1719 2207,-1719 2208,-1720 2208,-1720 2209,-1721 2210,-1722 2210,-1722 2210,-1723 2211,-1724 2211,-1725 2211,-1726 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="1032,-2053 1032,-2052 1033,-2051 1033,-2050 1033,-2050 1034,-2049 1035,-2048 1036,-2048 1036,-2047 1037,-2047 1038,-2047 1039,-2047 1040,-2047 1041,-2047 1042,-2047 1043,-2047 1043,-2048 1044,-2048 1045,-2049 1046,-2050 1046,-2050 1046,-2051 1047,-2052 1047,-2053 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1047,-2053 1047,-2052 1048,-2051 1048,-2050 1048,-2050 1049,-2049 1050,-2048 1051,-2048 1051,-2047 1052,-2047 1053,-2047 1054,-2047 1055,-2047 1056,-2047 1057,-2047 1058,-2047 1058,-2048 1059,-2048 1060,-2049 1061,-2050 1061,-2050 1061,-2051 1062,-2052 1062,-2053 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="407,-1796 407,-1795 407,-1794 407,-1793 407,-1792 408,-1791 408,-1791 409,-1790 410,-1789 410,-1789 411,-1788 412,-1788 413,-1787 414,-1787 415,-1787 416,-1787 417,-1788 418,-1788 419,-1789 419,-1789 420,-1790 421,-1791 421,-1791 422,-1792 422,-1793 422,-1794 422,-1795 422,-1796 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="422,-1796 422,-1795 422,-1794 422,-1793 422,-1792 423,-1791 423,-1791 424,-1790 425,-1789 425,-1789 426,-1788 427,-1788 428,-1787 429,-1787 430,-1787 431,-1787 432,-1788 433,-1788 434,-1789 434,-1789 435,-1790 436,-1791 436,-1791 437,-1792 437,-1793 437,-1794 437,-1795 437,-1796 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="404,-1570 404,-1569 404,-1568 404,-1567 405,-1566 405,-1566 406,-1565 406,-1564 407,-1564 408,-1563 409,-1563 410,-1562 411,-1562 412,-1562 412,-1562 413,-1562 414,-1563 415,-1563 416,-1564 417,-1564 417,-1565 418,-1566 418,-1566 419,-1567 419,-1568 419,-1569 419,-1570 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="419,-1570 419,-1569 419,-1568 419,-1567 420,-1566 420,-1566 421,-1565 421,-1564 422,-1564 423,-1563 424,-1563 425,-1562 426,-1562 427,-1562 427,-1562 428,-1562 429,-1563 430,-1563 431,-1564 432,-1564 432,-1565 433,-1566 433,-1566 434,-1567 434,-1568 434,-1569 434,-1570 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="412,-1235 412,-1234 413,-1233 413,-1232 413,-1232 414,-1231 415,-1230 416,-1230 416,-1229 417,-1229 418,-1229 419,-1229 420,-1229 421,-1229 422,-1229 423,-1229 423,-1230 424,-1230 425,-1231 426,-1232 426,-1232 426,-1233 427,-1234 427,-1235 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="427,-1235 427,-1234 428,-1233 428,-1232 428,-1232 429,-1231 430,-1230 431,-1230 431,-1229 432,-1229 433,-1229 434,-1229 435,-1229 436,-1229 437,-1229 438,-1229 438,-1230 439,-1230 440,-1231 441,-1232 441,-1232 441,-1233 442,-1234 442,-1235 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="950,-958 950,-957 950,-956 950,-955 951,-954 951,-954 952,-953 952,-952 953,-952 954,-951 955,-951 956,-950 957,-950 958,-950 958,-950 959,-950 960,-951 961,-951 962,-952 963,-952 963,-953 964,-954 964,-954 965,-955 965,-956 965,-957 965,-958 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="965,-958 965,-957 965,-956 965,-955 966,-954 966,-954 967,-953 967,-952 968,-952 969,-951 970,-951 971,-950 972,-950 973,-950 973,-950 974,-950 975,-951 976,-951 977,-952 978,-952 978,-953 979,-954 979,-954 980,-955 980,-956 980,-957 980,-958 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="953,-861 953,-860 953,-859 953,-858 954,-857 954,-857 955,-856 955,-855 956,-855 957,-854 958,-854 959,-853 960,-853 961,-853 961,-853 962,-853 963,-854 964,-854 965,-855 966,-855 966,-856 967,-857 967,-857 968,-858 968,-859 968,-860 968,-861 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="968,-861 968,-860 968,-859 968,-858 969,-857 969,-857 970,-856 970,-855 971,-855 972,-854 973,-854 974,-853 975,-853 976,-853 976,-853 977,-853 978,-854 979,-854 980,-855 981,-855 981,-856 982,-857 982,-857 983,-858 983,-859 983,-860 983,-861 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="3844,-1832 3845,-1832 3846,-1832 3847,-1832 3848,-1832 3849,-1833 3850,-1833 3850,-1834 3851,-1835 3851,-1835 3852,-1836 3852,-1837 3853,-1838 3853,-1839 3853,-1840 3853,-1841 3852,-1842 3852,-1843 3851,-1844 3851,-1844 3850,-1845 3850,-1846 3849,-1846 3848,-1847 3847,-1847 3846,-1847 3845,-1847 3844,-1847 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3844,-1817 3845,-1817 3846,-1817 3847,-1817 3848,-1817 3849,-1818 3850,-1818 3850,-1819 3851,-1820 3851,-1820 3852,-1821 3852,-1822 3853,-1823 3853,-1824 3853,-1825 3853,-1826 3852,-1827 3852,-1828 3851,-1829 3851,-1829 3850,-1830 3850,-1831 3849,-1831 3848,-1832 3847,-1832 3846,-1832 3845,-1832 3844,-1832 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3576,-1833 3577,-1833 3578,-1833 3579,-1834 3580,-1834 3580,-1834 3581,-1835 3582,-1836 3582,-1837 3583,-1837 3583,-1838 3583,-1839 3583,-1840 3583,-1841 3583,-1842 3583,-1843 3583,-1844 3582,-1845 3582,-1845 3581,-1846 3580,-1847 3580,-1847 3579,-1847 3578,-1848 3577,-1848 3576,-1848 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3576,-1818 3577,-1818 3578,-1818 3579,-1819 3580,-1819 3580,-1819 3581,-1820 3582,-1821 3582,-1822 3583,-1822 3583,-1823 3583,-1824 3583,-1825 3583,-1826 3583,-1827 3583,-1828 3583,-1829 3582,-1830 3582,-1830 3581,-1831 3580,-1832 3580,-1832 3579,-1832 3578,-1833 3577,-1833 3576,-1833 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3429,-1792 3430,-1792 3431,-1792 3432,-1792 3433,-1792 3434,-1793 3435,-1793 3435,-1794 3436,-1795 3436,-1795 3437,-1796 3437,-1797 3438,-1798 3438,-1799 3438,-1800 3438,-1801 3437,-1802 3437,-1803 3436,-1804 3436,-1804 3435,-1805 3435,-1806 3434,-1806 3433,-1807 3432,-1807 3431,-1807 3430,-1807 3429,-1807 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3429,-1777 3430,-1777 3431,-1777 3432,-1777 3433,-1777 3434,-1778 3435,-1778 3435,-1779 3436,-1780 3436,-1780 3437,-1781 3437,-1782 3438,-1783 3438,-1784 3438,-1785 3438,-1786 3437,-1787 3437,-1788 3436,-1789 3436,-1789 3435,-1790 3435,-1791 3434,-1791 3433,-1792 3432,-1792 3431,-1792 3430,-1792 3429,-1792 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3225,-1789 3226,-1789 3227,-1789 3228,-1789 3229,-1790 3229,-1790 3230,-1791 3231,-1791 3231,-1792 3232,-1793 3232,-1794 3233,-1795 3233,-1796 3233,-1797 3233,-1797 3233,-1798 3232,-1799 3232,-1800 3231,-1801 3231,-1802 3230,-1802 3229,-1803 3229,-1803 3228,-1804 3227,-1804 3226,-1804 3225,-1804 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="3225,-1774 3226,-1774 3227,-1774 3228,-1774 3229,-1775 3229,-1775 3230,-1776 3231,-1776 3231,-1777 3232,-1778 3232,-1779 3233,-1780 3233,-1781 3233,-1782 3233,-1782 3233,-1783 3232,-1784 3232,-1785 3231,-1786 3231,-1787 3230,-1787 3229,-1788 3229,-1788 3228,-1789 3227,-1789 3226,-1789 3225,-1789 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="3309,-1353 3310,-1353 3311,-1353 3312,-1354 3313,-1354 3313,-1354 3314,-1355 3315,-1356 3315,-1357 3316,-1357 3316,-1358 3316,-1359 3316,-1360 3316,-1361 3316,-1362 3316,-1363 3316,-1364 3315,-1365 3315,-1365 3314,-1366 3313,-1367 3313,-1367 3312,-1367 3311,-1368 3310,-1368 3309,-1368 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3309,-1338 3310,-1338 3311,-1338 3312,-1339 3313,-1339 3313,-1339 3314,-1340 3315,-1341 3315,-1342 3316,-1342 3316,-1343 3316,-1344 3316,-1345 3316,-1346 3316,-1347 3316,-1348 3316,-1349 3315,-1350 3315,-1350 3314,-1351 3313,-1352 3313,-1352 3312,-1352 3311,-1353 3310,-1353 3309,-1353 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2936,-1027 2937,-1027 2938,-1028 2939,-1028 2939,-1028 2940,-1029 2941,-1030 2941,-1031 2942,-1031 2942,-1032 2942,-1033 2942,-1034 2942,-1035 2942,-1036 2942,-1037 2942,-1038 2941,-1038 2941,-1039 2940,-1040 2939,-1041 2939,-1041 2938,-1041 2937,-1042 2936,-1042 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2936,-1012 2937,-1012 2938,-1013 2939,-1013 2939,-1013 2940,-1014 2941,-1015 2941,-1016 2942,-1016 2942,-1017 2942,-1018 2942,-1019 2942,-1020 2942,-1021 2942,-1022 2942,-1023 2941,-1023 2941,-1024 2940,-1025 2939,-1026 2939,-1026 2938,-1026 2937,-1027 2936,-1027 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2443,-1025 2444,-1025 2445,-1025 2446,-1025 2447,-1026 2447,-1026 2448,-1027 2449,-1027 2449,-1028 2450,-1029 2450,-1030 2451,-1031 2451,-1032 2451,-1033 2451,-1033 2451,-1034 2450,-1035 2450,-1036 2449,-1037 2449,-1038 2448,-1038 2447,-1039 2447,-1039 2446,-1040 2445,-1040 2444,-1040 2443,-1040 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2443,-1010 2444,-1010 2445,-1010 2446,-1010 2447,-1011 2447,-1011 2448,-1012 2449,-1012 2449,-1013 2450,-1014 2450,-1015 2451,-1016 2451,-1017 2451,-1018 2451,-1018 2451,-1019 2450,-1020 2450,-1021 2449,-1022 2449,-1023 2448,-1023 2447,-1024 2447,-1024 2446,-1025 2445,-1025 2444,-1025 2443,-1025 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2362,-390 2363,-390 2364,-391 2365,-391 2365,-391 2366,-392 2367,-393 2367,-394 2368,-394 2368,-395 2368,-396 2368,-397 2368,-398 2368,-399 2368,-400 2368,-401 2367,-401 2367,-402 2366,-403 2365,-404 2365,-404 2364,-404 2363,-405 2362,-405 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2362,-375 2363,-375 2364,-376 2365,-376 2365,-376 2366,-377 2367,-378 2367,-379 2368,-379 2368,-380 2368,-381 2368,-382 2368,-383 2368,-384 2368,-385 2368,-386 2367,-386 2367,-387 2366,-388 2365,-389 2365,-389 2364,-389 2363,-390 2362,-390 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="450,-872 451,-872 452,-873 453,-873 453,-873 454,-874 455,-875 455,-876 456,-876 456,-877 456,-878 456,-879 456,-880 456,-881 456,-882 456,-883 455,-883 455,-884 454,-885 453,-886 453,-886 452,-886 451,-887 450,-887 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="450,-887 451,-887 452,-888 453,-888 453,-888 454,-889 455,-890 455,-891 456,-891 456,-892 456,-893 456,-894 456,-895 456,-896 456,-897 456,-898 455,-898 455,-899 454,-900 453,-901 453,-901 452,-901 451,-902 450,-902 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="450,-674 451,-674 452,-674 453,-674 454,-675 454,-675 455,-676 456,-676 456,-677 457,-678 457,-679 458,-680 458,-681 458,-682 458,-682 458,-683 457,-684 457,-685 456,-686 456,-687 455,-687 454,-688 454,-688 453,-689 452,-689 451,-689 450,-689 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="450,-689 451,-689 452,-689 453,-689 454,-690 454,-690 455,-691 456,-691 456,-692 457,-693 457,-694 458,-695 458,-696 458,-697 458,-697 458,-698 457,-699 457,-700 456,-701 456,-702 455,-702 454,-703 454,-703 453,-704 452,-704 451,-704 450,-704 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1323,-1085 1324,-1085 1325,-1086 1326,-1086 1326,-1086 1327,-1087 1328,-1088 1328,-1089 1329,-1089 1329,-1090 1329,-1091 1329,-1092 1329,-1093 1329,-1094 1329,-1095 1329,-1096 1328,-1096 1328,-1097 1327,-1098 1326,-1099 1326,-1099 1325,-1099 1324,-1100 1323,-1100 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1323,-1070 1324,-1070 1325,-1071 1326,-1071 1326,-1071 1327,-1072 1328,-1073 1328,-1074 1329,-1074 1329,-1075 1329,-1076 1329,-1077 1329,-1078 1329,-1079 1329,-1080 1329,-1081 1328,-1081 1328,-1082 1327,-1083 1326,-1084 1326,-1084 1325,-1084 1324,-1085 1323,-1085 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1585,-1079 1586,-1079 1587,-1080 1588,-1080 1588,-1080 1589,-1081 1590,-1082 1590,-1083 1591,-1083 1591,-1084 1591,-1085 1591,-1086 1591,-1087 1591,-1088 1591,-1089 1591,-1090 1590,-1090 1590,-1091 1589,-1092 1588,-1093 1588,-1093 1587,-1093 1586,-1094 1585,-1094 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1585,-1064 1586,-1064 1587,-1065 1588,-1065 1588,-1065 1589,-1066 1590,-1067 1590,-1068 1591,-1068 1591,-1069 1591,-1070 1591,-1071 1591,-1072 1591,-1073 1591,-1074 1591,-1075 1590,-1075 1590,-1076 1589,-1077 1588,-1078 1588,-1078 1587,-1078 1586,-1079 1585,-1079 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1794,-1072 1795,-1072 1796,-1072 1797,-1072 1798,-1072 1799,-1073 1800,-1073 1800,-1074 1801,-1075 1801,-1075 1802,-1076 1802,-1077 1803,-1078 1803,-1079 1803,-1080 1803,-1081 1802,-1082 1802,-1083 1801,-1084 1801,-1084 1800,-1085 1800,-1086 1799,-1086 1798,-1087 1797,-1087 1796,-1087 1795,-1087 1794,-1087 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="1794,-1057 1795,-1057 1796,-1057 1797,-1057 1798,-1057 1799,-1058 1800,-1058 1800,-1059 1801,-1060 1801,-1060 1802,-1061 1802,-1062 1803,-1063 1803,-1064 1803,-1065 1803,-1066 1802,-1067 1802,-1068 1801,-1069 1801,-1069 1800,-1070 1800,-1071 1799,-1071 1798,-1072 1797,-1072 1796,-1072 1795,-1072 1794,-1072 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2174,-388 2175,-388 2176,-388 2177,-388 2178,-389 2178,-389 2179,-390 2180,-390 2180,-391 2181,-392 2181,-393 2182,-394 2182,-395 2182,-396 2182,-396 2182,-397 2181,-398 2181,-399 2180,-400 2180,-401 2179,-401 2178,-402 2178,-402 2177,-403 2176,-403 2175,-403 2174,-403 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2174,-373 2175,-373 2176,-373 2177,-373 2178,-374 2178,-374 2179,-375 2180,-375 2180,-376 2181,-377 2181,-378 2182,-379 2182,-380 2182,-381 2182,-381 2182,-382 2181,-383 2181,-384 2180,-385 2180,-386 2179,-386 2178,-387 2178,-387 2177,-388 2176,-388 2175,-388 2174,-388 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="3349,-2210 3349,-2209 3349,-2208 3349,-2207 3349,-2206 3350,-2205 3350,-2205 3351,-2204 3352,-2203 3352,-2203 3353,-2202 3354,-2202 3355,-2201 3356,-2201 3357,-2201 3358,-2201 3359,-2202 3360,-2202 3361,-2203 3361,-2203 3362,-2204 3363,-2205 3363,-2205 3364,-2206 3364,-2207 3364,-2208 3364,-2209 3364,-2210 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3364,-2210 3364,-2209 3364,-2208 3364,-2207 3364,-2206 3365,-2205 3365,-2205 3366,-2204 3367,-2203 3367,-2203 3368,-2202 3369,-2202 3370,-2201 3371,-2201 3372,-2201 3373,-2201 3374,-2202 3375,-2202 3376,-2203 3376,-2203 3377,-2204 3378,-2205 3378,-2205 3379,-2206 3379,-2207 3379,-2208 3379,-2209 3379,-2210 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="634,-2197 635,-2197 636,-2198 637,-2198 637,-2198 638,-2199 639,-2200 639,-2201 640,-2201 640,-2202 640,-2203 640,-2204 640,-2205 640,-2206 640,-2207 640,-2208 639,-2208 639,-2209 638,-2210 637,-2211 637,-2211 636,-2211 635,-2212 634,-2212 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="634,-2182 635,-2182 636,-2183 637,-2183 637,-2183 638,-2184 639,-2185 639,-2186 640,-2186 640,-2187 640,-2188 640,-2189 640,-2190 640,-2191 640,-2192 640,-2193 639,-2193 639,-2194 638,-2195 637,-2196 637,-2196 636,-2196 635,-2197 634,-2197 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1242,-2372 1242,-2371 1242,-2370 1242,-2369 1243,-2368 1243,-2368 1244,-2367 1244,-2366 1245,-2366 1246,-2365 1247,-2365 1248,-2364 1249,-2364 1250,-2364 1250,-2364 1251,-2364 1252,-2365 1253,-2365 1254,-2366 1255,-2366 1255,-2367 1256,-2368 1256,-2368 1257,-2369 1257,-2370 1257,-2371 1257,-2372 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="1257,-2372 1257,-2371 1257,-2370 1257,-2369 1258,-2368 1258,-2368 1259,-2367 1259,-2366 1260,-2366 1261,-2365 1262,-2365 1263,-2364 1264,-2364 1265,-2364 1265,-2364 1266,-2364 1267,-2365 1268,-2365 1269,-2366 1270,-2366 1270,-2367 1271,-2368 1271,-2368 1272,-2369 1272,-2370 1272,-2371 1272,-2372 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="855,-1361 855,-1360 855,-1359 855,-1358 855,-1357 856,-1356 856,-1356 857,-1355 858,-1354 858,-1354 859,-1353 860,-1353 861,-1352 862,-1352 863,-1352 864,-1352 865,-1353 866,-1353 867,-1354 867,-1354 868,-1355 869,-1356 869,-1356 870,-1357 870,-1358 870,-1359 870,-1360 870,-1361 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="870,-1361 870,-1360 870,-1359 870,-1358 870,-1357 871,-1356 871,-1356 872,-1355 873,-1354 873,-1354 874,-1353 875,-1353 876,-1352 877,-1352 878,-1352 879,-1352 880,-1353 881,-1353 882,-1354 882,-1354 883,-1355 884,-1356 884,-1356 885,-1357 885,-1358 885,-1359 885,-1360 885,-1361 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="2698,-2182 2698,-2181 2698,-2180 2698,-2179 2699,-2178 2699,-2178 2700,-2177 2700,-2176 2701,-2176 2702,-2175 2703,-2175 2704,-2174 2705,-2174 2706,-2174 2706,-2174 2707,-2174 2708,-2175 2709,-2175 2710,-2176 2711,-2176 2711,-2177 2712,-2178 2712,-2178 2713,-2179 2713,-2180 2713,-2181 2713,-2182 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2713,-2182 2713,-2181 2713,-2180 2713,-2179 2714,-2178 2714,-2178 2715,-2177 2715,-2176 2716,-2176 2717,-2175 2718,-2175 2719,-2174 2720,-2174 2721,-2174 2721,-2174 2722,-2174 2723,-2175 2724,-2175 2725,-2176 2726,-2176 2726,-2177 2727,-2178 2727,-2178 2728,-2179 2728,-2180 2728,-2181 2728,-2182 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="3330,-2158 3331,-2158 3332,-2158 3333,-2158 3334,-2158 3335,-2159 3336,-2159 3336,-2160 3337,-2161 3337,-2161 3338,-2162 3338,-2163 3339,-2164 3339,-2165 3339,-2166 3339,-2167 3338,-2168 3338,-2169 3337,-2170 3337,-2170 3336,-2171 3336,-2172 3335,-2172 3334,-2173 3333,-2173 3332,-2173 3331,-2173 3330,-2173 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3330,-2143 3331,-2143 3332,-2143 3333,-2143 3334,-2143 3335,-2144 3336,-2144 3336,-2145 3337,-2146 3337,-2146 3338,-2147 3338,-2148 3339,-2149 3339,-2150 3339,-2151 3339,-2152 3338,-2153 3338,-2154 3337,-2155 3337,-2155 3336,-2156 3336,-2157 3335,-2157 3334,-2158 3333,-2158 3332,-2158 3331,-2158 3330,-2158 " stroke="rgb(255,255,255)" stroke-width="0.025"/>
   <polyline DF8003:Layer="0" fill="none" points="3699,-1172 3700,-1172 3701,-1173 3702,-1173 3702,-1173 3703,-1174 3704,-1175 3704,-1176 3705,-1176 3705,-1177 3705,-1178 3705,-1179 3705,-1180 3705,-1181 3705,-1182 3705,-1183 3704,-1183 3704,-1184 3703,-1185 3702,-1186 3702,-1186 3701,-1186 3700,-1187 3699,-1187 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="3699,-1157 3700,-1157 3701,-1158 3702,-1158 3702,-1158 3703,-1159 3704,-1160 3704,-1161 3705,-1161 3705,-1162 3705,-1163 3705,-1164 3705,-1165 3705,-1166 3705,-1167 3705,-1168 3704,-1168 3704,-1169 3703,-1170 3702,-1171 3702,-1171 3701,-1171 3700,-1172 3699,-1172 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="3330,-1281 3330,-1280 3330,-1279 3330,-1278 3331,-1277 3331,-1277 3332,-1276 3332,-1275 3333,-1275 3334,-1274 3335,-1274 3336,-1273 3337,-1273 3338,-1273 3338,-1273 3339,-1273 3340,-1274 3341,-1274 3342,-1275 3343,-1275 3343,-1276 3344,-1277 3344,-1277 3345,-1278 3345,-1279 3345,-1280 3345,-1281 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="3345,-1281 3345,-1280 3345,-1279 3345,-1278 3346,-1277 3346,-1277 3347,-1276 3347,-1275 3348,-1275 3349,-1274 3350,-1274 3351,-1273 3352,-1273 3353,-1273 3353,-1273 3354,-1273 3355,-1274 3356,-1274 3357,-1275 3358,-1275 3358,-1276 3359,-1277 3359,-1277 3360,-1278 3360,-1279 3360,-1280 3360,-1281 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2774,-2447 2774,-2446 2774,-2445 2774,-2444 2775,-2443 2775,-2443 2776,-2442 2776,-2441 2777,-2441 2778,-2440 2779,-2440 2780,-2439 2781,-2439 2782,-2439 2782,-2439 2783,-2439 2784,-2440 2785,-2440 2786,-2441 2787,-2441 2787,-2442 2788,-2443 2788,-2443 2789,-2444 2789,-2445 2789,-2446 2789,-2447 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2789,-2447 2789,-2446 2789,-2445 2789,-2444 2790,-2443 2790,-2443 2791,-2442 2791,-2441 2792,-2441 2793,-2440 2794,-2440 2795,-2439 2796,-2439 2797,-2439 2797,-2439 2798,-2439 2799,-2440 2800,-2440 2801,-2441 2802,-2441 2802,-2442 2803,-2443 2803,-2443 2804,-2444 2804,-2445 2804,-2446 2804,-2447 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2443,-1070 2444,-1070 2445,-1071 2446,-1071 2446,-1071 2447,-1072 2448,-1073 2448,-1074 2449,-1074 2449,-1075 2449,-1076 2449,-1077 2449,-1078 2449,-1079 2449,-1080 2449,-1081 2448,-1081 2448,-1082 2447,-1083 2446,-1084 2446,-1084 2445,-1084 2444,-1085 2443,-1085 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2443,-1055 2444,-1055 2445,-1056 2446,-1056 2446,-1056 2447,-1057 2448,-1058 2448,-1059 2449,-1059 2449,-1060 2449,-1061 2449,-1062 2449,-1063 2449,-1064 2449,-1065 2449,-1066 2448,-1066 2448,-1067 2447,-1068 2446,-1069 2446,-1069 2445,-1069 2444,-1070 2443,-1070 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2706,-2254 2706,-2253 2707,-2252 2707,-2251 2707,-2251 2708,-2250 2709,-2249 2710,-2249 2710,-2248 2711,-2248 2712,-2248 2713,-2248 2714,-2248 2715,-2248 2716,-2248 2717,-2248 2717,-2249 2718,-2249 2719,-2250 2720,-2251 2720,-2251 2720,-2252 2721,-2253 2721,-2254 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="0" fill="none" points="2721,-2254 2721,-2253 2722,-2252 2722,-2251 2722,-2251 2723,-2250 2724,-2249 2725,-2249 2725,-2248 2726,-2248 2727,-2248 2728,-2248 2729,-2248 2730,-2248 2731,-2248 2732,-2248 2732,-2249 2733,-2249 2734,-2250 2735,-2251 2735,-2251 2735,-2252 2736,-2253 2736,-2254 " stroke="rgb(255,255,255)" stroke-width="0.02375"/>
   <polyline DF8003:Layer="支线" fill="none" points="3429,-26 3429,-25 3429,-25 3429,-24 3430,-24 3430,-23 3430,-23 3431,-22 3431,-22 3432,-21 3432,-21 3433,-21 3434,-21 3434,-21 3435,-21 3436,-21 3436,-21 3437,-22 3437,-22 3438,-23 3438,-23 3438,-24 3439,-24 3439,-25 3439,-25 3439,-26 " stroke="rgb(255,255,255)" stroke-width="0.01625"/>
   <polyline DF8003:Layer="支线" fill="none" points="3419,-26 3419,-25 3419,-25 3419,-24 3420,-24 3420,-23 3420,-23 3421,-22 3421,-22 3422,-21 3422,-21 3423,-21 3424,-21 3424,-21 3425,-21 3426,-21 3426,-21 3427,-22 3427,-22 3428,-23 3428,-23 3428,-24 3429,-24 3429,-25 3429,-25 3429,-26 " stroke="rgb(255,255,255)" stroke-width="0.01625"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 2359.000000 -2845.000000) translate(0,40)">新村变电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2708.000000 -2659.000000) translate(0,20)">064</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2705.000000 -2608.000000) translate(0,20)">0646</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2604.000000 -2721.000000) translate(0,20)">10kV大</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2604.000000 -2721.000000) translate(0,45)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2604.000000 -2721.000000) translate(0,70)">基</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2604.000000 -2721.000000) translate(0,95)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2705.000000 -2707.000000) translate(0,20)">0641</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -268.000000) translate(0,20)">071</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2711.000000 -217.000000) translate(0,20)">0711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2630.000000 -334.000000) translate(0,20)">大</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2630.000000 -334.000000) translate(0,45)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2630.000000 -334.000000) translate(0,70)">基</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2630.000000 -334.000000) translate(0,95)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2711.000000 -316.000000) translate(0,20)">0716</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 1975.000000 -102.000000) translate(0,40)">马龙河电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1984.000000 -259.000000) translate(0,20)">072</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1982.000000 -207.000000) translate(0,20)">0721</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1893.000000 -297.000000) translate(0,20)">马</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1893.000000 -297.000000) translate(0,45)">宜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1893.000000 -297.000000) translate(0,70)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2565.000000 -2400.000000) translate(0,20)">L0411</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -2365.000000) translate(0,20)">F0431</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2813.000000 -2307.000000) translate(0,12)">我地苴</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2655.000000 -2192.000000) translate(0,12)">17</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2892.000000 -2196.000000) translate(0,12)">新村下村变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2735.000000 -2235.000000) translate(0,12)">10kV下村支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2772.000000 -2170.000000) translate(0,12)">Z041</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2653.000000 -2107.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2799.000000 -2116.000000) translate(0,12)">打油盆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2834.000000 -2034.000000) translate(0,12)">箐边</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2654.000000 -2037.000000) translate(0,12)">         23</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2523.000000 -1980.000000) translate(0,12)">姚家</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2693.000000 -1966.000000) translate(0,12)">26</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2658.000000 -1821.000000) translate(0,12)">28</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2697.000000 -1794.000000) translate(0,10)">Z0421</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2746.000000 -1794.000000) translate(0,10)">Z042</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2746.000000 -1851.000000) translate(0,12)">10kV  水   卷   槽   支   线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2799.000000 -1689.000000) translate(0,12)">水卷槽村委会</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2839.000000 -1832.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3197.000000 -1698.000000) translate(0,12)">高建塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3216.000000 -1832.000000) translate(0,12)">17</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3407.000000 -1695.000000) translate(0,12)">锅底塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3421.000000 -1832.000000) translate(0,12)">26</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1694.000000) translate(0,12)">回龙村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3689.000000 -1832.000000) translate(0,12)">38</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3130.000000 -1807.000000) translate(0,12)">14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3321.000000 -1807.000000) translate(0,12)">23</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3570.000000 -1802.000000) translate(0,12)">33</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3836.000000 -1802.000000) translate(0,12)">39</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3149.000000 -1910.000000) translate(0,10)">BZ044</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -2120.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3000.000000 -2091.000000) translate(0,12)">上村领岗</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3099.000000 -2331.000000) translate(0,12)">中村平地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3340.000000 -1910.000000) translate(0,10)">BZ045</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3337.000000 -2120.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3192.000000 -2091.000000) translate(0,12)">干坝田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3546.000000 -1993.000000) translate(0,12)">大田口</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3813.000000 -1994.000000) translate(0,12)">小百么</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3915.000000 -1764.000000) translate(0,10)">BZ043</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3871.000000 -1765.000000) translate(0,12)">40</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3913.000000 -1160.000000) translate(0,12)">45</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3879.000000 -978.000000) translate(0,12)">51</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3915.000000 -765.000000) translate(0,12)">59</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3781.000000 -1140.000000) translate(0,10)">BZ046</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3694.000000 -1145.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3545.000000 -1171.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3670.000000 -1456.000000) translate(0,12)">土掌房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3643.000000 -1117.000000) translate(0,12)">10kV大龙潭分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3526.000000 -844.000000) translate(0,12)">大领岗</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3394.000000 -1130.000000) translate(0,12)">大龙潭</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3533.000000 -995.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3728.000000 -1011.000000) translate(0,12)">兜底村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3566.000000 -971.000000) translate(0,12)">10kV兜底村分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3611.000000 -1003.000000) translate(0,10)">Z0464</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3882.000000 -577.000000) translate(0,12)">新地基</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3960.000000 -949.000000) translate(0,12)">大白么</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3834.000000 -778.000000) translate(0,10)">Z0465</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3551.000000 -1130.000000) translate(0,10)">Z0463</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3639.000000 -759.000000) translate(0,12)">大麻栗树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2693.000000 -1660.000000) translate(0,12)">32</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2560.000000 -1630.000000) translate(0,12)">大亮山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2653.000000 -1481.000000) translate(0,12)">39</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2719.000000 -1462.000000) translate(0,10)">BZ047</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2885.000000 -1313.000000) translate(0,12)">背里树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3261.000000 -1589.000000) translate(0,12)">小姐娥#1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2909.000000 -1466.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3228.000000 -1466.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3435.000000 -1416.000000) translate(0,12)">小龙箐#1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3275.000000 -1044.000000) translate(0,12)">大姐峨</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3435.000000 -1291.000000) translate(0,12)">大地基大平掌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3130.000000 -1141.000000) translate(0,12)">羊厩房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2863.000000 -1515.000000) translate(0,12)">10kV大姐峨分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2700.000000 -877.000000) translate(0,20)">F0461</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2660.000000 -863.000000) translate(0,12)">59</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2636.000000 -1027.000000) translate(0,10)">Z0491</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2587.000000 -1030.000000) translate(0,10)">Z049</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2659.000000 -1071.000000) translate(0,12)">58</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2854.000000 -1216.000000) translate(0,12)">集镇1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2893.000000 -930.000000) translate(0,12)">集镇2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2728.000000 -1013.000000) translate(0,12)">10kV林产品公司支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2809.000000 -809.000000) translate(0,12)">大麦地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2660.000000 -805.000000) translate(0,12)">62</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2809.000000 -702.000000) translate(0,12)">旧村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2661.000000 -699.000000) translate(0,12)">63</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2930.000000 -597.000000) translate(0,12)">大母几苴</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2657.000000 -577.000000) translate(0,12)">71</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2746.000000 -561.000000) translate(0,10)">BZ065</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2566.000000 -1067.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2430.000000 -1046.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2315.000000 -1072.000000) translate(0,12)">18</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2295.000000 -732.000000) translate(0,12)">罗格堵</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2264.000000 -1024.000000) translate(0,10)">BZ051</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2443.000000 -846.000000) translate(0,12)">滚水塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2301.000000 -843.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2328.000000 -873.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2141.000000 -874.000000) translate(0,12)">荒麦地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2226.000000 -1044.000000) translate(0,12)">25</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2339.000000 -1032.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2339.000000 -1032.000000) translate(0,27)">罗</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2339.000000 -1032.000000) translate(0,42)">格</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2339.000000 -1032.000000) translate(0,57)">堵</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2339.000000 -1032.000000) translate(0,72)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2339.000000 -1032.000000) translate(0,87)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2339.000000 -1032.000000) translate(0,102)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2179.000000 -1093.000000) translate(0,10)">BZ052</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2249.000000 -2278.000000) translate(0,12)">46</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2074.000000 -1274.000000) translate(0,12)">碳山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2179.000000 -1299.000000) translate(0,10)">BZ053</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2245.000000 -1292.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2217.000000 -1435.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -1438.000000) translate(0,12)">阱头</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2074.000000 -1484.000000) translate(0,12)">杨家</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2240.000000 -1483.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -1582.000000) translate(0,12)">尹家</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2209.000000 -1582.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2240.000000 -1745.000000) translate(0,12)">23</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -1736.000000) translate(0,12)">羊厩地基私变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2074.000000 -1735.000000) translate(0,12)">田房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2208.000000 -1889.000000) translate(0,12)">26</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2415.000000 -1904.000000) translate(0,12)">萝卜地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2250.000000 -1883.000000) translate(0,10)">Z0532</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2246.000000 -1966.000000) translate(0,12)">27</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2053.000000 -1968.000000) translate(0,12)">旧地基</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2355.000000 -2146.000000) translate(0,12)">松树林</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2210.000000 -2144.000000) translate(0,12)">35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2028.000000 -936.000000) translate(0,12)">者力村委会公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2081.000000 -1074.000000) translate(0,12)">27</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1874.000000 -1071.000000) translate(0,12)">33</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1826.000000 -1038.000000) translate(0,10)">Z0492</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -1392.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -1392.000000) translate(0,27)">田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -1392.000000) translate(0,42)">口</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -1392.000000) translate(0,57)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -1392.000000) translate(0,72)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -1392.000000) translate(0,87)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1833.000000 -1070.000000) translate(0,12)">33</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1858.000000 -613.000000) translate(0,12)">下村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1888.000000 -944.000000) translate(0,12)">18</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1728.000000 -945.000000) translate(0,12)">新村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1817.000000 -840.000000) translate(0,10)">Z0561</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1640.000000 -1005.000000) translate(0,12)">大村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1637.000000 -731.000000) translate(0,12)">瓦窑</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1671.000000 -847.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1431.000000 -1001.000000) translate(0,12)">代家</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1443.000000 -841.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1528.000000 -679.000000) translate(0,12)">旧地基</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1543.000000 -869.000000) translate(0,12)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1341.000000 -731.000000) translate(0,12)">龙潭阱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1361.000000 -869.000000) translate(0,12)">14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1564.000000 -818.000000) translate(0,10)">Z0542</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1173.000000 -867.000000) translate(0,12)">罗么店</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1705.000000 -885.000000) translate(0,12)">10kV罗么店分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2184.000000 -765.000000) translate(0,12)">大马兰</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1895.000000 -745.000000) translate(0,10)">Z0551</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1858.000000 -763.000000) translate(0,12)">26</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2010.000000 -745.000000) translate(0,10)">BZ055</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1954.000000 -637.000000) translate(0,12)">马兰箐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2067.000000 -637.000000) translate(0,12)">小马兰</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1969.000000 -773.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2088.000000 -776.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1913.000000 -797.000000) translate(0,12)">10kV大马兰分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1830.000000 -998.000000) translate(0,10)">Z0541</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -1028.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -1028.000000) translate(0,27)">者</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -1028.000000) translate(0,42)">力</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -1028.000000) translate(0,57)">下</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -1028.000000) translate(0,72)">村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -1028.000000) translate(0,87)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -1028.000000) translate(0,102)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -1028.000000) translate(0,117)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1787.000000 -1046.000000) translate(0,12)">35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1701.000000 -1046.000000) translate(0,12)">42</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1724.000000 -1122.000000) translate(0,10)">BZ057</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1562.000000 -1337.000000) translate(0,12)">东风</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1723.000000 -1363.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1654.000000 -1344.000000) translate(0,10)">Z0572</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1864.000000 -1432.000000) translate(0,12)">臭洞</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1683.000000 -1435.000000) translate(0,12)">14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1684.000000 -1283.000000) translate(0,12)">者力大</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1684.000000 -1283.000000) translate(0,27)">麦</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1684.000000 -1283.000000) translate(0,42)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1684.000000 -1283.000000) translate(0,57)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1684.000000 -1283.000000) translate(0,72)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1684.000000 -1283.000000) translate(0,87)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1557.000000 -1200.000000) translate(0,12)">大树林</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1577.000000 -1046.000000) translate(0,12)">45</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1506.000000 -1449.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1449.000000 -1162.000000) translate(0,10)">Z0611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1516.000000 -1231.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1516.000000 -1231.000000) translate(0,27)">石</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1516.000000 -1231.000000) translate(0,42)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1516.000000 -1231.000000) translate(0,57)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1516.000000 -1231.000000) translate(0,72)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1516.000000 -1231.000000) translate(0,87)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1255.000000 -1443.000000) translate(0,12)">腊脚大平掌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1506.000000 -1566.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1322.000000 -1567.000000) translate(0,12)">双树林</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1444.000000 -1622.000000) translate(0,10)">BZ062</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1322.000000 -1701.000000) translate(0,12)">新铺子</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1506.000000 -1700.000000) translate(0,12)">21</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1444.000000 -1739.000000) translate(0,10)">BZ063</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1320.000000 -1826.000000) translate(0,12)">半坡田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1506.000000 -1811.000000) translate(0,12)">22</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1634.000000 -2129.000000) translate(0,12)">双箐专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1869.000000 -1892.000000) translate(0,12)">力干么</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1468.000000 -1893.000000) translate(0,12)">26</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1546.000000 -1874.000000) translate(0,12)">10kV力干么分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1506.000000 -2057.000000) translate(0,12)">30</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1176.000000 -2117.000000) translate(0,12)">半路村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1399.000000 -2071.000000) translate(0,10)">Z0641</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1322.000000 -2041.000000) translate(0,12)">10kV河边队分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1476.000000 -2263.000000) translate(0,12)">石井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1315.000000 -1046.000000) translate(0,12)">53</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1292.000000 -1220.000000) translate(0,12)">金竹林</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1131.000000 -1046.000000) translate(0,12)">61</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1108.000000 -1224.000000) translate(0,12)">腊脚村委会公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1049.000000 -1037.000000) translate(0,10)">Z0591</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 826.000000 -938.000000) translate(0,12)">席草坝</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1003.000000 -972.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 836.000000 -842.000000) translate(0,12)">小腊脚</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1003.000000 -875.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 836.000000 -735.000000) translate(0,12)">迤村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1003.000000 -779.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 840.000000 -631.000000) translate(0,12)">老郭家专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1005.000000 -658.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 840.000000 -543.000000) translate(0,12)">大腊脚</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1006.000000 -576.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 840.000000 -460.000000) translate(0,12)">山背后</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 796.000000 -401.000000) translate(0,12)">杨梅树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 840.000000 -306.000000) translate(0,12)">大村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1001.000000 -709.000000) translate(0,10)">BZ059</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1006.000000 -491.000000) translate(0,12)">14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1006.000000 -403.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1006.000000 -331.000000) translate(0,12)">27</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 967.000000 -232.000000) translate(0,12)">下腊脚</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,27)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,42)">下</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,57)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,72)">腊</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,87)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,102)">脚</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,117)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,132)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,147)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,162)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,177)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1002.000000) translate(0,192)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1991.000000 -435.000000) translate(0,10)">BZ017</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2472.000000 -423.000000) translate(0,12)">大山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2276.000000 -432.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2251.000000 -298.000000) translate(0,12)">塔本</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2358.000000 -433.000000) translate(0,12)">25</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2329.000000 -300.000000) translate(0,12)">小母几苴</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1822.000000 -474.000000) translate(0,12)">红卫桥村委会</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1696.000000 -313.000000) translate(0,12)">牛火塘1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2001.000000 -404.000000) translate(0,12)">10kV大山分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1731.000000 -505.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1681.000000 -504.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1681.000000 -504.000000) translate(0,27)">牛</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1681.000000 -504.000000) translate(0,42)">火</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1681.000000 -504.000000) translate(0,57)">塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1681.000000 -504.000000) translate(0,72)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1681.000000 -504.000000) translate(0,87)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1681.000000 -504.000000) translate(0,102)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1569.000000 -494.000000) translate(0,20)">F016</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1603.000000 -562.000000) translate(0,20)">F0161</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1362.000000 -566.000000) translate(0,20)">081</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1406.000000 -508.000000) translate(0,20)">0816</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1305.000000 -503.000000) translate(0,20)">0811</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1256.000000 -579.000000) translate(0,20)">宜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1256.000000 -579.000000) translate(0,45)">茨</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1256.000000 -579.000000) translate(0,70)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 842.000000 -1915.000000) translate(0,12)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 806.000000 -1617.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 808.000000 -1368.000000) translate(0,12)">25</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1060.000000 -2719.000000) translate(0,20)">F0651</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 780.000000 -2632.000000) translate(0,10)">BZ085</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 659.000000 -2537.000000) translate(0,12)">移动公司私变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 841.000000 -2565.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 816.000000 -2366.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 687.000000 -2359.000000) translate(0,12)">松毛利</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 912.000000 -2365.000000) translate(0,10)">BZ086</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1519.000000 -2385.000000) translate(0,12)">上邑舍</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1030.000000 -2255.000000) translate(0,12)">大舌腰</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1054.000000 -2395.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1325.000000 -2550.000000) translate(0,12)">泥苦利</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1160.000000 -2549.000000) translate(0,12)">蒿子地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1185.000000 -2369.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1351.000000 -2368.000000) translate(0,12)">14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1452.000000 -2372.000000) translate(0,12)">17</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 872.000000 -2407.000000) translate(0,12)">10kV上邑舍分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 807.000000 -2060.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1108.000000 -2036.000000) translate(0,12)">马立平</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 900.000000 -2043.000000) translate(0,10)">BZ087</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 983.000000 -1934.000000) translate(0,12)">锅洞山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 867.000000 -2083.000000) translate(0,12)">10kV锅洞山分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 679.000000 -1893.000000) translate(0,12)">上山尾巴</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 780.000000 -1772.000000) translate(0,10)">BZ088</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 842.000000 -1773.000000) translate(0,12)">14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 954.000000 -1620.000000) translate(0,12)">假格利1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 954.000000 -1371.000000) translate(0,12)">泥苦么</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 778.000000 -1174.000000) translate(0,12)">假格利2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 806.000000 -1243.000000) translate(0,12)">28</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 856.000000 -2720.000000) translate(0,12)">松</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 856.000000 -2720.000000) translate(0,27)">毛</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 856.000000 -2720.000000) translate(0,42)">利</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 856.000000 -2720.000000) translate(0,57)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 856.000000 -2720.000000) translate(0,72)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 396.000000 -2694.000000) translate(0,10)">Z089</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 307.000000 -2350.000000) translate(0,12)">在可么</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 457.000000 -2377.000000) translate(0,12)">22</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 423.000000 -2238.000000) translate(0,12)">28</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 724.000000 -2213.000000) translate(0,12)">石房山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 516.000000 -2221.000000) translate(0,10)">BZ093</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 574.000000 -2111.000000) translate(0,12)">大石房公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 484.000000 -2272.000000) translate(0,12)">10kV石房山分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 285.000000 -2062.000000) translate(0,12)">中邑舍村委会公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 457.000000 -2089.000000) translate(0,12)">36</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 397.000000 -1933.000000) translate(0,10)">BZ094</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 463.000000 -1935.000000) translate(0,12)">37</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 467.000000 -2712.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 467.000000 -2712.000000) translate(0,27)">中</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 467.000000 -2712.000000) translate(0,42)">邑</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 467.000000 -2712.000000) translate(0,57)">舍</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 467.000000 -2712.000000) translate(0,72)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 467.000000 -2712.000000) translate(0,87)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 306.000000 -1778.000000) translate(0,12)">泥苴地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 457.000000 -1805.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 306.000000 -1553.000000) translate(0,12)">茶厂专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 457.000000 -1556.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 571.000000 -1580.000000) translate(0,12)">移动公司专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 306.000000 -1217.000000) translate(0,12)">红卫山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 306.000000 -762.000000) translate(0,12)">土固房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 462.000000 -787.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 571.000000 -974.000000) translate(0,12)">上村大平掌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 420.000000 -574.000000) translate(0,12)">27</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 701.000000 -575.000000) translate(0,12)">羊厩山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 421.000000 -973.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 509.000000 -555.000000) translate(0,10)">BZ095</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 484.000000 -593.000000) translate(0,12)">10kV羊厩山分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 410.000000 -402.000000) translate(0,12)">下山尾巴</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 417.000000 -469.000000) translate(0,12)">28</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2700.000000 -1118.000000) translate(0,20)">F0451</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2660.000000 -1103.000000) translate(0,12)">58</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 298.000000 -2318.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 717.000000 -2191.000000) translate(0,12)">S11-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 295.000000 -2034.000000) translate(0,12)">S７-３0kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 294.000000 -1751.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 302.000000 -1523.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 476.000000 -1800.000000) translate(0,12)">10kV下山尾巴分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 303.000000 -1188.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 577.000000 -946.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 304.000000 -732.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 687.000000 -2330.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1523.000000 -2359.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1325.000000 -2520.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1161.000000 -2515.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1033.000000 -2227.000000) translate(0,12)">S11-M-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1110.000000 -2007.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 984.000000 -1909.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 680.000000 -1866.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 959.000000 -1592.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 954.000000 -1340.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 782.000000 -1147.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2490.000000 -1957.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2894.000000 -2164.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2801.000000 -2092.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2801.000000 -1656.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2064.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3102.000000 -2306.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3198.000000 -1667.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3194.000000 -2065.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3406.000000 -1668.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3548.000000 -1960.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3822.000000 -1960.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3659.000000 -1662.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2559.000000 -1603.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2884.000000 -1286.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3437.000000 -1387.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3480.000000 -1263.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3951.000000 -926.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3880.000000 -540.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3394.000000 -1101.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1478.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3274.000000 -1016.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3264.000000 -1560.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2896.000000 -901.000000) translate(0,12)">S8-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2857.000000 -1187.000000) translate(0,12)">S9-125kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2296.000000 -708.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2136.000000 -844.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2812.000000 -774.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2808.000000 -668.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2933.000000 -569.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2038.000000 -911.000000) translate(0,12)">S7-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2040.000000 -1249.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2350.000000 -1407.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2073.000000 -1453.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2353.000000 -1556.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2363.000000 -1715.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2072.000000 -1701.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2048.000000 -1938.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2221.000000 -2353.000000) translate(0,12)">田口</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2205.000000 -2332.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1729.000000 -921.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1632.000000 -985.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1639.000000 -705.000000) translate(0,12)">S7-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1528.000000 -653.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1342.000000 -705.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1164.000000 -844.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1848.000000 -594.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1954.000000 -611.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -610.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2186.000000 -743.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1773.000000 -1198.000000) translate(0,12)">大箐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1761.000000 -1175.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1557.000000 -1175.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1293.000000 -1188.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1321.000000 -1418.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1108.000000 -1190.000000) translate(0,12)">S7-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1311.000000 -1546.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 835.000000 -820.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 834.000000 -705.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 839.000000 -603.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 842.000000 -514.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 840.000000 -435.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 837.000000 -282.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 972.000000 -208.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1309.000000 -1679.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1469.000000 -2239.000000) translate(0,12)">S11-M-50kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1295.000000 -2304.000000) translate(0,12)">河边队</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1297.000000 -2281.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1175.000000 -2087.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1637.000000 -2099.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1867.000000 -1868.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1703.000000 -291.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2332.000000 -273.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2473.000000 -391.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2136.000000 -298.000000) translate(0,12)">多依树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2138.000000 -272.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2236.000000 -275.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1827.000000 -446.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 360.000000 -1817.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 360.000000 -800.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 640.000000 -560.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1001.000000 -290.000000) translate(0,12)">31</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 906.000000 -352.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 903.000000 -497.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 906.000000 -581.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 902.000000 -673.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 887.000000 -787.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 885.000000 -882.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 890.000000 -979.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1333.000000 -2219.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1332.000000 -2140.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1680.000000 -1902.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1799.000000 -1906.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1424.000000 -1827.000000) translate(0,12)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1559.000000 -1387.000000) translate(0,12)">10kV东风分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1487.000000 -1042.000000) translate(0,12)">51</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1342.000000 -1152.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1812.000000 -1131.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1284.000000 -871.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1503.000000 -839.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1503.000000 -839.000000) translate(0,27)">旧</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1503.000000 -839.000000) translate(0,42)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1503.000000 -839.000000) translate(0,57)">基</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1503.000000 -839.000000) translate(0,72)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1503.000000 -839.000000) translate(0,87)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1503.000000 -839.000000) translate(0,102)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2124.000000 -743.000000) translate(0,12)">25</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2294.000000 -792.000000) translate(0,12)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1738.000000 -342.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2371.000000 -362.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2404.000000 -433.000000) translate(0,12)">34</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2707.000000 -602.000000) translate(0,12)">10kV大母几苴支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2875.000000 -564.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2260.000000 -1210.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2260.000000 -1210.000000) translate(0,27)">碳</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2260.000000 -1210.000000) translate(0,42)">山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2260.000000 -1210.000000) translate(0,57)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2260.000000 -1210.000000) translate(0,72)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2260.000000 -1210.000000) translate(0,87)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2133.000000 -1760.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2250.000000 -1927.000000) translate(0,12)">10kV萝卜地分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2615.000000 -1670.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2526.000000 -1715.000000) translate(0,12)">10kV大亮山支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3093.000000 -2049.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3093.000000 -2049.000000) translate(0,27)">中</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3093.000000 -2049.000000) translate(0,42)">村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3093.000000 -2049.000000) translate(0,57)">平</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3093.000000 -2049.000000) translate(0,72)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3093.000000 -2049.000000) translate(0,87)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3093.000000 -2049.000000) translate(0,102)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3093.000000 -2049.000000) translate(0,117)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -1994.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -1994.000000) translate(0,27)">干</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -1994.000000) translate(0,42)">坝</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -1994.000000) translate(0,57)">田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -1994.000000) translate(0,72)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -1994.000000) translate(0,87)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -1994.000000) translate(0,102)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3369.000000 -1401.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3444.000000 -1172.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3652.000000 -1381.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3652.000000 -1381.000000) translate(0,27)">土</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3652.000000 -1381.000000) translate(0,42)">掌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3652.000000 -1381.000000) translate(0,57)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3652.000000 -1381.000000) translate(0,72)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3652.000000 -1381.000000) translate(0,87)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3652.000000 -1381.000000) translate(0,102)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -1084.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -1084.000000) translate(0,27)">大</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -1084.000000) translate(0,42)">岭</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -1084.000000) translate(0,57)">岗</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -1084.000000) translate(0,72)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -1084.000000) translate(0,87)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3505.000000 -1084.000000) translate(0,102)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2748.000000 -821.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2751.000000 -710.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3713.000000 -734.000000) translate(0,12)">10kV大麻栗树分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3320.000000 -1317.000000) translate(0,12)">10kV大平掌分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2456.000000 -1044.000000) translate(0,12)">10kV者腊支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 435.000000 -2751.000000) translate(0,12)">105</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 401.000000 -2721.000000) translate(0,10)">Z0891</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2094.000000 -570.000000) translate(0,12)">马鹿塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2096.000000 -544.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2128.000000 -429.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2926.000000 -475.000000) translate(0,12)">石桂山公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2654.000000 -457.000000) translate(0,12)">74</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2696.000000 -443.000000) translate(0,10)">Z0661</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2929.000000 -447.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3882.000000 -1386.000000) translate(0,12)">41</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3969.000000 -1361.000000) translate(0,12)">八月十五公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3988.000000 -1334.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3915.000000 -1366.000000) translate(0,10)">Z0432</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3100.000000 -1454.000000) translate(0,10)">Z0475</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3151.000000 -1379.000000) translate(0,12)">小姐峨#2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -1352.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3052.000000 -1282.000000) translate(0,12)">小龙箐#2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3051.000000 -1255.000000) translate(0,12)">D11-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3909.000000 -1426.000000) translate(0,12)">10kV八月十五分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2810.000000 -2283.000000) translate(0,12)">S11-M-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2819.000000 -2010.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2166.000000 -432.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1923.000000 -498.000000) translate(0,12)">68</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1961.000000 -431.000000) translate(0,12)">69</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2438.000000 -824.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2932.000000 -1070.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2655.000000 -1640.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3115.000000 -1501.000000) translate(0,12)">17</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3454.000000 -2220.000000) translate(0,12)">大地基村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3453.000000 -2195.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3287.000000 -1414.000000) translate(0,12)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3227.000000 -1178.000000) translate(0,10)">Z0482</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3254.000000 -1150.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -820.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3730.000000 -989.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3532.000000 -1113.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3600.000000 -985.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3797.000000 -1173.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1413.000000 -2093.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 626.000000 -2250.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 587.000000 -1555.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 459.000000 -890.000000) translate(0,12)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 461.000000 -695.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1049.000000 -2076.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1302.000000 -1804.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1507.000000 -1624.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 819.000000 -918.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 968.000000 -722.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 797.000000 -379.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1783.000000 -391.000000) translate(0,12)">牛火塘2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1788.000000 -364.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1737.000000 -460.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1712.000000 -419.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2024.000000 -773.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1886.000000 -856.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1891.000000 -996.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1418.000000 -979.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1554.000000 -1315.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1773.000000 -1659.000000) translate(0,10)">Z0574</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1756.000000 -1511.000000) translate(0,12)">大窑平掌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1685.000000 -1693.000000) translate(0,12)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1718.000000 -1678.000000) translate(0,10)">Z0573</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1949.000000 -1710.000000) translate(0,12)">河边</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1837.000000 -1706.000000) translate(0,12)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1837.000000 -1706.000000) translate(0,27)">大</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1837.000000 -1706.000000) translate(0,42)">窑</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1837.000000 -1706.000000) translate(0,57)">平</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1837.000000 -1706.000000) translate(0,72)">掌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1837.000000 -1706.000000) translate(0,87)">分</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1837.000000 -1706.000000) translate(0,102)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1837.000000 -1706.000000) translate(0,117)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1736.000000 -1728.000000) translate(0,12)">10kV河边分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1927.000000 -1670.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1757.000000 -1491.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1950.000000 -1686.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1859.000000 -1408.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1729.000000 -1143.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2020.000000 -1186.000000) translate(0,12)">者力集镇#1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2019.000000 -1161.000000) translate(0,12)">D11-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2238.000000 -1161.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2244.000000 -1090.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2195.000000 -1755.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2403.000000 -1882.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2346.000000 -2123.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3116.000000 -1915.000000) translate(0,12)">14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1964.000000 -515.000000) translate(0,20)">F0171</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2763.000000 -2213.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2862.000000 -1804.000000) translate(0,10)">Z0423</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -1360.000000) translate(0,12)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1817.000000 -1708.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1507.000000 -1740.000000) translate(0,12)">21</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1807.000000 -1076.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1837.000000 -866.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1337.000000 -1090.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 970.000000 -886.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 841.000000 -2632.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 532.000000 -2252.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 463.000000 -2516.000000) translate(0,12)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 395.000000 -2532.000000) translate(0,10)">Z0921</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 400.000000 -2504.000000) translate(0,10)">Z092</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 396.000000 -2403.000000) translate(0,10)">Z0922</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2337.000000 -2655.000000) translate(0,20)">061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2335.000000 -2604.000000) translate(0,20)">0616</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2333.000000 -2700.000000) translate(0,20)">0611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2235.000000 -2722.000000) translate(0,20)">10kV马</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2235.000000 -2722.000000) translate(0,45)">鞍</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2235.000000 -2722.000000) translate(0,70)">山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2235.000000 -2722.000000) translate(0,95)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1682.000000 -822.000000) translate(0,12)">瓦窑移动基站专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1699.000000 -840.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1887.000000 -821.000000) translate(0,12)">21</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1833.000000 -806.000000) translate(0,10)">K0543</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 395.000000 -1819.000000) translate(0,10)">BZ011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 389.000000 -1594.000000) translate(0,10)">BZ012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 379.000000 -1225.000000) translate(0,10)">BZ018</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 388.000000 -886.000000) translate(0,10)">BZ097</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 387.000000 -691.000000) translate(0,10)">BZ098</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 649.000000 -2213.000000) translate(0,10)">BZ099</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2701.000000 -2281.000000) translate(0,10)">BZ066</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2694.000000 -2077.000000) translate(0,10)">BZ067</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2705.000000 -2007.000000) translate(0,10)">Z0413</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2626.000000 -1936.000000) translate(0,10)">BZ068</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3232.000000 -1797.000000) translate(0,10)">BZ069</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3344.000000 -2231.000000) translate(0,10)">BZ071</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3437.000000 -1800.000000) translate(0,10)">BZ072</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3593.000000 -1846.000000) translate(0,10)">BZ073</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3852.000000 -1840.000000) translate(0,10)">BZ074</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2631.000000 -1686.000000) translate(0,10)">BZ075</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3317.000000 -1360.000000) translate(0,10)">BZ076</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2943.000000 -1034.000000) translate(0,10)">BZ077</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1045.000000 -1075.000000) translate(0,10)">61</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2497.000000 -1078.000000) translate(0,10)">BZ078</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2392.000000 -1015.000000) translate(0,10)">BZ079</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2329.000000 -821.000000) translate(0,10)">Z0513</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2176.000000 -1144.000000) translate(0,10)">Z0522</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2167.000000 -1708.000000) translate(0,10)">BZ081</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1740.000000 -1086.000000) translate(0,10)">BZ082</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1592.000000 -1073.000000) translate(0,10)">BZ057</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1268.000000 -1096.000000) translate(0,10)">BZ083</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 927.000000 -946.000000) translate(0,10)">BZ084</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 934.000000 -844.000000) translate(0,10)">BZ085</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2696.000000 -772.000000) translate(0,10)">BZ086</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2684.000000 -676.000000) translate(0,10)">  BZ087</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1734.000000 -393.000000) translate(0,10)">Z0164</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2071.000000 -454.000000) translate(0,10)">Z0172</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2181.000000 -396.000000) translate(0,10)">BZ018</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2369.000000 -397.000000) translate(0,10)">BZ019</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1033.000000 -2036.000000) translate(0,10)">BZ089</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 578.000000 -2087.000000) translate(0,12)">S７-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 451.000000 -1240.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 471.000000 -1412.000000) translate(0,10)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 575.000000 -1452.000000) translate(0,12)">电信公司专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 591.000000 -1426.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 684.000000 -546.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 402.000000 -371.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1241.000000 -2364.000000) translate(0,10)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2340.000000 -397.000000) translate(0,10)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3311.000000 -2167.000000) translate(0,10)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3257.000000 -2295.000000) translate(0,10)">10kV狐狸窝分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3600.000000 -734.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3124.000000 -1117.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2414.000000 -923.000000) translate(0,12)">核桃阱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2416.000000 -895.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2420.000000 -1194.000000) translate(0,12)">李中府</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2419.000000 -1168.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2727.000000 -1633.000000) translate(0,12)">我地么</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2729.000000 -1604.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2777.000000 -1570.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1910.000000 -772.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1668.000000 -1326.000000) translate(0,10)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1752.000000 -737.000000) translate(0,12)">瓦窑电信基站专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1829.000000 -776.000000) translate(0,10)">K0544</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1742.000000 -757.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1884.000000 -788.000000) translate(0,12)">22</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2762.000000 -2474.000000) translate(0,10)">BZ061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2635.000000 -2454.000000) translate(0,12)">1+1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2651.000000 -2497.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2717.000000 -2559.000000) translate(0,20)">F0411</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2714.000000 -2515.000000) translate(0,20)">F041</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2977.000000 -2453.000000) translate(0,12)">大地基线电容器组</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2504.000000 -1203.000000) translate(0,12)">者腊支线电容器组</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1732.000000 -1753.000000) translate(0,12)">21</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1695.000000 -1810.000000) translate(0,12)">大麦地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1688.000000 -1785.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3301.000000 -2452.000000) translate(0,12)">狐狸窝</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3299.000000 -2427.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3351.000000 -2385.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3026.000000 -1659.000000) translate(0,12)">王朝贵专变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3044.000000 -1624.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3326.000000 -1167.000000) translate(0,10)">34</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3279.000000 -1292.000000) translate(0,12)">2７</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1234.000000 -2409.000000) translate(0,10)">BZ091</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 849.000000 -1391.000000) translate(0,10)">BZ092</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2376.000000 -1093.000000) translate(0,10)">BZ054</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3323.000000 -1265.000000) translate(0,10)">BZ042</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3716.000000 -1183.000000) translate(0,10)">BZ048</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2699.000000 -2165.000000) translate(0,10)">BZ041</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 463.000000 -2430.000000) translate(0,12)">21</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 787.000000 -2430.000000) translate(0,10)">Z0852</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 851.000000 -2492.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2654.000000 -2264.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2648.000000 -2323.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1447.000000 -601.000000) translate(0,20)">马宜线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1509.000000 -557.000000) translate(0,12)">61</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 720.000000 -2270.000000) translate(0,10)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 645.000000 -2140.000000) translate(0,10)"> 2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 315.000000 -1263.000000) translate(0,10)">  7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 848.000000 -2441.000000) translate(0,10)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 703.000000 -2400.000000) translate(0,10)"> 1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 996.000000 -2041.000000) translate(0,10)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 978.000000 -1968.000000) translate(0,10)"> 8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1105.000000 -2084.000000) translate(0,10)">  6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 910.000000 -1392.000000) translate(0,10)"> 6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2134.000000 -324.000000) translate(0,10)"> 13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2804.000000 -2254.000000) translate(0,12)"> 5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2846.000000 -2165.000000) translate(0,12)"> 8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2754.000000 -2128.000000) translate(0,12)">  5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2794.000000 -2057.000000) translate(0,12)"> 6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2571.000000 -1996.000000) translate(0,12)"> 5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3016.000000 -2154.000000) translate(0,12)">  4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3158.000000 -2262.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3406.000000 -2248.000000) translate(0,12)"> 5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3217.000000 -2147.000000) translate(0,12)"> 9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3244.000000 -1732.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3452.000000 -1729.000000) translate(0,12)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3595.000000 -1916.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3722.000000 -1727.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3865.000000 -1914.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3720.000000 -1421.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3571.000000 -875.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3693.000000 -1020.000000) translate(0,12)"> 8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -784.000000) translate(0,12)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3923.000000 -615.000000) translate(0,12)">60</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3157.000000 -1191.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2886.000000 -1038.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2896.000000 -490.000000) translate(0,12)"> 1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2957.000000 -962.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2466.000000 -1139.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2464.000000 -953.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2405.000000 -815.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2140.000000 -1181.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2235.000000 -1240.000000) translate(0,12)"> 7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2136.000000 -1273.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2324.000000 -1462.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2353.000000 -1879.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1834.000000 -648.000000) translate(0,12)">31</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1991.000000 -671.000000) translate(0,12)"> 1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1609.000000 -956.000000) translate(0,12)"> 1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1618.000000 -765.000000) translate(0,12)"> 3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1564.000000 -715.000000) translate(0,12)"> 6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1460.000000 -954.000000) translate(0,12)"> 3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1376.000000 -754.000000) translate(0,12)"> 2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1543.000000 -1363.000000) translate(0,12)"> 6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1840.000000 -1449.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1786.000000 -1541.000000) translate(0,12)"> 7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1601.000000 -1146.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1400.000000 -1468.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1401.000000 -1590.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1399.000000 -1725.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1208.000000 -2170.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 816.000000 -2757.000000) translate(0,12)">87</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2695.000000 -2422.000000) translate(0,12)"> 2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2647.000000 -2368.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 588.000000 -2211.000000) translate(0,10)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3282.000000 -2231.000000) translate(0,10)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1518.000000 -2205.000000) translate(0,12)">32</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3340.000000 -2163.000000) translate(0,10)">BZ021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2136.000000 -490.000000) translate(0,8)">10kV马鹿塘分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2126.000000 -449.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2192.000000 -375.000000) translate(0,8)">10kV多依树分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2159.000000 -388.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2381.000000 -377.000000) translate(0,8)">10kV小母几苴分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 389.000000 -1273.000000) translate(0,8)">10kV红卫山分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 411.000000 -1568.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 376.000000 -1624.000000) translate(0,8)">10kV茶厂私变分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1549.000000 -1111.000000) translate(0,8)">10kV大树林分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1180.000000 -1339.000000) translate(0,12)">腊脚抽水专变</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1178.000000 -1314.000000) translate(0,12)">S11-M-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1355.000000 -1315.000000) translate(0,8)">10kV腊脚抽水支线</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1508.000000 -1328.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1446.000000 -1338.000000) translate(0,10)">K0612</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3496.000000 -73.000000) translate(0,10)">高压计量箱</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3507.000000 -35.000000) translate(0,10)">跌落保险</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3507.000000 -111.000000) translate(0,10)">断路器</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3506.000000 -157.000000) translate(0,10)">隔离开关</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 2725.000000 -2842.000000) translate(0,40)">10kV母线</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3884.000000 -24.000000) translate(0,10)">日  期</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3864.000000 -100.000000) translate(0,10)"> 大地基供电所区域电网接线图</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 2345.000000 -103.000000) translate(0,40)">10kV母线</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 655.000000 -2824.000000) translate(0,40)">10kV大过口线</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1428.000000 -2706.000000) translate(0,20)">至中山变</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 48.000000 -2706.000000) translate(0,20)">至密者河一级站</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1886.000000 -2472.000000) translate(0,20)">至密者河二级站</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3452.000000 -274.000000) translate(0,10)">注：图中红色部份为本次更改部份。</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3443.000000 -184.000000) translate(0,10)"> 图  例</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3613.000000 -141.000000) translate(0,10)">审　定</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3615.000000 -64.000000) translate(0,10)">校　核</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3615.000000 -22.000000) translate(0,10)">制  图</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3614.000000 -98.000000) translate(0,10)">审　核</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3680.000000 -195.000000) translate(0,18)">楚 雄 市 供 电 有 限 公 司</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3739.000000 -27.000000) translate(0,12)">王朝银</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3737.000000 -64.000000) translate(0,12)">张兴保</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3737.000000 -103.000000) translate(0,12)">李晓清</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3736.000000 -144.000000) translate(0,12)">刘福紧</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 4019.000000 -24.000000) translate(0,10)">2012年11月05日</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3942" x2="3942" y1="-34" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3874" x2="3874" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3605" x2="3605" y1="-210" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3680" x2="3680" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3874" x2="3605" y1="-73" y2="-73"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3605" x2="4205" y1="0" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="3605" y1="-34" y2="-34"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3874" x2="3605" y1="-115" y2="-115"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="3605" y1="-210" y2="-210"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="3605" y1="-149" y2="-150"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3450" x2="3414" y1="-50" y2="-50"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3450" x2="3414" y1="-69" y2="-69"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3432" x2="3450" y1="-69" y2="-69"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3364" x2="3364" y1="-208" y2="-8"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3587" x2="3587" y1="-208" y2="-8"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="3082" x2="3181" y1="-2730" y2="-2730"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-2730" y2="-2671"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2674" x2="2674" y1="-2671" y2="-2634"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2694" x2="2694" y1="-2671" y2="-2634"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2694" x2="2674" y1="-2634" y2="-2634"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2677" x2="2691" y1="-2605" y2="-2593"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2674" x2="2694" y1="-2671" y2="-2671"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-2634" y2="-2577"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2677" x2="2691" y1="-2704" y2="-2693"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-2729" y2="-2671"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-346" y2="-281"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="1.25" x1="2684" x2="2684" y1="-340" y2="-281"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2674" x2="2674" y1="-281" y2="-244"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2694" x2="2694" y1="-281" y2="-244"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2694" x2="2674" y1="-244" y2="-244"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2677" x2="2691" y1="-215" y2="-204"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2674" x2="2694" y1="-281" y2="-281"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2684" x2="2684" y1="-244" y2="-187"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2677" x2="2691" y1="-314" y2="-303"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-339" y2="-281"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-244" y2="-132"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1955" x2="1955" y1="-346" y2="-281"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1955" x2="1955" y1="-227" y2="-132"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="1.25" x1="1955" x2="1955" y1="-340" y2="-281"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1945" x2="1945" y1="-264" y2="-227"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1965" x2="1965" y1="-264" y2="-227"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1965" x2="1945" y1="-227" y2="-227"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1947" x2="1962" y1="-198" y2="-187"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1945" x2="1965" y1="-264" y2="-264"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1955" x2="1955" y1="-227" y2="-170"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1955" x2="1955" y1="-339" y2="-266"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2737" x2="2751" y1="-2182" y2="-2182"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2737" x2="2684" y1="-2182" y2="-2182"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2833" x2="2847" y1="-2182" y2="-2182"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2833" x2="2781" y1="-2182" y2="-2182"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2760" x2="2684" y1="-2093" y2="-2093"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2749" x2="2793" y1="-2023" y2="-2023"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2749" x2="2684" y1="-2023" y2="-2023"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2684" x2="2609" y1="-1955" y2="-1955"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2751" x2="2684" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2711" x2="2725" y1="-1818" y2="-1807"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2833" x2="2847" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2833" x2="2781" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2847" x2="3904" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2847" x2="2847" y1="-1736" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3210" x2="3225" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3225" x2="3225" y1="-1736" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3415" x2="3429" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3429" x2="3429" y1="-1736" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3684" x2="3698" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3698" x2="3698" y1="-1736" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3124" x2="3138" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3316" x2="3330" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3561" x2="3576" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3830" x2="3845" y1="-1812" y2="-1812"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3138" x2="3138" y1="-1812" y2="-2063"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3138" x2="3063" y1="-2112" y2="-2112"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3138" x2="3139" y1="-2063" y2="-2236"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3330" y1="-1812" y2="-2063"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3255" y1="-2112" y2="-2112"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3330" y1="-2063" y2="-2236"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3330" y1="-2236" y2="-2292"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3576" x2="3576" y1="-1812" y2="-1887"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3844" x2="3844" y1="-1809" y2="-1885"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3904" x2="3904" y1="-1812" y2="-618"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3904" x2="3499" y1="-1149" y2="-1149"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3699" x2="3699" y1="-1149" y2="-1225"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3699" x2="3698" y1="-1225" y2="-1395"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3499" x2="3437" y1="-1149" y2="-1149"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3549" x2="3549" y1="-1074" y2="-1150"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3549" x2="3548" y1="-1074" y2="-886"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3621" x2="3546" y1="-985" y2="-985"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3621" x2="3687" y1="-985" y2="-986"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3602" x2="3616" y1="-991" y2="-980"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3977" x2="3902" y1="-967" y2="-967"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3838" x2="3763" y1="-754" y2="-754"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3838" x2="3904" y1="-754" y2="-754"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3859" x2="3873" y1="-760" y2="-748"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3541" x2="3556" y1="-1118" y2="-1106"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2684" x2="2609" y1="-1651" y2="-1651"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3089" x2="2684" y1="-1472" y2="-1472"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3089" x2="3310" y1="-1472" y2="-1472"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2760" x2="2760" y1="-1472" y2="-1547"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2921" x2="2921" y1="-1349" y2="-1472"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3237" x2="3237" y1="-1470" y2="-1546"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3386" x2="3311" y1="-1406" y2="-1406"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3310" x2="3310" y1="-1472" y2="-1084"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3386" x2="3311" y1="-1281" y2="-1281"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3310" x2="3247" y1="-1157" y2="-1157"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3247" x2="3181" y1="-1157" y2="-1157"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2677" x2="2691" y1="-867" y2="-856"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2588" x2="2602" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2588" x2="2536" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2648" x2="2663" y1="-1053" y2="-1042"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2684" x2="2632" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2896" x2="2896" y1="-1046" y2="-1122"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2921" x2="2935" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2935" x2="2935" y1="-972" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2761" x2="2686" y1="-799" y2="-799"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2761" x2="2686" y1="-692" y2="-692"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2761" x2="2686" y1="-570" y2="-570"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2892" x2="2761" y1="-570" y2="-570"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2536" x2="1229" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2319" x2="2319" y1="-1048" y2="-774"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1883" x2="1883" y1="-1048" y2="-655"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1280" x2="1883" y1="-849" y2="-849"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2372" x2="1883" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2233" x2="2233" y1="-1048" y2="-2278"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1497" x2="1497" y1="-1048" y2="-2183"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2442" x2="2442" y1="-972" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2557" x2="2557" y1="-1046" y2="-1122"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2557" x2="2557" y1="-1170" y2="-1162"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2554" x2="2554" y1="-1162" y2="-1170"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2554" x2="2532" y1="-1166" y2="-1166"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2583" x2="2557" y1="-1166" y2="-1166"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2568" x2="2557" y1="-1141" y2="-1122"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2583" x2="2570" y1="-1166" y2="-1144"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2567" x2="2573" y1="-1146" y2="-1142"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2572" x2="2565" y1="-1139" y2="-1143"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2543" x2="2532" y1="-1147" y2="-1166"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2557" x2="2545" y1="-1122" y2="-1144"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2548" x2="2541" y1="-1146" y2="-1142"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2540" x2="2546" y1="-1145" y2="-1149"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2394" x2="2319" y1="-836" y2="-836"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2319" x2="2243" y1="-863" y2="-863"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2233" x2="2158" y1="-1238" y2="-1238"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2306" x2="2231" y1="-1427" y2="-1427"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2233" x2="2158" y1="-1474" y2="-1474"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2306" x2="2231" y1="-1572" y2="-1572"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2306" x2="2231" y1="-1726" y2="-1726"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2233" x2="2158" y1="-1726" y2="-1726"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2306" x2="2231" y1="-1894" y2="-1894"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2261" x2="2276" y1="-1899" y2="-1888"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2306" x2="2367" y1="-1894" y2="-1894"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2233" x2="2158" y1="-1958" y2="-1958"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2306" x2="2231" y1="-2136" y2="-2136"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2090" x2="2090" y1="-974" y2="-1049"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1831" x2="1846" y1="-1053" y2="-1042"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1875" x2="1890" y1="-996" y2="-985"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1883" x2="2136" y1="-755" y2="-755"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1883" x2="1808" y1="-936" y2="-936"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1826" x2="1841" y1="-855" y2="-843"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1661" x2="1661" y1="-848" y2="-923"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1661" x2="1661" y1="-774" y2="-849"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1451" x2="1451" y1="-848" y2="-923"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1553" x2="1553" y1="-774" y2="-849"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1366" x2="1366" y1="-774" y2="-849"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1545" x2="1560" y1="-817" y2="-806"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1553" x2="1553" y1="-774" y2="-722"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1903" x2="1918" y1="-760" y2="-749"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1979" x2="1979" y1="-679" y2="-755"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2092" x2="2092" y1="-679" y2="-755"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1794" x2="1794" y1="-1046" y2="-1122"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1714" x2="1638" y1="-1353" y2="-1353"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1638" x2="1601" y1="-1353" y2="-1353"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1669" x2="1683" y1="-1359" y2="-1347"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1778" x2="1822" y1="-1421" y2="-1421"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1778" x2="1714" y1="-1421" y2="-1421"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1585" x2="1585" y1="-1046" y2="-1122"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1490" x2="1504" y1="-1166" y2="-1155"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1497" x2="1422" y1="-1434" y2="-1434"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1497" x2="1422" y1="-1558" y2="-1558"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1497" x2="1422" y1="-1692" y2="-1692"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1497" x2="1422" y1="-1804" y2="-1804"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1497" x2="1641" y1="-1882" y2="-1882"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1641" x2="1767" y1="-1882" y2="-1882"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1669" x2="1669" y1="-1884" y2="-2034"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1767" x2="1820" y1="-1882" y2="-1882"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1497" x2="1422" y1="-2050" y2="-2050"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1422" x2="1325" y1="-2050" y2="-2050"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1325" x2="1325" y1="-2050" y2="-2219"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1325" x2="1229" y1="-2134" y2="-2134"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1414" x2="1429" y1="-2055" y2="-2044"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1323" x2="1323" y1="-1046" y2="-1122"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1139" x2="1139" y1="-1046" y2="-1122"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1060" x2="1074" y1="-1053" y2="-1042"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1229" x2="991" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="991" x2="991" y1="-1048" y2="-275"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1955" x2="1955" y1="-312" y2="-412"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1955" x2="2424" y1="-412" y2="-412"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2394" x2="1955" y1="-412" y2="-412"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2279" x2="2279" y1="-412" y2="-341"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2362" x2="2362" y1="-412" y2="-342"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2174" x2="2174" y1="-338" y2="-412"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1724" x2="1724" y1="-516" y2="-446"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1724" x2="1724" y1="-354" y2="-446"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1723" x2="1830" y1="-486" y2="-486"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1911" x2="1611" y1="-516" y2="-516"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1911" x2="1954" y1="-516" y2="-516"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1955" x2="1955" y1="-446" y2="-516"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1955" x2="1955" y1="-446" y2="-412"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1581" x2="1611" y1="-526" y2="-526"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1611" x2="1611" y1="-526" y2="-506"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1581" x2="1581" y1="-506" y2="-526"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1611" x2="1581" y1="-506" y2="-506"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1627" x2="1642" y1="-522" y2="-511"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1581" x2="1403" y1="-516" y2="-516"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1295" x2="1295" y1="-516" y2="-572"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1295" x2="1295" y1="-572" y2="-461"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1373" x2="1403" y1="-526" y2="-526"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1403" x2="1403" y1="-526" y2="-506"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1373" x2="1373" y1="-506" y2="-526"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1403" x2="1373" y1="-506" y2="-506"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1419" x2="1434" y1="-522" y2="-511"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1319" x2="1334" y1="-522" y2="-511"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1373" x2="1295" y1="-516" y2="-516"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1089" x2="833" y1="-2730" y2="-2730"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="833" x2="833" y1="-2730" y2="-1222"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="449" y1="-1222" y2="-446"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1089" x2="1487" y1="-2730" y2="-2730"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1487" x2="1464" y1="-2730" y2="-2743"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1464" x2="1487" y1="-2717" y2="-2730"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1081" x2="1096" y1="-2736" y2="-2725"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="206" x2="231" y1="-2730" y2="-2715"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="231" x2="206" y1="-2744" y2="-2730"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="833" x2="737" y1="-2555" y2="-2555"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="833" x2="737" y1="-2374" y2="-2374"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="833" x2="1471" y1="-2374" y2="-2374"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1048" x2="1063" y1="-2374" y2="-2374"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1063" x2="1063" y1="-2299" y2="-2374"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1178" x2="1192" y1="-2374" y2="-2374"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1343" x2="1357" y1="-2374" y2="-2374"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1357" x2="1357" y1="-2374" y2="-2450"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1192" x2="1192" y1="-2374" y2="-2450"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="908" x2="833" y1="-2053" y2="-2053"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="908" x2="1103" y1="-2053" y2="-2053"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1016" x2="1016" y1="-1978" y2="-2053"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="833" x2="737" y1="-1907" y2="-1907"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1669" x2="1669" y1="-1884" y2="-1882"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="906" x2="830" y1="-1609" y2="-1609"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="906" x2="830" y1="-1361" y2="-1361"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="449" y1="-2730" y2="-2701"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="354" y1="-2368" y2="-2368"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="525" x2="449" y1="-2231" y2="-2231"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="525" x2="720" y1="-2231" y2="-2231"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="632" x2="632" y1="-2155" y2="-2231"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="354" y1="-2079" y2="-2079"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="354" y1="-1796" y2="-1796"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="354" y1="-1570" y2="-1570"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="522" x2="447" y1="-1570" y2="-1570"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="354" y1="-1235" y2="-1235"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="354" y1="-779" y2="-779"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="522" x2="447" y1="-964" y2="-964"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="522" x2="447" y1="-564" y2="-564"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="653" x2="522" y1="-564" y2="-564"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2677" x2="2691" y1="-1107" y2="-1096"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="989" x2="882" y1="-862" y2="-862"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="989" x2="882" y1="-766" y2="-766"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="989" x2="900" y1="-650" y2="-650"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="989" x2="900" y1="-562" y2="-562"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="989" x2="900" y1="-479" y2="-479"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="989" x2="900" y1="-394" y2="-394"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="989" x2="900" y1="-325" y2="-325"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="989" x2="882" y1="-958" y2="-958"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="441" x2="455" y1="-2724" y2="-2712"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2124" x2="2124" y1="-412" y2="-487"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2117" x2="2132" y1="-458" y2="-446"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2758" x2="2683" y1="-450" y2="-450"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2889" x2="2758" y1="-450" y2="-450"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2704" x2="2718" y1="-456" y2="-445"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4026" x2="3905" y1="-1375" y2="-1375"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3926" x2="3941" y1="-1382" y2="-1371"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3094" x2="3094" y1="-1323" y2="-1470"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3154" x2="3094" y1="-1394" y2="-1394"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3099" x2="3088" y1="-1459" y2="-1445"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2706" x2="2720" y1="-2029" y2="-2018"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1949" x2="1964" y1="-493" y2="-481"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3406" x2="3330" y1="-2210" y2="-2210"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3252" x2="3266" y1="-1163" y2="-1151"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1725" x2="1831" y1="-409" y2="-409"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1734" x2="1748" y1="-415" y2="-403"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1813" x2="1828" y1="-1667" y2="-1656"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1733" x2="1747" y1="-1693" y2="-1681"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1910" x2="1751" y1="-1687" y2="-1687"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1751" x2="1713" y1="-1687" y2="-1687"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1822" x2="1822" y1="-1553" y2="-1687"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2233" x2="2158" y1="-1153" y2="-1153"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2202" x2="2216" y1="-1159" y2="-1148"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2871" x2="2886" y1="-1818" y2="-1807"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2329" x2="2344" y1="-842" y2="-831"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="441" x2="455" y1="-2534" y2="-2522"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="444" x2="458" y1="-2398" y2="-2387"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="449" y1="-2481" y2="-1222"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2304" x2="2304" y1="-2667" y2="-2630"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2324" x2="2324" y1="-2667" y2="-2630"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2324" x2="2304" y1="-2630" y2="-2630"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2306" x2="2321" y1="-2601" y2="-2590"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2314" x2="2314" y1="-2630" y2="-2495"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2306" x2="2321" y1="-2700" y2="-2689"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2314" x2="2314" y1="-2725" y2="-2667"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2304" x2="2324" y1="-2668" y2="-2668"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1932" x2="2681" y1="-2410" y2="-2410"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="449" y1="-2671" y2="-2511"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1882" x2="1806" y1="-814" y2="-814"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1853" x2="1868" y1="-820" y2="-808"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="525" x2="449" y1="-1431" y2="-1431"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2442" x2="2442" y1="-1082" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2442" x2="2442" y1="-972" y2="-965"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2442" x2="2442" y1="-1082" y2="-1108"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1881" x2="1806" y1="-778" y2="-778"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1853" x2="1867" y1="-784" y2="-773"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1910" x2="1957" y1="-2411" y2="-2423"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1913" x2="1952" y1="-2408" y2="-2395"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2314" x2="2314" y1="-2495" y2="-2435"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2314" x2="2314" y1="-2435" y2="-2410"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2314" x2="2490" y1="-2410" y2="-2410"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2672" x2="2701" y1="-2556" y2="-2545"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2674" x2="2694" y1="-2507" y2="-2507"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2694" x2="2694" y1="-2507" y2="-2465"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2694" x2="2673" y1="-2465" y2="-2465"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2673" x2="2673" y1="-2465" y2="-2507"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-2577" y2="-2507"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-2465" y2="-1958"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-1953" y2="-1653"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-1653" y2="-1648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2930" x2="2949" y1="-2432" y2="-2421"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2904" x2="2927" y1="-2447" y2="-2434"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2904" x2="2927" y1="-2447" y2="-2459"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2949" x2="2949" y1="-2443" y2="-2421"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2949" x2="2949" y1="-2472" y2="-2447"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2927" x2="2932" y1="-2428" y2="-2436"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2924" x2="2929" y1="-2430" y2="-2438"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2930" x2="2927" y1="-2461" y2="-2466"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2929" x2="2924" y1="-2455" y2="-2464"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2954" x2="2943" y1="-2443" y2="-2443"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2954" x2="2944" y1="-2446" y2="-2447"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2684" x2="2904" y1="-2447" y2="-2447"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2949" x2="2930" y1="-2472" y2="-2461"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2930" x2="2932" y1="-2461" y2="-2457"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2676" x2="2695" y1="-2375" y2="-2356"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2637" x2="2658" y1="-2422" y2="-2398"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3716" x2="3716" y1="-1149" y2="-1149"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1714" x2="1714" y1="-1661" y2="-1722"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1714" x2="1714" y1="-1353" y2="-1050"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1714" x2="1714" y1="-1351" y2="-1419"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1714" x2="1714" y1="-1424" y2="-1661"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3330" y1="-2292" y2="-2360"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3089" x2="3092" y1="-1472" y2="-1555"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2686" y1="-346" y2="-568"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2686" x2="2686" y1="-570" y2="-860"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2686" x2="2686" y1="-860" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-1048" y2="-1469"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2684" x2="2684" y1="-1469" y2="-1648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="444" x2="444" y1="-2433" y2="-2407"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="455" x2="455" y1="-2433" y2="-2407"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="824" x2="845" y1="-2443" y2="-2429"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="826" x2="826" y1="-2501" y2="-2474"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="841" x2="841" y1="-2501" y2="-2474"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2684" x2="2767" y1="-2254" y2="-2254"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2670" x2="2670" y1="-2328" y2="-2301"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2699" x2="2699" y1="-2328" y2="-2301"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1538" x2="1502" y1="-531" y2="-531"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1520" x2="1538" y1="-531" y2="-531"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1538" x2="1502" y1="-502" y2="-502"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1520" x2="1538" y1="-502" y2="-502"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2687" x2="2933" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2933" x2="2935" y1="-1048" y2="-1045"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="1497" x2="1346" y1="-1319" y2="-1319"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="1471" x2="1485" y1="-1325" y2="-1313"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3384" x2="3478" y1="-26" y2="-26"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3453" x2="3478" y1="-104" y2="-104"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3423" x2="3453" y1="-114" y2="-114"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3453" x2="3423" y1="-94" y2="-94"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3423" x2="3423" y1="-94" y2="-114"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3453" x2="3453" y1="-114" y2="-94"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3386" x2="3423" y1="-104" y2="-104"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3442" x2="3422" y1="-136" y2="-156"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3386" x2="3478" y1="-146" y2="-146"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3364" x2="3587" y1="-8" y2="-8"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3587" x2="3364" y1="-208" y2="-208"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3382" x2="3474" y1="-59" y2="-59"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2751" x2="2781" y1="-2192" y2="-2192"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2781" x2="2781" y1="-2192" y2="-2172"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2751" x2="2751" y1="-2172" y2="-2192"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2781" x2="2751" y1="-2172" y2="-2172"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2751" x2="2781" y1="-1821" y2="-1821"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2781" x2="2781" y1="-1821" y2="-1801"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2751" x2="2751" y1="-1801" y2="-1821"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2781" x2="2751" y1="-1801" y2="-1801"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2602" x2="2632" y1="-1058" y2="-1058"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2632" x2="2632" y1="-1058" y2="-1038"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2602" x2="2602" y1="-1038" y2="-1058"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2632" x2="2602" y1="-1038" y2="-1038"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="458" x2="458" y1="-2701" y2="-2671"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="458" x2="438" y1="-2671" y2="-2671"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="438" x2="458" y1="-2701" y2="-2701"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="438" x2="438" y1="-2671" y2="-2701"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="458" x2="458" y1="-2511" y2="-2481"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="458" x2="438" y1="-2481" y2="-2481"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="438" x2="458" y1="-2511" y2="-2511"/>
   <line DF8003:Layer="自变" stroke="rgb(255,255,255)" stroke-width="0.5" x1="438" x2="438" y1="-2481" y2="-2511"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="0" y1="0" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="4205" y1="-2970" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="0" x2="0" y1="0" y2="-2970"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="0" x2="4205" y1="-2970" y2="-2970"/>
   <line DF8003:Layer="粗线" stroke="rgb(255,255,255)" stroke-width="4" x1="2158" x2="3082" y1="-2730" y2="-2730"/>
   <line DF8003:Layer="粗线" stroke="rgb(255,255,255)" stroke-width="3" x1="206" x2="1089" y1="-2730" y2="-2730"/>
   <line DF8003:Layer="粗线" stroke="rgb(255,255,255)" stroke-width="4" x1="1846" x2="3048" y1="-132" y2="-132"/>
   <line DF8003:Layer="粗线" stroke="rgb(255,255,255)" stroke-width="4" x1="1599" x2="2563" y1="-132" y2="-132"/>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="0" cx="2684" cy="-2182" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-2092" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-1650" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-1471" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2872" cy="-2181" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2860" cy="-2181" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2785" cy="-2092" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2772" cy="-2092" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2818" cy="-2022" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2805" cy="-2022" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-2023" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2596" cy="-1955" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2583" cy="-1955" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-1955" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2847" cy="-1711" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2847" cy="-1723" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2847" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3224" cy="-1711" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3224" cy="-1723" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3224" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3429" cy="-1711" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3429" cy="-1723" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3429" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3698" cy="-1711" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3698" cy="-1723" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3698" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3138" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3575" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3844" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3137" cy="-2248" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3137" cy="-2261" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3138" cy="-2111" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3050" cy="-2110" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3037" cy="-2110" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-2111" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3242" cy="-2110" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3229" cy="-2110" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3574" cy="-1899" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3574" cy="-1912" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3842" cy="-1897" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3842" cy="-1909" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3904" cy="-1149" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3904" cy="-966" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3904" cy="-754" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3424" cy="-1149" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3412" cy="-1149" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3698" cy="-1149" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3548" cy="-1149" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3697" cy="-1407" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3697" cy="-1419" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3548" cy="-860" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3548" cy="-873" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3548" cy="-985" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3712" cy="-985" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3699" cy="-985" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3904" cy="-593" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3904" cy="-606" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4002" cy="-966" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3989" cy="-966" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3750" cy="-753" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3737" cy="-753" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2596" cy="-1650" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2583" cy="-1650" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2758" cy="-1559" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2758" cy="-1572" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2921" cy="-1324" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2921" cy="-1336" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2920" cy="-1472" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3235" cy="-1558" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3235" cy="-1570" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3236" cy="-1471" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3309" cy="-1406" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3411" cy="-1406" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3398" cy="-1406" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3308" cy="-1058" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3308" cy="-1071" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3309" cy="-1281" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3411" cy="-1280" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3398" cy="-1280" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3309" cy="-1157" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3168" cy="-1157" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3155" cy="-1157" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2893" cy="-1134" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2893" cy="-1146" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2895" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2935" cy="-947" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2935" cy="-959" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2935" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2685" cy="-798" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2786" cy="-798" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2773" cy="-798" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2685" cy="-691" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2786" cy="-691" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2773" cy="-691" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2685" cy="-570" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2916" cy="-570" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2904" cy="-570" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2443" cy="-939" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2443" cy="-951" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2442" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2442" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2557" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2318" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1882" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1713" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1497" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2318" cy="-749" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2318" cy="-762" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2318" cy="-836" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2419" cy="-835" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2406" cy="-835" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2318" cy="-862" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2230" cy="-862" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2218" cy="-862" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-1238" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-1427" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-1474" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-1572" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-1725" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-1893" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-1957" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-2136" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-2290" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2233" cy="-2302" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2145" cy="-1238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2132" cy="-1238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2331" cy="-1427" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2318" cy="-1427" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2145" cy="-1474" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2132" cy="-1474" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2331" cy="-1572" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2318" cy="-1572" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2331" cy="-1725" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2318" cy="-1725" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2145" cy="-1725" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2132" cy="-1725" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2392" cy="-1893" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2379" cy="-1893" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2145" cy="-1957" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2132" cy="-1957" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2331" cy="-2136" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2318" cy="-2136" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2089" cy="-1049" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2089" cy="-948" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2089" cy="-961" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2089" cy="-1049" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1882" cy="-630" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1882" cy="-642" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1883" cy="-935" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1795" cy="-935" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1782" cy="-935" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1882" cy="-849" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1659" cy="-935" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1659" cy="-948" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1661" cy="-849" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1661" cy="-748" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1661" cy="-761" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1661" cy="-849" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1449" cy="-935" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1449" cy="-948" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1450" cy="-849" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1450" cy="-849" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1552" cy="-849" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1552" cy="-696" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1552" cy="-709" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1552" cy="-849" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1365" cy="-849" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1365" cy="-748" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1365" cy="-761" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1365" cy="-849" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1267" cy="-849" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1254" cy="-849" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2161" cy="-754" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2148" cy="-754" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1882" cy="-754" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1978" cy="-754" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2091" cy="-754" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1978" cy="-654" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1978" cy="-666" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2091" cy="-654" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2091" cy="-666" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1792" cy="-1134" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1792" cy="-1146" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1794" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1794" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1713" cy="-1353" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1588" cy="-1353" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1575" cy="-1353" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1847" cy="-1421" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1834" cy="-1421" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1713" cy="-1421" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1583" cy="-1134" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1583" cy="-1146" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1584" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1584" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1497" cy="-1433" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1497" cy="-1557" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1497" cy="-1692" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1497" cy="-1804" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1497" cy="-1882" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1497" cy="-2049" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1409" cy="-1433" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1396" cy="-1433" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1409" cy="-1557" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1396" cy="-1557" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1409" cy="-1692" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1396" cy="-1692" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1409" cy="-1804" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1396" cy="-1804" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1667" cy="-2046" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1667" cy="-2059" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1845" cy="-1882" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1832" cy="-1882" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1216" cy="-2134" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1204" cy="-2134" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1323" cy="-2231" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1323" cy="-2243" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1495" cy="-2195" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1495" cy="-2207" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1322" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1322" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1321" cy="-1134" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1321" cy="-1146" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1138" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1138" cy="-1047" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1137" cy="-1134" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1137" cy="-1146" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-958" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-861" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-765" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-650" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-562" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-479" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-394" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-325" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-249" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="991" cy="-262" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2449" cy="-412" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2436" cy="-412" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1955" cy="-412" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2279" cy="-316" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2279" cy="-328" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2361" cy="-412" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2361" cy="-317" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2361" cy="-329" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2173" cy="-314" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2173" cy="-326" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2278" cy="-411" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1723" cy="-329" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1723" cy="-341" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1755" cy="-516" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1854" cy="-485" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1842" cy="-485" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1722" cy="-485" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="832" cy="-2555" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="832" cy="-1907" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="832" cy="-1609" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="832" cy="-1360" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="724" cy="-2554" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="712" cy="-2554" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="832" cy="-2374" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="724" cy="-2374" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="712" cy="-2374" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1495" cy="-2374" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1483" cy="-2374" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1062" cy="-2273" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1062" cy="-2286" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1062" cy="-2374" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1191" cy="-2374" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1357" cy="-2374" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1355" cy="-2462" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1355" cy="-2474" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1190" cy="-2462" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1190" cy="-2474" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="832" cy="-2053" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1128" cy="-2053" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1115" cy="-2053" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1015" cy="-1952" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1015" cy="-1965" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1015" cy="-2053" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="724" cy="-1907" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="712" cy="-1907" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="930" cy="-1609" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="918" cy="-1609" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="930" cy="-1360" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="918" cy="-1360" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="833" cy="-1196" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="833" cy="-1209" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-2367" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="341" cy="-2367" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="328" cy="-2367" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-2230" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="744" cy="-2230" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="732" cy="-2230" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="632" cy="-2130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="632" cy="-2142" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="632" cy="-2230" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-2079" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="341" cy="-2079" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="328" cy="-2079" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-1795" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="341" cy="-1795" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="328" cy="-1795" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-1570" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="341" cy="-1570" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="328" cy="-1570" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="547" cy="-1570" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="534" cy="-1570" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-1234" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="341" cy="-1234" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="328" cy="-1234" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-779" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="341" cy="-779" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="328" cy="-779" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-964" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="547" cy="-964" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="534" cy="-964" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-564" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="678" cy="-564" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="665" cy="-564" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-421" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="449" cy="-433" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="857" cy="-861" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="869" cy="-861" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="857" cy="-765" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="869" cy="-765" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="874" cy="-650" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="887" cy="-650" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="874" cy="-562" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="887" cy="-562" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="874" cy="-479" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="887" cy="-479" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="874" cy="-394" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="887" cy="-394" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="874" cy="-325" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="887" cy="-325" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="857" cy="-958" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="869" cy="-958" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2684" cy="-1089" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2122" cy="-499" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2122" cy="-512" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2124" cy="-412" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2682" cy="-450" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2914" cy="-449" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2901" cy="-449" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4050" cy="-1379" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="4038" cy="-1379" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3904" cy="-1375" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3179" cy="-1394" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3166" cy="-1394" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3095" cy="-1297" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3095" cy="-1310" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2174" cy="-412" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-2209" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3430" cy="-2209" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3418" cy="-2209" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1856" cy="-409" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1843" cy="-409" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1713" cy="-1687" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1935" cy="-1687" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1922" cy="-1687" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1821" cy="-1527" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1821" cy="-1540" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2232" cy="-1152" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2145" cy="-1152" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2132" cy="-1152" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1794" cy="-813" fill="none" r="13" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1781" cy="-813" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="549" cy="-1430" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="537" cy="-1430" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2442" cy="-1120" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2442" cy="-1133" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1794" cy="-778" fill="none" r="13" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1780" cy="-777" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1716" cy="-1732" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1716" cy="-1745" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3329" cy="-2372" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3329" cy="-2385" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3092" cy="-1567" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3092" cy="-1580" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2683" cy="-2254" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2791" cy="-2256" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2779" cy="-2256" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1497" cy="-1319" fill="none" r="2.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1333" cy="-1319" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1321" cy="-1319" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3432" cy="-50" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3432" cy="-50" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3432" cy="-69" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3432" cy="-69" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="456" cy="-2420" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="444" cy="-2420" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="825" cy="-2487" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="841" cy="-2487" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="2670" cy="-2315" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="2699" cy="-2314" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="1520" cy="-530" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="1520" cy="-501" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="标注、文字:0.000000 0.000000" layer11="图框（粗实线）:0.000000 0.000000" layer12="虚线0.2:0.000000 0.000000" layer13="粗线:0.000000 0.000000" layer14="细实线:0.000000 0.000000" layer15="填充层:0.000000 0.000000" layer16="轮廓:0.000000 0.000000" layer17="配电接线层:0.000000 0.000000" layer18="设备（实线）:0.000000 0.000000" layer19="标注线层:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer20="10kV母线:0.000000 0.000000" layer21="35kV母线:0.000000 0.000000" layer22="高压电缆0.35:0.000000 0.000000" layer23="实线:0.000000 0.000000" layer24="10KV线路:0.000000 0.000000" layer25="虚线:0.000000 0.000000" layer26="一二期农网:0.000000 0.000000" layer27="文字层:0.000000 0.000000" layer28="村委会、变电站、变压器:0.000000 0.000000" layer29="西部电网完善线路:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer30="30万无电通电线路:0.000000 0.000000" layer31="未改造过的线路:0.000000 0.000000" layer32="0:0.000000 0.000000" layer33="主干线:0.000000 0.000000" layer34="次干线:0.000000 0.000000" layer35="支线:0.000000 0.000000" layer36="公变:0.000000 0.000000" layer37="自变:0.000000 0.000000" layer38="图框（细实线）:0.000000 0.000000" layer39="标注、文字:0.000000 0.000000" layer4="主干线:0.000000 0.000000" layer40="图框（粗实线）:0.000000 0.000000" layer41="虚线0.2:0.000000 0.000000" layer42="粗线:0.000000 0.000000" layer43="细实线:0.000000 0.000000" layer44="填充层:0.000000 0.000000" layer45="轮廓:0.000000 0.000000" layer46="配电接线层:0.000000 0.000000" layer47="设备（实线）:0.000000 0.000000" layer48="标注线层:0.000000 0.000000" layer49="10kV母线:0.000000 0.000000" layer5="次干线:0.000000 0.000000" layer50="35kV母线:0.000000 0.000000" layer51="高压电缆0.35:0.000000 0.000000" layer52="实线:0.000000 0.000000" layer53="10KV线路:0.000000 0.000000" layer54="虚线:0.000000 0.000000" layer55="一二期农网:0.000000 0.000000" layer56="文字层:0.000000 0.000000" layer57="村委会、变电站、变压器:0.000000 0.000000" layer58="西部电网完善线路:0.000000 0.000000" layer59="30万无电通电线路:0.000000 0.000000" layer6="支线:0.000000 0.000000" layer60="未改造过的线路:0.000000 0.000000" layer7="公变:0.000000 0.000000" layer8="自变:0.000000 0.000000" layer9="图框（细实线）:0.000000 0.000000" layerN="61"/>
</svg>