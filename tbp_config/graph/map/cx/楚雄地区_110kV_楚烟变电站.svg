<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-34" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3116 -1243 2516 1273">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape5">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="19" y2="19"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="earth:shape8">
    <polyline DF8003:Layer="PUBLIC" points="0,8 5,18 9,8 0,8 " stroke-width="0.5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="3" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <rect height="29" stroke-width="2" width="15" x="1" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="8" x2="11" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="9" x2="6" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.8" x1="8" x2="8" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="11" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="1" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="51" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape99">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="1" y1="6" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="11" y1="6" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="1" y1="29" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="11" y1="29" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape98">
    <polyline DF8003:Layer="PUBLIC" points="0,17 4,32 8,17 0,17 " stroke-width="0.4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="4" y1="63" y2="51"/>
    <polyline DF8003:Layer="PUBLIC" points="0,51 4,36 8,51 0,51 " stroke-width="0.4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="4" y1="5" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape119">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="42" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="44" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="45" y2="45"/>
    <circle cx="25" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="25" cy="18" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="53" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="53" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="35" x2="17" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="53" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="53" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="reactance:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="12" y1="50" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="12" y1="15" y2="8"/>
    <polyline points="12,16 10,16 9,16 7,16 6,17 4,18 3,19 2,21 2,22 1,24 1,25 1,27 1,29 1,30 2,32 2,33 3,35 4,36 6,37 7,37 9,38 10,38 12,39 14,38 15,38 17,37 18,37 20,36 21,35 22,33 22,32 23,30 23,29 23,27 " stroke-width="0.0864"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="24" x2="12" y1="27" y2="27"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape9_0">
    <ellipse cx="9" cy="11" fillStyle="0" rx="8" ry="8.5" stroke-width="0.173469"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="25" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.203125" x1="31" x2="31" y1="32" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.203125" x1="25" x2="25" y1="32" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="52" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="31" y2="94"/>
    <polyline DF8003:Layer="PUBLIC" points="8,69 2,56 15,56 8,69 8,68 8,69 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173469" x1="9" x2="6" y1="13" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173469" x1="9" x2="12" y1="13" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173469" x1="6" x2="12" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape9_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="9,68 42,47 42,25 " stroke-width="1"/>
    <ellipse cx="9" cy="22" fillStyle="0" rx="8" ry="8.5" stroke-width="0.173469"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173469" x1="9" x2="9" y1="22" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173469" x1="9" x2="11" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173469" x1="6" x2="9" y1="27" y2="25"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
   </symbol>
   <symbol id="transformer2:shape5_0">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="35" y1="17" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="27" x2="35" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="43" y1="25" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape5_1">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="45" x2="29" y1="64" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="45" x2="29" y1="64" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="29" y1="73" y2="56"/>
   </symbol>
   <symbol id="voltageTransformer:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="19" x2="16" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="13" x2="16" y1="8" y2="6"/>
    <ellipse cx="15" cy="19" fillStyle="0" rx="6.5" ry="8.5" stroke-width="0.445094"/>
    <ellipse cx="25" cy="15" fillStyle="0" rx="6" ry="8" stroke-width="0.445094"/>
    <ellipse cx="16" cy="8" fillStyle="0" rx="6" ry="8.5" stroke-width="0.445094"/>
    <ellipse cx="7" cy="14" fillStyle="0" rx="6" ry="8" stroke-width="0.445094"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="22" x2="25" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="25" x2="25" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="28" x2="25" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="13" x2="16" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="16" x2="16" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="19" x2="16" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.781768" x1="8" x2="9" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.787381" x1="6" x2="3" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.749893" x1="3" x2="9" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="16" x2="16" y1="6" y2="3"/>
   </symbol>
   <symbol id="voltageTransformer:shape36">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline points="19,9 35,9 35,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2672160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2672fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26739b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2674170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26752a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2675ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2676600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2676ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26786e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26786e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2679b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2679b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_267bf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267db90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_267e780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_267f210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_267fb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2681210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2681c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2682390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2682b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26835f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2683f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2684a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2685420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2686a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_26873a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2688390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2697850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_268a650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_268ad00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_268bf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1283" width="2526" x="3111" y="-1248"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a34d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 992.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26301e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.000000 977.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b8e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 1007.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 780.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d3250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 765.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d3420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 795.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d3b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 992.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d3d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5019.000000 977.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2182240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 1007.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21828e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 717.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2182ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 747.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2182ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 702.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c78e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3981.000000 732.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c7fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5025.000000 717.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c81f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5011.000000 747.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f9990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5025.000000 702.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f9bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5000.000000 732.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fa5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.000000 404.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21fa790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 434.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285d140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.000000 389.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285d340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.000000 419.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18fff20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4954.000000 434.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19003e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4967.000000 389.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1900620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4943.000000 419.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1900860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 404.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19009d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 934.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2196fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.000000 919.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2197510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4273.000000 906.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18f2ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 891.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18f2d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 963.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18f2fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 948.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18f32d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 934.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18d8010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4997.000000 919.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18d8250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 906.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18d8490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 891.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18d86d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 963.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18d8910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 948.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18d8c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 913.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18d8eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 898.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190de40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 884.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190e090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.500000 869.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190e2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4382.000000 856.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190ea80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 913.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190ecb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 898.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a96ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 884.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a97130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4659.500000 869.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a97370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4655.000000 856.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3519,-145 3528,-133 3537,-145 3519,-145 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3593,-145 3602,-133 3611,-145 3593,-145 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3663,-145 3672,-133 3681,-145 3663,-145 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-145 3743,-133 3752,-145 3734,-145 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3804,-145 3813,-133 3822,-145 3804,-145 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3877,-146 3886,-134 3895,-146 3877,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-146 3956,-134 3965,-146 3947,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4017,-146 4026,-134 4035,-146 4017,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4087,-146 4096,-134 4105,-146 4087,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-146 4169,-134 4178,-146 4160,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4230,-146 4239,-134 4248,-146 4230,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4301,-145 4310,-133 4319,-145 4301,-145 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-145 4380,-133 4389,-145 4371,-145 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-196 4453,-186 4458,-196 4449,-196 " stroke="rgb(127,127,127)" stroke-width="0.5"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-171 4453,-181 4458,-171 4449,-171 " stroke="rgb(127,127,127)" stroke-width="0.5"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-146 4640,-134 4649,-146 4631,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4701,-146 4710,-134 4719,-146 4701,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4772,-146 4781,-134 4790,-146 4772,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4842,-146 4851,-134 4860,-146 4842,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4915,-147 4924,-135 4933,-147 4915,-147 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4985,-147 4994,-135 5003,-147 4985,-147 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5055,-147 5064,-135 5073,-147 5055,-147 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5125,-147 5134,-135 5143,-147 5125,-147 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5198,-147 5207,-135 5216,-147 5198,-147 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5268,-147 5277,-135 5286,-147 5268,-147 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5339,-146 5348,-134 5357,-146 5339,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5409,-146 5418,-134 5427,-146 5409,-146 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5487,-197 5491,-187 5496,-197 5487,-197 " stroke="rgb(127,127,127)" stroke-width="0.5"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5487,-172 5491,-182 5496,-172 5487,-172 " stroke="rgb(127,127,127)" stroke-width="0.5"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5560,-145 5569,-133 5578,-145 5560,-145 " stroke="rgb(127,127,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3573,-491 3577,-481 3582,-491 3573,-491 " stroke="rgb(127,127,127)" stroke-width="0.5"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3573,-466 3577,-476 3582,-466 3573,-466 " stroke="rgb(127,127,127)" stroke-width="0.5"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5517,-490 5521,-480 5526,-490 5517,-490 " stroke="rgb(127,127,127)" stroke-width="0.5"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5517,-465 5521,-475 5526,-465 5517,-465 " stroke="rgb(127,127,127)" stroke-width="0.5"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4969,-507 4973,-519 4977,-507 4969,-507 " stroke="rgb(213,0,0)" stroke-width="0.4"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4969,-534 4973,-522 4977,-534 4969,-534 " stroke="rgb(213,0,0)" stroke-width="0.4"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-27764">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4885.052347 -918.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4495" ObjectName="SW-CX_CY.CX_CY_172BK"/>
     <cge:Meas_Ref ObjectId="27764"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27740">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4217.052347 -918.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4485" ObjectName="SW-CX_CY.CX_CY_171BK"/>
     <cge:Meas_Ref ObjectId="27740"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27783">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.052347 -809.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4503" ObjectName="SW-CX_CY.CX_CY_112BK"/>
     <cge:Meas_Ref ObjectId="27783"/>
    <cge:TPSR_Ref TObjectID="4503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27818">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4217.052347 -690.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4508" ObjectName="SW-CX_CY.CX_CY_101BK"/>
     <cge:Meas_Ref ObjectId="27818"/>
    <cge:TPSR_Ref TObjectID="4508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27869">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4904.052347 -690.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4517" ObjectName="SW-CX_CY.CX_CY_102BK"/>
     <cge:Meas_Ref ObjectId="27869"/>
    <cge:TPSR_Ref TObjectID="4517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27891">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4887.052347 -395.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4523" ObjectName="SW-CX_CY.CX_CY_002BK"/>
     <cge:Meas_Ref ObjectId="27891"/>
    <cge:TPSR_Ref TObjectID="4523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4521.052347 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 3521.052347 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 3595.052347 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 3665.052347 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 3736.052347 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 3806.052347 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 3879.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 3949.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4019.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4089.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4162.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4232.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4303.052347 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4373.052347 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4446.052347 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4633.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4703.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4774.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4844.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4917.052347 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4987.052347 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 5057.052347 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 5127.052347 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 5200.052347 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 5270.052347 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 5341.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 5411.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 5484.052347 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 5562.052347 -247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 0.704545 3570.052347 -391.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 0.704545 5514.052347 -390.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27840">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.764706 -0.000000 0.000000 -0.704545 4200.052347 -394.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4514" ObjectName="SW-CX_CY.CX_CY_001BK"/>
     <cge:Meas_Ref ObjectId="27840"/>
    <cge:TPSR_Ref TObjectID="4514"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_25a5780">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 -708.000000)" xlink:href="#voltageTransformer:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b3f50">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4773.000000 -708.000000)" xlink:href="#voltageTransformer:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_193b030">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3706.000000 -405.000000)" xlink:href="#voltageTransformer:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24c13a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5403.000000 -405.000000)" xlink:href="#voltageTransformer:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21847a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3530.000000 -200.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_194f230">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5584.000000 -202.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4207,-1241 4207,-1179 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4207,-1241 4207,-1179 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_CY" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanlTcy_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4894,-1179 4894,-1235 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="14402" ObjectName="AC-110kV.xieyanlTcy_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4894,-1179 4894,-1235 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(0.500000 -0.000000 0.000000 -0.537037 4447.000000 -18.000000)" xlink:href="#reactance:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(0.500000 -0.000000 0.000000 -0.537037 5485.000000 -19.000000)" xlink:href="#reactance:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_285fbe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.052347 -737.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_240c440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.052347 -899.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_248f7f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.052347 -965.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a8ea50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4962.052347 -1081.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18e1770" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4142.052347 -737.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b10c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4142.052347 -899.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bea8e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4142.052347 -965.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_222f2a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4140.052347 -1079.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2683a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.052347 -886.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2219b40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.052347 -886.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c14650" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4142.052347 -1025.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2243cb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4958.947653 -1026.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26779d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4142.052347 -813.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2236860" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.052347 -813.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c7d5a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4142.052347 -672.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223fa10" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4146.052347 -611.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a99740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4958.947653 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2200bc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4958.947653 -615.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20783a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4302.000000 -483.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_271dfd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4989.000000 -488.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27c1b10" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 3574.040450 -158.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cb430" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 3648.040450 -158.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a5fd00" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 3718.040450 -158.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2408ae0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 3789.040450 -159.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_244a290" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 3859.040450 -159.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18f55d0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 3932.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a95f0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4002.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22af790" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4072.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27b0e40" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4142.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24c8b70" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4215.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_241cca0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4285.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2493aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4356.040450 -159.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a98d00" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4426.040450 -159.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247df10" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4499.040450 -207.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f2f00" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4499.040450 -114.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_220dbb0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4686.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_280c5e0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4756.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257eb70" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4827.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f37c0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4897.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27cd8f0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 4970.040450 -161.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ab6900" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5040.040450 -161.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19234f0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5110.040450 -161.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_226ab30" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5180.040450 -161.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27dcf30" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5253.040450 -161.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28aa5e0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5323.040450 -161.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2617870" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5394.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28398b0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5464.040450 -160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227bd40" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5537.040450 -208.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aa5270" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5537.040450 -115.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_206f4a0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5615.040450 -159.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22598f0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 3623.040450 -436.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_199f840" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3669.000000 -383.000000)" xlink:href="#earth:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27bb6c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5366.000000 -383.000000)" xlink:href="#earth:shape8"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26667c0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.772727 -0.000000 0.000000 -0.750000 5567.040450 -435.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_27c3ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-743 4912,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4517@x" ObjectIDND1="4518@x" ObjectIDZND0="4519@0" Pin0InfoVect0LinkObjId="SW-27871_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27869_0" Pin1InfoVect1LinkObjId="SW-27870_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-743 4912,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_241d110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-743 4965,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4519@1" ObjectIDZND0="g_285fbe0@0" Pin0InfoVect0LinkObjId="g_285fbe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27871_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-743 4965,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24ce7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-905 4912,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4495@x" ObjectIDND1="4496@x" ObjectIDZND0="4497@0" Pin0InfoVect0LinkObjId="SW-27766_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27764_0" Pin1InfoVect1LinkObjId="SW-27765_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-905 4912,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24a5bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-905 4965,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4497@1" ObjectIDZND0="g_240c440@0" Pin0InfoVect0LinkObjId="g_240c440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-905 4965,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2619ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-971 4965,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4499@1" ObjectIDZND0="g_248f7f0@0" Pin0InfoVect0LinkObjId="g_248f7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27768_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-971 4965,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa80d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4950,-1087 4967,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4494@1" ObjectIDZND0="g_1a8ea50@0" Pin0InfoVect0LinkObjId="g_1a8ea50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27763_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4950,-1087 4967,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2663930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-743 4190,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4508@x" ObjectIDND1="4509@x" ObjectIDZND0="4510@1" Pin0InfoVect0LinkObjId="SW-27820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27818_0" Pin1InfoVect1LinkObjId="SW-27819_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-743 4190,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_280e990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-743 4137,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4510@0" ObjectIDZND0="g_18e1770@0" Pin0InfoVect0LinkObjId="g_18e1770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-743 4137,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24caf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-905 4190,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4485@x" ObjectIDND1="4486@x" ObjectIDZND0="4487@1" Pin0InfoVect0LinkObjId="SW-27742_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27740_0" Pin1InfoVect1LinkObjId="SW-27741_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-905 4190,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23fcd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-905 4137,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4487@0" ObjectIDZND0="g_24b10c0@0" Pin0InfoVect0LinkObjId="g_24b10c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-905 4137,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24ce5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-971 4137,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4489@0" ObjectIDZND0="g_1bea8e0@0" Pin0InfoVect0LinkObjId="g_1bea8e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-971 4137,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_18e4940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-1085 4135,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4484@0" ObjectIDZND0="g_222f2a0@0" Pin0InfoVect0LinkObjId="g_222f2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-1085 4135,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_265ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-819 4546,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4505@x" ObjectIDND1="4504@x" ObjectIDZND0="4503@1" Pin0InfoVect0LinkObjId="SW-27783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27785_0" Pin1InfoVect1LinkObjId="SW-27784_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-819 4546,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_216cae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-819 4514,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4503@x" ObjectIDND1="4504@x" ObjectIDZND0="4505@0" Pin0InfoVect0LinkObjId="SW-27785_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27783_0" Pin1InfoVect1LinkObjId="SW-27784_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-819 4514,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2abce80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-873 4514,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4505@1" ObjectIDZND0="g_2683a10@0" Pin0InfoVect0LinkObjId="g_2683a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27785_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-873 4514,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22386b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-819 4605,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4503@x" ObjectIDND1="4506@x" ObjectIDZND0="4507@0" Pin0InfoVect0LinkObjId="SW-27787_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27783_0" Pin1InfoVect1LinkObjId="SW-27786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-819 4605,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_226e7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-873 4605,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4507@1" ObjectIDZND0="g_2219b40@0" Pin0InfoVect0LinkObjId="g_2219b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27787_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-873 4605,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1be1320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4573,-819 4605,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4503@0" ObjectIDZND0="4507@x" ObjectIDZND1="4506@x" Pin0InfoVect0LinkObjId="SW-27787_0" Pin0InfoVect1LinkObjId="SW-27786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27783_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4573,-819 4605,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a489e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-846 4894,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4496@0" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_285d8e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-846 4894,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2676990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-905 4894,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4495@x" ObjectIDND1="4497@x" ObjectIDZND0="4496@1" Pin0InfoVect0LinkObjId="SW-27765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27764_0" Pin1InfoVect1LinkObjId="SW-27766_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-905 4894,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_18d6e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-905 4207,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4485@x" ObjectIDND1="4487@x" ObjectIDZND0="4486@1" Pin0InfoVect0LinkObjId="SW-27741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27740_0" Pin1InfoVect1LinkObjId="SW-27742_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-905 4207,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2684440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-926 4894,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4495@0" ObjectIDZND0="4496@x" ObjectIDZND1="4497@x" Pin0InfoVect0LinkObjId="SW-27765_0" Pin0InfoVect1LinkObjId="SW-27766_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-926 4894,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_27bede0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-926 4207,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4485@0" ObjectIDZND0="4486@x" ObjectIDZND1="4487@x" Pin0InfoVect0LinkObjId="SW-27741_0" Pin0InfoVect1LinkObjId="SW-27742_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-926 4207,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a229d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-984 4894,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4498@0" ObjectIDZND0="4495@x" ObjectIDZND1="4499@x" Pin0InfoVect0LinkObjId="SW-27764_0" Pin0InfoVect1LinkObjId="SW-27768_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-984 4894,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_227a4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-984 4207,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4488@0" ObjectIDZND0="4485@x" ObjectIDZND1="4489@x" Pin0InfoVect0LinkObjId="SW-27740_0" Pin0InfoVect1LinkObjId="SW-27744_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27743_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-984 4207,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_19346f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-819 4634,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4503@x" ObjectIDND1="4507@x" ObjectIDZND0="4506@0" Pin0InfoVect0LinkObjId="SW-27786_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27783_0" Pin1InfoVect1LinkObjId="SW-27787_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-819 4634,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2473d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4487,-819 4514,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4504@1" ObjectIDZND0="4503@x" ObjectIDZND1="4505@x" Pin0InfoVect0LinkObjId="SW-27783_0" Pin0InfoVect1LinkObjId="SW-27785_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27784_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4487,-819 4514,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a07ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-1142 4835,-1142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_2661420@0" ObjectIDND1="4493@x" ObjectIDND2="14402@1" ObjectIDZND0="g_27db280@0" Pin0InfoVect0LinkObjId="g_27db280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2661420_0" Pin1InfoVect1LinkObjId="SW-27762_0" Pin1InfoVect2LinkObjId="g_2a85be0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-1142 4835,-1142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21add50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-1142 4921,-1142 4921,-1132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_27db280@0" ObjectIDND1="4493@x" ObjectIDND2="14402@1" ObjectIDZND0="g_2661420@0" Pin0InfoVect0LinkObjId="g_2661420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27db280_0" Pin1InfoVect1LinkObjId="SW-27762_0" Pin1InfoVect2LinkObjId="g_2a85be0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-1142 4921,-1142 4921,-1132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_27e7c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-737 4328,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_25a5780@0" ObjectIDZND0="4492@0" Pin0InfoVect0LinkObjId="SW-27756_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a5780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-737 4328,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2275870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4788,-737 4788,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_28b3f50@0" ObjectIDZND0="4502@0" Pin0InfoVect0LinkObjId="SW-27780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b3f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4788,-737 4788,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21e5a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4183,-1132 4183,-1141 4207,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2866e30@0" ObjectIDZND0="g_28a7fb0@0" ObjectIDZND1="4483@x" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_28a7fb0_0" Pin0InfoVect1LinkObjId="SW-27738_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2866e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4183,-1132 4183,-1141 4207,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_259f6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-1141 4264,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_2866e30@0" ObjectIDND1="4483@x" ObjectIDND2="0@1" ObjectIDZND0="g_28a7fb0@0" Pin0InfoVect0LinkObjId="g_28a7fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2866e30_0" Pin1InfoVect1LinkObjId="SW-27738_0" Pin1InfoVect2LinkObjId="SW-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-1141 4264,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ab5b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-1031 4190,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1acc210@0" ObjectIDND1="4488@x" ObjectIDZND0="4490@1" Pin0InfoVect0LinkObjId="SW-27745_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1acc210_0" Pin1InfoVect1LinkObjId="SW-27743_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-1031 4190,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1fddcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-1031 4137,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4490@0" ObjectIDZND0="g_1c14650@0" Pin0InfoVect0LinkObjId="g_1c14650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-1031 4137,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a1a1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-1032 4911,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_27193d0@0" ObjectIDND1="4498@x" ObjectIDZND0="4500@0" Pin0InfoVect0LinkObjId="SW-27769_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27193d0_0" Pin1InfoVect1LinkObjId="SW-27767_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-1032 4911,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_275f6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-1032 4964,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4500@1" ObjectIDZND0="g_2243cb0@0" Pin0InfoVect0LinkObjId="g_2243cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-1032 4964,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21af800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-1031 4207,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1acc210@0" ObjectIDND1="4490@x" ObjectIDZND0="4488@1" Pin0InfoVect0LinkObjId="SW-27743_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1acc210_0" Pin1InfoVect1LinkObjId="SW-27745_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-1031 4207,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_266c400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-1020 4894,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4498@1" ObjectIDZND0="g_27193d0@0" ObjectIDZND1="4500@x" Pin0InfoVect0LinkObjId="g_27193d0_0" Pin0InfoVect1LinkObjId="SW-27769_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27767_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-1020 4894,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2abb890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-1050 4207,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1acc210@0" ObjectIDZND0="4488@x" ObjectIDZND1="4490@x" Pin0InfoVect0LinkObjId="SW-27743_0" Pin0InfoVect1LinkObjId="SW-27745_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acc210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-1050 4207,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21d0730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-1031 4894,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4498@x" ObjectIDND1="4500@x" ObjectIDZND0="g_27193d0@0" Pin0InfoVect0LinkObjId="g_27193d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27767_0" Pin1InfoVect1LinkObjId="SW-27769_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-1031 4894,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24b64c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-819 4137,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4491@0" ObjectIDZND0="g_26779d0@0" Pin0InfoVect0LinkObjId="g_26779d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-819 4137,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2abdd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-819 4964,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4501@1" ObjectIDZND0="g_2236860@0" Pin0InfoVect0LinkObjId="g_2236860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-819 4964,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21d6bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-678 4192,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4508@x" ObjectIDND1="4511@x" ObjectIDZND0="4512@1" Pin0InfoVect0LinkObjId="SW-27822_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27818_0" Pin1InfoVect1LinkObjId="SW-27821_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-678 4192,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_20744b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-678 4137,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4512@0" ObjectIDZND0="g_1c7d5a0@0" Pin0InfoVect0LinkObjId="g_1c7d5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27822_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-678 4137,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1900e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-678 4207,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4511@x" ObjectIDND1="4512@x" ObjectIDZND0="4508@0" Pin0InfoVect0LinkObjId="SW-27818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27821_0" Pin1InfoVect1LinkObjId="SW-27822_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-678 4207,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2728330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-617 4196,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1ace870@0" ObjectIDND1="4511@x" ObjectIDZND0="4513@1" Pin0InfoVect0LinkObjId="SW-27823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ace870_0" Pin1InfoVect1LinkObjId="SW-27821_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-617 4196,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2190f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-617 4141,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4513@0" ObjectIDZND0="g_223fa10@0" Pin0InfoVect0LinkObjId="g_223fa10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-617 4141,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a4c0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-743 4207,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4508@x" ObjectIDND1="4510@x" ObjectIDZND0="4509@0" Pin0InfoVect0LinkObjId="SW-27819_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27818_0" Pin1InfoVect1LinkObjId="SW-27820_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-743 4207,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1994e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-725 4207,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4508@1" ObjectIDZND0="4509@x" ObjectIDZND1="4510@x" Pin0InfoVect0LinkObjId="SW-27819_0" Pin0InfoVect1LinkObjId="SW-27820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27818_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-725 4207,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf65c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-617 4207,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1ace870@0" ObjectIDND1="4513@x" ObjectIDZND0="4511@0" Pin0InfoVect0LinkObjId="SW-27821_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ace870_0" Pin1InfoVect1LinkObjId="SW-27823_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-617 4207,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1978070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-666 4207,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4511@1" ObjectIDZND0="4508@x" ObjectIDZND1="4512@x" Pin0InfoVect0LinkObjId="SW-27818_0" Pin0InfoVect1LinkObjId="SW-27822_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27821_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-666 4207,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24c23f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-684 4911,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4517@x" ObjectIDND1="4520@x" ObjectIDZND0="4521@0" Pin0InfoVect0LinkObjId="SW-27873_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27869_0" Pin1InfoVect1LinkObjId="SW-27872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-684 4911,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2492830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-684 4965,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4521@1" ObjectIDZND0="g_1a99740@0" Pin0InfoVect0LinkObjId="g_1a99740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27873_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-684 4965,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2492a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-621 4911,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_227fa90@0" ObjectIDND1="4520@x" ObjectIDZND0="4522@0" Pin0InfoVect0LinkObjId="SW-27874_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_227fa90_0" Pin1InfoVect1LinkObjId="SW-27872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-621 4911,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28a9800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-621 4964,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4522@1" ObjectIDZND0="g_2200bc0@0" Pin0InfoVect0LinkObjId="g_2200bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27874_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-621 4964,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28b6eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-679 4894,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4520@x" ObjectIDND1="4521@x" ObjectIDZND0="4517@0" Pin0InfoVect0LinkObjId="SW-27869_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27872_0" Pin1InfoVect1LinkObjId="SW-27873_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-679 4894,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_27e72f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-670 4894,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4520@1" ObjectIDZND0="4517@x" ObjectIDZND1="4521@x" Pin0InfoVect0LinkObjId="SW-27869_0" Pin0InfoVect1LinkObjId="SW-27873_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27872_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-670 4894,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2756730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-621 4894,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_227fa90@0" ObjectIDND1="4522@x" ObjectIDZND0="4520@0" Pin0InfoVect0LinkObjId="SW-27872_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_227fa90_0" Pin1InfoVect1LinkObjId="SW-27874_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-621 4894,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24baa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-564 4894,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="4480@1" ObjectIDZND0="g_227fa90@0" Pin0InfoVect0LinkObjId="g_227fa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-564 4894,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_282fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-607 4894,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_227fa90@1" ObjectIDZND0="4520@x" ObjectIDZND1="4522@x" Pin0InfoVect0LinkObjId="SW-27872_0" Pin0InfoVect1LinkObjId="SW-27874_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_227fa90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-607 4894,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_279c380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-558 4207,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="11887@1" ObjectIDZND0="g_1ace870@0" Pin0InfoVect0LinkObjId="g_1ace870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-558 4207,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_283a410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-602 4207,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ace870@1" ObjectIDZND0="4511@x" ObjectIDZND1="4513@x" Pin0InfoVect0LinkObjId="SW-27821_0" Pin0InfoVect1LinkObjId="SW-27823_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ace870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-602 4207,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23d9540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-527 4264,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_218a890@0" ObjectIDZND0="4516@x" Pin0InfoVect0LinkObjId="SW-27843_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218a890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-527 4264,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2416c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-539 4264,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_218a890@0" ObjectIDZND1="4516@x" Pin0InfoVect0LinkObjId="g_218a890_0" Pin0InfoVect1LinkObjId="SW-27843_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-539 4264,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2229390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-539 4308,-539 4308,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_218a890@0" ObjectIDZND0="4516@1" Pin0InfoVect0LinkObjId="SW-27843_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218a890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-539 4308,-539 4308,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2247980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4973,-497 4995,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" EndDevType1="switch" ObjectIDZND0="g_271dfd0@0" ObjectIDZND1="4525@x" Pin0InfoVect0LinkObjId="g_271dfd0_0" Pin0InfoVect1LinkObjId="SW-27894_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4973,-497 4995,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2215b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-483 4995,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_271dfd0@0" ObjectIDZND0="4525@x" Pin0InfoVect0LinkObjId="SW-27894_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_271dfd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-483 4995,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21d79b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-497 4995,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_271dfd0@0" ObjectIDZND0="4525@0" Pin0InfoVect0LinkObjId="SW-27894_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_271dfd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-497 4995,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2710790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-532 4951,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1986780@0" ObjectIDZND0="4525@x" Pin0InfoVect0LinkObjId="SW-27894_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1986780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-532 4951,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24720e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4895,-544 4951,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_1986780@0" ObjectIDZND1="4525@x" Pin0InfoVect0LinkObjId="g_1986780_0" Pin0InfoVect1LinkObjId="SW-27894_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4895,-544 4951,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2224af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-544 4995,-544 4995,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1986780@0" ObjectIDZND0="4525@1" Pin0InfoVect0LinkObjId="SW-27894_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1986780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-544 4995,-544 4995,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ee730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-477 4207,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="11887@0" ObjectIDZND0="4515@0" Pin0InfoVect0LinkObjId="SW-27841_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-477 4207,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20284f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-373 4207,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15738@1" ObjectIDZND0="4479@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27841_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-373 4207,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_259ff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-964 4894,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4498@x" ObjectIDND1="4499@x" ObjectIDZND0="4495@1" Pin0InfoVect0LinkObjId="SW-27764_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27767_0" Pin1InfoVect1LinkObjId="SW-27768_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-964 4894,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_218fd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-964 4207,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4488@x" ObjectIDND1="4489@x" ObjectIDZND0="4485@1" Pin0InfoVect0LinkObjId="SW-27740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-27743_0" Pin1InfoVect1LinkObjId="SW-27744_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-964 4207,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26f2780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-971 4894,-971 4894,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4499@0" ObjectIDZND0="4495@x" ObjectIDZND1="4498@x" Pin0InfoVect0LinkObjId="SW-27764_0" Pin0InfoVect1LinkObjId="SW-27767_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-971 4894,-971 4894,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1913ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-971 4207,-971 4207,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4489@1" ObjectIDZND0="4485@x" ObjectIDZND1="4488@x" Pin0InfoVect0LinkObjId="SW-27740_0" Pin0InfoVect1LinkObjId="SW-27743_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27744_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-971 4207,-971 4207,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a050e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-482 4894,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="4480@0" ObjectIDZND0="4524@0" Pin0InfoVect0LinkObjId="SW-27892_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-482 4894,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2279c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-373 4894,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15739@1" ObjectIDZND0="4481@0" Pin0InfoVect0LinkObjId="g_25a2cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27892_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-373 4894,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2279990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-428 4894,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4524@1" ObjectIDZND0="4523@0" Pin0InfoVect0LinkObjId="SW-27891_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27892_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-428 4894,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a3a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-398 4894,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4523@1" ObjectIDZND0="15739@0" Pin0InfoVect0LinkObjId="SW-27892_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27891_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-398 4894,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19029f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4528,-319 4528,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4528,-319 4528,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25a1630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4528,-284 4528,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4528,-284 4528,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25a3130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4528,-254 4528,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4528,-254 4528,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a2cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4589,-300 4589,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="4481@0" Pin0InfoVect0LinkObjId="g_2279c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4589,-300 4589,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a1b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-319 3528,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-319 3528,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be9470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-281 3528,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-281 3528,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27336b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-250 3528,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-250 3528,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2190ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3578,-163 3565,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27c1b10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27c1b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3578,-163 3565,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21a77a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3539,-163 3528,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_21847a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_21847a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3539,-163 3528,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2222220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-163 3528,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_21847a0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21847a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-163 3528,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f4050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-319 3602,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-319 3602,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22083b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-281 3602,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-281 3602,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2471220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-250 3602,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-250 3602,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_197b1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-163 3639,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24cb430@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cb430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-163 3639,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18d4100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-163 3602,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-163 3602,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2220240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-163 3602,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-163 3602,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a68730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-226 3602,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-226 3602,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e55c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3672,-319 3672,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3672,-319 3672,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a46940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3672,-281 3672,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3672,-281 3672,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_261bf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3672,-250 3672,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3672,-250 3672,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2206df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-163 3709,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a5fd00@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a5fd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-163 3709,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_224bde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-163 3672,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-163 3672,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1927910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3672,-163 3672,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3672,-163 3672,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22133d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3672,-226 3672,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3672,-226 3672,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22101f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-319 3743,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-319 3743,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28424b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-281 3743,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-281 3743,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a4f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-250 3743,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-250 3743,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_193c710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-163 3780,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2408ae0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2408ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-163 3780,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a484b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3754,-163 3743,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3754,-163 3743,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2730100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-163 3743,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-163 3743,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1febef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-226 3743,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-226 3743,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2684e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-319 3813,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-319 3813,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18e51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-281 3813,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-281 3813,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2478610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-250 3813,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-250 3813,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_279ce00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3863,-163 3850,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_244a290@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_244a290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3863,-163 3850,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2481350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-163 3813,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-163 3813,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_221c200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-163 3813,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-163 3813,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1991fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-226 3813,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-226 3813,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b6dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3886,-319 3886,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3886,-319 3886,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a20650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3886,-282 3886,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3886,-282 3886,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1936340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3886,-251 3886,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3886,-251 3886,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2232b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-164 3923,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_18f55d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18f55d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-164 3923,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be15b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3897,-164 3886,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3897,-164 3886,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19d6970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3886,-164 3886,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3886,-164 3886,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21637e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3886,-227 3886,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3886,-227 3886,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24c4310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-319 3956,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-319 3956,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26f48e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-282 3956,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-282 3956,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24b2270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-251 3956,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-251 3956,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c14000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4006,-164 3993,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25a95f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a95f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4006,-164 3993,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25bc3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-164 3956,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-164 3956,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18ed490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-164 3956,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-164 3956,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18df810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-227 3956,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-227 3956,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22004e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-319 4026,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-319 4026,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a90eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-282 4026,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-282 4026,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2896420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-251 4026,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-251 4026,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21ad810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-164 4063,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22af790@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22af790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-164 4063,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18eb760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4037,-164 4026,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4037,-164 4026,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2075670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-164 4026,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-164 4026,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2204360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-227 4026,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-227 4026,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2895bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-319 4096,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-319 4096,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2585fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-282 4096,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-282 4096,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_288c8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-251 4096,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-251 4096,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e3720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4146,-164 4133,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27b0e40@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b0e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4146,-164 4133,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1910c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-164 4096,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-164 4096,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19880a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-164 4096,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-164 4096,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_197e530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-227 4096,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-227 4096,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24725a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-319 4169,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-319 4169,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25844a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-282 4169,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-282 4169,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_260a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-251 4169,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-251 4169,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2abc470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-164 4206,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24c8b70@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24c8b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-164 4206,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1970c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-164 4169,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-164 4169,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18f1a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-164 4169,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-164 4169,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1990be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-227 4169,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-227 4169,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c3090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-319 4239,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-319 4239,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28bca60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-282 4239,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-282 4239,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28583b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-251 4239,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-251 4239,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21f4ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-164 4276,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_241cca0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_241cca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-164 4276,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a99990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4250,-164 4239,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4250,-164 4239,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a93710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-164 4239,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-164 4239,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a877c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-227 4239,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-227 4239,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_270f1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-319 4310,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-319 4310,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_281bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-281 4310,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-281 4310,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_281bf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-250 4310,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-250 4310,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1901890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-163 4347,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2493aa0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2493aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-163 4347,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1901ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-163 4310,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-163 4310,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25a99b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-163 4310,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-163 4310,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25a9c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-226 4310,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-226 4310,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be2be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-319 4380,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-319 4380,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be2e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-281 4380,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-281 4380,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_287c1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-250 4380,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-250 4380,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a4e460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-163 4417,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a98d00@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a98d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-163 4417,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2179820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4391,-163 4380,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4391,-163 4380,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2179a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-163 4380,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-163 4380,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24a5e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-226 4380,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-226 4380,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1942250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-319 4453,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-319 4453,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19440f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-281 4453,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-281 4453,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1944350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-250 4453,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-250 4453,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_287a6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4503,-211 4490,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_247df10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_247df10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4503,-211 4490,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28b3510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-211 4453,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-211 4453,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24ad980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-226 4453,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-226 4453,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c77330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-158 4453,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-158 4453,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23f52b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4503,-118 4490,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25f2f00@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25f2f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4503,-118 4490,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23f5510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-118 4453,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-118 4453,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2487210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-103 4453,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-103 4453,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2487450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-118 4453,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-118 4453,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_248ee70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-79 4453,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_248f0d0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_248f0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-79 4453,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c888a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-81 4453,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" ObjectIDND0="g_248f0d0@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_248f0d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-81 4453,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24cde60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-44 4453,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-44 4453,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a436b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4435,-68 4453,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="g_248f0d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_248f0d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4435,-68 4453,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_220e5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-61 4453,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_248f0d0@0" Pin0InfoVect0LinkObjId="g_248f0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-61 4453,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_220e820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-68 4453,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_248f0d0@0" Pin0InfoVect0LinkObjId="g_248f0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-68 4453,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19286e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-69 4428,-12 4453,-12 4453,-21 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-69 4428,-12 4453,-12 4453,-21 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_266eb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4640,-319 4640,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4640,-319 4640,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_266edc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4640,-282 4640,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4640,-282 4640,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27d7e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4640,-251 4640,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4640,-251 4640,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9aec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4690,-164 4677,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_220dbb0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_220dbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4690,-164 4677,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9b120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4651,-164 4640,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4651,-164 4640,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27dc0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4640,-164 4640,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4640,-164 4640,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27dc340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4640,-227 4640,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4640,-227 4640,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2233db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4710,-319 4710,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4710,-319 4710,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2234010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4710,-282 4710,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4710,-282 4710,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c06750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4710,-251 4710,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4710,-251 4710,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aba6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-164 4747,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_280c5e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_280c5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-164 4747,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2217750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-164 4710,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-164 4710,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2217980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4710,-164 4710,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4710,-164 4710,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2217be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4710,-227 4710,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4710,-227 4710,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1916a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-319 4781,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-319 4781,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28562b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-282 4781,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-282 4781,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28564f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-251 4781,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-251 4781,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21dd250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-164 4818,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_257eb70@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257eb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-164 4818,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21dd490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4792,-164 4781,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4792,-164 4781,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21dd6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-164 4781,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-164 4781,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27c1d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-227 4781,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-227 4781,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2603760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-319 4851,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-319 4851,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2886740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-282 4851,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-282 4851,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28869a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-251 4851,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-251 4851,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be6510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4901,-164 4888,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25f37c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25f37c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4901,-164 4888,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be6770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-164 4851,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4862,-164 4851,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be69d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-164 4851,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-164 4851,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fa190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-227 4851,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-227 4851,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f1b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-319 4924,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-319 4924,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_259e8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-283 4924,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-283 4924,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_259eb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-252 4924,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-252 4924,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24b1f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4974,-165 4961,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27cd8f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27cd8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4974,-165 4961,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fb2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4935,-165 4924,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4935,-165 4924,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fb540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-165 4924,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-165 4924,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fb7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-228 4924,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-228 4924,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19a44b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-319 4994,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-319 4994,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19a4710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-283 4994,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-283 4994,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2582f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-252 4994,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-252 4994,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_193f4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5044,-165 5031,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ab6900@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ab6900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5044,-165 5031,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_193f750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-165 4994,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-165 4994,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_193f9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-165 4994,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-165 4994,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2268ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4994,-228 4994,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4994,-228 4994,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2202530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-319 5064,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-319 5064,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2202790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-283 5064,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-283 5064,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22029f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-252 5064,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-252 5064,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_223e710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5114,-165 5101,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19234f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19234f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5114,-165 5101,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2230180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-165 5064,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-165 5064,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22303b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-165 5064,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-165 5064,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2230610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-228 5064,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-228 5064,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_203f510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5134,-319 5134,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5134,-319 5134,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_203f770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5134,-283 5134,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5134,-283 5134,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2186a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5134,-252 5134,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5134,-252 5134,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23efa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5184,-165 5171,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_226ab30@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_226ab30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5184,-165 5171,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_249eaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5145,-165 5134,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5145,-165 5134,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_249ed20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5134,-165 5134,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5134,-165 5134,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_249ef80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5134,-228 5134,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5134,-228 5134,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a4990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-319 5207,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-319 5207,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28a4bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-283 5207,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-283 5207,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28a4e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-252 5207,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-252 5207,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fc7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5257,-165 5244,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27dcf30@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27dcf30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5257,-165 5244,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5218,-165 5207,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5218,-165 5207,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2404570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-165 5207,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-165 5207,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24047d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-228 5207,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-228 5207,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_276b380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5277,-319 5277,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5277,-319 5277,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2246bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5277,-283 5277,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5277,-283 5277,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2246e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5277,-252 5277,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5277,-252 5277,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22455b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5327,-165 5314,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28aa5e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28aa5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5327,-165 5314,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27ac5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5288,-165 5277,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5288,-165 5277,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27ac800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5277,-165 5277,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5277,-165 5277,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27aca60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5277,-228 5277,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5277,-228 5277,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_284ec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-319 5348,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-319 5348,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_284eee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-282 5348,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-282 5348,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_284f140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-251 5348,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-251 5348,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e0a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5398,-164 5385,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2617870@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2617870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5398,-164 5385,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e0cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5359,-164 5348,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5359,-164 5348,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e0f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-164 5348,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-164 5348,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23e17e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5348,-227 5348,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5348,-227 5348,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288a0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5418,-319 5418,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5418,-319 5418,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be03a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5418,-282 5418,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5418,-282 5418,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be0600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5418,-251 5418,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5418,-251 5418,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2495d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5468,-164 5455,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28398b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28398b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5468,-164 5455,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2495ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5429,-164 5418,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5429,-164 5418,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2496250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5418,-164 5418,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5418,-164 5418,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_206ab70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5418,-227 5418,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5418,-227 5418,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25be690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-319 5491,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-319 5491,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25be8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-282 5491,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-282 5491,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25beb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-251 5491,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-251 5491,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_257fbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5541,-212 5528,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_227bd40@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_227bd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5541,-212 5528,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_257fe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5502,-212 5491,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5502,-212 5491,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_287ae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-227 5491,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-227 5491,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28476f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-159 5491,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-159 5491,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2239060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5541,-119 5528,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2aa5270@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa5270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5541,-119 5528,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22392c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5502,-119 5491,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5502,-119 5491,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2848fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-104 5491,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-104 5491,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28491f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-119 5491,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-119 5491,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2849590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5506,-80 5491,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_28497d0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28497d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5506,-80 5491,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_279ac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-82 5491,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" ObjectIDND0="g_28497d0@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28497d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-82 5491,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2804180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-45 5491,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-45 5491,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab7e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5473,-69 5491,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="g_28497d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_28497d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5473,-69 5491,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e2850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-62 5491,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28497d0@0" Pin0InfoVect0LinkObjId="g_28497d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-62 5491,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e2ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5491,-69 5491,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_28497d0@0" Pin0InfoVect0LinkObjId="g_28497d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5491,-69 5491,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21e2d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5466,-70 5466,-13 5491,-13 5491,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5466,-70 5466,-13 5491,-13 5491,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24499d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5569,-319 5569,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5569,-319 5569,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2449c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5569,-281 5569,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5569,-281 5569,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27a3690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5569,-250 5569,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5569,-250 5569,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28100b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5619,-163 5606,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_206f4a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206f4a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5619,-163 5606,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2810310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5580,-163 5569,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_194f230@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_194f230_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5580,-163 5569,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19ea020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5569,-163 5569,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_194f230@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_194f230_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5569,-163 5569,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_219b830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-319 3577,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-319 3577,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_219ba90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-357 3577,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-357 3577,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_219bcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-388 3577,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-388 3577,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_225a320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-440 3614,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22598f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22598f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-440 3614,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1969bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-440 3577,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-440 3577,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19735b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-412 3577,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-412 3577,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19737f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-516 3577,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-516 3577,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f9900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-356 3721,-356 3721,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_199f840@0" ObjectIDND1="0@x" ObjectIDZND0="g_193b030@0" Pin0InfoVect0LinkObjId="g_193b030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_199f840_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-356 3721,-356 3721,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f9b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3674,-386 3674,-356 3698,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_199f840@0" ObjectIDZND0="0@x" ObjectIDZND1="g_193b030@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_193b030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_199f840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3674,-386 3674,-356 3698,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c38bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-319 3698,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4479@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-319 3698,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c38e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-345 3698,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" EndDevType1="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_199f840@0" ObjectIDZND1="g_193b030@0" Pin0InfoVect0LinkObjId="g_199f840_0" Pin0InfoVect1LinkObjId="g_193b030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-345 3698,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c39070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5395,-356 5418,-356 5418,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_27bb6c0@0" ObjectIDND1="0@x" ObjectIDZND0="g_24c13a0@0" Pin0InfoVect0LinkObjId="g_24c13a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27bb6c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5395,-356 5418,-356 5418,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c392d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5371,-386 5371,-356 5395,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_27bb6c0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24c13a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_24c13a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27bb6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5371,-386 5371,-356 5395,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27fe990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5395,-345 5395,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" EndDevType1="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_27bb6c0@0" ObjectIDZND1="g_24c13a0@0" Pin0InfoVect0LinkObjId="g_27bb6c0_0" Pin0InfoVect1LinkObjId="g_24c13a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5395,-345 5395,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27febc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5395,-319 5395,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5395,-319 5395,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d1510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5521,-319 5521,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2279c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5521,-319 5521,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27d1770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5521,-356 5521,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5521,-356 5521,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27d19d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5521,-387 5521,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5521,-387 5521,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1993d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5571,-439 5558,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_26667c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26667c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5571,-439 5558,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1993fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5532,-439 5521,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5532,-439 5521,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1994200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5521,-411 5521,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5521,-411 5521,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2411540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5521,-515 5521,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5521,-515 5521,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2204ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4189,-1085 4208,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4484@1" ObjectIDZND0="g_1acc210@0" ObjectIDZND1="4483@x" Pin0InfoVect0LinkObjId="g_1acc210_0" Pin0InfoVect1LinkObjId="SW-27738_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27739_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4189,-1085 4208,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21f0310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-1075 4207,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1acc210@1" ObjectIDZND0="4483@x" ObjectIDZND1="4484@x" Pin0InfoVect0LinkObjId="SW-27738_0" Pin0InfoVect1LinkObjId="SW-27739_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acc210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-1075 4207,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21f0570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-1085 4207,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1acc210@0" ObjectIDND1="4484@x" ObjectIDZND0="4483@0" Pin0InfoVect0LinkObjId="SW-27738_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1acc210_0" Pin1InfoVect1LinkObjId="SW-27739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-1085 4207,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21f07d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-1133 4207,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4483@1" ObjectIDZND0="g_2866e30@0" ObjectIDZND1="g_28a7fb0@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_2866e30_0" Pin0InfoVect1LinkObjId="g_28a7fb0_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27738_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-1133 4207,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_190db90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-1087 4894,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4494@0" ObjectIDZND0="g_27193d0@0" ObjectIDZND1="4493@x" Pin0InfoVect0LinkObjId="g_27193d0_0" Pin0InfoVect1LinkObjId="SW-27762_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-1087 4894,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_281ded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-1075 4894,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_27193d0@1" ObjectIDZND0="4493@x" ObjectIDZND1="4494@x" Pin0InfoVect0LinkObjId="SW-27762_0" Pin0InfoVect1LinkObjId="SW-27763_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27193d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-1075 4894,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_27cb710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-1142 4894,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_2661420@0" ObjectIDND1="g_27db280@0" ObjectIDND2="14402@1" ObjectIDZND0="4493@1" Pin0InfoVect0LinkObjId="SW-27762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2661420_0" Pin1InfoVect1LinkObjId="g_27db280_0" Pin1InfoVect2LinkObjId="g_2a85be0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-1142 4894,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_27cb970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-1097 4894,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4493@0" ObjectIDZND0="g_27193d0@0" ObjectIDZND1="4494@x" Pin0InfoVect0LinkObjId="g_27193d0_0" Pin0InfoVect1LinkObjId="SW-27763_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-1097 4894,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2171e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-762 4894,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4518@0" ObjectIDZND0="4517@x" ObjectIDZND1="4519@x" Pin0InfoVect0LinkObjId="SW-27869_0" Pin0InfoVect1LinkObjId="SW-27871_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-762 4894,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2270140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-725 4894,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4517@1" ObjectIDZND0="4518@x" ObjectIDZND1="4519@x" Pin0InfoVect0LinkObjId="SW-27870_0" Pin0InfoVect1LinkObjId="SW-27871_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27869_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-725 4894,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f3450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4589,-283 4589,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4589,-283 4589,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27f3640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4589,-230 4589,-207 4528,-207 4528,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4589,-230 4589,-207 4528,-207 4528,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_226d630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4282,-492 4308,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" EndDevType1="switch" ObjectIDND0="g_27f3830@1" ObjectIDZND0="g_20783a0@0" ObjectIDZND1="4516@x" Pin0InfoVect0LinkObjId="g_20783a0_0" Pin0InfoVect1LinkObjId="SW-27843_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27f3830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4282,-492 4308,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_27dadc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-478 4308,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_20783a0@0" ObjectIDZND0="g_27f3830@0" ObjectIDZND1="4516@x" Pin0InfoVect0LinkObjId="g_27f3830_0" Pin0InfoVect1LinkObjId="SW-27843_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20783a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-478 4308,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_27db020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-491 4308,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_20783a0@0" ObjectIDND1="g_27f3830@0" ObjectIDZND0="4516@0" Pin0InfoVect0LinkObjId="SW-27843_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20783a0_0" Pin1InfoVect1LinkObjId="g_27f3830_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-491 4308,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_285d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4287,-804 4287,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2880060@0" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2880060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4287,-804 4287,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_285db10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-793 4328,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4492@1" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27756_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-793 4328,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_285dd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-819 4687,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4506@1" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27786_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-819 4687,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_285df70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4788,-794 4788,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4502@1" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4788,-794 4788,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_18ffc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-804 4828,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_287d330@0" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_287d330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-804 4828,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1932860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-219 3549,-219 3549,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_21847a0@0" Pin0InfoVect0LinkObjId="g_21847a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-219 3549,-219 3549,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1933330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-226 3528,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_21847a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_21847a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-226 3528,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1933590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-219 3528,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_21847a0@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21847a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3528,-219 3528,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25e9b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5569,-216 5603,-216 5603,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_194f230@0" Pin0InfoVect0LinkObjId="g_194f230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5569,-216 5603,-216 5603,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a84d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5569,-226 5569,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_194f230@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_194f230_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5569,-226 5569,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a84f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5569,-216 5569,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_194f230@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_194f230_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5569,-216 5569,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a85980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-1141 4207,-1179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2866e30@0" ObjectIDND1="g_28a7fb0@0" ObjectIDND2="4483@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2866e30_0" Pin1InfoVect1LinkObjId="g_28a7fb0_0" Pin1InfoVect2LinkObjId="SW-27738_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-1141 4207,-1179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a85be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-1142 4894,-1180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2661420@0" ObjectIDND1="g_27db280@0" ObjectIDND2="4493@x" ObjectIDZND0="14402@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2661420_0" Pin1InfoVect1LinkObjId="g_27db280_0" Pin1InfoVect2LinkObjId="SW-27762_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-1142 4894,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25bc830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-798 4894,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4518@1" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-798 4894,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25bca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4911,-819 4894,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4501@0" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4911,-819 4894,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25bd2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-846 4207,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4486@0" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-846 4207,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25bd520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-799 4207,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4509@1" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27819_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-799 4207,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25bd780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-819 4207,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4491@1" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27746_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-819 4207,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_280b410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4451,-819 4432,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4504@0" ObjectIDZND0="4477@0" Pin0InfoVect0LinkObjId="g_1a489e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4451,-819 4432,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2481790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-426 4207,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4515@1" ObjectIDZND0="4514@0" Pin0InfoVect0LinkObjId="SW-27840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27841_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-426 4207,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24819e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-397 4207,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4514@1" ObjectIDZND0="15738@0" Pin0InfoVect0LinkObjId="SW-27841_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-27840_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-397 4207,-391 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27661" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1007.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27661" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4485"/>
     <cge:Term_Ref ObjectID="6471"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27662" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1007.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4485"/>
     <cge:Term_Ref ObjectID="6471"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27658" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1007.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4485"/>
     <cge:Term_Ref ObjectID="6471"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1007.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4485"/>
     <cge:Term_Ref ObjectID="6471"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1007.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4485"/>
     <cge:Term_Ref ObjectID="6471"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1007.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4485"/>
     <cge:Term_Ref ObjectID="6471"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1007.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4485"/>
     <cge:Term_Ref ObjectID="6471"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-27657" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1007.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4485"/>
     <cge:Term_Ref ObjectID="6471"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-27656" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -1007.000000) translate(0,132)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4485"/>
     <cge:Term_Ref ObjectID="6471"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27691" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4562.000000 -792.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4503"/>
     <cge:Term_Ref ObjectID="6507"/>
    <cge:TPSR_Ref TObjectID="4503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27692" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4562.000000 -792.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4503"/>
     <cge:Term_Ref ObjectID="6507"/>
    <cge:TPSR_Ref TObjectID="4503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27688" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4562.000000 -792.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27688" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4503"/>
     <cge:Term_Ref ObjectID="6507"/>
    <cge:TPSR_Ref TObjectID="4503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27676" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -1007.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27676" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4495"/>
     <cge:Term_Ref ObjectID="6491"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27677" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -1007.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4495"/>
     <cge:Term_Ref ObjectID="6491"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27673" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -1007.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27673" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4495"/>
     <cge:Term_Ref ObjectID="6491"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -1007.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4495"/>
     <cge:Term_Ref ObjectID="6491"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -1007.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4495"/>
     <cge:Term_Ref ObjectID="6491"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -1007.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4495"/>
     <cge:Term_Ref ObjectID="6491"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -1007.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4495"/>
     <cge:Term_Ref ObjectID="6491"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-27672" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -1007.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27672" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4495"/>
     <cge:Term_Ref ObjectID="6491"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-27671" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -1007.000000) translate(0,132)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4495"/>
     <cge:Term_Ref ObjectID="6491"/>
    <cge:TPSR_Ref TObjectID="4495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56636" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4049.000000 -745.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4508"/>
     <cge:Term_Ref ObjectID="6517"/>
    <cge:TPSR_Ref TObjectID="4508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27697" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4049.000000 -745.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4508"/>
     <cge:Term_Ref ObjectID="6517"/>
    <cge:TPSR_Ref TObjectID="4508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27694" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4049.000000 -745.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27694" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4508"/>
     <cge:Term_Ref ObjectID="6517"/>
    <cge:TPSR_Ref TObjectID="4508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-27693" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4049.000000 -745.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4508"/>
     <cge:Term_Ref ObjectID="6517"/>
    <cge:TPSR_Ref TObjectID="4508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27718" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5066.000000 -745.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4517"/>
     <cge:Term_Ref ObjectID="6535"/>
    <cge:TPSR_Ref TObjectID="4517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27719" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5066.000000 -745.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4517"/>
     <cge:Term_Ref ObjectID="6535"/>
    <cge:TPSR_Ref TObjectID="4517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27715" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5066.000000 -745.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27715" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4517"/>
     <cge:Term_Ref ObjectID="6535"/>
    <cge:TPSR_Ref TObjectID="4517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-27714" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5066.000000 -745.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4517"/>
     <cge:Term_Ref ObjectID="6535"/>
    <cge:TPSR_Ref TObjectID="4517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27705" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -432.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4514"/>
     <cge:Term_Ref ObjectID="6529"/>
    <cge:TPSR_Ref TObjectID="4514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27706" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -432.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4514"/>
     <cge:Term_Ref ObjectID="6529"/>
    <cge:TPSR_Ref TObjectID="4514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27702" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -432.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4514"/>
     <cge:Term_Ref ObjectID="6529"/>
    <cge:TPSR_Ref TObjectID="4514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-27701" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4321.000000 -432.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4514"/>
     <cge:Term_Ref ObjectID="6529"/>
    <cge:TPSR_Ref TObjectID="4514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-27727" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -432.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4523"/>
     <cge:Term_Ref ObjectID="6547"/>
    <cge:TPSR_Ref TObjectID="4523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-27728" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -432.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27728" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4523"/>
     <cge:Term_Ref ObjectID="6547"/>
    <cge:TPSR_Ref TObjectID="4523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-27724" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -432.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27724" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4523"/>
     <cge:Term_Ref ObjectID="6547"/>
    <cge:TPSR_Ref TObjectID="4523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-27723" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -432.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4523"/>
     <cge:Term_Ref ObjectID="6547"/>
    <cge:TPSR_Ref TObjectID="4523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-27664" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -914.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4476"/>
     <cge:Term_Ref ObjectID="6460"/>
    <cge:TPSR_Ref TObjectID="4476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-27665" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -914.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4476"/>
     <cge:Term_Ref ObjectID="6460"/>
    <cge:TPSR_Ref TObjectID="4476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-27666" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -914.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27666" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4476"/>
     <cge:Term_Ref ObjectID="6460"/>
    <cge:TPSR_Ref TObjectID="4476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-27670" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -914.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4476"/>
     <cge:Term_Ref ObjectID="6460"/>
    <cge:TPSR_Ref TObjectID="4476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-27667" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -914.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27667" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4476"/>
     <cge:Term_Ref ObjectID="6460"/>
    <cge:TPSR_Ref TObjectID="4476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-27679" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -914.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27679" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4477"/>
     <cge:Term_Ref ObjectID="6461"/>
    <cge:TPSR_Ref TObjectID="4477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-27680" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -914.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4477"/>
     <cge:Term_Ref ObjectID="6461"/>
    <cge:TPSR_Ref TObjectID="4477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-27681" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -914.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4477"/>
     <cge:Term_Ref ObjectID="6461"/>
    <cge:TPSR_Ref TObjectID="4477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-27685" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -914.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27685" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4477"/>
     <cge:Term_Ref ObjectID="6461"/>
    <cge:TPSR_Ref TObjectID="4477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-27682" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -914.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4477"/>
     <cge:Term_Ref ObjectID="6461"/>
    <cge:TPSR_Ref TObjectID="4477"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3234" y="-1184"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3234" y="-1184"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3185" y="-1201"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3185" y="-1201"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3234" y="-1184"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3185" y="-1201"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.726459" x1="4419" x2="4419" y1="-66" y2="-72"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.747557" x1="4415" x2="4420" y1="-69" y2="-66"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.750018" x1="4417" x2="4419" y1="-71" y2="-72"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.726441" x1="4426" x2="4428" y1="-67" y2="-70"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.72647" x1="4428" x2="4430" y1="-69" y2="-69"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.726441" x1="4426" x2="4428" y1="-72" y2="-70"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="4447" x2="4447" y1="-17" y2="-17"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="4447" x2="4459" y1="-15" y2="-15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="4448" x2="4448" y1="-15" y2="-15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="4447" x2="4447" y1="-15" y2="-18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="4459" x2="4459" y1="-15" y2="-18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.726459" x1="5457" x2="5457" y1="-67" y2="-73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.747557" x1="5454" x2="5458" y1="-68" y2="-67"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.750018" x1="5453" x2="5457" y1="-70" y2="-73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.726441" x1="5464" x2="5466" y1="-68" y2="-71"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.72647" x1="5466" x2="5468" y1="-70" y2="-70"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="0.726441" x1="5464" x2="5466" y1="-73" y2="-71"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="5485" x2="5485" y1="-18" y2="-18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="5485" x2="5497" y1="-16" y2="-16"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="5486" x2="5486" y1="-16" y2="-16"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="5485" x2="5485" y1="-16" y2="-19"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="5497" x2="5497" y1="-16" y2="-19"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="3668" x2="3668" y1="-371" y2="-368"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="3692" x2="3692" y1="-368" y2="-368"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="3680" x2="3680" y1="-371" y2="-368"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="3668" x2="3680" y1="-371" y2="-371"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="5365" x2="5365" y1="-371" y2="-368"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="5389" x2="5389" y1="-368" y2="-368"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="5377" x2="5377" y1="-371" y2="-368"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(127,127,127)" stroke-width="1" x1="5365" x2="5377" y1="-371" y2="-371"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="4973" x2="4973" y1="-543" y2="-534"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="4973" x2="4973" y1="-498" y2="-507"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-27870">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4885.000000 -757.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4518" ObjectName="SW-CX_CY.CX_CY_1022SW"/>
     <cge:Meas_Ref ObjectId="27870"/>
    <cge:TPSR_Ref TObjectID="4518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27871">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4907.000000 -738.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4519" ObjectName="SW-CX_CY.CX_CY_10220SW"/>
     <cge:Meas_Ref ObjectId="27871"/>
    <cge:TPSR_Ref TObjectID="4519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27765">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4885.000000 -841.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4496" ObjectName="SW-CX_CY.CX_CY_1722SW"/>
     <cge:Meas_Ref ObjectId="27765"/>
    <cge:TPSR_Ref TObjectID="4496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27766">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4907.000000 -900.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4497" ObjectName="SW-CX_CY.CX_CY_17220SW"/>
     <cge:Meas_Ref ObjectId="27766"/>
    <cge:TPSR_Ref TObjectID="4497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27768">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4907.000000 -966.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4499" ObjectName="SW-CX_CY.CX_CY_17230SW"/>
     <cge:Meas_Ref ObjectId="27768"/>
    <cge:TPSR_Ref TObjectID="4499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27767">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4885.000000 -979.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4498" ObjectName="SW-CX_CY.CX_CY_1723SW"/>
     <cge:Meas_Ref ObjectId="27767"/>
    <cge:TPSR_Ref TObjectID="4498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27763">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4909.000000 -1082.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4494" ObjectName="SW-CX_CY.CX_CY_17267SW"/>
     <cge:Meas_Ref ObjectId="27763"/>
    <cge:TPSR_Ref TObjectID="4494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27819">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -758.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4509" ObjectName="SW-CX_CY.CX_CY_1011SW"/>
     <cge:Meas_Ref ObjectId="27819"/>
    <cge:TPSR_Ref TObjectID="4509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27820">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -738.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4510" ObjectName="SW-CX_CY.CX_CY_10110SW"/>
     <cge:Meas_Ref ObjectId="27820"/>
    <cge:TPSR_Ref TObjectID="4510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27741">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -841.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4486" ObjectName="SW-CX_CY.CX_CY_1711SW"/>
     <cge:Meas_Ref ObjectId="27741"/>
    <cge:TPSR_Ref TObjectID="4486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27742">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -900.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4487" ObjectName="SW-CX_CY.CX_CY_17110SW"/>
     <cge:Meas_Ref ObjectId="27742"/>
    <cge:TPSR_Ref TObjectID="4487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27744">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -966.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4489" ObjectName="SW-CX_CY.CX_CY_17130SW"/>
     <cge:Meas_Ref ObjectId="27744"/>
    <cge:TPSR_Ref TObjectID="4489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27743">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -979.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4488" ObjectName="SW-CX_CY.CX_CY_1713SW"/>
     <cge:Meas_Ref ObjectId="27743"/>
    <cge:TPSR_Ref TObjectID="4488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27739">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -1080.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4484" ObjectName="SW-CX_CY.CX_CY_17167SW"/>
     <cge:Meas_Ref ObjectId="27739"/>
    <cge:TPSR_Ref TObjectID="4484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27785">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.000000 -832.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4505" ObjectName="SW-CX_CY.CX_CY_11217SW"/>
     <cge:Meas_Ref ObjectId="27785"/>
    <cge:TPSR_Ref TObjectID="4505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27787">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.000000 -832.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4507" ObjectName="SW-CX_CY.CX_CY_11227SW"/>
     <cge:Meas_Ref ObjectId="27787"/>
    <cge:TPSR_Ref TObjectID="4507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27784">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4446.000000 -814.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4504" ObjectName="SW-CX_CY.CX_CY_1121SW"/>
     <cge:Meas_Ref ObjectId="27784"/>
    <cge:TPSR_Ref TObjectID="4504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27786">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 -814.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4506" ObjectName="SW-CX_CY.CX_CY_1122SW"/>
     <cge:Meas_Ref ObjectId="27786"/>
    <cge:TPSR_Ref TObjectID="4506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27756">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 -752.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4492" ObjectName="SW-CX_CY.CX_CY_1901SW"/>
     <cge:Meas_Ref ObjectId="27756"/>
    <cge:TPSR_Ref TObjectID="4492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27780">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -753.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4502" ObjectName="SW-CX_CY.CX_CY_1902SW"/>
     <cge:Meas_Ref ObjectId="27780"/>
    <cge:TPSR_Ref TObjectID="4502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27745">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -1026.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4490" ObjectName="SW-CX_CY.CX_CY_17137SW"/>
     <cge:Meas_Ref ObjectId="27745"/>
    <cge:TPSR_Ref TObjectID="4490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27769">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 -1027.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4500" ObjectName="SW-CX_CY.CX_CY_17237SW"/>
     <cge:Meas_Ref ObjectId="27769"/>
    <cge:TPSR_Ref TObjectID="4500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27746">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -814.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4491" ObjectName="SW-CX_CY.CX_CY_17117SW"/>
     <cge:Meas_Ref ObjectId="27746"/>
    <cge:TPSR_Ref TObjectID="4491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27770">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 -814.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4501" ObjectName="SW-CX_CY.CX_CY_17227SW"/>
     <cge:Meas_Ref ObjectId="27770"/>
    <cge:TPSR_Ref TObjectID="4501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27822">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -673.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4512" ObjectName="SW-CX_CY.CX_CY_10160SW"/>
     <cge:Meas_Ref ObjectId="27822"/>
    <cge:TPSR_Ref TObjectID="4512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27823">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 -612.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4513" ObjectName="SW-CX_CY.CX_CY_10167SW"/>
     <cge:Meas_Ref ObjectId="27823"/>
    <cge:TPSR_Ref TObjectID="4513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27821">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -625.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4511" ObjectName="SW-CX_CY.CX_CY_1016SW"/>
     <cge:Meas_Ref ObjectId="27821"/>
    <cge:TPSR_Ref TObjectID="4511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27872">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4885.000000 -629.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4520" ObjectName="SW-CX_CY.CX_CY_1026SW"/>
     <cge:Meas_Ref ObjectId="27872"/>
    <cge:TPSR_Ref TObjectID="4520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27873">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4521" ObjectName="SW-CX_CY.CX_CY_10260SW"/>
     <cge:Meas_Ref ObjectId="27873"/>
    <cge:TPSR_Ref TObjectID="4521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27874">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 -616.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4522" ObjectName="SW-CX_CY.CX_CY_10267SW"/>
     <cge:Meas_Ref ObjectId="27874"/>
    <cge:TPSR_Ref TObjectID="4522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27843">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.714286 -0.000000 0.000000 -0.717391 4302.000000 -501.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4516" ObjectName="SW-CX_CY.CX_CY_1010SW"/>
     <cge:Meas_Ref ObjectId="27843"/>
    <cge:TPSR_Ref TObjectID="4516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27894">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.714286 -0.000000 0.000000 -0.717391 4989.000000 -507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4525" ObjectName="SW-CX_CY.CX_CY_1020SW"/>
     <cge:Meas_Ref ObjectId="27894"/>
    <cge:TPSR_Ref TObjectID="4525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27892">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.052347 -421.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4524" ObjectName="SW-CX_CY.CX_CY_002XC"/>
     <cge:Meas_Ref ObjectId="27892"/>
    <cge:TPSR_Ref TObjectID="4524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27892">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.052347 -367.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15739" ObjectName="SW-CX_CY.CX_CY_002XC1"/>
     <cge:Meas_Ref ObjectId="27892"/>
    <cge:TPSR_Ref TObjectID="15739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.052347 -224.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.052347 -277.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 -276.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 -224.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3518.052347 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3518.052347 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 3535.000000 -160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.052347 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.052347 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 3609.000000 -160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3662.052347 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3662.052347 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 3679.000000 -160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.052347 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.052347 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 3750.000000 -160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3803.052347 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3803.052347 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 3820.000000 -160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 3893.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 3963.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4033.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4103.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4176.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4246.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.052347 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.052347 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4317.000000 -160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.052347 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.052347 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4387.000000 -160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.052347 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.052347 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4460.000000 -208.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.304348 -0.000000 0.000000 -2.000000 4448.000000 -130.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4460.000000 -115.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4647.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4717.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4788.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4858.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.052347 -276.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.052347 -222.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 4931.000000 -162.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.052347 -276.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.052347 -222.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5001.000000 -162.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.052347 -276.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.052347 -222.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5071.000000 -162.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.052347 -276.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.052347 -222.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5141.000000 -162.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5197.052347 -276.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5197.052347 -222.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5214.000000 -162.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5267.052347 -276.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5267.052347 -222.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5284.000000 -162.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5338.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5338.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5355.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5408.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5408.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5425.000000 -161.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5481.052347 -275.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5481.052347 -221.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5498.000000 -209.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.217391 -0.000000 0.000000 -1.857143 5488.000000 -133.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5498.000000 -116.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5559.052347 -274.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5559.052347 -220.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5576.000000 -160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3567.052347 -364.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3567.052347 -418.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 3584.000000 -437.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.700000 -0.000000 0.000000 -0.750000 3691.000000 -327.250000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.700000 -0.000000 0.000000 -0.750000 5388.000000 -327.250000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5511.052347 -363.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5511.052347 -417.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.739130 -0.000000 0.000000 -0.714286 5528.000000 -436.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27738">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -1092.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4483" ObjectName="SW-CX_CY.CX_CY_1716SW"/>
     <cge:Meas_Ref ObjectId="27738"/>
    <cge:TPSR_Ref TObjectID="4483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27762">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4885.000000 -1092.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4493" ObjectName="SW-CX_CY.CX_CY_1726SW"/>
     <cge:Meas_Ref ObjectId="27762"/>
    <cge:TPSR_Ref TObjectID="4493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27841">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.052347 -367.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15738" ObjectName="SW-CX_CY.CX_CY_001XC1"/>
     <cge:Meas_Ref ObjectId="27841"/>
    <cge:TPSR_Ref TObjectID="15738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27841">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.052347 -419.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4515" ObjectName="SW-CX_CY.CX_CY_001XC"/>
     <cge:Meas_Ref ObjectId="27841"/>
    <cge:TPSR_Ref TObjectID="4515"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.451613 -0.000000 0.000000 -0.416667 4446.000000 -53.000000)" xlink:href="#capacitor:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.451613 -0.000000 0.000000 -0.416667 5484.000000 -54.000000)" xlink:href="#capacitor:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2866e30">
    <use class="BV-110KV" transform="matrix(-0.764706 -0.000000 0.000000 -0.761244 4189.052347 -1093.131579)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2661420">
    <use class="BV-110KV" transform="matrix(0.764706 -0.000000 0.000000 -0.761244 4915.052347 -1093.131579)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_287d330">
    <use class="BV-110KV" transform="matrix(-0.764706 -0.000000 0.000000 -0.761244 4834.052347 -765.131579)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2880060">
    <use class="BV-110KV" transform="matrix(-0.764706 -0.000000 0.000000 -0.761244 4293.052347 -765.131579)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acc210">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.052347 -1045.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27193d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.052347 -1045.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ace870">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.052347 -572.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227fa90">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.052347 -577.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218a890">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.000000 -476.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1986780">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4943.000000 -481.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_248f0d0">
    <use class="BV-0KV" transform="matrix(-0.000000 0.764706 -0.761244 -0.000000 4506.868421 -85.052347)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28497d0">
    <use class="BV-0KV" transform="matrix(-0.000000 0.764706 -0.761244 -0.000000 5544.868421 -86.052347)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27f3830">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -0.779412 4278.000000 -489.000000)" xlink:href="#lightningRod:shape98"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27db280">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -1097.000000)" xlink:href="#lightningRod:shape119"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28a7fb0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4317.000000 -1096.000000)" xlink:href="#lightningRod:shape119"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19337f0">
    <use class="BV-0KV" transform="matrix(0.555556 -0.000000 0.000000 -0.357143 3544.000000 -200.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a851e0">
    <use class="BV-0KV" transform="matrix(0.444444 -0.000000 0.000000 -0.340909 5599.000000 -201.681818)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="4528" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="3528" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="3602" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="3672" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="3743" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="3813" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="3886" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="3956" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="4026" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="4096" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="4169" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="4239" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="4310" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="4380" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="4453" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="4589" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="4640" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="4710" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="4781" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="4851" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="4924" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="4994" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5064" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5134" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5207" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5277" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5348" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5418" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5491" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5569" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="3577" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="3698" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5395" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="5521" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4481" cx="4894" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4828" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4287" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4479" cx="4207" cy="-319" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4207" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4894" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4894" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4207" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4328" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4788" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4432" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4687" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4894" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4477" cx="4207" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a93970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3515.000000 -95.000000) translate(0,15)">保</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a93970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3515.000000 -95.000000) translate(0,33)">安</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a93970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3515.000000 -95.000000) translate(0,51)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a93970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3515.000000 -95.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a93970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3515.000000 -95.000000) translate(0,87)">(5000kW)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aa4680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -97.000000) translate(0,15)">23S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aa4680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -97.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aa4680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -97.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aa4680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -97.000000) translate(0,69)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3664.000000 -97.000000) translate(0,15)">21S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3664.000000 -97.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3664.000000 -97.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3664.000000 -97.000000) translate(0,69)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -97.000000) translate(0,15)">19S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -97.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -97.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25dfac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -97.000000) translate(0,69)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27fff50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -97.000000) translate(0,15)">17S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27fff50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -97.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27fff50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -97.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27fff50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -97.000000) translate(0,69)">四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22215e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -97.000000) translate(0,15)">15S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22215e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -97.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22215e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -97.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22215e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -97.000000) translate(0,69)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2410770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -97.000000) translate(0,15)">13S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2410770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -97.000000) translate(0,33)">制</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2410770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -97.000000) translate(0,51)">冷</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2410770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -97.000000) translate(0,69)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2410770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -97.000000) translate(0,87)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2410770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -97.000000) translate(0,105)">Ⅲ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2410770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -97.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -97.000000) translate(0,15)">11S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -97.000000) translate(0,33)">制</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -97.000000) translate(0,51)">冷</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -97.000000) translate(0,69)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -97.000000) translate(0,87)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -97.000000) translate(0,105)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -97.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195cb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -97.000000) translate(0,15)">9S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195cb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -97.000000) translate(0,33)">办</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195cb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -97.000000) translate(0,51)">公</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195cb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -97.000000) translate(0,69)">楼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195cb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -97.000000) translate(0,87)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_195cb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -97.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -97.000000) translate(0,15)">7S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -97.000000) translate(0,33)">卷</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -97.000000) translate(0,51)">包</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -97.000000) translate(0,69)">车</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -97.000000) translate(0,87)">间</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -97.000000) translate(0,105)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -97.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -97.000000) translate(0,15)">5S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -97.000000) translate(0,33)">制</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -97.000000) translate(0,51)">丝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -97.000000) translate(0,69)">车</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -97.000000) translate(0,87)">间</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -97.000000) translate(0,105)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdeea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -97.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4302.000000 -96.000000) translate(0,15)">3S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4302.000000 -96.000000) translate(0,33)">打</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4302.000000 -96.000000) translate(0,51)">叶</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4302.000000 -96.000000) translate(0,69)">复</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4302.000000 -96.000000) translate(0,87)">烤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4302.000000 -96.000000) translate(0,105)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b3770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4302.000000 -96.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a97700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -96.000000) translate(0,15)">1S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a97700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -96.000000) translate(0,33)">动</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a97700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -96.000000) translate(0,51)">力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a97700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -96.000000) translate(0,69)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a97700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -96.000000) translate(0,87)">心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a97700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -96.000000) translate(0,105)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a97700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -96.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24b3b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4406.000000 -7.000000) translate(0,15)">1号无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27accc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -98.000000) translate(0,15)">2S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27accc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -98.000000) translate(0,33)">动</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27accc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -98.000000) translate(0,51)">力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27accc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -98.000000) translate(0,69)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27accc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -98.000000) translate(0,87)">心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27accc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -98.000000) translate(0,105)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27accc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -98.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -98.000000) translate(0,15)">4S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -98.000000) translate(0,33)">打</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -98.000000) translate(0,51)">叶</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -98.000000) translate(0,69)">复</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -98.000000) translate(0,87)">烤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -98.000000) translate(0,105)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -98.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -98.000000) translate(0,15)">6S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -98.000000) translate(0,33)">制</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -98.000000) translate(0,51)">丝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -98.000000) translate(0,69)">车</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -98.000000) translate(0,87)">间</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -98.000000) translate(0,105)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dbed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -98.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -98.000000) translate(0,15)">8S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -98.000000) translate(0,33)">卷</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -98.000000) translate(0,51)">包</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -98.000000) translate(0,69)">车</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -98.000000) translate(0,87)">间</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -98.000000) translate(0,105)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -98.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4918.000000 -98.000000) translate(0,15)">10S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4918.000000 -98.000000) translate(0,33)">办</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4918.000000 -98.000000) translate(0,51)">公</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4918.000000 -98.000000) translate(0,69)">楼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4918.000000 -98.000000) translate(0,87)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4918.000000 -98.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -98.000000) translate(0,15)">12S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -98.000000) translate(0,33)">制</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -98.000000) translate(0,51)">冷</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -98.000000) translate(0,69)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -98.000000) translate(0,87)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -98.000000) translate(0,105)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21dc440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -98.000000) translate(0,123)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281cbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -98.000000) translate(0,15)">14S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281cbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -98.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281cbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -98.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281cbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -98.000000) translate(0,69)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281cf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5126.000000 -98.000000) translate(0,15)">16S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281cf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5126.000000 -98.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281cf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5126.000000 -98.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281cf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5126.000000 -98.000000) translate(0,69)">七</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -98.000000) translate(0,15)">18S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -98.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -98.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_281d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -98.000000) translate(0,69)">八</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c82210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5269.000000 -98.000000) translate(0,15)">20S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c82210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5269.000000 -98.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c82210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5269.000000 -98.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c82210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5269.000000 -98.000000) translate(0,69)">九</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_266a9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5340.000000 -97.000000) translate(0,15)">22S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_266a9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5340.000000 -97.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_266a9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5340.000000 -97.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_266a9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5340.000000 -97.000000) translate(0,69)">十</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_266aff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5410.000000 -97.000000) translate(0,15)">24S</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_266aff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5410.000000 -97.000000) translate(0,33)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_266aff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5410.000000 -97.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_266aff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5410.000000 -97.000000) translate(0,69)">十</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_266aff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5410.000000 -97.000000) translate(0,87)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2272190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5449.000000 -8.000000) translate(0,15)">2号无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ea280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5560.000000 -97.000000) translate(0,15)">保</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ea280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5560.000000 -97.000000) translate(0,33)">安</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ea280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5560.000000 -97.000000) translate(0,51)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ea280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5560.000000 -97.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c09ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5574.000000 -343.000000) translate(0,15)">Ⅱ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2844b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3501.000000 -343.000000) translate(0,15)">Ⅰ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_196a7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3534.000000 -628.000000) translate(0,15)">低压配电屏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21caa70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3642.000000 -422.000000) translate(0,15)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20693e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5335.000000 -424.000000) translate(0,15)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24117e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5476.000000 -628.000000) translate(0,15)">低压配电屏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2411e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5430.000000 -542.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b125d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -488.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_194d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -589.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_194d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -589.000000) translate(0,33)">SZ11-25000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_194d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -589.000000) translate(0,51)">25000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_194d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -589.000000) translate(0,69)">(110±8*1.25%)/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_194d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -589.000000) translate(0,87)">YN, d11   Ud=10.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22121f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -573.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22121f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -573.000000) translate(0,33)">SZ11-25000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22121f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -573.000000) translate(0,51)">25000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22121f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -573.000000) translate(0,69)">(110±8*1.25%)/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22121f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -573.000000) translate(0,87)">YN, d11   Ud=10.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2212460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4792.000000 -488.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.000000 -710.000000) translate(0,15)">110kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22128d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -704.000000) translate(0,15)">110kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2212b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4543.000000 -203.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8f900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4086.000000 -1229.000000) translate(0,15)">谢烟双线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a90160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4923.000000 -1230.000000) translate(0,15)">谢烟龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2068f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -839.000000) translate(0,15)">Ⅰ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2069170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -839.000000) translate(0,15)">Ⅱ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b51a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4219.000000 -947.000000) translate(0,12)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b58d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -871.000000) translate(0,12)">1711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b5b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -900.000000) translate(0,12)">17110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b5d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -845.000000) translate(0,12)">17117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2890a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4214.500000 -1009.000000) translate(0,12)">1713</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2890cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -966.000000) translate(0,12)">17130</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28910c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -1026.000000) translate(0,12)">17137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2891300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -1080.000000) translate(0,12)">17167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f0a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 -1121.000000) translate(0,12)">1716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196d800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.500000 -719.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190ce50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.500000 -788.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190d050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -769.000000) translate(0,12)">10110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190d290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 -655.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190d4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -704.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190d710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4156.000000 -643.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.500000 -947.000000) translate(0,12)">172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27cbbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4855.000000 -1121.000000) translate(0,12)">1726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27cc0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4912.000000 -1082.000000) translate(0,12)">17267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27cc300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -1009.000000) translate(0,12)">1723</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b21130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4909.000000 -1027.000000) translate(0,12)">17237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b21340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4910.000000 -966.000000) translate(0,12)">17230</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b21580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4910.000000 -900.000000) translate(0,12)">17220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b217c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4909.000000 -845.000000) translate(0,12)">17227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b21a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.500000 -871.000000) translate(0,12)">1722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b21c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4546.000000 -840.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b21e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -813.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285ffc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4635.000000 -813.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28601d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4518.000000 -862.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2860410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4609.000000 -862.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2860650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4332.000000 -782.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2860b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -782.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2860d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -416.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2239920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4907.000000 -417.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223a2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5004.000000 -531.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223a630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4317.000000 -525.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28291b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.500000 -719.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28293b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.500000 -787.000000) translate(0,12)">1022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28295f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4910.000000 -769.000000) translate(0,12)">10220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2829830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4909.000000 -710.000000) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2829a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4909.000000 -647.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2829cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -659.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3538.000000 -270.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3538.000000 -159.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -188.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2270c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -270.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_282a010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -270.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e2b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -188.000000) translate(0,12)">07367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e2dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -270.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e3000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -188.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e3430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3822.000000 -270.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e3670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3822.000000 -188.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e38b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -270.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a71360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -188.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a715a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.000000 -270.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a717e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.000000 -188.000000) translate(0,12)">07767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a71a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4035.000000 -270.000000) translate(0,12)">078</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a71cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4035.000000 -188.000000) translate(0,12)">07867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a72150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -270.000000) translate(0,12)">079</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2723390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -188.000000) translate(0,12)">07967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27235d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -270.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2723810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -188.000000) translate(0,12)">09467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2723a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4178.000000 -270.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2723c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4178.000000 -188.000000) translate(0,12)">09167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2723ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -270.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2724110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -188.000000) translate(0,12)">09267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c5230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -270.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c5470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -188.000000) translate(0,12)">09367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c56b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 -270.000000) translate(0,12)">095</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c58f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 -207.000000) translate(0,12)">09560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c5b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4536.000000 -273.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c5d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -115.000000) translate(0,12)">09567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c5fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4580.000000 -273.000000) translate(0,12)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4649.000000 -270.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4649.000000 -188.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f08c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -270.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -188.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -270.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -188.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f11c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4860.000000 -270.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4860.000000 -188.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -270.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a1e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -188.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a20b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -270.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a22f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -188.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a2530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5073.000000 -270.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a2770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5073.000000 -188.000000) translate(0,12)">06767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2166030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -270.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2166270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -188.000000) translate(0,12)">06867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21664b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5215.000000 -270.000000) translate(0,12)">069</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21666f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5215.000000 -188.000000) translate(0,12)">06967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2166930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5285.000000 -270.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2166b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5285.000000 -188.000000) translate(0,12)">08167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2166db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5356.000000 -270.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5356.000000 -188.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2403710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5426.000000 -270.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2403950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5426.000000 -188.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2403b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5499.000000 -270.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2403dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5499.000000 -207.000000) translate(0,12)">08460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2404010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5577.000000 -270.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2404250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5577.000000 -159.000000) translate(0,12)">08567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285e870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5531.000000 -460.000000) translate(0,12)">08667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285eab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5531.000000 -382.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285ecf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -460.000000) translate(0,12)">09667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285ef30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -382.000000) translate(0,12)">096</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285f170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5499.000000 -115.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285f3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5499.000000 -155.000000) translate(0,12)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285f5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -148.000000) translate(0,12)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a975b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -583.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a32c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1062.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a32c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1062.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a32c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1062.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a32c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1062.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a32c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1062.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a32c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1062.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a32c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1062.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_28a3680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3274.000000 -1173.500000) translate(0,16)">楚烟变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a75ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -451.000000) translate(0,15)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a768c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4043.000000 -456.000000) translate(0,15)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a4fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4836.000000 -1166.000000) translate(0,12)">N12T21</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25de820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3490.000000 -536.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_1996820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3133.000000 -792.000000) translate(0,15)">全站检修停电前应挂“全站检修”牌，“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_1996820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3133.000000 -792.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_1996820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3133.000000 -792.000000) translate(0,51)">全站检修完工后仅可摘除“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_1996820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3133.000000 -792.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_1996820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3133.000000 -792.000000) translate(0,87)">全站检修复电后才可以摘除“全站检修”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28172f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -222.000000) translate(0,12)">3160971</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28172f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -222.000000) translate(0,27)">3253391</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4419" cy="-69" fill="none" fillStyle="0" r="6.5" stroke="rgb(127,127,127)" stroke-width="0.448432"/>
   <circle DF8003:Layer="PUBLIC" cx="4428" cy="-68" fill="none" fillStyle="0" r="6.5" stroke="rgb(127,127,127)" stroke-width="0.448432"/>
   <circle DF8003:Layer="PUBLIC" cx="5456" cy="-68" fill="none" fillStyle="0" r="6.5" stroke="rgb(127,127,127)" stroke-width="0.448432"/>
   <circle DF8003:Layer="PUBLIC" cx="5466" cy="-69" fill="none" fillStyle="0" r="6.5" stroke="rgb(127,127,127)" stroke-width="0.448432"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_CY.CX_CY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3495,-319 4547,-319 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4479" ObjectName="BS-CX_CY.CX_CY_9IM"/>
    <cge:TPSR_Ref TObjectID="4479"/></metadata>
   <polyline fill="none" opacity="0" points="3495,-319 4547,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_CY.CX_CY_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4573,-319 5616,-319 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4481" ObjectName="BS-CX_CY.CX_CY_9IIM"/>
    <cge:TPSR_Ref TObjectID="4481"/></metadata>
   <polyline fill="none" opacity="0" points="4573,-319 5616,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_CY.CX_CY_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-819 4433,-819 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4477" ObjectName="BS-CX_CY.CX_CY_1IIM"/>
    <cge:TPSR_Ref TObjectID="4477"/></metadata>
   <polyline fill="none" opacity="0" points="4203,-819 4433,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_CY.CX_CY_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4684,-819 4898,-819 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4477" ObjectName="BS-CX_CY.CX_CY_1IIM"/>
    <cge:TPSR_Ref TObjectID="4477"/></metadata>
   <polyline fill="none" opacity="0" points="4684,-819 4898,-819 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3222.000000 -1125.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-56835" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4826.000000 -450.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56835" ObjectName="CX_CY:CX_CY_2T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-57108" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4121.000000 -455.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57108" ObjectName="CX_CY:CX_CY_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62656" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3254.538462 -1020.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62656" ObjectName="CX_CY:CX_CY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79711" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3254.538462 -977.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79711" ObjectName="CX_CY:CX_CY_sumQ"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="474" lineStyle="1" stroke="rgb(255,255,255)" stroke-dasharray="10 5 " stroke-width="1" width="878" x="4110" y="-1131"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(127,127,127)" stroke-width="1" width="9" x="4449" y="-103"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(127,127,127)" stroke-width="1" width="9" x="5487" y="-104"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="9" stroke="rgb(127,127,127)" stroke-width="1" width="6" x="3671" y="-369"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="9" stroke="rgb(127,127,127)" stroke-width="1" width="6" x="3671" y="-382"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="9" stroke="rgb(127,127,127)" stroke-width="1" width="6" x="5368" y="-369"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="9" stroke="rgb(127,127,127)" stroke-width="1" width="6" x="5368" y="-382"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="9" stroke="rgb(127,127,127)" stroke-width="1" width="6" x="3718" y="-370"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="9" stroke="rgb(127,127,127)" stroke-width="1" width="6" x="5415" y="-370"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-604"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1084"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1204"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-27646" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3415.000000 -1093.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4467" ObjectName="DYN-CX_CY"/>
     <cge:Meas_Ref ObjectId="27646"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5513.000000 -513.000000)" xlink:href="#transformer2:shape9_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5513.000000 -513.000000)" xlink:href="#transformer2:shape9_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -511.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -511.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_CY.CX_CY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16699"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 -472.000000)" xlink:href="#transformer2:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 -472.000000)" xlink:href="#transformer2:shape5_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="11887" ObjectName="TF-CX_CY.CX_CY_1T"/>
    <cge:TPSR_Ref TObjectID="11887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_CY.CX_CY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16701"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -477.000000)" xlink:href="#transformer2:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -477.000000)" xlink:href="#transformer2:shape5_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4480" ObjectName="TF-CX_CY.CX_CY_2T"/>
    <cge:TPSR_Ref TObjectID="4480"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_CY"/>
</svg>