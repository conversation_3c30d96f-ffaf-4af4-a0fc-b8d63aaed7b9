<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-199" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="1353 -1487 2604 1255">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape125">
    <ellipse cx="14" cy="17" fillStyle="0" rx="9" ry="7" stroke-width="0.153636"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="20" x2="20" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="14" y1="16" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="33" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="33" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer:shape1_0">
    <circle cx="26" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="24" y2="31"/>
   </symbol>
   <symbol id="transformer:shape1_1">
    <circle cx="26" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape1-2">
    <circle cx="56" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="57" x2="57" y1="54" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="72" x2="57" y1="45" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="72" x2="57" y1="45" y2="54"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.15789"/>
    <polyline points="58,100 64,100 " stroke-width="1.15789"/>
    <polyline points="64,100 64,93 " stroke-width="1.15789"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3765c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37665f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3766fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3767c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3768e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3769a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376a2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376aca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376b570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376bf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376bf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376d9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376d9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_376e9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3770370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3770f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37718d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37721e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3773bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37743d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37749f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3775180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3776200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3776b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3777670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_377c6a0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_377d2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37793a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_377a800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_377b2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_377e6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_377faa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1265" width="2614" x="1348" y="-1492"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3021" x2="3021" y1="-416" y2="-416"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3021" x2="3021" y1="-414" y2="-414"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3009" x2="3009" y1="-433" y2="-433"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3030" x2="3030" y1="-373" y2="-373"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3019" x2="3019" y1="-414" y2="-414"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3020" x2="3020" y1="-416" y2="-416"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2902" x2="2912" y1="-1301" y2="-1295"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2902" x2="2912" y1="-1301" y2="-1306"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2912" x2="2912" y1="-1295" y2="-1306"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2385" x2="2379" y1="-1108" y2="-1098"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2385" x2="2390" y1="-1108" y2="-1098"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2379" x2="2390" y1="-1098" y2="-1098"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-262666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2742.500771 -1017.558877)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24162" ObjectName="SW-NH_MJ.NH_MJ_301BK"/>
     <cge:Meas_Ref ObjectId="262666"/>
    <cge:TPSR_Ref TObjectID="24162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131745">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2744.500771 -805.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24164" ObjectName="SW-NH_MJ.NH_MJ_001BK"/>
     <cge:Meas_Ref ObjectId="131745"/>
    <cge:TPSR_Ref TObjectID="24164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131761">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3113.582268 -1014.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24166" ObjectName="SW-NH_MJ.NH_MJ_302BK"/>
     <cge:Meas_Ref ObjectId="131761"/>
    <cge:TPSR_Ref TObjectID="24166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131767">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3112.582268 -805.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24168" ObjectName="SW-NH_MJ.NH_MJ_002BK"/>
     <cge:Meas_Ref ObjectId="131767"/>
    <cge:TPSR_Ref TObjectID="24168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2570.215639 -543.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24141" ObjectName="SW-NH_MJ.NH_MJ_072BK"/>
     <cge:Meas_Ref ObjectId="131662"/>
    <cge:TPSR_Ref TObjectID="24141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131706">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3632.456461 -535.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24153" ObjectName="SW-NH_MJ.NH_MJ_076BK"/>
     <cge:Meas_Ref ObjectId="131706"/>
    <cge:TPSR_Ref TObjectID="24153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193510">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 -1226.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29423" ObjectName="SW-NH_MJ.NH_MJ_372BK"/>
     <cge:Meas_Ref ObjectId="193510"/>
    <cge:TPSR_Ref TObjectID="29423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131648">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2922.000000 -1242.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24137" ObjectName="SW-NH_MJ.NH_MJ_371BK"/>
     <cge:Meas_Ref ObjectId="131648"/>
    <cge:TPSR_Ref TObjectID="24137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131684">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3012.320934 -536.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24147" ObjectName="SW-NH_MJ.NH_MJ_074BK"/>
     <cge:Meas_Ref ObjectId="131684"/>
    <cge:TPSR_Ref TObjectID="24147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2797.914456 -536.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24144" ObjectName="SW-NH_MJ.NH_MJ_073BK"/>
     <cge:Meas_Ref ObjectId="131673"/>
    <cge:TPSR_Ref TObjectID="24144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3434.329751 -542.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24150" ObjectName="SW-NH_MJ.NH_MJ_075BK"/>
     <cge:Meas_Ref ObjectId="131695"/>
    <cge:TPSR_Ref TObjectID="24150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131717">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3241.329751 -547.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24156" ObjectName="SW-NH_MJ.NH_MJ_077BK"/>
     <cge:Meas_Ref ObjectId="131717"/>
    <cge:TPSR_Ref TObjectID="24156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1738.000000 -1032.897163)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 -922.062677)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1734.000000 -811.295354)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1735.000000 -699.419920)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1734.000000 -583.975038)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1737.000000 -1146.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2265.338841 -585.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24159" ObjectName="SW-NH_MJ.NH_MJ_071BK"/>
     <cge:Meas_Ref ObjectId="131728"/>
    <cge:TPSR_Ref TObjectID="24159"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.904762 -0.831461 -0.000000 2030.415730 -452.778762)" xlink:href="#transformer:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.904762 -0.831461 -0.000000 2030.415730 -452.778762)" xlink:href="#transformer:shape1_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.904762 -0.831461 -0.000000 2030.415730 -452.778762)" xlink:href="#transformer:shape1-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NH_MJ.NH_MJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2451,-1150 3635,-1150 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24135" ObjectName="BS-NH_MJ.NH_MJ_3IM"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   <polyline fill="none" opacity="0" points="2451,-1150 3635,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_MJ.NH_MJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2207,-717 3957,-717 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24136" ObjectName="BS-NH_MJ.NH_MJ_9IM"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   <polyline fill="none" opacity="0" points="2207,-717 3957,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1686,-1196 1686,-325 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1686,-1196 1686,-325 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2569.976118 -337.897163)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34110" ObjectName="EC-NH_MJ.072Ld"/>
    <cge:TPSR_Ref TObjectID="34110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3012.401413 -330.897163)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34112" ObjectName="EC-NH_MJ.074Ld"/>
    <cge:TPSR_Ref TObjectID="34112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2797.674935 -333.062677)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34111" ObjectName="EC-NH_MJ.073Ld"/>
    <cge:TPSR_Ref TObjectID="34111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.077Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3241.340230 -335.295354)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34115" ObjectName="EC-NH_MJ.077Ld"/>
    <cge:TPSR_Ref TObjectID="34115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.076Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3632.216940 -329.649789)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34114" ObjectName="EC-NH_MJ.076Ld"/>
    <cge:TPSR_Ref TObjectID="34114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_MJ.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3434.340230 -333.295354)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34113" ObjectName="EC-NH_MJ.075Ld"/>
    <cge:TPSR_Ref TObjectID="34113"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c125a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3024.200000 -1378.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bbbd20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3532.200000 -1336.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af1d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3519.200000 -1270.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bba5b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3529.200000 -1209.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7c6f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1961.000000 -1107.558877)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb55c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1892.000000 -422.231143)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2e4a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1956.000000 -996.897163)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b30ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1956.000000 -886.062677)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b33780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1956.000000 -775.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b74e00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1955.000000 -663.149789)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b18150" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1956.000000 -547.204907)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b09f90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3025.200000 -1219.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0dd10" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2663.800000 -1100.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d04840" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3028.800000 -1080.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d09190" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3316.800000 -1131.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4ef40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3506.200000 -1044.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2cc1a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3029,-1384 3007,-1384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c125a0@0" ObjectIDZND0="24140@1" Pin0InfoVect0LinkObjId="SW-262654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c125a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3029,-1384 3007,-1384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cac940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2755,-1150 2755,-1130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24135@0" ObjectIDZND0="24163@1" Pin0InfoVect0LinkObjId="SW-262667_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c9caf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2755,-1150 2755,-1130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca1e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2752,-1026 2752,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24162@0" ObjectIDZND0="24174@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-262666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2752,-1026 2752,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5cbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2754,-813 2754,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24164@0" ObjectIDZND0="24165@1" Pin0InfoVect0LinkObjId="SW-131747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2754,-813 2754,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5cdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2754,-744 2754,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24165@0" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_2b93c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131747_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2754,-744 2754,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c38220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3123,-1150 3123,-1130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24135@0" ObjectIDZND0="24167@1" Pin0InfoVect0LinkObjId="SW-131763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c9caf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3123,-1150 3123,-1130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca6f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3123,-1022 3123,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24166@0" ObjectIDZND0="24181@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3123,-1022 3123,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca8720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3122,-897 3122,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="24181@1" ObjectIDZND0="24168@1" Pin0InfoVect0LinkObjId="SW-131767_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca6f00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3122,-897 3122,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b93a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3122,-813 3122,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24168@0" ObjectIDZND0="24169@1" Pin0InfoVect0LinkObjId="SW-131769_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3122,-813 3122,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b93c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3122,-744 3122,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24169@0" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_2c5cdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3122,-744 3122,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c9caf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2931,-1170 2931,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24138@0" ObjectIDZND0="24135@0" Pin0InfoVect0LinkObjId="g_2b9c9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2931,-1170 2931,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b35e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2931,-1286 2931,-1277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="24137@1" Pin0InfoVect0LinkObjId="SW-131648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2931,-1286 2931,-1277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b36080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2930,-1384 2930,-1409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="24171@x" ObjectIDND1="24140@x" ObjectIDND2="24139@x" ObjectIDZND0="34577@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-262658_0" Pin1InfoVect1LinkObjId="SW-262654_0" Pin1InfoVect2LinkObjId="SW-262651_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2930,-1384 2930,-1409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b36270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2847,-1320 2814,-1320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24171@x" ObjectIDND1="g_2cb0730@0" ObjectIDZND0="g_298b960@0" Pin0InfoVect0LinkObjId="g_298b960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-262658_0" Pin1InfoVect1LinkObjId="g_2cb0730_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2847,-1320 2814,-1320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb0e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2847,-1263 2847,-1274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="24182@1" ObjectIDZND0="g_2cb0730@1" Pin0InfoVect0LinkObjId="g_2cb0730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2847,-1263 2847,-1274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb0ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2847,-1306 2847,-1320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2cb0730@0" ObjectIDZND0="24171@x" ObjectIDZND1="g_298b960@0" Pin0InfoVect0LinkObjId="SW-262658_0" Pin0InfoVect1LinkObjId="g_298b960_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb0730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2847,-1306 2847,-1320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb11e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2847,-1320 2847,-1336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2cb0730@0" ObjectIDND1="g_298b960@0" ObjectIDZND0="24171@0" Pin0InfoVect0LinkObjId="SW-262658_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cb0730_0" Pin1InfoVect1LinkObjId="g_298b960_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2847,-1320 2847,-1336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb13d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-717 2274,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24136@0" ObjectIDZND0="24160@1" Pin0InfoVect0LinkObjId="SW-131730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cdd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-717 2274,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0eb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-643 2274,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24160@0" ObjectIDZND0="24159@1" Pin0InfoVect0LinkObjId="SW-131728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-643 2274,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c10850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2579,-717 2579,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24136@0" ObjectIDZND0="24142@1" Pin0InfoVect0LinkObjId="SW-131664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cdd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2579,-717 2579,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c1aab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2579,-610 2579,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24142@0" ObjectIDZND0="24141@1" Pin0InfoVect0LinkObjId="SW-131662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2579,-610 2579,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2be5db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-717 3641,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24136@0" ObjectIDZND0="24154@1" Pin0InfoVect0LinkObjId="SW-131708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cdd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-717 3641,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b4bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-493 3814,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b4b810@1" ObjectIDZND0="g_2bfd280@0" Pin0InfoVect0LinkObjId="g_2bfd280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4b810_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-493 3814,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c43150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3361,-971 3361,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c428a0@1" ObjectIDZND0="g_2b4c0f0@0" Pin0InfoVect0LinkObjId="g_2b4c0f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c428a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3361,-971 3361,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c30f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-570 3641,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24153@1" ObjectIDZND0="24154@0" Pin0InfoVect0LinkObjId="SW-131708_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131706_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-570 3641,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8e840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-644 3814,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24172@1" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_2c5cdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131787_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-644 3814,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-608 3814,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="24172@0" ObjectIDZND0="g_2b4b810@0" ObjectIDZND1="g_2bfe5e0@0" Pin0InfoVect0LinkObjId="g_2b4b810_0" Pin0InfoVect1LinkObjId="g_2bfe5e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-608 3814,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8f340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-525 3814,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b4b810@0" ObjectIDZND0="24172@x" ObjectIDZND1="g_2bfe5e0@0" Pin0InfoVect0LinkObjId="SW-131787_0" Pin0InfoVect1LinkObjId="g_2bfe5e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4b810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-525 3814,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8f550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-559 3864,-559 3864,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24172@x" ObjectIDND1="g_2b4b810@0" ObjectIDZND0="g_2bfe5e0@0" Pin0InfoVect0LinkObjId="g_2bfe5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131787_0" Pin1InfoVect1LinkObjId="g_2b4b810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-559 3864,-559 3864,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b8fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2930,-1384 2847,-1384 2847,-1372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34577@1" ObjectIDND1="24140@x" ObjectIDND2="24139@x" ObjectIDZND0="24171@1" Pin0InfoVect0LinkObjId="SW-262658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b36080_1" Pin1InfoVect1LinkObjId="SW-262654_0" Pin1InfoVect2LinkObjId="SW-262651_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2930,-1384 2847,-1384 2847,-1372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbbae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3537,-1342 3515,-1342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2bbbd20@0" ObjectIDZND0="29426@1" Pin0InfoVect0LinkObjId="SW-193516_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bbbd20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3537,-1342 3515,-1342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9c9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-1164 3465,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29424@0" ObjectIDZND0="24135@0" Pin0InfoVect0LinkObjId="g_2c9caf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-1164 3465,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af1b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3524,-1276 3508,-1276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2af1d90@0" ObjectIDZND0="29428@1" Pin0InfoVect0LinkObjId="SW-193515_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2af1d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3524,-1276 3508,-1276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bba350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3534,-1215 3512,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2bba5b0@0" ObjectIDZND0="29427@1" Pin0InfoVect0LinkObjId="SW-193513_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bba5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3534,-1215 3512,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3472,-1276 3465,-1276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29428@0" ObjectIDZND0="29425@x" ObjectIDZND1="29423@x" Pin0InfoVect0LinkObjId="SW-193514_0" Pin0InfoVect1LinkObjId="SW-193510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193515_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3472,-1276 3465,-1276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-1289 3465,-1276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29425@0" ObjectIDZND0="29428@x" ObjectIDZND1="29423@x" Pin0InfoVect0LinkObjId="SW-193515_0" Pin0InfoVect1LinkObjId="SW-193510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-1289 3465,-1276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bda140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-1276 3465,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29425@x" ObjectIDND1="29428@x" ObjectIDZND0="29423@1" Pin0InfoVect0LinkObjId="SW-193510_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193514_0" Pin1InfoVect1LinkObjId="SW-193515_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-1276 3465,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bda350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3476,-1215 3465,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29427@0" ObjectIDZND0="29423@x" ObjectIDZND1="29424@x" Pin0InfoVect0LinkObjId="SW-193510_0" Pin0InfoVect1LinkObjId="SW-193512_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193513_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3476,-1215 3465,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bda580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-1234 3465,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29423@0" ObjectIDZND0="29427@x" ObjectIDZND1="29424@x" Pin0InfoVect0LinkObjId="SW-193513_0" Pin0InfoVect1LinkObjId="SW-193512_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-1234 3465,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bda7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-1215 3465,-1200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29427@x" ObjectIDND1="29423@x" ObjectIDZND0="29424@1" Pin0InfoVect0LinkObjId="SW-193512_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193513_0" Pin1InfoVect1LinkObjId="SW-193510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-1215 3465,-1200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bda9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3479,-1342 3478,-1343 3465,-1343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29426@0" ObjectIDZND0="29425@x" ObjectIDZND1="g_2b9cbd0@0" ObjectIDZND2="g_2b51860@0" Pin0InfoVect0LinkObjId="SW-193514_0" Pin0InfoVect1LinkObjId="g_2b9cbd0_0" Pin0InfoVect2LinkObjId="g_2b51860_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3479,-1342 3478,-1343 3465,-1343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bdac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-1343 3465,-1325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29426@x" ObjectIDND1="g_2b9cbd0@0" ObjectIDND2="g_2b51860@0" ObjectIDZND0="29425@1" Pin0InfoVect0LinkObjId="SW-193514_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193516_0" Pin1InfoVect1LinkObjId="g_2b9cbd0_0" Pin1InfoVect2LinkObjId="g_2b51860_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-1343 3465,-1325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c909e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2274,-593 2274,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="24159@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2274,-593 2274,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c92170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-469 3641,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24155@1" ObjectIDZND0="g_2c91580@0" Pin0InfoVect0LinkObjId="g_2c91580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-469 3641,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc5f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-524 3641,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2c91580@1" ObjectIDZND0="24153@0" Pin0InfoVect0LinkObjId="SW-131706_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c91580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-524 3641,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc8c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3426,-1383 3426,-1389 3465,-1389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b527a0@0" ObjectIDZND0="29426@x" ObjectIDZND1="29425@x" ObjectIDZND2="g_2b9cbd0@0" Pin0InfoVect0LinkObjId="SW-193516_0" Pin0InfoVect1LinkObjId="SW-193514_0" Pin0InfoVect2LinkObjId="g_2b9cbd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b527a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3426,-1383 3426,-1389 3465,-1389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc8e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-1389 3465,-1343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b9cbd0@0" ObjectIDND1="g_2b51860@0" ObjectIDND2="g_2b527a0@0" ObjectIDZND0="29426@x" ObjectIDZND1="29425@x" Pin0InfoVect0LinkObjId="SW-193516_0" Pin0InfoVect1LinkObjId="SW-193514_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b9cbd0_0" Pin1InfoVect1LinkObjId="g_2b51860_0" Pin1InfoVect2LinkObjId="g_2b527a0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-1389 3465,-1343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc9040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3474,-1389 3465,-1389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b9cbd0@0" ObjectIDZND0="29426@x" ObjectIDZND1="29425@x" ObjectIDZND2="g_2b51860@0" Pin0InfoVect0LinkObjId="SW-193516_0" Pin0InfoVect1LinkObjId="SW-193514_0" Pin0InfoVect2LinkObjId="g_2b51860_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b9cbd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3474,-1389 3465,-1389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7d000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1939,-1043 1939,-1079 1961,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2c7d260@0" Pin0InfoVect0LinkObjId="g_2c7d260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1939,-1043 1939,-1079 1961,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7df90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1939,-1043 1955,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2c7d260@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c7d260_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1939,-1043 1955,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bb6020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1865,-428 1896,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2bb55c0@0" Pin0InfoVect0LinkObjId="g_2bb55c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1865,-428 1896,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb6630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2754,-840 2754,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="24164@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2754,-840 2754,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b221b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3021,-571 3021,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24147@1" ObjectIDZND0="24148@0" Pin0InfoVect0LinkObjId="SW-131686_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131684_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3021,-571 3021,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf57b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2807,-604 2807,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24145@0" ObjectIDZND0="24144@1" Pin0InfoVect0LinkObjId="SW-131673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2807,-604 2807,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2becea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3443,-646 3443,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24151@1" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_2c5cdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131697_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3443,-646 3443,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bed090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3443,-577 3443,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24150@1" ObjectIDZND0="24151@0" Pin0InfoVect0LinkObjId="SW-131697_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131695_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3443,-577 3443,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bee550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3443,-459 3443,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24152@1" ObjectIDZND0="g_2bedb60@0" Pin0InfoVect0LinkObjId="g_2bedb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131698_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3443,-459 3443,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bee7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3443,-524 3443,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2bedb60@1" ObjectIDZND0="24150@0" Pin0InfoVect0LinkObjId="SW-131695_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bedb60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3443,-524 3443,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b72db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3250,-655 3250,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24157@1" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_2c5cdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131719_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3250,-655 3250,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b72fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3250,-582 3250,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24156@1" ObjectIDZND0="24157@0" Pin0InfoVect0LinkObjId="SW-131719_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131717_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3250,-582 3250,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ba8250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2600,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2600,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2e240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1816,-1043 1774,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1816,-1043 1774,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b33520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1810,-820 1770,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1810,-820 1770,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b74ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1811,-708 1771,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1811,-708 1771,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b75890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1966,-744 1940,-745 1940,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b01d20@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b01d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1966,-744 1940,-745 1940,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b75af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1940,-709 1956,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2b01d20@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b01d20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1940,-709 1956,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b75d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1959,-968 1938,-968 1938,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b02a90@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b02a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1959,-968 1938,-968 1938,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b75fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-932 1954,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_2b02a90@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2b02a90_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-932 1954,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b76210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1959,-856 1938,-856 1938,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b03840@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b03840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1959,-856 1938,-856 1938,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b76470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-820 1954,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_2b03840@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2b03840_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-820 1954,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b766d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1806,-932 1766,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1806,-932 1766,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b17ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-594 1770,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-594 1770,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b18be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1964,-630 1946,-630 1946,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b7c0c0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b7c0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1964,-630 1946,-630 1946,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b18e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,-594 2024,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2b7c0c0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b7c0c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1946,-594 2024,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b19a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1748,-1045 1686,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-1045 1686,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1a380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1740,-934 1686,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1740,-934 1686,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1743,-822 1686,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1743,-822 1686,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1a780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-711 1686,-711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-711 1686,-711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1a9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1743,-595 1686,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1743,-595 1686,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1ac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1759,-477 1686,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1759,-477 1686,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1e580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1746,-1156 1686,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1746,-1156 1686,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1e7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1816,-1156 1773,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1816,-1156 1773,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b81a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1829,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b82510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1882,-1156 1852,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2b1ea40@0" ObjectIDND2="g_2affee0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b1ea40_0" Pin1InfoVect2LinkObjId="g_2affee0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1882,-1156 1852,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b83000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1849,-1043 1879,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2c7d260@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2c7d260_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1849,-1043 1879,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b83260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-1043 1939,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2c7d260@0" Pin0InfoVect0LinkObjId="g_2c7d260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1879,-1043 1939,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b83d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-932 1877,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b02a90@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b02a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-932 1877,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b83fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1877,-932 1842,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b02a90@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b02a90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1877,-932 1842,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b84aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-820 1879,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b03840@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b03840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-820 1879,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b84d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-820 1846,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b03840@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b03840_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1879,-820 1846,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b84f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1940,-709 1880,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b01d20@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b01d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1940,-709 1880,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b851c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1880,-709 1847,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b01d20@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b01d20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1880,-709 1847,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b85cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,-594 1882,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b7c0c0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b7c0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1946,-594 1882,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b85f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1882,-594 1853,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b7c0c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b7c0c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1882,-594 1853,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af3b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1881,-1043 1881,-1003 1904,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2c7d260@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2c7d260_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1881,-1043 1881,-1003 1904,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2af3de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1940,-1003 1961,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2b2e4a0@0" Pin0InfoVect0LinkObjId="g_2b2e4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1940,-1003 1961,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af6a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1881,-932 1881,-892 1894,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b02a90@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b02a90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1881,-932 1881,-892 1894,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2af6cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1930,-892 1960,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2b30ce0@0" Pin0InfoVect0LinkObjId="g_2b30ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1930,-892 1960,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af99a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1881,-820 1881,-780 1901,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b03840@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b03840_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1881,-820 1881,-780 1901,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2af9c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1937,-780 1960,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2b33780@0" Pin0InfoVect0LinkObjId="g_2b33780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1937,-780 1960,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2afc8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1940,-669 1962,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2b74e00@0" Pin0InfoVect0LinkObjId="g_2b74e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1940,-669 1962,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aff560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1883,-594 1883,-554 1906,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b7c0c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b7c0c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1883,-594 1883,-554 1906,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aff7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1942,-554 1962,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2b18150@0" Pin0InfoVect0LinkObjId="g_2b18150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1942,-554 1962,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2affa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1882,-1156 1882,-1114 1906,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2b1ea40@0" ObjectIDND2="g_2affee0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b1ea40_0" Pin1InfoVect2LinkObjId="g_2affee0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1882,-1156 1882,-1114 1906,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2affc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1942,-1114 1965,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2c7c6f0@0" Pin0InfoVect0LinkObjId="g_2c7c6f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1942,-1114 1965,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b00b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2752,-901 2752,-888 2320,-888 2320,-1156 1994,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="24174@1" ObjectIDZND0="g_2affee0@0" Pin0InfoVect0LinkObjId="g_2affee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca1e80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2752,-901 2752,-888 2320,-888 2320,-1156 1994,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b00d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1963,-1193 1938,-1193 1938,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b1ea40@0" ObjectIDZND0="g_2affee0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2affee0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b1ea40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1963,-1193 1938,-1193 1938,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b01860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-1156 1938,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2affee0@1" ObjectIDZND0="g_2b1ea40@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b1ea40_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2affee0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-1156 1938,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b01ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-1156 1882,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b1ea40@0" ObjectIDND1="g_2affee0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b1ea40_0" Pin1InfoVect1LinkObjId="g_2affee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-1156 1882,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b04c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,-669 1881,-669 1881,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2b01d20@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b01d20_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1904,-669 1881,-669 1881,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b04eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1806,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1806,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b05110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1848,-512 1815,-512 1815,-428 1829,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bb2e60@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb2e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1848,-512 1815,-512 1815,-428 1829,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b05c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1958,-476 1872,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b05380@1" Pin0InfoVect0LinkObjId="g_2b05380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1958,-476 1872,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b05ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1841,-476 1796,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b05380@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b05380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1841,-476 1796,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b06140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2930,-1384 2971,-1384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24171@x" ObjectIDND1="34577@1" ObjectIDND2="24139@x" ObjectIDZND0="24140@0" Pin0InfoVect0LinkObjId="SW-262654_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-262658_0" Pin1InfoVect1LinkObjId="g_2b36080_1" Pin1InfoVect2LinkObjId="SW-262651_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2930,-1384 2971,-1384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b06ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2752,-1053 2752,-1070 2384,-1070 2384,-1164 2888,-1164 2888,-1301 2930,-1301 2930,-1315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24162@1" ObjectIDZND0="24139@0" Pin0InfoVect0LinkObjId="SW-262651_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-262666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2752,-1053 2752,-1070 2384,-1070 2384,-1164 2888,-1164 2888,-1301 2930,-1301 2930,-1315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b09d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3030,-1225 3008,-1225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b09f90@0" ObjectIDZND0="42327@1" Pin0InfoVect0LinkObjId="SW-257204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b09f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3030,-1225 3008,-1225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0b050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2931,-1225 2972,-1225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24137@x" ObjectIDND1="24138@x" ObjectIDZND0="42327@0" Pin0InfoVect0LinkObjId="SW-257204_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131648_0" Pin1InfoVect1LinkObjId="SW-131650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2931,-1225 2972,-1225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0dab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2659,-1094 2681,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b0dd10@0" ObjectIDZND0="42330@1" Pin0InfoVect0LinkObjId="SW-257478_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0dd10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2659,-1094 2681,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0e7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2758,-1094 2717,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24163@0" ObjectIDZND0="42330@0" Pin0InfoVect0LinkObjId="SW-257478_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-262667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2758,-1094 2717,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0f670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2931,-1250 2931,-1225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24137@0" ObjectIDZND0="42327@x" ObjectIDZND1="24138@x" Pin0InfoVect0LinkObjId="SW-257204_0" Pin0InfoVect1LinkObjId="SW-131650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2931,-1250 2931,-1225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0f860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2931,-1225 2931,-1206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42327@x" ObjectIDND1="24137@x" ObjectIDZND0="24138@1" Pin0InfoVect0LinkObjId="SW-131650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-257204_0" Pin1InfoVect1LinkObjId="SW-131648_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2931,-1225 2931,-1206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0fa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2930,-1384 2930,-1351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24171@x" ObjectIDND1="34577@1" ObjectIDND2="24140@x" ObjectIDZND0="24139@1" Pin0InfoVect0LinkObjId="SW-262651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-262658_0" Pin1InfoVect1LinkObjId="g_2b36080_1" Pin1InfoVect2LinkObjId="SW-262654_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2930,-1384 2930,-1351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b12a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2404,-717 2404,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24136@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5cdd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2404,-717 2404,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cfbbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2404,-628 2404,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2404,-628 2404,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfcdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2579,-551 2579,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24141@0" ObjectIDZND0="24143@1" Pin0InfoVect0LinkObjId="SW-131665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2579,-551 2579,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfcfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2579,-497 2579,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24143@0" ObjectIDZND0="g_2c90bd0@1" Pin0InfoVect0LinkObjId="g_2c90bd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2579,-497 2579,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfd190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2579,-446 2579,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2c90bd0@0" ObjectIDZND0="g_2c46fc0@0" ObjectIDZND1="34110@x" Pin0InfoVect0LinkObjId="g_2c46fc0_0" Pin0InfoVect1LinkObjId="EC-NH_MJ.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c90bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2579,-446 2579,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfd3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3021,-544 3021,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24147@0" ObjectIDZND0="24149@1" Pin0InfoVect0LinkObjId="SW-131687_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3021,-544 3021,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfd5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3021,-491 3021,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24149@0" ObjectIDZND0="g_2c684c0@1" Pin0InfoVect0LinkObjId="g_2c684c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131687_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3021,-491 3021,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfd850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3021,-439 3021,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_2c684c0@0" ObjectIDZND0="34112@x" ObjectIDZND1="g_2b223a0@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.074Ld_0" Pin0InfoVect1LinkObjId="g_2b223a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c684c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3021,-439 3021,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfdab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2807,-544 2807,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24144@0" ObjectIDZND0="24146@1" Pin0InfoVect0LinkObjId="SW-131676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2807,-544 2807,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfdd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2807,-491 2807,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24146@0" ObjectIDZND0="g_2b6bec0@1" Pin0InfoVect0LinkObjId="g_2b6bec0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2807,-491 2807,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfdf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2807,-437 2807,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_2b6bec0@0" ObjectIDZND0="34111@x" ObjectIDZND1="g_2b69a00@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.073Ld_0" Pin0InfoVect1LinkObjId="g_2b69a00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b6bec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2807,-437 2807,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfe1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3250,-555 3250,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24156@0" ObjectIDZND0="24158@1" Pin0InfoVect0LinkObjId="SW-131720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131717_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3250,-555 3250,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfe430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3250,-505 3250,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24158@0" ObjectIDZND0="g_2ba2290@1" Pin0InfoVect0LinkObjId="g_2ba2290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3250,-505 3250,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfe690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3250,-448 3250,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2ba2290@0" ObjectIDZND0="34115@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.077Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ba2290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3250,-448 3250,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d014f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3123,-1074 3082,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="24167@x" ObjectIDND1="24166@x" ObjectIDZND0="42331@0" Pin0InfoVect0LinkObjId="SW-257606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131763_0" Pin1InfoVect1LinkObjId="SW-131761_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3123,-1074 3082,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d04650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3024,-1074 3046,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d04840@0" ObjectIDZND0="42331@1" Pin0InfoVect0LinkObjId="SW-257606_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d04840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3024,-1074 3046,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d05920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3123,-1094 3123,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24167@0" ObjectIDZND0="42331@x" ObjectIDZND1="24166@x" Pin0InfoVect0LinkObjId="SW-257606_0" Pin0InfoVect1LinkObjId="SW-131761_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3123,-1094 3123,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d05b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3123,-1074 3123,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="42331@x" ObjectIDND1="24167@x" ObjectIDZND0="24166@1" Pin0InfoVect0LinkObjId="SW-131761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-257606_0" Pin1InfoVect1LinkObjId="SW-131763_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3123,-1074 3123,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d05de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3411,-1125 3370,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24135@0" ObjectIDND1="24170@x" ObjectIDZND0="42333@0" Pin0InfoVect0LinkObjId="SW-257741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c9caf0_0" Pin1InfoVect1LinkObjId="SW-131783_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3411,-1125 3370,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d08fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3312,-1125 3334,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d09190@0" ObjectIDZND0="42333@1" Pin0InfoVect0LinkObjId="SW-257741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d09190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3312,-1125 3334,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0a840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3411,-1150 3411,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24135@0" ObjectIDZND0="42333@x" ObjectIDZND1="24170@x" Pin0InfoVect0LinkObjId="SW-257741_0" Pin0InfoVect1LinkObjId="SW-131783_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c9caf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3411,-1150 3411,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0aaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3411,-1125 3411,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="42333@x" ObjectIDND1="24135@0" ObjectIDZND0="24170@1" Pin0InfoVect0LinkObjId="SW-131783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-257741_0" Pin1InfoVect1LinkObjId="g_2c9caf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3411,-1125 3411,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0ad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3412,-1050 3453,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24170@x" ObjectIDND1="g_2c428a0@0" ObjectIDND2="g_2b4d520@0" ObjectIDZND0="42332@0" Pin0InfoVect0LinkObjId="SW-257740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-131783_0" Pin1InfoVect1LinkObjId="g_2c428a0_0" Pin1InfoVect2LinkObjId="g_2b4d520_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3412,-1050 3453,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4ece0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3511,-1050 3489,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b4ef40@0" ObjectIDZND0="42332@1" Pin0InfoVect0LinkObjId="SW-257740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4ef40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3511,-1050 3489,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b50260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3411,-1050 3411,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="42332@x" ObjectIDND1="g_2c428a0@0" ObjectIDND2="g_2b4d520@0" ObjectIDZND0="24170@0" Pin0InfoVect0LinkObjId="SW-131783_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-257740_0" Pin1InfoVect1LinkObjId="g_2c428a0_0" Pin1InfoVect2LinkObjId="g_2b4d520_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3411,-1050 3411,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b50af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3361,-1003 3361,-1030 3411,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2c428a0@0" ObjectIDZND0="g_2b4d520@0" ObjectIDZND1="42332@x" ObjectIDZND2="24170@x" Pin0InfoVect0LinkObjId="g_2b4d520_0" Pin0InfoVect1LinkObjId="SW-257740_0" Pin0InfoVect2LinkObjId="SW-131783_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c428a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3361,-1003 3361,-1030 3411,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b51460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3461,-1005 3461,-1030 3411,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b4d520@0" ObjectIDZND0="g_2c428a0@0" ObjectIDZND1="42332@x" ObjectIDZND2="24170@x" Pin0InfoVect0LinkObjId="g_2c428a0_0" Pin0InfoVect1LinkObjId="SW-257740_0" Pin0InfoVect2LinkObjId="SW-131783_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4d520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3461,-1005 3461,-1030 3411,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b51650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3411,-1030 3411,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2c428a0@0" ObjectIDND1="g_2b4d520@0" ObjectIDZND0="42332@x" ObjectIDZND1="24170@x" Pin0InfoVect0LinkObjId="SW-257740_0" Pin0InfoVect1LinkObjId="SW-131783_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c428a0_0" Pin1InfoVect1LinkObjId="g_2b4d520_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3411,-1030 3411,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b522e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-1389 3465,-1403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29426@x" ObjectIDND1="29425@x" ObjectIDND2="g_2b9cbd0@0" ObjectIDZND0="g_2b51860@0" Pin0InfoVect0LinkObjId="g_2b51860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193516_0" Pin1InfoVect1LinkObjId="SW-193514_0" Pin1InfoVect2LinkObjId="g_2b9cbd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-1389 3465,-1403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b52540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-1442 3465,-1462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2b51860@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b51860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-1442 3465,-1462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b54200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3210,-364 3210,-405 3281,-405 3281,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2cfe8f0@0" ObjectIDZND0="g_2ba3c70@0" Pin0InfoVect0LinkObjId="g_2ba3c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cfe8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3210,-364 3210,-405 3281,-405 3281,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b543f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3443,-423 3443,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24152@0" ObjectIDZND0="g_2b56160@0" ObjectIDZND1="34113@x" Pin0InfoVect0LinkObjId="g_2b56160_0" Pin0InfoVect1LinkObjId="EC-NH_MJ.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3443,-423 3443,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b54d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3474,-395 3443,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b56160@0" ObjectIDZND0="24152@x" ObjectIDZND1="34113@x" Pin0InfoVect0LinkObjId="SW-131698_0" Pin0InfoVect1LinkObjId="EC-NH_MJ.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b56160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3474,-395 3443,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b54f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3443,-395 3443,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24152@x" ObjectIDND1="g_2b56160@0" ObjectIDZND0="34113@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.075Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131698_0" Pin1InfoVect1LinkObjId="g_2b56160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3443,-395 3443,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b551b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-433 3641,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="24155@0" ObjectIDZND0="34114@x" ObjectIDZND1="g_2ba6c40@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.076Ld_0" Pin0InfoVect1LinkObjId="g_2ba6c40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-433 3641,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b55ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-357 3641,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34114@0" ObjectIDZND0="24155@x" ObjectIDZND1="g_2ba6c40@0" Pin0InfoVect0LinkObjId="SW-131709_0" Pin0InfoVect1LinkObjId="g_2ba6c40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-NH_MJ.076Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-357 3641,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b55f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-393 3671,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24155@x" ObjectIDND1="34114@x" ObjectIDZND0="g_2ba6c40@0" Pin0InfoVect0LinkObjId="g_2ba6c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-131709_0" Pin1InfoVect1LinkObjId="EC-NH_MJ.076Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-393 3671,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b57720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2613,-398 2579,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2c46fc0@0" ObjectIDZND0="g_2c90bd0@0" ObjectIDZND1="34110@x" Pin0InfoVect0LinkObjId="g_2c90bd0_0" Pin0InfoVect1LinkObjId="EC-NH_MJ.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c46fc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2613,-398 2579,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b57980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2579,-398 2579,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_2c90bd0@0" ObjectIDND1="g_2c46fc0@0" ObjectIDZND0="34110@0" Pin0InfoVect0LinkObjId="EC-NH_MJ.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c90bd0_0" Pin1InfoVect1LinkObjId="g_2c46fc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2579,-398 2579,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b58470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2807,-360 2807,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="34111@0" ObjectIDZND0="g_2b6bec0@0" ObjectIDZND1="g_2b69a00@0" Pin0InfoVect0LinkObjId="g_2b6bec0_0" Pin0InfoVect1LinkObjId="g_2b69a00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-NH_MJ.073Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2807,-360 2807,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b586d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2807,-393 2838,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_2b6bec0@0" ObjectIDND1="34111@x" ObjectIDZND0="g_2b69a00@0" Pin0InfoVect0LinkObjId="g_2b69a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b6bec0_0" Pin1InfoVect1LinkObjId="EC-NH_MJ.073Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2807,-393 2838,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b591c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3021,-358 3021,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="34112@0" ObjectIDZND0="g_2c684c0@0" ObjectIDZND1="g_2b223a0@0" Pin0InfoVect0LinkObjId="g_2c684c0_0" Pin0InfoVect1LinkObjId="g_2b223a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-NH_MJ.074Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3021,-358 3021,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b59420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3021,-393 3051,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_2c684c0@0" ObjectIDND1="34112@x" ObjectIDZND0="g_2b223a0@0" Pin0InfoVect0LinkObjId="g_2b223a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c684c0_0" Pin1InfoVect1LinkObjId="EC-NH_MJ.074Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3021,-393 3051,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b59680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2807,-640 2807,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24145@1" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_2c5cdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131675_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2807,-640 2807,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b598e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3021,-640 3021,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24148@1" ObjectIDZND0="24136@0" Pin0InfoVect0LinkObjId="g_2c5cdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-131686_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3021,-640 3021,-717 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="2755" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="3123" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="2931" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="3465" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1686" cy="-595" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1686" cy="-1156" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1686" cy="-477" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24135" cx="3411" cy="-1150" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2754" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3122" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2274" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2579" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3641" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3814" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3443" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3250" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2404" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="2807" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24136" cx="3021" cy="-717" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1686" cy="-1045" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1686" cy="-934" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1686" cy="-822" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1686" cy="-711" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-131037" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.000000 -1295.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24024" ObjectName="DYN-NH_MJ"/>
     <cge:Meas_Ref ObjectId="131037"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c6ba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2193.000000 -713.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2980870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2980870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2980870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2980870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2980870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2980870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2980870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2980870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2980870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2946d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2408.000000 -1140.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2c42550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.000000 -1373.000000) translate(0,16)">马街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c53320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3320.000000 -921.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b36460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2808.000000 -1413.000000) translate(0,15)">35kV大马红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b4b480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 -452.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c43370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3420.000000 -1095.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b9c290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3454.000000 -1487.000000) translate(0,15)">35kV八马线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bbafe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3424.000000 -1256.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd90e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3488.000000 -1368.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd9360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3473.000000 -1318.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd95a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.000000 -1298.000000) translate(0,12)">37260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd97a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3472.000000 -1189.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd99e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3491.000000 -1238.000000) translate(0,12)">37217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c17b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2589.000000 -572.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2586.000000 -635.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2586.000000 -522.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2854.000000 -1359.558877) translate(0,12)">3806</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2980.000000 -1411.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -1338.558877) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2947.000000 -1270.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c18f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -1195.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c729d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -458.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c72eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -626.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c730f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3651.000000 -564.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c73470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3821.000000 -633.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c73780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2281.000000 -668.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c739c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2289.000000 -615.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c73d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2723.000000 -1231.558877) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c740a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -1043.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c744b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1119.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c746f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -834.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -769.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4bef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2763.000000 -834.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2761.000000 -769.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4c340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 -1045.558877) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4c580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2762.000000 -1119.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c4c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2788.000000 -987.000000) translate(0,15)">S9-5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c4c7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2788.000000 -987.000000) translate(0,33)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c4e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3157.000000 -981.000000) translate(0,15)">SZ11-2000</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c4e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3157.000000 -981.000000) translate(0,33)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2c4ea20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3401.000000 -1304.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2bafb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1692.000000 -1346.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2aef8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1692.000000 -1385.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb2640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -988.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c8ecf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -442.000000) translate(0,17)">7361060</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c8fab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2799.000000 -933.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2bc61b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -515.000000) translate(0,12)">10kV避雷器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc9250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2555.000000 -332.897163) translate(0,15)">唐家线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c05ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3166.000000 -914.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c06100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3166.000000 -931.000000) translate(0,12)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c084d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -400.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c084d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -400.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b9f450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -410.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b9f450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -410.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b9f450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -410.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c7ab50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2666.000000 -967.558877) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c7b4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3029.000000 -962.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2c7ba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.500000 -1372.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c67a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3028.000000 -629.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c67f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3031.000000 -565.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c68140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3028.000000 -516.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c68eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2991.000000 -325.897163) translate(0,15)">平掌孜线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2814.000000 -516.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2814.000000 -629.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6bb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2816.000000 -565.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b6c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2783.000000 -329.062677) translate(0,15)">后山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bed280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3453.000000 -571.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bed5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3450.000000 -448.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bed7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3450.000000 -635.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b73190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -576.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b734b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -530.000000) translate(0,12)">0776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba1f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -644.000000) translate(0,12)">0771</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2ba48f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3182.000000 -326.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba4de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3230.000000 -328.295354) translate(0,15)">兔街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba6260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3628.000000 -324.649789) translate(0,15)">机关线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba84b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1919.500000 -1100.000000) translate(0,12)">01017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba89a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.500000 -1138.000000) translate(0,12)">010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b36ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2084.000000 -1175.000000) translate(0,12)">10kV转供电缆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b37c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1746.000000 -449.000000) translate(0,12)">0701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b38070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1839.000000 -408.000000) translate(0,12)">07017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b382b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2037.000000 -1077.000000) translate(0,12)">10kV转供Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b382b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2037.000000 -1077.000000) translate(0,27)">（唐家线、平掌孜线）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3a560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2024.000000 -735.000000) translate(0,12)">10kV转供Ⅳ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3a560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2024.000000 -735.000000) translate(0,27)">（法空线、机关线）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3b870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2032.000000 -973.000000) translate(0,12)">10kV转供Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3b870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2032.000000 -973.000000) translate(0,27)">（后山线）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3c410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2026.000000 -861.000000) translate(0,12)">10kV转供Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3c410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2026.000000 -861.000000) translate(0,27)">（兔街线）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1825.000000 -381.000000) translate(0,12)">1号转供电环网柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1825.000000 -381.000000) translate(0,27)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2c510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.500000 -1139.000000) translate(0,12)">0101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b76930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1919.500000 -986.000000) translate(0,12)">02017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b76e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.500000 -1021.000000) translate(0,12)">0201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b77060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.500000 -1024.000000) translate(0,12)">020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b772a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 -911.000000) translate(0,12)">030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b774e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -911.000000) translate(0,12)">0301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b77720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1919.000000 -876.000000) translate(0,12)">03017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b77960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1919.000000 -759.000000) translate(0,12)">04017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b77ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -800.000000) translate(0,12)">0401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b77de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 -799.000000) translate(0,12)">040</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b78020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 -686.000000) translate(0,12)">050</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b78260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -688.000000) translate(0,12)">0501</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b784a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1919.000000 -656.000000) translate(0,12)">05017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b190a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 -571.000000) translate(0,12)">060</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b19590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -573.000000) translate(0,12)">0601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b197d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1919.000000 -540.000000) translate(0,12)">06017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2002.000000 -581.000000) translate(0,12)">10kV转供Ⅴ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2002.000000 -581.000000) translate(0,27)">（备用一）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0aa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2974.000000 -1217.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0ea00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2681.000000 -1121.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b11cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2241.000000 -480.897163) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfbe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2365.000000 -455.897163) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="9" graphid="g_2cff3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3169.000000 -308.000000) translate(0,8)">（安装于站外杆塔）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d04020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3046.000000 -1101.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d08ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3333.000000 -1113.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b504c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3455.000000 -1073.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32effd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3423.000000 -322.000000) translate(0,15)">法空线</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="1846" y="-1383"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cfe8f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3201.000000 -333.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-NH_MJ.NH_MJ_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34122"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2861.621739 -1267.558877)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2861.621739 -1267.558877)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24182" ObjectName="TF-NH_MJ.NH_MJ_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_MJ.NH_MJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34118"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 3089.500000 -893.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.863636 -0.000000 0.000000 -0.882353 3089.500000 -893.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24181" ObjectName="TF-NH_MJ.NH_MJ_2T"/>
    <cge:TPSR_Ref TObjectID="24181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_MJ.NH_MJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34110"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.020000 -0.000000 0.000000 1.000000 2726.500000 -985.558877)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.020000 -0.000000 0.000000 1.000000 2726.500000 -985.558877)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24174" ObjectName="TF-NH_MJ.NH_MJ_1T"/>
    <cge:TPSR_Ref TObjectID="24174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2419.000000 -571.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2419.000000 -571.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1448.500000 -1327.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1450.000000 -1328.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208961" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 -1232.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208961" ObjectName="NH_MJ:NH_MJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208961" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 -1191.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208961" ObjectName="NH_MJ:NH_MJ_sumP"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="1460" y="-1386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1412" y="-1403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1413" y="-1404"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1413" y="-1404"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3424" y="-1256"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3424" y="-1256"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2589" y="-573"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2589" y="-573"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3651" y="-564"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3651" y="-564"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2289" y="-615"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2289" y="-615"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2947" y="-1270"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2947" y="-1270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1682" y="-1353"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1682" y="-1353"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1682" y="-1392"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1682" y="-1392"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="1373" y="-988"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="1373" y="-988"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2665" y="-967"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2665" y="-967"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="3029" y="-962"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="3029" y="-962"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="1845" y="-1384"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="1845" y="-1384"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3031" y="-565"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3031" y="-565"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2816" y="-565"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2816" y="-565"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3453" y="-571"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3453" y="-571"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3260" y="-576"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3260" y="-576"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c64720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2205.000000 290.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c64c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2194.000000 275.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c64e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2219.000000 260.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c651b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2812.000000 1058.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2801.000000 1043.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2826.000000 1028.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2819.000000 856.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2808.000000 841.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2833.000000 826.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c661b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2992.000000 1291.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2981.000000 1276.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3006.000000 1261.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2375.000000 1188.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2383.000000 1218.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2e4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2383.000000 1233.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2e710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2383.000000 1249.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2e920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2382.000000 1203.250000) translate(0,12)">U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2ec20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2210.000000 764.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2ee30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2218.000000 794.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2f040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2218.000000 809.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2f250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2218.000000 825.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c2f460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2217.000000 779.250000) translate(0,12)">U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd8ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3568.000000 1268.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd8ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3557.000000 1253.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd8ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3582.000000 1238.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c056f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3178.000000 1052.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c05a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3167.000000 1037.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c05c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3192.000000 1022.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c06b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3186.000000 851.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c06e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3175.000000 836.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c07050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3200.000000 821.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c07470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2548.000000 283.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c07730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2537.000000 268.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c07970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2562.000000 253.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c07d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3613.000000 286.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c08050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 271.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c08290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 256.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2746" x2="2760" y1="865" y2="850"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2746" x2="2761" y1="849" y2="864"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2746" x2="2760" y1="865" y2="850"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c69f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2999.000000 277.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6a200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2988.000000 262.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6a440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3013.000000 247.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6a860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 281.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6ab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2746.000000 266.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6ad60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2771.000000 251.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beec50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3405.000000 284.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bef1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3394.000000 269.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bef3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3419.000000 254.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba2d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3208.000000 281.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba3250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3197.000000 266.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ba3490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3222.000000 251.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2747" x2="2761" y1="1102" y2="1087"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2747" x2="2762" y1="1086" y2="1101"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2747" x2="2761" y1="1102" y2="1087"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2924" x2="2938" y1="1292" y2="1277"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2924" x2="2939" y1="1276" y2="1291"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2924" x2="2938" y1="1292" y2="1277"/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_MJ" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_damahongTmj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2930,-1449 2930,-1409 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34577" ObjectName="AC-35kV.LN_damahongTmj"/>
    <cge:TPSR_Ref TObjectID="34577_SS-199"/></metadata>
   <polyline fill="none" opacity="0" points="2930,-1449 2930,-1409 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2257.338841 -290.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24159"/>
     <cge:Term_Ref ObjectID="34078"/>
    <cge:TPSR_Ref TObjectID="24159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2257.338841 -290.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24159"/>
     <cge:Term_Ref ObjectID="34078"/>
    <cge:TPSR_Ref TObjectID="24159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2257.338841 -290.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24159"/>
     <cge:Term_Ref ObjectID="34078"/>
    <cge:TPSR_Ref TObjectID="24159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2603.976118 -280.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24141"/>
     <cge:Term_Ref ObjectID="34042"/>
    <cge:TPSR_Ref TObjectID="24141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2603.976118 -280.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24141"/>
     <cge:Term_Ref ObjectID="34042"/>
    <cge:TPSR_Ref TObjectID="24141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2603.976118 -280.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24141"/>
     <cge:Term_Ref ObjectID="34042"/>
    <cge:TPSR_Ref TObjectID="24141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3670.216940 -287.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24153"/>
     <cge:Term_Ref ObjectID="34066"/>
    <cge:TPSR_Ref TObjectID="24153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3670.216940 -287.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24153"/>
     <cge:Term_Ref ObjectID="34066"/>
    <cge:TPSR_Ref TObjectID="24153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3670.216940 -287.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24153"/>
     <cge:Term_Ref ObjectID="34066"/>
    <cge:TPSR_Ref TObjectID="24153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2886.712348 -851.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24164"/>
     <cge:Term_Ref ObjectID="34088"/>
    <cge:TPSR_Ref TObjectID="24164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2886.712348 -851.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24164"/>
     <cge:Term_Ref ObjectID="34088"/>
    <cge:TPSR_Ref TObjectID="24164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2886.712348 -851.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24164"/>
     <cge:Term_Ref ObjectID="34088"/>
    <cge:TPSR_Ref TObjectID="24164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131626" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3243.793845 -851.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131626" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24168"/>
     <cge:Term_Ref ObjectID="34096"/>
    <cge:TPSR_Ref TObjectID="24168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3243.793845 -851.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24168"/>
     <cge:Term_Ref ObjectID="34096"/>
    <cge:TPSR_Ref TObjectID="24168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3243.793845 -851.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24168"/>
     <cge:Term_Ref ObjectID="34096"/>
    <cge:TPSR_Ref TObjectID="24168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3231.793845 -1051.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24166"/>
     <cge:Term_Ref ObjectID="34092"/>
    <cge:TPSR_Ref TObjectID="24166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3231.793845 -1051.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24166"/>
     <cge:Term_Ref ObjectID="34092"/>
    <cge:TPSR_Ref TObjectID="24166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3231.793845 -1051.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24166"/>
     <cge:Term_Ref ObjectID="34092"/>
    <cge:TPSR_Ref TObjectID="24166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3070.000000 -1290.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24137"/>
     <cge:Term_Ref ObjectID="34034"/>
    <cge:TPSR_Ref TObjectID="24137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3070.000000 -1290.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24137"/>
     <cge:Term_Ref ObjectID="34034"/>
    <cge:TPSR_Ref TObjectID="24137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3070.000000 -1290.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24137"/>
     <cge:Term_Ref ObjectID="34034"/>
    <cge:TPSR_Ref TObjectID="24137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-131633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2286.000000 -823.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-131634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2286.000000 -823.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-131635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2286.000000 -823.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-131638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2286.000000 -823.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-131636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2286.000000 -823.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24136"/>
     <cge:Term_Ref ObjectID="34033"/>
    <cge:TPSR_Ref TObjectID="24136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-131629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -1246.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-131630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -1246.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-131631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -1246.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-131637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -1246.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-131632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -1246.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24135"/>
     <cge:Term_Ref ObjectID="34032"/>
    <cge:TPSR_Ref TObjectID="24135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-131639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3230.793845 -930.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24181"/>
     <cge:Term_Ref ObjectID="34119"/>
    <cge:TPSR_Ref TObjectID="24181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-131641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3230.793845 -930.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24181"/>
     <cge:Term_Ref ObjectID="34119"/>
    <cge:TPSR_Ref TObjectID="24181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-193532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3621.000000 -1268.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29423"/>
     <cge:Term_Ref ObjectID="41904"/>
    <cge:TPSR_Ref TObjectID="29423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-193533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3621.000000 -1268.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29423"/>
     <cge:Term_Ref ObjectID="41904"/>
    <cge:TPSR_Ref TObjectID="29423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-193518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3621.000000 -1268.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29423"/>
     <cge:Term_Ref ObjectID="41904"/>
    <cge:TPSR_Ref TObjectID="29423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-131640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2863.000000 -933.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24174"/>
     <cge:Term_Ref ObjectID="34111"/>
    <cge:TPSR_Ref TObjectID="24174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.641413 -277.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24147"/>
     <cge:Term_Ref ObjectID="34054"/>
    <cge:TPSR_Ref TObjectID="24147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.641413 -277.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24147"/>
     <cge:Term_Ref ObjectID="34054"/>
    <cge:TPSR_Ref TObjectID="24147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.641413 -277.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24147"/>
     <cge:Term_Ref ObjectID="34054"/>
    <cge:TPSR_Ref TObjectID="24147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2816.174935 -277.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24144"/>
     <cge:Term_Ref ObjectID="34048"/>
    <cge:TPSR_Ref TObjectID="24144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2816.174935 -277.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24144"/>
     <cge:Term_Ref ObjectID="34048"/>
    <cge:TPSR_Ref TObjectID="24144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2816.174935 -277.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24144"/>
     <cge:Term_Ref ObjectID="34048"/>
    <cge:TPSR_Ref TObjectID="24144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3461.840230 -283.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24150"/>
     <cge:Term_Ref ObjectID="34060"/>
    <cge:TPSR_Ref TObjectID="24150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3461.840230 -283.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24150"/>
     <cge:Term_Ref ObjectID="34060"/>
    <cge:TPSR_Ref TObjectID="24150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3461.840230 -283.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24150"/>
     <cge:Term_Ref ObjectID="34060"/>
    <cge:TPSR_Ref TObjectID="24150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3266.000000 -280.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24156"/>
     <cge:Term_Ref ObjectID="34072"/>
    <cge:TPSR_Ref TObjectID="24156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3266.000000 -280.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24156"/>
     <cge:Term_Ref ObjectID="34072"/>
    <cge:TPSR_Ref TObjectID="24156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3266.000000 -280.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24156"/>
     <cge:Term_Ref ObjectID="34072"/>
    <cge:TPSR_Ref TObjectID="24156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-262681" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2871.000000 -1056.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="262681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24162"/>
     <cge:Term_Ref ObjectID="34084"/>
    <cge:TPSR_Ref TObjectID="24162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-262682" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2871.000000 -1056.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="262682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24162"/>
     <cge:Term_Ref ObjectID="34084"/>
    <cge:TPSR_Ref TObjectID="24162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-262678" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2871.000000 -1056.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="262678" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24162"/>
     <cge:Term_Ref ObjectID="34084"/>
    <cge:TPSR_Ref TObjectID="24162"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-262654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2979.600000 -1358.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24140" ObjectName="SW-NH_MJ.NH_MJ_37167SW"/>
     <cge:Meas_Ref ObjectId="262654"/>
    <cge:TPSR_Ref TObjectID="24140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2265.338841 -638.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24160" ObjectName="SW-NH_MJ.NH_MJ_0711SW"/>
     <cge:Meas_Ref ObjectId="131730"/>
    <cge:TPSR_Ref TObjectID="24160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-262667">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2745.712348 -1089.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24163" ObjectName="SW-NH_MJ.NH_MJ_3011SW"/>
     <cge:Meas_Ref ObjectId="262667"/>
    <cge:TPSR_Ref TObjectID="24163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2744.712348 -739.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24165" ObjectName="SW-NH_MJ.NH_MJ_0011SW"/>
     <cge:Meas_Ref ObjectId="131747"/>
    <cge:TPSR_Ref TObjectID="24165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131763">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3113.793845 -1089.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24167" ObjectName="SW-NH_MJ.NH_MJ_3021SW"/>
     <cge:Meas_Ref ObjectId="131763"/>
    <cge:TPSR_Ref TObjectID="24167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3112.793845 -739.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24169" ObjectName="SW-NH_MJ.NH_MJ_0021SW"/>
     <cge:Meas_Ref ObjectId="131769"/>
    <cge:TPSR_Ref TObjectID="24169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2922.000000 -1165.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24138" ObjectName="SW-NH_MJ.NH_MJ_3711SW"/>
     <cge:Meas_Ref ObjectId="131650"/>
    <cge:TPSR_Ref TObjectID="24138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-262651">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2921.000000 -1309.558877)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24139" ObjectName="SW-NH_MJ.NH_MJ_3716SW"/>
     <cge:Meas_Ref ObjectId="262651"/>
    <cge:TPSR_Ref TObjectID="24139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-262658">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2838.000000 -1330.558877)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24171" ObjectName="SW-NH_MJ.NH_MJ_3806SW"/>
     <cge:Meas_Ref ObjectId="262658"/>
    <cge:TPSR_Ref TObjectID="24171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131664">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2569.976118 -605.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24142" ObjectName="SW-NH_MJ.NH_MJ_0721SW"/>
     <cge:Meas_Ref ObjectId="131664"/>
    <cge:TPSR_Ref TObjectID="24142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2569.976118 -492.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24143" ObjectName="SW-NH_MJ.NH_MJ_0726SW"/>
     <cge:Meas_Ref ObjectId="131665"/>
    <cge:TPSR_Ref TObjectID="24143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131708">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3632.216940 -596.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24154" ObjectName="SW-NH_MJ.NH_MJ_0761SW"/>
     <cge:Meas_Ref ObjectId="131708"/>
    <cge:TPSR_Ref TObjectID="24154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131709">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3632.216940 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24155" ObjectName="SW-NH_MJ.NH_MJ_0766SW"/>
     <cge:Meas_Ref ObjectId="131709"/>
    <cge:TPSR_Ref TObjectID="24155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.279570 -603.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24172" ObjectName="SW-NH_MJ.NH_MJ_0901SW"/>
     <cge:Meas_Ref ObjectId="131787"/>
    <cge:TPSR_Ref TObjectID="24172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131783">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3402.000000 -1065.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24170" ObjectName="SW-NH_MJ.NH_MJ_3901SW"/>
     <cge:Meas_Ref ObjectId="131783"/>
    <cge:TPSR_Ref TObjectID="24170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3487.600000 -1316.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29426" ObjectName="SW-NH_MJ.NH_MJ_37267SW"/>
     <cge:Meas_Ref ObjectId="193516"/>
    <cge:TPSR_Ref TObjectID="29426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193512">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 -1159.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29424" ObjectName="SW-NH_MJ.NH_MJ_3721SW"/>
     <cge:Meas_Ref ObjectId="193512"/>
    <cge:TPSR_Ref TObjectID="29424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193514">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 -1284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29425" ObjectName="SW-NH_MJ.NH_MJ_3726SW"/>
     <cge:Meas_Ref ObjectId="193514"/>
    <cge:TPSR_Ref TObjectID="29425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3480.600000 -1250.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29428" ObjectName="SW-NH_MJ.NH_MJ_37260SW"/>
     <cge:Meas_Ref ObjectId="193515"/>
    <cge:TPSR_Ref TObjectID="29428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193513">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3484.600000 -1189.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29427" ObjectName="SW-NH_MJ.NH_MJ_37217SW"/>
     <cge:Meas_Ref ObjectId="193513"/>
    <cge:TPSR_Ref TObjectID="29427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3012.081413 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24149" ObjectName="SW-NH_MJ.NH_MJ_0746SW"/>
     <cge:Meas_Ref ObjectId="131687"/>
    <cge:TPSR_Ref TObjectID="24149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131686">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3012.081413 -599.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24148" ObjectName="SW-NH_MJ.NH_MJ_0741SW"/>
     <cge:Meas_Ref ObjectId="131686"/>
    <cge:TPSR_Ref TObjectID="24148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2797.674935 -599.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24145" ObjectName="SW-NH_MJ.NH_MJ_0731SW"/>
     <cge:Meas_Ref ObjectId="131675"/>
    <cge:TPSR_Ref TObjectID="24145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2797.674935 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24146" ObjectName="SW-NH_MJ.NH_MJ_0736SW"/>
     <cge:Meas_Ref ObjectId="131676"/>
    <cge:TPSR_Ref TObjectID="24146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131697">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3434.280230 -605.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24151" ObjectName="SW-NH_MJ.NH_MJ_0751SW"/>
     <cge:Meas_Ref ObjectId="131697"/>
    <cge:TPSR_Ref TObjectID="24151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131698">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3434.280230 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24152" ObjectName="SW-NH_MJ.NH_MJ_0756SW"/>
     <cge:Meas_Ref ObjectId="131698"/>
    <cge:TPSR_Ref TObjectID="24152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131719">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3241.280230 -614.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24157" ObjectName="SW-NH_MJ.NH_MJ_0771SW"/>
     <cge:Meas_Ref ObjectId="131719"/>
    <cge:TPSR_Ref TObjectID="24157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-131720">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3241.280230 -500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24158" ObjectName="SW-NH_MJ.NH_MJ_0776SW"/>
     <cge:Meas_Ref ObjectId="131720"/>
    <cge:TPSR_Ref TObjectID="24158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1811.000000 -1151.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1801.000000 -927.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1805.000000 -815.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1806.000000 -704.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1812.000000 -589.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1808.000000 -1038.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1754.000000 -472.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1824.000000 -423.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1901.000000 -1109.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1899.000000 -998.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1889.000000 -887.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1896.000000 -775.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1899.000000 -664.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1901.000000 -549.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257204">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2980.600000 -1199.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42327" ObjectName="SW-NH_MJ.NH_MJ_37117SW"/>
     <cge:Meas_Ref ObjectId="257204"/>
    <cge:TPSR_Ref TObjectID="42327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257478">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2708.400000 -1120.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42330" ObjectName="SW-NH_MJ.NH_MJ_30117SW"/>
     <cge:Meas_Ref ObjectId="257478"/>
    <cge:TPSR_Ref TObjectID="42330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 2399.000000 -678.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3073.400000 -1100.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42331" ObjectName="SW-NH_MJ.NH_MJ_30217SW"/>
     <cge:Meas_Ref ObjectId="257606"/>
    <cge:TPSR_Ref TObjectID="42331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3361.400000 -1151.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42333" ObjectName="SW-NH_MJ.NH_MJ_39010SW"/>
     <cge:Meas_Ref ObjectId="257741"/>
    <cge:TPSR_Ref TObjectID="42333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-257740">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3461.600000 -1024.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42332" ObjectName="SW-NH_MJ.NH_MJ_39017SW"/>
     <cge:Meas_Ref ObjectId="257740"/>
    <cge:TPSR_Ref TObjectID="42332"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/></g>
   <g href="cx_配调_配网接线图35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/></g>
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1413" y="-1404"/></g>
   <g href="35kV马街变35kV八马线372间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3424" y="-1256"/></g>
   <g href="35kV马街变10kV唐家线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2589" y="-573"/></g>
   <g href="35kV马街变10kV机关线076间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3651" y="-564"/></g>
   <g href="35kV马街变10kV电容器组071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2289" y="-615"/></g>
   <g href="35kV马街变35kV大马红线371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2947" y="-1270"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1682" y="-1353"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1682" y="-1392"/></g>
   <g href="35kV马街变NH_MJ_GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="1373" y="-988"/></g>
   <g href="35kV马街变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2665" y="-967"/></g>
   <g href="35kV马街变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="3029" y="-962"/></g>
   <g href="AVC马街站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="1845" y="-1384"/></g>
   <g href="35kV马街变10kV平掌孜线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3031" y="-565"/></g>
   <g href="35kV马街变10kV后山线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2816" y="-565"/></g>
   <g href="35kV马街变10kV法空线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3453" y="-571"/></g>
   <g href="35kV马街变10kV兔街线077间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3260" y="-576"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_298b960">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 1.000000 0.000000 2760.400000 -1313.225544)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb0730">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2838.000000 -1268.558877)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c46fc0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2619.548972 -343.897163)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfd280">
    <use class="BV-10KV" transform="matrix(-0.857143 -0.000000 0.000000 -1.000000 3826.229570 -451.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfe5e0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3870.562904 -479.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4b810">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.229570 -488.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4c0f0">
    <use class="BV-35KV" transform="matrix(0.857143 -0.000000 -0.000000 -1.000000 3349.000000 -929.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4d520">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3468.333333 -951.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c428a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3352.000000 -966.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b9cbd0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 3528.000000 -1396.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c90bd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2569.000000 -441.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c91580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3631.000000 -480.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7d260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.000000 -1070.897163)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb2e60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1845.000000 -504.231143)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b223a0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3058.247789 -338.897163)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c684c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3011.000000 -434.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b69a00">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2845.247789 -339.062677)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b6bec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2797.000000 -432.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bedb60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3433.000000 -480.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ba2290">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3240.000000 -443.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ba3c70">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3288.247789 -335.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ba6c40">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3678.247789 -338.649789)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b7c0c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1960.000000 -622.204907)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1ea40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1959.000000 -1184.897163)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2affee0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1999.500000 -1146.397163)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b01d20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1962.000000 -736.295354)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b02a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1955.000000 -960.295354)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b03840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1955.000000 -848.295354)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b05380">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1836.000000 -467.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b51860">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3455.000000 -1398.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b527a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3415.976118 -1311.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b56160">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3481.247789 -340.649789)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="NH_MJ"/>
</svg>