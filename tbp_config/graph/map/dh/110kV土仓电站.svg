<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684666369" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666666" x2="27.83333333333334" y1="6.416666666666666" y2="22.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.5" x2="28.5" y1="3.833333333333332" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.16666666666667" x2="35.5" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.5" x2="21.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:RT1122_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="22.91666666666667" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="18.16666666666666" y2="20.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="18.16666666666666" y2="20.16666666666666"/>
   <path d="M 13 11 L 17 11 L 15 8 L 13 11 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.41666666666666" y2="18.16666666666666"/>
   <ellipse cx="14.95" cy="18.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="9.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路911_0" viewBox="0,0,30,38">
   <use terminal-index="0" type="0" x="6.95833333333333" xlink:href="#terminal" y="37.9408407585088"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.25545055364641" x2="14.25545055364641" y1="12.29138864447597" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.21058610156545" x2="6.90436235204273" y1="12.17541949757221" y2="12.17541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66094883543335" x2="17.13823024054981" y1="12.17541949757221" y2="12.17541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.910223367697554" x2="6.910223367697554" y1="38.08333333333334" y2="0.08333333333333215"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.21383161512025" x2="24.16666666666666" y1="12.24758812251703" y2="12.24758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.10940244368076" x2="17.10940244368076" y1="12.24758812251703" y2="12.24758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.11598892707138" x2="24.11598892707138" y1="8.999999999999996" y2="15.49517624503405"/>
   <ellipse cx="14.26" cy="23.07" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.3" cy="27.33" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.51" cy="31.33" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="17.10940244368077" x2="17.10940244368077" y1="9.733252937417257" y2="14.83409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.26910080183275" x2="25.26910080183275" y1="10.26295093653441" y2="14.59306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.13393470790376" x2="26.13393470790376" y1="11.16505874834466" y2="13.14969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.325897289041579" x2="9.325897289041579" y1="12.24758812251703" y2="12.24758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.27148720885832" x2="19.27148720885832" y1="9.733252937417255" y2="14.8340919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.242563955708249" x2="9.242563955708249" y1="9.583333333333334" y2="14.68417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.48798205421914" x2="11.48798205421914" y1="9.733252937417257" y2="14.83409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="16.66666666666667" y1="23.33333333333334" y2="23.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.08333333333334" x2="24.08333333333334" y1="27.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="17.33333333333334" y1="31.25" y2="31.25"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变YD2022_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="24" y1="24" y2="18"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="ACLineSegment:220kV线路_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.20833333333333" xlink:href="#terminal" y="39.00547521122963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="30.75" y2="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="27.5" y1="25.75" y2="30.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.50545055364641" x2="21.50545055364641" y1="13.04138864447597" y2="19.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="24.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="5.5" y1="37" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.91094883543335" x2="24.38823024054981" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="18.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="25.75" y2="27.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="6.5" y1="36" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="7.5" y1="35" y2="35"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.04,24.92) scale(1,1) translate(0,0)" width="6.08" x="2" y="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="32" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="4" y1="25.75" y2="19.75"/>
   <path d="M 14 13 L 5 13 L 5 25 L 5 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="6" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="21.5" y1="20.75" y2="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.46058610156545" x2="14.15436235204273" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16022336769755" x2="14.16022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46383161512025" x2="31.41666666666666" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.35940244368076" x2="24.35940244368076" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.36598892707138" x2="31.36598892707138" y1="9.749999999999996" y2="16.24517624503405"/>
   <ellipse cx="21.51" cy="23.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.55" cy="28.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.76" cy="32.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.35940244368077" x2="24.35940244368077" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51910080183274" x2="32.51910080183274" y1="11.01295093653441" y2="15.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.38393470790376" x2="33.38393470790376" y1="11.91505874834466" y2="13.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.57589728904158" x2="16.57589728904158" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.52148720885832" x2="26.52148720885832" y1="10.48325293741726" y2="15.5840919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49256395570825" x2="16.49256395570825" y1="10.33333333333333" y2="15.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="18.73798205421914" x2="18.73798205421914" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="18.58333333333333" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333334" x2="24.58333333333334" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="21.58333333333333" y1="29.5" y2="32.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV土仓电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="47" xlink:href="logo.png" y="36.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,175,66.25) scale(1,1) translate(0,0)" writing-mode="lr" x="175" xml:space="preserve" y="69.75" zvalue="27"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,175.5,65.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="175.5" xml:space="preserve" y="74.94" zvalue="28">110kV土仓电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="113" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="432"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="432">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1642.22,324.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1642.22" xml:space="preserve" y="328.64" zvalue="3">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,511.688,685.444) scale(1,1) translate(0,0)" writing-mode="lr" x="511.69" xml:space="preserve" y="689.9400000000001" zvalue="6">10.5kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.333,225.361) scale(1,1) translate(0,0)" writing-mode="lr" x="792.33" xml:space="preserve" y="229.86" zvalue="7">161</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,783.556,272.917) scale(1,1) translate(0,3.79465e-13)" writing-mode="lr" x="783.5599999999999" xml:space="preserve" y="277.42" zvalue="9">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,785.222,164.917) scale(1,1) translate(0,0)" writing-mode="lr" x="785.22" xml:space="preserve" y="169.42" zvalue="11">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,762.212,74.6944) scale(1,1) translate(-1.55367e-13,0)" writing-mode="lr" x="762.21" xml:space="preserve" y="79.19" zvalue="16">110kV朗外河电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724.056,264.028) scale(1,1) translate(0,0)" writing-mode="lr" x="724.0599999999999" xml:space="preserve" y="268.53" zvalue="19">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724.056,211.583) scale(1,1) translate(0,0)" writing-mode="lr" x="724.0599999999999" xml:space="preserve" y="216.08" zvalue="21">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724.056,157.139) scale(1,1) translate(0,0)" writing-mode="lr" x="724.0599999999999" xml:space="preserve" y="161.64" zvalue="23">67</text>
  <line fill="none" id="198" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="381" x2="381" y1="4.25" y2="1034.25" zvalue="29"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1556.78,345.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1556.78" xml:space="preserve" y="349.97" zvalue="32">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1510.11,409.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.11" xml:space="preserve" y="413.97" zvalue="33">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.56,533.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.56" xml:space="preserve" y="538.42" zvalue="35">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1496.44,374.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1496.44" xml:space="preserve" y="378.97" zvalue="39">17</text>
  <line fill="none" id="182" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.000000000000455" x2="374" y1="140.1204926140824" y2="140.1204926140824" zvalue="46"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1600.53,515.722) scale(1,1) translate(-1.38936e-12,0)" writing-mode="lr" x="1600.527777777778" xml:space="preserve" y="520.2222222222222" zvalue="81">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070.67,548.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1070.67" xml:space="preserve" y="552.64" zvalue="110">064</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.65,450.158) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.65" xml:space="preserve" y="454.66" zvalue="117">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,601.25,607.25) scale(1,1) translate(0,0)" writing-mode="lr" x="601.25" xml:space="preserve" y="611.75" zvalue="123">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1162.13,677.806) scale(1,1) translate(0,0)" writing-mode="lr" x="1162.13" xml:space="preserve" y="682.3099999999999" zvalue="130">10.5kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,894.778,347.472) scale(1,1) translate(0,0)" writing-mode="lr" x="894.78" xml:space="preserve" y="351.97" zvalue="132">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.111,406.472) scale(1,1) translate(0,0)" writing-mode="lr" x="851.11" xml:space="preserve" y="410.97" zvalue="134">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,810.556,532.06) scale(1,1) translate(0,0)" writing-mode="lr" x="810.5599999999999" xml:space="preserve" y="536.5599999999999" zvalue="136">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.444,375.472) scale(1,1) translate(-7.33586e-13,0)" writing-mode="lr" x="833.4400000000001" xml:space="preserve" y="379.97" zvalue="140">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.901,510.5) scale(1,1) translate(0,0)" writing-mode="lr" x="962.9013038711244" xml:space="preserve" y="515" zvalue="148">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.17,223.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.17" xml:space="preserve" y="228.19" zvalue="194">162</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1616.06,163.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1616.06" xml:space="preserve" y="167.75" zvalue="196">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1601.68,68.0278) scale(1,1) translate(0,0)" writing-mode="lr" x="1601.68" xml:space="preserve" y="72.53" zvalue="199">110kV土仓电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1554.89,262.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1554.89" xml:space="preserve" y="266.86" zvalue="202">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1554.89,209.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1554.89" xml:space="preserve" y="214.42" zvalue="204">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1554.89,155.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1554.89" xml:space="preserve" y="159.97" zvalue="206">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1613.83,272.417) scale(1,1) translate(0,-3.49609e-13)" writing-mode="lr" x="1613.83" xml:space="preserve" y="276.92" zvalue="211">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1155.29,255.259) scale(1,1) translate(0,0)" writing-mode="lr" x="1155.29" xml:space="preserve" y="259.76" zvalue="215">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1073.33,249.694) scale(1,1) translate(-4.67774e-13,0)" writing-mode="lr" x="1073.33" xml:space="preserve" y="254.19" zvalue="217">19017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.41,151.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.41" xml:space="preserve" y="156.25" zvalue="223">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1073.33,290.861) scale(1,1) translate(-4.67552e-13,0)" writing-mode="lr" x="1073.33" xml:space="preserve" y="295.36" zvalue="225">19010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.948,626.667) scale(1,1) translate(0,0)" writing-mode="lr" x="913.95" xml:space="preserve" y="631.17" zvalue="229">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1576.8,626.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1576.8" xml:space="preserve" y="631.17" zvalue="235">0022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.67,630.456) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.67" xml:space="preserve" y="634.96" zvalue="243">0641</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1693.17,544.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1693.17" xml:space="preserve" y="548.89" zvalue="249">066</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1691.59,499.5) scale(1,1) translate(0,-1.08691e-13)" writing-mode="lr" x="1691.59" xml:space="preserve" y="504" zvalue="252">近区变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1688.17,626.706) scale(1,1) translate(0,0)" writing-mode="lr" x="1688.17" xml:space="preserve" y="631.21" zvalue="254">0662</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1266.25,608.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1266.25" xml:space="preserve" y="613" zvalue="261">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" x="633.8515625" xml:space="preserve" y="945.2369357854257" zvalue="268">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="633.8515625" xml:space="preserve" y="962.2369357854257" zvalue="268">17.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,607.122,761.919) scale(1,1) translate(0,0)" writing-mode="lr" x="607.12" xml:space="preserve" y="766.42" zvalue="274">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,591.44,709.919) scale(1,1) translate(0,0)" writing-mode="lr" x="591.4400000000001" xml:space="preserve" y="714.42" zvalue="276">0611</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.784,854.47) scale(1,1) translate(0,0)" writing-mode="lr" x="669.78" xml:space="preserve" y="858.97" zvalue="278">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,532.396,859.041) scale(1,1) translate(0,0)" writing-mode="lr" x="532.4" xml:space="preserve" y="863.54" zvalue="287">0911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" x="1001.3359375" xml:space="preserve" y="946.4869357854257" zvalue="305">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1001.3359375" xml:space="preserve" y="963.4869357854257" zvalue="305">17.5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="356" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070.56,735.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1070.56" xml:space="preserve" y="740.49" zvalue="308">#2磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.622,763.169) scale(1,1) translate(0,0)" writing-mode="lr" x="974.62" xml:space="preserve" y="767.67" zvalue="309">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,958.94,711.169) scale(1,1) translate(0,0)" writing-mode="lr" x="958.9400000000001" xml:space="preserve" y="715.67" zvalue="311">0621</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037.28,855.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.28" xml:space="preserve" y="860.22" zvalue="313">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.896,860.291) scale(1,1) translate(0,0)" writing-mode="lr" x="899.9" xml:space="preserve" y="864.79" zvalue="320">0921</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" x="1378.8359375" xml:space="preserve" y="943.9869357854257" zvalue="331">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1378.8359375" xml:space="preserve" y="960.9869357854257" zvalue="331">12MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="357" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1453.06,742.244) scale(1,1) translate(0,0)" writing-mode="lr" x="1453.06" xml:space="preserve" y="746.74" zvalue="334">#3励磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1352.12,760.669) scale(1,1) translate(0,0)" writing-mode="lr" x="1352.12" xml:space="preserve" y="765.17" zvalue="335">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336.44,708.669) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.44" xml:space="preserve" y="713.17" zvalue="337">0632</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1414.78,853.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1414.78" xml:space="preserve" y="857.72" zvalue="339">0932</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="317" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.4,857.791) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.4" xml:space="preserve" y="862.29" zvalue="346">0931</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1632.01,890.38) scale(1,1) translate(0,0)" writing-mode="lr" x="1632.01" xml:space="preserve" y="894.88" zvalue="357">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1652.19,752.78) scale(1,1) translate(0,0)" writing-mode="lr" x="1652.19" xml:space="preserve" y="757.28" zvalue="359">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="343" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1603.21,698.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1603.21" xml:space="preserve" y="703.12" zvalue="362">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="355" stroke="rgb(255,255,255)" text-anchor="middle" x="680.4765625" xml:space="preserve" y="711.9596827952298" zvalue="369">高频切机第</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="355" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="680.4765625" xml:space="preserve" y="728.9596827952298" zvalue="369">一轮</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1660.19,449.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1660.19" xml:space="preserve" y="454" zvalue="378">至首部</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="133" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="410"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="412">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="413">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="414">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="415">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="416">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="418">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="419">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="420">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="421">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="422">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="423">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.625,334) scale(1,1) translate(0,0)" writing-mode="lr" x="241.63" xml:space="preserve" y="338.5" zvalue="424">10.5kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="363.75" zvalue="425">10.5kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="433">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="434">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="437">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="439">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="441">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="442">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="443">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="445">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,181,955) scale(1,1) translate(0,0)" writing-mode="lr" x="181" xml:space="preserve" y="961" zvalue="452">TuCang-01-2012</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="432"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="223">
   <path class="kv110" d="M 598 306.92 L 1661 306.92" stroke-width="4" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422489091" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674422489091"/></metadata>
  <path d="M 598 306.92 L 1661 306.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="v10500" d="M 504.13 666.14 L 1075 666.14" stroke-width="4" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422423555" ObjectName="10.5kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674422423555"/></metadata>
  <path d="M 504.13 666.14 L 1075 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="v10500" d="M 1170.13 666.14 L 1768 666.14" stroke-width="4" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422358019" ObjectName="10.5kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674422358019"/></metadata>
  <path d="M 1170.13 666.14 L 1768 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="221">
   <use class="kv110" height="20" transform="rotate(0,771,226.361) scale(1.22222,1.11111) translate(-139.071,-21.525)" width="10" x="764.8888888888889" xlink:href="#Breaker:开关_0" y="215.25" zvalue="5"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220499459" ObjectName="110kV朗外河电站线161断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220499459"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,771,226.361) scale(1.22222,1.11111) translate(-139.071,-21.525)" width="10" x="764.8888888888889" y="215.25"/></g>
  <g id="195">
   <use class="kv110" height="20" transform="rotate(0,1545.33,410.472) scale(1.22222,1.11111) translate(-279.859,-39.9361)" width="10" x="1539.222232407994" xlink:href="#Breaker:开关_0" y="399.3611111111111" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220433923" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220433923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1545.33,410.472) scale(1.22222,1.11111) translate(-279.859,-39.9361)" width="10" x="1539.222232407994" y="399.3611111111111"/></g>
  <g id="249">
   <use class="v10500" height="20" transform="rotate(0,1040.27,549.903) scale(1.14306,1.14306) translate(-129.476,-67.3907)" width="10" x="1034.555555555556" xlink:href="#Breaker:开关_0" y="538.4722256130644" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220368387" ObjectName="#1站用变064断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220368387"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1040.27,549.903) scale(1.14306,1.14306) translate(-129.476,-67.3907)" width="10" x="1034.555555555556" y="538.4722256130644"/></g>
  <g id="185">
   <use class="kv110" height="20" transform="rotate(0,882.333,407.472) scale(1.22222,1.11111) translate(-159.313,-39.6361)" width="10" x="876.2222324079938" xlink:href="#Breaker:开关_0" y="396.3611111111111" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220302851" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220302851"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,882.333,407.472) scale(1.22222,1.11111) translate(-159.313,-39.6361)" width="10" x="876.2222324079938" y="396.3611111111111"/></g>
  <g id="214">
   <use class="kv110" height="20" transform="rotate(0,1601.83,224.694) scale(1.22222,1.11111) translate(-290.131,-21.3583)" width="10" x="1595.722222222222" xlink:href="#Breaker:开关_0" y="213.5833333333334" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220237315" ObjectName="110kV土仓电站线162断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220237315"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1601.83,224.694) scale(1.22222,1.11111) translate(-290.131,-21.3583)" width="10" x="1595.722222222222" y="213.5833333333334"/></g>
  <g id="238">
   <use class="v10500" height="20" transform="rotate(0,1662.77,546.153) scale(1.14306,1.14306) translate(-207.383,-66.9214)" width="10" x="1657.055555555556" xlink:href="#Breaker:开关_0" y="534.7222256130644" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220564996" ObjectName="近区变066断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220564996"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1662.77,546.153) scale(1.14306,1.14306) translate(-207.383,-66.9214)" width="10" x="1657.055555555556" y="534.7222256130644"/></g>
  <g id="268">
   <use class="v10500" height="20" transform="rotate(0,632.784,762.875) scale(1.59229,1.43307) translate(-232.419,-226.206)" width="10" x="624.8229380627334" xlink:href="#Breaker:开关_0" y="748.5439675338483" zvalue="273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220630531" ObjectName="#1发电机061断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220630531"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,632.784,762.875) scale(1.59229,1.43307) translate(-232.419,-226.206)" width="10" x="624.8229380627334" y="748.5439675338483"/></g>
  <g id="312">
   <use class="v10500" height="20" transform="rotate(0,1000.28,764.125) scale(1.59229,1.43307) translate(-369.12,-226.584)" width="10" x="992.3229380627333" xlink:href="#Breaker:开关_0" y="749.7939675338483" zvalue="308"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220696067" ObjectName="#2发电机062断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220696067"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1000.28,764.125) scale(1.59229,1.43307) translate(-369.12,-226.584)" width="10" x="992.3229380627333" y="749.7939675338483"/></g>
  <g id="338">
   <use class="v10500" height="20" transform="rotate(0,1377.78,761.625) scale(1.59229,1.43307) translate(-509.541,-225.829)" width="10" x="1369.822938062734" xlink:href="#Breaker:开关_0" y="747.2939675338483" zvalue="334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220761603" ObjectName="#3发电机063断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220761603"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1377.78,761.625) scale(1.59229,1.43307) translate(-509.541,-225.829)" width="10" x="1369.822938062734" y="747.2939675338483"/></g>
  <g id="352">
   <use class="v10500" height="20" transform="rotate(0,1628.43,753.756) scale(1.55425,1.39882) translate(-577.93,-210.917)" width="10" x="1620.659093348474" xlink:href="#Breaker:开关_0" y="739.7675450491263" zvalue="358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925220827139" ObjectName="#2站用变065断路器"/>
   <cge:TPSR_Ref TObjectID="6473925220827139"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1628.43,753.756) scale(1.55425,1.39882) translate(-577.93,-210.917)" width="10" x="1620.659093348474" y="739.7675450491263"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="220">
   <use class="kv110" height="30" transform="rotate(0,771,273.917) scale(-1.11111,-0.814815) translate(-1464.07,-612.865)" width="15" x="762.6666666666666" xlink:href="#Disconnector:刀闸_0" y="261.6944580078125" zvalue="8"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816366594" ObjectName="110kV朗外河电站线1611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454816366594"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,771,273.917) scale(-1.11111,-0.814815) translate(-1464.07,-612.865)" width="15" x="762.6666666666666" y="261.6944580078125"/></g>
  <g id="219">
   <use class="kv110" height="30" transform="rotate(0,771,165.917) scale(-1.11111,-0.814815) translate(-1464.07,-372.319)" width="15" x="762.6666666931577" xlink:href="#Disconnector:刀闸_0" y="153.6944444444445" zvalue="10"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816301058" ObjectName="110kV朗外河电站线1616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454816301058"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,771,165.917) scale(-1.11111,-0.814815) translate(-1464.07,-372.319)" width="15" x="762.6666666931577" y="153.6944444444445"/></g>
  <g id="197">
   <use class="kv110" height="30" transform="rotate(0,1545.33,346.472) scale(1.11111,0.814815) translate(-153.7,75.9659)" width="15" x="1537.000010172526" xlink:href="#Disconnector:刀闸_0" y="334.25" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815776770" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454815776770"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1545.33,346.472) scale(1.11111,0.814815) translate(-153.7,75.9659)" width="15" x="1537.000010172526" y="334.25"/></g>
  <g id="250">
   <use class="v10500" height="25" transform="rotate(0,571,608.25) scale(0.311111,-1.44) translate(1248.86,-1025.15)" width="45" x="564" xlink:href="#Disconnector:特殊刀闸_0" y="590.25" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815318018" ObjectName="10.5kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454815318018"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,571,608.25) scale(0.311111,-1.44) translate(1248.86,-1025.15)" width="45" x="564" y="590.25"/></g>
  <g id="186">
   <use class="kv110" height="30" transform="rotate(0,883.333,348.472) scale(1.11111,0.814815) translate(-87.5,76.4205)" width="15" x="875.0000101725263" xlink:href="#Disconnector:刀闸_0" y="336.25" zvalue="131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815186946" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454815186946"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,883.333,348.472) scale(1.11111,0.814815) translate(-87.5,76.4205)" width="15" x="875.0000101725263" y="336.25"/></g>
  <g id="212">
   <use class="kv110" height="30" transform="rotate(0,1601.83,164.25) scale(-1.11111,-0.814815) translate(-3042.65,-368.607)" width="15" x="1593.500000026491" xlink:href="#Disconnector:刀闸_0" y="152.0277777777777" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814859266" ObjectName="110kV土仓电站线1626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454814859266"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1601.83,164.25) scale(-1.11111,-0.814815) translate(-3042.65,-368.607)" width="15" x="1593.500000026491" y="152.0277777777777"/></g>
  <g id="217">
   <use class="kv110" height="30" transform="rotate(0,1601.83,273.417) scale(-1.11111,-0.814815) translate(-3042.65,-611.751)" width="15" x="1593.500000026491" xlink:href="#Disconnector:刀闸_0" y="261.1944444444444" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814334978" ObjectName="110kV土仓电站线1621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454814334978"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1601.83,273.417) scale(-1.11111,-0.814815) translate(-3042.65,-611.751)" width="15" x="1593.500000026491" y="261.1944444444444"/></g>
  <g id="280">
   <use class="kv110" height="30" transform="rotate(0,1115.22,259.417) scale(-1.11111,-0.814815) translate(-2118.09,-580.569)" width="15" x="1106.888888888889" xlink:href="#Disconnector:刀闸_0" y="247.1944581137762" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814269442" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454814269442"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1115.22,259.417) scale(-1.11111,-0.814815) translate(-2118.09,-580.569)" width="15" x="1106.888888888889" y="247.1944581137762"/></g>
  <g id="282">
   <use class="v10500" height="30" transform="rotate(0,882.364,626.417) scale(-1.11111,-0.814815) translate(-1675.66,-1397.98)" width="15" x="874.0309335007539" xlink:href="#Disconnector:刀闸_0" y="614.1944580078125" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454813876226" ObjectName="#1主变10.5kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454813876226"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,882.364,626.417) scale(-1.11111,-0.814815) translate(-1675.66,-1397.98)" width="15" x="874.0309335007539" y="614.1944580078125"/></g>
  <g id="291">
   <use class="v10500" height="30" transform="rotate(0,1545.21,626.417) scale(-1.11111,-0.814815) translate(-2935.07,-1397.98)" width="15" x="1536.87962962963" xlink:href="#Disconnector:刀闸_0" y="614.1944580078125" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454813745154" ObjectName="#2主变10.5kV侧0022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454813745154"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1545.21,626.417) scale(-1.11111,-0.814815) translate(-2935.07,-1397.98)" width="15" x="1536.87962962963" y="614.1944580078125"/></g>
  <g id="224">
   <use class="v10500" height="25" transform="rotate(0,1040.33,631.456) scale(0.232099,-1.07429) translate(3424.66,-1218.32)" width="45" x="1035.104728485716" xlink:href="#Disconnector:特殊刀闸_0" y="618.0277777777778" zvalue="242"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816432130" ObjectName="#1站用变0641隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454816432130"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1040.33,631.456) scale(0.232099,-1.07429) translate(3424.66,-1218.32)" width="45" x="1035.104728485716" y="618.0277777777778"/></g>
  <g id="235">
   <use class="v10500" height="25" transform="rotate(0,1662.83,627.706) scale(0.232099,-1.07429) translate(5484.2,-1211.08)" width="45" x="1657.604728485716" xlink:href="#Disconnector:特殊刀闸_0" y="614.2777777777777" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816497666" ObjectName="近区变0662隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454816497666"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1662.83,627.706) scale(0.232099,-1.07429) translate(5484.2,-1211.08)" width="45" x="1657.604728485716" y="614.2777777777777"/></g>
  <g id="244">
   <use class="v10500" height="25" transform="rotate(0,1236,609.5) scale(0.311111,-1.44) translate(2721.36,-1027.26)" width="45" x="1229" xlink:href="#Disconnector:特殊刀闸_0" y="591.5" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816759810" ObjectName="10.5kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454816759810"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1236,609.5) scale(0.311111,-1.44) translate(2721.36,-1027.26)" width="45" x="1229" y="591.5"/></g>
  <g id="267">
   <use class="v10500" height="25" transform="rotate(0,632.666,708.567) scale(0.353843,0.934146) translate(1140.78,49.1282)" width="45" x="624.7049337208474" xlink:href="#Disconnector:特殊刀闸_0" y="696.8902229389662" zvalue="275"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817087490" ObjectName="#1发电机0611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454817087490"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,632.666,708.567) scale(0.353843,0.934146) translate(1140.78,49.1282)" width="45" x="624.7049337208474" y="696.8902229389662"/></g>
  <g id="266">
   <use class="v10500" height="25" transform="rotate(0,702.357,854.656) scale(0.353843,0.934146) translate(1268.04,59.4269)" width="45" x="694.3955620613175" xlink:href="#Disconnector:特殊刀闸_0" y="842.9796554901828" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817021954" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454817021954"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,702.357,854.656) scale(0.353843,0.934146) translate(1268.04,59.4269)" width="45" x="694.3955620613175" y="842.9796554901828"/></g>
  <g id="301">
   <use class="v10500" height="25" transform="rotate(0,565.049,857.733) scale(0.353843,0.934146) translate(1017.3,59.6438)" width="45" x="557.087869753625" xlink:href="#Disconnector:特殊刀闸_0" y="846.0565785671059" zvalue="285"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816890882" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454816890882"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,565.049,857.733) scale(0.353843,0.934146) translate(1017.3,59.6438)" width="45" x="557.087869753625" y="846.0565785671059"/></g>
  <g id="281">
   <use class="v10500" height="30" transform="rotate(0,565.014,897.375) scale(0.791667,0.791667) translate(147.125,233.026)" width="15" x="559.0764916090839" xlink:href="#Disconnector:令克_0" y="885.5" zvalue="300"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817349634" ObjectName="#1发电机令克"/>
   <cge:TPSR_Ref TObjectID="6192454817349634"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,565.014,897.375) scale(0.791667,0.791667) translate(147.125,233.026)" width="15" x="559.0764916090839" y="885.5"/></g>
  <g id="311">
   <use class="v10500" height="25" transform="rotate(0,1000.17,709.817) scale(0.353843,0.934146) translate(1811.88,49.2163)" width="45" x="992.2049337208474" xlink:href="#Disconnector:特殊刀闸_0" y="698.1402229389662" zvalue="310"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817742850" ObjectName="#2发电机0621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454817742850"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1000.17,709.817) scale(0.353843,0.934146) translate(1811.88,49.2163)" width="45" x="992.2049337208474" y="698.1402229389662"/></g>
  <g id="310">
   <use class="v10500" height="25" transform="rotate(0,1069.86,855.906) scale(0.353843,0.934146) translate(1939.14,59.5151)" width="45" x="1061.895562061317" xlink:href="#Disconnector:特殊刀闸_0" y="844.2296554901828" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817677314" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454817677314"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1069.86,855.906) scale(0.353843,0.934146) translate(1939.14,59.5151)" width="45" x="1061.895562061317" y="844.2296554901828"/></g>
  <g id="303">
   <use class="v10500" height="25" transform="rotate(0,932.549,858.983) scale(0.353843,0.934146) translate(1688.4,59.732)" width="45" x="924.587869753625" xlink:href="#Disconnector:特殊刀闸_0" y="847.3065785671059" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817546242" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454817546242"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,932.549,858.983) scale(0.353843,0.934146) translate(1688.4,59.732)" width="45" x="924.587869753625" y="847.3065785671059"/></g>
  <g id="295">
   <use class="v10500" height="30" transform="rotate(0,932.514,898.625) scale(0.791667,0.791667) translate(243.836,233.355)" width="15" x="926.5764916090839" xlink:href="#Disconnector:令克_0" y="886.75" zvalue="326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817415170" ObjectName="#2发电机令克"/>
   <cge:TPSR_Ref TObjectID="6192454817415170"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,932.514,898.625) scale(0.791667,0.791667) translate(243.836,233.355)" width="15" x="926.5764916090839" y="886.75"/></g>
  <g id="337">
   <use class="v10500" height="25" transform="rotate(0,1377.67,707.317) scale(0.353843,0.934146) translate(2501.23,49.04)" width="45" x="1369.704933720847" xlink:href="#Disconnector:特殊刀闸_0" y="695.6402229389662" zvalue="336"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818332674" ObjectName="#3发电机0632隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454818332674"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1377.67,707.317) scale(0.353843,0.934146) translate(2501.23,49.04)" width="45" x="1369.704933720847" y="695.6402229389662"/></g>
  <g id="336">
   <use class="v10500" height="25" transform="rotate(0,1447.36,853.406) scale(0.353843,0.934146) translate(2628.49,59.3388)" width="45" x="1439.395562061317" xlink:href="#Disconnector:特殊刀闸_0" y="841.7296554901828" zvalue="338"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818267138" ObjectName="#3发电机0932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454818267138"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1447.36,853.406) scale(0.353843,0.934146) translate(2628.49,59.3388)" width="45" x="1439.395562061317" y="841.7296554901828"/></g>
  <g id="330">
   <use class="v10500" height="25" transform="rotate(0,1310.05,856.483) scale(0.353843,0.934146) translate(2377.76,59.5557)" width="45" x="1302.087869753625" xlink:href="#Disconnector:特殊刀闸_0" y="844.8065785671059" zvalue="345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818136066" ObjectName="#3发电机0931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454818136066"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1310.05,856.483) scale(0.353843,0.934146) translate(2377.76,59.5557)" width="45" x="1302.087869753625" y="844.8065785671059"/></g>
  <g id="324">
   <use class="v10500" height="30" transform="rotate(0,1310.01,896.125) scale(0.791667,0.791667) translate(343.178,232.697)" width="15" x="1304.076491609084" xlink:href="#Disconnector:令克_0" y="884.25" zvalue="352"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818004994" ObjectName="#3发电机令克"/>
   <cge:TPSR_Ref TObjectID="6192454818004994"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1310.01,896.125) scale(0.791667,0.791667) translate(343.178,232.697)" width="15" x="1304.076491609084" y="884.25"/></g>
  <g id="350">
   <use class="v10500" height="25" transform="rotate(0,1628.67,704.557) scale(0.345388,0.911825) translate(3072.07,67.0298)" width="45" x="1620.896955180224" xlink:href="#Disconnector:特殊刀闸_0" y="693.1592963335944" zvalue="361"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818660354" ObjectName="#2站用变0652隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454818660354"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1628.67,704.557) scale(0.345388,0.911825) translate(3072.07,67.0298)" width="45" x="1620.896955180224" y="693.1592963335944"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="218">
   <path class="kv110" d="M 770.9 285.73 L 770.9 306.92" stroke-width="1" zvalue="12"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="223@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 770.9 285.73 L 770.9 306.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv110" d="M 770.93 261.9 L 770.93 236.97" stroke-width="1" zvalue="13"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@1" LinkObjectIDznd="221@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 770.93 261.9 L 770.93 236.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv110" d="M 770.96 215.73 L 770.9 177.73" stroke-width="1" zvalue="14"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 770.96 215.73 L 770.9 177.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv110" d="M 770.93 153.9 L 770.93 132.59" stroke-width="1" zvalue="17"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@1" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 770.93 153.9 L 770.93 132.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv110" d="M 733.44 249.53 L 771.06 249.53" stroke-width="1" zvalue="24"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.44 249.53 L 771.06 249.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv110" d="M 733.44 197.08 L 770.93 197.08" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.44 197.08 L 770.93 197.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv110" d="M 733.44 142.64 L 770.93 142.64" stroke-width="1" zvalue="26"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="211" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.44 142.64 L 770.93 142.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv110" d="M 1545.4 358.49 L 1545.4 399.84" stroke-width="1" zvalue="36"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@1" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545.4 358.49 L 1545.4 399.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv110" d="M 1545.28 490.98 L 1414.89 490.98 L 1414.89 521.36" stroke-width="1" zvalue="37"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@2" LinkObjectIDznd="194@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545.28 490.98 L 1414.89 490.98 L 1414.89 521.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv110" d="M 1507.28 362.31 L 1545.4 362.31" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="193" MaxPinNum="2"/>
   </metadata>
  <path d="M 1507.28 362.31 L 1545.4 362.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv110" d="M 1545.43 334.65 L 1545.43 306.92" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="223@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545.43 334.65 L 1545.43 306.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="v10500" d="M 571.02 625.17 L 571.02 666.14" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="222@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 571.02 625.17 L 571.02 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="v10500" d="M 571.02 591.04 L 571.02 580.13" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@1" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 571.02 591.04 L 571.02 580.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv110" d="M 883.4 360.49 L 883.4 396.84" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.4 360.49 L 883.4 396.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv110" d="M 882.43 487.98 L 751.89 487.98 L 751.89 519.5" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@2" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 882.43 487.98 L 751.89 487.98 L 751.89 519.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv110" d="M 844.28 363.31 L 883.4 363.31" stroke-width="1" zvalue="141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 844.28 363.31 L 883.4 363.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 883.43 336.65 L 883.43 306.92" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="223@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.43 336.65 L 883.43 306.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv110" d="M 1601.79 214.06 L 1601.74 176.07" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1601.79 214.06 L 1601.74 176.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv110" d="M 1601.77 152.24 L 1601.77 126.96" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@1" LinkObjectIDznd="208@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1601.77 152.24 L 1601.77 126.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv110" d="M 1564.27 247.86 L 1601.9 247.86" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1564.27 247.86 L 1601.9 247.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv110" d="M 1564.28 195.42 L 1601.76 195.42" stroke-width="1" zvalue="208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 1564.28 195.42 L 1601.76 195.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 1564.28 140.97 L 1601.77 140.97" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 1564.28 140.97 L 1601.77 140.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv110" d="M 1601.91 235.31 L 1601.91 261.4" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@1" LinkObjectIDznd="217@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1601.91 235.31 L 1601.91 261.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 1601.74 285.23 L 1601.74 306.92" stroke-width="1" zvalue="213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="223@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1601.74 285.23 L 1601.74 306.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv110" d="M 1115.12 271.23 L 1115.12 306.92" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="223@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.12 271.23 L 1115.12 306.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 1115.15 247.4 L 1115.15 220.12" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="274@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.15 247.4 L 1115.15 220.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv110" d="M 1084.17 233.53 L 1115.15 233.53" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="69" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.17 233.53 L 1115.15 233.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv110" d="M 1084.17 277.36 L 1115.12 277.36" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.17 277.36 L 1115.12 277.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv110" d="M 882.41 418.08 L 882.47 460.14" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 882.41 418.08 L 882.47 460.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v10500" d="M 882.4 556.72 L 882.4 614.4" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="282@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 882.4 556.72 L 882.4 614.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v10500" d="M 882.27 638.23 L 882.27 666.14" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@0" LinkObjectIDznd="222@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 882.27 638.23 L 882.27 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="v10500" d="M 918.13 599.94 L 882.4 599.94" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 918.13 599.94 L 882.4 599.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="v10500" d="M 1583.13 599.94 L 1545.25 599.94" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 1583.13 599.94 L 1545.25 599.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v10500" d="M 1545.25 559.72 L 1545.25 614.4" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@1" LinkObjectIDznd="291@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545.25 559.72 L 1545.25 614.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="v10500" d="M 1545.12 638.23 L 1545.12 666.14" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@0" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545.12 638.23 L 1545.12 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 1545.41 421.08 L 1545.32 463.14" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@1" LinkObjectIDznd="135@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545.41 421.08 L 1545.32 463.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="v10500" d="M 1041.09 523.96 L 1041.09 538.97" stroke-width="1" zvalue="243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="249@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.09 523.96 L 1041.09 538.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v10500" d="M 1040.35 560.82 L 1040.34 618.62" stroke-width="1" zvalue="244"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="224@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.35 560.82 L 1040.34 618.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="v10500" d="M 1040.34 644.08 L 1040.34 666.14" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="222@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.34 644.08 L 1040.34 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="v10500" d="M 1065.3 576.14 L 1040.34 576.14" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="107" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.3 576.14 L 1040.34 576.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="v10500" d="M 1661.84 510.52 L 1661.84 535.22" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="238@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1661.84 510.52 L 1661.84 535.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="v10500" d="M 1662.85 557.07 L 1662.84 614.87" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@1" LinkObjectIDznd="235@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1662.85 557.07 L 1662.84 614.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="v10500" d="M 1662.84 640.33 L 1662.84 666.14" stroke-width="1" zvalue="257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="187@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1662.84 640.33 L 1662.84 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="v10500" d="M 1687.8 572.39 L 1662.84 572.39" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="233" MaxPinNum="2"/>
   </metadata>
  <path d="M 1687.8 572.39 L 1662.84 572.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="v10500" d="M 1236.02 626.42 L 1236.02 666.14" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@0" LinkObjectIDznd="187@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1236.02 626.42 L 1236.02 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="v10500" d="M 1236.02 592.29 L 1236.02 581.38" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@1" LinkObjectIDznd="243@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1236.02 592.29 L 1236.02 581.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="v10500" d="M 632.73 749.16 L 632.68 719.73" stroke-width="1" zvalue="279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="267@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 632.73 749.16 L 632.68 719.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="v10500" d="M 632.89 776.56 L 632.89 882.92" stroke-width="1" zvalue="280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="270@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 632.89 776.56 L 632.89 882.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="v10500" d="M 701.3 884.84 L 701.3 865.82" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="266@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.3 884.84 L 701.3 865.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="v10500" d="M 702.37 843.68 L 702.39 825.97 L 632.89 825.97" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="264" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.37 843.68 L 702.39 825.97 L 632.89 825.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="v10500" d="M 652.38 809.24 L 632.89 809.24" stroke-width="1" zvalue="294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="264" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.38 809.24 L 632.89 809.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="v10500" d="M 632.68 697.59 L 632.68 666.14" stroke-width="1" zvalue="295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="222@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 632.68 697.59 L 632.68 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="v10500" d="M 565.07 846.76 L 565.08 830.5 L 632.89 830.5" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@0" LinkObjectIDznd="264" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.07 846.76 L 565.08 830.5 L 632.89 830.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="v10500" d="M 701.81 792.13 L 701.81 825.97" stroke-width="1" zvalue="299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@0" LinkObjectIDznd="262" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.81 792.13 L 701.81 825.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="v10500" d="M 563.44 910.21 L 563.44 907.07" stroke-width="1" zvalue="301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="281@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 563.44 910.21 L 563.44 907.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="284">
   <path class="v10500" d="M 565.08 886.89 L 565.07 868.9" stroke-width="1" zvalue="302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.08 886.89 L 565.07 868.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="v10500" d="M 1000.23 750.41 L 1000.18 720.98" stroke-width="1" zvalue="314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="311@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.23 750.41 L 1000.18 720.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="v10500" d="M 1000.39 777.81 L 1000.39 884.17" stroke-width="1" zvalue="315"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@1" LinkObjectIDznd="316@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.39 777.81 L 1000.39 884.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="v10500" d="M 1068.8 886.09 L 1068.8 867.07" stroke-width="1" zvalue="316"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="310@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1068.8 886.09 L 1068.8 867.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="v10500" d="M 1069.87 844.93 L 1069.89 827.22 L 1000.39 827.22" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="310@0" LinkObjectIDznd="307" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.87 844.93 L 1069.89 827.22 L 1000.39 827.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="v10500" d="M 1019.88 810.49 L 1000.39 810.49" stroke-width="1" zvalue="322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="302@0" LinkObjectIDznd="307" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.88 810.49 L 1000.39 810.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="v10500" d="M 1000.18 698.84 L 1000.18 666.14" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="311@0" LinkObjectIDznd="222@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.18 698.84 L 1000.18 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="v10500" d="M 932.57 848.01 L 932.58 831.75 L 1000.39 831.75" stroke-width="1" zvalue="324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@0" LinkObjectIDznd="307" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.57 848.01 L 932.58 831.75 L 1000.39 831.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="v10500" d="M 1069.31 793.38 L 1069.31 827.22" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="305" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.31 793.38 L 1069.31 827.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="v10500" d="M 930.94 911.46 L 930.94 908.32" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@0" LinkObjectIDznd="295@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 930.94 911.46 L 930.94 908.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="v10500" d="M 932.58 888.14 L 932.57 870.15" stroke-width="1" zvalue="328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="295@0" LinkObjectIDznd="303@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.58 888.14 L 932.57 870.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="335">
   <path class="v10500" d="M 1377.73 747.91 L 1377.68 718.48" stroke-width="1" zvalue="340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="337@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1377.73 747.91 L 1377.68 718.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="334">
   <path class="v10500" d="M 1377.89 775.31 L 1377.89 881.67" stroke-width="1" zvalue="341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@1" LinkObjectIDznd="342@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1377.89 775.31 L 1377.89 881.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="333">
   <path class="v10500" d="M 1446.3 883.59 L 1446.3 864.57" stroke-width="1" zvalue="342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@0" LinkObjectIDznd="336@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.3 883.59 L 1446.3 864.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="332">
   <path class="v10500" d="M 1447.37 842.43 L 1447.39 824.72 L 1377.89 824.72" stroke-width="1" zvalue="343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@0" LinkObjectIDznd="334" MaxPinNum="2"/>
   </metadata>
  <path d="M 1447.37 842.43 L 1447.39 824.72 L 1377.89 824.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="328">
   <path class="v10500" d="M 1397.38 807.99 L 1377.89 807.99" stroke-width="1" zvalue="348"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@0" LinkObjectIDznd="334" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.38 807.99 L 1377.89 807.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="v10500" d="M 1377.68 696.34 L 1377.68 666.14" stroke-width="1" zvalue="349"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="337@0" LinkObjectIDznd="187@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1377.68 696.34 L 1377.68 666.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="v10500" d="M 1310.07 845.51 L 1310.08 829.25 L 1377.89 829.25" stroke-width="1" zvalue="350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="334" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.07 845.51 L 1310.08 829.25 L 1377.89 829.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="v10500" d="M 1446.81 790.88 L 1446.81 824.72" stroke-width="1" zvalue="351"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="339@0" LinkObjectIDznd="332" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.81 790.88 L 1446.81 824.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="323">
   <path class="v10500" d="M 1308.44 908.96 L 1308.44 905.82" stroke-width="1" zvalue="353"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331@0" LinkObjectIDznd="324@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1308.44 908.96 L 1308.44 905.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="v10500" d="M 1310.08 885.64 L 1310.07 867.65" stroke-width="1" zvalue="354"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@0" LinkObjectIDznd="330@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.08 885.64 L 1310.07 867.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="351">
   <path class="v10500" d="M 1628.53 767.11 L 1628.53 825.91" stroke-width="1" zvalue="360"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="352@1" LinkObjectIDznd="353@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1628.53 767.11 L 1628.53 825.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="349">
   <path class="v10500" d="M 1628.69 666.14 L 1628.69 693.84" stroke-width="1" zvalue="363"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@4" LinkObjectIDznd="350@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1628.69 666.14 L 1628.69 693.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="348">
   <path class="v10500" d="M 1628.69 715.45 L 1628.69 740.37" stroke-width="1" zvalue="364"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="350@1" LinkObjectIDznd="352@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1628.69 715.45 L 1628.69 740.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="358">
   <path class="v10500" d="M 1660.67 794.62 L 1628.53 794.62" stroke-width="1" zvalue="370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@0" LinkObjectIDznd="351" MaxPinNum="2"/>
   </metadata>
  <path d="M 1660.67 794.62 L 1628.53 794.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="213">
   <use class="kv110" height="40" transform="rotate(0,774.212,111.472) scale(1.26984,1.11111) translate(-159.798,-8.925)" width="35" x="751.9902406134634" xlink:href="#ACLineSegment:220kV线路_0" y="89.24999999999994" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249315278854" ObjectName="110kV朗外河电站线"/>
   <cge:TPSR_Ref TObjectID="8444249315278854_5066549684666369"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,774.212,111.472) scale(1.26984,1.11111) translate(-159.798,-8.925)" width="35" x="751.9902406134634" y="89.24999999999994"/></g>
  <g id="208">
   <use class="kv110" height="38" transform="rotate(0,1613.68,104.806) scale(1.48148,1.16959) translate(-517.223,-11.9746)" width="30" x="1591.456730913287" xlink:href="#ACLineSegment:线路911_0" y="82.58333333333314" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249309052934" ObjectName="110kV土仓电站线"/>
   <cge:TPSR_Ref TObjectID="8444249309052934_5066549684666369"/></metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1613.68,104.806) scale(1.48148,1.16959) translate(-517.223,-11.9746)" width="30" x="1591.456730913287" y="82.58333333333314"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="210">
   <use class="kv110" height="20" transform="rotate(90,722.611,249.472) scale(1.11111,1.11111) translate(-71.7056,-23.8361)" width="10" x="717.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="238.3611111111111" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816169986" ObjectName="110kV朗外河电站线16117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454816169986"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,722.611,249.472) scale(1.11111,1.11111) translate(-71.7056,-23.8361)" width="10" x="717.0555556615193" y="238.3611111111111"/></g>
  <g id="209">
   <use class="kv110" height="20" transform="rotate(90,722.611,197.028) scale(1.11111,1.11111) translate(-71.7056,-18.5917)" width="10" x="717.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="185.9166666666667" zvalue="20"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816038914" ObjectName="110kV朗外河电站线16160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454816038914"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,722.611,197.028) scale(1.11111,1.11111) translate(-71.7056,-18.5917)" width="10" x="717.0555556615193" y="185.9166666666667"/></g>
  <g id="207">
   <use class="kv110" height="20" transform="rotate(90,722.611,142.583) scale(1.11111,1.11111) translate(-71.7056,-13.1472)" width="10" x="717.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="131.4722222222222" zvalue="22"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815907842" ObjectName="110kV朗外河电站线16167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454815907842"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,722.611,142.583) scale(1.11111,1.11111) translate(-71.7056,-13.1472)" width="10" x="717.0555555555555" y="131.4722222222222"/></g>
  <g id="194">
   <use class="kv110" height="40" transform="rotate(0,1429.78,534.917) scale(1.11111,-1.11111) translate(-140.756,-1014.12)" width="40" x="1407.555555555555" xlink:href="#GroundDisconnector:中性点地刀12_0" y="512.6944444444445" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815711234" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454815711234"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1429.78,534.917) scale(1.11111,-1.11111) translate(-140.756,-1014.12)" width="40" x="1407.555555555555" y="512.6944444444445"/></g>
  <g id="190">
   <use class="kv110" height="20" transform="rotate(90,1496.44,362.25) scale(1.11111,1.11111) translate(-149.089,-35.1139)" width="10" x="1490.888899061415" xlink:href="#GroundDisconnector:地刀_0" y="351.1388888888889" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815580162" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454815580162"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1496.44,362.25) scale(1.11111,1.11111) translate(-149.089,-35.1139)" width="10" x="1490.888899061415" y="351.1388888888889"/></g>
  <g id="181">
   <use class="kv110" height="40" transform="rotate(0,766.778,533.06) scale(1.11111,-1.11111) translate(-74.4556,-1010.59)" width="40" x="744.5555555555555" xlink:href="#GroundDisconnector:中性点地刀12_0" y="510.8373015873016" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815121410" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454815121410"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,766.778,533.06) scale(1.11111,-1.11111) translate(-74.4556,-1010.59)" width="40" x="744.5555555555555" y="510.8373015873016"/></g>
  <g id="177">
   <use class="kv110" height="20" transform="rotate(90,833.444,363.25) scale(1.11111,1.11111) translate(-82.7889,-35.2139)" width="10" x="827.8888990614149" xlink:href="#GroundDisconnector:地刀_0" y="352.1388888888889" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814990338" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454814990338"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,833.444,363.25) scale(1.11111,1.11111) translate(-82.7889,-35.2139)" width="10" x="827.8888990614149" y="352.1388888888889"/></g>
  <g id="206">
   <use class="kv110" height="20" transform="rotate(90,1553.44,247.806) scale(1.11111,1.11111) translate(-154.789,-23.6694)" width="10" x="1547.888888994853" xlink:href="#GroundDisconnector:地刀_0" y="236.6944444444444" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814728194" ObjectName="110kV土仓电站线16217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454814728194"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1553.44,247.806) scale(1.11111,1.11111) translate(-154.789,-23.6694)" width="10" x="1547.888888994853" y="236.6944444444444"/></g>
  <g id="205">
   <use class="kv110" height="20" transform="rotate(90,1553.44,195.361) scale(1.11111,1.11111) translate(-154.789,-18.425)" width="10" x="1547.888888994853" xlink:href="#GroundDisconnector:地刀_0" y="184.2500000000001" zvalue="203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814597122" ObjectName="110kV土仓电站线16260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454814597122"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1553.44,195.361) scale(1.11111,1.11111) translate(-154.789,-18.425)" width="10" x="1547.888888994853" y="184.2500000000001"/></g>
  <g id="204">
   <use class="kv110" height="20" transform="rotate(90,1553.44,140.917) scale(1.11111,1.11111) translate(-154.789,-12.9806)" width="10" x="1547.888888888889" xlink:href="#GroundDisconnector:地刀_0" y="129.8055555555553" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814466050" ObjectName="110kV土仓电站线16267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454814466050"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1553.44,140.917) scale(1.11111,1.11111) translate(-154.789,-12.9806)" width="10" x="1547.888888888889" y="129.8055555555553"/></g>
  <g id="279">
   <use class="kv110" height="20" transform="rotate(90,1073.33,233.472) scale(1.11111,1.11111) translate(-106.778,-22.2361)" width="10" x="1067.777784559462" xlink:href="#GroundDisconnector:地刀_0" y="222.3611008326212" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814203906" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454814203906"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1073.33,233.472) scale(1.11111,1.11111) translate(-106.778,-22.2361)" width="10" x="1067.777784559462" y="222.3611008326212"/></g>
  <g id="273">
   <use class="kv110" height="20" transform="rotate(90,1073.33,277.306) scale(1.11111,1.11111) translate(-106.778,-26.6194)" width="10" x="1067.777784559462" xlink:href="#GroundDisconnector:地刀_0" y="266.1944444444444" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814007298" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454814007298"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1073.33,277.306) scale(1.11111,1.11111) translate(-106.778,-26.6194)" width="10" x="1067.777784559462" y="266.1944444444444"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="135">
   <g id="1350">
    <use class="kv110" height="50" transform="rotate(0,1545.25,511.331) scale(1.98333,1.96323) translate(-751.382,-226.796)" width="30" x="1515.5" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="462.25" zvalue="80"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594254850" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1351">
    <use class="v10500" height="50" transform="rotate(0,1545.25,511.331) scale(1.98333,1.96323) translate(-751.382,-226.796)" width="30" x="1515.5" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="462.25" zvalue="80"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594320386" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399534182402" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399534182402"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1545.25,511.331) scale(1.98333,1.96323) translate(-751.382,-226.796)" width="30" x="1515.5" y="462.25"/></g>
  <g id="163">
   <g id="1630">
    <use class="kv110" height="50" transform="rotate(0,882.401,508.331) scale(1.98333,1.96323) translate(-422.743,-225.324)" width="30" x="852.65" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="459.25" zvalue="147"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594123778" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="v10500" height="50" transform="rotate(0,882.401,508.331) scale(1.98333,1.96323) translate(-422.743,-225.324)" width="30" x="852.65" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="459.25" zvalue="147"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594189314" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399534116866" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399534116866"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,882.401,508.331) scale(1.98333,1.96323) translate(-422.743,-225.324)" width="30" x="852.65" y="459.25"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="246">
   <use class="v10500" height="26" transform="rotate(270,1077.67,576.181) scale(1.19213,1) translate(-172.529,0)" width="12" x="1070.51389058431" xlink:href="#Accessory:避雷器1_0" y="563.1805572509766" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815449090" ObjectName="#1站用变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1077.67,576.181) scale(1.19213,1) translate(-172.529,0)" width="12" x="1070.51389058431" y="563.1805572509766"/></g>
  <g id="105">
   <use class="v10500" height="18" transform="rotate(0,575,554.583) scale(3.66667,-3.05556) translate(-398.182,-717.583)" width="15" x="547.5000000000001" xlink:href="#Accessory:PT8_0" y="527.0833333333334" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815252482" ObjectName="10.5kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,575,554.583) scale(3.66667,-3.05556) translate(-398.182,-717.583)" width="15" x="547.5000000000001" y="527.0833333333334"/></g>
  <g id="274">
   <use class="kv110" height="35" transform="rotate(0,1112.41,198) scale(1.3,1.3) translate(-250.711,-40.4423)" width="40" x="1086.412488107495" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="175.25" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454814072834" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1112.41,198) scale(1.3,1.3) translate(-250.711,-40.4423)" width="40" x="1086.412488107495" y="175.25"/></g>
  <g id="285">
   <use class="v10500" height="26" transform="rotate(270,930.5,599.972) scale(1,1) translate(0,0)" width="12" x="924.5000000000001" xlink:href="#Accessory:避雷器1_0" y="586.9722256130643" zvalue="232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454813810690" ObjectName="#1主变10.5kV侧避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,930.5,599.972) scale(1,1) translate(0,0)" width="12" x="924.5000000000001" y="586.9722256130643"/></g>
  <g id="290">
   <use class="v10500" height="26" transform="rotate(270,1595.5,599.972) scale(1,1) translate(0,0)" width="12" x="1589.5" xlink:href="#Accessory:避雷器1_0" y="586.9722256130643" zvalue="236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454813679618" ObjectName="#2主变10.5kV侧避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1595.5,599.972) scale(1,1) translate(0,0)" width="12" x="1589.5" y="586.9722256130643"/></g>
  <g id="237">
   <use class="v10500" height="26" transform="rotate(270,1700.17,572.431) scale(1.19213,1) translate(-272.854,0)" width="12" x="1693.01389058431" xlink:href="#Accessory:避雷器1_0" y="559.4305572509766" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816628738" ObjectName="近区变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1700.17,572.431) scale(1.19213,1) translate(-272.854,0)" width="12" x="1693.01389058431" y="559.4305572509766"/></g>
  <g id="243">
   <use class="v10500" height="18" transform="rotate(0,1240,555.833) scale(3.66667,-3.05556) translate(-881.818,-719.242)" width="15" x="1212.5" xlink:href="#Accessory:PT8_0" y="528.3333333333334" zvalue="262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816694274" ObjectName="10.5kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1240,555.833) scale(3.66667,-3.05556) translate(-881.818,-719.242)" width="15" x="1212.5" y="528.3333333333334"/></g>
  <g id="276">
   <use class="v10500" height="40" transform="rotate(0,701.297,904.061) scale(1.3853,-1.03897) translate(-189.274,-1773.43)" width="30" x="680.5175622031613" xlink:href="#Accessory:带熔断器的线路PT1_0" y="883.2818193095626" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817218562" ObjectName="#1发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,701.297,904.061) scale(1.3853,-1.03897) translate(-189.274,-1773.43)" width="30" x="680.5175622031613" y="883.2818193095626"/></g>
  <g id="269">
   <use class="v10500" height="30" transform="rotate(0,701.811,776.261) scale(1.48718,1.44283) translate(-222.596,-231.605)" width="30" x="679.5030407543832" xlink:href="#Accessory:RT1122_0" y="754.6190543701709" zvalue="271"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817153026" ObjectName="#1发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,701.811,776.261) scale(1.48718,1.44283) translate(-222.596,-231.605)" width="30" x="679.5030407543832" y="754.6190543701709"/></g>
  <g id="298">
   <use class="v10500" height="30" transform="rotate(0,563.509,930.504) scale(1.3853,1.3853) translate(-150.951,-253.024)" width="30" x="542.7291006646999" xlink:href="#Accessory:PT789_0" y="909.7241270018703" zvalue="283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816956418" ObjectName="#1发电机互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,563.509,930.504) scale(1.3853,1.3853) translate(-150.951,-253.024)" width="30" x="542.7291006646999" y="909.7241270018703"/></g>
  <g id="313">
   <use class="v10500" height="26" transform="rotate(90,664.75,809.269) scale(-1,-1) translate(-1329.5,-1618.54)" width="12" x="658.7499999999999" xlink:href="#Accessory:避雷器1_0" y="796.2692307692307" zvalue="293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816825346" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,664.75,809.269) scale(-1,-1) translate(-1329.5,-1618.54)" width="12" x="658.7499999999999" y="796.2692307692307"/></g>
  <g id="315">
   <use class="v10500" height="40" transform="rotate(0,1068.8,905.311) scale(1.3853,-1.03897) translate(-291.488,-1775.88)" width="30" x="1048.017562203161" xlink:href="#Accessory:带熔断器的线路PT1_0" y="884.5318193095626" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817873922" ObjectName="#2发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1068.8,905.311) scale(1.3853,-1.03897) translate(-291.488,-1775.88)" width="30" x="1048.017562203161" y="884.5318193095626"/></g>
  <g id="314">
   <use class="v10500" height="30" transform="rotate(0,1069.31,777.511) scale(1.48718,1.44283) translate(-342.984,-231.988)" width="30" x="1047.003040754383" xlink:href="#Accessory:RT1122_0" y="755.8690543701709" zvalue="307"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817808386" ObjectName="#2发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1069.31,777.511) scale(1.48718,1.44283) translate(-342.984,-231.988)" width="30" x="1047.003040754383" y="755.8690543701709"/></g>
  <g id="304">
   <use class="v10500" height="30" transform="rotate(0,931.009,931.754) scale(1.3853,1.3853) translate(-253.165,-253.372)" width="30" x="910.2291006646999" xlink:href="#Accessory:PT789_0" y="910.9741270018703" zvalue="318"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817611778" ObjectName="#2发电机互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.009,931.754) scale(1.3853,1.3853) translate(-253.165,-253.372)" width="30" x="910.2291006646999" y="910.9741270018703"/></g>
  <g id="302">
   <use class="v10500" height="26" transform="rotate(90,1032.25,810.519) scale(-1,-1) translate(-2064.5,-1621.04)" width="12" x="1026.25" xlink:href="#Accessory:避雷器1_0" y="797.5192307692307" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817480706" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1032.25,810.519) scale(-1,-1) translate(-2064.5,-1621.04)" width="12" x="1026.25" y="797.5192307692307"/></g>
  <g id="340">
   <use class="v10500" height="40" transform="rotate(0,1446.3,902.811) scale(1.3853,-1.03897) translate(-396.483,-1770.98)" width="30" x="1425.517562203161" xlink:href="#Accessory:带熔断器的线路PT1_0" y="882.0318193095626" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818463746" ObjectName="#3发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1446.3,902.811) scale(1.3853,-1.03897) translate(-396.483,-1770.98)" width="30" x="1425.517562203161" y="882.0318193095626"/></g>
  <g id="339">
   <use class="v10500" height="30" transform="rotate(0,1446.81,775.011) scale(1.48718,1.44283) translate(-466.648,-231.221)" width="30" x="1424.503040754383" xlink:href="#Accessory:RT1122_0" y="753.3690543701709" zvalue="333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818398210" ObjectName="#3发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1446.81,775.011) scale(1.48718,1.44283) translate(-466.648,-231.221)" width="30" x="1424.503040754383" y="753.3690543701709"/></g>
  <g id="331">
   <use class="v10500" height="30" transform="rotate(0,1308.51,929.254) scale(1.3853,1.3853) translate(-358.16,-252.676)" width="30" x="1287.7291006647" xlink:href="#Accessory:PT789_0" y="908.4741270018703" zvalue="344"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818201602" ObjectName="#3发电机互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1308.51,929.254) scale(1.3853,1.3853) translate(-358.16,-252.676)" width="30" x="1287.7291006647" y="908.4741270018703"/></g>
  <g id="329">
   <use class="v10500" height="26" transform="rotate(90,1409.75,808.019) scale(-1,-1) translate(-2819.5,-1616.04)" width="12" x="1403.75" xlink:href="#Accessory:避雷器1_0" y="795.0192307692307" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818070530" ObjectName="#3发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1409.75,808.019) scale(-1,-1) translate(-2819.5,-1616.04)" width="12" x="1403.75" y="795.0192307692307"/></g>
  <g id="347">
   <use class="v10500" height="26" transform="rotate(90,1672.74,794.59) scale(0.976105,-0.976105) translate(40.8052,-1608.94)" width="12" x="1666.887792942395" xlink:href="#Accessory:避雷器1_0" y="781.9005064554289" zvalue="365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818594818" ObjectName="#2站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1672.74,794.59) scale(0.976105,-0.976105) translate(40.8052,-1608.94)" width="12" x="1666.887792942395" y="781.9005064554289"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="228">
   <use class="v10500" height="30" transform="rotate(0,1040.96,498.654) scale(1.5031,-1.75708) translate(-341.373,-771.095)" width="28" x="1019.913205993436" xlink:href="#EnergyConsumer:站用变YD2022_0" y="472.2981464433085" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454815383554" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1040.96,498.654) scale(1.5031,-1.75708) translate(-341.373,-771.095)" width="28" x="1019.913205993436" y="472.2981464433085"/></g>
  <g id="236">
   <use class="v10500" height="30" transform="rotate(0,1661.71,490.931) scale(1.44953,-1.36014) translate(-509.036,-846.469)" width="28" x="1641.413205993436" xlink:href="#EnergyConsumer:站用变YD2022_0" y="470.5289855072466" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454816563202" ObjectName="近区变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1661.71,490.931) scale(1.44953,-1.36014) translate(-509.036,-846.469)" width="28" x="1641.413205993436" y="470.5289855072466"/></g>
  <g id="353">
   <use class="v10500" height="30" transform="rotate(0,1627.04,849.931) scale(1.65589,1.66751) translate(-635.282,-330.219)" width="28" x="1603.859106203922" xlink:href="#EnergyConsumer:站用变DY接地_0" y="824.9186190813464" zvalue="356"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818725890" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1627.04,849.931) scale(1.65589,1.66751) translate(-635.282,-330.219)" width="28" x="1603.859106203922" y="824.9186190813464"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="270">
   <use class="v10500" height="30" transform="rotate(0,632.891,904.061) scale(1.43307,1.43307) translate(-184.76,-266.707)" width="30" x="611.3945868895569" xlink:href="#Generator:发电机_0" y="882.5652867370809" zvalue="267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817284098" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454817284098"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,632.891,904.061) scale(1.43307,1.43307) translate(-184.76,-266.707)" width="30" x="611.3945868895569" y="882.5652867370809"/></g>
  <g id="316">
   <use class="v10500" height="30" transform="rotate(0,1000.39,905.311) scale(1.43307,1.43307) translate(-295.817,-267.085)" width="30" x="978.8945868895569" xlink:href="#Generator:发电机_0" y="883.8152867370809" zvalue="304"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454817939458" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454817939458"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1000.39,905.311) scale(1.43307,1.43307) translate(-295.817,-267.085)" width="30" x="978.8945868895569" y="883.8152867370809"/></g>
  <g id="342">
   <use class="v10500" height="30" transform="rotate(0,1377.89,902.811) scale(1.43307,1.43307) translate(-409.896,-266.329)" width="30" x="1356.394586889557" xlink:href="#Generator:发电机_0" y="881.3152867370809" zvalue="330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454818529282" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192454818529282"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1377.89,902.811) scale(1.43307,1.43307) translate(-409.896,-266.329)" width="30" x="1356.394586889557" y="881.3152867370809"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="14" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,766.212,17.9771) scale(1,1) translate(0,-8.49321e-15)" writing-mode="lr" x="765.74" xml:space="preserve" y="22.69" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136504700930" ObjectName="P"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="15" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,766.212,39) scale(1,1) translate(0,2.80586e-15)" writing-mode="lr" x="765.74" xml:space="preserve" y="43.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136504766466" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,766.212,56.0229) scale(1,1) translate(0,4.69579e-15)" writing-mode="lr" x="765.74" xml:space="preserve" y="60.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136504832002" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1601.93,8.52778) scale(1,1) translate(0,1.03903e-14)" writing-mode="lr" x="1601.46" xml:space="preserve" y="13.25" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136496050178" ObjectName="P"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1603.04,32.1781) scale(1,1) translate(0,-5.69045e-14)" writing-mode="lr" x="1602.57" xml:space="preserve" y="36.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136496115714" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1601.93,50.5278) scale(1,1) translate(0,4.0857e-15)" writing-mode="lr" x="1601.46" xml:space="preserve" y="55.25" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136496181250" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="25" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,955.735,380.052) scale(1,1) translate(0,-2.45452e-13)" writing-mode="lr" x="955.1799999999999" xml:space="preserve" y="384.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136497623042" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="31" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1611.92,382.113) scale(1,1) translate(0,3.28947e-13)" writing-mode="lr" x="1611.36" xml:space="preserve" y="386.8" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136502145026" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="34" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,956.846,405.27) scale(1,1) translate(0,1.74834e-13)" writing-mode="lr" x="956.29" xml:space="preserve" y="409.95" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136497688578" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="36" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1611.92,406.958) scale(1,1) translate(0,-8.77536e-14)" writing-mode="lr" x="1611.36" xml:space="preserve" y="411.64" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136502210562" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="37" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,812.401,585.76) scale(1,1) translate(0,6.37108e-14)" writing-mode="lr" x="811.85" xml:space="preserve" y="590.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136497754114" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="38" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1476.36,583.614) scale(1,1) translate(0,-1.26854e-13)" writing-mode="lr" x="1475.81" xml:space="preserve" y="588.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136502276098" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="39" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,812.401,610.928) scale(1,1) translate(0,-2.6602e-13)" writing-mode="lr" x="811.85" xml:space="preserve" y="615.62" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136497819650" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="40" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1476.36,609.65) scale(1,1) translate(0,-1.32635e-13)" writing-mode="lr" x="1475.81" xml:space="preserve" y="614.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136502341634" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="41" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,956.846,429.753) scale(1,1) translate(0,1.85706e-13)" writing-mode="lr" x="956.29" xml:space="preserve" y="434.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136497885186" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="42" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1611.92,431.804) scale(1,1) translate(0,-9.32704e-14)" writing-mode="lr" x="1611.36" xml:space="preserve" y="436.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136502407170" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="44" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,812.401,636.096) scale(1,1) translate(0,-2.77197e-13)" writing-mode="lr" x="811.85" xml:space="preserve" y="640.79" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136498212866" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="67" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1476.36,635.686) scale(1,1) translate(0,-1.38416e-13)" writing-mode="lr" x="1475.81" xml:space="preserve" y="640.38" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136502734850" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="78" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,625.113,976.288) scale(1,1) translate(0,-1.50043e-12)" writing-mode="lr" x="624.5599999999999" xml:space="preserve" y="980.96" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136509288450" ObjectName="P"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="79" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,999.279,983.963) scale(1,1) translate(0,-2.37444e-12)" writing-mode="lr" x="998.73" xml:space="preserve" y="988.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136510468098" ObjectName="P"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1380.11,981.151) scale(1,1) translate(0,8.61214e-13)" writing-mode="lr" x="1379.56" xml:space="preserve" y="985.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136511647746" ObjectName="P"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="81" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,625.113,999.445) scale(1,1) translate(0,-1.53643e-12)" writing-mode="lr" x="624.5599999999999" xml:space="preserve" y="1004.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136509353986" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="82" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,999.279,1008.96) scale(1,1) translate(0,2.4355e-12)" writing-mode="lr" x="998.73" xml:space="preserve" y="1013.65" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136510533634" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="83" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1380.11,1005.49) scale(1,1) translate(0,8.82829e-13)" writing-mode="lr" x="1379.56" xml:space="preserve" y="1010.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136511713282" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="84" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,625.113,1022.6) scale(1,1) translate(0,-1.57242e-12)" writing-mode="lr" x="624.5599999999999" xml:space="preserve" y="1027.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136509419522" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,999.279,1033.96) scale(1,1) translate(0,-2.49655e-12)" writing-mode="lr" x="998.73" xml:space="preserve" y="1038.64" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136510599170" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1380.11,1029.82) scale(1,1) translate(0,9.04443e-13)" writing-mode="lr" x="1379.56" xml:space="preserve" y="1034.51" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136511778818" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136507191298" ObjectName="F"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="116" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127232471044" ObjectName="土仓电站上网有功"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136513613826" ObjectName="F"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,335.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="340.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136506667010" ObjectName="F"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="151" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136513417218" ObjectName="F"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136513482754" ObjectName="F"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123908681732" ObjectName="F"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123908812804" ObjectName="F"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181156357" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181090821" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,157.222,355.167) scale(1,1) translate(0,0)" writing-mode="lr" x="157.38" xml:space="preserve" y="360.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136500572162" ObjectName="F"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,399.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="404.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123908747268" ObjectName="F"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,401.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="406.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123908943876" ObjectName="F"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="88" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1241.13,457.639) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.66" xml:space="preserve" y="462.42" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136500178946" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="136" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,592.133,454.639) scale(1,1) translate(0,0)" writing-mode="lr" x="591.66" xml:space="preserve" y="459.42" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136506273794" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="137" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1126,77.4167) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.53" xml:space="preserve" y="82.19" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136506798082" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1241.13,477.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.66" xml:space="preserve" y="481.92" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136500244482" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="139" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,592.133,474.639) scale(1,1) translate(0,0)" writing-mode="lr" x="591.66" xml:space="preserve" y="479.42" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136506339330" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1126,99.4167) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.53" xml:space="preserve" y="104.19" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136506863618" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="141" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1241.13,496.639) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.66" xml:space="preserve" y="501.42" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136500310018" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="142" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,592.133,494.639) scale(1,1) translate(0,0)" writing-mode="lr" x="591.66" xml:space="preserve" y="499.42" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136506404866" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="146" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1126,121.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.53" xml:space="preserve" y="126.19" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136506929154" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="21" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1155.13,640.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1154.66" xml:space="preserve" y="644.92" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136500441090" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="22" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,473.133,642.139) scale(1,1) translate(-2.85194e-13,0)" writing-mode="lr" x="472.66" xml:space="preserve" y="646.92" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136506535938" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="23" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,559,284.917) scale(1,1) translate(0,0)" writing-mode="lr" x="558.53" xml:space="preserve" y="289.69" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136507060226" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="161">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="430"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374884012036" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="160">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="431"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562949988941832" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
  <g id="174">
   <use height="30" transform="rotate(0,320.812,122.464) scale(1.27778,1.03333) translate(-57.2418,-3.45045)" width="90" x="263.31" xlink:href="#State:全站检修_0" y="106.96" zvalue="519"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549684666369" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,320.812,122.464) scale(1.27778,1.03333) translate(-57.2418,-3.45045)" width="90" x="263.31" y="106.96"/></g>
 </g>
</svg>