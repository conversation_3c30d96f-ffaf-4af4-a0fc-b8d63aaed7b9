<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549590818818" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <rect fill-opacity="0" height="8.92" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.54) scale(1,1) translate(0,0)" width="4" x="13" y="2.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.999999999999998" y2="12.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.083333333333332" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="EnergyConsumer:Y-Y站用_0" viewBox="0,0,17,26">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350814"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="5.503560677018262" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="Compensator:遮放35kV电容_0" viewBox="0,0,60,40">
   <use terminal-index="0" type="0" x="0.6873530692755132" xlink:href="#terminal" y="23.15315325549626"/>
   <path d="M 20.4238 13.4601 L 20.4238 23.2699 L 30.2336 23.2699" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 22.0587 7.73774 L 3.84051 7.73774 L 3.84051 23.1532" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.96429701311517" x2="1.154486884904586" y1="23.15315325549626" y2="23.15315325549626"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.05839172695363" x2="27.25558883317917" y1="7.796129066690407" y2="7.796129066690407"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.45979317384086" x2="31.45979317384086" y1="9.197530513577634" y2="6.394727619803181"/>
   <rect fill-opacity="0" height="12.38" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,20.95,7.8) scale(1,1) translate(0,0)" width="5.61" x="18.15" y="1.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.11711686125934" x2="17.91291252059766" y1="7.796129066690407" y2="9.197530513577634"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.05839172695363" x2="30.05839172695363" y1="9.898231237021246" y2="5.694026896359567"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.86119462072809" x2="32.86119462072809" y1="8.49682979013402" y2="7.095428343246795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38.11645004655519" x2="56.33466885608912" y1="37.4591263591367" y2="37.4591263591367"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.11711686125934" x2="17.91291252059766" y1="7.796129066690407" y2="6.394727619803181"/>
   <rect fill-opacity="0" height="5.61" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,40.34,23.33) scale(1,1) translate(0,0)" width="2.8" x="38.93" y="20.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="56.13613698444676" x2="56.13613698444676" y1="23.44511189026444" y2="31.73673711768053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.17250678603577" x2="59.17250678603577" y1="12.43243218680899" y2="34.59899781143184"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.90257007663224" x2="59.37103865767813" y1="23.29329340018499" y2="23.29329340018499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="57.26893648734728" x2="59.28929023994304" y1="12.31564873290172" y2="12.31564873290172"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.28929023994304" x2="57.38571994125455" y1="34.48221435752456" y2="34.48221435752456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="53.0219115469196" x2="56.16208886309282" y1="31.75360583880046" y2="31.75360583880046"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.76476491941656" x2="34.76476491941656" y1="31.73673711768053" y2="23.29329340018499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.23356690781454" x2="49.82983047345427" y1="23.29329340018499" y2="23.29329340018499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.91791817491283" x2="34.82964461603171" y1="31.70170208150834" y2="31.70170208150834"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="51.23123192034149" x2="56.16208886309283" y1="23.29329340018499" y2="23.29329340018499"/>
   <path d="M 42.9614 31.7017 A 4.15364 2.547 -90 0 1 37.8674 31.7017" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 48.0554 31.7017 A 4.15364 2.547 -90 0 1 42.9614 31.7017" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 53.0092 31.7017 A 4.15364 2.547 -90 0 1 47.9152 31.7017" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="51.23123192034147" x2="51.23123192034147" y1="21.19119122985414" y2="25.39539557051582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="49.82983047345425" x2="49.82983047345425" y1="21.19119122985414" y2="25.39539557051582"/>
   <path d="M 20.5873 13.1682 A 9.58792 9.95831 -360 1 1 10.9993 23.1265" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15" xlink:href="#terminal" y="0.3938957475994442"/>
   <path d="M 10 16 L 20.1667 16 L 15 7.83333 z" fill-opacity="0" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15" cy="15.25" fill-opacity="0" rx="14.91" ry="14.91" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-D_1" viewBox="0,0,30,50">
   <path d="M 10 40 L 20 40 L 15.0833 32 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15" cy="35" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.01097393689986" xlink:href="#terminal" y="49.60973936899863"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="EnergyConsumer:电炉_0" viewBox="0,0,27,14">
   <use terminal-index="0" type="0" x="13.5" xlink:href="#terminal" y="0.25"/>
   <rect fill-opacity="0" height="10.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.54,5.54) scale(1,1) translate(0,0)" width="3.08" x="12" y="0.25"/>
   <path d="M 0.833333 8.25 A 12.5 5 0 0 0 25.8333 8.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="25.75" y1="8.25" y2="8.25"/>
  </symbol>
  <symbol id="Compensator:并联电容器组_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="19.25" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.33333333333334" x2="29" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.25" x2="26.25" y1="17.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29" x2="22.58333333333333" y1="24" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="19.91666666666667" y1="2" y2="9.833333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="9.75" y1="2.249999999999996" y2="10.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="12" y1="24.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="14" y1="8.333333333333332" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="1" y1="15.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.75" x2="11.5" y1="12.33333333333333" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.5" x2="23.5" y1="13.25" y2="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.4" x2="17.4" y1="19.35" y2="28.93333333333333"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV星云铝厂开关站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="31"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="31"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="31">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="138"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="139">35kV星云铝厂开关站</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="388.75" x2="388.75" y1="11" y2="1039.25" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.22692454998003" x2="331.8381846035992" y1="168.8779458827686" y2="168.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3219285714284" x2="361.3219285714284" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,208.338,957.386) scale(1,1) translate(0,1.05089e-13)" writing-mode="lr" x="60.68" xml:space="preserve" y="963.39" zvalue="10">参考图号      XingYun-01-2017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,132.899,994.755) scale(1,1) translate(-6.25312e-14,-1.52933e-12)" writing-mode="lr" x="70.40000000000001" xml:space="preserve" y="1000.75" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.202,995.755) scale(1,1) translate(1.38222e-13,-1.53089e-12)" writing-mode="lr" x="207.5" xml:space="preserve" y="1001.75" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9923,1024.31) scale(1,1) translate(-6.10858e-14,-1.57527e-12)" writing-mode="lr" x="83.98999999999999" xml:space="preserve" y="1030.31" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,280.836,1022.31) scale(1,1) translate(-4.58902e-14,1.12298e-13)" writing-mode="lr" x="206.67" xml:space="preserve" y="1028.31" zvalue="14">更新日期  </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.60611355802337" x2="359.2173736116425" y1="623.6445306693694" y2="623.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,97.6707,640.686) scale(1,1) translate(5.99964e-15,-1.38109e-13)" writing-mode="lr" x="97.67070806204492" xml:space="preserve" y="645.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,502.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="507.1071428571429" zvalue="23">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,528.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="532.6071428571429" zvalue="24">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,553.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="558.1071428571429" zvalue="25">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,579.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="583.6071428571429" zvalue="26">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,604.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="609.1071428571429" zvalue="27">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.3214,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="54.32" xml:space="preserve" y="191.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.571,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="240.57" xml:space="preserve" y="191.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="296.4" xml:space="preserve" y="345" zvalue="380">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.722,372.889) scale(1,1) translate(0,0)" writing-mode="lr" x="744.72" xml:space="preserve" y="377.39" zvalue="490">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070.61,368.778) scale(1,1) translate(-2.28398e-13,0)" writing-mode="lr" x="1070.61" xml:space="preserve" y="373.28" zvalue="492">35kVⅢ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1475.89,367.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1475.89" xml:space="preserve" y="372.17" zvalue="494">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.556,97.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="695.5599999999999" xml:space="preserve" y="101.83" zvalue="495">35kV昔铝Ⅰ线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.944,329.444) scale(1,1) translate(0,0)" writing-mode="lr" x="718.9400000000001" xml:space="preserve" y="333.94" zvalue="497">331</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,663.556,212.778) scale(1,1) translate(0,0)" writing-mode="lr" x="663.5599999999999" xml:space="preserve" y="217.28" zvalue="500">3316</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1690.44,97.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="1690.44" xml:space="preserve" y="101.83" zvalue="506">35kV昔铝Ⅱ线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1714.94,329.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1714.94" xml:space="preserve" y="333.94" zvalue="508">332</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1659.56,212.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1659.56" xml:space="preserve" y="217.28" zvalue="512">3326</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978.182,97.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="978.1799999999999" xml:space="preserve" y="101.83" zvalue="518">35kV星云铝厂一级电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1002.94,329.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1002.94" xml:space="preserve" y="333.94" zvalue="520">333</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.44,233.889) scale(1,1) translate(2.20589e-13,0)" writing-mode="lr" x="1009.44" xml:space="preserve" y="238.39" zvalue="531">3339</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.18,97.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.18" xml:space="preserve" y="101.83" zvalue="535">35kV星云铝厂二级电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1346.94,329.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1346.94" xml:space="preserve" y="333.94" zvalue="537">334</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1353.44,233.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.44" xml:space="preserve" y="238.39" zvalue="543">3349</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" x="562.890625" xml:space="preserve" y="185.0312475628323" zvalue="546">#1动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="562.890625" xml:space="preserve" y="201.0312475628323" zvalue="546">1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,588.056,329.444) scale(1,1) translate(0,0)" writing-mode="lr" x="588.0599999999999" xml:space="preserve" y="333.94" zvalue="548">335</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,671.167,459.667) scale(1,1) translate(0,0)" writing-mode="lr" x="671.17" xml:space="preserve" y="464.17" zvalue="556">336</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1551.17,459.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1551.17" xml:space="preserve" y="464.17" zvalue="561">341</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1667.17,458.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1667.17" xml:space="preserve" y="463.06" zvalue="567">342</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" x="1297.3125" xml:space="preserve" y="595.0156225628323" zvalue="572">#2动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1297.3125" xml:space="preserve" y="611.0156225628323" zvalue="572">1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1323.61,450.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.61" xml:space="preserve" y="455.06" zvalue="574">338</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" x="1409.3125" xml:space="preserve" y="595.0156225628323" zvalue="580">#3动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1409.3125" xml:space="preserve" y="611.0156225628323" zvalue="580">1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1435.61,450.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1435.61" xml:space="preserve" y="455.06" zvalue="582">339</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1795.17,458.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1795.17" xml:space="preserve" y="463.06" zvalue="587">343</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" x="1768.875" xml:space="preserve" y="615.0225670072767" zvalue="590">#1无功补偿装置</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1768.875" xml:space="preserve" y="631.0225670072767" zvalue="590">7600kVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,919.167,459.667) scale(1,1) translate(0,0)" writing-mode="lr" x="919.17" xml:space="preserve" y="464.17" zvalue="593">313</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.44,459.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.44" xml:space="preserve" y="464.17" zvalue="602">323</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,555.167,459.667) scale(1,1) translate(0,0)" writing-mode="lr" x="555.17" xml:space="preserve" y="464.17" zvalue="610">329</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" x="463.109375" xml:space="preserve" y="826.3038194444443" zvalue="612">#1炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="463.109375" xml:space="preserve" y="842.3038194444443" zvalue="612">25.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,554.611,759.667) scale(1,1) translate(0,0)" writing-mode="lr" x="554.61" xml:space="preserve" y="764.17" zvalue="615">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,493.444,695) scale(1,1) translate(0,0)" writing-mode="lr" x="493.44" xml:space="preserve" y="699.5" zvalue="623">3019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,529.294,969.111) scale(1,1) translate(0,0)" writing-mode="lr" x="529.29" xml:space="preserve" y="973.61" zvalue="627">#1电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" x="444.2890625" xml:space="preserve" y="951.7690972222222" zvalue="629">#1炉变电容器组</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="444.2890625" xml:space="preserve" y="967.7690972222222" zvalue="629">16000kVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,559.375,685.556) scale(1,1) translate(0,0)" writing-mode="lr" x="559.38" xml:space="preserve" y="690.0599999999999" zvalue="633">3016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" x="639.828125" xml:space="preserve" y="907.5156225628323" zvalue="643">#1硅炉动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="639.828125" xml:space="preserve" y="923.5156225628323" zvalue="643">1250kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" x="751.828125" xml:space="preserve" y="907.5156225628323" zvalue="650">#1硅炉环保变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="751.828125" xml:space="preserve" y="923.5156225628323" zvalue="650">3150kVA</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,161.571,186) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="192.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126259195908" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,343.571,185.857) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="192.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126259261444" ObjectName=""/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="66" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,763,205.5) scale(1,1) translate(0,0)" writing-mode="lr" x="762.45" xml:space="preserve" y="211.78" zvalue="1">Ia:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126236192772" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.1" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="68" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,763,156.389) scale(1,1) translate(0,0)" writing-mode="lr" x="762.53" xml:space="preserve" y="161.17" zvalue="1">P:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126235144196" ObjectName="P"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.1" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="80" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,763,179.5) scale(1,1) translate(0,0)" writing-mode="lr" x="762.53" xml:space="preserve" y="184.28" zvalue="1">Q:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126235209732" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="91" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1038.39,205.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.84" xml:space="preserve" y="211.78" zvalue="1">Ia:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126238289924" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.1" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1038.39,156.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.92" xml:space="preserve" y="161.17" zvalue="1">P:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126238486532" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.1" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="93" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1038.39,179.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.92" xml:space="preserve" y="184.28" zvalue="1">Q:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126238552068" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="62" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1377,205.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.45" xml:space="preserve" y="211.78" zvalue="1">Ia:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126239862788" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.1" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="64" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1377,156.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.53" xml:space="preserve" y="161.17" zvalue="1">P:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126240059396" ObjectName="P"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.1" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="65" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1377,179.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.53" xml:space="preserve" y="184.28" zvalue="1">Q:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126240124932" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="77" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1759.39,205.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1758.84" xml:space="preserve" y="211.78" zvalue="1">Ia:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126236717060" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.1" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="94" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1759.39,156.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1758.92" xml:space="preserve" y="161.17" zvalue="1">P:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126237241348" ObjectName="P"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.1" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="96" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1759.39,179.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1758.92" xml:space="preserve" y="184.28" zvalue="1">Q:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126237306884" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="97" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,463.278,593.944) scale(1,1) translate(0,0)" writing-mode="lr" x="462.73" xml:space="preserve" y="600.22" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126252904452" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="98" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,463.278,622.944) scale(1,1) translate(0,0)" writing-mode="lr" x="462.73" xml:space="preserve" y="629.22" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126252969988" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="99" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,463.278,651.944) scale(1,1) translate(0,2.8175e-13)" writing-mode="lr" x="462.73" xml:space="preserve" y="658.22" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126253166596" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,768.167,228.056) scale(1,1) translate(0,0)" writing-mode="lr" x="767.7" xml:space="preserve" y="232.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126235996164" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1771.17,231.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1770.7" xml:space="preserve" y="235.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126238093316" ObjectName="Ux"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="132">
   <use height="30" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="381"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374892924931" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" y="330.52"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="10">
   <path class="kv35" d="M 472.67 392 L 787 392" stroke-width="6" zvalue="489"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674251243524" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674251243524"/></metadata>
  <path d="M 472.67 392 L 787 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv35" d="M 842 392 L 1118 392" stroke-width="6" zvalue="491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674251309060" ObjectName="35kVⅢ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674251309060"/></metadata>
  <path d="M 842 392 L 1118 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 1173 392 L 1825.33 392" stroke-width="6" zvalue="493"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674251374596" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674251374596"/></metadata>
  <path d="M 1173 392 L 1825.33 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="37">
   <use class="kv35" height="20" transform="rotate(0,694.167,330.444) scale(2.44444,2.44444) translate(-402.967,-180.818)" width="10" x="681.9444444444446" xlink:href="#Breaker:小车断路器_0" y="306.0000000000001" zvalue="496"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924544495621" ObjectName="35kV昔铝Ⅰ线331断路器"/>
   <cge:TPSR_Ref TObjectID="6473924544495621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,694.167,330.444) scale(2.44444,2.44444) translate(-402.967,-180.818)" width="10" x="681.9444444444446" y="306.0000000000001"/></g>
  <g id="56">
   <use class="kv35" height="20" transform="rotate(0,1690.17,330.444) scale(2.44444,2.44444) translate(-991.513,-180.818)" width="10" x="1677.944444444445" xlink:href="#Breaker:小车断路器_0" y="306.0000000000001" zvalue="507"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924544561157" ObjectName="35kV昔铝Ⅱ线332断路器"/>
   <cge:TPSR_Ref TObjectID="6473924544561157"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1690.17,330.444) scale(2.44444,2.44444) translate(-991.513,-180.818)" width="10" x="1677.944444444445" y="306.0000000000001"/></g>
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,978.167,330.444) scale(2.44444,2.44444) translate(-570.785,-180.818)" width="10" x="965.9444444444446" xlink:href="#Breaker:小车断路器_0" y="306.0000000000001" zvalue="519"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924544626693" ObjectName="35kV星云铝厂一级电站线333断路器"/>
   <cge:TPSR_Ref TObjectID="6473924544626693"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,978.167,330.444) scale(2.44444,2.44444) translate(-570.785,-180.818)" width="10" x="965.9444444444446" y="306.0000000000001"/></g>
  <g id="88">
   <use class="kv35" height="20" transform="rotate(0,1322.17,330.444) scale(2.44444,2.44444) translate(-774.058,-180.818)" width="10" x="1309.944444444445" xlink:href="#Breaker:小车断路器_0" y="306.0000000000001" zvalue="536"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924544692229" ObjectName="35kV星云铝厂二级电站线334断路器"/>
   <cge:TPSR_Ref TObjectID="6473924544692229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.17,330.444) scale(2.44444,2.44444) translate(-774.058,-180.818)" width="10" x="1309.944444444445" y="306.0000000000001"/></g>
  <g id="102">
   <use class="kv35" height="20" transform="rotate(0,562.167,330.444) scale(2.44444,2.44444) translate(-324.967,-180.818)" width="10" x="549.9444444444446" xlink:href="#Breaker:小车断路器_0" y="306.0000000000001" zvalue="547"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924544757765" ObjectName="#1动力变335断路器"/>
   <cge:TPSR_Ref TObjectID="6473924544757765"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,562.167,330.444) scale(2.44444,2.44444) translate(-324.967,-180.818)" width="10" x="549.9444444444446" y="306.0000000000001"/></g>
  <g id="107">
   <use class="kv35" height="20" transform="rotate(0,645.278,460.667) scale(2.44444,2.44444) translate(-374.078,-257.768)" width="10" x="633.0555555555555" xlink:href="#Breaker:小车断路器_0" y="436.2222222222223" zvalue="555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924544823301" ObjectName="336线336断路器"/>
   <cge:TPSR_Ref TObjectID="6473924544823301"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,645.278,460.667) scale(2.44444,2.44444) translate(-374.078,-257.768)" width="10" x="633.0555555555555" y="436.2222222222223"/></g>
  <g id="114">
   <use class="kv35" height="20" transform="rotate(0,1525.28,460.667) scale(2.44444,2.44444) translate(-894.078,-257.768)" width="10" x="1513.055555555556" xlink:href="#Breaker:小车断路器_0" y="436.2222222222223" zvalue="560"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924544888837" ObjectName="341线341断路器"/>
   <cge:TPSR_Ref TObjectID="6473924544888837"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1525.28,460.667) scale(2.44444,2.44444) translate(-894.078,-257.768)" width="10" x="1513.055555555556" y="436.2222222222223"/></g>
  <g id="119">
   <use class="kv35" height="20" transform="rotate(0,1641.28,459.556) scale(2.44444,2.44444) translate(-962.624,-257.111)" width="10" x="1629.055555555556" xlink:href="#Breaker:小车断路器_0" y="435.1111111111111" zvalue="566"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924544954373" ObjectName="342线342断路器"/>
   <cge:TPSR_Ref TObjectID="6473924544954373"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1641.28,459.556) scale(2.44444,2.44444) translate(-962.624,-257.111)" width="10" x="1629.055555555556" y="435.1111111111111"/></g>
  <g id="125">
   <use class="kv35" height="20" transform="rotate(0,1297.72,454.222) scale(2.44444,-2.44444) translate(-759.614,-625.596)" width="10" x="1285.5" xlink:href="#Breaker:小车断路器_0" y="429.7777773539224" zvalue="573"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924545019909" ObjectName="#2动力变338断路器"/>
   <cge:TPSR_Ref TObjectID="6473924545019909"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1297.72,454.222) scale(2.44444,-2.44444) translate(-759.614,-625.596)" width="10" x="1285.5" y="429.7777773539224"/></g>
  <g id="133">
   <use class="kv35" height="20" transform="rotate(0,1409.72,454.222) scale(2.44444,-2.44444) translate(-825.795,-625.596)" width="10" x="1397.5" xlink:href="#Breaker:小车断路器_0" y="429.7777773539224" zvalue="581"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924545085445" ObjectName="#3动力变339断路器"/>
   <cge:TPSR_Ref TObjectID="6473924545085445"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1409.72,454.222) scale(2.44444,-2.44444) translate(-825.795,-625.596)" width="10" x="1397.5" y="429.7777773539224"/></g>
  <g id="137">
   <use class="kv35" height="20" transform="rotate(0,1769.28,459.556) scale(2.44444,2.44444) translate(-1038.26,-257.111)" width="10" x="1757.055555555556" xlink:href="#Breaker:小车断路器_0" y="435.1111111111111" zvalue="586"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924545150981" ObjectName="#1无功补偿装置343断路器"/>
   <cge:TPSR_Ref TObjectID="6473924545150981"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1769.28,459.556) scale(2.44444,2.44444) translate(-1038.26,-257.111)" width="10" x="1757.055555555556" y="435.1111111111111"/></g>
  <g id="147">
   <use class="kv35" height="20" transform="rotate(0,893.278,460.667) scale(2.44444,2.44444) translate(-520.624,-257.768)" width="10" x="881.0555555555555" xlink:href="#Breaker:小车断路器_0" y="436.2222222222223" zvalue="592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924545216517" ObjectName="分段313断路器"/>
   <cge:TPSR_Ref TObjectID="6473924545216517"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,893.278,460.667) scale(2.44444,2.44444) translate(-520.624,-257.768)" width="10" x="881.0555555555555" y="436.2222222222223"/></g>
  <g id="159">
   <use class="kv35" height="20" transform="rotate(0,1072.33,460.667) scale(-2.44444,2.44444) translate(-1503.79,-257.768)" width="10" x="1060.111111429002" xlink:href="#Breaker:小车断路器_0" y="436.2222222222223" zvalue="601"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924545282053" ObjectName="分段323断路器"/>
   <cge:TPSR_Ref TObjectID="6473924545282053"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1072.33,460.667) scale(-2.44444,2.44444) translate(-1503.79,-257.768)" width="10" x="1060.111111429002" y="436.2222222222223"/></g>
  <g id="161">
   <use class="kv35" height="20" transform="rotate(0,529.278,460.667) scale(2.44444,2.44444) translate(-305.533,-257.768)" width="10" x="517.0555555555555" xlink:href="#Breaker:小车断路器_0" y="436.2222222222223" zvalue="609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924545347589" ObjectName="35kV星云铝厂329断路器"/>
   <cge:TPSR_Ref TObjectID="6473924545347589"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,529.278,460.667) scale(2.44444,2.44444) translate(-305.533,-257.768)" width="10" x="517.0555555555555" y="436.2222222222223"/></g>
  <g id="168">
   <use class="kv35" height="20" transform="rotate(0,529.278,760.667) scale(2.44444,2.44444) translate(-305.533,-435.04)" width="10" x="517.0555555555555" xlink:href="#Breaker:小车断路器_0" y="736.2222222222223" zvalue="614"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924545413125" ObjectName="#1炉变301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924545413125"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,529.278,760.667) scale(2.44444,2.44444) translate(-305.533,-435.04)" width="10" x="517.0555555555555" y="736.2222222222223"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="40">
   <path class="kv35" d="M 694.17 352.44 L 694.17 392" stroke-width="1" zvalue="498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 694.17 352.44 L 694.17 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 694.17 163.3 L 694.17 203.07" stroke-width="1" zvalue="500"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="41@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 694.17 163.3 L 694.17 203.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 693.4 226.9 L 693.4 307.83" stroke-width="1" zvalue="501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@1" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.4 226.9 L 693.4 307.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv35" d="M 721.64 261.08 L 693.4 261.08" stroke-width="1" zvalue="503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 721.64 261.08 L 693.4 261.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv35" d="M 1690.17 352.44 L 1690.17 392" stroke-width="1" zvalue="509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="29@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1690.17 352.44 L 1690.17 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv35" d="M 1690.17 163.3 L 1690.17 203.07" stroke-width="1" zvalue="511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1690.17 163.3 L 1690.17 203.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv35" d="M 1689.4 226.9 L 1689.4 307.83" stroke-width="1" zvalue="513"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@1" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1689.4 226.9 L 1689.4 307.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv35" d="M 1717.64 261.08 L 1689.4 261.08" stroke-width="1" zvalue="515"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 1717.64 261.08 L 1689.4 261.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv35" d="M 978.17 352.44 L 978.17 392" stroke-width="1" zvalue="521"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="27@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 978.17 352.44 L 978.17 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 978.59 163.14 L 978.59 307.83" stroke-width="1" zvalue="528"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="69@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 978.59 163.14 L 978.59 307.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv35" d="M 1033.64 261.08 L 1020.48 261.08" stroke-width="1" zvalue="531"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.64 261.08 L 1020.48 261.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv35" d="M 996.65 260.51 L 978.59 260.51" stroke-width="1" zvalue="532"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.65 260.51 L 978.59 260.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 1322.17 352.44 L 1322.17 392" stroke-width="1" zvalue="538"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@1" LinkObjectIDznd="29@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.17 352.44 L 1322.17 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 1322.59 163.14 L 1322.59 307.83" stroke-width="1" zvalue="540"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.59 163.14 L 1322.59 307.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv35" d="M 1377.64 261.08 L 1364.48 261.08" stroke-width="1" zvalue="542"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1377.64 261.08 L 1364.48 261.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1340.65 260.51 L 1322.59 260.51" stroke-width="1" zvalue="544"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@1" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 1340.65 260.51 L 1322.59 260.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 562.17 352.44 L 562.17 392" stroke-width="1" zvalue="548"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="10@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.17 352.44 L 562.17 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 562.18 264.74 L 562.17 307.83" stroke-width="1" zvalue="549"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.18 264.74 L 562.17 307.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv35" d="M 645.28 556.33 L 645.28 482.67" stroke-width="1" zvalue="556"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="107@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 645.28 556.33 L 645.28 482.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 645.28 438.06 L 645.28 392" stroke-width="1" zvalue="557"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="10@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 645.28 438.06 L 645.28 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1525.28 556.33 L 1525.28 482.67" stroke-width="1" zvalue="562"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="114@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1525.28 556.33 L 1525.28 482.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv35" d="M 1525.28 438.06 L 1525.28 392" stroke-width="1" zvalue="563"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="29@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1525.28 438.06 L 1525.28 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv35" d="M 1642.39 557.44 L 1642.39 481.56" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1642.39 557.44 L 1642.39 481.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 1641.28 436.94 L 1641.28 392" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="29@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1641.28 436.94 L 1641.28 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv35" d="M 1297.72 432.22 L 1297.72 392" stroke-width="1" zvalue="575"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="29@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1297.72 432.22 L 1297.72 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv35" d="M 1297.73 519.92 L 1297.72 476.83" stroke-width="1" zvalue="576"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1297.73 519.92 L 1297.72 476.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv35" d="M 1409.72 432.22 L 1409.72 392" stroke-width="1" zvalue="583"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="29@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.72 432.22 L 1409.72 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv35" d="M 1409.73 519.92 L 1409.72 476.83" stroke-width="1" zvalue="584"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.73 519.92 L 1409.72 476.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv35" d="M 1769.28 436.94 L 1769.28 392" stroke-width="1" zvalue="588"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="29@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1769.28 436.94 L 1769.28 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv35" d="M 1769.28 481.56 L 1769.28 521.21" stroke-width="1" zvalue="590"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@1" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1769.28 481.56 L 1769.28 521.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv35" d="M 893.28 438.06 L 893.28 392" stroke-width="1" zvalue="594"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="27@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.28 438.06 L 893.28 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv35" d="M 893.28 482.67 L 893.28 512.67 L 747.11 512.67 L 747.11 392" stroke-width="1" zvalue="595"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@1" LinkObjectIDznd="10@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.28 482.67 L 893.28 512.67 L 747.11 512.67 L 747.11 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv35" d="M 747.15 535.86 L 747.15 512.67" stroke-width="1" zvalue="599"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.15 535.86 L 747.15 512.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv35" d="M 1072.33 438.06 L 1072.33 392" stroke-width="1" zvalue="603"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="27@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.33 438.06 L 1072.33 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv35" d="M 1072.33 482.67 L 1072.33 512.67 L 1218.5 512.67 L 1218.5 392" stroke-width="1" zvalue="604"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@1" LinkObjectIDznd="29@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.33 482.67 L 1072.33 512.67 L 1218.5 512.67 L 1218.5 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv35" d="M 1218.46 535.86 L 1218.46 512.67" stroke-width="1" zvalue="606"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.46 535.86 L 1218.46 512.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 529.28 438.06 L 529.28 392" stroke-width="1" zvalue="610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="10@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 529.28 438.06 L 529.28 392" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv35" d="M 529.28 821.02 L 529.28 782.67" stroke-width="1" zvalue="615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="168@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 529.28 821.02 L 529.28 782.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv35" d="M 547.04 802.67 L 529.28 802.67" stroke-width="1" zvalue="618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="169" MaxPinNum="2"/>
   </metadata>
  <path d="M 547.04 802.67 L 529.28 802.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv35" d="M 474.97 718.85 L 488.13 718.85" stroke-width="1" zvalue="622"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 474.97 718.85 L 488.13 718.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv35" d="M 511.96 718.29 L 529.29 718.29" stroke-width="1" zvalue="624"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@1" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 511.96 718.29 L 529.29 718.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="v400" d="M 529.29 893.21 L 529.29 937.39" stroke-width="1" zvalue="627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@1" LinkObjectIDznd="180@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 529.29 893.21 L 529.29 937.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="v400" d="M 468.47 914.31 L 529.29 914.31" stroke-width="1" zvalue="630"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 468.47 914.31 L 529.29 914.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv35" d="M 529.28 738.06 L 529.31 697.74" stroke-width="1" zvalue="633"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="187@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 529.28 738.06 L 529.31 697.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv35" d="M 529.28 673.9 L 529.28 482.67" stroke-width="1" zvalue="634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 529.28 673.9 L 529.28 482.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv35" d="M 542.04 507.42 L 529.28 507.42" stroke-width="1" zvalue="639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="190" MaxPinNum="2"/>
   </metadata>
  <path d="M 542.04 507.42 L 529.28 507.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 542.04 651.42 L 529.28 651.42" stroke-width="1" zvalue="640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="190" MaxPinNum="2"/>
   </metadata>
  <path d="M 542.04 651.42 L 529.28 651.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv35" d="M 640.23 832.42 L 640.23 798.69" stroke-width="1" zvalue="646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="199@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 640.23 832.42 L 640.23 798.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv35" d="M 529.29 718.5 L 640.36 718.5 L 640.36 757.13" stroke-width="1" zvalue="647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189" LinkObjectIDznd="199@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 529.29 718.5 L 640.36 718.5 L 640.36 757.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv35" d="M 752.23 832.42 L 752.23 798.69" stroke-width="1" zvalue="652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="204@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.23 832.42 L 752.23 798.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv35" d="M 640.36 718.5 L 752.36 718.5 L 752.36 757.13" stroke-width="1" zvalue="653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201" LinkObjectIDznd="204@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 640.36 718.5 L 752.36 718.5 L 752.36 757.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="41">
   <use class="kv35" height="30" transform="rotate(0,693.333,214.889) scale(1.11111,0.814815) translate(-68.5,46.0606)" width="15" x="685" xlink:href="#Disconnector:刀闸_0" y="202.6666666666668" zvalue="499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449964736517" ObjectName="35kV昔铝Ⅰ线3316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449964736517"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,693.333,214.889) scale(1.11111,0.814815) translate(-68.5,46.0606)" width="15" x="685" y="202.6666666666668"/></g>
  <g id="54">
   <use class="kv35" height="30" transform="rotate(0,1689.33,214.889) scale(1.11111,0.814815) translate(-168.1,46.0606)" width="15" x="1681" xlink:href="#Disconnector:刀闸_0" y="202.6666666666668" zvalue="510"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449964933125" ObjectName="35kV昔铝Ⅱ线3326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449964933125"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1689.33,214.889) scale(1.11111,0.814815) translate(-168.1,46.0606)" width="15" x="1681" y="202.6666666666668"/></g>
  <g id="73">
   <use class="kv35" height="30" transform="rotate(90,1008.67,260.444) scale(1.11111,0.814815) translate(-100.033,56.4141)" width="15" x="1000.333333333333" xlink:href="#Disconnector:刀闸_0" y="248.2222222222223" zvalue="530"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965195269" ObjectName="35kV星云铝厂一级电站线3339隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449965195269"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1008.67,260.444) scale(1.11111,0.814815) translate(-100.033,56.4141)" width="15" x="1000.333333333333" y="248.2222222222223"/></g>
  <g id="84">
   <use class="kv35" height="30" transform="rotate(90,1352.67,260.444) scale(1.11111,0.814815) translate(-134.433,56.4141)" width="15" x="1344.333333333333" xlink:href="#Disconnector:刀闸_0" y="248.2222222222223" zvalue="541"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965260805" ObjectName="35kV星云铝厂二级电站线3349隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449965260805"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1352.67,260.444) scale(1.11111,0.814815) translate(-134.433,56.4141)" width="15" x="1344.333333333333" y="248.2222222222223"/></g>
  <g id="177">
   <use class="kv35" height="30" transform="rotate(270,499.944,718.222) scale(-1.11111,0.814815) translate(-949.061,160.455)" width="15" x="491.6111111111109" xlink:href="#Disconnector:刀闸_0" y="706" zvalue="621"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966112773" ObjectName="#1炉变3019隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449966112773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,499.944,718.222) scale(-1.11111,0.814815) translate(-949.061,160.455)" width="15" x="491.6111111111109" y="706"/></g>
  <g id="187">
   <use class="kv35" height="30" transform="rotate(0,529.375,685.722) scale(-1.11111,0.814815) translate(-1004.98,153.068)" width="15" x="521.0419745009563" xlink:href="#Disconnector:刀闸_0" y="673.5" zvalue="632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966374917" ObjectName="#1炉变3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449966374917"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,529.375,685.722) scale(-1.11111,0.814815) translate(-1004.98,153.068)" width="15" x="521.0419745009563" y="673.5"/></g>
  <g id="199">
   <use class="kv35" height="30" transform="rotate(0,640.222,778.722) scale(1.62963,1.62963) translate(-242.636,-291.426)" width="15" x="628" xlink:href="#Disconnector:令克_0" y="754.2777773539224" zvalue="644"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966571525" ObjectName="#1硅炉动力变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192449966571525"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,640.222,778.722) scale(1.62963,1.62963) translate(-242.636,-291.426)" width="15" x="628" y="754.2777773539224"/></g>
  <g id="204">
   <use class="kv35" height="30" transform="rotate(0,752.222,778.722) scale(1.62963,1.62963) translate(-285.909,-291.426)" width="15" x="740" xlink:href="#Disconnector:令克_0" y="754.2777773539224" zvalue="651"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966702597" ObjectName="#1硅炉环保变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192449966702597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,752.222,778.722) scale(1.62963,1.62963) translate(-285.909,-291.426)" width="15" x="740" y="754.2777773539224"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="45">
   <use class="kv35" height="30" transform="rotate(270,743.889,261) scale(1.51852,1.51852) translate(-246.233,-81.3442)" width="30" x="721.1111111111111" xlink:href="#Accessory:PT带熔断器_0" y="238.2222222222222" zvalue="502"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449964802053" ObjectName="35kV昔铝Ⅰ线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,743.889,261) scale(1.51852,1.51852) translate(-246.233,-81.3442)" width="30" x="721.1111111111111" y="238.2222222222222"/></g>
  <g id="51">
   <use class="kv35" height="30" transform="rotate(270,1739.89,261) scale(1.51852,1.51852) translate(-586.331,-81.3442)" width="30" x="1717.111111111111" xlink:href="#Accessory:PT带熔断器_0" y="238.2222222222222" zvalue="514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449964867590" ObjectName="35kV昔铝Ⅱ线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1739.89,261) scale(1.51852,1.51852) translate(-586.331,-81.3442)" width="30" x="1717.111111111111" y="238.2222222222222"/></g>
  <g id="63">
   <use class="kv35" height="30" transform="rotate(270,1055.89,261) scale(1.51852,1.51852) translate(-352.77,-81.3442)" width="30" x="1033.111111111111" xlink:href="#Accessory:PT带熔断器_0" y="238.2222222222222" zvalue="526"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965064197" ObjectName="35kV星云铝厂一级电站线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1055.89,261) scale(1.51852,1.51852) translate(-352.77,-81.3442)" width="30" x="1033.111111111111" y="238.2222222222222"/></g>
  <g id="86">
   <use class="kv35" height="30" transform="rotate(270,1399.89,261) scale(1.51852,1.51852) translate(-470.233,-81.3442)" width="30" x="1377.111111111111" xlink:href="#Accessory:PT带熔断器_0" y="238.2222222222222" zvalue="539"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965326341" ObjectName="35kV星云铝厂二级电站线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1399.89,261) scale(1.51852,1.51852) translate(-470.233,-81.3442)" width="30" x="1377.111111111111" y="238.2222222222222"/></g>
  <g id="150">
   <use class="kv35" height="30" transform="rotate(0,747.222,558.111) scale(1.51852,1.51852) translate(-247.371,-182.797)" width="30" x="724.4444444444445" xlink:href="#Accessory:PT带熔断器_0" y="535.3333333333333" zvalue="597"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965916165" ObjectName="313PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,747.222,558.111) scale(1.51852,1.51852) translate(-247.371,-182.797)" width="30" x="724.4444444444445" y="535.3333333333333"/></g>
  <g id="156">
   <use class="kv35" height="30" transform="rotate(0,1218.39,558.111) scale(-1.51852,1.51852) translate(-2012.96,-182.797)" width="30" x="1195.611111429002" xlink:href="#Accessory:PT带熔断器_0" y="535.3333333333333" zvalue="605"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965981701" ObjectName="323PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1218.39,558.111) scale(-1.51852,1.51852) translate(-2012.96,-182.797)" width="30" x="1195.611111429002" y="535.3333333333333"/></g>
  <g id="172">
   <use class="kv35" height="20" transform="rotate(270,562.111,802.667) scale(1.44444,1.72222) translate(-168.513,-329.38)" width="20" x="547.6666666666665" xlink:href="#Accessory:线路PT3_0" y="785.4444444444443" zvalue="617"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966047237" ObjectName="#1炉变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,562.111,802.667) scale(1.44444,1.72222) translate(-168.513,-329.38)" width="20" x="547.6666666666665" y="785.4444444444443"/></g>
  <g id="178">
   <use class="kv35" height="30" transform="rotate(90,452.722,718.778) scale(-1.51852,1.51852) translate(-743.079,-237.659)" width="30" x="429.9444444444446" xlink:href="#Accessory:PT带熔断器_0" y="696" zvalue="620"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966178309" ObjectName="301PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,452.722,718.778) scale(-1.51852,1.51852) translate(-743.079,-237.659)" width="30" x="429.9444444444446" y="696"/></g>
  <g id="191">
   <use class="kv35" height="20" transform="rotate(270,557.111,651.417) scale(1.44444,1.72222) translate(-166.974,-265.953)" width="20" x="542.6666666666665" xlink:href="#Accessory:线路PT3_0" y="634.1944444444443" zvalue="636"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966440453" ObjectName="301避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,557.111,651.417) scale(1.44444,1.72222) translate(-166.974,-265.953)" width="20" x="542.6666666666665" y="634.1944444444443"/></g>
  <g id="192">
   <use class="kv35" height="20" transform="rotate(270,557.111,507.417) scale(1.44444,1.72222) translate(-166.974,-205.565)" width="20" x="542.6666666666665" xlink:href="#Accessory:线路PT3_0" y="490.1944444444443" zvalue="638"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966505989" ObjectName="329避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,557.111,507.417) scale(1.44444,1.72222) translate(-166.974,-205.565)" width="20" x="542.6666666666665" y="490.1944444444443"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="90">
   <use class="kv35" height="26" transform="rotate(0,561.833,238.32) scale(2.05882,-2.05882) translate(-279.943,-340.311)" width="17" x="544.3333333333334" xlink:href="#EnergyConsumer:Y-Y站用_0" y="211.5555555555555" zvalue="545"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965457413" ObjectName="#1动力变"/>
   <cge:TPSR_Ref TObjectID="6192449965457413"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,561.833,238.32) scale(2.05882,-2.05882) translate(-279.943,-340.311)" width="17" x="544.3333333333334" y="211.5555555555555"/></g>
  <g id="105">
   <use class="kv35" height="30" transform="rotate(0,650.046,578.585) scale(1.90741,-1.67949) translate(-302.44,-912.894)" width="15" x="635.7407407407406" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="553.3931520250109" zvalue="551"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965522949" ObjectName="336线"/>
   <cge:TPSR_Ref TObjectID="6192449965522949"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,650.046,578.585) scale(1.90741,-1.67949) translate(-302.44,-912.894)" width="15" x="635.7407407407406" y="553.3931520250109"/></g>
  <g id="115">
   <use class="kv35" height="30" transform="rotate(0,1530.05,578.585) scale(1.90741,-1.67949) translate(-721.081,-912.894)" width="15" x="1515.740740740741" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="553.3931520250109" zvalue="559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965588485" ObjectName="341线"/>
   <cge:TPSR_Ref TObjectID="6192449965588485"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1530.05,578.585) scale(1.90741,-1.67949) translate(-721.081,-912.894)" width="15" x="1515.740740740741" y="553.3931520250109"/></g>
  <g id="120">
   <use class="kv35" height="30" transform="rotate(0,1647.16,579.697) scale(1.90741,-1.67949) translate(-776.794,-914.667)" width="15" x="1632.851851851852" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="554.5042631361219" zvalue="565"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965654021" ObjectName="342线"/>
   <cge:TPSR_Ref TObjectID="6192449965654021"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1647.16,579.697) scale(1.90741,-1.67949) translate(-776.794,-914.667)" width="15" x="1632.851851851852" y="554.5042631361219"/></g>
  <g id="126">
   <use class="kv35" height="26" transform="rotate(0,1297.39,546.346) scale(2.05882,2.05882) translate(-658.229,-267.213)" width="17" x="1279.888888888889" xlink:href="#EnergyConsumer:Y-Y站用_0" y="519.5816989225501" zvalue="571"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965719557" ObjectName="#2动力变"/>
   <cge:TPSR_Ref TObjectID="6192449965719557"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1297.39,546.346) scale(2.05882,2.05882) translate(-658.229,-267.213)" width="17" x="1279.888888888889" y="519.5816989225501"/></g>
  <g id="134">
   <use class="kv35" height="26" transform="rotate(0,1409.39,546.346) scale(2.05882,2.05882) translate(-715.829,-267.213)" width="17" x="1391.888888888889" xlink:href="#EnergyConsumer:Y-Y站用_0" y="519.5816989225501" zvalue="579"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965785093" ObjectName="#3动力变"/>
   <cge:TPSR_Ref TObjectID="6192449965785093"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1409.39,546.346) scale(2.05882,2.05882) translate(-715.829,-267.213)" width="17" x="1391.888888888889" y="519.5816989225501"/></g>
  <g id="180">
   <use class="v400" height="14" transform="rotate(0,529.294,944.889) scale(1.11111,1.11111) translate(-51.4294,-93.7111)" width="27" x="514.2938728852308" xlink:href="#EnergyConsumer:电炉_0" y="937.1111111111111" zvalue="626"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966243845" ObjectName="#1电炉"/>
   <cge:TPSR_Ref TObjectID="6192449966243845"/></metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,529.294,944.889) scale(1.11111,1.11111) translate(-51.4294,-93.7111)" width="27" x="514.2938728852308" y="937.1111111111111"/></g>
  <g id="200">
   <use class="kv35" height="26" transform="rotate(0,639.889,858.846) scale(2.05882,2.05882) translate(-320.086,-427.928)" width="17" x="622.3888888888889" xlink:href="#EnergyConsumer:Y-Y站用_0" y="832.08169892255" zvalue="642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966637061" ObjectName="#1硅炉动力变"/>
   <cge:TPSR_Ref TObjectID="6192449966637061"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,639.889,858.846) scale(2.05882,2.05882) translate(-320.086,-427.928)" width="17" x="622.3888888888889" y="832.08169892255"/></g>
  <g id="205">
   <use class="kv35" height="26" transform="rotate(0,751.889,858.846) scale(2.05882,2.05882) translate(-377.686,-427.928)" width="17" x="734.3888888888889" xlink:href="#EnergyConsumer:Y-Y站用_0" y="832.08169892255" zvalue="649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966768133" ObjectName="#1硅炉环保变"/>
   <cge:TPSR_Ref TObjectID="6192449966768133"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,751.889,858.846) scale(2.05882,2.05882) translate(-377.686,-427.928)" width="17" x="734.3888888888889" y="832.08169892255"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="138">
   <use class="kv35" height="40" transform="rotate(270,1765.77,553.778) scale(-1.11111,1.11111) translate(-3351.64,-53.1556)" width="60" x="1732.440940827226" xlink:href="#Compensator:遮放35kV电容_0" y="531.5555555555555" zvalue="589"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449965850629" ObjectName="#1无功补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192449965850629"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1765.77,553.778) scale(-1.11111,1.11111) translate(-3351.64,-53.1556)" width="60" x="1732.440940827226" y="531.5555555555555"/></g>
  <g id="182">
   <use class="v400" height="30" transform="rotate(90,454.028,914.306) scale(-1.11111,1.11111) translate(-860.986,-89.7639)" width="30" x="437.3611111111112" xlink:href="#Compensator:并联电容器组_0" y="897.638888888889" zvalue="628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449966309381" ObjectName="#1炉变电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449966309381"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,454.028,914.306) scale(-1.11111,1.11111) translate(-860.986,-89.7639)" width="30" x="437.3611111111112" y="897.638888888889"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="164">
   <g id="1640">
    <use class="kv35" height="50" transform="rotate(0,529.278,857.111) scale(1.46667,1.46667) translate(-161.407,-261.051)" width="30" x="507.28" xlink:href="#PowerTransformer2:D-D_0" y="820.4400000000001" zvalue="611"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874449289219" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1641">
    <use class="v400" height="50" transform="rotate(0,529.278,857.111) scale(1.46667,1.46667) translate(-161.407,-261.051)" width="30" x="507.28" xlink:href="#PowerTransformer2:D-D_1" y="820.4400000000001" zvalue="611"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874449354755" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399456587779" ObjectName="#1炉变"/>
   <cge:TPSR_Ref TObjectID="6755399456587779"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,529.278,857.111) scale(1.46667,1.46667) translate(-161.407,-261.051)" width="30" x="507.28" y="820.4400000000001"/></g>
 </g>
</svg>