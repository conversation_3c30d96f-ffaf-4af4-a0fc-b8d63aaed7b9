<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586362370" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV芒牛坝电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="44.43" xlink:href="logo.png" y="43.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,179.054,73.4286) scale(1,1) translate(0,0)" writing-mode="lr" x="179.05" xml:space="preserve" y="76.93000000000001" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.762,73.1189) scale(1,1) translate(6.93096e-15,0)" writing-mode="lr" x="180.76" xml:space="preserve" y="82.12" zvalue="3">10kV芒牛坝电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="5" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,70.4375,364) scale(1,1) translate(0,0)" width="72.88" x="34" y="352" zvalue="74"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.4375,364) scale(1,1) translate(0,0)" writing-mode="lr" x="70.44" xml:space="preserve" y="368.5" zvalue="74">信号一览</text>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="378.4285714285716" x2="378.4285714285716" y1="11.42857142857156" y2="1041.428571428572" zvalue="4"/>
  <line fill="none" id="31" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.428571428571786" x2="371.4285714285713" y1="147.2990640426539" y2="147.2990640426539" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="159.4285714285716" y2="159.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="185.4285714285716" y2="185.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="5.428571428571558" y1="159.4285714285716" y2="185.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="159.4285714285716" y2="185.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="159.4285714285716" y2="159.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="185.4285714285716" y2="185.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="159.4285714285716" y2="185.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.4285714285716" x2="367.4285714285716" y1="159.4285714285716" y2="185.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="185.4285714285716" y2="185.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="209.6785714285716" y2="209.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="5.428571428571558" y1="185.4285714285716" y2="209.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="185.4285714285716" y2="209.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="185.4285714285716" y2="185.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="209.6785714285716" y2="209.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="185.4285714285716" y2="209.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.4285714285716" x2="367.4285714285716" y1="185.4285714285716" y2="209.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="209.6785714285716" y2="209.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="232.4285714285716" y2="232.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="5.428571428571558" y1="209.6785714285716" y2="232.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="209.6785714285716" y2="232.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="209.6785714285716" y2="209.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="232.4285714285716" y2="232.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="209.6785714285716" y2="232.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.4285714285716" x2="367.4285714285716" y1="209.6785714285716" y2="232.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="232.4285714285716" y2="232.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="255.1785714285716" y2="255.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="5.428571428571558" y1="232.4285714285716" y2="255.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="232.4285714285716" y2="255.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="232.4285714285716" y2="232.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="255.1785714285716" y2="255.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="232.4285714285716" y2="255.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.4285714285716" x2="367.4285714285716" y1="232.4285714285716" y2="255.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="255.1785714285716" y2="255.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="186.4285714285716" y1="277.9285714285716" y2="277.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.428571428571558" x2="5.428571428571558" y1="255.1785714285716" y2="277.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="255.1785714285716" y2="277.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="255.1785714285716" y2="255.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="367.4285714285716" y1="277.9285714285716" y2="277.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.4285714285716" x2="186.4285714285716" y1="255.1785714285716" y2="277.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.4285714285716" x2="367.4285714285716" y1="255.1785714285716" y2="277.9285714285716"/>
  <line fill="none" id="29" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.428571428571786" x2="371.4285714285713" y1="617.299064042654" y2="617.299064042654" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.428571428571558" x2="94.42857142857156" y1="932.4285714285717" y2="932.4285714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.428571428571558" x2="94.42857142857156" y1="971.5918714285717" y2="971.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.428571428571558" x2="4.428571428571558" y1="932.4285714285717" y2="971.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="94.42857142857156" y1="932.4285714285717" y2="971.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="364.4285714285716" y1="932.4285714285717" y2="932.4285714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="364.4285714285716" y1="971.5918714285717" y2="971.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="94.42857142857156" y1="932.4285714285717" y2="971.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.4285714285716" x2="364.4285714285716" y1="932.4285714285717" y2="971.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.428571428571558" x2="94.42857142857156" y1="971.5918414285717" y2="971.5918414285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.428571428571558" x2="94.42857142857156" y1="999.5102414285716" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.428571428571558" x2="4.428571428571558" y1="971.5918414285717" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="94.42857142857156" y1="971.5918414285717" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="184.4285714285716" y1="971.5918414285717" y2="971.5918414285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="184.4285714285716" y1="999.5102414285716" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="94.42857142857156" y1="971.5918414285717" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4285714285716" x2="184.4285714285716" y1="971.5918414285717" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4285714285717" x2="274.4285714285717" y1="971.5918414285717" y2="971.5918414285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4285714285717" x2="274.4285714285717" y1="999.5102414285716" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="971.5918414285717" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.4285714285717" x2="274.4285714285717" y1="971.5918414285717" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.4285714285716" x2="364.4285714285716" y1="971.5918414285717" y2="971.5918414285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.4285714285716" x2="364.4285714285716" y1="999.5102414285716" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.4285714285716" x2="274.4285714285716" y1="971.5918414285717" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.4285714285716" x2="364.4285714285716" y1="971.5918414285717" y2="999.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.428571428571558" x2="94.42857142857156" y1="999.5101714285717" y2="999.5101714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.428571428571558" x2="94.42857142857156" y1="1027.428571428572" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.428571428571558" x2="4.428571428571558" y1="999.5101714285717" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="94.42857142857156" y1="999.5101714285717" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="184.4285714285716" y1="999.5101714285717" y2="999.5101714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="184.4285714285716" y1="1027.428571428572" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.42857142857156" x2="94.42857142857156" y1="999.5101714285717" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4285714285716" x2="184.4285714285716" y1="999.5101714285717" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4285714285717" x2="274.4285714285717" y1="999.5101714285717" y2="999.5101714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4285714285717" x2="274.4285714285717" y1="1027.428571428572" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="999.5101714285717" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.4285714285717" x2="274.4285714285717" y1="999.5101714285717" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.4285714285716" x2="364.4285714285716" y1="999.5101714285717" y2="999.5101714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.4285714285716" x2="364.4285714285716" y1="1027.428571428572" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.4285714285716" x2="274.4285714285716" y1="999.5101714285717" y2="1027.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.4285714285716" x2="364.4285714285716" y1="999.5101714285717" y2="1027.428571428572"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.4286,952.429) scale(1,1) translate(0,0)" writing-mode="lr" x="49.43" xml:space="preserve" y="958.4299999999999" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.4286,986.429) scale(1,1) translate(0,0)" writing-mode="lr" x="46.43" xml:space="preserve" y="992.4299999999999" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.429,986.429) scale(1,1) translate(0,0)" writing-mode="lr" x="228.43" xml:space="preserve" y="992.4299999999999" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.4286,1014.43) scale(1,1) translate(0,0)" writing-mode="lr" x="45.43" xml:space="preserve" y="1020.43" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.429,1014.43) scale(1,1) translate(0,0)" writing-mode="lr" x="227.43" xml:space="preserve" y="1020.43" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.9286,646.929) scale(1,1) translate(0,2.09142e-13)" writing-mode="lr" x="69.92857142857156" xml:space="preserve" y="651.4285714285716" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,203.241,954.429) scale(1,1) translate(0,0)" writing-mode="lr" x="203.24" xml:space="preserve" y="960.4299999999999" zvalue="27">MangNiuBa-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139.483,1014.43) scale(1,1) translate(0,0)" writing-mode="lr" x="139.48" xml:space="preserve" y="1020.43" zvalue="28">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.4286,173.429) scale(1,1) translate(0,0)" writing-mode="lr" x="43.43" xml:space="preserve" y="178.93" zvalue="30">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.429,173.429) scale(1,1) translate(0,0)" writing-mode="lr" x="223.43" xml:space="preserve" y="178.93" zvalue="31">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.6161,245.429) scale(1,1) translate(0,0)" writing-mode="lr" x="50.62" xml:space="preserve" y="249.93" zvalue="33">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.512,200.123) scale(1,1) translate(0,0)" writing-mode="lr" x="235.51" xml:space="preserve" y="204.62" zvalue="34">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,599.143,600.429) scale(1,1) translate(0,0)" writing-mode="lr" x="599.14" xml:space="preserve" y="604.9299999999999" zvalue="38">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" x="742.1328125" xml:space="preserve" y="952.9618490134186" zvalue="41">#1发电机    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="742.1328125" xml:space="preserve" y="968.9618490134186" zvalue="41">160KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.864,701.505) scale(1,1) translate(0,0)" writing-mode="lr" x="718.86" xml:space="preserve" y="706" zvalue="43">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,763.47,779.926) scale(1,1) translate(0,0)" writing-mode="lr" x="763.47" xml:space="preserve" y="784.4299999999999" zvalue="47">401</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" x="1245.5859375" xml:space="preserve" y="952.1024740134186" zvalue="51">#2发电机    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1245.5859375" xml:space="preserve" y="968.1024740134186" zvalue="51">320KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212.58,702.648) scale(1,1) translate(0,0)" writing-mode="lr" x="1212.58" xml:space="preserve" y="707.15" zvalue="53">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1265.18,781.069) scale(1,1) translate(0,1.37325e-12)" writing-mode="lr" x="1265.18" xml:space="preserve" y="785.5700000000001" zvalue="57">402</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" x="1048.234375" xml:space="preserve" y="469.25" zvalue="60">#1主变           </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1048.234375" xml:space="preserve" y="485.25" zvalue="60">630KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957.571,341.571) scale(1,1) translate(0,0)" writing-mode="lr" x="957.5700000000001" xml:space="preserve" y="346.07" zvalue="62">0411</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.27,266.825) scale(1,1) translate(3.34322e-13,0)" writing-mode="lr" x="1014.27" xml:space="preserve" y="271.33" zvalue="65">041</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.444,182.222) scale(1,1) translate(0,0)" writing-mode="lr" x="990.4400000000001" xml:space="preserve" y="186.72" zvalue="67">10kV三仙洞线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186.211,364.591) scale(1,1) translate(0,0)" writing-mode="lr" x="186.21" xml:space="preserve" y="369.09" zvalue="70">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,291.211,364.591) scale(1,1) translate(0,0)" writing-mode="lr" x="291.21" xml:space="preserve" y="369.09" zvalue="71">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV芒牛坝电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="34" y="352" zvalue="74"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="36">
   <path class="v400" d="M 535.71 630.86 L 1449 630.86" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242985988" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242985988"/></metadata>
  <path d="M 535.71 630.86 L 1449 630.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v400" height="30" transform="rotate(0,737.348,898.148) scale(1.85899,1.85899) translate(-327.824,-402.125)" width="30" x="709.4628110403481" xlink:href="#Generator:发电机_0" y="870.2629266475365" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795391493" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449795391493"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,737.348,898.148) scale(1.85899,1.85899) translate(-327.824,-402.125)" width="30" x="709.4628110403481" y="870.2629266475365"/></g>
  <g id="52">
   <use class="v400" height="30" transform="rotate(0,1240.81,897.291) scale(1.85899,1.85899) translate(-560.46,-401.729)" width="30" x="1212.923192025473" xlink:href="#Generator:发电机_0" y="869.4057837903937" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795522566" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449795522566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1240.81,897.291) scale(1.85899,1.85899) translate(-560.46,-401.729)" width="30" x="1212.923192025473" y="869.4057837903937"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v400" height="30" transform="rotate(0,738.082,702.505) scale(1.9625,1.2338) translate(-354.771,-129.613)" width="15" x="723.3635212897802" xlink:href="#Disconnector:刀闸_0" y="683.9980158730156" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795325957" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449795325957"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,738.082,702.505) scale(1.9625,1.2338) translate(-354.771,-129.613)" width="15" x="723.3635212897802" y="683.9980158730156"/></g>
  <g id="51">
   <use class="v400" height="30" transform="rotate(0,1239.8,703.648) scale(1.9625,1.2338) translate(-600.834,-129.83)" width="15" x="1225.077807004066" xlink:href="#Disconnector:刀闸_0" y="685.1408730158728" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795457029" ObjectName="#2发电机4021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449795457029"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1239.8,703.648) scale(1.9625,1.2338) translate(-600.834,-129.83)" width="15" x="1225.077807004066" y="685.1408730158728"/></g>
  <g id="56">
   <use class="kv10" height="30" transform="rotate(0,990.286,342.571) scale(1.42857,1.42857) translate(-293.871,-96.3429)" width="15" x="979.5714285714286" xlink:href="#Disconnector:令克_0" y="321.1428571428572" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795588102" ObjectName="#1主变10kV侧0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449795588102"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,990.286,342.571) scale(1.42857,1.42857) translate(-293.871,-96.3429)" width="15" x="979.5714285714286" y="321.1428571428572"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="43">
   <path class="v400" d="M 738.25 684.61 L 738.25 630.86" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.25 684.61 L 738.25 630.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v400" d="M 737.35 870.73 L 737.35 789.23" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 737.35 870.73 L 737.35 789.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="v400" d="M 738.97 767.99 L 738.97 720.7" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.97 767.99 L 738.97 720.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v400" d="M 1240.81 869.87 L 1240.81 790.37" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="49@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1240.81 869.87 L 1240.81 790.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v400" d="M 1240.69 769.13 L 1240.69 721.84" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="51@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1240.69 769.13 L 1240.69 721.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v400" d="M 990.29 505.21 L 990.29 630.86" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.29 505.21 L 990.29 630.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 990.17 360.07 L 990.17 434.93" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.17 360.07 L 990.17 434.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 990.4 273.44 L 990.4 323.64" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@1" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.4 273.44 L 990.4 323.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 990.44 199.79 L 990.28 252.2" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="59@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.44 199.79 L 990.28 252.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="v400" d="M 1239.97 685.75 L 1239.97 630.86" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.97 685.75 L 1239.97 630.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="170">
   <use class="v400" height="20" transform="rotate(0,739.012,778.619) scale(1.22222,1.11111) translate(-133.255,-76.7508)" width="10" x="732.9011668709484" xlink:href="#Breaker:开关_0" y="767.5076312576309" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510154757" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510154757"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,739.012,778.619) scale(1.22222,1.11111) translate(-133.255,-76.7508)" width="10" x="732.9011668709484" y="767.5076312576309"/></g>
  <g id="49">
   <use class="v400" height="20" transform="rotate(0,1240.73,779.762) scale(1.22222,1.11111) translate(-224.476,-76.865)" width="10" x="1234.615452585234" xlink:href="#Breaker:开关_0" y="768.650488400488" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510220293" ObjectName="#2发电机402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510220293"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1240.73,779.762) scale(1.22222,1.11111) translate(-224.476,-76.865)" width="10" x="1234.615452585234" y="768.650488400488"/></g>
  <g id="59">
   <use class="kv10" height="20" transform="rotate(0,990.323,262.825) scale(1.22222,1.11111) translate(-178.948,-25.1714)" width="10" x="984.2121693121693" xlink:href="#Breaker:开关_0" y="251.7142857142858" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510285829" ObjectName="#1主变041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510285829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,990.323,262.825) scale(1.22222,1.11111) translate(-178.948,-25.1714)" width="10" x="984.2121693121693" y="251.7142857142858"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="53">
   <g id="530">
    <use class="kv10" height="60" transform="rotate(0,990.286,470) scale(1.07143,1.19048) translate(-64.5905,-69.4857)" width="40" x="968.86" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="434.29" zvalue="59"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436116484" ObjectName="10"/>
    </metadata>
   </g>
   <g id="531">
    <use class="v400" height="60" transform="rotate(0,990.286,470) scale(1.07143,1.19048) translate(-64.5905,-69.4857)" width="40" x="968.86" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="434.29" zvalue="59"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436182020" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450296324" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450296324"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,990.286,470) scale(1.07143,1.19048) translate(-64.5905,-69.4857)" width="40" x="968.86" y="434.29"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,318.485,365.107) scale(0.708333,0.665547) translate(126.766,178.458)" width="30" x="307.86" xlink:href="#State:红绿圆(方形)_0" y="355.12" zvalue="72"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,318.485,365.107) scale(0.708333,0.665547) translate(126.766,178.458)" width="30" x="307.86" y="355.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,222.86,365.107) scale(0.708333,0.665547) translate(87.3909,178.458)" width="30" x="212.24" xlink:href="#State:红绿圆(方形)_0" y="355.12" zvalue="73"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,222.86,365.107) scale(0.708333,0.665547) translate(87.3909,178.458)" width="30" x="212.24" y="355.12"/></g>
 </g>
</svg>