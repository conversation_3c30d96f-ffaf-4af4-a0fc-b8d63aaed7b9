<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" LineID="8444249328254979_5066549581119490" MapType="line" StationID="5066549581119490" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV勐乃河一级电站支线" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="276.5" x="43.5" xlink:href="logo.png" y="35"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,181.75,65) scale(1,1) translate(0,0)" writing-mode="lr" x="181.75" xml:space="preserve" y="68.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.75,64.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="180.75" xml:space="preserve" y="73.69" zvalue="3">10kV勐乃河一级电站支线</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="25" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,84,306) scale(1,1) translate(0,0)" width="97" x="35.5" y="294" zvalue="9"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84,306) scale(1,1) translate(0,0)" writing-mode="lr" x="84" xml:space="preserve" y="310.5" zvalue="9">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="51" id="52" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,824,130.5) scale(1,1) translate(0,0)" width="174" x="737" y="105" zvalue="52"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824,130.5) scale(1,1) translate(0,0)" writing-mode="lr" x="824" xml:space="preserve" y="135" zvalue="52">昔马简易变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="51" id="72" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1537,635.5) scale(1,1) translate(0,0)" width="174" x="1450" y="610" zvalue="72"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1537,635.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1537" xml:space="preserve" y="640" zvalue="72">勐乃河一级</text>
  <rect fill="none" fill-opacity="0" height="148" id="33" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,812,158) scale(1,1) translate(0,0)" width="274" x="675" y="84" zvalue="78"/>
  <rect fill="none" fill-opacity="0" height="145" id="34" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1507,642.5) scale(1,1) translate(0,0)" width="362" x="1326" y="570" zvalue="79"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.5" x2="377.5" y1="3" y2="1033" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.500000000000227" x2="370.4999999999998" y1="138.8704926140824" y2="138.8704926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="151.0000000000001" y2="151.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="177.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="151.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="151.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="151.0000000000001" y2="151.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="177.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="151.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="151.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="177.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="201.2500000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="177.0000000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="177.0000000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="177.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="201.2500000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="177.0000000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="177.0000000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="201.2500000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="224.0000000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="201.2500000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="201.2500000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="201.2500000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="224.0000000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="201.2500000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="201.2500000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="224.0000000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="246.7500000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="224.0000000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="224.0000000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="224.0000000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="246.7500000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="224.0000000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="224.0000000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="246.7500000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="269.5000000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="246.7500000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="246.7500000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="246.7500000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="269.5000000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="246.7500000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="246.7500000000001" y2="269.5000000000001"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.500000000000227" x2="370.4999999999998" y1="608.8704926140824" y2="608.8704926140824" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="924.0000000000002" y2="924.0000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="963.1633000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="3.5" y1="924.0000000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="924.0000000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="363.5" y1="924.0000000000002" y2="924.0000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="363.5" y1="963.1633000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="924.0000000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.5" x2="363.5" y1="924.0000000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="963.1632700000002" y2="963.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="991.0816700000003" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="3.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="963.1632700000002" y2="963.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="991.0816700000003" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5" x2="183.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="963.1632700000002" y2="963.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="991.0816700000003" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="183.5000000000001" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5000000000001" x2="273.5000000000001" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="963.1632700000002" y2="963.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="991.0816700000003" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="273.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.5" x2="363.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="991.0816000000002" y2="991.0816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="3.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="991.0816000000002" y2="991.0816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5" x2="183.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="991.0816000000002" y2="991.0816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="183.5000000000001" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5000000000001" x2="273.5000000000001" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="991.0816000000002" y2="991.0816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="273.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.5" x2="363.5" y1="991.0816000000002" y2="1019"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.5,944) scale(1,1) translate(0,0)" writing-mode="lr" x="48.5" xml:space="preserve" y="950" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.5,978) scale(1,1) translate(0,0)" writing-mode="lr" x="45.5" xml:space="preserve" y="984" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.5,978) scale(1,1) translate(0,0)" writing-mode="lr" x="227.5" xml:space="preserve" y="984" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.5,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="44.5" xml:space="preserve" y="1012" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.5,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="226.5" xml:space="preserve" y="1012" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69,638.5) scale(1,1) translate(0,2.06335e-13)" writing-mode="lr" x="69" xml:space="preserve" y="643.0000000000001" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.899,305.841) scale(1,1) translate(0,0)" writing-mode="lr" x="201.9" xml:space="preserve" y="310.34" zvalue="19">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.899,305.841) scale(1,1) translate(0,0)" writing-mode="lr" x="306.9" xml:space="preserve" y="310.34" zvalue="20">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,481.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="486" zvalue="21">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,507) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="511.5" zvalue="22">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,532.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="537" zvalue="23">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.5,557) scale(1,1) translate(0,-1.20459e-13)" writing-mode="lr" x="79.5" xml:space="preserve" y="561.5000000000001" zvalue="24">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,583.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="588" zvalue="25">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.5,165) scale(1,1) translate(0,0)" writing-mode="lr" x="42.5" xml:space="preserve" y="170.5" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.5,165) scale(1,1) translate(0,0)" writing-mode="lr" x="222.5" xml:space="preserve" y="170.5" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.6875,237) scale(1,1) translate(0,0)" writing-mode="lr" x="49.69" xml:space="preserve" y="241.5" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.583,191.694) scale(1,1) translate(0,0)" writing-mode="lr" x="234.58" xml:space="preserve" y="196.19" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" x="255.875" xml:space="preserve" y="449.75" zvalue="33">0.4kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="255.875" xml:space="preserve" y="465.75" zvalue="33">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845.25,206) scale(1,1) translate(0,0)" writing-mode="lr" x="845.25" xml:space="preserve" y="210.5" zvalue="55">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,928.65,406) scale(1,1) translate(0,0)" writing-mode="lr" x="928.65" xml:space="preserve" y="410.5" zvalue="57">勐昔线#1+1杆G01断路器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,856.189,456) scale(1,1) translate(0,0)" writing-mode="lr" x="856.1900000000001" xml:space="preserve" y="460.5" zvalue="59">G012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327,528) scale(1,1) translate(0,0)" writing-mode="lr" x="1327" xml:space="preserve" y="532.5" zvalue="62">10kV勐昔线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" x="1010.5" xml:space="preserve" y="591.5" zvalue="64">勐昔线勐乃河一级电站支线#0+1杆</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1010.5" xml:space="preserve" y="607.5" zvalue="64">A01断路器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1082.5,673.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1082.5" xml:space="preserve" y="678" zvalue="66">A012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.25,611) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.25" xml:space="preserve" y="615.5" zvalue="71">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.5,506) scale(1,1) translate(0,0)" writing-mode="lr" x="916.5" xml:space="preserve" y="510.5" zvalue="75">#19杆</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,879.5,638) scale(1,1) translate(0,0)" writing-mode="lr" x="879.5" xml:space="preserve" y="642.5" zvalue="77">#35杆</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1233,666) scale(1,1) translate(0,0)" writing-mode="lr" x="1233" xml:space="preserve" y="670.5" zvalue="81">10kV勐乃河一级电站支线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,856.5,340) scale(1,1) translate(0,0)" writing-mode="lr" x="856.5" xml:space="preserve" y="344.5" zvalue="84">G011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.5,671.5) scale(1,1) translate(0,0)" writing-mode="lr" x="955.5" xml:space="preserve" y="676" zvalue="88">A011</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="35.5" y="294" zvalue="9"/></g>
  <g href="35kV昔马简易变.svg"><rect fill-opacity="0" height="51" width="174" x="737" y="105" zvalue="52"/></g>
  <g href="35kV勐乃河一级电站.svg"><rect fill-opacity="0" height="51" width="174" x="1450" y="610" zvalue="72"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="ConnectiveNodeClass">
  <g id="53">
   <path class="kv10" d="M 824.95 158 L 824.95 194.08" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.95 158 L 824.95 194.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 825.25 446.19 L 825.25 415.89" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@1" LinkObjectIDznd="56@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.25 446.19 L 825.25 415.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 825.28 467.64 L 825.28 529 L 1256.58 529" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.28 467.64 L 825.28 529 L 1256.58 529" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 909 529 L 909 726" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 909 529 L 909 726" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 1020.42 637.45 L 1071.86 637.45" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="66@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.42 637.45 L 1071.86 637.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 1093.31 638.06 L 1360.61 638.06" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@1" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1093.31 638.06 L 1360.61 638.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 1386.42 636.45 L 1450 636.45" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.42 636.45 L 1450 636.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 825.1 219.89 L 825.1 330.19" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@1" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.1 219.89 L 825.1 330.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 824.59 351.64 L 824.59 390.09" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@0" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.59 351.64 L 824.59 390.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 994.61 637.6 L 966.31 637.6" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@1" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.61 637.6 L 966.31 637.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 944.86 636.09 L 909 636.09" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 944.86 636.09 L 909 636.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="54">
   <use class="kv10" height="20" transform="rotate(0,825,207) scale(1.5,1.35) translate(-272.5,-50.1667)" width="10" x="817.5" xlink:href="#Breaker:开关_0" y="193.5" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924588470276" ObjectName="001"/>
   <cge:TPSR_Ref TObjectID="6473924588470276"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,825,207) scale(1.5,1.35) translate(-272.5,-50.1667)" width="10" x="817.5" y="193.5"/></g>
  <g id="56">
   <use class="kv10" height="20" transform="rotate(0,826.15,403) scale(1.5,1.35) translate(-272.883,-100.981)" width="10" x="818.65" xlink:href="#Breaker:开关_0" y="389.5" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924588535812" ObjectName="勐昔线#1+1杆G01断路器"/>
   <cge:TPSR_Ref TObjectID="6473924588535812"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,826.15,403) scale(1.5,1.35) translate(-272.883,-100.981)" width="10" x="818.65" y="389.5"/></g>
  <g id="64">
   <use class="kv10" height="20" transform="rotate(90,1007.5,637.5) scale(1.5,1.35) translate(-333.333,-161.778)" width="10" x="1000" xlink:href="#Breaker:开关_0" y="624" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924591419396" ObjectName="勐昔线勐乃河一级电站支线#0+1杆A01断路器"/>
   <cge:TPSR_Ref TObjectID="6473924591419396"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1007.5,637.5) scale(1.5,1.35) translate(-333.333,-161.778)" width="10" x="1000" y="624"/></g>
  <g id="70">
   <use class="kv10" height="20" transform="rotate(90,1373.5,636.5) scale(1.5,1.35) translate(-455.333,-161.519)" width="10" x="1366" xlink:href="#Breaker:开关_0" y="623" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924588666884" ObjectName="051"/>
   <cge:TPSR_Ref TObjectID="6473924588666884"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1373.5,636.5) scale(1.5,1.35) translate(-455.333,-161.519)" width="10" x="1366" y="623"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="59">
   <use class="kv10" height="30" transform="rotate(0,825.189,457) scale(1,-0.733333) translate(0,-1084.18)" width="15" x="817.6888355758942" xlink:href="#Disconnector:刀闸_0" y="446" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450268364805" ObjectName="勐昔线#1杆G012隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450268364805"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,825.189,457) scale(1,-0.733333) translate(0,-1084.18)" width="15" x="817.6888355758942" y="446"/></g>
  <g id="66">
   <use class="kv10" height="30" transform="rotate(90,1082.5,638) scale(1,-0.733333) translate(0,-1512)" width="15" x="1075" xlink:href="#Disconnector:刀闸_0" y="627" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450286190597" ObjectName="勐昔线勐乃河一级电站支线#0+1杆A012隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450286190597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1082.5,638) scale(1,-0.733333) translate(0,-1512)" width="15" x="1075" y="627"/></g>
  <g id="6">
   <use class="kv10" height="30" transform="rotate(0,824.5,341) scale(1,-0.733333) translate(0,-810)" width="15" x="817" xlink:href="#Disconnector:刀闸_0" y="330" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285993989" ObjectName="勐昔线#1+1杆G011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450285993989"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,824.5,341) scale(1,-0.733333) translate(0,-810)" width="15" x="817" y="330"/></g>
  <g id="38">
   <use class="kv10" height="30" transform="rotate(90,955.5,636) scale(1,-0.733333) translate(0,-1507.27)" width="15" x="948" xlink:href="#Disconnector:刀闸_0" y="625" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450286256133" ObjectName="勐昔线勐乃河一级电站支线#0+1杆A011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450286256133"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,955.5,636) scale(1,-0.733333) translate(0,-1507.27)" width="15" x="948" y="625"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="62">
   <use class="kv10" height="30" transform="rotate(90,1264,529) scale(1.85714,0.5) translate(-580.385,521.5)" width="7" x="1257.5" xlink:href="#ACLineSegment:线路_0" y="521.5" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249328254979" ObjectName="10kV勐昔线"/>
   <cge:TPSR_Ref TObjectID="8444249328254979_5066549581119490"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1264,529) scale(1.85714,0.5) translate(-580.385,521.5)" width="7" x="1257.5" y="521.5"/></g>
 </g>
</svg>