<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549585903618" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="1" x="10" xlink:href="#terminal" y="2.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="6.047799292079558" y2="8.646787392096492"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="11.24577549211341" y2="8.646787392096474"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="11.24577549211341" y2="8.646787392096474"/>
   <ellipse cx="10.03" cy="9.050000000000001" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_1" viewBox="0,0,20,30">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="22.74577549211341" y2="20.14678739209647"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="17.54779929207956" y2="20.14678739209649"/>
   <use terminal-index="1" type="1" x="10.08333333333333" xlink:href="#terminal" y="26.16666666666667"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="22.74577549211341" y2="20.14678739209647"/>
   <ellipse cx="10.03" cy="19.8" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV万马河施工电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.75" xlink:href="logo.png" y="45.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.375,75.25) scale(1,1) translate(0,0)" writing-mode="lr" x="178.37" xml:space="preserve" y="78.75" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.083,74.9403) scale(1,1) translate(6.85563e-15,0)" writing-mode="lr" x="180.08" xml:space="preserve" y="83.94" zvalue="3">10kV万马河施工电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="3" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,71.4375,367) scale(1,1) translate(0,0)" width="72.88" x="35" y="355" zvalue="76"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.4375,367) scale(1,1) translate(0,0)" writing-mode="lr" x="71.44" xml:space="preserve" y="371.5" zvalue="76">信号一览</text>
  <line fill="none" id="34" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.75" x2="377.75" y1="13.25" y2="1043.25" zvalue="4"/>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="149.1204926140824" y2="149.1204926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="161.2500000000001" y2="161.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="161.2500000000001" y2="161.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="279.7500000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="279.7500000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line fill="none" id="30" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="619.1204926140825" y2="619.1204926140825" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="1001.3316" y2="1029.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.75,954.25) scale(1,1) translate(0,0)" writing-mode="lr" x="48.75" xml:space="preserve" y="960.25" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="45.75" xml:space="preserve" y="994.25" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.75" xml:space="preserve" y="994.25" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="44.75" xml:space="preserve" y="1022.25" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="1022.25" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.25,648.75) scale(1,1) translate(0,2.09749e-13)" writing-mode="lr" x="69.25" xml:space="preserve" y="653.2500000000001" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.804,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="228.8" xml:space="preserve" y="962.25" zvalue="27">WanMaHeShiGong-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.804,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="138.8" xml:space="preserve" y="1022.25" zvalue="28">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.75,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="42.75" xml:space="preserve" y="180.75" zvalue="30">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.75,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="222.75" xml:space="preserve" y="180.75" zvalue="31">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.2917,197.583) scale(1,1) translate(0,0)" writing-mode="lr" x="61.29" xml:space="preserve" y="202.08" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.9375,247.25) scale(1,1) translate(0,0)" writing-mode="lr" x="49.94" xml:space="preserve" y="251.75" zvalue="33">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,542.714,692.714) scale(1,1) translate(0,0)" writing-mode="lr" x="542.71" xml:space="preserve" y="697.21" zvalue="39">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" x="1054.328125" xml:space="preserve" y="468.2321428571429" zvalue="40">#1主变          </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1054.328125" xml:space="preserve" y="484.2321428571429" zvalue="40">800KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.14,79.7143) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.14" xml:space="preserve" y="84.20999999999999" zvalue="42">10kV万马河施工电站线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" x="781.1796875" xml:space="preserve" y="1035.34151663099" zvalue="44">#1发电机          </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="781.1796875" xml:space="preserve" y="1051.34151663099" zvalue="44">400KW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" x="1239.5078125" xml:space="preserve" y="1028.823659488133" zvalue="47">#2发电机        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1239.5078125" xml:space="preserve" y="1044.823659488133" zvalue="47">200KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.43,332.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.43" xml:space="preserve" y="337.21" zvalue="49">071</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,973,247) scale(1,1) translate(0,0)" writing-mode="lr" x="973" xml:space="preserve" y="251.5" zvalue="52">0716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.857,788.429) scale(1,1) translate(0,0)" writing-mode="lr" x="750.86" xml:space="preserve" y="792.9299999999999" zvalue="55">4711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.86,785.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.86" xml:space="preserve" y="790.0700000000001" zvalue="59">4721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,810.016,869.143) scale(1,1) translate(0,0)" writing-mode="lr" x="810.02" xml:space="preserve" y="873.64" zvalue="63">471</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267,866.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1267" xml:space="preserve" y="870.79" zvalue="67">472</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.211,367.591) scale(1,1) translate(0,0)" writing-mode="lr" x="187.21" xml:space="preserve" y="372.09" zvalue="72">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,292.211,367.591) scale(1,1) translate(0,0)" writing-mode="lr" x="292.21" xml:space="preserve" y="372.09" zvalue="73">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV万马河施工电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="35" y="355" zvalue="76"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="37">
   <path class="v400" d="M 518.57 715.14 L 1707.14 715.14" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242134020" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242134020"/></metadata>
  <path d="M 518.57 715.14 L 1707.14 715.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="38">
   <g id="380">
    <use class="kv10" height="30" transform="rotate(0,1005.71,473.714) scale(2.14286,2.38095) translate(-524.952,-254.04)" width="20" x="984.29" xlink:href="#PowerTransformer2:Y-y不带中性点_0" y="438" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874434543620" ObjectName="10"/>
    </metadata>
   </g>
   <g id="381">
    <use class="v400" height="30" transform="rotate(0,1005.71,473.714) scale(2.14286,2.38095) translate(-524.952,-254.04)" width="20" x="984.29" xlink:href="#PowerTransformer2:Y-y不带中性点_1" y="438" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874434609156" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399449509892" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399449509892"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1005.71,473.714) scale(2.14286,2.38095) translate(-524.952,-254.04)" width="20" x="984.29" y="438"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="42">
   <use class="v400" height="30" transform="rotate(0,781.429,982.286) scale(2.14286,2.14286) translate(-399.619,-506.743)" width="30" x="749.2857142857144" xlink:href="#Generator:发电机_0" y="950.1428571428571" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449786216454" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449786216454"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,781.429,982.286) scale(2.14286,2.14286) translate(-399.619,-506.743)" width="30" x="749.2857142857144" y="950.1428571428571"/></g>
  <g id="44">
   <use class="v400" height="30" transform="rotate(0,1239.29,981.571) scale(2.14286,2.14286) translate(-643.81,-506.362)" width="30" x="1207.142857142857" xlink:href="#Generator:发电机_0" y="949.4285714285714" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449786281990" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449786281990"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1239.29,981.571) scale(2.14286,2.14286) translate(-643.81,-506.362)" width="30" x="1207.142857142857" y="949.4285714285714"/></g>
 </g>
 <g id="BreakerClass">
  <g id="46">
   <use class="kv10" height="20" transform="rotate(0,1005.71,333.714) scale(2.14286,1.92857) translate(-530.667,-151.392)" width="10" x="994.9999999999999" xlink:href="#Breaker:开关_0" y="314.4285714285716" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924507467781" ObjectName="10kV万马河施工电站线071断路器"/>
   <cge:TPSR_Ref TObjectID="6473924507467781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1005.71,333.714) scale(2.14286,1.92857) translate(-530.667,-151.392)" width="10" x="994.9999999999999" y="314.4285714285716"/></g>
  <g id="58">
   <use class="v400" height="20" transform="rotate(0,783.016,870.143) scale(2.14286,1.92857) translate(-411.894,-409.672)" width="10" x="772.3016634630083" xlink:href="#Breaker:开关_0" y="850.8571428571429" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924507533317" ObjectName="#1发电机471断路器"/>
   <cge:TPSR_Ref TObjectID="6473924507533317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,783.016,870.143) scale(2.14286,1.92857) translate(-411.894,-409.672)" width="10" x="772.3016634630083" y="850.8571428571429"/></g>
  <g id="61">
   <use class="v400" height="20" transform="rotate(0,1239.29,867.286) scale(2.14286,1.92857) translate(-655.238,-408.296)" width="10" x="1228.571428571428" xlink:href="#Breaker:开关_0" y="848" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924507598854" ObjectName="#2发电机472断路器"/>
   <cge:TPSR_Ref TObjectID="6473924507598854"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1239.29,867.286) scale(2.14286,1.92857) translate(-655.238,-408.296)" width="10" x="1228.571428571428" y="848"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="49">
   <use class="kv10" height="30" transform="rotate(0,1007.14,248) scale(1.42857,1.04762) translate(-298.929,-10.5584)" width="15" x="996.4285714285713" xlink:href="#Disconnector:刀闸_0" y="232.2857142857144" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449786347526" ObjectName="10kV万马河施工电站线0716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449786347526"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1007.14,248) scale(1.42857,1.04762) translate(-298.929,-10.5584)" width="15" x="996.4285714285713" y="232.2857142857144"/></g>
  <g id="52">
   <use class="v400" height="30" transform="rotate(0,782.857,789.429) scale(1.42857,1.04762) translate(-231.643,-35.1688)" width="15" x="772.1428571428571" xlink:href="#Disconnector:刀闸_0" y="773.7142857142856" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449786413062" ObjectName="#1发电机4711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449786413062"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,782.857,789.429) scale(1.42857,1.04762) translate(-231.643,-35.1688)" width="15" x="772.1428571428571" y="773.7142857142856"/></g>
  <g id="55">
   <use class="v400" height="30" transform="rotate(0,1237.86,786.571) scale(1.42857,1.04762) translate(-368.143,-35.039)" width="15" x="1227.142857142857" xlink:href="#Disconnector:刀闸_0" y="770.8571428571429" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449786478598" ObjectName="#2发电机4721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449786478598"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1237.86,786.571) scale(1.42857,1.04762) translate(-368.143,-35.039)" width="15" x="1227.142857142857" y="770.8571428571429"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="50">
   <path class="kv10" d="M 1007.14 118.61 L 1007.14 232.81" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.14 118.61 L 1007.14 232.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 1007.23 263.45 L 1007.23 315.26" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.23 263.45 L 1007.23 315.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="v400" d="M 782.98 774.23 L 782.98 715.14" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.98 774.23 L 782.98 715.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v400" d="M 1237.98 771.38 L 1237.98 715.14" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="37@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.98 771.38 L 1237.98 715.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="v400" d="M 781.43 950.68 L 781.43 888.56" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="58@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.43 950.68 L 781.43 888.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v400" d="M 782.94 851.69 L 782.94 804.87" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="52@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.94 851.69 L 782.94 804.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="v400" d="M 1239.29 949.96 L 1239.29 885.7" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.29 949.96 L 1239.29 885.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v400" d="M 1239.21 848.84 L 1239.21 802.02" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="55@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.21 848.84 L 1239.21 802.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv10" d="M 1005.86 352.13 L 1005.86 444.55" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.86 352.13 L 1005.86 444.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="v400" d="M 1005.89 500.3 L 1005.89 715.14" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="37@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.89 500.3 L 1005.89 715.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,319.485,368.107) scale(0.708333,0.665547) translate(127.178,179.966)" width="30" x="308.86" xlink:href="#State:红绿圆(方形)_0" y="358.12" zvalue="74"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,319.485,368.107) scale(0.708333,0.665547) translate(127.178,179.966)" width="30" x="308.86" y="358.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,223.86,368.107) scale(0.708333,0.665547) translate(87.8027,179.966)" width="30" x="213.24" xlink:href="#State:红绿圆(方形)_0" y="358.12" zvalue="75"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,223.86,368.107) scale(0.708333,0.665547) translate(87.8027,179.966)" width="30" x="213.24" y="358.12"/></g>
 </g>
</svg>