<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549596323842" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:5卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2019.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
   <ellipse cx="8.710000000000001" cy="0.47" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.10887049225937" x2="11.10887049225937" y1="1.654102266954478" y2="1.654102266954478"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.60887049225937" x2="11.60887049225937" y1="1.154102266954478" y2="1.154102266954478"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.289699242498646" x2="7.787306517404822" y1="0.309450744955857" y2="-2.343680756240978"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.289699242498656" x2="10.74248159896798" y1="0.3094507449558748" y2="1.438770861071294"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.339309611123124" x2="8.289699242498626" y1="1.833262130037291" y2="0.3094507449558748"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="3" y1="23" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="12" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.3333333333333357" y2="12.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="EnergyConsumer:炉变D-y型_0" viewBox="0,0,17,30">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="25.25" y2="30.08333333333334"/>
   <path d="M 8.5 5 L 5.5 11 L 11.5 11 L 8.5 5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="9.5" y1="30" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="7.5" y1="30" y2="28"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350779"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
  </symbol>
  <symbol id="EnergyConsumer:23230225_0" viewBox="0,0,45,41">
   <use terminal-index="0" type="0" x="22.65" xlink:href="#terminal" y="40.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34" x2="36" y1="31.75" y2="33.75"/>
   <path d="M 33.5 17.5 L 31.5 20.5 L 36.5 20.5 L 34.5 17.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 34 28.75 L 34 31.75 L 32 33.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 25.5833 25.4167 L 28.5833 25.4167 L 29.5833 23.4167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.5" x2="30.5" y1="25.5" y2="27.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="7.5" y1="22.66666666666667" y2="16.66666666666667"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,8.54,21.83) scale(1,1) translate(0,0)" width="6.08" x="5.5" y="14.67"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="9" y1="33.91666666666666" y2="33.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="11" y1="31.91666666666666" y2="31.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="22.66666666666667" y2="9.999999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="10" y1="32.91666666666666" y2="32.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.66022336769755" x2="22.66022336769755" y1="40.83333333333334" y2="0.4166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="34.25545055364641" x2="34.25545055364641" y1="10.04138864447597" y2="16.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="9.5" y1="22.66666666666667" y2="16.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="34.25" x2="8.416666666666663" y1="10.00875283090554" y2="10.00875283090554"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="28.91666666666666" y2="31.91666666666666"/>
   <ellipse cx="34.18" cy="20.99" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="29.51" cy="25.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="37.82589728904158" x2="37.82589728904158" y1="9.997588122517026" y2="9.997588122517026"/>
   <ellipse cx="38.78" cy="25.94" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.5" x2="42.5" y1="25.75" y2="27.75"/>
   <path d="M 37.5833 25.6667 L 40.5833 25.6667 L 41.5833 23.6667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="34.18" cy="30.49" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV勐嘎临时变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="308" zvalue="1589"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="381" zvalue="1590"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="343.5" zvalue="1592"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="140" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,320) scale(1,1) translate(0,0)" width="72.88" x="38" y="308" zvalue="1589"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,320) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="324.5" zvalue="1589">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="139" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,393) scale(1,1) translate(0,0)" width="72.88" x="38" y="381" zvalue="1590"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,393) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="397.5" zvalue="1590">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="137" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,355.5) scale(1,1) translate(0,0)" width="72.88" x="38" y="343.5" zvalue="1592"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,355.5) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="360" zvalue="1592">信号一览</text>
  <rect fill="none" fill-opacity="0" height="355.82" id="7" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1158,275.909) scale(1,1) translate(0,0)" width="272" x="1022" y="98" zvalue="1"/>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="59"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1057"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="254.75" y2="277.5"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1059"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1063">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1064">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1065">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1066">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1067">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1070">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="598" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.333,458.5) scale(1,1) translate(0,0)" writing-mode="lr" x="327.3333333333335" xml:space="preserve" y="463" zvalue="1074">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,489.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="494" zvalue="1076">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,515) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="519.5" zvalue="1077">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,540.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="545" zvalue="1078">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84,565) scale(1,1) translate(0,0)" writing-mode="lr" x="84" xml:space="preserve" y="569.5" zvalue="1079">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,591.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="596" zvalue="1080">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="233.05" xml:space="preserve" y="960" zvalue="1081">DHDDBH-MengGa-01-2024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.054,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="143.05" xml:space="preserve" y="1020" zvalue="1082">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="1020" zvalue="1083">20240802</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,173) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="177.5" zvalue="1084">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,173) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="177.5" zvalue="1085">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="200.75" zvalue="1086">35kV侧频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.125,197.25) scale(1,1) translate(0,0)" writing-mode="lr" x="234.13" xml:space="preserve" y="201.75" zvalue="1087">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1875,245) scale(1,1) translate(0,0)" writing-mode="lr" x="54.19" xml:space="preserve" y="249.5" zvalue="1088">临时变油温</text>
  <image height="81" id="4" preserveAspectRatio="xMidYMid slice" width="266" x="53" xlink:href="logo.png" y="38"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186,78.5) scale(1,1) translate(0,0)" writing-mode="lr" x="186" xml:space="preserve" y="83" zvalue="1440"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.961,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="190.96" xml:space="preserve" y="325.09" zvalue="1584">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,295.961,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="295.96" xml:space="preserve" y="325.09" zvalue="1585">通道</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="19" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,215.5,76.25) scale(1,1) translate(0,0)" writing-mode="lr" x="215.5" xml:space="preserve" y="82.75" zvalue="1711">35kV勐嘎临时变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1875,266.778) scale(1,1) translate(0,0)" writing-mode="lr" x="54.19" xml:space="preserve" y="271.28" zvalue="1832">临时变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.13,378.038) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.13" xml:space="preserve" y="382.54" zvalue="1898">35kV1号临时主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.961,530.957) scale(1,1) translate(0,0)" writing-mode="lr" x="869.96" xml:space="preserve" y="535.46" zvalue="1899">10kV临时母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1155.89,525.597) scale(1,1) translate(0,0)" writing-mode="lr" x="1155.89" xml:space="preserve" y="530.1" zvalue="1901">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,907.296,604.75) scale(1,1) translate(1.97529e-13,0)" writing-mode="lr" x="907.3" xml:space="preserve" y="609.25" zvalue="1904">042</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,941.167,669.667) scale(1,1) translate(0,7.28699e-14)" writing-mode="lr" x="941.17" xml:space="preserve" y="674.17" zvalue="1917">04267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1062.83,604.75) scale(1,1) translate(-4.64128e-13,2.6265e-13)" writing-mode="lr" x="1062.83" xml:space="preserve" y="609.25" zvalue="1919">043</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.1,908.047) scale(1,1) translate(0,-1.98973e-13)" writing-mode="lr" x="1035.1" xml:space="preserve" y="912.55" zvalue="1921">10kV勐稳线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1060.09,824.612) scale(1,1) translate(2.30962e-13,1.17094e-12)" writing-mode="lr" x="1060.09" xml:space="preserve" y="829.11" zvalue="1924">0336</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108.48,669.667) scale(1,1) translate(0,-4.37219e-13)" writing-mode="lr" x="1108.48" xml:space="preserve" y="674.17" zvalue="1934">04367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1221.73,604.75) scale(1,1) translate(-1.06939e-12,2.6265e-13)" writing-mode="lr" x="1221.73" xml:space="preserve" y="609.25" zvalue="1937">046</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1194,908.047) scale(1,1) translate(0,-1.98973e-13)" writing-mode="lr" x="1194" xml:space="preserve" y="912.55" zvalue="1939">10kV勐戛线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1221.28,823.612) scale(1,1) translate(2.66753e-13,1.1695e-12)" writing-mode="lr" x="1221.28" xml:space="preserve" y="828.11" zvalue="1942">0366</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1265.09,669.667) scale(1,1) translate(0,-4.37219e-13)" writing-mode="lr" x="1265.09" xml:space="preserve" y="674.17" zvalue="1952">04667</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375.7,604.75) scale(1,1) translate(-1.20615e-12,2.6265e-13)" writing-mode="lr" x="1375.7" xml:space="preserve" y="609.25" zvalue="1955">047</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1347.98,908.047) scale(1,1) translate(1.44735e-13,-1.98973e-13)" writing-mode="lr" x="1347.98" xml:space="preserve" y="912.55" zvalue="1957">10kV三仙洞线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1373.26,823.612) scale(1,1) translate(3.00498e-13,1.1695e-12)" writing-mode="lr" x="1373.26" xml:space="preserve" y="828.11" zvalue="1960">0376</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1420.78,669.667) scale(1,1) translate(1.24163e-12,-4.37219e-13)" writing-mode="lr" x="1420.78" xml:space="preserve" y="674.17" zvalue="1970">04767</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1149.58,317.378) scale(1,1) translate(0,0)" writing-mode="lr" x="1149.58" xml:space="preserve" y="321.88" zvalue="1973">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1155.59,260.069) scale(1,1) translate(0,0)" writing-mode="lr" x="1155.59" xml:space="preserve" y="264.57" zvalue="1975">3016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1521.79,604.75) scale(1,1) translate(-1.66987e-12,2.6265e-13)" writing-mode="lr" x="1521.79" xml:space="preserve" y="609.25" zvalue="1979">0411</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1489.62,719.292) scale(1,1) translate(0,0)" writing-mode="lr" x="1489.619919422937" xml:space="preserve" y="724.7923631961075" zvalue="1982">10kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.33,78.1782) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.33" xml:space="preserve" y="82.68000000000001" zvalue="1986">35kV帕嘎线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" x="1601.1640625" xml:space="preserve" y="721.6521966851662" zvalue="1996">10kV临时母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1601.1640625" xml:space="preserve" y="737.6521966851662" zvalue="1996">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1630.82,604.75) scale(1,1) translate(2.32819e-12,0)" writing-mode="lr" x="1630.82" xml:space="preserve" y="609.25" zvalue="1998">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1214.92,403.667) scale(1,1) translate(-2.57461e-13,-8.74116e-14)" writing-mode="lr" x="1214.92" xml:space="preserve" y="408.17" zvalue="2012">SZ11-8000/35</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1082,309) scale(1,1) translate(0,0)" writing-mode="lr" x="1082" xml:space="preserve" y="313.5" zvalue="2016">30160</text>
  <rect fill="none" fill-opacity="0" height="235.36" id="28" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1202.91,890.682) scale(1,1) translate(0,-1.7164e-13)" width="1023.64" x="691.09" y="773" zvalue="2021"/>
  <rect fill="none" fill-opacity="0" height="284.55" id="27" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1202.91,612.455) scale(1,1) translate(0,0)" width="1021.82" x="692" y="470.18" zvalue="2022"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,923.5,249.5) scale(1,1) translate(0,0)" writing-mode="lr" x="923.5" xml:space="preserve" y="255.5" zvalue="2033">35kV1号变电车</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,583.409,625.136) scale(1,1) translate(0,-1.3137e-13)" writing-mode="lr" x="583.41" xml:space="preserve" y="631.14" zvalue="2035">10kV2号配电车</text>
  <rect fill="none" height="41.82" id="29" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,583,869.273) scale(1,1) translate(-2.23982e-13,0)" width="157.27" x="504.36" y="848.36" zvalue="2037"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,583,869.273) scale(1,1) translate(-2.23982e-13,0)" writing-mode="lr" x="583" xml:space="preserve" y="875.27" zvalue="2037">35kV勐嘎变(老站)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876.102,908.047) scale(1,1) translate(0,-1.98973e-13)" writing-mode="lr" x="876.1" xml:space="preserve" y="912.55" zvalue="2121">10kV中山线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.094,824.612) scale(1,1) translate(1.97086e-13,0)" writing-mode="lr" x="901.09" xml:space="preserve" y="829.11" zvalue="2123">0326</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139.333,459.5) scale(1,1) translate(0,0)" writing-mode="lr" x="139.3333333333335" xml:space="preserve" y="464" zvalue="2132">35kV电压</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.222,172.167) scale(1,1) translate(0,0)" writing-mode="lr" x="146.42" xml:space="preserve" y="177.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127830159364" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,325.222,173.167) scale(1,1) translate(0,0)" writing-mode="lr" x="325.42" xml:space="preserve" y="178.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127830224900" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145,196.5) scale(1,1) translate(0,0)" writing-mode="lr" x="145.2" xml:space="preserve" y="201.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128387805188" ObjectName="F"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,325,197.5) scale(1,1) translate(0,0)" writing-mode="lr" x="325.2" xml:space="preserve" y="202.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127820984324" ObjectName="F"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145,242.5) scale(1,1) translate(0,0)" writing-mode="lr" x="145.2" xml:space="preserve" y="247.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128387149828" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,329.222,489.667) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="494.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127820853252" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,329.222,513.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="518.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127820591108" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="152" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,329.222,537.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="542.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127820656644" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,329.222,561.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="566.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127820722180" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,329.222,586.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="591.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127821049860" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,143.757,265.389) scale(1,1) translate(0,0)" writing-mode="lr" x="143.95" xml:space="preserve" y="270.3" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128387215364" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1214.61,141.251) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.82" xml:space="preserve" y="145.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128386691076" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="30" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1214.61,161.751) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.82" xml:space="preserve" y="166.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128386756612" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="33" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1055.22,492.036) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.43" xml:space="preserve" y="496.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128386822148" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="34" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1055.22,513.036) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.43" xml:space="preserve" y="517.45" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128386887684" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="35" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1214.61,182.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.82" xml:space="preserve" y="186.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128386953220" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="36" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1055.22,534.036) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.43" xml:space="preserve" y="538.45" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128387280900" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="37" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1607.33,486.356) scale(1,1) translate(0,0)" writing-mode="lr" x="1556.54" xml:space="preserve" y="490.77" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127820591108" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1607.33,503.356) scale(1,1) translate(0,0)" writing-mode="lr" x="1556.54" xml:space="preserve" y="507.77" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127820656644" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="39" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1607.33,520.356) scale(1,1) translate(0,0)" writing-mode="lr" x="1556.54" xml:space="preserve" y="524.77" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127820722180" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="40" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1607.33,535.856) scale(1,1) translate(0,0)" writing-mode="lr" x="1556.54" xml:space="preserve" y="540.27" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127820787716" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="70" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1351.26,933.311) scale(1,1) translate(0,2.0537e-13)" writing-mode="lr" x="1313.64" xml:space="preserve" y="937.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128380399620" ObjectName="P"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="71" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1196.29,933.311) scale(1,1) translate(3.85369e-13,2.0537e-13)" writing-mode="lr" x="1158.67" xml:space="preserve" y="937.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128381841412" ObjectName="P"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="76" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1352.26,955.811) scale(1,1) translate(0,2.10366e-13)" writing-mode="lr" x="1314.64" xml:space="preserve" y="960.24" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128380465156" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="77" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1196.29,955.811) scale(1,1) translate(3.85369e-13,2.10366e-13)" writing-mode="lr" x="1158.67" xml:space="preserve" y="960.24" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128381906948" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="82" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1355.26,978.311) scale(1,1) translate(0,2.15362e-13)" writing-mode="lr" x="1317.64" xml:space="preserve" y="982.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128380530692" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="83" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1196.29,978.311) scale(1,1) translate(-5.13825e-13,2.15362e-13)" writing-mode="lr" x="1158.67" xml:space="preserve" y="982.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128381972484" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="41" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,882.387,933.311) scale(1,1) translate(0,2.0537e-13)" writing-mode="lr" x="844.76" xml:space="preserve" y="937.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128384724996" ObjectName="P"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="42" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1041.39,933.311) scale(1,1) translate(2.22517e-13,2.0537e-13)" writing-mode="lr" x="1003.76" xml:space="preserve" y="937.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128383283204" ObjectName="P"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="43" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,882.387,955.811) scale(1,1) translate(0,2.10366e-13)" writing-mode="lr" x="844.76" xml:space="preserve" y="960.24" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128384790532" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="47" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1041.39,955.811) scale(1,1) translate(2.22517e-13,2.10366e-13)" writing-mode="lr" x="1003.76" xml:space="preserve" y="960.24" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128383348740" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="48" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,882.387,978.311) scale(1,1) translate(0,2.15362e-13)" writing-mode="lr" x="844.76" xml:space="preserve" y="982.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128384856068" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="49" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1041.39,978.311) scale(1,1) translate(2.22517e-13,2.15362e-13)" writing-mode="lr" x="1003.76" xml:space="preserve" y="982.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128383414276" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,141.222,490.667) scale(1,1) translate(-2.53871e-14,0)" writing-mode="lr" x="141.34" xml:space="preserve" y="495.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128387739652" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,141.222,514.5) scale(1,1) translate(-2.53871e-14,0)" writing-mode="lr" x="141.34" xml:space="preserve" y="519.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128388132868" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="56" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,141.222,538.5) scale(1,1) translate(-2.53871e-14,0)" writing-mode="lr" x="141.34" xml:space="preserve" y="543.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128388198404" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,141.222,562.5) scale(1,1) translate(-2.53871e-14,0)" writing-mode="lr" x="141.34" xml:space="preserve" y="567.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128388263940" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="50" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1234,353.249) scale(1,1) translate(0,0)" writing-mode="lr" x="1183.21" xml:space="preserve" y="357.67" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128387739652" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,329.188,321.25) scale(0.708333,0.665547) translate(131.173,156.419)" width="30" x="318.56" xlink:href="#State:红绿圆(方形)_0" y="311.27" zvalue="1586"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374896791555" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.188,321.25) scale(0.708333,0.665547) translate(131.173,156.419)" width="30" x="318.56" y="311.27"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,233.562,321.25) scale(0.708333,0.665547) translate(91.7978,156.419)" width="30" x="222.94" xlink:href="#State:红绿圆(方形)_0" y="311.27" zvalue="1587"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562961525571586" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,233.562,321.25) scale(0.708333,0.665547) translate(91.7978,156.419)" width="30" x="222.94" y="311.27"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="295">
   <g id="2950">
    <use class="kv35" height="30" transform="rotate(0,1126.22,396.519) scale(3.00114,2.93445) translate(-726.943,-232.377)" width="24" x="1090.21" xlink:href="#PowerTransformer2:可调不带中性点_0" y="352.5" zvalue="1896"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874468556803" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2951">
    <use class="kv10" height="30" transform="rotate(0,1126.22,396.519) scale(3.00114,2.93445) translate(-726.943,-232.377)" width="24" x="1090.21" xlink:href="#PowerTransformer2:可调不带中性点_1" y="352.5" zvalue="1896"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874468622339" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399465697283" ObjectName="35kV1号临时主变"/>
   <cge:TPSR_Ref TObjectID="6755399465697283"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1126.22,396.519) scale(3.00114,2.93445) translate(-726.943,-232.377)" width="24" x="1090.21" y="352.5"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="294">
   <path class="kv10" d="M 792.33 558.86 L 1667.73 558.86" stroke-width="6" zvalue="1897"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674261991428" ObjectName="10kV临时母线"/>
   <cge:TPSR_Ref TObjectID="9288674261991428"/></metadata>
  <path d="M 792.33 558.86 L 1667.73 558.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="293">
   <use class="kv10" height="20" transform="rotate(0,1126.22,524.544) scale(1.80664,1.80664) translate(-498.808,-226.135)" width="10" x="1117.187605352861" xlink:href="#Breaker:小车断路器_0" y="506.4776976914955" zvalue="1900"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924603084804" ObjectName="35kV1号临时主变变低侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924603084804"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1126.22,524.544) scale(1.80664,1.80664) translate(-498.808,-226.135)" width="10" x="1117.187605352861" y="506.4776976914955"/></g>
  <g id="291">
   <use class="kv10" height="20" transform="rotate(0,876.81,603.697) scale(1.80664,1.80664) translate(-387.45,-261.476)" width="10" x="867.7769269849678" xlink:href="#Breaker:小车断路器_0" y="585.630791729774" zvalue="1903"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924603019268" ObjectName="10kV中山线042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924603019268"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,876.81,603.697) scale(1.80664,1.80664) translate(-387.45,-261.476)" width="10" x="867.7769269849678" y="585.630791729774"/></g>
  <g id="280">
   <use class="kv10" height="20" transform="rotate(0,1035.73,603.697) scale(1.80664,1.80664) translate(-458.404,-261.476)" width="10" x="1026.694636888398" xlink:href="#Breaker:小车断路器_0" y="585.630791729774" zvalue="1918"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924602953732" ObjectName="10kV勐稳线043断路器"/>
   <cge:TPSR_Ref TObjectID="6473924602953732"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1035.73,603.697) scale(1.80664,1.80664) translate(-458.404,-261.476)" width="10" x="1026.694636888398" y="585.630791729774"/></g>
  <g id="266">
   <use class="kv10" height="20" transform="rotate(0,1194.63,603.697) scale(1.80664,1.80664) translate(-529.352,-261.476)" width="10" x="1185.596652171343" xlink:href="#Breaker:小车断路器_0" y="585.630791729774" zvalue="1936"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924602888196" ObjectName="10kV勐戛线046断路器"/>
   <cge:TPSR_Ref TObjectID="6473924602888196"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1194.63,603.697) scale(1.80664,1.80664) translate(-529.352,-261.476)" width="10" x="1185.596652171343" y="585.630791729774"/></g>
  <g id="252">
   <use class="kv10" height="20" transform="rotate(0,1348.6,603.697) scale(1.80664,1.80664) translate(-598.099,-261.476)" width="10" x="1339.571473181948" xlink:href="#Breaker:小车断路器_0" y="585.6307907104492" zvalue="1954"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924602822660" ObjectName="10kV三仙洞线047断路器"/>
   <cge:TPSR_Ref TObjectID="6473924602822660"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1348.6,603.697) scale(1.80664,1.80664) translate(-598.099,-261.476)" width="10" x="1339.571473181948" y="585.6307907104492"/></g>
  <g id="236">
   <use class="kv35" height="20" transform="rotate(0,1126.17,318.609) scale(1.2318,1.10862) translate(-210.763,-30.1301)" width="10" x="1120.012616411961" xlink:href="#Breaker:开关_0" y="307.523212722013" zvalue="1972"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924602757124" ObjectName="35kV1号临时主变变高侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924602757124"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1126.17,318.609) scale(1.2318,1.10862) translate(-210.763,-30.1301)" width="10" x="1120.012616411961" y="307.523212722013"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="292">
   <path class="kv10" d="M 1126.22 540.8 L 1126.22 558.86" stroke-width="1" zvalue="1902"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@1" LinkObjectIDznd="294@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.22 540.8 L 1126.22 558.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv10" d="M 876.81 586.99 L 876.81 558.86" stroke-width="1" zvalue="1907"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@0" LinkObjectIDznd="294@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 876.81 586.99 L 876.81 558.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv10" d="M 1035.73 586.99 L 1035.73 558.86" stroke-width="1" zvalue="1922"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="294@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.73 586.99 L 1035.73 558.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="kv10" d="M 1035.7 851.44 L 1035.7 836.23" stroke-width="1" zvalue="1925"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="277@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.7 851.44 L 1035.7 836.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv10" d="M 1194.63 586.99 L 1194.63 558.86" stroke-width="1" zvalue="1940"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="294@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1194.63 586.99 L 1194.63 558.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv10" d="M 1194.6 851.44 L 1194.6 836.23" stroke-width="1" zvalue="1943"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="263@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1194.6 851.44 L 1194.6 836.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 1348.6 586.99 L 1348.6 558.86" stroke-width="1" zvalue="1958"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="294@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.6 586.99 L 1348.6 558.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv10" d="M 1348.58 851.44 L 1348.58 836.23" stroke-width="1" zvalue="1961"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="249@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.58 851.44 L 1348.58 836.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv35" d="M 1126.1 273.72 L 1126.13 308" stroke-width="1" zvalue="1977"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@1" LinkObjectIDznd="236@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.1 273.72 L 1126.13 308" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv10" d="M 1488.52 585.85 L 1488.52 558.86" stroke-width="1" zvalue="1981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="294@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1488.52 585.85 L 1488.52 558.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv10" d="M 1488.53 621.22 L 1488.53 641.81" stroke-width="1" zvalue="1983"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1488.53 621.22 L 1488.53 641.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv35" d="M 1126.38 213.64 L 1126.38 244.48" stroke-width="1" zvalue="1987"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@0" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.38 213.64 L 1126.38 244.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 1596.6 585.74 L 1596.6 558.86" stroke-width="1" zvalue="1997"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@1" LinkObjectIDznd="294@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1596.6 585.74 L 1596.6 558.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 1596.72 621.72 L 1596.67 630.49" stroke-width="1" zvalue="1999"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1596.72 621.72 L 1596.67 630.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv35" d="M 1126.25 355.66 L 1126.25 329.2" stroke-width="1" zvalue="2001"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="295@0" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.25 355.66 L 1126.25 329.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 1126.22 507.83 L 1126.22 437.74" stroke-width="1" zvalue="2011"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@0" LinkObjectIDznd="295@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.22 507.83 L 1126.22 437.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv10" d="M 1147.74 487.8 L 1126.22 487.8" stroke-width="1" zvalue="2013"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.74 487.8 L 1126.22 487.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 1097.63 287.43 L 1126.12 287.43" stroke-width="1" zvalue="2016"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="232" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.63 287.43 L 1126.12 287.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv10" d="M 854.46 663.55 L 854.46 638.41 L 876.8 638.41" stroke-width="1" zvalue="2017"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.46 663.55 L 854.46 638.41 L 876.8 638.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv10" d="M 911.17 656.56 L 911.17 639.38 L 876.8 639.38" stroke-width="1" zvalue="2018"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.17 656.56 L 911.17 639.38 L 876.8 639.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 1035.73 806.98 L 1035.73 619.96" stroke-width="1" zvalue="2019"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@0" LinkObjectIDznd="280@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.73 806.98 L 1035.73 619.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv10" d="M 1194.63 806.98 L 1194.63 619.96" stroke-width="1" zvalue="2020"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="266@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1194.63 806.98 L 1194.63 619.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 1348.6 806.98 L 1348.6 619.96" stroke-width="1" zvalue="2023"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="252@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.6 806.98 L 1348.6 619.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 1035.64 654.29 L 1035.73 654.29" stroke-width="1" zvalue="2024"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.64 654.29 L 1035.73 654.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 1194.54 654.29 L 1194.63 654.29" stroke-width="1" zvalue="2025"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="14" MaxPinNum="2"/>
   </metadata>
  <path d="M 1194.54 654.29 L 1194.63 654.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 1348.52 654.29 L 1348.6 654.29" stroke-width="1" zvalue="2026"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.52 654.29 L 1348.6 654.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 1010.06 663.55 L 1010.06 635.43 L 1035.73 635.43" stroke-width="1" zvalue="2027"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@0" LinkObjectIDznd="13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1010.06 663.55 L 1010.06 635.43 L 1035.73 635.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 1075.95 656.56 L 1075.95 634.71 L 1035.73 634.71" stroke-width="1" zvalue="2028"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.95 656.56 L 1075.95 634.71 L 1035.73 634.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv10" d="M 1164.97 663.55 L 1164.97 638.29 L 1194.63 638.29" stroke-width="1" zvalue="2029"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="14" MaxPinNum="2"/>
   </metadata>
  <path d="M 1164.97 663.55 L 1164.97 638.29 L 1194.63 638.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 1234.86 656.56 L 1234.86 639 L 1194.63 639" stroke-width="1" zvalue="2030"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="14" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.86 656.56 L 1234.86 639 L 1194.63 639" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 1318.94 663.55 L 1318.94 638.29 L 1348.6 638.29" stroke-width="1" zvalue="2031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 1318.94 663.55 L 1318.94 638.29 L 1348.6 638.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv10" d="M 1388.83 656.56 L 1388.83 638.29 L 1348.6 638.29" stroke-width="1" zvalue="2032"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@0" LinkObjectIDznd="23" MaxPinNum="2"/>
   </metadata>
  <path d="M 1388.83 656.56 L 1388.83 638.29 L 1348.6 638.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv35" d="M 1126.14 223 L 1126.17 130.52" stroke-width="1" zvalue="2039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225" LinkObjectIDznd="221@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.14 223 L 1126.17 130.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv35" d="M 1126.17 130.52 L 1126.17 61" stroke-width="1" zvalue="2040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.17 130.52 L 1126.17 61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 876.74 654.29 L 876.8 654.29" stroke-width="1" zvalue="2108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 876.74 654.29 L 876.8 654.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv10" d="M 876.73 806.98 L 876.81 619.96" stroke-width="1" zvalue="2111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="291@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 876.73 806.98 L 876.81 619.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 876.7 851.44 L 876.7 836.23" stroke-width="1" zvalue="2124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="54@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 876.7 851.44 L 876.7 836.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="285">
   <use class="kv10" height="22" transform="rotate(0,876.738,662.506) scale(0.821199,0.821199) translate(189.821,142.282)" width="12" x="871.8108397815437" xlink:href="#Accessory:传输线_0" y="653.472326816287" zvalue="1912"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388557829" ObjectName="10kV中山线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,876.738,662.506) scale(0.821199,0.821199) translate(189.821,142.282)" width="12" x="871.8108397815437" y="653.472326816287"/></g>
  <g id="282">
   <use class="kv10" height="20" transform="rotate(0,854.457,672.889) scale(1.0171,1.06756) translate(-14.1972,-41.9071)" width="20" x="844.2857142857143" xlink:href="#Accessory:线路PT3_0" y="662.2129669189453" zvalue="1915"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388492293" ObjectName="10kV中山线避雷器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,854.457,672.889) scale(1.0171,1.06756) translate(-14.1972,-41.9071)" width="20" x="844.2857142857143" y="662.2129669189453"/></g>
  <g id="274">
   <use class="kv10" height="22" transform="rotate(0,1035.64,662.506) scale(0.821199,0.821199) translate(224.419,142.282)" width="12" x="1030.712855064487" xlink:href="#Accessory:传输线_0" y="653.472326816287" zvalue="1927"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388164613" ObjectName="10kV勐稳线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1035.64,662.506) scale(0.821199,0.821199) translate(224.419,142.282)" width="12" x="1030.712855064487" y="653.472326816287"/></g>
  <g id="270">
   <use class="kv10" height="20" transform="rotate(0,1010.06,672.889) scale(1.0171,1.06756) translate(-16.8139,-41.9071)" width="20" x="999.8924696688249" xlink:href="#Accessory:线路PT3_0" y="662.2129661951924" zvalue="1931"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388099077" ObjectName="10kV勐稳线避雷器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1010.06,672.889) scale(1.0171,1.06756) translate(-16.8139,-41.9071)" width="20" x="999.8924696688249" y="662.2129661951924"/></g>
  <g id="260">
   <use class="kv10" height="22" transform="rotate(0,1194.54,662.506) scale(0.821199,0.821199) translate(259.017,142.282)" width="12" x="1189.614870347432" xlink:href="#Accessory:传输线_0" y="653.472326816287" zvalue="1945"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387771397" ObjectName="10kV勐戛线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1194.54,662.506) scale(0.821199,0.821199) translate(259.017,142.282)" width="12" x="1189.614870347432" y="653.472326816287"/></g>
  <g id="256">
   <use class="kv10" height="20" transform="rotate(0,1164.97,672.889) scale(1.0171,1.06756) translate(-19.4186,-41.9071)" width="20" x="1154.79448495177" xlink:href="#Accessory:线路PT3_0" y="662.2129661951924" zvalue="1949"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387705861" ObjectName="10kV勐戛线避雷器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1164.97,672.889) scale(1.0171,1.06756) translate(-19.4186,-41.9071)" width="20" x="1154.79448495177" y="662.2129661951924"/></g>
  <g id="246">
   <use class="kv10" height="22" transform="rotate(0,1348.52,662.506) scale(0.821199,0.821199) translate(292.542,142.282)" width="12" x="1343.589691358037" xlink:href="#Accessory:传输线_0" y="653.472326816287" zvalue="1963"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387378181" ObjectName="10kV三仙洞线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1348.52,662.506) scale(0.821199,0.821199) translate(292.542,142.282)" width="12" x="1343.589691358037" y="653.472326816287"/></g>
  <g id="240">
   <use class="kv10" height="20" transform="rotate(0,1318.94,672.889) scale(1.0171,1.06756) translate(-22.0078,-41.9071)" width="20" x="1308.769305962375" xlink:href="#Accessory:线路PT3_0" y="662.2129661951924" zvalue="1967"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387312645" ObjectName="10kV三仙洞线避雷器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1318.94,672.889) scale(1.0171,1.06756) translate(-22.0078,-41.9071)" width="20" x="1308.769305962375" y="662.2129661951924"/></g>
  <g id="221">
   <use class="kv35" height="22" transform="rotate(0,1126.17,140.245) scale(0.972129,0.972227) translate(32.1198,3.70083)" width="12" x="1120.334448897824" xlink:href="#Accessory:传输线_0" y="129.5510046084551" zvalue="1991"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388754437" ObjectName="35kV帕嘎线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1126.17,140.245) scale(0.972129,0.972227) translate(32.1198,3.70083)" width="12" x="1120.334448897824" y="129.5510046084551"/></g>
  <g id="220">
   <use class="kv10" height="20" transform="rotate(270,1157.09,487.802) scale(1.0171,1.06756) translate(-19.2861,-30.1942)" width="20" x="1146.914917417696" xlink:href="#Accessory:线路PT3_0" y="477.1265225113578" zvalue="1992"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450386853893" ObjectName="变低侧避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1157.09,487.802) scale(1.0171,1.06756) translate(-19.2861,-30.1942)" width="20" x="1146.914917417696" y="477.1265225113578"/></g>
  <g id="218">
   <use class="kv10" height="42" transform="rotate(0,1610.48,660.045) scale(1.45078,-1.45078) translate(-493.643,-1105.54)" width="30" x="1588.717606406923" xlink:href="#Accessory:5卷PT带容断器_0" y="629.5783220432364" zvalue="1994"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450252898309" ObjectName="10kV临时母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1610.48,660.045) scale(1.45078,-1.45078) translate(-493.643,-1105.54)" width="30" x="1588.717606406923" y="629.5783220432364"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="281">
   <use class="kv10" height="20" transform="rotate(0,911.1,671.185) scale(1.5,1.5) translate(-301.2,-218.728)" width="10" x="903.5997582608903" xlink:href="#GroundDisconnector:地刀_0" y="656.1854553222656" zvalue="1916"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388426757" ObjectName="10kV中山线04267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450388426757"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,911.1,671.185) scale(1.5,1.5) translate(-301.2,-218.728)" width="10" x="903.5997582608903" y="656.1854553222656"/></g>
  <g id="268">
   <use class="kv10" height="20" transform="rotate(0,1075.88,671.185) scale(1.5,1.5) translate(-356.126,-218.728)" width="10" x="1068.378802560931" xlink:href="#GroundDisconnector:地刀_0" y="656.1854550303087" zvalue="1933"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388033541" ObjectName="10kV勐稳线04367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450388033541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1075.88,671.185) scale(1.5,1.5) translate(-356.126,-218.728)" width="10" x="1068.378802560931" y="656.1854550303087"/></g>
  <g id="254">
   <use class="kv10" height="20" transform="rotate(0,1234.78,671.185) scale(1.5,1.5) translate(-409.094,-218.728)" width="10" x="1227.280817843876" xlink:href="#GroundDisconnector:地刀_0" y="656.1854550303087" zvalue="1951"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387640325" ObjectName="10kV勐戛线04667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450387640325"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1234.78,671.185) scale(1.5,1.5) translate(-409.094,-218.728)" width="10" x="1227.280817843876" y="656.1854550303087"/></g>
  <g id="238">
   <use class="kv10" height="20" transform="rotate(0,1388.76,671.185) scale(1.5,1.5) translate(-460.419,-218.728)" width="10" x="1381.255638854481" xlink:href="#GroundDisconnector:地刀_0" y="656.1854550303087" zvalue="1969"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387247109" ObjectName="10kV三仙洞线04767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450387247109"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1388.76,671.185) scale(1.5,1.5) translate(-460.419,-218.728)" width="10" x="1381.255638854481" y="656.1854550303087"/></g>
  <g id="8">
   <use class="kv35" height="20" transform="rotate(90,1083,287.5) scale(-1.5,1.5) translate(-1802.5,-90.8333)" width="10" x="1075.5" xlink:href="#GroundDisconnector:地刀_0" y="272.5" zvalue="2015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388885509" ObjectName="35kV1号临时主变变高侧30160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450388885509"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1083,287.5) scale(-1.5,1.5) translate(-1802.5,-90.8333)" width="10" x="1075.5" y="272.5"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="279">
   <use class="kv10" height="30" transform="rotate(0,1041.39,871.308) scale(2.27417,-1.49981) translate(-573.911,-1444.76)" width="15" x="1024.330350122758" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="848.810791015625" zvalue="1920"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388295685" ObjectName="10kV勐稳线"/>
   <cge:TPSR_Ref TObjectID="6192450388295685"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1041.39,871.308) scale(2.27417,-1.49981) translate(-573.911,-1444.76)" width="15" x="1024.330350122758" y="848.810791015625"/></g>
  <g id="265">
   <use class="kv10" height="30" transform="rotate(0,1200.29,871.308) scale(2.27417,-1.49981) translate(-662.941,-1444.76)" width="15" x="1183.232365405703" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="848.810791015625" zvalue="1938"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387902469" ObjectName="10kV勐戛线"/>
   <cge:TPSR_Ref TObjectID="6192450387902469"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1200.29,871.308) scale(2.27417,-1.49981) translate(-662.941,-1444.76)" width="15" x="1183.232365405703" y="848.810791015625"/></g>
  <g id="251">
   <use class="kv10" height="30" transform="rotate(0,1354.26,871.308) scale(2.27417,-1.49981) translate(-749.21,-1444.76)" width="15" x="1337.207186416308" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="848.810791015625" zvalue="1956"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387509253" ObjectName="10kV三仙洞线"/>
   <cge:TPSR_Ref TObjectID="6192450387509253"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1354.26,871.308) scale(2.27417,-1.49981) translate(-749.21,-1444.76)" width="15" x="1337.207186416308" y="848.810791015625"/></g>
  <g id="230">
   <use class="kv10" height="30" transform="rotate(0,1488.18,671.319) scale(2.10631,1.9893) translate(-772.243,-319.014)" width="17" x="1470.276607867568" xlink:href="#EnergyConsumer:炉变D-y型_0" y="641.4798411482681" zvalue="1980"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450386984965" ObjectName="10kV1号站用变"/>
   <cge:TPSR_Ref TObjectID="6192450386984965"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1488.18,671.319) scale(2.10631,1.9893) translate(-772.243,-319.014)" width="17" x="1470.276607867568" y="641.4798411482681"/></g>
  <g id="226">
   <use class="kv35" height="41" transform="rotate(0,1126.2,187.76) scale(1.165,1.27796) translate(-155.789,-35.1401)" width="45" x="1099.988997822252" xlink:href="#EnergyConsumer:23230225_0" y="161.5619313668142" zvalue="1985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450386919429" ObjectName="35kV帕嘎线勐嘎临时变侧"/>
   <cge:TPSR_Ref TObjectID="6192450386919429"/></metadata>
  <rect fill="white" height="41" opacity="0" stroke="white" transform="rotate(0,1126.2,187.76) scale(1.165,1.27796) translate(-155.789,-35.1401)" width="45" x="1099.988997822252" y="161.5619313668142"/></g>
  <g id="55">
   <use class="kv10" height="30" transform="rotate(0,882.387,871.308) scale(2.27417,-1.49981) translate(-484.827,-1444.76)" width="15" x="865.330350122758" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="848.810791015625" zvalue="2120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388688901" ObjectName="10kV中山线"/>
   <cge:TPSR_Ref TObjectID="6192450388688901"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,882.387,871.308) scale(2.27417,-1.49981) translate(-484.827,-1444.76)" width="15" x="865.330350122758" y="848.810791015625"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="277">
   <use class="kv10" height="30" transform="rotate(0,1035.64,821.486) scale(1,1) translate(6.84879e-13,-4.29782e-12)" width="15" x="1028.140049336826" xlink:href="#Disconnector:刀闸_0" y="806.4864054619039" zvalue="1923"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388230149" ObjectName="10kV勐稳线0336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450388230149"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1035.64,821.486) scale(1,1) translate(6.84879e-13,-4.29782e-12)" width="15" x="1028.140049336826" y="806.4864054619039"/></g>
  <g id="263">
   <use class="kv10" height="30" transform="rotate(0,1194.54,821.486) scale(1,1) translate(7.90729e-13,-4.29782e-12)" width="15" x="1187.042064619771" xlink:href="#Disconnector:刀闸_0" y="806.4864054619039" zvalue="1941"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387836933" ObjectName="10kV勐戛线0366隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450387836933"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1194.54,821.486) scale(1,1) translate(7.90729e-13,-4.29782e-12)" width="15" x="1187.042064619771" y="806.4864054619039"/></g>
  <g id="249">
   <use class="kv10" height="30" transform="rotate(0,1348.52,821.486) scale(1,1) translate(8.93297e-13,-4.29782e-12)" width="15" x="1341.016885630376" xlink:href="#Disconnector:刀闸_0" y="806.4864054619039" zvalue="1959"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387443717" ObjectName="10kV三仙洞线0376隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450387443717"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1348.52,821.486) scale(1,1) translate(8.93297e-13,-4.29782e-12)" width="15" x="1341.016885630376" y="806.4864054619039"/></g>
  <g id="234">
   <use class="kv35" height="30" transform="rotate(0,1126.04,258.98) scale(1,1) translate(0,-6.50093e-13)" width="15" x="1118.543507617158" xlink:href="#Disconnector:刀闸_0" y="243.9798552021621" zvalue="1974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387116037" ObjectName="35kV1号临时主变变高侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450387116037"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1126.04,258.98) scale(1,1) translate(0,-6.50093e-13)" width="15" x="1118.543507617158" y="243.9798552021621"/></g>
  <g id="231">
   <use class="kv10" height="33" transform="rotate(0,1488.53,603.697) scale(1.29046,1.09493) translate(-333.005,-50.775)" width="14" x="1479.498133370064" xlink:href="#Disconnector:手车隔离开关13_0" y="585.6307906991798" zvalue="1978"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450387050501" ObjectName="10kV1号站用变0411熔断器"/>
   <cge:TPSR_Ref TObjectID="6192450387050501"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1488.53,603.697) scale(1.29046,1.09493) translate(-333.005,-50.775)" width="14" x="1479.498133370064" y="585.6307906991798"/></g>
  <g id="217">
   <use class="kv10" height="26" transform="rotate(0,1596.6,603.697) scale(1.50553,1.38972) translate(-533.077,-164.229)" width="12" x="1587.56441351595" xlink:href="#Disconnector:手车刀闸2020_0" y="585.6307908888716" zvalue="1995"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450386788357" ObjectName="10kV临时母线电压0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450386788357"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1596.6,603.697) scale(1.50553,1.38972) translate(-533.077,-164.229)" width="12" x="1587.56441351595" y="585.6307908888716"/></g>
  <g id="54">
   <use class="kv10" height="30" transform="rotate(0,876.64,821.486) scale(1,1) translate(5.78964e-13,-4.29782e-12)" width="15" x="869.1400493368261" xlink:href="#Disconnector:刀闸_0" y="806.4864054619039" zvalue="2122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388623365" ObjectName="10kV中山线0326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450388623365"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,876.64,821.486) scale(1,1) translate(5.78964e-13,-4.29782e-12)" width="15" x="869.1400493368261" y="806.4864054619039"/></g>
 </g>
</svg>