<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549677195265" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1746.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2326.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Compensator:35kV并联电容电抗器111_0" viewBox="0,0,70,40">
   <use terminal-index="0" type="0" x="3.499999699592589" xlink:href="#terminal" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.85000010347366" x2="45.85000010347366" y1="17.89999997997284" y2="22.10000002002716"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="47.25000011682511" x2="47.25000011682511" y1="17.89999997997284" y2="22.10000002002716"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="67.55000031042098" x2="67.55000031042098" y1="9.033333228747058" y2="31.17773178018145"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="64.75000028371811" x2="67.55000031042098" y1="9.033333228747054" y2="9.033333228747054"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="43.40000008010864" x2="59.85000023698807" y1="36.45000015687943" y2="36.45000015687943"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="58.83888911623426" x2="61.97592618318841" y1="30.2018519491443" y2="30.2018519491443"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="57.05000021028519" x2="61.97592618318841" y1="20" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="61.95000025701523" x2="61.95000025701523" y1="20" y2="30.16296305988453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="47.25000011682511" x2="55.65000019693375" y1="20" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="43.75000008344651" x2="40.6648148688387" y1="30.15000009679795" y2="30.15000009679795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="51.45000015687943" x2="51.45000015687943" y1="20" y2="34.20740754289981"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.60000005340576" x2="40.60000005340576" y1="30.11111120753818" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="67.55000031042098" x2="64.75000028371811" y1="31.17773178018145" y2="31.17773178018145"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.50925931179965" x2="45.85000010347366" y1="20" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="61.95000025701523" x2="67.55000031042098" y1="20" y2="20"/>
   <path d="M 48.7884 30.15 A 4.14948 2.54445 -90 0 1 43.6995 30.15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 53.8773 30.15 A 4.14948 2.54445 -90 0 1 48.7884 30.15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 58.8262 30.15 A 4.14948 2.54445 -90 0 1 53.7373 30.15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.65000019693375" x2="55.65000019693375" y1="17.89999997997284" y2="22.10000002002716"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="57.05000021028519" x2="57.05000021028519" y1="17.89999997997284" y2="22.10000002002716"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.916666360696155" x2="8.924999751329416" y1="19.96499999966622" y2="19.96499999966622"/>
   <path d="M 15.8317 26.5917 A 6.88333 6.65 -180 1 0 8.94833 19.9417" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.8083 26.4983 L 15.925 19.965 L 40.8333 19.965" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT222288_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1.166666666666671"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.14073473482975" x2="25.14073473482975" y1="19.25" y2="9.250000000000005"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="17" y1="28" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="9.166666666666671" y2="1.166666666666671"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="17.5" y1="9.16666666666667" y2="9.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="23.41666666666667" y2="19.16666666666667"/>
   <rect fill-opacity="0" height="4.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(225,20.85,13.88) scale(1,-1) translate(0,-807)" width="10.25" x="15.72" y="11.83"/>
   <ellipse cx="15.15" cy="28.4" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="34" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.08333333333333" x2="17.08333333333333" y1="34.75" y2="34.75"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV大厂变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="59.4" id="1" preserveAspectRatio="xMidYMid slice" width="222.19" x="55.56" xlink:href="logo.png" y="31.68"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,166.653,61.3772) scale(1,1) translate(-1.23361e-14,0)" writing-mode="lr" x="166.65" xml:space="preserve" y="65.88" zvalue="10006"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,184.625,58.3894) scale(1,1) translate(0,4.03906e-15)" writing-mode="lr" x="184.63" xml:space="preserve" y="65.89" zvalue="10007"> 35kV大厂变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="178" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,296.438,407.25) scale(1,1) translate(0,-2.63289e-13)" width="72.88" x="260" y="395.25" zvalue="10113"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.438,407.25) scale(1,1) translate(0,-2.63289e-13)" writing-mode="lr" x="296.44" xml:space="preserve" y="411.75" zvalue="10113">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="75" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,92.625,326.25) scale(1,1) translate(0,0)" width="72.88" x="56.19" y="314.25" zvalue="10114"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.625,326.25) scale(1,1) translate(0,0)" writing-mode="lr" x="92.63" xml:space="preserve" y="330.75" zvalue="10114">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="176" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,194.531,407.25) scale(1,1) translate(0,0)" width="72.88" x="158.09" y="395.25" zvalue="10115"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.531,407.25) scale(1,1) translate(0,0)" writing-mode="lr" x="194.53" xml:space="preserve" y="411.75" zvalue="10115">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="79" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,92.625,407.25) scale(1,1) translate(0,0)" width="72.88" x="56.19" y="395.25" zvalue="10116"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.625,407.25) scale(1,1) translate(0,0)" writing-mode="lr" x="92.63" xml:space="preserve" y="411.75" zvalue="10116">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="78" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,92.625,366.75) scale(1,1) translate(0,0)" width="72.88" x="56.19" y="354.75" zvalue="10117"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.625,366.75) scale(1,1) translate(0,0)" writing-mode="lr" x="92.63" xml:space="preserve" y="371.25" zvalue="10117">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.076,341.091) scale(1,1) translate(0,0)" writing-mode="lr" x="534.08" xml:space="preserve" y="345.59" zvalue="7577">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.076,658.535) scale(1,1) translate(0,0)" writing-mode="lr" x="534.08" xml:space="preserve" y="663.04" zvalue="7716">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1659.34,923.663) scale(1,1) translate(0,0)" writing-mode="lr" x="1659.34" xml:space="preserve" y="928.16" zvalue="7786">07760</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.983,431.584) scale(1,1) translate(0,0)" writing-mode="lr" x="801.98" xml:space="preserve" y="436.08" zvalue="7803">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.555,386.68) scale(1,1) translate(0,0)" writing-mode="lr" x="801.55" xml:space="preserve" y="391.18" zvalue="7810">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987.98,407.07) scale(1,1) translate(-2.16045e-13,0)" writing-mode="lr" x="987.98" xml:space="preserve" y="411.57" zvalue="8016">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.61,367.947) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.61" xml:space="preserve" y="372.45" zvalue="8030">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.61,419.947) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.61" xml:space="preserve" y="424.45" zvalue="8035">39017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.59,75.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.59" xml:space="preserve" y="80.31" zvalue="8808">35kV梁大线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1270.81,264.645) scale(1,1) translate(0,0)" writing-mode="lr" x="1270.81" xml:space="preserve" y="269.14" zvalue="8809">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1274.3,202.754) scale(1,1) translate(0,0)" writing-mode="lr" x="1274.3" xml:space="preserve" y="207.25" zvalue="8811">3716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1264.23,176.316) scale(1,1) translate(0,0)" writing-mode="lr" x="1264.23" xml:space="preserve" y="180.82" zvalue="8813">37167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1274.42,324.549) scale(1,1) translate(0,0)" writing-mode="lr" x="1274.42" xml:space="preserve" y="329.05" zvalue="8816">3711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="441" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.106,585.542) scale(1,1) translate(0,2.56258e-13)" writing-mode="lr" x="853.11" xml:space="preserve" y="590.04" zvalue="9223">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1695.17,843.135) scale(1,1) translate(0,0)" writing-mode="lr" x="1695.17" xml:space="preserve" y="847.63" zvalue="9338">07767</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="458" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,650.201,811.474) scale(1,1) translate(0,0)" writing-mode="lr" x="650.2" xml:space="preserve" y="815.97" zvalue="9388">0756</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="442" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.13,765.118) scale(1,1) translate(0,0)" writing-mode="lr" x="603.13" xml:space="preserve" y="769.62" zvalue="9394">075</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="553" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.537,915.625) scale(1,1) translate(0,0)" writing-mode="lr" x="630.54" xml:space="preserve" y="920.13" zvalue="9422">10kV大永线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1005.45,503.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1005.45" xml:space="preserve" y="508" zvalue="9703">35kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.11,484.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1200.11" xml:space="preserve" y="489.21" zvalue="9705">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="538" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.321,508.562) scale(1,1) translate(0,0)" writing-mode="lr" x="892.3209777738311" xml:space="preserve" y="513.0625" zvalue="9714">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="550" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.25,630.125) scale(1,1) translate(0,0)" writing-mode="lr" x="801.25" xml:space="preserve" y="634.63" zvalue="9718">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="631" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1382.04,431.584) scale(1,1) translate(0,0)" writing-mode="lr" x="1382.04" xml:space="preserve" y="436.08" zvalue="9723">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="629" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381.55,386.68) scale(1,1) translate(0,0)" writing-mode="lr" x="1381.55" xml:space="preserve" y="391.18" zvalue="9725">3021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="628" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1431.07,585.542) scale(1,1) translate(0,2.56258e-13)" writing-mode="lr" x="1431.07" xml:space="preserve" y="590.04" zvalue="9729">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="625" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1379.69,630.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1379.69" xml:space="preserve" y="634.63" zvalue="9735">0021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="823" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1345.19,506.437) scale(1,1) translate(0,5.44981e-14)" writing-mode="lr" x="1345.18814141645" xml:space="preserve" y="510.9374998410543" zvalue="9740">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="854" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,683.583,638.096) scale(1,1) translate(1.48104e-13,0)" writing-mode="lr" x="683.58" xml:space="preserve" y="642.6" zvalue="9762">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.614,557.667) scale(1,1) translate(-2.98257e-13,-1.21939e-13)" writing-mode="lr" x="710.61" xml:space="preserve" y="562.17" zvalue="9772">10kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,650.201,719.474) scale(1,1) translate(0,0)" writing-mode="lr" x="650.2" xml:space="preserve" y="723.97" zvalue="9778">0751</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.201,811.474) scale(1,1) translate(0,0)" writing-mode="lr" x="851.2" xml:space="preserve" y="815.97" zvalue="9784">0746</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,804.13,765.118) scale(1,1) translate(0,0)" writing-mode="lr" x="804.13" xml:space="preserve" y="769.62" zvalue="9786">074</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.537,915.625) scale(1,1) translate(0,0)" writing-mode="lr" x="831.54" xml:space="preserve" y="920.13" zvalue="9790">10kV大三线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850.201,719.474) scale(1,1) translate(0,0)" writing-mode="lr" x="850.2" xml:space="preserve" y="723.97" zvalue="9794">0741</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.2,811.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.2" xml:space="preserve" y="815.97" zvalue="9859">0736</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.84,765.118) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.84" xml:space="preserve" y="769.62" zvalue="9861">073</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.54,915.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.54" xml:space="preserve" y="920.13" zvalue="9865">10kV大小线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.2,719.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.2" xml:space="preserve" y="723.97" zvalue="9869">0731</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1250.2,811.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1250.2" xml:space="preserve" y="815.97" zvalue="9874">0726</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1203.84,765.118) scale(1,1) translate(0,0)" writing-mode="lr" x="1203.84" xml:space="preserve" y="769.62" zvalue="9876">072</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1250.2,719.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1250.2" xml:space="preserve" y="723.97" zvalue="9884">0721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1450.2,811.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1450.2" xml:space="preserve" y="815.97" zvalue="9889">0716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1403.84,765.118) scale(1,1) translate(0,0)" writing-mode="lr" x="1403.84" xml:space="preserve" y="769.62" zvalue="9891">071</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430.54,915.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.54" xml:space="preserve" y="920.13" zvalue="9895">10kV大勐线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1450.2,719.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1450.2" xml:space="preserve" y="723.97" zvalue="9899">0711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1650.91,791.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1650.91" xml:space="preserve" y="795.97" zvalue="9904">0776</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1603.84,751.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1603.84" xml:space="preserve" y="755.95" zvalue="9906">077</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1650.2,711.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1650.2" xml:space="preserve" y="715.97" zvalue="9914">0771</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1653.83,935.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1653.83" xml:space="preserve" y="940" zvalue="9918">10kV#1电容器</text>
  <line fill="none" id="192" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="381" x2="381" y1="16" y2="1006" zvalue="10008"/>
  <line fill="none" id="184" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.90549597855136" x2="321.5167560321705" y1="169.7708030256257" y2="169.7708030256257" zvalue="10010"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="45" x2="116.4400000000001" y1="928.7056480819297" y2="928.7056480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="45" x2="116.4400000000001" y1="980.0451480819296" y2="980.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="45" x2="45" y1="928.7056480819297" y2="980.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4400000000001" x2="116.4400000000001" y1="928.7056480819297" y2="980.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4405" x2="351.0005" y1="928.7056480819297" y2="928.7056480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4405" x2="351.0005" y1="980.0451480819296" y2="980.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4405" x2="116.4405" y1="928.7056480819297" y2="980.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="351.0005" x2="351.0005" y1="928.7056480819297" y2="980.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="45" x2="116.4400000000001" y1="980.0451280819295" y2="980.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="45" x2="116.4400000000001" y1="1007.52262808193" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="45" x2="45" y1="980.0451280819295" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4400000000001" x2="116.4400000000001" y1="980.0451280819295" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4405" x2="187.4758" y1="980.0451280819295" y2="980.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4405" x2="187.4758" y1="1007.52262808193" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4405" x2="116.4405" y1="980.0451280819295" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.4758" x2="187.4758" y1="980.0451280819295" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.4758" x2="269.2379" y1="980.0451280819295" y2="980.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.4758" x2="269.2379" y1="1007.52262808193" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.4758" x2="187.4758" y1="980.0451280819295" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="269.2379" x2="269.2379" y1="980.0451280819295" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="269.2378" x2="350.9999" y1="980.0451280819295" y2="980.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="269.2378" x2="350.9999" y1="1007.52262808193" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="269.2378" x2="269.2378" y1="980.0451280819295" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="350.9999" x2="350.9999" y1="980.0451280819295" y2="1007.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="45" x2="116.4400000000001" y1="1007.52254808193" y2="1007.52254808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="45" x2="116.4400000000001" y1="1035.00004808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="45" x2="45" y1="1007.52254808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4400000000001" x2="116.4400000000001" y1="1007.52254808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4405" x2="187.4758" y1="1007.52254808193" y2="1007.52254808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4405" x2="187.4758" y1="1035.00004808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="116.4405" x2="116.4405" y1="1007.52254808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.4758" x2="187.4758" y1="1007.52254808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.4758" x2="269.2379" y1="1007.52254808193" y2="1007.52254808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.4758" x2="269.2379" y1="1035.00004808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.4758" x2="187.4758" y1="1007.52254808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="269.2379" x2="269.2379" y1="1007.52254808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="269.2378" x2="350.9999" y1="1007.52254808193" y2="1007.52254808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="269.2378" x2="350.9999" y1="1035.00004808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="269.2378" x2="269.2378" y1="1007.52254808193" y2="1035.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="350.9999" x2="350.9999" y1="1007.52254808193" y2="1035.00004808193"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="182" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,82.1776,958.279) scale(1,1) translate(0,1.05188e-13)" writing-mode="lr" x="50.36" xml:space="preserve" y="964.28" zvalue="10012">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="181" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,78.0412,995.648) scale(1,1) translate(0,1.09337e-13)" writing-mode="lr" x="60.08" xml:space="preserve" y="1001.65" zvalue="10013">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="177" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,265.881,996.648) scale(1,1) translate(1.31347e-13,-1.53227e-12)" writing-mode="lr" x="197.18" xml:space="preserve" y="1002.65" zvalue="10014">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.6709,1025.21) scale(1,1) translate(-4.96267e-14,-1.57666e-12)" writing-mode="lr" x="73.67" xml:space="preserve" y="1031.21" zvalue="10015">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="169" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,274.175,1023.21) scale(1,1) translate(0,1.12397e-13)" writing-mode="lr" x="197.35" xml:space="preserve" y="1029.21" zvalue="10016">更新日期    20200902</text>
  <line fill="none" id="161" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.28468498659493" x2="320.8959450402141" y1="620.5373878122265" y2="620.5373878122265" zvalue="10017"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.3493,641.579) scale(1,1) translate(3.70782e-15,1.38307e-13)" writing-mode="lr" x="87.34927949061648" xml:space="preserve" y="646.0792383117242" zvalue="10019">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="173" y2="173"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="199" y2="199"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="173" y2="199"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="173" y2="199"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="173" y2="173"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="199" y2="199"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="173" y2="199"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="173" y2="199"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="199" y2="199"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="223.25" y2="223.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="199" y2="223.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="199" y2="223.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="199" y2="199"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="223.25" y2="223.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="199" y2="223.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="199" y2="223.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="223.25" y2="223.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="246" y2="246"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="223.25" y2="246"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="223.25" y2="246"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="223.25" y2="223.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="246" y2="246"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="223.25" y2="246"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="223.25" y2="246"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="246" y2="246"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="268.75" y2="268.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="246" y2="268.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="246" y2="268.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="246" y2="246"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="268.75" y2="268.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="246" y2="268.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="246" y2="268.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="268.75" y2="268.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="291.5" y2="291.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="268.75" y2="291.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="268.75" y2="291.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="268.75" y2="268.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="291.5" y2="291.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="268.75" y2="291.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="268.75" y2="291.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="453" y2="453"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="57" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="453" y2="453"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="453" y2="453"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3873" x2="220.3873" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="453" y2="453"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="220.3872" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="453" y2="453"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338" x2="338" y1="453" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="57" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3873" x2="220.3873" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="220.3872" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="491.2823" y2="491.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338" x2="338" y1="491.2823" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="57" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3873" x2="220.3873" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="220.3872" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="515.9617" y2="515.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338" x2="338" y1="515.9617" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="565.3205" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="57" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="565.3205" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="565.3205" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3873" x2="220.3873" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="565.3205" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="220.3872" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="540.6411000000001" y2="540.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="565.3205" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338" x2="338" y1="540.6411000000001" y2="565.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="565.3206" y2="565.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="57" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="565.3206" y2="565.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="565.3206" y2="565.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3873" x2="220.3873" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="565.3206" y2="565.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="220.3872" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="565.3206" y2="565.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338" x2="338" y1="565.3206" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="102.7745" y1="614.6794" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57" x2="57" y1="590" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="590" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="161.5809" y1="614.6794" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.7745" x2="102.7745" y1="590" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="590" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="220.3873" y1="614.6794" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5809" x2="161.5809" y1="590" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3873" x2="220.3873" y1="590" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="279.1936000000001" y1="614.6794" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.3872" x2="220.3872" y1="590" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="590" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="590" y2="590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="338" y1="614.6794" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.1936000000001" x2="279.1936000000001" y1="590" y2="614.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338" x2="338" y1="590" y2="614.6794"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" x="134" xml:space="preserve" y="468" zvalue="10023">35kV    母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="134" xml:space="preserve" y="484" zvalue="10023">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,203.399,327.841) scale(1,1) translate(0,0)" writing-mode="lr" x="203.4" xml:space="preserve" y="332.34" zvalue="10024">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,308.399,327.841) scale(1,1) translate(0,0)" writing-mode="lr" x="308.4" xml:space="preserve" y="332.34" zvalue="10025">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" x="251.34375" xml:space="preserve" y="468" zvalue="10026">10kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="251.34375" xml:space="preserve" y="484" zvalue="10026">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,503.5) scale(1,1) translate(0,0)" writing-mode="lr" x="82" xml:space="preserve" y="508" zvalue="10027">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,529) scale(1,1) translate(0,0)" writing-mode="lr" x="82" xml:space="preserve" y="533.5" zvalue="10028">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,554.5) scale(1,1) translate(0,0)" writing-mode="lr" x="82" xml:space="preserve" y="559" zvalue="10029">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,580) scale(1,1) translate(0,0)" writing-mode="lr" x="82" xml:space="preserve" y="584.5" zvalue="10030">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,605.5) scale(1,1) translate(0,0)" writing-mode="lr" x="82" xml:space="preserve" y="610" zvalue="10031">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,187) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="192.5" zvalue="10032">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.25,187) scale(1,1) translate(0,0)" writing-mode="lr" x="230.25" xml:space="preserve" y="192.5" zvalue="10033">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.6875,211.25) scale(1,1) translate(0,0)" writing-mode="lr" x="47.69" xml:space="preserve" y="215.75" zvalue="10034">35kV母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.25,258) scale(1,1) translate(0,0)" writing-mode="lr" x="233.25" xml:space="preserve" y="262.5" zvalue="10035">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.25,279) scale(1,1) translate(0,0)" writing-mode="lr" x="234.25" xml:space="preserve" y="283.5" zvalue="10036">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.6875,235.25) scale(1,1) translate(0,0)" writing-mode="lr" x="48.69" xml:space="preserve" y="239.75" zvalue="10037">10kV母频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="76" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,226.678,955.279) scale(1,1) translate(3.51619e-14,1.04855e-13)" writing-mode="lr" x="158.36" xml:space="preserve" y="961.28" zvalue="10119">DaChang-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="77" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,152.041,995.648) scale(1,1) translate(0,1.09337e-13)" writing-mode="lr" x="134.08" xml:space="preserve" y="1001.65" zvalue="10121">段勇</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1233,915.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1233" xml:space="preserve" y="920.13" zvalue="10123">10kV大叠线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.855,71.5) scale(1,1) translate(0,0)" writing-mode="lr" x="932.85" xml:space="preserve" y="76" zvalue="10143">35kV厂山线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.078,262.332) scale(1,1) translate(0,0)" writing-mode="lr" x="911.08" xml:space="preserve" y="266.83" zvalue="10144">372</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.033,200.442) scale(1,1) translate(0,0)" writing-mode="lr" x="911.03" xml:space="preserve" y="204.94" zvalue="10146">3726</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.75,174.003) scale(1,1) translate(0,0)" writing-mode="lr" x="901.75" xml:space="preserve" y="178.5" zvalue="10148">37267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.597,322.237) scale(1,1) translate(0,0)" writing-mode="lr" x="911.6" xml:space="preserve" y="326.74" zvalue="10151">3721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900,286.232) scale(1,1) translate(0,0)" writing-mode="lr" x="900" xml:space="preserve" y="290.73" zvalue="10162">37217</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921,971.714) scale(1,1) translate(0,0)" writing-mode="lr" x="921" xml:space="preserve" y="976.21" zvalue="10166">10kV2号站用变</text>
  <ellipse cx="840.66" cy="432.66" fill="rgb(255,0,0)" fill-opacity="1" id="289" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10185"/>
  <ellipse cx="1424.66" cy="432.66" fill="rgb(255,0,0)" fill-opacity="1" id="293" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10187"/>
  <ellipse cx="1314.66" cy="264.66" fill="rgb(255,0,0)" fill-opacity="1" id="294" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10189"/>
  <ellipse cx="953.66" cy="264.66" fill="rgb(255,0,0)" fill-opacity="1" id="295" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10191"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.6875,257.5) scale(1,1) translate(0,0)" writing-mode="lr" x="45.69" xml:space="preserve" y="262" zvalue="10193">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.6875,279.5) scale(1,1) translate(0,0)" writing-mode="lr" x="45.69" xml:space="preserve" y="284" zvalue="10195">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,890.5,532) scale(1,1) translate(0,0)" writing-mode="lr" x="890.5" xml:space="preserve" y="537.5" zvalue="10200">2000KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1345.5,529.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1345.5" xml:space="preserve" y="535" zvalue="10201">2500kVA</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="260" y="395.25" zvalue="10113"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="56.19" y="314.25" zvalue="10114"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="158.09" y="395.25" zvalue="10115"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="56.19" y="395.25" zvalue="10116"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="56.19" y="354.75" zvalue="10117"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv35" d="M 543.67 354.09 L 1626 354.09" stroke-width="6" zvalue="7576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674392211459" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674392211459"/></metadata>
  <path d="M 543.67 354.09 L 1626 354.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 543.67 674.09 L 1742 674.09" stroke-width="6" zvalue="7715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674392276995" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674392276995"/></metadata>
  <path d="M 543.67 674.09 L 1742 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="235">
   <use class="kv10" height="20" transform="rotate(180,1629.85,921.914) scale(1.24619,-1.0068) translate(-320.75,-1837.53)" width="10" x="1623.622569954628" xlink:href="#GroundDisconnector:地刀_0" y="911.8463545646038" zvalue="7785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453490835458" ObjectName="10kV#1电容器07760接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453490835458"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1629.85,921.914) scale(1.24619,-1.0068) translate(-320.75,-1837.53)" width="10" x="1623.622569954628" y="911.8463545646038"/></g>
  <g id="150">
   <use class="kv35" height="20" transform="rotate(90,1035.37,381.948) scale(1.24619,-1.0068) translate(-203.309,-761.248)" width="10" x="1029.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="371.8800877847167" zvalue="8029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453491949570" ObjectName="35kV母线39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453491949570"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1035.37,381.948) scale(1.24619,-1.0068) translate(-203.309,-761.248)" width="10" x="1029.143242399862" y="371.8800877847167"/></g>
  <g id="170">
   <use class="kv35" height="20" transform="rotate(90,1035.37,433.948) scale(1.24619,-1.0068) translate(-203.309,-864.897)" width="10" x="1029.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="423.8800877847167" zvalue="8034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453491818498" ObjectName="35kV母线39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453491818498"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1035.37,433.948) scale(1.24619,-1.0068) translate(-203.309,-864.897)" width="10" x="1029.143242399862" y="423.8800877847167"/></g>
  <g id="67">
   <use class="kv35" height="20" transform="rotate(270,1259,162.042) scale(-1.19164,-1.0068) translate(-2314.57,-322.921)" width="10" x="1253.040951036204" xlink:href="#GroundDisconnector:地刀_0" y="151.9737839181951" zvalue="8812"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453491097602" ObjectName="35kV梁大线37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453491097602"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1259,162.042) scale(-1.19164,-1.0068) translate(-2314.57,-322.921)" width="10" x="1253.040951036204" y="151.9737839181951"/></g>
  <g id="175">
   <use class="kv10" height="20" transform="rotate(0,1660.57,840.219) scale(-1.24619,1.0068) translate(-2991.86,-5.60742)" width="10" x="1654.340680866889" xlink:href="#GroundDisconnector:地刀_0" y="830.1511097754801" zvalue="9337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453491359746" ObjectName="10kV#1电容器07767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453491359746"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1660.57,840.219) scale(-1.24619,1.0068) translate(-2991.86,-5.60742)" width="10" x="1654.340680866889" y="830.1511097754801"/></g>
  <g id="274">
   <use class="kv35" height="20" transform="rotate(270,899.268,159.73) scale(-1.19164,-1.0068) translate(-1652.96,-318.312)" width="10" x="893.3092928717977" xlink:href="#GroundDisconnector:地刀_0" y="149.6615585763984" zvalue="10147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450123268101" ObjectName="35kV厂山线37267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450123268101"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,899.268,159.73) scale(-1.19164,-1.0068) translate(-1652.96,-318.312)" width="10" x="893.3092928717977" y="149.6615585763984"/></g>
  <g id="281">
   <use class="kv35" height="20" transform="rotate(270,899.768,298.958) scale(-1.19164,-1.0068) translate(-1653.87,-595.829)" width="10" x="893.8092928717977" xlink:href="#GroundDisconnector:地刀_0" y="288.8902033789901" zvalue="10161"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450123530245" ObjectName="35kV厂山线37217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450123530245"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,899.768,298.958) scale(-1.19164,-1.0068) translate(-1653.87,-595.829)" width="10" x="893.8092928717977" y="288.8902033789901"/></g>
 </g>
 <g id="BreakerClass">
  <g id="276">
   <use class="kv35" height="20" transform="rotate(180,823.588,431.209) scale(1.5542,1.35421) translate(-290.905,-109.246)" width="10" x="815.8167107229006" xlink:href="#Breaker:开关_0" y="417.6666962122334" zvalue="7801"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925057708036" ObjectName="#1主变高压侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925057708036"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,823.588,431.209) scale(1.5542,1.35421) translate(-290.905,-109.246)" width="10" x="815.8167107229006" y="417.6666962122334"/></g>
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,1292.21,265.02) scale(1.5542,1.35421) translate(-458.006,-65.7772)" width="10" x="1284.434034534565" xlink:href="#Breaker:开关_0" y="251.4779758524579" zvalue="8807"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925057773572" ObjectName="35kV梁大线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473925057773572"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1292.21,265.02) scale(1.5542,1.35421) translate(-458.006,-65.7772)" width="10" x="1284.434034534565" y="251.4779758524579"/></g>
  <g id="461">
   <use class="kv10" height="20" transform="rotate(180,824.768,581.542) scale(1.5542,1.35421) translate(-291.326,-148.567)" width="10" x="816.9973442678217" xlink:href="#Breaker:开关_0" y="568" zvalue="9222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925057839108" ObjectName="#1主变低压侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925057839108"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,824.768,581.542) scale(1.5542,1.35421) translate(-291.326,-148.567)" width="10" x="816.9973442678217" y="568"/></g>
  <g id="546">
   <use class="kv10" height="20" transform="rotate(180,629.293,762.368) scale(1.5542,1.35421) translate(-221.623,-195.865)" width="10" x="621.5216911906348" xlink:href="#Breaker:开关_0" y="748.8258941321849" zvalue="9393"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925057904644" ObjectName="10kV大永线075断路器"/>
   <cge:TPSR_Ref TObjectID="6473925057904644"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,629.293,762.368) scale(1.5542,1.35421) translate(-221.623,-195.865)" width="10" x="621.5216911906348" y="748.8258941321849"/></g>
  <g id="816">
   <use class="kv35" height="20" transform="rotate(180,1403.64,431.209) scale(1.5542,1.35421) translate(-497.743,-109.246)" width="10" x="1395.872923835404" xlink:href="#Breaker:开关_0" y="417.6666962122334" zvalue="9722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925058035716" ObjectName="#2主变高压侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925058035716"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1403.64,431.209) scale(1.5542,1.35421) translate(-497.743,-109.246)" width="10" x="1395.872923835404" y="417.6666962122334"/></g>
  <g id="811">
   <use class="kv10" height="20" transform="rotate(180,1402.73,581.542) scale(1.5542,1.35421) translate(-497.417,-148.567)" width="10" x="1394.95761649399" xlink:href="#Breaker:开关_0" y="568" zvalue="9728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925057970180" ObjectName="#2主变低压侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925057970180"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1402.73,581.542) scale(1.5542,1.35421) translate(-497.417,-148.567)" width="10" x="1394.95761649399" y="568"/></g>
  <g id="21">
   <use class="kv10" height="20" transform="rotate(180,830.293,762.368) scale(1.5542,1.35421) translate(-293.296,-195.865)" width="10" x="822.5216911906346" xlink:href="#Breaker:开关_0" y="748.8258941321849" zvalue="9785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925058101252" ObjectName="10kV大三线074断路器"/>
   <cge:TPSR_Ref TObjectID="6473925058101252"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,830.293,762.368) scale(1.5542,1.35421) translate(-293.296,-195.865)" width="10" x="822.5216911906346" y="748.8258941321849"/></g>
  <g id="115">
   <use class="kv10" height="20" transform="rotate(180,1030,762.368) scale(1.5542,1.35421) translate(-364.508,-195.865)" width="10" x="1022.229003203369" xlink:href="#Breaker:开关_0" y="748.8258941321849" zvalue="9860"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925058166788" ObjectName="10kV大小线073断路器"/>
   <cge:TPSR_Ref TObjectID="6473925058166788"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1030,762.368) scale(1.5542,1.35421) translate(-364.508,-195.865)" width="10" x="1022.229003203369" y="748.8258941321849"/></g>
  <g id="132">
   <use class="kv10" height="20" transform="rotate(180,1230,762.368) scale(1.5542,1.35421) translate(-435.825,-195.865)" width="10" x="1222.229003203369" xlink:href="#Breaker:开关_0" y="748.8258941321849" zvalue="9875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925058232324" ObjectName="10kV大叠线072断路器"/>
   <cge:TPSR_Ref TObjectID="6473925058232324"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1230,762.368) scale(1.5542,1.35421) translate(-435.825,-195.865)" width="10" x="1222.229003203369" y="748.8258941321849"/></g>
  <g id="158">
   <use class="kv10" height="20" transform="rotate(180,1430,762.368) scale(1.5542,1.35421) translate(-507.141,-195.865)" width="10" x="1422.229003203369" xlink:href="#Breaker:开关_0" y="748.8258941321849" zvalue="9890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925058297860" ObjectName="10kV大勐线071断路器"/>
   <cge:TPSR_Ref TObjectID="6473925058297860"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1430,762.368) scale(1.5542,1.35421) translate(-507.141,-195.865)" width="10" x="1422.229003203369" y="748.8258941321849"/></g>
  <g id="191">
   <use class="kv10" height="20" transform="rotate(180,1630,748.701) scale(1.5542,1.35421) translate(-578.457,-192.29)" width="10" x="1622.229003203369" xlink:href="#Breaker:开关_0" y="735.1592274655184" zvalue="9905"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925058363396" ObjectName="10kV#1电容器077断路器"/>
   <cge:TPSR_Ref TObjectID="6473925058363396"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1630,748.701) scale(1.5542,1.35421) translate(-578.457,-192.29)" width="10" x="1622.229003203369" y="735.1592274655184"/></g>
  <g id="277">
   <use class="kv35" height="20" transform="rotate(0,932.473,262.708) scale(1.5542,1.35421) translate(-329.732,-65.1724)" width="10" x="924.7023763701587" xlink:href="#Breaker:开关_0" y="249.165750510661" zvalue="10142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924570841092" ObjectName="35kV厂山线372断路器"/>
   <cge:TPSR_Ref TObjectID="6473924570841092"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,932.473,262.708) scale(1.5542,1.35421) translate(-329.732,-65.1724)" width="10" x="924.7023763701587" y="249.165750510661"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="272">
   <use class="kv35" height="30" transform="rotate(0,824.588,386.372) scale(0.947693,-0.6712) translate(45.1201,-966.948)" width="15" x="817.4800105358408" xlink:href="#Disconnector:刀闸_0" y="376.3043387992516" zvalue="7809"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453490900994" ObjectName="#1主变高压侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453490900994"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,824.588,386.372) scale(0.947693,-0.6712) translate(45.1201,-966.948)" width="15" x="817.4800105358408" y="376.3043387992516"/></g>
  <g id="1453">
   <use class="kv35" height="30" transform="rotate(180,1008.01,407.763) scale(0.947693,-0.6712) translate(55.2439,-1020.21)" width="15" x="1000.902401042228" xlink:href="#Disconnector:刀闸_0" y="397.6951361579612" zvalue="8015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492015106" ObjectName="35kV母线3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453492015106"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1008.01,407.763) scale(0.947693,-0.6712) translate(55.2439,-1020.21)" width="15" x="1000.902401042228" y="397.6951361579612"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,1292.21,200.588) scale(-0.947693,0.6712) translate(-2656.14,93.3295)" width="15" x="1285.103492892157" xlink:href="#Disconnector:刀闸_0" y="190.5197927208583" zvalue="8810"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453491163138" ObjectName="35kV梁大线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453491163138"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1292.21,200.588) scale(-0.947693,0.6712) translate(-2656.14,93.3295)" width="15" x="1285.103492892157" y="190.5197927208583"/></g>
  <g id="65">
   <use class="kv35" height="30" transform="rotate(180,1292.39,323.857) scale(0.947693,-0.6712) translate(70.9401,-811.292)" width="15" x="1285.284133425996" xlink:href="#Disconnector:刀闸_0" y="313.7885305077181" zvalue="8815"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453490966530" ObjectName="35kV梁大线3711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453490966530"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1292.39,323.857) scale(0.947693,-0.6712) translate(70.9401,-811.292)" width="15" x="1285.284133425996" y="313.7885305077181"/></g>
  <g id="549">
   <use class="kv10" height="30" transform="rotate(180,629.287,810.891) scale(-0.947693,0.6712) translate(-1293.7,392.298)" width="15" x="622.1788326557261" xlink:href="#Disconnector:刀闸_0" y="800.8230764122643" zvalue="9387"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453491556354" ObjectName="10kV大永线0756隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453491556354"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,629.287,810.891) scale(-0.947693,0.6712) translate(-1293.7,392.298)" width="15" x="622.1788326557261" y="800.8230764122643"/></g>
  <g id="551">
   <use class="kv10" height="30" transform="rotate(0,824.283,629.818) scale(0.947693,-0.6712) translate(45.1033,-1573.1)" width="15" x="817.1752049457245" xlink:href="#Disconnector:刀闸_0" y="619.75" zvalue="9717"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492211714" ObjectName="#1主变低压侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453492211714"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,824.283,629.818) scale(0.947693,-0.6712) translate(45.1033,-1573.1)" width="15" x="817.1752049457245" y="619.75"/></g>
  <g id="815">
   <use class="kv35" height="30" transform="rotate(0,1404.59,386.372) scale(0.947693,-0.6712) translate(77.1327,-966.948)" width="15" x="1397.480010535841" xlink:href="#Disconnector:刀闸_0" y="376.3043387992516" zvalue="9724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492342786" ObjectName="#2主变高压侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453492342786"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1404.59,386.372) scale(0.947693,-0.6712) translate(77.1327,-966.948)" width="15" x="1397.480010535841" y="376.3043387992516"/></g>
  <g id="636">
   <use class="kv10" height="30" transform="rotate(0,1402.72,629.818) scale(0.947693,-0.6712) translate(77.0297,-1573.1)" width="15" x="1395.614757959082" xlink:href="#Disconnector:刀闸_0" y="619.75" zvalue="9734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492277250" ObjectName="#2主变低压侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453492277250"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1402.72,629.818) scale(0.947693,-0.6712) translate(77.0297,-1573.1)" width="15" x="1395.614757959082" y="619.75"/></g>
  <g id="864">
   <use class="kv10" height="30" transform="rotate(180,705.697,637.404) scale(0.947693,-0.6712) translate(38.558,-1591.98)" width="15" x="698.5894229335038" xlink:href="#Disconnector:刀闸_0" y="627.3355178058908" zvalue="9761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492473858" ObjectName="10kV母线0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453492473858"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,705.697,637.404) scale(0.947693,-0.6712) translate(38.558,-1591.98)" width="15" x="698.5894229335038" y="627.3355178058908"/></g>
  <g id="5">
   <use class="kv10" height="30" transform="rotate(180,629.287,718.891) scale(-0.947693,0.6712) translate(-1293.7,347.23)" width="15" x="622.1788224832005" xlink:href="#Disconnector:刀闸_0" y="708.8230764122643" zvalue="9777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492539394" ObjectName="10kV大永线0751隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453492539394"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,629.287,718.891) scale(-0.947693,0.6712) translate(-1293.7,347.23)" width="15" x="622.1788224832005" y="708.8230764122643"/></g>
  <g id="22">
   <use class="kv10" height="30" transform="rotate(180,830.287,810.891) scale(-0.947693,0.6712) translate(-1706.79,392.298)" width="15" x="823.1788326557261" xlink:href="#Disconnector:刀闸_0" y="800.8230764122643" zvalue="9783"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492801538" ObjectName="10kV大三线0746隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453492801538"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,830.287,810.891) scale(-0.947693,0.6712) translate(-1706.79,392.298)" width="15" x="823.1788326557261" y="800.8230764122643"/></g>
  <g id="15">
   <use class="kv10" height="30" transform="rotate(180,829.287,718.891) scale(-0.947693,0.6712) translate(-1704.74,347.23)" width="15" x="822.1788224832005" xlink:href="#Disconnector:刀闸_0" y="708.8230764122643" zvalue="9793"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492604930" ObjectName="10kV大三线0741隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453492604930"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,829.287,718.891) scale(-0.947693,0.6712) translate(-1704.74,347.23)" width="15" x="822.1788224832005" y="708.8230764122643"/></g>
  <g id="116">
   <use class="kv10" height="30" transform="rotate(180,1029.29,810.891) scale(-0.947693,0.6712) translate(-2115.78,392.298)" width="15" x="1022.178832655726" xlink:href="#Disconnector:刀闸_0" y="800.8230764122643" zvalue="9858"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493063682" ObjectName="10kV大小线0736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453493063682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1029.29,810.891) scale(-0.947693,0.6712) translate(-2115.78,392.298)" width="15" x="1022.178832655726" y="800.8230764122643"/></g>
  <g id="109">
   <use class="kv10" height="30" transform="rotate(180,1029.29,718.891) scale(-0.947693,0.6712) translate(-2115.78,347.23)" width="15" x="1022.178822483201" xlink:href="#Disconnector:刀闸_0" y="708.8230764122643" zvalue="9868"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492867074" ObjectName="10kV大小线0731隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453492867074"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1029.29,718.891) scale(-0.947693,0.6712) translate(-2115.78,347.23)" width="15" x="1022.178822483201" y="708.8230764122643"/></g>
  <g id="133">
   <use class="kv10" height="30" transform="rotate(180,1229.29,810.891) scale(-0.947693,0.6712) translate(-2526.81,392.298)" width="15" x="1222.178832655726" xlink:href="#Disconnector:刀闸_0" y="800.8230764122643" zvalue="9873"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493325826" ObjectName="10kV大叠线0726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453493325826"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1229.29,810.891) scale(-0.947693,0.6712) translate(-2526.81,392.298)" width="15" x="1222.178832655726" y="800.8230764122643"/></g>
  <g id="125">
   <use class="kv10" height="30" transform="rotate(180,1229.29,718.891) scale(-0.947693,0.6712) translate(-2526.81,347.23)" width="15" x="1222.178822483201" xlink:href="#Disconnector:刀闸_0" y="708.8230764122643" zvalue="9883"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493129218" ObjectName="10kV大叠线0721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453493129218"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1229.29,718.891) scale(-0.947693,0.6712) translate(-2526.81,347.23)" width="15" x="1222.178822483201" y="708.8230764122643"/></g>
  <g id="159">
   <use class="kv10" height="30" transform="rotate(180,1429.29,810.891) scale(-0.947693,0.6712) translate(-2937.85,392.298)" width="15" x="1422.178832655726" xlink:href="#Disconnector:刀闸_0" y="800.8230764122643" zvalue="9888"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493587970" ObjectName="10kV大勐线0716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453493587970"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1429.29,810.891) scale(-0.947693,0.6712) translate(-2937.85,392.298)" width="15" x="1422.178832655726" y="800.8230764122643"/></g>
  <g id="145">
   <use class="kv10" height="30" transform="rotate(180,1429.29,718.891) scale(-0.947693,0.6712) translate(-2937.85,347.23)" width="15" x="1422.178822483201" xlink:href="#Disconnector:刀闸_0" y="708.8230764122643" zvalue="9898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493391362" ObjectName="10kV大勐线0711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453493391362"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1429.29,718.891) scale(-0.947693,0.6712) translate(-2937.85,347.23)" width="15" x="1422.178822483201" y="708.8230764122643"/></g>
  <g id="197">
   <use class="kv10" height="30" transform="rotate(180,1630,790.891) scale(-0.947693,0.6712) translate(-3350.36,382.5)" width="15" x="1622.892303114711" xlink:href="#Disconnector:刀闸_0" y="780.8230764122643" zvalue="9903"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493784578" ObjectName="10kV#1电容器0776隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453493784578"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1630,790.891) scale(-0.947693,0.6712) translate(-3350.36,382.5)" width="15" x="1622.892303114711" y="780.8230764122643"/></g>
  <g id="168">
   <use class="kv10" height="30" transform="rotate(180,1629.29,710.891) scale(-0.947693,0.6712) translate(-3348.89,343.311)" width="15" x="1622.178822483201" xlink:href="#Disconnector:刀闸_0" y="700.8230764122643" zvalue="9913"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493653506" ObjectName="10kV#1电容器0771隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453493653506"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1629.29,710.891) scale(-0.947693,0.6712) translate(-3348.89,343.311)" width="15" x="1622.178822483201" y="700.8230764122643"/></g>
  <g id="2">
   <use class="kv35" height="30" transform="rotate(0,1191,389) scale(1,1) translate(0,0)" width="15" x="1183.5" xlink:href="#Disconnector:令克_0" y="374" zvalue="9922"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493915650" ObjectName="35kV1号站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192453493915650"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1191,389) scale(1,1) translate(0,0)" width="15" x="1183.5" y="374"/></g>
  <g id="275">
   <use class="kv35" height="30" transform="rotate(0,931.48,198.276) scale(-0.947693,0.6712) translate(-1914.76,92.1969)" width="15" x="924.3718347277504" xlink:href="#Disconnector:刀闸_0" y="188.2075673790615" zvalue="10145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450123333637" ObjectName="35kV厂山线3726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450123333637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.48,198.276) scale(-0.947693,0.6712) translate(-1914.76,92.1969)" width="15" x="924.3718347277504" y="188.2075673790615"/></g>
  <g id="271">
   <use class="kv35" height="30" transform="rotate(180,932.66,321.544) scale(0.947693,-0.6712) translate(51.0851,-805.535)" width="15" x="925.5524752615892" xlink:href="#Disconnector:刀闸_0" y="311.4763051659212" zvalue="10150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450123137029" ObjectName="35kV厂山线3721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450123137029"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,932.66,321.544) scale(0.947693,-0.6712) translate(51.0851,-805.535)" width="15" x="925.5524752615892" y="311.4763051659212"/></g>
  <g id="284">
   <use class="kv10" height="30" transform="rotate(0,963.886,886) scale(1,1) translate(0,0)" width="15" x="956.3863639011004" xlink:href="#Disconnector:令克_0" y="871" zvalue="10167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450123595781" ObjectName="10kV2号站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450123595781"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,963.886,886) scale(1,1) translate(0,0)" width="15" x="956.3863639011004" y="871"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="263">
   <path class="kv35" d="M 824.65 376.48 L 824.65 354.09" stroke-width="1" zvalue="7822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.65 376.48 L 824.65 354.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv35" d="M 1007.93 398.03 L 1007.93 354.09" stroke-width="1" zvalue="8031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.93 398.03 L 1007.93 354.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv35" d="M 1025.56 382.01 L 1007.93 382.01" stroke-width="1" zvalue="8032"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.56 382.01 L 1007.93 382.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv35" d="M 1007.95 417.66 L 1007.95 452.83" stroke-width="1" zvalue="8038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.95 417.66 L 1007.95 452.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 1025.56 434.01 L 1007.95 434.01" stroke-width="1" zvalue="8039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.56 434.01 L 1007.95 434.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 1292.15 252.06 L 1292.15 210.48" stroke-width="1" zvalue="8814"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.15 252.06 L 1292.15 210.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv35" d="M 1292.13 190.85 L 1292.13 118.55" stroke-width="1" zvalue="8817"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.13 190.85 L 1292.13 118.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv35" d="M 1269.28 140.81 L 1292.13 140.81" stroke-width="1" zvalue="8818"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 1269.28 140.81 L 1292.13 140.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 1292.33 333.75 L 1292.33 354.09" stroke-width="1" zvalue="8824"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="48@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.33 333.75 L 1292.33 354.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 1292.31 277.95 L 1292.31 314.12" stroke-width="1" zvalue="8825"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.31 277.95 L 1292.31 314.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv35" d="M 823.48 418.28 L 823.48 396.11" stroke-width="1" zvalue="8872"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.48 418.28 L 823.48 396.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="555">
   <path class="kv10" d="M 629.37 820.63 L 629.29 864.88" stroke-width="1" zvalue="9422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@0" LinkObjectIDznd="552@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.37 820.63 L 629.29 864.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="557">
   <path class="kv10" d="M 658.03 856.75 L 658.03 844.05 L 629.33 844.05" stroke-width="1" zvalue="9424"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="520@0" LinkObjectIDznd="555" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.03 856.75 L 658.03 844.05 L 629.33 844.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="535">
   <path class="kv35" d="M 823.64 444.16 L 823.64 471.06" stroke-width="1" zvalue="9712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="536@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.64 444.16 L 823.64 471.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="539">
   <path class="kv10" d="M 824.66 568.61 L 824.66 532.56" stroke-width="1" zvalue="9714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@1" LinkObjectIDznd="536@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.66 568.61 L 824.66 532.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="556">
   <path class="kv10" d="M 824.82 594.5 L 824.82 619.92" stroke-width="1" zvalue="9719"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@0" LinkObjectIDznd="551@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.82 594.5 L 824.82 619.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="623">
   <path class="kv10" d="M 824.37 639.55 L 824.37 674.09" stroke-width="1" zvalue="9720"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="551@0" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.37 639.55 L 824.37 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="813">
   <path class="kv35" d="M 1404.65 376.48 L 1404.65 354.09" stroke-width="1" zvalue="9726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="815@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1404.65 376.48 L 1404.65 354.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="812">
   <path class="kv35" d="M 1403.54 418.28 L 1403.54 396.11" stroke-width="1" zvalue="9727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@1" LinkObjectIDznd="815@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1403.54 418.28 L 1403.54 396.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="633">
   <path class="kv10" d="M 1402.78 594.5 L 1402.78 619.92" stroke-width="1" zvalue="9736"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@0" LinkObjectIDznd="636@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.78 594.5 L 1402.78 619.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="632">
   <path class="kv10" d="M 1402.81 639.55 L 1402.81 674.09" stroke-width="1" zvalue="9737"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="636@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.81 639.55 L 1402.81 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="865">
   <path class="kv10" d="M 705.64 647.3 L 705.64 674.09" stroke-width="1" zvalue="9765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@1" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.64 647.3 L 705.64 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="857">
   <path class="kv10" d="M 705.61 627.67 L 705.61 608.33" stroke-width="1" zvalue="9769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@0" LinkObjectIDznd="855@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.61 627.67 L 705.61 608.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv10" d="M 629.34 775.32 L 629.34 801" stroke-width="1" zvalue="9773"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@0" LinkObjectIDznd="549@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.34 775.32 L 629.34 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 629.37 728.63 L 629.37 749.44" stroke-width="1" zvalue="9780"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="546@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.37 728.63 L 629.37 749.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 629.34 709 L 629.34 674.09" stroke-width="1" zvalue="9781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.34 709 L 629.34 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 830.37 820.63 L 830.29 864.88" stroke-width="1" zvalue="9789"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="19@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.37 820.63 L 830.29 864.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 859.03 856.75 L 859.03 844.05 L 830.33 844.05" stroke-width="1" zvalue="9791"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="18" MaxPinNum="2"/>
   </metadata>
  <path d="M 859.03 856.75 L 859.03 844.05 L 830.33 844.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 830.34 775.32 L 830.34 801" stroke-width="1" zvalue="9792"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="22@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.34 775.32 L 830.34 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv10" d="M 829.37 728.63 L 829.37 749.44" stroke-width="1" zvalue="9795"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="21@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.37 728.63 L 829.37 749.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 829.34 709 L 829.34 674.09" stroke-width="1" zvalue="9796"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@1" LinkObjectIDznd="166@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.34 709 L 829.34 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1029.37 820.63 L 1029.29 864.88" stroke-width="1" zvalue="9864"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1029.37 820.63 L 1029.29 864.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 1058.03 856.75 L 1058.03 844.05 L 1029.33 844.05" stroke-width="1" zvalue="9866"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1058.03 856.75 L 1058.03 844.05 L 1029.33 844.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1030.05 775.32 L 1030.05 801" stroke-width="1" zvalue="9867"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.05 775.32 L 1030.05 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1029.37 728.63 L 1029.37 749.44" stroke-width="1" zvalue="9870"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="115@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1029.37 728.63 L 1029.37 749.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 1029.34 709 L 1029.34 674.09" stroke-width="1" zvalue="9871"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="166@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1029.34 709 L 1029.34 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1229.37 820.63 L 1229.37 864.15" stroke-width="1" zvalue="9879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.37 820.63 L 1229.37 864.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 1258.03 856.75 L 1258.03 843.16 L 1229.37 843.16" stroke-width="1" zvalue="9881"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1258.03 856.75 L 1258.03 843.16 L 1229.37 843.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1230.05 775.32 L 1230.05 801" stroke-width="1" zvalue="9882"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.05 775.32 L 1230.05 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 1229.37 728.63 L 1229.37 749.44" stroke-width="1" zvalue="9885"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.37 728.63 L 1229.37 749.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv10" d="M 1229.34 709 L 1229.34 674.09" stroke-width="1" zvalue="9886"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="166@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.34 709 L 1229.34 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 1429.37 820.63 L 1429.29 864.88" stroke-width="1" zvalue="9894"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1429.37 820.63 L 1429.29 864.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1458.03 856.75 L 1458.03 844.05 L 1429.33 844.05" stroke-width="1" zvalue="9896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.03 856.75 L 1458.03 844.05 L 1429.33 844.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 1430.05 775.32 L 1430.05 801" stroke-width="1" zvalue="9897"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="159@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1430.05 775.32 L 1430.05 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 1429.37 728.63 L 1429.37 749.44" stroke-width="1" zvalue="9900"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="158@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1429.37 728.63 L 1429.37 749.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 1429.34 709 L 1429.34 674.09" stroke-width="1" zvalue="9901"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="166@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1429.34 709 L 1429.34 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 1630.05 761.66 L 1630.06 781" stroke-width="1" zvalue="9912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="197@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1630.05 761.66 L 1630.06 781" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 1629.37 720.63 L 1629.37 735.77" stroke-width="1" zvalue="9915"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="191@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1629.37 720.63 L 1629.37 735.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv10" d="M 1629.34 701 L 1629.34 674.09" stroke-width="1" zvalue="9916"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@1" LinkObjectIDznd="166@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1629.34 701 L 1629.34 674.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 1630.08 812.67 L 1660.48 812.67 L 1660.51 830.4" stroke-width="1" zvalue="9920"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1630.08 812.67 L 1660.48 812.67 L 1660.51 830.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv10" d="M 1630.08 814.33 L 1593.03 814.33 L 1593.03 828.75" stroke-width="1" zvalue="9921"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1630.08 814.33 L 1593.03 814.33 L 1593.03 828.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv35" d="M 1191.08 354.09 L 1191.08 375.75" stroke-width="1" zvalue="9923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@3" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1191.08 354.09 L 1191.08 375.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 1190.92 401.25 L 1190.92 424.08" stroke-width="1" zvalue="9924"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@1" LinkObjectIDznd="533@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1190.92 401.25 L 1190.92 424.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 1630.08 800.63 L 1630.08 856.36 L 1629.79 856.36 L 1629.79 912.1" stroke-width="1" zvalue="10075"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="235@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1630.08 800.63 L 1630.08 856.36 L 1629.79 856.36 L 1629.79 912.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 1629.75 842.54 L 1630.08 842.54" stroke-width="1" zvalue="10076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 1629.75 842.54 L 1630.08 842.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv35" d="M 1403.7 444.16 L 1403.7 469.28" stroke-width="1" zvalue="10131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="822@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1403.7 444.16 L 1403.7 469.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 1402.63 568.61 L 1402.63 541.54" stroke-width="1" zvalue="10133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@1" LinkObjectIDznd="822@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.63 568.61 L 1402.63 541.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv35" d="M 1268.82 162.1 L 1292.13 162.1" stroke-width="1" zvalue="10134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 1268.82 162.1 L 1292.13 162.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv35" d="M 1322 124.83 L 1322 131 L 1292.13 131" stroke-width="1" zvalue="10139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322 124.83 L 1322 131 L 1292.13 131" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv35" d="M 932.42 249.75 L 932.42 208.17" stroke-width="1" zvalue="10149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@0" LinkObjectIDznd="275@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.42 249.75 L 932.42 208.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv35" d="M 931.4 188.54 L 931.4 114.24" stroke-width="1" zvalue="10152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="278@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.4 188.54 L 931.4 114.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv35" d="M 909.54 138.49 L 931.4 138.49" stroke-width="1" zvalue="10153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.54 138.49 L 931.4 138.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv35" d="M 932.6 331.44 L 932.6 354.09" stroke-width="1" zvalue="10154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="48@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.6 331.44 L 932.6 354.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv35" d="M 932.58 275.64 L 932.58 311.81" stroke-width="1" zvalue="10155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@1" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.58 275.64 L 932.58 311.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv35" d="M 909.08 159.79 L 931.4 159.79" stroke-width="1" zvalue="10157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.08 159.79 L 931.4 159.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv35" d="M 962.27 122.52 L 962.27 128.69 L 931.4 128.69" stroke-width="1" zvalue="10159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.27 122.52 L 962.27 128.69 L 931.4 128.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv35" d="M 909.58 299.02 L 932.58 299.02" stroke-width="1" zvalue="10163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="267" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.58 299.02 L 932.58 299.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 963.8 898.25 L 963.8 921.08" stroke-width="1" zvalue="10168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@1" LinkObjectIDznd="285@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 963.8 898.25 L 963.8 921.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 963.97 872.75 L 963.97 860 L 1029.3 860" stroke-width="1" zvalue="10169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 963.97 872.75 L 963.97 860 L 1029.3 860" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,1292.13,104.844) scale(1.98323,0.922926) translate(-637.159,7.59944)" width="7" x="1285.186704179698" xlink:href="#ACLineSegment:线路_0" y="90.99999999999994" zvalue="8806"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249318621189" ObjectName="35kV梁大线"/>
   <cge:TPSR_Ref TObjectID="8444249318621189_5066549677195265"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1292.13,104.844) scale(1.98323,0.922926) translate(-637.159,7.59944)" width="7" x="1285.186704179698" y="90.99999999999994"/></g>
  <g id="278">
   <use class="kv35" height="30" transform="rotate(0,931.396,100.532) scale(1.98323,0.922926) translate(-458.319,7.23932)" width="7" x="924.4550460152917" xlink:href="#ACLineSegment:线路_0" y="86.68777465820318" zvalue="10141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249322225669" ObjectName="35kV厂山线"/>
   <cge:TPSR_Ref TObjectID="8444249322225669_5066549677195265"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.396,100.532) scale(1.98323,0.922926) translate(-458.319,7.23932)" width="7" x="924.4550460152917" y="86.68777465820318"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="520">
   <use class="kv10" height="26" transform="rotate(0,658.056,868.219) scale(-0.838049,0.927421) translate(-1444.25,67.0024)" width="12" x="653.0281809263649" xlink:href="#Accessory:避雷器1_0" y="856.1626333680676" zvalue="9414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453491490818" ObjectName="10kV大永线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,658.056,868.219) scale(-0.838049,0.927421) translate(-1444.25,67.0024)" width="12" x="653.0281809263649" y="856.1626333680676"/></g>
  <g id="171">
   <use class="kv35" height="26" transform="rotate(90,1257.81,140.778) scale(0.838049,0.927421) translate(242.097,10.0736)" width="12" x="1252.778180926365" xlink:href="#Accessory:避雷器1_0" y="128.7218190736352" zvalue="9700"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453491687426" ObjectName="35kV梁大线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1257.81,140.778) scale(0.838049,0.927421) translate(242.097,10.0736)" width="12" x="1252.778180926365" y="128.7218190736352"/></g>
  <g id="218">
   <use class="kv35" height="30" transform="rotate(0,1004.2,470.25) scale(1.25,1.25) translate(-197.09,-90.3)" width="30" x="985.4521328359561" xlink:href="#Accessory:避雷器PT带熔断器_0" y="451.5" zvalue="9702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492080642" ObjectName="35kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1004.2,470.25) scale(1.25,1.25) translate(-197.09,-90.3)" width="30" x="985.4521328359561" y="451.5"/></g>
  <g id="855">
   <use class="kv10" height="30" transform="rotate(0,709.364,590.917) scale(-1.25,-1.25) translate(-1273.11,-1059.9)" width="30" x="690.6139341293261" xlink:href="#Accessory:避雷器PT带熔断器_0" y="572.1666666666666" zvalue="9771"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492408322" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,709.364,590.917) scale(-1.25,-1.25) translate(-1273.11,-1059.9)" width="30" x="690.6139341293261" y="572.1666666666666"/></g>
  <g id="20">
   <use class="kv10" height="26" transform="rotate(0,859.056,868.219) scale(-0.838049,0.927421) translate(-1885.1,67.0024)" width="12" x="854.0281809263649" xlink:href="#Accessory:避雷器1_0" y="856.1626333680676" zvalue="9787"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492736002" ObjectName="10kV大三线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,859.056,868.219) scale(-0.838049,0.927421) translate(-1885.1,67.0024)" width="12" x="854.0281809263649" y="856.1626333680676"/></g>
  <g id="114">
   <use class="kv10" height="26" transform="rotate(0,1058.06,868.219) scale(-0.838049,0.927421) translate(-2321.55,67.0024)" width="12" x="1053.028180926365" xlink:href="#Accessory:避雷器1_0" y="856.1626333680676" zvalue="9862"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492998146" ObjectName="10kV大小线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1058.06,868.219) scale(-0.838049,0.927421) translate(-2321.55,67.0024)" width="12" x="1053.028180926365" y="856.1626333680676"/></g>
  <g id="131">
   <use class="kv10" height="26" transform="rotate(0,1258.06,868.219) scale(-0.838049,0.927421) translate(-2760.2,67.0024)" width="12" x="1253.028180926365" xlink:href="#Accessory:避雷器1_0" y="856.1626333680676" zvalue="9877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493260290" ObjectName="10kV大叠线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1258.06,868.219) scale(-0.838049,0.927421) translate(-2760.2,67.0024)" width="12" x="1253.028180926365" y="856.1626333680676"/></g>
  <g id="157">
   <use class="kv10" height="26" transform="rotate(0,1458.06,868.219) scale(-0.838049,0.927421) translate(-3198.85,67.0024)" width="12" x="1453.028180926365" xlink:href="#Accessory:避雷器1_0" y="856.1626333680676" zvalue="9892"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493522434" ObjectName="10kV大勐线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1458.06,868.219) scale(-0.838049,0.927421) translate(-3198.85,67.0024)" width="12" x="1453.028180926365" y="856.1626333680676"/></g>
  <g id="190">
   <use class="kv10" height="26" transform="rotate(0,1593.06,840.219) scale(-0.838049,0.927421) translate(-3494.94,64.8111)" width="12" x="1588.028180926365" xlink:href="#Accessory:避雷器1_0" y="828.162643239694" zvalue="9907"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493719042" ObjectName="10kV#1电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1593.06,840.219) scale(-0.838049,0.927421) translate(-3494.94,64.8111)" width="12" x="1588.028180926365" y="828.162643239694"/></g>
  <g id="254">
   <use class="kv35" height="40" transform="rotate(0,1322,106) scale(1,-1) translate(0,-212)" width="30" x="1307" xlink:href="#Accessory:PT222288_0" y="86" zvalue="10138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450122940421" ObjectName="35kV梁大线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1322,106) scale(1,-1) translate(0,-212)" width="30" x="1307" y="86"/></g>
  <g id="266">
   <use class="kv35" height="26" transform="rotate(90,898.075,138.466) scale(0.838049,0.927421) translate(172.58,9.89268)" width="12" x="893.0465227619584" xlink:href="#Accessory:避雷器1_0" y="126.4095937318383" zvalue="10156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450123071493" ObjectName="35kV厂山线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,898.075,138.466) scale(0.838049,0.927421) translate(172.58,9.89268)" width="12" x="893.0465227619584" y="126.4095937318383"/></g>
  <g id="264">
   <use class="kv35" height="40" transform="rotate(0,962.268,103.688) scale(1,-1) translate(0,-207.376)" width="30" x="947.2683418355936" xlink:href="#Accessory:PT222288_0" y="83.68777465820312" zvalue="10158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450123005957" ObjectName="35kV厂山线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,962.268,103.688) scale(1,-1) translate(0,-207.376)" width="30" x="947.2683418355936" y="83.68777465820312"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="552">
   <use class="kv10" height="30" transform="rotate(0,629.287,881.75) scale(1.25,-1.25) translate(-124.357,-1583.4)" width="12" x="621.7865295410156" xlink:href="#EnergyConsumer:负荷_0" y="863" zvalue="9421"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453491621890" ObjectName="10kV大永线"/>
   <cge:TPSR_Ref TObjectID="6192453491621890"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,629.287,881.75) scale(1.25,-1.25) translate(-124.357,-1583.4)" width="12" x="621.7865295410156" y="863"/></g>
  <g id="533">
   <use class="kv35" height="30" transform="rotate(0,1191.02,446.205) scale(1.53571,1.53571) translate(-407.973,-147.617)" width="28" x="1169.52272700799" xlink:href="#EnergyConsumer:站用变DY接地_0" y="423.1688311688312" zvalue="9704"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492146178" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1191.02,446.205) scale(1.53571,1.53571) translate(-407.973,-147.617)" width="28" x="1169.52272700799" y="423.1688311688312"/></g>
  <g id="19">
   <use class="kv10" height="30" transform="rotate(0,830.287,881.75) scale(1.25,-1.25) translate(-164.557,-1583.4)" width="12" x="822.7865295410156" xlink:href="#EnergyConsumer:负荷_0" y="863" zvalue="9788"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492670466" ObjectName="10kV大三线"/>
   <cge:TPSR_Ref TObjectID="6192453492670466"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,830.287,881.75) scale(1.25,-1.25) translate(-164.557,-1583.4)" width="12" x="822.7865295410156" y="863"/></g>
  <g id="113">
   <use class="kv10" height="30" transform="rotate(0,1029.29,881.75) scale(1.25,-1.25) translate(-204.357,-1583.4)" width="12" x="1021.786529541016" xlink:href="#EnergyConsumer:负荷_0" y="863" zvalue="9863"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453492932610" ObjectName="10kV大小线"/>
   <cge:TPSR_Ref TObjectID="6192453492932610"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1029.29,881.75) scale(1.25,-1.25) translate(-204.357,-1583.4)" width="12" x="1021.786529541016" y="863"/></g>
  <g id="155">
   <use class="kv10" height="30" transform="rotate(0,1429.29,881.75) scale(1.25,-1.25) translate(-284.357,-1583.4)" width="12" x="1421.786529541016" xlink:href="#EnergyConsumer:负荷_0" y="863" zvalue="9893"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493456898" ObjectName="10kV大勐线"/>
   <cge:TPSR_Ref TObjectID="6192453493456898"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1429.29,881.75) scale(1.25,-1.25) translate(-284.357,-1583.4)" width="12" x="1421.786529541016" y="863"/></g>
  <g id="285">
   <use class="kv10" height="30" transform="rotate(0,963.909,943.205) scale(1.53571,1.53571) translate(-328.747,-320.989)" width="28" x="942.4090909090908" xlink:href="#EnergyConsumer:站用变DY接地_0" y="920.1688311688313" zvalue="10165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450123661317" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,963.909,943.205) scale(1.53571,1.53571) translate(-328.747,-320.989)" width="28" x="942.4090909090908" y="920.1688311688313"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="536">
   <g id="5360">
    <use class="kv35" height="60" transform="rotate(0,824.665,501.75) scale(1.05625,1.04167) translate(-42.7921,-18.82)" width="40" x="803.54" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="470.5" zvalue="9713"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874562928642" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5361">
    <use class="kv10" height="60" transform="rotate(0,824.665,501.75) scale(1.05625,1.04167) translate(-42.7921,-18.82)" width="40" x="803.54" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="470.5" zvalue="9713"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874562994178" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399520419842" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399520419842"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,824.665,501.75) scale(1.05625,1.04167) translate(-42.7921,-18.82)" width="40" x="803.54" y="470.5"/></g>
  <g id="822">
   <g id="8220">
    <use class="kv35" height="30" transform="rotate(0,1403.67,505.25) scale(2.53125,2.58333) translate(-830.757,-285.919)" width="24" x="1373.29" xlink:href="#PowerTransformer2:可调不带中性点_0" y="466.5" zvalue="9739"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874563059714" ObjectName="35"/>
    </metadata>
   </g>
   <g id="8221">
    <use class="kv10" height="30" transform="rotate(0,1403.67,505.25) scale(2.53125,2.58333) translate(-830.757,-285.919)" width="24" x="1373.29" xlink:href="#PowerTransformer2:可调不带中性点_1" y="466.5" zvalue="9739"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874563125250" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399520485378" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399520485378"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1403.67,505.25) scale(2.53125,2.58333) translate(-830.757,-285.919)" width="24" x="1373.29" y="466.5"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="202">
   <use class="kv10" height="40" transform="rotate(270,1629.75,871.417) scale(-0.916667,0.9625) translate(-3410.58,33.2013)" width="70" x="1597.666666666666" xlink:href="#Compensator:35kV并联电容电抗器111_0" y="852.1666666666665" zvalue="9917"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453493850114" ObjectName="10kV#1电容器"/>
   <cge:TPSR_Ref TObjectID="6192453493850114"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1629.75,871.417) scale(-0.916667,0.9625) translate(-3410.58,33.2013)" width="70" x="1597.666666666666" y="852.1666666666665"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="28" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,624.287,936.25) scale(1,1) translate(0,0)" writing-mode="lr" x="624.48" xml:space="preserve" y="941.0599999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132144590851" ObjectName="P"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="29" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,624.287,956) scale(1,1) translate(0,0)" writing-mode="lr" x="624.48" xml:space="preserve" y="960.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132144656387" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="30" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,624.287,972.5) scale(1,1) translate(0,0)" writing-mode="lr" x="624.48" xml:space="preserve" y="977.3099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132144721923" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,624.287,988.75) scale(1,1) translate(0,0)" writing-mode="lr" x="624.48" xml:space="preserve" y="993.5599999999999" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132145115139" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="39" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,825.287,934.25) scale(1,1) translate(0,0)" writing-mode="lr" x="825.48" xml:space="preserve" y="939.0599999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132150161410" ObjectName="P"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="38" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,825.287,953) scale(1,1) translate(0,0)" writing-mode="lr" x="825.48" xml:space="preserve" y="957.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132150226946" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="37" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,825.287,969.5) scale(1,1) translate(0,0)" writing-mode="lr" x="825.48" xml:space="preserve" y="974.3099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132150292482" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,825.287,987.75) scale(1,1) translate(0,0)" writing-mode="lr" x="825.48" xml:space="preserve" y="992.5599999999999" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132150685698" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="43" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1023.29,930.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.48" xml:space="preserve" y="935.0599999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132151341058" ObjectName="P"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="42" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1024.29,950) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.48" xml:space="preserve" y="954.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132151406594" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="41" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1023.29,967.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.48" xml:space="preserve" y="972.3099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132151472130" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1022.29,984.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.48" xml:space="preserve" y="989.5599999999999" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132151865346" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="47" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1240.29,932.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.48" xml:space="preserve" y="937.0599999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132152520706" ObjectName="P"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="46" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1240.29,950) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.48" xml:space="preserve" y="954.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132152586242" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="45" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1240.29,966.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.48" xml:space="preserve" y="971.3099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132152651778" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1240.29,986.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.48" xml:space="preserve" y="991.5599999999999" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132153044994" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="60" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1430.29,935.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.48" xml:space="preserve" y="940.0599999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132153700354" ObjectName="P"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="57" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1430.29,953) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.48" xml:space="preserve" y="957.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132153765890" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1430.29,971.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.48" xml:space="preserve" y="976.3099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132153831426" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430.29,991.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.48" xml:space="preserve" y="996.5599999999999" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132154224642" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="61" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1283.07,9.94794) scale(1,1) translate(0,0)" writing-mode="lr" x="1283.26" xml:space="preserve" y="14.76" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132142428163" ObjectName="P"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="62" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1284.07,26.3959) scale(1,1) translate(0,0)" writing-mode="lr" x="1284.26" xml:space="preserve" y="31.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132142493699" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="71" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1284.07,39.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1284.26" xml:space="preserve" y="44.31" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132142559235" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1284.07,55.8439) scale(1,1) translate(0,0)" writing-mode="lr" x="1284.28" xml:space="preserve" y="60.75" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132142821379" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,131.5,530.111) scale(1,1) translate(0,0)" writing-mode="lr" x="131.63" xml:space="preserve" y="535.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132140593155" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="204" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,131.5,553.111) scale(1,1) translate(0,-1.20484e-13)" writing-mode="lr" x="131.63" xml:space="preserve" y="558.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132140658691" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="203">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,131.5,576.111) scale(1,1) translate(0,0)" writing-mode="lr" x="131.63" xml:space="preserve" y="581.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132140724227" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,132.5,504.611) scale(1,1) translate(0,0)" writing-mode="lr" x="132.63" xml:space="preserve" y="509.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132140855299" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="201" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135,211.111) scale(1,1) translate(0,0)" writing-mode="lr" x="135.2" xml:space="preserve" y="217.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132140986371" ObjectName="F"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,249.333,530) scale(1,1) translate(0,0)" writing-mode="lr" x="249.46" xml:space="preserve" y="534.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132141117443" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="199" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,249.333,553) scale(1,1) translate(0,0)" writing-mode="lr" x="249.46" xml:space="preserve" y="557.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132141182979" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,249.333,576) scale(1,1) translate(0,0)" writing-mode="lr" x="249.46" xml:space="preserve" y="580.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132141248515" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,249.333,503.5) scale(1,1) translate(0,0)" writing-mode="lr" x="249.46" xml:space="preserve" y="508.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132141379587" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="195" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135,236) scale(1,1) translate(0,0)" writing-mode="lr" x="135.2" xml:space="preserve" y="242.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132141510659" ObjectName="F"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,311.468,256.194) scale(1,1) translate(-5.75096e-14,0)" writing-mode="lr" x="311.7" xml:space="preserve" y="262.68" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132766003203" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.5,278.194) scale(1,1) translate(0,2.9498e-13)" writing-mode="lr" x="309.7" xml:space="preserve" y="284.68" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132766068739" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,131.75,604.111) scale(1,1) translate(0,0)" writing-mode="lr" x="131.88" xml:space="preserve" y="609.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132141051907" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,250.042,603) scale(1,1) translate(0,0)" writing-mode="lr" x="250.17" xml:space="preserve" y="607.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132141576195" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="25" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,135,185) scale(1,1) translate(0,0)" writing-mode="lr" x="135.15" xml:space="preserve" y="191.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132155666434" ObjectName=""/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="24" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,311,186) scale(1,1) translate(0,0)" writing-mode="lr" x="311.15" xml:space="preserve" y="192.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132155731970" ObjectName=""/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="206" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,713.79,397.5) scale(1,1) translate(0,0)" writing-mode="lr" x="713.99" xml:space="preserve" y="402.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132763578371" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="207" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,713.79,424.5) scale(1,1) translate(0,0)" writing-mode="lr" x="713.99" xml:space="preserve" y="429.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132763643907" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="210" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,713.79,451.5) scale(1,1) translate(0,0)" writing-mode="lr" x="713.99" xml:space="preserve" y="456.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132763840515" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="211">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="211" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,926.29,566.25) scale(1,1) translate(0,0)" writing-mode="lr" x="926.49" xml:space="preserve" y="571.16" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132763709444" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="212" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,926.29,593.25) scale(1,1) translate(0,0)" writing-mode="lr" x="926.49" xml:space="preserve" y="598.16" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132763774980" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="213" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,926.29,620.25) scale(1,1) translate(0,0)" writing-mode="lr" x="926.49" xml:space="preserve" y="625.16" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132764168195" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="214" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1498.17,393.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.36" xml:space="preserve" y="398.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132765544451" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="216" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1498.17,420.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.36" xml:space="preserve" y="425.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132765609987" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="220" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1498.17,447.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.36" xml:space="preserve" y="452.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132765806596" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="223" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1506.92,549.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1507.11" xml:space="preserve" y="554.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132765675523" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="224" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1506.92,578) scale(1,1) translate(0,0)" writing-mode="lr" x="1507.11" xml:space="preserve" y="582.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132765741060" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="225" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1506.92,605) scale(1,1) translate(0,0)" writing-mode="lr" x="1507.11" xml:space="preserve" y="609.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132766134275" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="73" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1644,977.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1644.2" xml:space="preserve" y="982.24" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132155273218" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="74" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1644,1000.33) scale(1,1) translate(0,0)" writing-mode="lr" x="1644.2" xml:space="preserve" y="1005.24" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132155338754" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="55" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,930.396,11.354) scale(1,1) translate(0,-4.32529e-16)" writing-mode="lr" x="929.9299999999999" xml:space="preserve" y="16" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127239680004" ObjectName="P"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="217" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,931.396,27.802) scale(1,1) translate(0,-4.08472e-15)" writing-mode="lr" x="930.9299999999999" xml:space="preserve" y="32.45" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127239745540" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="227" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,931.396,39.5) scale(1,1) translate(0,0)" writing-mode="lr" x="930.9299999999999" xml:space="preserve" y="44.13" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127239811076" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.338,55.0317) scale(1,1) translate(0,0)" writing-mode="lr" x="931.84" xml:space="preserve" y="59.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127240073220" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1009.5,521.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.63" xml:space="preserve" y="526.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132140855299" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,704.5,533.5) scale(1,1) translate(0,0)" writing-mode="lr" x="704.63" xml:space="preserve" y="538.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132141379587" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,134.468,257.5) scale(1,1) translate(-1.82077e-14,0)" writing-mode="lr" x="134.7" xml:space="preserve" y="263.99" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132764037124" ObjectName="YW1"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,341.625,328.5) scale(0.708333,0.665547) translate(136.294,160.062)" width="30" x="331" xlink:href="#State:红绿圆(方形)_0" y="318.52" zvalue="10050"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374920843265" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,341.625,328.5) scale(0.708333,0.665547) translate(136.294,160.062)" width="30" x="331" y="318.52"/></g>
  <g id="33">
   <use height="30" transform="rotate(0,246,328.5) scale(0.708333,0.665547) translate(96.9191,160.062)" width="30" x="235.38" xlink:href="#State:红绿圆(方形)_0" y="318.52" zvalue="10051"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562953778102279" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,246,328.5) scale(0.708333,0.665547) translate(96.9191,160.062)" width="30" x="235.38" y="318.52"/></g>
 </g>
</svg>