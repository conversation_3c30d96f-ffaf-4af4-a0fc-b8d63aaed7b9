<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549589704706" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:SVG标准开关_0" viewBox="0,0,11,21">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="5.5" xlink:href="#terminal" y="20.5"/>
   <rect fill-opacity="0" height="20.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.54,10.54) scale(1,1) translate(0,0)" width="10.08" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:SVG标准开关_1" viewBox="0,0,11,21">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="5.5" xlink:href="#terminal" y="20.5"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="20.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.54,10.54) scale(1,1) translate(0,0)" width="10.08" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:SVG标准开关_2" viewBox="0,0,11,21">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="5.5" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="0.5" y1="0.5" y2="20.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.5" y1="0.5" y2="20.5"/>
   <rect fill-opacity="0" height="20.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.54,10.54) scale(1,1) translate(0,0)" width="10.08" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:厂用变负荷_0" viewBox="0,0,24,49">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.05" x2="12.05" y1="34.5" y2="44.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="2.5" y2="0.5"/>
   <path d="M 7.66667 15.3333 L 16.75 15.3333 L 12 8 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333333" x2="12" y1="30.58333333333333" y2="25.75"/>
   <ellipse cx="12" cy="10.75" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12" cy="26.25" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.91666666666667" x2="17.75" y1="25.66427881928568" y2="30.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.91666666666667" x2="11.91666666666667" y1="25.66666666666666" y2="20.5"/>
   <path d="M 11.55 44.5 L 12.6333 44.5 L 12.05 48.0833 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 10.3333 35.9167 L 13.75 35.9167 L 12.0833 39.1667 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:D-D（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="1" x="5.083333333333334" xlink:href="#terminal" y="1.412397212819791"/>
   <path d="M 3.51854 6.65052 L 6.70029 6.65052 L 5.08333 3.90941 z" fill-opacity="0" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.08" cy="6.4" fill-opacity="0" rx="4.67" ry="5" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-D（规范制图）_1" viewBox="0,0,10,20">
   <path d="M 3.51854 14.706 L 6.64813 14.706 L 5.10941 12.0208 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.08" cy="13.03" fill-opacity="0" rx="4.64" ry="4.98" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="5.086767719454341" xlink:href="#terminal" y="17.93148293438639"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:电容器组带熔断器_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="2.5" xlink:href="#terminal" y="8.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="7.5" y2="9.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="12.5" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="9.5" y2="9.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="9.5" y2="7.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="8.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="7.5" y2="7.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.64166666666666" x2="12.64166666666666" y1="16.15" y2="19.09166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.9" x2="10.5" y1="14.14166666666666" y2="14.14166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.9" x2="10.5" y1="16.14166666666666" y2="16.14166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.70132377611978" x2="15.17043856134933" y1="18.9457213535914" y2="20.80633280016981"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61648296720332" x2="9.966407395865955" y1="19.19994964792486" y2="20.79227569618845"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.52953748863437" x2="13.88155138676536" y1="19.00274760664634" y2="22.51674385085443"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.12953504837116" x2="8.863367518766925" y1="22.72804518360739" y2="18.95650906051809"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.415200446966939" x2="7.149032917362698" y1="23.7581213334275" y2="19.98658521033821"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.12680850872896" x2="15.47882240685994" y1="20.20637765295043" y2="23.72037389715852"/>
  </symbol>
  <symbol id="EnergyConsumer:糖厂负荷_0" viewBox="0,0,21,30">
   <use terminal-index="0" type="0" x="7.166666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="19.25" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.75" x2="18.75" y1="25" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="17.75" y1="26.25" y2="26.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="25.25" y2="30.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.25" x2="17.25" y1="19" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="17.25" y1="19" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.097025948103791" x2="7.097025948103791" y1="1.79293756914176" y2="0.2382945839350779"/>
   <ellipse cx="6.92" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.111718118722353" x2="7.111718118722353" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.705265608193804" x2="7.11171811872235" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.518170629250881" x2="7.111718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.111718118722353" x2="7.111718118722353" y1="5.503560677018264" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.705265608193804" x2="7.11171811872235" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.518170629250881" x2="7.111718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV越盛弄相硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="277.25" x="73" xlink:href="logo.png" y="60"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,211.625,90) scale(1,1) translate(0,0)" writing-mode="lr" x="211.63" xml:space="preserve" y="93.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,206.25,87.1903) scale(1,1) translate(0,0)" writing-mode="lr" x="206.25" xml:space="preserve" y="96.19" zvalue="3">35kV越盛弄相硅厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="12" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,102.219,210.5) scale(1,1) translate(0,0)" width="73.56" x="65.44" y="198.5" zvalue="16"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,102.219,210.5) scale(1,1) translate(0,0)" writing-mode="lr" x="102.22" xml:space="preserve" y="215" zvalue="16">信号一览</text>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="407" x2="407" y1="28" y2="1058" zvalue="4"/>
  <line fill="none" id="22" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.00000000000045" x2="400" y1="163.8704926140824" y2="163.8704926140824" zvalue="6"/>
  <line fill="none" id="21" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.25000000000045" x2="394.25" y1="518.1204926140824" y2="518.1204926140824" zvalue="7"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="27.25" x2="117.25" y1="949.25" y2="949.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="27.25" x2="117.25" y1="988.4132999999999" y2="988.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="27.25" x2="27.25" y1="949.25" y2="988.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="117.25" y1="949.25" y2="988.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="387.25" y1="949.25" y2="949.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="387.25" y1="988.4132999999999" y2="988.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="117.25" y1="949.25" y2="988.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="387.25" x2="387.25" y1="949.25" y2="988.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="27.25" x2="117.25" y1="988.41327" y2="988.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="27.25" x2="117.25" y1="1016.33167" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="27.25" x2="27.25" y1="988.41327" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="117.25" y1="988.41327" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="207.25" y1="988.41327" y2="988.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="207.25" y1="1016.33167" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="117.25" y1="988.41327" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="207.25" x2="207.25" y1="988.41327" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="207.2500000000001" x2="297.2500000000001" y1="988.41327" y2="988.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="207.2500000000001" x2="297.2500000000001" y1="1016.33167" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="207.2500000000001" x2="207.2500000000001" y1="988.41327" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="297.2500000000001" x2="297.2500000000001" y1="988.41327" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="297.25" x2="387.25" y1="988.41327" y2="988.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="297.25" x2="387.25" y1="1016.33167" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="297.25" x2="297.25" y1="988.41327" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="387.25" x2="387.25" y1="988.41327" y2="1016.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="27.25" x2="117.25" y1="1016.3316" y2="1016.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="27.25" x2="117.25" y1="1044.25" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="27.25" x2="27.25" y1="1016.3316" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="117.25" y1="1016.3316" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="207.25" y1="1016.3316" y2="1016.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="207.25" y1="1044.25" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.25" x2="117.25" y1="1016.3316" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="207.25" x2="207.25" y1="1016.3316" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="207.2500000000001" x2="297.2500000000001" y1="1016.3316" y2="1016.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="207.2500000000001" x2="297.2500000000001" y1="1044.25" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="207.2500000000001" x2="207.2500000000001" y1="1016.3316" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="297.2500000000001" x2="297.2500000000001" y1="1016.3316" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="297.25" x2="387.25" y1="1016.3316" y2="1016.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="297.25" x2="387.25" y1="1044.25" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="297.25" x2="297.25" y1="1016.3316" y2="1044.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="387.25" x2="387.25" y1="1016.3316" y2="1044.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.25,969.25) scale(1,1) translate(0,0)" writing-mode="lr" x="72.25" xml:space="preserve" y="975.25" zvalue="9">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.1389,1002.14) scale(1,1) translate(0,0)" writing-mode="lr" x="68.14" xml:space="preserve" y="1008.14" zvalue="10">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,250.139,1002.14) scale(1,1) translate(0,0)" writing-mode="lr" x="250.14" xml:space="preserve" y="1008.14" zvalue="11">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.1389,1030.14) scale(1,1) translate(0,0)" writing-mode="lr" x="67.14" xml:space="preserve" y="1036.14" zvalue="12">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,249.139,1030.14) scale(1,1) translate(0,0)" writing-mode="lr" x="249.14" xml:space="preserve" y="1036.14" zvalue="13">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.75,583.75) scale(1,1) translate(0,0)" writing-mode="lr" x="92.75" xml:space="preserve" y="588.25" zvalue="15">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,248.25,970.75) scale(1,1) translate(0,0)" writing-mode="lr" x="248.25" xml:space="preserve" y="975.25" zvalue="17">YSGC-002-2006</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.649,211.253) scale(1,1) translate(0,0)" writing-mode="lr" x="218.65" xml:space="preserve" y="215.75" zvalue="18">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,322.649,211.253) scale(1,1) translate(0,0)" writing-mode="lr" x="322.65" xml:space="preserve" y="215.75" zvalue="19">通道</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="260.25" y2="260.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="286.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="28.25" y1="260.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="260.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="260.25" y2="260.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="286.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="260.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="390.25" x2="390.25" y1="260.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="286.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="310.5" y2="310.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="28.25" y1="286.25" y2="310.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="286.25" y2="310.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="286.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="310.5" y2="310.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="286.25" y2="310.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="390.25" x2="390.25" y1="286.25" y2="310.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="310.5" y2="310.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="333.25" y2="333.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="28.25" y1="310.5" y2="333.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="310.5" y2="333.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="310.5" y2="310.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="333.25" y2="333.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="310.5" y2="333.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="390.25" x2="390.25" y1="310.5" y2="333.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="333.25" y2="333.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="356" y2="356"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="28.25" y1="333.25" y2="356"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="333.25" y2="356"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="333.25" y2="333.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="356" y2="356"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="333.25" y2="356"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="390.25" x2="390.25" y1="333.25" y2="356"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="356" y2="356"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="378.75" y2="378.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="28.25" y1="356" y2="378.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="356" y2="378.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="356" y2="356"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="378.75" y2="378.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="356" y2="378.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="390.25" x2="390.25" y1="356" y2="378.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="378.75" y2="378.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="401.5" y2="401.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="28.25" y1="378.75" y2="401.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="378.75" y2="401.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="378.75" y2="378.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="401.5" y2="401.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="378.75" y2="401.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="390.25" x2="390.25" y1="378.75" y2="401.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="401.5" y2="401.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="209.25" y1="424.25" y2="424.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.25" x2="28.25" y1="401.5" y2="424.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="401.5" y2="424.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="401.5" y2="401.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="390.25" y1="424.25" y2="424.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="209.25" x2="209.25" y1="401.5" y2="424.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="390.25" x2="390.25" y1="401.5" y2="424.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,74.75,273.25) scale(1,1) translate(0,0)" writing-mode="lr" x="32.25" xml:space="preserve" y="277.75" zvalue="21">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,255.25,273.25) scale(1,1) translate(0,0)" writing-mode="lr" x="212.75" xml:space="preserve" y="277.75" zvalue="22">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.9375,346.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.94" xml:space="preserve" y="351" zvalue="23">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,73.75,299.25) scale(1,1) translate(0,0)" writing-mode="lr" x="31.25" xml:space="preserve" y="303.75" zvalue="24">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,254.25,299.25) scale(1,1) translate(0,0)" writing-mode="lr" x="211.75" xml:space="preserve" y="303.75" zvalue="25">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,73.75,322.25) scale(1,1) translate(0,0)" writing-mode="lr" x="31.25" xml:space="preserve" y="326.75" zvalue="26">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,253.75,321.25) scale(1,1) translate(0,0)" writing-mode="lr" x="211.25" xml:space="preserve" y="325.75" zvalue="27">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.875,382.25) scale(1,1) translate(0,0)" writing-mode="lr" x="684.88" xml:space="preserve" y="386.75" zvalue="30">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,614.625,476.25) scale(1,1) translate(0,0)" writing-mode="lr" x="614.63" xml:space="preserve" y="480.75" zvalue="31">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,627.125,285.25) scale(1,1) translate(0,0)" writing-mode="lr" x="627.13" xml:space="preserve" y="289.75" zvalue="32">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,652.25,129.625) scale(1,1) translate(0,0)" writing-mode="lr" x="652.25" xml:space="preserve" y="134.13" zvalue="33">35kV越盛弄相硅厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,594.875,400.75) scale(1,1) translate(0,0)" writing-mode="lr" x="594.88" xml:space="preserve" y="405.25" zvalue="34">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,592.375,305.75) scale(1,1) translate(0,0)" writing-mode="lr" x="592.38" xml:space="preserve" y="310.25" zvalue="35">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.625,209.5) scale(1,1) translate(0,0)" writing-mode="lr" x="573.63" xml:space="preserve" y="214" zvalue="36">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1561.38,497.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1561.38" xml:space="preserve" y="501.75" zvalue="43">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.875,678.25) scale(1,1) translate(0,0)" writing-mode="lr" x="703.88" xml:space="preserve" y="682.75" zvalue="45">304</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" x="679.25" xml:space="preserve" y="846.75" zvalue="46">#1环保动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="679.25" xml:space="preserve" y="862.75" zvalue="46">1250kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.5,592) scale(1,1) translate(0,0)" writing-mode="lr" x="695.5" xml:space="preserve" y="596.5" zvalue="47">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,633.875,617) scale(1,1) translate(0,0)" writing-mode="lr" x="633.88" xml:space="preserve" y="621.5" zvalue="48">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" x="855.4296875" xml:space="preserve" y="784.15625" zvalue="55">#1炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="855.4296875" xml:space="preserve" y="800.15625" zvalue="55">12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.042,678.25) scale(1,1) translate(0,0)" writing-mode="lr" x="953.04" xml:space="preserve" y="682.75" zvalue="56">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.917,589.5) scale(1,1) translate(0,0)" writing-mode="lr" x="945.92" xml:space="preserve" y="594" zvalue="57">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.875,622.639) scale(1,1) translate(0,0)" writing-mode="lr" x="891.88" xml:space="preserve" y="627.14" zvalue="58">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,930.434,915) scale(1,1) translate(0,0)" writing-mode="lr" x="930.4299999999999" xml:space="preserve" y="919.5" zvalue="63">#1电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" x="1105.984375" xml:space="preserve" y="784.15625" zvalue="71">#1炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1105.984375" xml:space="preserve" y="800.15625" zvalue="71">12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1203.61,678.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1203.61" xml:space="preserve" y="682.75" zvalue="73">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.08,587) scale(1,1) translate(0,0)" writing-mode="lr" x="1200.08" xml:space="preserve" y="591.5" zvalue="75">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1139.88,619.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.88" xml:space="preserve" y="624" zvalue="77">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1181,915) scale(1,1) translate(0,0)" writing-mode="lr" x="1181" xml:space="preserve" y="919.5" zvalue="83">#2电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.55,924.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.549107142857" xml:space="preserve" y="928.75" zvalue="99">#1电容器组3000kVar</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" x="1271" xml:space="preserve" y="913.625" zvalue="103">#2电容器组</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1271" xml:space="preserve" y="929.625" zvalue="103">3000kVar</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430.83,848.281) scale(1,1) translate(-9.21814e-13,0)" writing-mode="lr" x="1430.83" xml:space="preserve" y="852.78" zvalue="105">#1厂变0.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1454.88,593.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1454.88" xml:space="preserve" y="597.75" zvalue="106">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381.38,640.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1381.38" xml:space="preserve" y="645.25" zvalue="107">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.375,475.75) scale(1,1) translate(0,0)" writing-mode="lr" x="927.38" xml:space="preserve" y="480.25" zvalue="111">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" x="993.5859375" xml:space="preserve" y="268.2717867902573" zvalue="113">35kV母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="993.5859375" xml:space="preserve" y="284.2717867902573" zvalue="113">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.9,444.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.9" xml:space="preserve" y="449.02" zvalue="116">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,926,384) scale(1,1) translate(0,0)" writing-mode="lr" x="926" xml:space="preserve" y="388.5" zvalue="120">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,159.139,1030.14) scale(1,1) translate(0,0)" writing-mode="lr" x="159.14" xml:space="preserve" y="1036.14" zvalue="163">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="73.56" x="65.44" y="198.5" zvalue="16"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="29">
   <use class="kv35" height="21" transform="rotate(0,652.25,377) scale(1.25,1.25) translate(-129.075,-72.775)" width="11" x="645.375" xlink:href="#Breaker:SVG标准开关_0" y="363.875" zvalue="29"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924532043781" ObjectName="35kV越盛弄相硅厂线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473924532043781"/></metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,652.25,377) scale(1.25,1.25) translate(-129.075,-72.775)" width="11" x="645.375" y="363.875"/></g>
  <g id="52">
   <use class="kv35" height="21" transform="rotate(0,681.25,679.25) scale(1.25,1.25) translate(-134.875,-133.225)" width="11" x="674.375" xlink:href="#Breaker:SVG标准开关_0" y="666.125" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924532109317" ObjectName="#1环保动力变304断路器"/>
   <cge:TPSR_Ref TObjectID="6473924532109317"/></metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,681.25,679.25) scale(1.25,1.25) translate(-134.875,-133.225)" width="11" x="674.375" y="666.125"/></g>
  <g id="70">
   <use class="kv35" height="21" transform="rotate(0,931.667,679.25) scale(1.25,1.25) translate(-184.958,-133.225)" width="11" x="924.7916666666666" xlink:href="#Breaker:SVG标准开关_0" y="666.125" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924532174853" ObjectName="#1炉变301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924532174853"/></metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,931.667,679.25) scale(1.25,1.25) translate(-184.958,-133.225)" width="11" x="924.7916666666666" y="666.125"/></g>
  <g id="107">
   <use class="kv35" height="21" transform="rotate(0,1182.24,679.25) scale(1.25,1.25) translate(-235.072,-133.225)" width="11" x="1175.361244393598" xlink:href="#Breaker:SVG标准开关_0" y="666.125" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924532240389" ObjectName="#2炉变302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924532240389"/></metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1182.24,679.25) scale(1.25,1.25) translate(-235.072,-133.225)" width="11" x="1175.361244393598" y="666.125"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="31">
   <use class="kv35" height="30" transform="rotate(0,652.25,477.25) scale(2.5,1.25) translate(-380.1,-91.7)" width="15" x="633.5" xlink:href="#Disconnector:刀闸_0" y="458.5" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887272965" ObjectName="35kV越盛弄相硅厂线3711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449887272965"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,652.25,477.25) scale(2.5,1.25) translate(-380.1,-91.7)" width="15" x="633.5" y="458.5"/></g>
  <g id="32">
   <use class="kv35" height="30" transform="rotate(0,652.25,286.25) scale(2.5,1.25) translate(-380.1,-53.5)" width="15" x="633.5" xlink:href="#Disconnector:刀闸_0" y="267.5" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887338502" ObjectName="35kV越盛弄相硅厂线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449887338502"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,652.25,286.25) scale(2.5,1.25) translate(-380.1,-53.5)" width="15" x="633.5" y="267.5"/></g>
  <g id="56">
   <use class="kv35" height="30" transform="rotate(0,681.25,591.75) scale(2.5,1.25) translate(-397.5,-114.6)" width="15" x="662.5" xlink:href="#Disconnector:刀闸_0" y="573" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887928326" ObjectName="#1环保动力变3041隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449887928326"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,681.25,591.75) scale(2.5,1.25) translate(-397.5,-114.6)" width="15" x="662.5" y="573"/></g>
  <g id="72">
   <use class="kv35" height="30" transform="rotate(0,931.667,591.75) scale(2.5,1.25) translate(-547.75,-114.6)" width="15" x="912.9166666666666" xlink:href="#Disconnector:刀闸_0" y="573" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888124933" ObjectName="#1炉变3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449888124933"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.667,591.75) scale(2.5,1.25) translate(-547.75,-114.6)" width="15" x="912.9166666666666" y="573"/></g>
  <g id="106">
   <use class="kv35" height="30" transform="rotate(0,1182.08,591.75) scale(2.5,1.25) translate(-698,-114.6)" width="15" x="1163.333333333333" xlink:href="#Disconnector:刀闸_0" y="573" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888583685" ObjectName="#2炉变3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449888583685"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1182.08,591.75) scale(2.5,1.25) translate(-698,-114.6)" width="15" x="1163.333333333333" y="573"/></g>
  <g id="127">
   <use class="kv35" height="30" transform="rotate(0,1432.5,591.75) scale(2.5,1.25) translate(-848.25,-114.6)" width="15" x="1413.75" xlink:href="#Disconnector:刀闸_0" y="573" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888845829" ObjectName="#1厂变3031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449888845829"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1432.5,591.75) scale(2.5,1.25) translate(-848.25,-114.6)" width="15" x="1413.75" y="573"/></g>
  <g id="139">
   <use class="kv35" height="30" transform="rotate(0,985.61,442.951) scale(1.58535,1.16259) translate(-359.52,-59.508)" width="15" x="973.7197831123441" xlink:href="#Disconnector:刀闸_0" y="425.512040690463" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449889173509" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449889173509"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,985.61,442.951) scale(1.58535,1.16259) translate(-359.52,-59.508)" width="15" x="973.7197831123441" y="425.512040690463"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="37">
   <use class="kv35" height="30" transform="rotate(90,592.25,420.5) scale(1.04167,0.833333) translate(-23.44,81.6)" width="12" x="586" xlink:href="#GroundDisconnector:地刀12_0" y="408" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887535109" ObjectName="35kV越盛弄相硅厂线37117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449887535109"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,592.25,420.5) scale(1.04167,0.833333) translate(-23.44,81.6)" width="12" x="586" y="408"/></g>
  <g id="39">
   <use class="kv35" height="30" transform="rotate(90,592.25,328) scale(1.04167,0.833333) translate(-23.44,63.1)" width="12" x="586" xlink:href="#GroundDisconnector:地刀12_0" y="315.5" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887666181" ObjectName="35kV越盛弄相硅厂线37160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449887666181"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,592.25,328) scale(1.04167,0.833333) translate(-23.44,63.1)" width="12" x="586" y="315.5"/></g>
  <g id="41">
   <use class="kv35" height="30" transform="rotate(90,577.25,229.25) scale(1.04167,0.833333) translate(-22.84,43.35)" width="12" x="571" xlink:href="#GroundDisconnector:地刀12_0" y="216.75" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887797253" ObjectName="35kV越盛弄相硅厂线37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449887797253"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,577.25,229.25) scale(1.04167,0.833333) translate(-22.84,43.35)" width="12" x="571" y="216.75"/></g>
  <g id="58">
   <use class="kv35" height="30" transform="rotate(270,635,635.5) scale(-1.25,-1.25) translate(-1141.5,-1140.15)" width="12" x="627.5" xlink:href="#GroundDisconnector:地刀12_0" y="616.75" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888059397" ObjectName="#1环保动力变30417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449888059397"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,635,635.5) scale(-1.25,-1.25) translate(-1141.5,-1140.15)" width="12" x="627.5" y="616.75"/></g>
  <g id="74">
   <use class="kv35" height="30" transform="rotate(90,886.75,638.639) scale(1.25,1.25) translate(-175.85,-123.978)" width="12" x="879.25" xlink:href="#GroundDisconnector:地刀12_0" y="619.8893337991232" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888256005" ObjectName="#1炉变30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449888256005"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,886.75,638.639) scale(1.25,1.25) translate(-175.85,-123.978)" width="12" x="879.25" y="619.8893337991232"/></g>
  <g id="105">
   <use class="kv35" height="30" transform="rotate(90,1134.75,635.5) scale(1.25,1.25) translate(-225.45,-123.35)" width="12" x="1127.25" xlink:href="#GroundDisconnector:地刀12_0" y="616.75" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888518149" ObjectName="#2炉变30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449888518149"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1134.75,635.5) scale(1.25,1.25) translate(-225.45,-123.35)" width="12" x="1127.25" y="616.75"/></g>
  <g id="129">
   <use class="kv35" height="30" transform="rotate(270,1391.25,660.5) scale(-1.25,-1.25) translate(-2502.75,-1185.15)" width="12" x="1383.75" xlink:href="#GroundDisconnector:地刀12_0" y="641.75" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888976901" ObjectName="#1厂变30317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449888976901"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1391.25,660.5) scale(-1.25,-1.25) translate(-2502.75,-1185.15)" width="12" x="1383.75" y="641.75"/></g>
  <g id="134">
   <use class="kv35" height="30" transform="rotate(90,932.25,495.5) scale(1.25,1.25) translate(-184.95,-95.35)" width="12" x="924.75" xlink:href="#GroundDisconnector:地刀12_0" y="476.75" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449889107973" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449889107973"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,932.25,495.5) scale(1.25,1.25) translate(-184.95,-95.35)" width="12" x="924.75" y="476.75"/></g>
  <g id="142">
   <use class="kv35" height="30" transform="rotate(90,932.25,407.5) scale(1.25,1.25) translate(-184.95,-77.75)" width="12" x="924.75" xlink:href="#GroundDisconnector:地刀12_0" y="388.75" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449889370117" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449889370117"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,932.25,407.5) scale(1.25,1.25) translate(-184.95,-77.75)" width="12" x="924.75" y="388.75"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="43">
   <path class="kv35" d="M 652.04 195.08 L 652.04 268.12" stroke-width="1" zvalue="36"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.04 195.08 L 652.04 268.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 652.4 304.68 L 652.4 364.5" stroke-width="1" zvalue="37"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="29@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.4 304.68 L 652.4 364.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv35" d="M 652.25 389.5 L 652.25 459.12" stroke-width="1" zvalue="38"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@1" LinkObjectIDznd="31@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.25 389.5 L 652.25 459.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv35" d="M 589.19 229.24 L 652.04 229.24" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.19 229.24 L 652.04 229.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 604.19 327.99 L 652.25 327.99" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.19 327.99 L 652.25 327.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv35" d="M 604.19 420.49 L 652.25 420.49" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="45" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.19 420.49 L 652.25 420.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv35" d="M 652.4 495.68 L 652.4 539.5" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@1" LinkObjectIDznd="49@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.4 495.68 L 652.4 539.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv35" d="M 681.47 573.62 L 681.47 539.5" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.47 573.62 L 681.47 539.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv35" d="M 681.4 610.18 L 681.4 666.75" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="52@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.4 610.18 L 681.4 666.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv35" d="M 681.25 691.75 L 681.25 733.12" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.25 691.75 L 681.25 733.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv35" d="M 652.9 635.49 L 681.25 635.49" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="61" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.9 635.49 L 681.25 635.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv35" d="M 931.89 573.62 L 931.89 539.5" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="49@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.89 573.62 L 931.89 539.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 931.82 610.18 L 931.82 666.75" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@1" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.82 610.18 L 931.82 666.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 931.67 734.31 L 931.67 691.75" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.67 734.31 L 931.67 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv35" d="M 904.65 638.63 L 931.67 638.63" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.65 638.63 L 931.67 638.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 1182.3 573.62 L 1182.3 539.5" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="49@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1182.3 573.62 L 1182.3 539.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 1182.24 610.18 L 1182.24 666.75" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@1" LinkObjectIDznd="107@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1182.24 610.18 L 1182.24 666.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv35" d="M 1182.24 734.31 L 1182.24 691.75" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="107@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1182.24 734.31 L 1182.24 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1152.65 635.49 L 1182.24 635.49" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 1152.65 635.49 L 1182.24 635.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 1182.25 848.44 L 1182.25 794.61" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="108@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1182.25 848.44 L 1182.25 794.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 931.68 848.44 L 931.68 794.61" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.68 848.44 L 931.68 794.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 965.42 832.28 L 931.68 832.28" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.42 832.28 L 931.68 832.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 1238.79 827.65 L 1182.25 827.65" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="99" MaxPinNum="2"/>
   </metadata>
  <path d="M 1238.79 827.65 L 1182.25 827.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv35" d="M 1432.72 573.62 L 1432.72 539.5" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="49@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.72 573.62 L 1432.72 539.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv35" d="M 1432.65 610.18 L 1432.65 734.09" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@1" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.65 610.18 L 1432.65 734.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv35" d="M 1409.15 660.49 L 1432.5 660.49" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="132" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.15 660.49 L 1432.5 660.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv35" d="M 985.71 460.09 L 985.71 539.5" stroke-width="1" zvalue="121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@1" LinkObjectIDznd="49@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 985.71 460.09 L 985.71 539.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv35" d="M 950.15 495.49 L 985.71 495.49" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="145" MaxPinNum="2"/>
   </metadata>
  <path d="M 950.15 495.49 L 985.71 495.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv35" d="M 985.65 373.38 L 985.75 426.09" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="139@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 985.65 373.38 L 985.75 426.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv35" d="M 950.15 407.49 L 985.72 407.49" stroke-width="1" zvalue="128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 950.15 407.49 L 985.72 407.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="49">
   <path class="kv35" d="M 552.5 539.5 L 1533.75 539.5" stroke-width="4" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674248622084" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674248622084"/></metadata>
  <path d="M 552.5 539.5 L 1533.75 539.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="54">
   <use class="kv35" height="49" transform="rotate(0,683.125,768.876) scale(-2.13542,1.48983) translate(-989.402,-240.791)" width="24" x="657.5" xlink:href="#EnergyConsumer:厂用变负荷_0" y="732.375" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887862789" ObjectName="#1环保动力变"/>
   <cge:TPSR_Ref TObjectID="6192449887862789"/></metadata>
  <rect fill="white" height="49" opacity="0" stroke="white" transform="rotate(0,683.125,768.876) scale(-2.13542,1.48983) translate(-989.402,-240.791)" width="24" x="657.5" y="732.375"/></g>
  <g id="80">
   <use class="kv10" height="30" transform="rotate(0,931.684,869.25) scale(1.5625,-1.54167) translate(-332.031,-1424.96)" width="12" x="922.3092678955368" xlink:href="#EnergyConsumer:负荷_0" y="846.125" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888321541" ObjectName="#1电炉"/>
   <cge:TPSR_Ref TObjectID="6192449888321541"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.684,869.25) scale(1.5625,-1.54167) translate(-332.031,-1424.96)" width="12" x="922.3092678955368" y="846.125"/></g>
  <g id="100">
   <use class="kv10" height="30" transform="rotate(0,1182.25,869.25) scale(1.5625,-1.54167) translate(-422.236,-1424.96)" width="12" x="1172.878845622468" xlink:href="#EnergyConsumer:负荷_0" y="846.125" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888387077" ObjectName="#2电炉"/>
   <cge:TPSR_Ref TObjectID="6192449888387077"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1182.25,869.25) scale(1.5625,-1.54167) translate(-422.236,-1424.96)" width="12" x="1172.878845622468" y="846.125"/></g>
  <g id="125">
   <use class="kv35" height="30" transform="rotate(0,1442.35,770.188) scale(2.44048,2.43338) translate(-836.214,-432.178)" width="21" x="1416.726190476191" xlink:href="#EnergyConsumer:糖厂负荷_0" y="733.6867732558142" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888780293" ObjectName="#1厂变"/>
   <cge:TPSR_Ref TObjectID="6192449888780293"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1442.35,770.188) scale(2.44048,2.43338) translate(-836.214,-432.178)" width="21" x="1416.726190476191" y="733.6867732558142"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="68">
   <g id="680">
    <use class="kv35" height="20" transform="rotate(0,931.24,765.656) scale(5.125,3.65007) translate(-728.909,-529.391)" width="10" x="905.61" xlink:href="#PowerTransformer2:D-D（规范制图）_0" y="729.16" zvalue="54"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874444439555" ObjectName="35"/>
    </metadata>
   </g>
   <g id="681">
    <use class="kv10" height="20" transform="rotate(0,931.24,765.656) scale(5.125,3.65007) translate(-728.909,-529.391)" width="10" x="905.61" xlink:href="#PowerTransformer2:D-D（规范制图）_1" y="729.16" zvalue="54"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874444505091" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399454490627" ObjectName="#1炉变"/>
   <cge:TPSR_Ref TObjectID="6755399454490627"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,931.24,765.656) scale(5.125,3.65007) translate(-728.909,-529.391)" width="10" x="905.61" y="729.16"/></g>
  <g id="108">
   <g id="1080">
    <use class="kv35" height="20" transform="rotate(0,1181.81,765.656) scale(5.125,3.65007) translate(-930.587,-529.391)" width="10" x="1156.18" xlink:href="#PowerTransformer2:D-D（规范制图）_0" y="729.16" zvalue="70"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874444570627" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1081">
    <use class="kv10" height="20" transform="rotate(0,1181.81,765.656) scale(5.125,3.65007) translate(-930.587,-529.391)" width="10" x="1156.18" xlink:href="#PowerTransformer2:D-D（规范制图）_1" y="729.16" zvalue="70"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874444636163" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399454556163" ObjectName="#2炉变"/>
   <cge:TPSR_Ref TObjectID="6755399454556163"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1181.81,765.656) scale(5.125,3.65007) translate(-930.587,-529.391)" width="10" x="1156.18" y="729.16"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="117">
   <use class="kv10" height="35" transform="rotate(0,1000.6,863.938) scale(3.51786,3.51786) translate(-684.691,-574.289)" width="25" x="956.625" xlink:href="#Compensator:电容器组带熔断器_0" y="802.375" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888649221" ObjectName="#1电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449888649221"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1000.6,863.938) scale(3.51786,3.51786) translate(-684.691,-574.289)" width="25" x="956.625" y="802.375"/></g>
  <g id="122">
   <use class="kv10" height="35" transform="rotate(0,1271.47,857.062) scale(3.26786,3.26786) translate(-854.04,-555.105)" width="25" x="1230.625" xlink:href="#Compensator:电容器组带熔断器_0" y="799.875" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449888714757" ObjectName="#2电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449888714757"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1271.47,857.062) scale(3.26786,3.26786) translate(-854.04,-555.105)" width="25" x="1230.625" y="799.875"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="140">
   <use class="kv35" height="40" transform="rotate(0,1005.44,335.794) scale(1.97823,-1.97823) translate(-477.62,-485.974)" width="40" x="965.8709230747786" xlink:href="#Accessory:五卷PT_0" y="296.2293327484349" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449889239045" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1005.44,335.794) scale(1.97823,-1.97823) translate(-477.62,-485.974)" width="40" x="965.8709230747786" y="296.2293327484349"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="153" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1022.99,668.656) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.41" xml:space="preserve" y="674.9299999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125848481796" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="154" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1021.74,695.656) scale(1,1) translate(0,0)" writing-mode="lr" x="1021.16" xml:space="preserve" y="701.9299999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125848547332" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="155" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1022.99,722.656) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.41" xml:space="preserve" y="728.9299999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125848743940" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="156" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1278.56,697.406) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.98" xml:space="preserve" y="703.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125853396996" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="157">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="157" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1286.06,729.406) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.48" xml:space="preserve" y="735.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125853462532" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="158" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292.31,761.406) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.73" xml:space="preserve" y="767.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125853659140" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="159" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1139.25,340.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1138.78" xml:space="preserve" y="345.03" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125846515716" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="160" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1138,372.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1137.53" xml:space="preserve" y="377.03" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125846581252" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="161" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1136.75,431.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.28" xml:space="preserve" y="436.53" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125846646788" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="162" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1143,400.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1142.53" xml:space="preserve" y="405.03" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125846777860" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="163" prefix="F:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1133.12,456.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1132.66" xml:space="preserve" y="461.28" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125846908932" ObjectName="F"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="164" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,761.875,701.126) scale(1,1) translate(0,0)" writing-mode="lr" x="761.41" xml:space="preserve" y="705.9" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125847564292" ObjectName="P"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="165" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,766.875,741.876) scale(1,1) translate(0,0)" writing-mode="lr" x="766.41" xml:space="preserve" y="746.65" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125847629828" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="166" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,766.875,778.876) scale(1,1) translate(0,0)" writing-mode="lr" x="766.41" xml:space="preserve" y="783.65" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125847695364" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="167" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,761,121.25) scale(1,1) translate(0,0)" writing-mode="lr" x="760.53" xml:space="preserve" y="126.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125845467140" ObjectName="P"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="168" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,758.5,155.75) scale(1,1) translate(0,0)" writing-mode="lr" x="758.03" xml:space="preserve" y="160.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125845532676" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="169" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,761,187.75) scale(1,1) translate(0,0)" writing-mode="lr" x="760.53" xml:space="preserve" y="192.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125845598212" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="170">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="170" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.357,273.75) scale(1,1) translate(0,0)" writing-mode="lr" x="159.51" xml:space="preserve" y="280.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125911724036" ObjectName=""/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328.472,270.944) scale(1,1) translate(-2.42448e-13,0)" writing-mode="lr" x="328.71" xml:space="preserve" y="277.43" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125911789574" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,151.722,297.694) scale(1,1) translate(-8.54625e-14,0)" writing-mode="lr" x="151.96" xml:space="preserve" y="304.18" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328.472,298.944) scale(1,1) translate(-2.42448e-13,0)" writing-mode="lr" x="328.71" xml:space="preserve" y="305.43" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125911789574" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="174">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328.472,322.944) scale(1,1) translate(-2.42448e-13,0)" writing-mode="lr" x="328.71" xml:space="preserve" y="329.43" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125911789574" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="175">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,151.722,321.694) scale(1,1) translate(-8.54625e-14,0)" writing-mode="lr" x="151.96" xml:space="preserve" y="328.18" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="176">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,151.722,345.694) scale(1,1) translate(-8.54625e-14,0)" writing-mode="lr" x="151.96" xml:space="preserve" y="352.18" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125846908932" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="299">
   <use height="30" transform="rotate(0,364.734,210.77) scale(0.708333,0.665547) translate(145.81,100.9)" width="30" x="354.11" xlink:href="#State:红绿圆(方形)_0" y="200.79" zvalue="160"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374889975811" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,364.734,210.77) scale(0.708333,0.665547) translate(145.81,100.9)" width="30" x="354.11" y="200.79"/></g>
  <g id="276">
   <use height="30" transform="rotate(0,265.109,210.77) scale(0.708333,0.665547) translate(104.788,100.9)" width="30" x="254.48" xlink:href="#State:红绿圆(方形)_0" y="200.79" zvalue="161"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,265.109,210.77) scale(0.708333,0.665547) translate(104.788,100.9)" width="30" x="254.48" y="200.79"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,345.812,143.464) scale(1.27778,1.03333) translate(-62.6766,-4.12787)" width="90" x="288.31" xlink:href="#State:全站检修_0" y="127.96" zvalue="167"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549589704706" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,345.812,143.464) scale(1.27778,1.03333) translate(-62.6766,-4.12787)" width="90" x="288.31" y="127.96"/></g>
 </g>
</svg>