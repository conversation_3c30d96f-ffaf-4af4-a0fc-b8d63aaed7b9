<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549589573634" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:隔离变_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="14.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="11.63558333333333" y1="8.877992633517497" y2="11.52992633517496"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="14.86725" y1="5.231583793738499" y2="8.877992633517501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="18.32975" y1="8.87799263351749" y2="11.41942909760589"/>
   <ellipse cx="15.08" cy="19.29" fill-opacity="0" rx="6.93" ry="6.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.08" cy="8.550000000000001" fill-opacity="0" rx="6.93" ry="6.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="14.86725" y1="15.94981583793738" y2="19.2647329650092"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725000000001" x2="11.63558333333334" y1="19.26473296500919" y2="21.91666666666665"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="18.32975" y1="19.26473296500921" y2="21.8061694290976"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="Accessory:带电容器的互感器_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="0" x="16.64454135671556" xlink:href="#terminal" y="1.899791906472284"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.80000022125245" x2="25.80000022125245" y1="32.55513439909467" y2="52.24553022481447"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.30000001144409" x2="16.69999987411499" y1="56.74195385044482" y2="56.74195385044482"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.30000001144409" x2="16.69999987411499" y1="27.89999991989135" y2="27.89999991989135"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.69999987411499" x2="16.69999987411499" y1="54.30000092697147" y2="58.70000109481816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.29999978256225" x2="14.29999978256225" y1="57.60000105285648" y2="58.80000109863285"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.30000001144409" x2="20.30000001144409" y1="55.20000096130374" y2="56.40000100708012"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.29999978256225" x2="19.09999996566772" y1="58.80000109863285" y2="58.80000109863285"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.30000001144409" x2="20.30000001144409" y1="27.69999991226196" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.09999996566772" x2="19.09999996566772" y1="57.60000105285648" y2="58.80000109863285"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.71484703625342" x2="16.71484703625342" y1="21.43967834040884" y2="13.28421951401894"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.70078590034584" x2="16.70078590034584" y1="1.971708915871165" y2="4.848389291823086"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.72969450356765" x2="16.71484703625341" y1="13.1188453726847" y2="13.1188453726847"/>
   <path d="M 16.75 4.70456 A 8.05 8.34237 -360 1 0 24.8 13.0469" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.80561785567208" x2="16.80561785567208" y1="21.39999967193602" y2="52.4169428361697"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.79999987792969" x2="30.90000041580202" y1="24.99999980926513" y2="24.99999980926513"/>
   <rect fill-opacity="0" height="14.97" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,16.8,39.79) scale(1,1) translate(0,0)" width="5" x="14.3" y="32.31"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.08651666649808" x2="18.53220968183597" y1="54.23949554462703" y2="54.23949554462703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.08651666649808" x2="18.53220968183597" y1="52.69210819007492" y2="52.69210819007492"/>
   <path d="M 20.023 29.7393 A 4.26667 4.20359 0 0 1 20.2455 38.1282" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 20.023 38.3334 A 4.26667 4.20359 0 0 1 20.2455 46.7222" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 20.023 46.7406 A 4.26667 4.20359 0 0 1 20.2455 55.1294" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314756" x2="31.01691584314756" y1="45.66453494179018" y2="45.66453494179018"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314755" x2="28.98706501944623" y1="39.11821372069851" y2="36.0903078306318"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314756" x2="31.01691584314756" y1="45.66453494179018" y2="49.29802200987024"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.40055315020022" x2="32.85881751650618" y1="50.74645886254542" y2="50.74645886254542"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314756" x2="31.01691584314756" y1="29.51570352810102" y2="38.98975465842889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.04897771888256" x2="32.4265344707179" y1="52.14088920665508" y2="52.14088920665508"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314757" x2="33.0467666668489" y1="39.11821372069851" y2="36.0903078306318"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.01691584314756" x2="31.01691584314756" y1="24.99999980926513" y2="29.51570352810103"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="28.53598705862374" x2="33.72338360808266" y1="49.35202851843577" y2="49.35202851843577"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,31,37.59) scale(1,1) translate(0,0)" width="6.8" x="27.6" y="29.52"/>
  </symbol>
  <symbol id="EnergyConsumer:接地站用变两卷_0" viewBox="0,0,18,30">
   <use terminal-index="0" type="0" x="8.949999999999999" xlink:href="#terminal" y="0.5"/>
   <ellipse cx="9.039999999999999" cy="8.970000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.970000000000001" cy="21.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670173" x2="9.035256190670173" y1="4.6" y2="8.708948332339869"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670172" x2="5.05" y1="8.733948332339875" y2="11.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.063029030724982" x2="13.15" y1="8.689905009168513" y2="11.5"/>
   <path d="M 9 19.8333 L 4.08333 25.4167 L 14.0833 25.4167 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666666" x2="27.83333333333334" y1="6.416666666666666" y2="22.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.5" x2="28.5" y1="3.833333333333332" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.16666666666667" x2="35.5" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.5" x2="21.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV越盛硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="277.25" x="57.75" xlink:href="logo.png" y="36.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.375,66.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.38" xml:space="preserve" y="70.25" zvalue="47"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,191,63.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="72.94" zvalue="48">35kV越盛硅厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="69" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.9688,187.25) scale(1,1) translate(0,0)" width="73.56" x="50.19" y="175.25" zvalue="61"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.9688,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.97" xml:space="preserve" y="191.75" zvalue="61">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.636,174.26) scale(1,1) translate(0,0)" writing-mode="lr" x="899.64" xml:space="preserve" y="178.76" zvalue="2">35kV越盛硅厂Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.877,316.517) scale(1,1) translate(0,-4.09334e-13)" writing-mode="lr" x="951.88" xml:space="preserve" y="321.02" zvalue="4">3716</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,658.525,380.837) scale(1,1) translate(-2.73659e-13,-4.92709e-13)" writing-mode="lr" x="658.53" xml:space="preserve" y="385.34" zvalue="5">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,673.318,711.125) scale(1,1) translate(-1.36061e-13,7.60557e-14)" writing-mode="lr" x="673.3175367667834" xml:space="preserve" y="715.6246035837893" zvalue="8">#1电炉变  12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,642.68,564.08) scale(1,1) translate(9.80913e-13,1.23192e-13)" writing-mode="lr" x="642.6799999999999" xml:space="preserve" y="568.58" zvalue="9">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,640.975,492.093) scale(1,1) translate(8.50862e-13,1.07208e-13)" writing-mode="lr" x="640.97" xml:space="preserve" y="496.59" zvalue="10">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" x="901.953125" xml:space="preserve" y="627.2561617902574" zvalue="16">35kVⅠ段母线电压互感</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="901.953125" xml:space="preserve" y="643.2561617902574" zvalue="16">器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770.774,574.109) scale(1,1) translate(6.73776e-13,1.25419e-13)" writing-mode="lr" x="770.77" xml:space="preserve" y="578.61" zvalue="17">373</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.715,488.528) scale(1,1) translate(1.02771e-12,1.06416e-13)" writing-mode="lr" x="773.72" xml:space="preserve" y="493.03" zvalue="19">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" x="808.2734375" xml:space="preserve" y="727.0537492667791" zvalue="22">#1无功补偿装置  </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="808.2734375" xml:space="preserve" y="743.0537492667791" zvalue="22">10020kVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,863.398,494.768) scale(1,1) translate(0,1.07802e-13)" writing-mode="lr" x="863.4" xml:space="preserve" y="499.27" zvalue="27">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1001.58,486.745) scale(1,1) translate(1.33128e-12,1.0602e-13)" writing-mode="lr" x="1001.58" xml:space="preserve" y="491.24" zvalue="30">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031.17,550.931) scale(1,1) translate(3.3959e-13,-1.20273e-13)" writing-mode="lr" x="1031.17" xml:space="preserve" y="555.4299999999999" zvalue="31">381</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1015.89,678.836) scale(1,1) translate(-2.15665e-13,4.37453e-13)" writing-mode="lr" x="1015.885064530768" xml:space="preserve" y="683.3355337876488" zvalue="32">#1厂用变  800kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1658.29,487.745) scale(1,1) translate(0,0)" writing-mode="lr" x="1658.29" xml:space="preserve" y="492.24" zvalue="39">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1687.89,551.931) scale(1,1) translate(0,0)" writing-mode="lr" x="1687.89" xml:space="preserve" y="556.4299999999999" zvalue="40">383</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1672.6,679.836) scale(1,1) translate(-7.22968e-13,4.38119e-13)" writing-mode="lr" x="1672.598638515233" xml:space="preserve" y="684.3355337876488" zvalue="43">#3厂用变  800kVA</text>
  <line fill="none" id="81" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="391.75" x2="391.75" y1="4.75" y2="1034.75" zvalue="49"/>
  <line fill="none" id="79" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75000000000045" x2="384.75" y1="140.6204926140824" y2="140.6204926140824" zvalue="51"/>
  <line fill="none" id="78" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="52"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="993.0816" y2="1021"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,946) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="952" zvalue="54">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.8889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="52.89" xml:space="preserve" y="984.89" zvalue="55">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="234.89" xml:space="preserve" y="984.89" zvalue="56">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.8889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="51.89" xml:space="preserve" y="1012.89" zvalue="57">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="233.89" xml:space="preserve" y="1012.89" zvalue="58">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,560.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="565" zvalue="60">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233,947.5) scale(1,1) translate(0,0)" writing-mode="lr" x="233" xml:space="preserve" y="952" zvalue="62">YueSheng-01-2024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="63">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="64">通道</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="237" y2="237"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="263" y2="263"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="237" y2="263"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="237" y2="263"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="237" y2="237"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="263" y2="263"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="237" y2="263"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="237" y2="263"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="263" y2="263"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="287.25" y2="287.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="263" y2="287.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="263" y2="287.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="263" y2="263"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="287.25" y2="287.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="263" y2="287.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="263" y2="287.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="287.25" y2="287.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="310" y2="310"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="287.25" y2="310"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="287.25" y2="310"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="287.25" y2="287.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="310" y2="310"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="287.25" y2="310"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="287.25" y2="310"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="310" y2="310"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="332.75" y2="332.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="310" y2="332.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="310" y2="332.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="310" y2="310"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="332.75" y2="332.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="310" y2="332.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="310" y2="332.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="332.75" y2="332.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="355.5" y2="355.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="332.75" y2="355.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="332.75" y2="355.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="332.75" y2="332.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="355.5" y2="355.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="332.75" y2="355.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="332.75" y2="355.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="355.5" y2="355.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="378.25" y2="378.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="355.5" y2="378.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="355.5" y2="378.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="355.5" y2="355.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="378.25" y2="378.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="355.5" y2="378.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="355.5" y2="378.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="378.25" y2="378.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="401" y2="401"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="378.25" y2="401"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="378.25" y2="401"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="378.25" y2="378.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="401" y2="401"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="378.25" y2="401"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="378.25" y2="401"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,250) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="254.5" zvalue="66">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,250) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="254.5" zvalue="67">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.6875,323.25) scale(1,1) translate(0,0)" writing-mode="lr" x="74.69" xml:space="preserve" y="327.75" zvalue="68">35kVⅠ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,276) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="280.5" zvalue="69">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,276) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="280.5" zvalue="70">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,299) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="303.5" zvalue="71">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,298) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="302.5" zvalue="72">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1431.87,174.255) scale(1,1) translate(0,0)" writing-mode="lr" x="1431.87" xml:space="preserve" y="178.76" zvalue="75">35kV越盛硅厂Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1474.82,312.982) scale(1,1) translate(1.77211e-12,6.74382e-14)" writing-mode="lr" x="1474.82" xml:space="preserve" y="317.48" zvalue="77">3726</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1673.93,394.654) scale(1,1) translate(5.42869e-13,-1.70374e-13)" writing-mode="lr" x="1673.93" xml:space="preserve" y="399.15" zvalue="80">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.43,711.162) scale(1,1) translate(0,1.52122e-13)" writing-mode="lr" x="1293.430043214111" xml:space="preserve" y="715.6619266761666" zvalue="83">#2电炉变  12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1260.64,560.538) scale(1,1) translate(1.38609e-13,1.22407e-13)" writing-mode="lr" x="1260.64" xml:space="preserve" y="565.04" zvalue="86">302</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" x="1522.21875" xml:space="preserve" y="629.3871525777944" zvalue="94">35kVⅡ段母线电压互感</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1522.21875" xml:space="preserve" y="645.3871525777944" zvalue="94">器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1391.1,573.95) scale(1,1) translate(-3.06056e-13,1.25385e-13)" writing-mode="lr" x="1391.1" xml:space="preserve" y="578.45" zvalue="95">374</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1394.04,488.401) scale(1,1) translate(1.54512e-12,1.06389e-13)" writing-mode="lr" x="1394.04" xml:space="preserve" y="492.9" zvalue="97">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" x="1428.5625" xml:space="preserve" y="717.7485361367346" zvalue="100">#2无功补偿装置  </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1428.5625" xml:space="preserve" y="733.7485361367346" zvalue="100">10020kVar</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1129.96,683.794) scale(1,1) translate(-2.40997e-13,7.34603e-14)" writing-mode="lr" x="1129.956020445078" xml:space="preserve" y="688.2941136827512" zvalue="114">#2厂用变   200kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1262.14,482.698) scale(1,1) translate(6.28542e-12,1.05123e-13)" writing-mode="lr" x="1262.14" xml:space="preserve" y="487.2" zvalue="128">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1467.29,489.649) scale(1,1) translate(-1.93244e-12,1.06666e-13)" writing-mode="lr" x="1467.29" xml:space="preserve" y="494.15" zvalue="133">3902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="148.89" xml:space="preserve" y="984.89" zvalue="142">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328,1008) scale(1,1) translate(0,0)" writing-mode="lr" x="328" xml:space="preserve" y="1014" zvalue="158">20240527</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="73.56" x="50.19" y="175.25" zvalue="61"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="4">
   <use class="kv35" height="30" transform="rotate(0,907.838,315.068) scale(1.44865,1.06234) translate(-277.794,-17.5545)" width="15" x="896.9732204795324" xlink:href="#Disconnector:刀闸_0" y="299.1333318709824" zvalue="3"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883209733" ObjectName="35kV越盛硅厂Ⅰ回线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449883209733"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,907.838,315.068) scale(1.44865,1.06234) translate(-277.794,-17.5545)" width="15" x="896.9732204795324" y="299.1333318709824"/></g>
  <g id="14">
   <use class="kv35" height="30" transform="rotate(0,672.711,491.469) scale(0.891477,0.65375) translate(81.0781,255.107)" width="15" x="666.0250421786686" xlink:href="#Disconnector:刀闸_0" y="481.6631806528977" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883340805" ObjectName="#1电炉变3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449883340805"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,672.711,491.469) scale(0.891477,0.65375) translate(81.0781,255.107)" width="15" x="666.0250421786686" y="481.6631806528977"/></g>
  <g id="24">
   <use class="kv35" height="30" transform="rotate(0,796.983,490.578) scale(0.891477,0.65375) translate(96.2063,254.635)" width="15" x="790.2968835833414" xlink:href="#Disconnector:刀闸_0" y="480.7717039799701" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883537413" ObjectName="#1无功补偿3731隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449883537413"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,796.983,490.578) scale(0.891477,0.65375) translate(96.2063,254.635)" width="15" x="790.2968835833414" y="480.7717039799701"/></g>
  <g id="34">
   <use class="kv35" height="30" transform="rotate(0,892.906,496.818) scale(0.891477,0.65375) translate(107.883,257.94)" width="15" x="886.2197831123441" xlink:href="#Disconnector:刀闸_0" y="487.012040690463" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883668485" ObjectName="35kVⅠ段母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449883668485"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,892.906,496.818) scale(0.891477,0.65375) translate(107.883,257.94)" width="15" x="886.2197831123441" y="487.012040690463"/></g>
  <g id="38">
   <use class="kv35" height="30" transform="rotate(0,1012.36,487.904) scale(0.891477,0.65375) translate(122.426,253.218)" width="15" x="1005.67765730589" xlink:href="#Disconnector:刀闸_0" y="478.0972739611875" zvalue="29"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883734021" ObjectName="#1厂用变3811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449883734021"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1012.36,487.904) scale(0.891477,0.65375) translate(122.426,253.218)" width="15" x="1005.67765730589" y="478.0972739611875"/></g>
  <g id="57">
   <use class="kv35" height="30" transform="rotate(0,1669.08,488.904) scale(0.891477,0.65375) translate(202.37,253.748)" width="15" x="1662.391231290355" xlink:href="#Disconnector:刀闸_0" y="479.0972739611875" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883930629" ObjectName="#3厂用变3831隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449883930629"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1669.08,488.904) scale(0.891477,0.65375) translate(202.37,253.748)" width="15" x="1662.391231290355" y="479.0972739611875"/></g>
  <g id="135">
   <use class="kv35" height="30" transform="rotate(0,1440.07,315.01) scale(1.44809,1.06193) translate(-442.245,-17.4422)" width="15" x="1429.205000577656" xlink:href="#Disconnector:刀闸_0" y="299.0806504409444" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449884323845" ObjectName="35kV越盛硅厂Ⅱ回线3726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449884323845"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1440.07,315.01) scale(1.44809,1.06193) translate(-442.245,-17.4422)" width="15" x="1429.205000577656" y="299.0806504409444"/></g>
  <g id="122">
   <use class="kv35" height="30" transform="rotate(0,1417.3,490.451) scale(0.891131,0.653496) translate(172.334,254.855)" width="15" x="1410.613769508921" xlink:href="#Disconnector:刀闸_0" y="480.6486020718603" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449884651525" ObjectName="#2无功补偿3741隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449884651525"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1417.3,490.451) scale(0.891131,0.653496) translate(172.334,254.855)" width="15" x="1410.613769508921" y="480.6486020718603"/></g>
  <g id="137">
   <use class="kv35" height="25" transform="rotate(0,1294.58,483.857) scale(1.15847,1.15847) translate(-173.524,-64.2072)" width="45" x="1268.515132943384" xlink:href="#Disconnector:特殊刀闸_0" y="469.3757942773519" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449884454917" ObjectName="#2电炉变3022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449884454917"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1294.58,483.857) scale(1.15847,1.15847) translate(-173.524,-64.2072)" width="45" x="1268.515132943384" y="469.3757942773519"/></g>
  <g id="143">
   <use class="kv35" height="25" transform="rotate(0,1512.47,490.807) scale(1.15847,1.15847) translate(-203.329,-65.158)" width="45" x="1486.404474910463" xlink:href="#Disconnector:特殊刀闸_0" y="476.3266164747563" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450371584005" ObjectName="35kVⅡ段母线电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450371584005"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1512.47,490.807) scale(1.15847,1.15847) translate(-203.329,-65.158)" width="45" x="1486.404474910463" y="476.3266164747563"/></g>
  <g id="150">
   <use class="kv35" height="30" transform="rotate(0,1125.63,505.234) scale(1.15847,1.15847) translate(-152.79,-66.7353)" width="15" x="1116.945507759848" xlink:href="#Disconnector:令克_0" y="487.8566738552777" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449884585989" ObjectName="#2厂用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449884585989"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1125.63,505.234) scale(1.15847,1.15847) translate(-152.79,-66.7353)" width="15" x="1116.945507759848" y="487.8566738552777"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv35" d="M 644.18 422.27 L 1155 422.27" stroke-width="4" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674248359940" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674248359940"/></metadata>
  <path d="M 644.18 422.27 L 1155 422.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv35" d="M 1202 419.85 L 1712.62 419.85" stroke-width="4" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674248425477" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674248425477"/></metadata>
  <path d="M 1202 419.85 L 1712.62 419.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="8">
   <path class="kv35" d="M 907.93 330.73 L 907.93 422.27" stroke-width="1" zvalue="5"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@1" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.93 330.73 L 907.93 422.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv35" d="M 907.97 299.66 L 907.97 233.41" stroke-width="1" zvalue="6"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.97 299.66 L 907.97 233.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 672.79 481.99 L 672.79 422.27" stroke-width="1" zvalue="10"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 672.79 481.99 L 672.79 422.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 672.77 501.11 L 672.77 544.97" stroke-width="1" zvalue="11"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@1" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 672.77 501.11 L 672.77 544.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 673.58 662.16 L 673.58 577.24" stroke-width="1" zvalue="13"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="12@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.58 662.16 L 673.58 577.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 892.98 487.34 L 892.98 422.27" stroke-width="1" zvalue="27"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 892.98 487.34 L 892.98 422.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 1012.44 478.42 L 1012.44 422.27" stroke-width="1" zvalue="32"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1012.44 478.42 L 1012.44 422.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 1012.42 497.54 L 1012.42 540.58" stroke-width="1" zvalue="33"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1012.42 497.54 L 1012.42 540.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 1013.34 563.58 L 1013.34 617.61" stroke-width="1" zvalue="34"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@1" LinkObjectIDznd="40@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1013.34 563.58 L 1013.34 617.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 892.96 506.46 L 892.96 543.96" stroke-width="1" zvalue="35"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@1" LinkObjectIDznd="21@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 892.96 506.46 L 892.96 543.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv35" d="M 1669.16 479.42 L 1669.16 419.85" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="134@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1669.16 479.42 L 1669.16 419.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv35" d="M 1669.13 498.54 L 1669.13 541.58" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@1" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1669.13 498.54 L 1669.13 541.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv35" d="M 1670.06 564.58 L 1670.06 618.61" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1670.06 564.58 L 1670.06 618.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv35" d="M 1440.15 330.67 L 1440.15 419.85" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@1" LinkObjectIDznd="134@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1440.15 330.67 L 1440.15 419.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv35" d="M 1440.19 299.61 L 1440.19 233.38" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1440.19 299.61 L 1440.19 233.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1293.7 662.21 L 1293.7 575.38" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.7 662.21 L 1293.7 575.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv35" d="M 1417.38 480.97 L 1417.38 419.85" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="134@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.38 480.97 L 1417.38 419.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv35" d="M 1417.35 500.09 L 1417.25 560.93" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@1" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.35 500.09 L 1417.25 560.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 1416.58 640.3 L 1416.58 583.92" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="123@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1416.58 640.3 L 1416.58 583.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv35" d="M 1365.7 561.61 L 1365.7 547.09 L 1417.28 547.09" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.7 561.61 L 1365.7 547.09 L 1417.28 547.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv35" d="M 1292.73 541.47 L 1292.73 497.7" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.73 541.47 L 1292.73 497.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv35" d="M 1294.64 470.24 L 1294.64 419.85" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="134@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1294.64 470.24 L 1294.64 419.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv35" d="M 1513.41 546.12 L 1513.41 504.65" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="143@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1513.41 546.12 L 1513.41 504.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv35" d="M 1512.53 477.2 L 1512.53 419.85" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="134@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1512.53 477.2 L 1512.53 419.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv35" d="M 1125.54 519.42 L 1125.54 622.6" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@1" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125.54 519.42 L 1125.54 622.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv35" d="M 1125.73 489.88 L 1125.73 422.27" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="6@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125.73 489.88 L 1125.73 422.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 797.06 481.1 L 797.06 422.27" stroke-width="1" zvalue="143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.06 481.1 L 797.06 422.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 797.04 500.22 L 797.04 561.15" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@1" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.04 500.22 L 797.04 561.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv35" d="M 796.27 649.57 L 796.27 587.23" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="22@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 796.27 649.57 L 796.27 587.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 745.37 561.76 L 745.37 541 L 797.04 541" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.37 561.76 L 745.37 541 L 797.04 541" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="10">
   <use class="kv35" height="29" transform="rotate(0,673.585,662.762) scale(1.44865,1.44865) translate(-201.881,-198.753)" width="30" x="651.8552358660529" xlink:href="#Accessory:隔离变_0" y="641.7565749716142" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883275269" ObjectName="#1电炉变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,673.585,662.762) scale(1.44865,1.44865) translate(-201.881,-198.753)" width="30" x="651.8552358660529" y="641.7565749716142"/></g>
  <g id="20">
   <use class="kv35" height="40" transform="rotate(0,745.366,582.103) scale(1.09949,-1.09949) translate(-65.9527,-1109.54)" width="30" x="728.8741503406317" xlink:href="#Accessory:带熔断器的线路PT1_0" y="560.113127870522" zvalue="14"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883406341" ObjectName="#1无功补偿装置PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,745.366,582.103) scale(1.09949,-1.09949) translate(-65.9527,-1109.54)" width="30" x="728.8741503406317" y="560.113127870522"/></g>
  <g id="21">
   <use class="kv35" height="40" transform="rotate(0,905.387,567.245) scale(1.22578,1.22578) translate(-162.25,-99.9671)" width="40" x="880.8709230747786" xlink:href="#Accessory:五卷PT_0" y="542.7293327484349" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883471877" ObjectName="35kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,905.387,567.245) scale(1.22578,1.22578) translate(-162.25,-99.9671)" width="40" x="880.8709230747786" y="542.7293327484349"/></g>
  <g id="28">
   <use class="kv35" height="60" transform="rotate(0,799.657,677.964) scale(1.01034,1.01034) translate(-7.97721,-6.62835)" width="40" x="779.4505840768925" xlink:href="#Accessory:带电容器的互感器_0" y="647.6539841790556" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883602949" ObjectName="#1无功补偿装置"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,799.657,677.964) scale(1.01034,1.01034) translate(-7.97721,-6.62835)" width="40" x="779.4505840768925" y="647.6539841790556"/></g>
  <g id="131">
   <use class="kv35" height="29" transform="rotate(0,1293.7,662.818) scale(1.44809,1.44809) translate(-393.593,-198.601)" width="30" x="1271.976063162508" xlink:href="#Accessory:隔离变_0" y="641.8207917741747" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449884258309" ObjectName="#2电炉变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1293.7,662.818) scale(1.44809,1.44809) translate(-393.593,-198.601)" width="30" x="1271.976063162508" y="641.8207917741747"/></g>
  <g id="125">
   <use class="kv35" height="40" transform="rotate(0,1365.7,581.94) scale(1.09906,-1.09906) translate(-121.609,-1109.45)" width="30" x="1349.214849616822" xlink:href="#Accessory:带熔断器的线路PT1_0" y="559.9592656063463" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449884192773" ObjectName="#2无功补偿装置PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1365.7,581.94) scale(1.09906,-1.09906) translate(-121.609,-1109.45)" width="30" x="1349.214849616822" y="559.9592656063463"/></g>
  <g id="124">
   <use class="kv35" height="40" transform="rotate(0,1525.66,569.405) scale(1.22531,1.22531) translate(-276.027,-100.194)" width="40" x="1501.152693803675" xlink:href="#Accessory:五卷PT_0" y="544.8991508453034" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449884127237" ObjectName="35kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1525.66,569.405) scale(1.22531,1.22531) translate(-276.027,-100.194)" width="40" x="1501.152693803675" y="544.8991508453034"/></g>
  <g id="120">
   <use class="kv35" height="60" transform="rotate(0,1419.97,668.677) scale(1.00995,1.00995) translate(-13.7885,-6.28837)" width="40" x="1399.771675069869" xlink:href="#Accessory:带电容器的互感器_0" y="638.3787980898833" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449884061701" ObjectName="#2无功补偿装置"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1419.97,668.677) scale(1.00995,1.00995) translate(-13.7885,-6.28837)" width="40" x="1399.771675069869" y="638.3787980898833"/></g>
 </g>
 <g id="BreakerClass">
  <g id="12">
   <use class="kv35" height="20" transform="rotate(0,674.369,561.117) scale(1.87591,1.68832) translate(-310.5,-221.881)" width="10" x="664.9894824265825" xlink:href="#Breaker:开关_0" y="544.2336996340001" zvalue="8"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531060741" ObjectName="#1电炉变301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531060741"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,674.369,561.117) scale(1.87591,1.68832) translate(-310.5,-221.881)" width="10" x="664.9894824265825" y="544.2336996340001"/></g>
  <g id="22">
   <use class="kv35" height="20" transform="rotate(0,797.876,574.201) scale(1.51577,1.36419) translate(-268.912,-149.649)" width="10" x="790.2968835408326" xlink:href="#Breaker:开关_0" y="560.5588662069858" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531126277" ObjectName="#1无功补偿373断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531126277"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,797.876,574.201) scale(1.51577,1.36419) translate(-268.912,-149.649)" width="10" x="790.2968835408326" y="560.5588662069858"/></g>
  <g id="39">
   <use class="kv35" height="20" transform="rotate(0,1013.26,552.09) scale(1.33722,1.20349) translate(-253.834,-91.3155)" width="10" x="1006.569133925681" xlink:href="#Breaker:开关_0" y="540.0549027296521" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531191813" ObjectName="#1厂用变381断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531191813"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1013.26,552.09) scale(1.33722,1.20349) translate(-253.834,-91.3155)" width="10" x="1006.569133925681" y="540.0549027296521"/></g>
  <g id="56">
   <use class="kv35" height="20" transform="rotate(0,1669.97,553.09) scale(1.33722,1.20349) translate(-419.442,-91.4846)" width="10" x="1663.282707910147" xlink:href="#Breaker:开关_0" y="541.0549027296521" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531257349" ObjectName="#3厂用变383断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531257349"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1669.97,553.09) scale(1.33722,1.20349) translate(-419.442,-91.4846)" width="10" x="1663.282707910147" y="541.0549027296521"/></g>
  <g id="130">
   <use class="kv35" height="20" transform="rotate(0,1292.8,558.44) scale(1.97127,1.77414) translate(-632.121,-235.932)" width="10" x="1282.941293507316" xlink:href="#Breaker:开关_0" y="540.6990095820919" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531388421" ObjectName="#2电炉变302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531388421"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1292.8,558.44) scale(1.97127,1.77414) translate(-632.121,-235.932)" width="10" x="1282.941293507316" y="540.6990095820919"/></g>
  <g id="123">
   <use class="kv35" height="20" transform="rotate(0,1417.3,572.435) scale(1.3367,1.20303) translate(-355.315,-94.5758)" width="10" x="1410.613769466428" xlink:href="#Breaker:开关_0" y="560.4048311318211" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531322885" ObjectName="#2无功补偿374断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531322885"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1417.3,572.435) scale(1.3367,1.20303) translate(-355.315,-94.5758)" width="10" x="1410.613769466428" y="560.4048311318211"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="40">
   <use class="kv35" height="30" transform="rotate(0,1012.36,630.54) scale(0.891477,0.891477) translate(122.263,75.1305)" width="18" x="1004.340442296499" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="617.1676349378853" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883799557" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1012.36,630.54) scale(0.891477,0.891477) translate(122.263,75.1305)" width="18" x="1004.340442296499" y="617.1676349378853"/></g>
  <g id="55">
   <use class="kv35" height="30" transform="rotate(0,1669.08,631.54) scale(0.891477,0.891477) translate(202.207,75.2522)" width="18" x="1661.054016280964" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="618.1676349378853" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883865093" ObjectName="#3厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1669.08,631.54) scale(0.891477,0.891477) translate(202.207,75.2522)" width="18" x="1661.054016280964" y="618.1676349378853"/></g>
  <g id="111">
   <use class="kv35" height="30" transform="rotate(0,1126.44,635.517) scale(0.891131,0.891131) translate(136.636,76.0077)" width="18" x="1118.415874015161" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="622.1501232333344" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883996165" ObjectName="#2厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1126.44,635.517) scale(0.891131,0.891131) translate(136.636,76.0077)" width="18" x="1118.415874015161" y="622.1501232333344"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="219">
   <use height="30" transform="rotate(0,337.673,189.357) scale(0.708333,0.665547) translate(134.667,90.1398)" width="30" x="327.05" xlink:href="#State:红绿圆(方形)_0" y="179.37" zvalue="148"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,337.673,189.357) scale(0.708333,0.665547) translate(134.667,90.1398)" width="30" x="327.05" y="179.37"/></g>
  <g id="218">
   <use height="30" transform="rotate(0,242.048,189.357) scale(0.708333,0.665547) translate(95.2917,90.1398)" width="30" x="231.42" xlink:href="#State:红绿圆(方形)_0" y="179.37" zvalue="149"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,242.048,189.357) scale(0.708333,0.665547) translate(95.2917,90.1398)" width="30" x="231.42" y="179.37"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,329.812,114.464) scale(1.22222,1.03092) translate(-49.9659,-2.96944)" width="90" x="274.81" xlink:href="#State:全站检修_0" y="99" zvalue="153"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549589573634" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.812,114.464) scale(1.22222,1.03092) translate(-49.9659,-2.96944)" width="90" x="274.81" y="99"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="30" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1440.19,97.1462) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.07" xml:space="preserve" y="101.51" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125827444740" ObjectName="P"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="31" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1440.19,118.146) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.07" xml:space="preserve" y="122.51" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125827510276" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1440.19,143.146) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.07" xml:space="preserve" y="147.51" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125827575812" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
</svg>