<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549596454914" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="EnergyConsumer:厂变_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="2.833333333333344" y2="0.9166666666666679"/>
   <ellipse cx="7.38" cy="6.97" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.44" cy="12.96" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="虚设备厂站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="285" x="45" xlink:href="logo.png" y="117"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.5,147) scale(1,1) translate(0,0)" writing-mode="lr" x="187.5" xml:space="preserve" y="150.5" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,189.5,147.69) scale(1,1) translate(0,0)" writing-mode="lr" x="189.5" xml:space="preserve" y="156.69" zvalue="5">虚设备厂站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="3" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,77.4375,436) scale(1,1) translate(0,0)" width="72.88" x="41" y="424" zvalue="32"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.4375,436) scale(1,1) translate(0,0)" writing-mode="lr" x="77.44" xml:space="preserve" y="440.5" zvalue="32">信号一览</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="233" y2="233"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="259" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="233" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="233" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="233" y2="233"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="259" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="233" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="233" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="259" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="283.25" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="259" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="259" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="259" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="283.25" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="259" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="259" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="283.25" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="306" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="283.25" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="283.25" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="283.25" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="306" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="283.25" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="283.25" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="306" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="328.75" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="306" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="306" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="306" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="328.75" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="306" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="306" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="328.75" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="351.5" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="328.75" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="328.75" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="328.75" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="351.5" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="328.75" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="328.75" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1006" y2="1006"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1045.1633" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="17" y1="1006" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1006" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="377" y1="1006" y2="1006"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="377" y1="1045.1633" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1006" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377" x2="377" y1="1006" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1045.16327" y2="1045.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1073.08167" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="17" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="1045.16327" y2="1045.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="1073.08167" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197" x2="197" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="1045.16327" y2="1045.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="1073.08167" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="197.0000000000001" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.0000000000001" x2="287.0000000000001" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="1045.16327" y2="1045.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="1073.08167" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="287" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377" x2="377" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1073.0816" y2="1073.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1101" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="17" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="1073.0816" y2="1073.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="1101" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197" x2="197" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="1073.0816" y2="1073.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="1101" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="197.0000000000001" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.0000000000001" x2="287.0000000000001" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="1073.0816" y2="1073.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="1101" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="287" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377" x2="377" y1="1073.0816" y2="1101"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62,1026) scale(1,1) translate(0,0)" writing-mode="lr" x="62" xml:space="preserve" y="1032" zvalue="10">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59,1060) scale(1,1) translate(0,0)" writing-mode="lr" x="59" xml:space="preserve" y="1066" zvalue="11">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241,1060) scale(1,1) translate(0,0)" writing-mode="lr" x="241" xml:space="preserve" y="1066" zvalue="12">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58,1088) scale(1,1) translate(0,0)" writing-mode="lr" x="58" xml:space="preserve" y="1094" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240,1088) scale(1,1) translate(0,0)" writing-mode="lr" x="240" xml:space="preserve" y="1094" zvalue="14">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" x="145" xml:space="preserve" y="528" zvalue="15">10kVⅠ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="145" xml:space="preserve" y="544" zvalue="15">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" x="205.671875" xml:space="preserve" y="528" zvalue="17">10kVⅡ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="205.671875" xml:space="preserve" y="544" zvalue="17">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94,563.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94" xml:space="preserve" y="568" zvalue="18">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94,589) scale(1,1) translate(0,0)" writing-mode="lr" x="94" xml:space="preserve" y="593.5" zvalue="19">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94,614.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94" xml:space="preserve" y="619" zvalue="20">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93,639) scale(1,1) translate(0,0)" writing-mode="lr" x="93" xml:space="preserve" y="643.5" zvalue="21">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94,665.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94" xml:space="preserve" y="670" zvalue="22">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.054,1028) scale(1,1) translate(0,0)" writing-mode="lr" x="242.05" xml:space="preserve" y="1032" zvalue="23">ceshizhan-01-2021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,152.054,1060) scale(1,1) translate(0,0)" writing-mode="lr" x="152.05" xml:space="preserve" y="1066" zvalue="24">XX</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,332.054,1060) scale(1,1) translate(0,0)" writing-mode="lr" x="332.05" xml:space="preserve" y="1066" zvalue="25">20210220</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56,247) scale(1,1) translate(0,0)" writing-mode="lr" x="56" xml:space="preserve" y="252.5" zvalue="26">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,247) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="252.5" zvalue="27">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,243.125,271.25) scale(1,1) translate(0,0)" writing-mode="lr" x="243.13" xml:space="preserve" y="275.75" zvalue="28">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244.75,317.5) scale(1,1) translate(0,0)" writing-mode="lr" x="244.75" xml:space="preserve" y="322" zvalue="29">110kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,193.211,436.591) scale(1,1) translate(0,0)" writing-mode="lr" x="193.21" xml:space="preserve" y="441.09" zvalue="30">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,298.211,436.591) scale(1,1) translate(0,0)" writing-mode="lr" x="298.21" xml:space="preserve" y="441.09" zvalue="31">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709.346,595.07) scale(1,1) translate(2.20356e-13,0)" writing-mode="lr" x="709.3463450238801" xml:space="preserve" y="599.0696428578999" zvalue="142">龙盆十八岔基站台变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.43,546.903) scale(1,1) translate(2.208e-13,0)" writing-mode="lr" x="708.4299999999999" xml:space="preserve" y="550.9" zvalue="144">龙盆十八岔公变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,719.98,500.903) scale(1,1) translate(0,0)" writing-mode="lr" x="719.98" xml:space="preserve" y="504.9" zvalue="146">龙盆二社公变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="34" stroke="rgb(255,255,255)" text-anchor="middle" x="923.625" xml:space="preserve" y="527.875" zvalue="149">回龙河水库管理</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="34" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="923.625" xml:space="preserve" y="541.875" zvalue="149">局专用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="33" stroke="rgb(255,255,255)" text-anchor="middle" x="923.2265625" xml:space="preserve" y="469.78125" zvalue="151">龙盆二社基站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="33" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="923.2265625" xml:space="preserve" y="483.78125" zvalue="151">台变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.88,444.903) scale(1,1) translate(0,0)" writing-mode="lr" x="791.88" xml:space="preserve" y="448.9" zvalue="155">#62</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841.38,479.153) scale(1,1) translate(-5.53144e-13,0)" writing-mode="lr" x="841.38" xml:space="preserve" y="483.15" zvalue="156">#66</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.88,502.153) scale(1,1) translate(0,0)" writing-mode="lr" x="791.88" xml:space="preserve" y="506.15" zvalue="157">#73</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841.38,527.153) scale(1,1) translate(0,0)" writing-mode="lr" x="841.38" xml:space="preserve" y="531.15" zvalue="158">#78</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,840.88,574.153) scale(1,1) translate(-5.53144e-13,1.25711e-13)" writing-mode="lr" x="840.88" xml:space="preserve" y="578.15" zvalue="159">#83</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="41" y="424" zvalue="32"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,652.864,343.773) scale(1,1) translate(0,0)" writing-mode="lr" x="652.4400000000001" xml:space="preserve" y="348.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123897802756" ObjectName="德宏地区网供负荷"/>
   </metadata>
  </g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="369">
   <use class="kv35" height="18" transform="rotate(90,732.68,572.903) scale(1.98,2.13333) translate(-355.289,-294.155)" width="15" x="717.8297027712756" xlink:href="#EnergyConsumer:厂变_0" y="553.7029639842017" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450268954629" ObjectName="龙盆十八岔基站台变"/>
   <cge:TPSR_Ref TObjectID="6192450268954629"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(90,732.68,572.903) scale(1.98,2.13333) translate(-355.289,-294.155)" width="15" x="717.8297027712756" y="553.7029639842017"/></g>
  <g id="373">
   <use class="kv35" height="18" transform="rotate(90,732.68,525.903) scale(1.98,2.13333) translate(-355.289,-269.186)" width="15" x="717.8297027712756" xlink:href="#EnergyConsumer:厂变_0" y="506.7029639842017" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450268889093" ObjectName="龙盆十八岔公变"/>
   <cge:TPSR_Ref TObjectID="6192450268889093"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(90,732.68,525.903) scale(1.98,2.13333) translate(-355.289,-269.186)" width="15" x="717.8297027712756" y="506.7029639842017"/></g>
  <g id="375">
   <use class="kv35" height="18" transform="rotate(90,732.68,478.903) scale(1.98,2.13333) translate(-355.289,-244.217)" width="15" x="717.8297027587893" xlink:href="#EnergyConsumer:厂变_0" y="459.7029639842017" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450268823557" ObjectName="龙盆二社公变"/>
   <cge:TPSR_Ref TObjectID="6192450268823557"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(90,732.68,478.903) scale(1.98,2.13333) translate(-355.289,-244.217)" width="15" x="717.8297027587893" y="459.7029639842017"/></g>
  <g id="377">
   <use class="kv35" height="18" transform="rotate(270,913.23,501.903) scale(-1.98,2.13333) translate(-1367.11,-256.436)" width="15" x="898.3796919947558" xlink:href="#EnergyConsumer:厂变_0" y="482.7029639842017" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450268758021" ObjectName="回龙河水库管理局专用"/>
   <cge:TPSR_Ref TObjectID="6192450268758021"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,913.23,501.903) scale(-1.98,2.13333) translate(-1367.11,-256.436)" width="15" x="898.3796919947558" y="482.7029639842017"/></g>
  <g id="376">
   <use class="kv35" height="18" transform="rotate(270,913.23,444.903) scale(-1.98,2.13333) translate(-1367.11,-226.155)" width="15" x="898.3796920901236" xlink:href="#EnergyConsumer:厂变_0" y="425.7029639842017" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450268692485" ObjectName="龙盆二社基站台变"/>
   <cge:TPSR_Ref TObjectID="6192450268692485"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,913.23,444.903) scale(-1.98,2.13333) translate(-1367.11,-226.155)" width="15" x="898.3796920901236" y="425.7029639842017"/></g>
  <g id="101">
   <use class="kv35" height="30" transform="rotate(0,815.879,362) scale(1.25,1.23333) translate(-161.676,-64.9865)" width="12" x="808.3791074585301" xlink:href="#EnergyConsumer:负荷_0" y="343.5" zvalue="184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450269282309" ObjectName="35kV弄昔T线"/>
   <cge:TPSR_Ref TObjectID="6192450269282309"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,815.879,362) scale(1.25,1.23333) translate(-161.676,-64.9865)" width="12" x="808.3791074585301" y="343.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="772">
   <use class="kv35" height="30" transform="rotate(270,770.88,478.653) scale(1,1) translate(0,0)" width="30" x="755.8797149658203" xlink:href="#Disconnector:跌落刀闸_0" y="463.6529761912332" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450268626949" ObjectName="龙盆二社公变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192450268626949"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,770.88,478.653) scale(1,1) translate(0,0)" width="30" x="755.8797149658203" y="463.6529761912332"/></g>
  <g id="74">
   <use class="kv35" height="30" transform="rotate(270,770.88,525) scale(1,1) translate(0,0)" width="30" x="755.8797149658203" xlink:href="#Disconnector:跌落刀闸_0" y="510" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450269020165" ObjectName="龙盆十八岔公变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192450269020165"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,770.88,525) scale(1,1) translate(0,0)" width="30" x="755.8797149658203" y="510"/></g>
  <g id="76">
   <use class="kv35" height="30" transform="rotate(270,770.88,573) scale(1,1) translate(0,0)" width="30" x="755.8797149658203" xlink:href="#Disconnector:跌落刀闸_0" y="558" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450269085701" ObjectName="龙盆十八岔基站台变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192450269085701"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,770.88,573) scale(1,1) translate(0,0)" width="30" x="755.8797149658203" y="558"/></g>
  <g id="81">
   <use class="kv35" height="30" transform="rotate(90,871,445) scale(-1,1) translate(-1742,0)" width="30" x="856" xlink:href="#Disconnector:跌落刀闸_0" y="430" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450269151237" ObjectName="龙盆二社基站台变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192450269151237"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,871,445) scale(-1,1) translate(-1742,0)" width="30" x="856" y="430"/></g>
  <g id="96">
   <use class="kv35" height="30" transform="rotate(90,871,502) scale(-1,1) translate(-1742,0)" width="30" x="856.0000000755922" xlink:href="#Disconnector:跌落刀闸_0" y="487" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450269216773" ObjectName="回龙河水库管理局专用跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192450269216773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,871,502) scale(-1,1) translate(-1742,0)" width="30" x="856.0000000755922" y="487"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="42">
   <path class="kv35" d="M 755.88 478.65 L 749.92 478.65" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="772@0" LinkObjectIDznd="375@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.88 478.65 L 749.92 478.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 749.92 525.9 L 755.88 525.9" stroke-width="1" zvalue="172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="373@0" LinkObjectIDznd="74@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 749.92 525.9 L 755.88 525.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv35" d="M 749.92 572.9 L 755.88 573" stroke-width="1" zvalue="174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@0" LinkObjectIDznd="76@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 749.92 572.9 L 755.88 573" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 895.99 444.9 L 886 445" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="376@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.99 444.9 L 886 445" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv35" d="M 886 502 L 895.99 501.9" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="377@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 886 502 L 895.99 501.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 815.88 378.65 L 815.88 573 L 784.88 573" stroke-width="1" zvalue="186"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="76@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.88 378.65 L 815.88 573 L 784.88 573" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 784.88 478.65 L 815.88 478.65" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="772@1" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 784.88 478.65 L 815.88 478.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 857 445 L 815.88 445" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 857 445 L 815.88 445" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv35" d="M 857 502 L 815.88 502" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 857 502 L 815.88 502" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv35" d="M 784.88 525 L 815.88 525" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@1" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 784.88 525 L 815.88 525" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
</svg>