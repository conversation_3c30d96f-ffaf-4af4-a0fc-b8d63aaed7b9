<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587804162" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:腊撒线路PT_0" viewBox="0,0,12,32">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.6666666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="3" y1="27" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="27" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="13.66666666666667" y2="1"/>
   <path d="M 6 15 L 3 20 L 9 20 L 6 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="9" y1="27" y2="25"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,8) scale(1,1) translate(0,0)" width="4" x="4" y="5"/>
   <ellipse cx="5.9" cy="18.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.9" cy="26.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器DY_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 11.5417 13.7917 L 8.54167 13.7917 L 10.0417 11.0417 L 11.5417 13.7917 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV上东电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="35" xlink:href="logo.png" y="42"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,184,72) scale(1,1) translate(0,0)" writing-mode="lr" x="184" xml:space="preserve" y="75.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.5,70.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="179.5" xml:space="preserve" y="79.69" zvalue="3">35kV上东电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="134" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,76.4375,320) scale(1,1) translate(0,0)" width="72.88" x="40" y="308" zvalue="174"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,76.4375,320) scale(1,1) translate(0,0)" writing-mode="lr" x="76.44" xml:space="preserve" y="324.5" zvalue="174">信号一览</text>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.0000000000001" x2="377.0000000000001" y1="10" y2="1040" zvalue="4"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.000000000000796" x2="370.0000000000003" y1="145.8704926140824" y2="145.8704926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="158.0000000000001" y2="158.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="158.0000000000001" y2="158.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="276.5000000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="253.7500000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="253.7500000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="276.5000000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="253.7500000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="253.7500000000001" y2="276.5000000000001"/>
  <line fill="none" id="28" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.000000000000796" x2="370.0000000000003" y1="615.8704926140824" y2="615.8704926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="931" y2="931"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="970.1632999999999" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="3.000000000000114" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="363.0000000000001" y1="931" y2="931"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="363.0000000000001" y1="970.1632999999999" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.0000000000001" x2="363.0000000000001" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="3.000000000000114" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="183.0000000000002" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000002" x2="273.0000000000002" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="273.0000000000001" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.0000000000001" x2="363.0000000000001" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="3.000000000000114" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="183.0000000000002" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000002" x2="273.0000000000002" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="273.0000000000001" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.0000000000001" x2="363.0000000000001" y1="998.0816" y2="1026"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48,951) scale(1,1) translate(0,0)" writing-mode="lr" x="48" xml:space="preserve" y="957" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45,985) scale(1,1) translate(0,0)" writing-mode="lr" x="45" xml:space="preserve" y="991" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,985) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="991" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="1019" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="226" xml:space="preserve" y="1019" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.5,645.5) scale(1,1) translate(0,-2.78222e-13)" writing-mode="lr" x="68.50000000000011" xml:space="preserve" y="649.9999999999999" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.399,312.841) scale(1,1) translate(0,0)" writing-mode="lr" x="201.4" xml:space="preserve" y="317.34" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.399,312.841) scale(1,1) translate(0,0)" writing-mode="lr" x="306.4" xml:space="preserve" y="317.34" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.054,953) scale(1,1) translate(0,0)" writing-mode="lr" x="228.05" xml:space="preserve" y="959" zvalue="28">ShangDong-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.054,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="138.05" xml:space="preserve" y="1019" zvalue="29">杨立超</text>
  
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42,172) scale(1,1) translate(0,0)" writing-mode="lr" x="42" xml:space="preserve" y="177.5" zvalue="31">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222,172) scale(1,1) translate(0,0)" writing-mode="lr" x="222" xml:space="preserve" y="177.5" zvalue="32">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.6875,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="49.69" xml:space="preserve" y="200.75" zvalue="33">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.125,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="229.13" xml:space="preserve" y="200.75" zvalue="34">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.1875,244) scale(1,1) translate(0,0)" writing-mode="lr" x="49.19" xml:space="preserve" y="248.5" zvalue="35">35kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,450.333,690.333) scale(1,1) translate(0,0)" writing-mode="lr" x="450.33" xml:space="preserve" y="694.83" zvalue="37">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,449.5,337.333) scale(1,1) translate(0,0)" writing-mode="lr" x="449.5" xml:space="preserve" y="341.83" zvalue="38">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" x="697.3671875" xml:space="preserve" y="994.1962240134186" zvalue="41">#1发电机              </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="697.3671875" xml:space="preserve" y="1010.196224013419" zvalue="41">3200KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,674.09,742.743) scale(1,1) translate(0,0)" writing-mode="lr" x="674.09" xml:space="preserve" y="747.24" zvalue="43">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.696,821.165) scale(1,1) translate(0,0)" writing-mode="lr" x="718.7" xml:space="preserve" y="825.66" zvalue="49">651</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,537.75,919.757) scale(1,1) translate(0,0)" writing-mode="lr" x="537.75" xml:space="preserve" y="924.26" zvalue="58">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770.219,822.161) scale(1,1) translate(0,0)" writing-mode="lr" x="770.22" xml:space="preserve" y="826.66" zvalue="62">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" x="1300.6015625" xml:space="preserve" y="997.0243490134186" zvalue="70">#2发电机              </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1300.6015625" xml:space="preserve" y="1013.024349013419" zvalue="70">3200KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.34,745.576) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.34" xml:space="preserve" y="750.08" zvalue="72">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1321.95,823.998) scale(1,1) translate(0,0)" writing-mode="lr" x="1321.95" xml:space="preserve" y="828.5" zvalue="76">652</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1141,922.59) scale(1,1) translate(0,0)" writing-mode="lr" x="1141" xml:space="preserve" y="927.09" zvalue="81">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1373.47,824.995) scale(1,1) translate(0,0)" writing-mode="lr" x="1373.47" xml:space="preserve" y="829.49" zvalue="85">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,829.959,641.647) scale(1,1) translate(0,0)" writing-mode="lr" x="829.96" xml:space="preserve" y="646.15" zvalue="92">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.705,583.071) scale(1,1) translate(0,0)" writing-mode="lr" x="798.71" xml:space="preserve" y="587.5700000000001" zvalue="94">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,873.656,564.069) scale(1,1) translate(0,0)" writing-mode="lr" x="873.66" xml:space="preserve" y="568.5700000000001" zvalue="96">601</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" x="903.921875" xml:space="preserve" y="410.0959595959596" zvalue="102">#1主变      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="903.921875" xml:space="preserve" y="426.0959595959596" zvalue="102">8000KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827,501.285) scale(1,1) translate(0,0)" writing-mode="lr" x="827" xml:space="preserve" y="505.78" zvalue="107">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.403,296.98) scale(1,1) translate(0,0)" writing-mode="lr" x="744.4" xml:space="preserve" y="301.48" zvalue="112">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.611,266.432) scale(1,1) translate(0,0)" writing-mode="lr" x="711.61" xml:space="preserve" y="270.93" zvalue="114">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.1,227.402) scale(1,1) translate(0,0)" writing-mode="lr" x="788.1" xml:space="preserve" y="231.9" zvalue="116">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,741.444,156.618) scale(1,1) translate(0,0)" writing-mode="lr" x="741.4400000000001" xml:space="preserve" y="161.12" zvalue="121">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.594,210.107) scale(1,1) translate(0,0)" writing-mode="lr" x="711.59" xml:space="preserve" y="214.61" zvalue="125">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755.028,42.4444) scale(1,1) translate(0,0)" writing-mode="lr" x="755.03" xml:space="preserve" y="46.94" zvalue="127">35kV上亦线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.944,121.99) scale(1,1) translate(-1.40177e-12,0)" writing-mode="lr" x="708.9400000000001" xml:space="preserve" y="126.49" zvalue="130">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,817.915,119.713) scale(1,1) translate(0,0)" writing-mode="lr" x="817.91" xml:space="preserve" y="124.21" zvalue="133">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,860.604,172.303) scale(1,1) translate(0,0)" writing-mode="lr" x="860.6" xml:space="preserve" y="176.8" zvalue="136">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1311.3,223.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1311.3" xml:space="preserve" y="228.32" zvalue="143">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1355.54,253.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1355.54" xml:space="preserve" y="257.89" zvalue="145">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1311.12,319.025) scale(1,1) translate(0,0)" writing-mode="lr" x="1311.12" xml:space="preserve" y="323.53" zvalue="152">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513.54,613.815) scale(1,1) translate(0,0)" writing-mode="lr" x="1513.54" xml:space="preserve" y="618.3099999999999" zvalue="155">6811</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="40" y="308" zvalue="174"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="35">
   <path class="v6300" d="M 486.67 689.67 L 1604.62 689.67" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245476356" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674245476356"/></metadata>
  <path d="M 486.67 689.67 L 1604.62 689.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 481.67 340 L 1793.33 340" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245541892" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674245541892"/></metadata>
  <path d="M 481.67 340 L 1793.33 340" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,692.574,939.386) scale(1.85899,1.85899) translate(-307.135,-421.181)" width="30" x="664.6895607466616" xlink:href="#Generator:发电机_0" y="911.5010218856319" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820557317" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449820557317"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,692.574,939.386) scale(1.85899,1.85899) translate(-307.135,-421.181)" width="30" x="664.6895607466616" y="911.5010218856319"/></g>
  <g id="82">
   <use class="v6300" height="30" transform="rotate(0,1295.82,942.219) scale(1.85899,1.85899) translate(-585.881,-422.49)" width="30" x="1267.939560746662" xlink:href="#Generator:发电机_0" y="914.3343552189651" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821343749" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449821343749"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1295.82,942.219) scale(1.85899,1.85899) translate(-585.881,-422.49)" width="30" x="1267.939560746662" y="914.3343552189651"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v6300" height="30" transform="rotate(0,693.309,743.743) scale(1.9625,1.2338) translate(-332.812,-137.427)" width="15" x="678.5902709960938" xlink:href="#Disconnector:刀闸_0" y="725.2361111111111" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820491781" ObjectName="#1发电机6511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449820491781"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,693.309,743.743) scale(1.9625,1.2338) translate(-332.812,-137.427)" width="15" x="678.5902709960938" y="725.2361111111111"/></g>
  <g id="50">
   <use class="v6300" height="30" transform="rotate(0,571.969,920.757) scale(1.9625,1.2338) translate(-273.301,-170.97)" width="15" x="557.25" xlink:href="#Disconnector:刀闸_0" y="902.25" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820622853" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449820622853"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,571.969,920.757) scale(1.9625,1.2338) translate(-273.301,-170.97)" width="15" x="557.25" y="902.25"/></g>
  <g id="55">
   <use class="v6300" height="30" transform="rotate(90,770.219,857.88) scale(1.9625,1.2338) translate(-370.532,-159.056)" width="15" x="755.5" xlink:href="#Disconnector:刀闸_0" y="839.3730203732479" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820753925" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449820753925"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,770.219,857.88) scale(1.9625,1.2338) translate(-370.532,-159.056)" width="15" x="755.5" y="839.3730203732479"/></g>
  <g id="81">
   <use class="v6300" height="30" transform="rotate(0,1296.56,746.576) scale(1.9625,1.2338) translate(-628.673,-137.964)" width="15" x="1281.840270996094" xlink:href="#Disconnector:刀闸_0" y="728.0694444444443" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821278213" ObjectName="#2发电机6521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449821278213"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1296.56,746.576) scale(1.9625,1.2338) translate(-628.673,-137.964)" width="15" x="1281.840270996094" y="728.0694444444443"/></g>
  <g id="75">
   <use class="v6300" height="30" transform="rotate(0,1175.22,923.59) scale(1.9625,1.2338) translate(-569.162,-171.507)" width="15" x="1160.5" xlink:href="#Disconnector:刀闸_0" y="905.0833333333333" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821212677" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449821212677"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1175.22,923.59) scale(1.9625,1.2338) translate(-569.162,-171.507)" width="15" x="1160.5" y="905.0833333333333"/></g>
  <g id="72">
   <use class="v6300" height="30" transform="rotate(90,1373.47,860.713) scale(1.9625,1.2338) translate(-666.393,-159.593)" width="15" x="1358.75" xlink:href="#Disconnector:刀闸_0" y="842.2063537065811" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821081605" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449821081605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1373.47,860.713) scale(1.9625,1.2338) translate(-666.393,-159.593)" width="15" x="1358.75" y="842.2063537065811"/></g>
  <g id="90">
   <use class="v6300" height="30" transform="rotate(0,849.178,642.647) scale(1.9625,1.2338) translate(-409.257,-118.27)" width="15" x="834.4589578647805" xlink:href="#Disconnector:刀闸_0" y="624.1401515151514" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821540358" ObjectName="#1发电机6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449821540358"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,849.178,642.647) scale(1.9625,1.2338) translate(-409.257,-118.27)" width="15" x="834.4589578647805" y="624.1401515151514"/></g>
  <g id="92">
   <use class="v6300" height="30" transform="rotate(0,847.608,502.285) scale(1.9625,1.2338) translate(-408.487,-91.6727)" width="15" x="832.8888888888889" xlink:href="#Disconnector:刀闸_0" y="483.7777777777778" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821605894" ObjectName="#1发电机6016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449821605894"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,847.608,502.285) scale(1.9625,1.2338) translate(-408.487,-91.6727)" width="15" x="832.8888888888889" y="483.7777777777778"/></g>
  <g id="108">
   <use class="kv35" height="30" transform="rotate(0,763.622,297.98) scale(1.9625,1.2338) translate(-367.297,-52.9584)" width="15" x="748.9034023092249" xlink:href="#Disconnector:刀闸_0" y="279.4734848484846" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821868038" ObjectName="35kV上亦线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449821868038"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,763.622,297.98) scale(1.9625,1.2338) translate(-367.297,-52.9584)" width="15" x="748.9034023092249" y="279.4734848484846"/></g>
  <g id="102">
   <use class="kv35" height="30" transform="rotate(0,762.052,157.618) scale(1.9625,1.2338) translate(-366.527,-26.3606)" width="15" x="747.3333333333333" xlink:href="#Disconnector:刀闸_0" y="139.111111111111" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821671430" ObjectName="35kV上亦线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449821671430"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,762.052,157.618) scale(1.9625,1.2338) translate(-366.527,-26.3606)" width="15" x="747.3333333333333" y="139.111111111111"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(90,820.222,108.126) scale(0.909091,-0.666667) translate(81.3404,-275.314)" width="15" x="813.4040404040403" xlink:href="#Disconnector:刀闸_0" y="98.12554950143277" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822392326" ObjectName="35kV上亦线3519隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449822392326"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,820.222,108.126) scale(0.909091,-0.666667) translate(81.3404,-275.314)" width="15" x="813.4040404040403" y="98.12554950143277"/></g>
  <g id="161">
   <use class="kv35" height="30" transform="rotate(0,1384.99,254.393) scale(1.9625,-1.2338) translate(-672.044,-457.074)" width="15" x="1370.271953662792" xlink:href="#Disconnector:刀闸_0" y="235.8863636363639" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822720006" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449822720006"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1384.99,254.393) scale(1.9625,-1.2338) translate(-672.044,-457.074)" width="15" x="1370.271953662792" y="235.8863636363639"/></g>
  <g id="131">
   <use class="v6300" height="30" transform="rotate(0,1541.03,614.815) scale(1.9625,1.2338) translate(-748.571,-112.996)" width="15" x="1526.307692307692" xlink:href="#Disconnector:刀闸_0" y="596.3076923076924" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822916614" ObjectName="#1厂用变6811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449822916614"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1541.03,614.815) scale(1.9625,1.2338) translate(-748.571,-112.996)" width="15" x="1526.307692307692" y="596.3076923076924"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="48">
   <path class="v6300" d="M 693.48 725.85 L 693.48 689.67" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.48 725.85 L 693.48 689.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v6300" d="M 692.57 911.97 L 692.57 830.47" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.57 911.97 L 692.57 830.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v6300" d="M 694.2 809.23 L 694.2 761.93" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 694.2 809.23 L 694.2 761.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v6300" d="M 692.57 858 L 572.14 858 L 572.14 902.86" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.57 858 L 572.14 858 L 572.14 902.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="v6300" d="M 572.09 941.08 L 572.09 938.95" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 572.09 941.08 L 572.09 938.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v6300" d="M 752.03 858 L 692.57 858" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="47" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.03 858 L 692.57 858" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v6300" d="M 788.11 858.05 L 855.31 858.05 L 855.31 924.34" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="59@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 788.11 858.05 L 855.31 858.05 L 855.31 924.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="v6300" d="M 816 907.21 L 816 858.05" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 816 907.21 L 816 858.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="v6300" d="M 1296.73 728.68 L 1296.73 689.67" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1296.73 728.68 L 1296.73 689.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="v6300" d="M 1295.82 914.8 L 1295.82 833.3" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="79@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1295.82 914.8 L 1295.82 833.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="v6300" d="M 1297.45 812.06 L 1297.45 764.77" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1297.45 812.06 L 1297.45 764.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="v6300" d="M 1295.82 860.83 L 1175.39 860.83 L 1175.39 905.7" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78" LinkObjectIDznd="75@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1295.82 860.83 L 1175.39 860.83 L 1175.39 905.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="v6300" d="M 1175.34 943.92 L 1175.34 941.78" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="75@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.34 943.92 L 1175.34 941.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v6300" d="M 1355.28 860.83 L 1295.82 860.83" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@1" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.28 860.83 L 1295.82 860.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v6300" d="M 1391.36 860.89 L 1458.56 860.89 L 1458.56 927.18" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="69@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1391.36 860.89 L 1458.56 860.89 L 1458.56 927.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v6300" d="M 1419.25 910.04 L 1419.25 860.89" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1419.25 910.04 L 1419.25 860.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v6300" d="M 849.35 624.75 L 849.28 573.37" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="87@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 849.35 624.75 L 849.28 573.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v6300" d="M 849.3 660.84 L 849.3 689.67" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@1" LinkObjectIDznd="35@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 849.3 660.84 L 849.3 689.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="v6300" d="M 809.95 603.44 L 849.32 603.44" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="86" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.95 603.44 L 849.32 603.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="v6300" d="M 847.78 484.39 L 849.16 450.57" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.78 484.39 L 849.16 450.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="v6300" d="M 847.73 520.48 L 849.16 552.13" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@1" LinkObjectIDznd="87@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.73 520.48 L 849.16 552.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 849.21 378.3 L 849.21 340" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 849.21 378.3 L 849.21 340" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 763.79 280.09 L 763.72 236.71" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="106@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.79 280.09 L 763.72 236.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 763.74 316.17 L 763.74 340" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.74 316.17 L 763.74 340" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 724.4 253.73 L 763.75 253.73" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 724.4 253.73 L 763.75 253.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 762.17 175.81 L 763.6 215.46" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.17 175.81 L 763.6 215.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 762.22 73.81 L 762.22 139.72" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.22 73.81 L 762.22 139.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 721.73 108.21 L 762.22 108.21" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 721.73 108.21 L 762.22 108.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 870.74 108.18 L 830.05 108.18" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.74 108.18 L 830.05 108.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv35" d="M 844.51 160.81 L 844.51 108.18" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 844.51 160.81 L 844.51 108.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv35" d="M 810.55 108.21 L 762.22 108.21" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.55 108.21 L 762.22 108.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv35" d="M 722.84 194.01 L 762.82 194.01" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.84 194.01 L 762.82 194.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv35" d="M 1385.16 272.29 L 1385.16 340" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.16 272.29 L 1385.16 340" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv35" d="M 1382.45 173.1 L 1385.11 236.2" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.45 173.1 L 1385.11 236.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv35" d="M 1323.17 205.84 L 1383.83 205.84" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.17 205.84 L 1383.83 205.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1322.99 303.77 L 1385.16 303.77" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="130" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.99 303.77 L 1385.16 303.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="v6300" d="M 1541.15 633.01 L 1541.15 689.67" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="35@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1541.15 633.01 L 1541.15 689.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="v6300" d="M 1703 539.47 L 1703 645.38 L 1703.33 645.38 L 1703.33 751.3" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1703 539.47 L 1703 645.38 L 1703.33 645.38 L 1703.33 751.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="v6300" d="M 1541.2 596.92 L 1541.2 572.17 L 1703 572.17" stroke-width="1" zvalue="172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="143" MaxPinNum="2"/>
   </metadata>
  <path d="M 1541.2 596.92 L 1541.2 572.17 L 1703 572.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="170">
   <use class="v6300" height="20" transform="rotate(0,694.239,819.857) scale(1.22222,1.11111) translate(-125.114,-80.8746)" width="10" x="688.1279165772619" xlink:href="#Breaker:开关_0" y="808.7457264957264" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516577285" ObjectName="#1发电机651断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516577285"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,694.239,819.857) scale(1.22222,1.11111) translate(-125.114,-80.8746)" width="10" x="688.1279165772619" y="808.7457264957264"/></g>
  <g id="79">
   <use class="v6300" height="20" transform="rotate(0,1297.49,822.69) scale(1.22222,1.11111) translate(-234.796,-81.1579)" width="10" x="1291.377916577262" xlink:href="#Breaker:开关_0" y="811.5790598290598" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516642821" ObjectName="#2发电机652断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516642821"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1297.49,822.69) scale(1.22222,1.11111) translate(-234.796,-81.1579)" width="10" x="1291.377916577262" y="811.5790598290598"/></g>
  <g id="87">
   <use class="v6300" height="20" transform="rotate(0,849.199,562.761) scale(1.22222,1.11111) translate(-153.289,-55.165)" width="10" x="843.0875125368578" xlink:href="#Breaker:开关_0" y="551.6497668997667" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516708357" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516708357"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,849.199,562.761) scale(1.22222,1.11111) translate(-153.289,-55.165)" width="10" x="843.0875125368578" y="551.6497668997667"/></g>
  <g id="106">
   <use class="kv35" height="20" transform="rotate(0,763.643,226.094) scale(1.22222,1.11111) translate(-137.733,-21.4983)" width="10" x="757.5319569813023" xlink:href="#Breaker:开关_0" y="214.9831002331001" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516773893" ObjectName="35kV上亦线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516773893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,763.643,226.094) scale(1.22222,1.11111) translate(-137.733,-21.4983)" width="10" x="757.5319569813023" y="214.9831002331001"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="52">
   <use class="v6300" height="32" transform="rotate(0,572.089,960.25) scale(1.25,1.25) translate(-112.918,-188.05)" width="12" x="564.5887851823077" xlink:href="#Accessory:腊撒线路PT_0" y="940.25" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820688389" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,572.089,960.25) scale(1.25,1.25) translate(-112.918,-188.05)" width="12" x="564.5887851823077" y="940.25"/></g>
  <g id="59">
   <use class="v6300" height="18" transform="rotate(0,855.312,935.562) scale(1.28472,1.28472) translate(-186.993,-204.778)" width="18" x="843.75" xlink:href="#Accessory:四卷PT_0" y="924" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820819461" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,855.312,935.562) scale(1.28472,1.28472) translate(-186.993,-204.778)" width="18" x="843.75" y="924"/></g>
  <g id="60">
   <use class="v6300" height="32" transform="rotate(0,816,926.375) scale(1.25,1.25) translate(-161.7,-181.275)" width="12" x="808.5" xlink:href="#Accessory:腊撒线路PT_0" y="906.375" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820884997" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,816,926.375) scale(1.25,1.25) translate(-161.7,-181.275)" width="12" x="808.5" y="906.375"/></g>
  <g id="74">
   <use class="v6300" height="32" transform="rotate(0,1175.34,963.083) scale(1.25,1.25) translate(-233.568,-188.617)" width="12" x="1167.838785182308" xlink:href="#Accessory:腊撒线路PT_0" y="943.0833333333333" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821147141" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1175.34,963.083) scale(1.25,1.25) translate(-233.568,-188.617)" width="12" x="1167.838785182308" y="943.0833333333333"/></g>
  <g id="69">
   <use class="v6300" height="18" transform="rotate(0,1458.56,938.396) scale(1.28472,1.28472) translate(-320.686,-205.406)" width="18" x="1447" xlink:href="#Accessory:四卷PT_0" y="926.8333333333333" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821016069" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1458.56,938.396) scale(1.28472,1.28472) translate(-320.686,-205.406)" width="18" x="1447" y="926.8333333333333"/></g>
  <g id="68">
   <use class="v6300" height="32" transform="rotate(0,1419.25,929.208) scale(1.25,1.25) translate(-282.35,-181.842)" width="12" x="1411.75" xlink:href="#Accessory:腊撒线路PT_0" y="909.2083333333333" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820950533" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1419.25,929.208) scale(1.25,1.25) translate(-282.35,-181.842)" width="12" x="1411.75" y="909.2083333333333"/></g>
  <g id="121">
   <use class="kv35" height="40" transform="rotate(90,888.769,108.181) scale(0.974359,0.974359) translate(23.004,2.33405)" width="30" x="874.1538461538462" xlink:href="#Accessory:带熔断器的线路PT1_0" y="88.69397403616773" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822457862" ObjectName="35kV上亦线PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,888.769,108.181) scale(0.974359,0.974359) translate(23.004,2.33405)" width="30" x="874.1538461538462" y="88.69397403616773"/></g>
  <g id="159">
   <use class="kv35" height="18" transform="rotate(0,1380.46,155.231) scale(-2.13675,-2.13675) translate(-2017.99,-217.648)" width="15" x="1364.433566433566" xlink:href="#Accessory:PT8_0" y="136" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822654470" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1380.46,155.231) scale(-2.13675,-2.13675) translate(-2017.99,-217.648)" width="15" x="1364.433566433566" y="136"/></g>
  <g id="135">
   <use class="v6300" height="18" transform="rotate(0,1701.01,521.603) scale(-2.13675,-2.13675) translate(-2488.56,-755.482)" width="15" x="1684.98717948718" xlink:href="#Accessory:PT8_0" y="502.3717948717949" zvalue="158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822982150" ObjectName="#1站用变电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1701.01,521.603) scale(-2.13675,-2.13675) translate(-2488.56,-755.482)" width="15" x="1684.98717948718" y="502.3717948717949"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="89">
   <use class="v6300" height="30" transform="rotate(90,797.167,603.458) scale(1.11574,0.892592) translate(-81.999,71.0043)" width="12" x="790.4722205268012" xlink:href="#GroundDisconnector:地刀12_0" y="590.068749225196" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821474821" ObjectName="#1发电机60167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449821474821"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,797.167,603.458) scale(1.11574,0.892592) translate(-81.999,71.0043)" width="12" x="790.4722205268012" y="590.068749225196"/></g>
  <g id="107">
   <use class="kv35" height="30" transform="rotate(90,711.611,253.742) scale(1.11574,0.892592) translate(-73.124,28.9223)" width="12" x="704.9166649712456" xlink:href="#GroundDisconnector:地刀12_0" y="240.3533966662622" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821802502" ObjectName="35kV上亦线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449821802502"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,711.611,253.742) scale(1.11574,0.892592) translate(-73.124,28.9223)" width="12" x="704.9166649712456" y="240.3533966662622"/></g>
  <g id="109">
   <use class="kv35" height="30" transform="rotate(90,710.056,194.028) scale(1.11574,0.892592) translate(-72.9626,21.7367)" width="12" x="703.3611094156901" xlink:href="#GroundDisconnector:地刀12_0" y="180.6388905843099" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449821999109" ObjectName="35kV上亦线35160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449821999109"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,710.056,194.028) scale(1.11574,0.892592) translate(-72.9626,21.7367)" width="12" x="703.3611094156901" y="180.6388905843099"/></g>
  <g id="114">
   <use class="kv35" height="30" transform="rotate(90,708.944,108.218) scale(1.11574,0.892592) translate(-72.8474,11.411)" width="12" x="702.2499983045789" xlink:href="#GroundDisconnector:地刀12_0" y="94.8292555637878" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822195718" ObjectName="35kV上亦线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449822195718"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,708.944,108.218) scale(1.11574,0.892592) translate(-72.8474,11.411)" width="12" x="702.2499983045789" y="94.8292555637878"/></g>
  <g id="125">
   <use class="kv35" height="30" transform="rotate(0,844.52,173.599) scale(1.11574,0.892592) translate(-86.9112,19.2784)" width="12" x="837.8257558803366" xlink:href="#GroundDisconnector:地刀12_0" y="160.2099861554053" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822326790" ObjectName="35kV上亦线35197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449822326790"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,844.52,173.599) scale(1.11574,0.892592) translate(-86.9112,19.2784)" width="12" x="837.8257558803366" y="160.2099861554053"/></g>
  <g id="162">
   <use class="kv35" height="30" transform="rotate(90,1310.39,205.854) scale(1.11574,0.892592) translate(-135.238,23.1597)" width="12" x="1303.693669139088" xlink:href="#GroundDisconnector:地刀12_0" y="192.4646481600674" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822851078" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449822851078"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1310.39,205.854) scale(1.11574,0.892592) translate(-135.238,23.1597)" width="12" x="1303.693669139088" y="192.4646481600674"/></g>
  <g id="127">
   <use class="kv35" height="30" transform="rotate(90,1310.21,303.785) scale(1.11574,0.892592) translate(-135.219,34.9441)" width="12" x="1303.51185095727" xlink:href="#GroundDisconnector:地刀12_0" y="290.3964663418857" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449822588934" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449822588934"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1310.21,303.785) scale(1.11574,0.892592) translate(-135.219,34.9441)" width="12" x="1303.51185095727" y="290.3964663418857"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="88">
   <g id="880">
    <use class="kv35" height="50" transform="rotate(0,849.158,414.359) scale(1.46909,1.46909) translate(-264.106,-120.58)" width="30" x="827.12" xlink:href="#PowerTransformer2:Y-D_0" y="377.63" zvalue="100"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440310788" ObjectName="35"/>
    </metadata>
   </g>
   <g id="881">
    <use class="v6300" height="50" transform="rotate(0,849.158,414.359) scale(1.46909,1.46909) translate(-264.106,-120.58)" width="30" x="827.12" xlink:href="#PowerTransformer2:Y-D_1" y="377.63" zvalue="100"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440376324" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452393475" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399452393475"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,849.158,414.359) scale(1.46909,1.46909) translate(-264.106,-120.58)" width="30" x="827.12" y="377.63"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="111">
   <use class="kv35" height="30" transform="rotate(0,762.222,65.5556) scale(2.06349,0.555556) translate(-389.115,45.7778)" width="7" x="755" xlink:href="#ACLineSegment:线路_0" y="57.22222222222217" zvalue="126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249304268805" ObjectName="35kV上亦线"/>
   <cge:TPSR_Ref TObjectID="8444249304268805_5066549587804162"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,762.222,65.5556) scale(2.06349,0.555556) translate(-389.115,45.7778)" width="7" x="755" y="57.22222222222217"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="136">
   <use class="v6300" height="30" transform="rotate(0,1703.33,778.417) scale(1.91667,1.91667) translate(-805.471,-358.536)" width="20" x="1684.166666666667" xlink:href="#EnergyConsumer:站用变带熔断器DY_0" y="749.6666666666667" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449823047686" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1703.33,778.417) scale(1.91667,1.91667) translate(-805.471,-358.536)" width="20" x="1684.166666666667" y="749.6666666666667"/></g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,340.25,314.983) scale(0.708333,0.665547) translate(135.728,153.27)" width="30" x="329.63" xlink:href="#State:红绿圆(方形)_0" y="305" zvalue="176"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374888206339" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,340.25,314.983) scale(0.708333,0.665547) translate(135.728,153.27)" width="30" x="329.63" y="305"/></g>
  <g id="160">
   <use height="30" transform="rotate(0,240.625,314.983) scale(0.708333,0.665547) translate(94.7059,153.27)" width="30" x="230" xlink:href="#State:红绿圆(方形)_0" y="305" zvalue="177"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,240.625,314.983) scale(0.708333,0.665547) translate(94.7059,153.27)" width="30" x="230" y="305"/></g>
  <g id="10">
   <use height="30" transform="rotate(0,313.812,123.464) scale(1.22222,1.03092) translate(-47.0568,-3.23939)" width="90" x="258.81" xlink:href="#State:全站检修_0" y="108" zvalue="183"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549587804162" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,313.812,123.464) scale(1.22222,1.03092) translate(-47.0568,-3.23939)" width="90" x="258.81" y="108"/></g>
 </g>
</svg>