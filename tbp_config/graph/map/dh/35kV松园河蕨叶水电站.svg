<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584461826" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:PT1515_0" viewBox="0,0,15,23">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.2666666666666639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="3.916666666666664" y2="0.2499999999999982"/>
   <ellipse cx="7.67" cy="16.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.57" cy="8.85" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="7.583333333333334" y1="19.25" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.749999999999996" x2="9.694444444444443" y1="9.549457994579948" y2="9.549457994579948"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.750000000000003" x2="7.064814814814818" y1="9.549457994579944" y2="7.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.694444444444443" x2="8.379629629629626" y1="9.549457994579944" y2="7.083333333333332"/>
  </symbol>
  <symbol id="Accessory:PT带保险_0" viewBox="0,0,11,29">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.1666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.5,7.5) scale(1,1) translate(0,0)" width="4" x="3.5" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="13.16666666666667" y2="0.5"/>
   <ellipse cx="5.4" cy="18.1" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.4" cy="23.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.49999999999999"/>
   <rect fill-opacity="0" height="9.5" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.04,13.75) scale(1,1) translate(0,0)" width="6.08" x="4" y="9"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="7.041666666666663" y1="1.166666666666664" y2="26.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.49999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="3.999999999999998" y2="23.66666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
   <rect fill-opacity="0" height="9.5" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.04,13.75) scale(1,1) translate(0,0)" width="6.08" x="4" y="9"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.49999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV松园河蕨叶水电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="277.25" x="57.75" xlink:href="logo.png" y="36.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.375,66.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.38" xml:space="preserve" y="70.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,191,63.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="72.94" zvalue="3">35kV松园河蕨叶水电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="64" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.9688,187.25) scale(1,1) translate(0,0)" width="73.56" x="50.19" y="175.25" zvalue="296"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.9688,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.97" xml:space="preserve" y="191.75" zvalue="296">信号一览</text>
  <line fill="none" id="131" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="391.75" x2="391.75" y1="4.75" y2="1034.75" zvalue="4"/>
  <line fill="none" id="129" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75000000000045" x2="384.75" y1="140.6204926140824" y2="140.6204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" x="708.15625" xml:space="preserve" y="945.2764304175597" zvalue="18">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="708.15625" xml:space="preserve" y="961.2764304175597" zvalue="18">5000KW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,619.325,673.182) scale(1,1) translate(0,0)" writing-mode="lr" x="619.3200000000001" xml:space="preserve" y="677.6799999999999" zvalue="20">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,672.24,753.825) scale(1,1) translate(7.31359e-14,0)" writing-mode="lr" x="672.24" xml:space="preserve" y="758.3200000000001" zvalue="22">661</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.586,919.63) scale(1,1) translate(0,0)" writing-mode="lr" x="966.59" xml:space="preserve" y="924.13" zvalue="25">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724.275,277.887) scale(1,1) translate(-3.0832e-13,-3.5623e-13)" writing-mode="lr" x="724.28" xml:space="preserve" y="282.39" zvalue="29">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138.72,381.527) scale(1,1) translate(0,0)" writing-mode="lr" x="1138.72" xml:space="preserve" y="386.03" zvalue="33">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1153.95,435.827) scale(1,1) translate(0,0)" writing-mode="lr" x="1153.95" xml:space="preserve" y="440.33" zvalue="36">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1191.43,488.713) scale(1,1) translate(0,0)" writing-mode="lr" x="1191.43" xml:space="preserve" y="493.21" zvalue="39">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1187.73,499.846) scale(1,1) translate(0,1.09101e-13)" writing-mode="lr" x="1187.73" xml:space="preserve" y="504.35" zvalue="42">12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,939.492,230.239) scale(1,1) translate(1.03004e-13,0)" writing-mode="lr" x="939.49" xml:space="preserve" y="234.74" zvalue="45">361</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.799,79.1506) scale(1,1) translate(0,0)" writing-mode="lr" x="904.8" xml:space="preserve" y="83.65000000000001" zvalue="53">35kV蕨叶电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,967.903,184.147) scale(1,1) translate(0,0)" writing-mode="lr" x="967.9" xml:space="preserve" y="188.65" zvalue="58">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1141.53,639.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1141.53" xml:space="preserve" y="643.5" zvalue="74">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1179.7,576.224) scale(1,1) translate(0,0)" writing-mode="lr" x="1179.7" xml:space="preserve" y="580.72" zvalue="81">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933.583,757.008) scale(1,1) translate(1.01983e-13,0)" writing-mode="lr" x="933.58" xml:space="preserve" y="761.51" zvalue="100">6631</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.125,400.75) scale(1,1) translate(0,0)" writing-mode="lr" x="901.13" xml:space="preserve" y="405.25" zvalue="206">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,852.293,499.375) scale(1,1) translate(0,0)" writing-mode="lr" x="852.29" xml:space="preserve" y="503.88" zvalue="207">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,588.625,878.75) scale(1,1) translate(0,0)" writing-mode="lr" x="588.63" xml:space="preserve" y="883.25" zvalue="216">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,745.875,876.25) scale(1,1) translate(0,0)" writing-mode="lr" x="745.88" xml:space="preserve" y="880.75" zvalue="221">6912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,606.493,800.862) scale(1,1) translate(0,0)" writing-mode="lr" x="606.49" xml:space="preserve" y="805.36" zvalue="227">66167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.919,846.112) scale(1,1) translate(-1.95603e-13,0)" writing-mode="lr" x="900.92" xml:space="preserve" y="850.61" zvalue="230">66317</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" x="1320.140625" xml:space="preserve" y="949.2764304175597" zvalue="233">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1320.140625" xml:space="preserve" y="965.2764304175597" zvalue="233">5000KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1284.24,753.825) scale(1,1) translate(1.41082e-13,0)" writing-mode="lr" x="1284.24" xml:space="preserve" y="758.3200000000001" zvalue="235">662</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.56,878.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1196.56" xml:space="preserve" y="883.25" zvalue="242">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1357.88,876.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1357.88" xml:space="preserve" y="880.75" zvalue="246">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1218.49,800.862) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.49" xml:space="preserve" y="805.36" zvalue="251">66267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1642.38,775.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1642.38" xml:space="preserve" y="780.25" zvalue="256">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1576.04,885.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1576.04" xml:space="preserve" y="890.13" zvalue="257">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1710.63,525.02) scale(1,1) translate(0,0)" writing-mode="lr" x="1710.63" xml:space="preserve" y="529.52" zvalue="264">近区变400kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1656.12,632.454) scale(1,1) translate(0,0)" writing-mode="lr" x="1656.12" xml:space="preserve" y="636.95" zvalue="265">664</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1708.2,579.585) scale(1,1) translate(0,0)" writing-mode="lr" x="1708.2" xml:space="preserve" y="584.09" zvalue="266">66467</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="80" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="278"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="993.0816" y2="1021"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,946) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="952" zvalue="280">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.8889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="52.89" xml:space="preserve" y="984.89" zvalue="281">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="234.89" xml:space="preserve" y="984.89" zvalue="282">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.8889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="51.89" xml:space="preserve" y="1012.89" zvalue="283">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="233.89" xml:space="preserve" y="1012.89" zvalue="284">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,560.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="565" zvalue="286">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="287">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="288">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="289">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="290">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="291">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="297">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="298">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="384.75" zvalue="301">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="303">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="305">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="306">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="307">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="309">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.688,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="242.69" xml:space="preserve" y="339.75" zvalue="316">6.3kV母线频率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="73.56" x="50.19" y="175.25" zvalue="296"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,709.721,902.149) scale(1.39882,1.39882) translate(-196.368,-251.232)" width="30" x="688.7383087230376" xlink:href="#Generator:发电机_0" y="881.1664821930161" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738178566" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449738178566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,709.721,902.149) scale(1.39882,1.39882) translate(-196.368,-251.232)" width="30" x="688.7383087230376" y="881.1664821930161"/></g>
  <g id="200">
   <use class="v6300" height="30" transform="rotate(0,1321.72,906.149) scale(1.39882,1.39882) translate(-370.857,-252.372)" width="30" x="1300.738308723038" xlink:href="#Generator:发电机_0" y="885.1664821930161" zvalue="232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739554822" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449739554822"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1321.72,906.149) scale(1.39882,1.39882) translate(-370.857,-252.372)" width="30" x="1300.738308723038" y="885.1664821930161"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="255">
   <path class="v6300" d="M 591 694.33 L 1792.22 694.33" stroke-width="6" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674240299013" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674240299013"/></metadata>
  <path d="M 591 694.33 L 1792.22 694.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv35" d="M 693.48 299.93 L 1653.11 299.93" stroke-width="6" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674240233476" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674240233476"/></metadata>
  <path d="M 693.48 299.93 L 1653.11 299.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="249">
   <use class="v6300" height="20" transform="rotate(0,709.721,761.885) scale(2.34616,2.11155) translate(-400.487,-389.951)" width="10" x="697.9898184003972" xlink:href="#Breaker:小车断路器_0" y="740.7690399834298" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924498882565" ObjectName="#1发电机661断路器"/>
   <cge:TPSR_Ref TObjectID="6473924498882565"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,709.721,761.885) scale(2.34616,2.11155) translate(-400.487,-389.951)" width="10" x="697.9898184003972" y="740.7690399834298"/></g>
  <g id="267">
   <use class="kv35" height="20" transform="rotate(0,1111.39,378.903) scale(2.03445,1.8495) translate(-559.935,-165.54)" width="10" x="1101.220953390723" xlink:href="#Breaker:小车断路器_0" y="360.4077889488315" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924498817029" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924498817029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1111.39,378.903) scale(2.03445,1.8495) translate(-559.935,-165.54)" width="10" x="1101.220953390723" y="360.4077889488315"/></g>
  <g id="370">
   <use class="kv35" height="20" transform="rotate(0,908.703,227.935) scale(2.20717,2.00651) translate(-490.962,-104.272)" width="10" x="897.6673507561992" xlink:href="#Breaker:小车断路器_0" y="207.8697059740354" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924498751493" ObjectName="35kV蕨叶电站线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473924498751493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,908.703,227.935) scale(2.20717,2.00651) translate(-490.962,-104.272)" width="10" x="897.6673507561992" y="207.8697059740354"/></g>
  <g id="247">
   <use class="v6300" height="20" transform="rotate(0,1110.08,641.454) scale(2.20507,2.00461) translate(-600.634,-311.419)" width="10" x="1099.055673400231" xlink:href="#Breaker:小车断路器_0" y="621.4077889488317" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924498685957" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924498685957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1110.08,641.454) scale(2.20507,2.00461) translate(-600.634,-311.419)" width="10" x="1099.055673400231" y="621.4077889488317"/></g>
  <g id="199">
   <use class="v6300" height="20" transform="rotate(0,1321.72,761.885) scale(2.34616,2.11155) translate(-751.636,-389.951)" width="10" x="1309.989818400397" xlink:href="#Breaker:小车断路器_0" y="740.7690399834298" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924498948101" ObjectName="#2发电机662断路器"/>
   <cge:TPSR_Ref TObjectID="6473924498948101"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1321.72,761.885) scale(2.34616,2.11155) translate(-751.636,-389.951)" width="10" x="1309.989818400397" y="740.7690399834298"/></g>
  <g id="2">
   <use class="v6300" height="20" transform="rotate(0,1627.98,633.454) scale(2.20507,2.00461) translate(-883.667,-307.409)" width="10" x="1616.957851487132" xlink:href="#Breaker:小车断路器_0" y="613.4077889488317" zvalue="264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499013637" ObjectName="近区变6.3kV侧664断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499013637"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1627.98,633.454) scale(2.20507,2.00461) translate(-883.667,-307.409)" width="10" x="1616.957851487132" y="613.4077889488317"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="118">
   <path class="v6300" d="M 709.72 780.89 L 709.72 881.52" stroke-width="1" zvalue="23"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.72 780.89 L 709.72 881.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 1111.39 395.55 L 1111.39 458.99" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.39 395.55 L 1111.39 458.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="v6300" d="M 1111.35 526.35 L 1111.35 622.91" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@1" LinkObjectIDznd="247@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.35 526.35 L 1111.35 622.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 955.92 169.99 L 908.7 169.99" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.92 169.99 L 908.7 169.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v6300" d="M 970.52 732.28 L 970.52 694.33" stroke-width="1" zvalue="172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375@0" LinkObjectIDznd="255@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 970.52 732.28 L 970.52 694.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="v6300" d="M 970.52 773.7 L 970.52 852.66" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 970.52 773.7 L 970.52 852.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv35" d="M 908.7 245.99 L 908.7 299.93" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="370@1" LinkObjectIDznd="251@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.7 245.99 L 908.7 299.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 908.7 209.37 L 908.7 112.65" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="370@0" LinkObjectIDznd="364@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.7 209.37 L 908.7 112.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv35" d="M 1111.39 361.79 L 1111.39 299.93" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="251@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.39 361.79 L 1111.39 299.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv35" d="M 862.81 175.53 L 862.81 144.75 L 908.7 144.75" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.81 175.53 L 862.81 144.75 L 908.7 144.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv35" d="M 1143.38 415.95 L 1111.39 415.95" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.38 415.95 L 1111.39 415.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv35" d="M 1063.12 415.95 L 1111.39 415.95" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.12 415.95 L 1111.39 415.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="v6300" d="M 1110.08 659.5 L 1110.08 694.33" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@1" LinkObjectIDznd="255@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1110.08 659.5 L 1110.08 694.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="v6300" d="M 1141.63 572.7 L 1111.35 572.7" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 1141.63 572.7 L 1111.35 572.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="v6300" d="M 1063.12 547.95 L 1111.35 547.95" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.12 547.95 L 1111.35 547.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="v6300" d="M 1063.12 591.95 L 1111.35 591.95" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.12 591.95 L 1111.35 591.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv35" d="M 861.25 381.84 L 861.25 363 L 791.58 363 L 791.58 435.1" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 861.25 381.84 L 861.25 363 L 791.58 363 L 791.58 435.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv35" d="M 861.36 439.25 L 861.36 414.21" stroke-width="1" zvalue="210"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 861.36 439.25 L 861.36 414.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv35" d="M 822.5 363 L 822.5 299.93" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.5 363 L 822.5 299.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="v6300" d="M 709.72 742.35 L 709.72 694.33" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="255@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.72 742.35 L 709.72 694.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="v6300" d="M 812.08 912.57 L 812.08 827.03 L 709.72 827.03" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.08 912.57 L 812.08 827.03 L 709.72 827.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="v6300" d="M 709.72 830.5 L 613.75 830.5 L 613.75 863.59" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118" LinkObjectIDznd="163@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.72 830.5 L 613.75 830.5 L 613.75 863.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="v6300" d="M 613.86 895.96 L 613.86 923.89" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="165@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 613.86 895.96 L 613.86 923.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="v6300" d="M 769.75 883.59 L 769.75 827.03" stroke-width="1" zvalue="223"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@1" LinkObjectIDznd="162" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.75 883.59 L 769.75 827.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="v6300" d="M 768.08 944.57 L 768.08 915.96" stroke-width="1" zvalue="224"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="168@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.08 944.57 L 768.08 915.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="v6300" d="M 658.25 801.21 L 709.72 801.21" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.25 801.21 L 709.72 801.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="v6300" d="M 914.25 813.21 L 970.52 813.21" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="62" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.25 813.21 L 970.52 813.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="v6300" d="M 1321.72 780.89 L 1321.72 885.52" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@1" LinkObjectIDznd="200@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.72 780.89 L 1321.72 885.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="v6300" d="M 1321.72 742.35 L 1321.72 694.33" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="255@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.72 742.35 L 1321.72 694.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="v6300" d="M 1424.08 912.57 L 1424.08 827.03 L 1321.72 827.03" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.08 912.57 L 1424.08 827.03 L 1321.72 827.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="v6300" d="M 1321.72 830.5 L 1225.75 830.5 L 1225.75 863.59" stroke-width="1" zvalue="243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198" LinkObjectIDznd="194@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.72 830.5 L 1225.75 830.5 L 1225.75 863.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="v6300" d="M 1225.86 895.96 L 1225.86 923.89" stroke-width="1" zvalue="244"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="193@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1225.86 895.96 L 1225.86 923.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="v6300" d="M 1381.75 883.59 L 1381.75 827.03" stroke-width="1" zvalue="248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@1" LinkObjectIDznd="195" MaxPinNum="2"/>
   </metadata>
  <path d="M 1381.75 883.59 L 1381.75 827.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="v6300" d="M 1380.08 944.57 L 1380.08 915.96" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1380.08 944.57 L 1380.08 915.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="v6300" d="M 1270.25 801.21 L 1321.72 801.21" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 1270.25 801.21 L 1321.72 801.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="v6300" d="M 1602.5 756.84 L 1602.5 738 L 1532.83 738 L 1532.83 810.1" stroke-width="1" zvalue="259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@1" LinkObjectIDznd="207@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602.5 756.84 L 1602.5 738 L 1532.83 738 L 1532.83 810.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="v6300" d="M 1602.61 814.25 L 1602.61 789.21" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602.61 814.25 L 1602.61 789.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="v6300" d="M 1563.75 738 L 1563.75 694.33" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206" LinkObjectIDznd="255@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1563.75 738 L 1563.75 694.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="v6300" d="M 1627.98 549.34 L 1627.98 614.91" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@1" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.98 549.34 L 1627.98 614.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="v6300" d="M 1627.98 651.5 L 1627.98 694.33" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@1" LinkObjectIDznd="255@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.98 651.5 L 1627.98 694.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="v6300" d="M 1599.12 579.95 L 1627.98 579.95" stroke-width="1" zvalue="269"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.12 579.95 L 1627.98 579.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="v6300" d="M 1657.63 579.95 L 1627.98 579.95" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@0" LinkObjectIDznd="6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1657.63 579.95 L 1627.98 579.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 1628.02 502.79 L 1628.02 462.28" stroke-width="1" zvalue="272"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="10@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1628.02 502.79 L 1628.02 462.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 1628.2 433.94 L 1628.2 390.43" stroke-width="1" zvalue="275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1628.2 433.94 L 1628.2 390.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="v6300" height="30" transform="rotate(0,970.368,876.681) scale(1.65589,1.66751) translate(-375.176,-340.927)" width="28" x="947.1856110738383" xlink:href="#EnergyConsumer:站用变DY接地_0" y="851.6686190813465" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738113030" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,970.368,876.681) scale(1.65589,1.66751) translate(-375.176,-340.927)" width="28" x="947.1856110738383" y="851.6686190813465"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="265">
   <use class="kv35" height="20" transform="rotate(270,1153.95,415.897) scale(-1.08456,1.08456) translate(-2217.51,-31.5811)" width="10" x="1148.528734781175" xlink:href="#GroundDisconnector:地刀_0" y="405.0510948657603" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738047494" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449738047494"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1153.95,415.897) scale(-1.08456,1.08456) translate(-2217.51,-31.5811)" width="10" x="1148.528734781175" y="405.0510948657603"/></g>
  <g id="360">
   <use class="kv35" height="20" transform="rotate(270,966.493,169.939) scale(-1.08456,1.08456) translate(-1857.21,-12.4042)" width="10" x="961.0701584662548" xlink:href="#GroundDisconnector:地刀_0" y="159.09379389233" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449737850886" ObjectName="35kV蕨叶电站线36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449737850886"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,966.493,169.939) scale(-1.08456,1.08456) translate(-1857.21,-12.4042)" width="10" x="961.0701584662548" y="159.09379389233"/></g>
  <g id="260">
   <use class="v6300" height="20" transform="rotate(270,1152.2,572.647) scale(-1.08456,1.08456) translate(-2214.15,-43.8026)" width="10" x="1146.778734781175" xlink:href="#GroundDisconnector:地刀_0" y="561.8010948657603" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449737654278" ObjectName="#1主变6.3kV侧60167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449737654278"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1152.2,572.647) scale(-1.08456,1.08456) translate(-2214.15,-43.8026)" width="10" x="1146.778734781175" y="561.8010948657603"/></g>
  <g id="173">
   <use class="v6300" height="20" transform="rotate(90,644.919,801.278) scale(-1.36693,1.36693) translate(-1114.89,-211.42)" width="10" x="638.0846439972852" xlink:href="#GroundDisconnector:地刀_0" y="787.6082794233603" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738899462" ObjectName="#1发电机66167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449738899462"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,644.919,801.278) scale(-1.36693,1.36693) translate(-1114.89,-211.42)" width="10" x="638.0846439972852" y="787.6082794233603"/></g>
  <g id="176">
   <use class="v6300" height="20" transform="rotate(90,900.919,813.278) scale(-1.36693,1.36693) translate(-1558.17,-214.641)" width="10" x="894.0846439972852" xlink:href="#GroundDisconnector:地刀_0" y="799.6082794233603" zvalue="229"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739030534" ObjectName="#1站用变66317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449739030534"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,900.919,813.278) scale(-1.36693,1.36693) translate(-1558.17,-214.641)" width="10" x="894.0846439972852" y="799.6082794233603"/></g>
  <g id="186">
   <use class="v6300" height="20" transform="rotate(90,1256.92,801.278) scale(-1.36693,1.36693) translate(-2174.61,-211.42)" width="10" x="1250.084643997285" xlink:href="#GroundDisconnector:地刀_0" y="787.6082794233603" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739161606" ObjectName="#2发电机66267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449739161606"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1256.92,801.278) scale(-1.36693,1.36693) translate(-2174.61,-211.42)" width="10" x="1250.084643997285" y="787.6082794233603"/></g>
  <g id="3">
   <use class="v6300" height="20" transform="rotate(270,1668.2,579.897) scale(-1.08456,1.08456) translate(-3205.91,-44.3678)" width="10" x="1662.778734781175" xlink:href="#GroundDisconnector:地刀_0" y="569.0510948657603" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739882502" ObjectName="近区变6.3kV侧66467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449739882502"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1668.2,579.897) scale(-1.08456,1.08456) translate(-3205.91,-44.3678)" width="10" x="1662.778734781175" y="569.0510948657603"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="263">
   <g id="2630">
    <use class="kv35" height="60" transform="rotate(0,1111.35,492.599) scale(1.19121,1.14107) translate(-174.565,-56.6665)" width="40" x="1087.52" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="458.37" zvalue="38"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430414852" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2631">
    <use class="v6300" height="60" transform="rotate(0,1111.35,492.599) scale(1.19121,1.14107) translate(-174.565,-56.6665)" width="40" x="1087.52" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="458.37" zvalue="38"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430480388" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447478276" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447478276"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1111.35,492.599) scale(1.19121,1.14107) translate(-174.565,-56.6665)" width="40" x="1087.52" y="458.37"/></g>
  <g id="1">
   <g id="10">
    <use class="kv10" height="60" transform="rotate(0,1627.98,526.02) scale(0.823062,0.788416) translate(346.438,134.818)" width="40" x="1611.52" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="502.37" zvalue="263"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430545924" ObjectName="10"/>
    </metadata>
   </g>
   <g id="11">
    <use class="v6300" height="60" transform="rotate(0,1627.98,526.02) scale(0.823062,0.788416) translate(346.438,134.818)" width="40" x="1611.52" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="502.37" zvalue="263"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430611460" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447543812" ObjectName="近区变"/>
   <cge:TPSR_Ref TObjectID="6755399447543812"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1627.98,526.02) scale(0.823062,0.788416) translate(346.438,134.818)" width="40" x="1611.52" y="502.37"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="144">
   <use class="kv35" height="26" transform="rotate(270,1051.05,415.918) scale(-0.976105,-0.976105) translate(-2127.97,-842.329)" width="12" x="1045.193348497951" xlink:href="#Accessory:避雷器1_0" y="403.2290321031971" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449737719814" ObjectName="#1主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1051.05,415.918) scale(-0.976105,-0.976105) translate(-2127.97,-842.329)" width="12" x="1045.193348497951" y="403.2290321031971"/></g>
  <g id="209">
   <use class="kv35" height="29" transform="rotate(0,862.806,199.417) scale(1.66667,1.66667) translate(-341.456,-70.1)" width="11" x="853.6388888888888" xlink:href="#Accessory:PT带保险_0" y="175.2499999999997" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449737457670" ObjectName="35kV蕨叶电站线设备1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,862.806,199.417) scale(1.66667,1.66667) translate(-341.456,-70.1)" width="11" x="853.6388888888888" y="175.2499999999997"/></g>
  <g id="253">
   <use class="v6300" height="23" transform="rotate(0,812.085,931.833) scale(1.71498,1.71498) translate(-333.197,-380.26)" width="15" x="799.2222222222222" xlink:href="#Accessory:PT1515_0" y="912.1111111111111" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449737392134" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,812.085,931.833) scale(1.71498,1.71498) translate(-333.197,-380.26)" width="15" x="799.2222222222222" y="912.1111111111111"/></g>
  <g id="142">
   <use class="v6300" height="26" transform="rotate(270,1051.05,547.918) scale(-0.976105,-0.976105) translate(-2127.97,-1109.56)" width="12" x="1045.193348497951" xlink:href="#Accessory:避雷器1_0" y="535.2290321031971" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738244102" ObjectName="#1主变6.3kV侧避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1051.05,547.918) scale(-0.976105,-0.976105) translate(-2127.97,-1109.56)" width="12" x="1045.193348497951" y="535.2290321031971"/></g>
  <g id="143">
   <use class="v6300" height="26" transform="rotate(270,1051.05,591.918) scale(-0.976105,-0.976105) translate(-2127.97,-1198.64)" width="12" x="1045.193348497951" xlink:href="#Accessory:避雷器1_0" y="579.2290321031971" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738309638" ObjectName="#1主变6.3kV侧避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1051.05,591.918) scale(-0.976105,-0.976105) translate(-2127.97,-1198.64)" width="12" x="1045.193348497951" y="579.2290321031971"/></g>
  <g id="154">
   <use class="kv35" height="20" transform="rotate(0,861.355,456) scale(1.675,1.675) translate(-338.676,-177.011)" width="25" x="840.4175840298944" xlink:href="#Accessory:4绕组母线PT_0" y="439.25" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738440710" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,861.355,456) scale(1.675,1.675) translate(-338.676,-177.011)" width="25" x="840.4175840298944" y="439.25"/></g>
  <g id="156">
   <use class="kv35" height="26" transform="rotate(180,791.55,447.168) scale(-0.976105,-0.976105) translate(-1602.62,-905.594)" width="12" x="785.6933484979505" xlink:href="#Accessory:避雷器1_0" y="434.4790321031971" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738506246" ObjectName="35kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,791.55,447.168) scale(-0.976105,-0.976105) translate(-1602.62,-905.594)" width="12" x="785.6933484979505" y="434.4790321031971"/></g>
  <g id="165">
   <use class="v6300" height="30" transform="rotate(0,621.277,949.25) scale(1.07143,-1.75) translate(-40.1685,-1480.43)" width="35" x="602.5270108739028" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="923" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738637318" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,621.277,949.25) scale(1.07143,-1.75) translate(-40.1685,-1480.43)" width="35" x="602.5270108739028" y="923"/></g>
  <g id="170">
   <use class="v6300" height="23" transform="rotate(0,768.085,963.833) scale(1.71498,1.71498) translate(-314.853,-393.601)" width="15" x="755.2222222222222" xlink:href="#Accessory:PT1515_0" y="944.1111111111111" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738768390" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,768.085,963.833) scale(1.71498,1.71498) translate(-314.853,-393.601)" width="15" x="755.2222222222222" y="944.1111111111111"/></g>
  <g id="197">
   <use class="v6300" height="23" transform="rotate(0,1424.08,931.833) scale(1.71498,1.71498) translate(-588.341,-380.26)" width="15" x="1411.222222222222" xlink:href="#Accessory:PT1515_0" y="912.1111111111111" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739489286" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,1424.08,931.833) scale(1.71498,1.71498) translate(-588.341,-380.26)" width="15" x="1411.222222222222" y="912.1111111111111"/></g>
  <g id="193">
   <use class="v6300" height="30" transform="rotate(0,1233.28,949.25) scale(1.07143,-1.75) translate(-80.9685,-1480.43)" width="35" x="1214.527010873903" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="923" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739358214" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1233.28,949.25) scale(1.07143,-1.75) translate(-80.9685,-1480.43)" width="35" x="1214.527010873903" y="923"/></g>
  <g id="189">
   <use class="v6300" height="23" transform="rotate(0,1380.08,963.833) scale(1.71498,1.71498) translate(-569.997,-393.601)" width="15" x="1367.222222222222" xlink:href="#Accessory:PT1515_0" y="944.1111111111111" zvalue="247"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739227142" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,1380.08,963.833) scale(1.71498,1.71498) translate(-569.997,-393.601)" width="15" x="1367.222222222222" y="944.1111111111111"/></g>
  <g id="211">
   <use class="v6300" height="20" transform="rotate(0,1602.61,831) scale(1.675,1.675) translate(-637.388,-328.131)" width="25" x="1581.667584029894" xlink:href="#Accessory:4绕组母线PT_0" y="814.25" zvalue="255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739685894" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1602.61,831) scale(1.675,1.675) translate(-637.388,-328.131)" width="25" x="1581.667584029894" y="814.25"/></g>
  <g id="207">
   <use class="v6300" height="26" transform="rotate(180,1532.8,822.168) scale(-0.976105,-0.976105) translate(-3103.27,-1664.77)" width="12" x="1526.943348497951" xlink:href="#Accessory:避雷器1_0" y="809.4790321031971" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739620358" ObjectName="6.3kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1532.8,822.168) scale(-0.976105,-0.976105) translate(-3103.27,-1664.77)" width="12" x="1526.943348497951" y="809.4790321031971"/></g>
  <g id="4">
   <use class="v6300" height="26" transform="rotate(270,1587.05,579.918) scale(-0.976105,-0.976105) translate(-3213.09,-1174.34)" width="12" x="1581.193348497951" xlink:href="#Accessory:避雷器1_0" y="567.2290321031971" zvalue="266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739948038" ObjectName="近区变6.3kV侧避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1587.05,579.918) scale(-0.976105,-0.976105) translate(-3213.09,-1174.34)" width="12" x="1581.193348497951" y="567.2290321031971"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="375">
   <use class="v6300" height="27" transform="rotate(0,970.52,752.789) scale(1.62002,1.60822) translate(-367.102,-276.49)" width="14" x="959.1800831229488" xlink:href="#Disconnector:带融断手车刀闸_0" y="731.0780115704102" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449737523206" ObjectName="#1站用变6631隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449737523206"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,970.52,752.789) scale(1.62002,1.60822) translate(-367.102,-276.49)" width="14" x="959.1800831229488" y="731.0780115704102"/></g>
  <g id="152">
   <use class="kv35" height="26" transform="rotate(0,861.25,398) scale(1.25,1.25) translate(-170.75,-76.35)" width="12" x="853.75" xlink:href="#Disconnector:20210316_0" y="381.75" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738375174" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449738375174"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,861.25,398) scale(1.25,1.25) translate(-170.75,-76.35)" width="12" x="853.75" y="381.75"/></g>
  <g id="163">
   <use class="v6300" height="26" transform="rotate(0,613.75,879.75) scale(1.25,1.25) translate(-121.25,-172.7)" width="12" x="606.25" xlink:href="#Disconnector:20210316_0" y="863.5" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738571782" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449738571782"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,613.75,879.75) scale(1.25,1.25) translate(-121.25,-172.7)" width="12" x="606.25" y="863.5"/></g>
  <g id="168">
   <use class="v6300" height="26" transform="rotate(0,769.75,899.75) scale(1.25,1.25) translate(-152.45,-176.7)" width="12" x="762.25" xlink:href="#Disconnector:20210316_0" y="883.5" zvalue="220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449738702854" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449738702854"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,769.75,899.75) scale(1.25,1.25) translate(-152.45,-176.7)" width="12" x="762.25" y="883.5"/></g>
  <g id="194">
   <use class="v6300" height="26" transform="rotate(0,1225.75,879.75) scale(1.25,1.25) translate(-243.65,-172.7)" width="12" x="1218.25" xlink:href="#Disconnector:20210316_0" y="863.5" zvalue="240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739423750" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449739423750"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1225.75,879.75) scale(1.25,1.25) translate(-243.65,-172.7)" width="12" x="1218.25" y="863.5"/></g>
  <g id="190">
   <use class="v6300" height="26" transform="rotate(0,1381.75,899.75) scale(1.25,1.25) translate(-274.85,-176.7)" width="12" x="1374.25" xlink:href="#Disconnector:20210316_0" y="883.5" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739292678" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449739292678"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1381.75,899.75) scale(1.25,1.25) translate(-274.85,-176.7)" width="12" x="1374.25" y="883.5"/></g>
  <g id="212">
   <use class="v6300" height="26" transform="rotate(0,1602.5,773) scale(1.25,1.25) translate(-319,-151.35)" width="12" x="1595" xlink:href="#Disconnector:20210316_0" y="756.75" zvalue="254"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449739751430" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449739751430"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1602.5,773) scale(1.25,1.25) translate(-319,-151.35)" width="12" x="1595" y="756.75"/></g>
  <g id="10">
   <use class="kv10" height="30" transform="rotate(0,1628.11,448.667) scale(1.11111,1.11111) translate(-161.977,-43.2)" width="15" x="1619.774984806294" xlink:href="#Disconnector:令克_0" y="432" zvalue="271"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740013574" ObjectName="近区变10kV侧刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449740013574"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1628.11,448.667) scale(1.11111,1.11111) translate(-161.977,-43.2)" width="15" x="1619.774984806294" y="432"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="12">
   <use class="kv10" height="30" transform="rotate(0,1628.2,381.648) scale(6.19749,0.591361) translate(-1347.29,257.595)" width="7" x="1606.509687368373" xlink:href="#ACLineSegment:线路_0" y="372.7777777777778" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740079110" ObjectName="至近区"/>
   <cge:TPSR_Ref TObjectID="6192449740079110_5066549584461826"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1628.2,381.648) scale(6.19749,0.591361) translate(-1347.29,257.595)" width="7" x="1606.509687368373" y="372.7777777777778"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="222" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124883922949" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="221" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124883988485" ObjectName="GEN_QSUM"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="34" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124883791877" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124883857413" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124884185093" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124884316165" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124883791877" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124883791877" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,403.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="408.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124884250629" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876582917" ObjectName="F"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,339.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877697029" ObjectName="F"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="84" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,908.703,13.0963) scale(1,1) translate(0,-2.22045e-15)" writing-mode="lr" x="908.23" xml:space="preserve" y="17.76" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124872060933" ObjectName="P"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="85" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,908.703,35.5) scale(1,1) translate(0,-2.21189e-14)" writing-mode="lr" x="908.23" xml:space="preserve" y="40.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124872126469" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,908.703,57.9037) scale(1,1) translate(0,-4.20174e-14)" writing-mode="lr" x="908.23" xml:space="preserve" y="62.57" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124872192005" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="89" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1043.85,332.831) scale(1,1) translate(0,3.55679e-14)" writing-mode="lr" x="1043.29" xml:space="preserve" y="337.53" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124873633797" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="90" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1043.85,359.184) scale(1,1) translate(0,3.84936e-14)" writing-mode="lr" x="1043.29" xml:space="preserve" y="363.88" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124873699333" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1210.1,614.69) scale(1,1) translate(0,-1.338e-13)" writing-mode="lr" x="1209.54" xml:space="preserve" y="619.38" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124873764869" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="93" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1210.1,640.291) scale(1,1) translate(0,-1.39484e-13)" writing-mode="lr" x="1209.54" xml:space="preserve" y="644.98" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124873830405" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="94" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1043.85,385.536) scale(1,1) translate(0,4.14193e-14)" writing-mode="lr" x="1043.29" xml:space="preserve" y="390.23" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124873895941" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1210.1,665.892) scale(1,1) translate(0,2.90338e-13)" writing-mode="lr" x="1209.54" xml:space="preserve" y="670.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124874223621" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="96" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,703.471,976.549) scale(1,1) translate(0,-1.92872e-12)" writing-mode="lr" x="702.92" xml:space="preserve" y="981.23" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877828101" ObjectName="P"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="97" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1315.47,980.549) scale(1,1) translate(0,-1.93671e-12)" writing-mode="lr" x="1314.92" xml:space="preserve" y="985.23" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124879532037" ObjectName="P"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="98" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,703.471,1000.69) scale(1,1) translate(0,-1.97696e-12)" writing-mode="lr" x="702.92" xml:space="preserve" y="1005.37" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877893637" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="99" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1315.47,1004.69) scale(1,1) translate(0,-1.98496e-12)" writing-mode="lr" x="1314.92" xml:space="preserve" y="1009.37" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124879597573" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,703.471,1024.83) scale(1,1) translate(0,-2.02521e-12)" writing-mode="lr" x="702.92" xml:space="preserve" y="1029.51" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877959173" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="101" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1315.47,1028.83) scale(1,1) translate(0,-2.0332e-12)" writing-mode="lr" x="1314.92" xml:space="preserve" y="1033.51" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124879663109" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,841.483,536.432) scale(1,1) translate(8.84274e-14,0)" writing-mode="lr" x="841.01" xml:space="preserve" y="541.21" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876189701" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1704,862.831) scale(1,1) translate(0,0)" writing-mode="lr" x="1703.53" xml:space="preserve" y="867.61" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877303813" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="46" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,841.483,573.432) scale(1,1) translate(8.84274e-14,0)" writing-mode="lr" x="841.01" xml:space="preserve" y="578.21" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876255237" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="47" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1704,899.831) scale(1,1) translate(0,0)" writing-mode="lr" x="1703.53" xml:space="preserve" y="904.61" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877369349" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="48" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,841.483,610.432) scale(1,1) translate(8.84274e-14,0)" writing-mode="lr" x="841.01" xml:space="preserve" y="615.21" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876320773" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="49" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1704,936.831) scale(1,1) translate(0,0)" writing-mode="lr" x="1703.53" xml:space="preserve" y="941.61" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877434885" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="50" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,663.483,324.932) scale(1,1) translate(0,0)" writing-mode="lr" x="663.01" xml:space="preserve" y="329.71" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876451845" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="51" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,505,717.331) scale(1,1) translate(0,0)" writing-mode="lr" x="504.53" xml:space="preserve" y="722.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877565957" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,661.483,345.932) scale(1,1) translate(0,0)" writing-mode="lr" x="661.01" xml:space="preserve" y="350.71" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876517381" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,660.483,371.932) scale(1,1) translate(0,0)" writing-mode="lr" x="660.01" xml:space="preserve" y="376.71" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876386309" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="57" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,661.483,397.932) scale(1,1) translate(0,0)" writing-mode="lr" x="661.01" xml:space="preserve" y="402.71" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876189701" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="58" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,660.483,427.932) scale(1,1) translate(0,0)" writing-mode="lr" x="660.01" xml:space="preserve" y="432.71" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876255237" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="59" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,657.483,455.932) scale(1,1) translate(0,0)" writing-mode="lr" x="657.01" xml:space="preserve" y="460.71" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876320773" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="60" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,658.483,484.932) scale(1,1) translate(0,0)" writing-mode="lr" x="658.01" xml:space="preserve" y="489.71" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124876648453" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="61" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,502,741.331) scale(1,1) translate(0,0)" writing-mode="lr" x="501.53" xml:space="preserve" y="746.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877631493" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="67" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,503,766.331) scale(1,1) translate(0,0)" writing-mode="lr" x="502.53" xml:space="preserve" y="771.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877500421" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,502,790.331) scale(1,1) translate(0,0)" writing-mode="lr" x="501.53" xml:space="preserve" y="795.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877303813" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="87" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,499,816.331) scale(1,1) translate(0,0)" writing-mode="lr" x="498.53" xml:space="preserve" y="821.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877369349" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="88" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,498,844.331) scale(1,1) translate(0,0)" writing-mode="lr" x="497.53" xml:space="preserve" y="849.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877434885" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="102" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,495,869.331) scale(1,1) translate(0,0)" writing-mode="lr" x="494.53" xml:space="preserve" y="874.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124877762565" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="218">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="294"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374887616515" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="215">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="295"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950401556484" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>