<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549580922882" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV香柏河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="48.25" xlink:href="logo.png" y="38.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,197.25,68.75) scale(1,1) translate(0,0)" writing-mode="lr" x="197.25" xml:space="preserve" y="72.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,193.75,68.4403) scale(1,1) translate(0,0)" writing-mode="lr" x="193.75" xml:space="preserve" y="77.44" zvalue="3">110kV香柏河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="53" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="292"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="292">信号一览</text>
  <line fill="none" id="140" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="390.25" x2="390.25" y1="6.75" y2="1036.75" zvalue="4"/>
  <line fill="none" id="136" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25000000000045" x2="383.25" y1="142.6204926140824" y2="142.6204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,602.889,270) scale(1,1) translate(0,0)" writing-mode="lr" x="602.89" xml:space="preserve" y="274.5" zvalue="37">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.5,339.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.5" xml:space="preserve" y="343.72" zvalue="39">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.86,395.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.86" xml:space="preserve" y="399.72" zvalue="41">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1126.19,372.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.19" xml:space="preserve" y="376.72" zvalue="44">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.61,435.669) scale(1,1) translate(0,0)" writing-mode="lr" x="1127.61" xml:space="preserve" y="440.17" zvalue="47">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1708.99,629.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1708.99" xml:space="preserve" y="634" zvalue="51">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.306,364.056) scale(1,1) translate(0,0)" writing-mode="lr" x="784.3099999999999" xml:space="preserve" y="368.56" zvalue="53">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.333,421.694) scale(1,1) translate(0,0)" writing-mode="lr" x="794.33" xml:space="preserve" y="426.19" zvalue="55">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.833,338.75) scale(1,1) translate(0,0)" writing-mode="lr" x="801.83" xml:space="preserve" y="343.25" zvalue="57">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,771.974,511.875) scale(1,1) translate(-1.56092e-13,0)" writing-mode="lr" x="771.97" xml:space="preserve" y="516.38" zvalue="63">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.836,951.455) scale(1,1) translate(0,3.09984e-13)" writing-mode="lr" x="628.8360901404426" xml:space="preserve" y="955.9547398511555" zvalue="70">#1发电机 6.3MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1357.88,903.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1357.88" xml:space="preserve" y="907.75" zvalue="72">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.52,583.743) scale(1,1) translate(0,0)" writing-mode="lr" x="1468.52" xml:space="preserve" y="588.24" zvalue="74">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1687.88,582.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1687.88" xml:space="preserve" y="587" zvalue="81">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.58,215.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.58" xml:space="preserve" y="219.67" zvalue="109">111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1094.81,262.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.81" xml:space="preserve" y="267.22" zvalue="110">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1096.47,154.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1096.47" xml:space="preserve" y="159.22" zvalue="113">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.93,19.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1076.93" xml:space="preserve" y="24" zvalue="117">110kV香柏河一二级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.31,250.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.31" xml:space="preserve" y="254.67" zvalue="120">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.31,201.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.31" xml:space="preserve" y="205.89" zvalue="122">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.31,146.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.31" xml:space="preserve" y="151.44" zvalue="124">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1218.22,493) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.22" xml:space="preserve" y="497.5" zvalue="132">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1432.83,453.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.83" xml:space="preserve" y="457.75" zvalue="136">10kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,504.75,970.5) scale(1,1) translate(0,0)" writing-mode="lr" x="504.75" xml:space="preserve" y="975" zvalue="141">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557,966.5) scale(1,1) translate(0,0)" writing-mode="lr" x="557" xml:space="preserve" y="971" zvalue="143">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,707.75,968.25) scale(1,1) translate(0,0)" writing-mode="lr" x="707.75" xml:space="preserve" y="972.75" zvalue="152">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324.47,701.493) scale(1,1) translate(0,0)" writing-mode="lr" x="1324.47" xml:space="preserve" y="705.99" zvalue="183">0131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1402.92,723.667) scale(1,1) translate(-3.09752e-13,0)" writing-mode="lr" x="1402.92" xml:space="preserve" y="728.17" zvalue="187">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383.07,790.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.07" xml:space="preserve" y="795.22" zvalue="189">013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,605.562,701.493) scale(1,1) translate(0,0)" writing-mode="lr" x="605.5599999999999" xml:space="preserve" y="705.99" zvalue="197">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,672.861,698.667) scale(1,1) translate(0,0)" writing-mode="lr" x="672.86" xml:space="preserve" y="703.17" zvalue="201">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,645.553,774.222) scale(1,1) translate(0,0)" writing-mode="lr" x="645.55" xml:space="preserve" y="778.72" zvalue="204">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,471.812,843.993) scale(1,1) translate(0,0)" writing-mode="lr" x="471.81" xml:space="preserve" y="848.49" zvalue="211">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,550.111,848.417) scale(1,1) translate(0,0)" writing-mode="lr" x="550.11" xml:space="preserve" y="852.92" zvalue="215">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.84,955.455) scale(1,1) translate(0,3.11316e-13)" writing-mode="lr" x="1084.836090140443" xml:space="preserve" y="959.9547398511555" zvalue="218">#2发电机 6.3MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,960.75,970.5) scale(1,1) translate(0,0)" writing-mode="lr" x="960.75" xml:space="preserve" y="975" zvalue="220">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1013,966.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1013" xml:space="preserve" y="971" zvalue="222">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1163.75,968.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.75" xml:space="preserve" y="972.75" zvalue="225">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.56,701.493) scale(1,1) translate(0,0)" writing-mode="lr" x="1061.56" xml:space="preserve" y="705.99" zvalue="228">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1128.86,698.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.86" xml:space="preserve" y="703.17" zvalue="232">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1101.55,774.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.55" xml:space="preserve" y="778.72" zvalue="235">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.812,843.993) scale(1,1) translate(0,0)" writing-mode="lr" x="927.8099999999999" xml:space="preserve" y="848.49" zvalue="239">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.111,852.417) scale(1,1) translate(0,0)" writing-mode="lr" x="998.11" xml:space="preserve" y="856.92" zvalue="243">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1110.6,559.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1110.6" xml:space="preserve" y="563.72" zvalue="250">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1066.33,523.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.33" xml:space="preserve" y="527.72" zvalue="252">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1059.23,615.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1059.23" xml:space="preserve" y="619.72" zvalue="255">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1153.19,594.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1153.19" xml:space="preserve" y="598.9400000000001" zvalue="263">17</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="90" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="266"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="268">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="269">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="270">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="271">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="272">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="274">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="275">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="276">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="280">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="281">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="282">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.125,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="238.13" xml:space="preserve" y="339.75" zvalue="283">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="293">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="294">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="297">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="299">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="301">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="303">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="305">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="307">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194,954) scale(1,1) translate(0,0)" writing-mode="lr" x="194" xml:space="preserve" y="960" zvalue="329">XBH1-01-2009</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="292"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="146">
   <path class="kv110" d="M 584 296 L 1459 296" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674231910404" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674231910404"/></metadata>
  <path d="M 584 296 L 1459 296" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 510.11 651.75 L 1772.86 651.75" stroke-width="6" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674231844869" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674231844869"/></metadata>
  <path d="M 510.11 651.75 L 1772.86 651.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="145">
   <use class="kv110" height="30" transform="rotate(0,1082.05,340.222) scale(1.11111,0.814815) translate(-107.372,74.5455)" width="15" x="1073.721606553643" xlink:href="#Disconnector:刀闸_0" y="328" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501331462" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449501331462"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1082.05,340.222) scale(1.11111,0.814815) translate(-107.372,74.5455)" width="15" x="1073.721606553643" y="328"/></g>
  <g id="125">
   <use class="kv110" height="30" transform="rotate(0,758.972,365.056) scale(-1.11111,-0.814815) translate(-1441.21,-815.856)" width="15" x="750.6388888888888" xlink:href="#Disconnector:刀闸_0" y="352.8333470026652" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501134854" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449501134854"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,758.972,365.056) scale(-1.11111,-0.814815) translate(-1441.21,-815.856)" width="15" x="750.6388888888888" y="352.8333470026652"/></g>
  <g id="130">
   <use class="kv10" height="30" transform="rotate(0,1433.53,574.743) scale(1.9625,1.31713) translate(-695.851,-133.626)" width="15" x="1418.8125" xlink:href="#Disconnector:刀闸_0" y="554.9861111111111" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500610566" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449500610566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1433.53,574.743) scale(1.9625,1.31713) translate(-695.851,-133.626)" width="15" x="1418.8125" y="554.9861111111111"/></g>
  <g id="154">
   <use class="kv10" height="30" transform="rotate(0,1691.11,438.917) scale(3.89103,1.4055) translate(-1213.13,-120.55)" width="30" x="1632.745610127697" xlink:href="#Disconnector:跌落刀闸_0" y="417.834974364315" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500479494" ObjectName="#2站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449500479494"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1691.11,438.917) scale(3.89103,1.4055) translate(-1213.13,-120.55)" width="30" x="1632.745610127697" y="417.834974364315"/></g>
  <g id="219">
   <use class="kv110" height="30" transform="rotate(0,1082.25,263.722) scale(-1.11111,-0.814815) translate(-2055.44,-590.159)" width="15" x="1073.916666666667" xlink:href="#Disconnector:刀闸_0" y="251.5000133514405" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500413958" ObjectName="110kV香柏河一二级线1111隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449500413958"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1082.25,263.722) scale(-1.11111,-0.814815) translate(-2055.44,-590.159)" width="15" x="1073.916666666667" y="251.5000133514405"/></g>
  <g id="218">
   <use class="kv110" height="30" transform="rotate(0,1082.25,155.722) scale(-1.11111,-0.814815) translate(-2055.44,-349.614)" width="15" x="1073.916666693158" xlink:href="#Disconnector:刀闸_0" y="143.4999997880724" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500348422" ObjectName="110kV香柏河一二级线1116隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449500348422"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1082.25,155.722) scale(-1.11111,-0.814815) translate(-2055.44,-349.614)" width="15" x="1073.916666693158" y="143.4999997880724"/></g>
  <g id="149">
   <use class="kv10" height="30" transform="rotate(0,1361.03,693.743) scale(1.9625,1.2338) translate(-660.293,-127.953)" width="15" x="1346.3125" xlink:href="#Disconnector:刀闸_0" y="675.2361111111111" zvalue="182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501396998" ObjectName="#1站用变0131隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449501396998"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1361.03,693.743) scale(1.9625,1.2338) translate(-660.293,-127.953)" width="15" x="1346.3125" y="675.2361111111111"/></g>
  <g id="165">
   <use class="kv10" height="30" transform="rotate(0,624.781,702.493) scale(1.9625,1.2338) translate(-299.203,-129.611)" width="15" x="610.0625" xlink:href="#Disconnector:刀闸_0" y="683.9861111111111" zvalue="196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501593606" ObjectName="#1发电机0111隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449501593606"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,624.781,702.493) scale(1.9625,1.2338) translate(-299.203,-129.611)" width="15" x="610.0625" y="683.9861111111111"/></g>
  <g id="178">
   <use class="kv10" height="30" transform="rotate(0,506.031,844.993) scale(1.9625,1.2338) translate(-240.962,-156.614)" width="15" x="491.3125" xlink:href="#Disconnector:刀闸_0" y="826.4861111111111" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501790214" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449501790214"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,506.031,844.993) scale(1.9625,1.2338) translate(-240.962,-156.614)" width="15" x="491.3125" y="826.4861111111111"/></g>
  <g id="225">
   <use class="kv10" height="30" transform="rotate(0,1080.78,702.493) scale(1.9625,1.2338) translate(-522.846,-129.611)" width="15" x="1066.0625" xlink:href="#Disconnector:刀闸_0" y="683.9861111111111" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449502314502" ObjectName="#2发电机0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449502314502"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1080.78,702.493) scale(1.9625,1.2338) translate(-522.846,-129.611)" width="15" x="1066.0625" y="683.9861111111111"/></g>
  <g id="215">
   <use class="kv10" height="30" transform="rotate(0,962.031,844.993) scale(1.9625,1.2338) translate(-464.605,-156.614)" width="15" x="947.3125" xlink:href="#Disconnector:刀闸_0" y="826.4861111111111" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449502117894" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449502117894"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,962.031,844.993) scale(1.9625,1.2338) translate(-464.605,-156.614)" width="15" x="947.3125" y="826.4861111111111"/></g>
  <g id="4">
   <use class="kv10" height="30" transform="rotate(0,1083.02,524.222) scale(1.11111,0.814815) translate(-107.469,116.364)" width="15" x="1074.689688312406" xlink:href="#Disconnector:刀闸_0" y="512" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449668513798" ObjectName="#1主变10kV侧0016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449668513798"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1083.02,524.222) scale(1.11111,0.814815) translate(-107.469,116.364)" width="15" x="1074.689688312406" y="512"/></g>
  <g id="6">
   <use class="kv10" height="30" transform="rotate(0,1083.07,616.222) scale(1.11111,0.814815) translate(-107.473,137.273)" width="15" x="1074.733961610741" xlink:href="#Disconnector:刀闸_0" y="604" zvalue="254"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449668579334" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449668579334"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1083.07,616.222) scale(1.11111,0.814815) translate(-107.473,137.273)" width="15" x="1074.733961610741" y="604"/></g>
 </g>
 <g id="BreakerClass">
  <g id="144">
   <use class="kv110" height="20" transform="rotate(0,1083.08,396.222) scale(1.22222,1.11111) translate(-195.813,-38.5111)" width="10" x="1076.972232407994" xlink:href="#Breaker:开关_0" y="385.1111111111111" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466114565" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466114565"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1083.08,396.222) scale(1.22222,1.11111) translate(-195.813,-38.5111)" width="10" x="1076.972232407994" y="385.1111111111111"/></g>
  <g id="220">
   <use class="kv110" height="20" transform="rotate(0,1082.25,216.167) scale(1.22222,1.11111) translate(-195.662,-20.5056)" width="10" x="1076.138888888889" xlink:href="#Breaker:开关_0" y="205.0555553436279" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466049029" ObjectName="110kV香柏河一二级线111断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466049029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1082.25,216.167) scale(1.22222,1.11111) translate(-195.662,-20.5056)" width="10" x="1076.138888888889" y="205.0555553436279"/></g>
  <g id="155">
   <use class="kv10" height="20" transform="rotate(0,1361.83,791.722) scale(1.22222,1.11111) translate(-246.495,-78.0611)" width="10" x="1355.722232407994" xlink:href="#Breaker:开关_0" y="780.6111111111111" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466180101" ObjectName="#1站用变013断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466180101"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1361.83,791.722) scale(1.22222,1.11111) translate(-246.495,-78.0611)" width="10" x="1355.722232407994" y="780.6111111111111"/></g>
  <g id="170">
   <use class="kv10" height="20" transform="rotate(0,624.942,775.222) scale(1.22222,1.11111) translate(-112.515,-76.4111)" width="10" x="618.8309148119373" xlink:href="#Breaker:开关_0" y="764.1111111111111" zvalue="203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466245637" ObjectName="#1发电机011断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466245637"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,624.942,775.222) scale(1.22222,1.11111) translate(-112.515,-76.4111)" width="10" x="618.8309148119373" y="764.1111111111111"/></g>
  <g id="221">
   <use class="kv10" height="20" transform="rotate(0,1080.94,775.222) scale(1.22222,1.11111) translate(-195.424,-76.4111)" width="10" x="1074.830914811937" xlink:href="#Breaker:开关_0" y="764.1111111111111" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466311173" ObjectName="#2发电机012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466311173"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1080.94,775.222) scale(1.22222,1.11111) translate(-195.424,-76.4111)" width="10" x="1074.830914811937" y="764.1111111111111"/></g>
  <g id="3">
   <use class="kv10" height="20" transform="rotate(0,1083.08,560.222) scale(1.22222,1.11111) translate(-195.813,-54.9111)" width="10" x="1076.972232407994" xlink:href="#Breaker:开关_0" y="549.1111111111111" zvalue="249"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924487348229" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924487348229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1083.08,560.222) scale(1.22222,1.11111) translate(-195.813,-54.9111)" width="10" x="1076.972232407994" y="549.1111111111111"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="99">
   <path class="kv110" d="M 1082.12 352.24 L 1082.12 385.59" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="144@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.12 352.24 L 1082.12 385.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 1082.15 328.4 L 1082.15 296" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="146@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.15 328.4 L 1082.15 296" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 1083.16 406.83 L 1083.16 436.52" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@1" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.16 406.83 L 1083.16 436.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 1115.58 359.99 L 1082.12 359.99" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="99" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.58 359.99 L 1082.12 359.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv110" d="M 758.9 353.04 L 758.9 296" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="146@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.9 353.04 L 758.9 296" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv110" d="M 791.22 326.29 L 758.9 326.29" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 791.22 326.29 L 758.9 326.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv10" d="M 1433.7 513.36 L 1433.7 555.64" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.7 513.36 L 1433.7 555.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 1433.65 594.16 L 1433.65 651.75" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.65 594.16 L 1433.65 651.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv110" d="M 1082.15 275.54 L 1082.15 296" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="146@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.15 275.54 L 1082.15 296" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv110" d="M 1082.18 251.71 L 1082.18 226.78" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@1" LinkObjectIDznd="220@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.18 251.71 L 1082.18 226.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv110" d="M 1082.21 205.54 L 1082.15 167.54" stroke-width="1" zvalue="115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.21 205.54 L 1082.15 167.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv110" d="M 1082.18 143.71 L 1082.18 103.28" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@1" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.18 143.71 L 1082.18 103.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 1119.25 132.38 L 1082.18 132.38" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.25 132.38 L 1082.18 132.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv110" d="M 1119.25 186.82 L 1082.18 186.82" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="75" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.25 186.82 L 1082.18 186.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 1119.25 235.6 L 1082.18 235.6" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.25 235.6 L 1082.18 235.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv110" d="M 1083.14 452.97 L 1163.56 452.97 L 1163.56 480.44" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@2" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.14 452.97 L 1163.56 452.97 L 1163.56 480.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 1691.91 513.89 L 1691.91 458.59" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="154@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1691.91 513.89 L 1691.91 458.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 1691.11 417.83 L 1691.11 374.53" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1691.11 417.83 L 1691.11 374.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 554.44 914.46 L 554.44 895 L 505.86 895" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 554.44 914.46 L 554.44 895 L 505.86 895" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv110" d="M 1014.19 109.47 L 1082.18 109.47" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 1014.19 109.47 L 1082.18 109.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv110" d="M 1019.4 137.99 L 1041 137.99 L 1041 109.47" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.4 137.99 L 1041 137.99 L 1041 109.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 1361.2 675.85 L 1361.2 651.75" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1361.2 675.85 L 1361.2 651.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 1361.91 834.64 L 1361.91 802.33" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="155@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1361.91 834.64 L 1361.91 802.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv10" d="M 1361.79 781.09 L 1361.79 711.93" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="149@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1361.79 781.09 L 1361.79 711.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 1397.58 752.6 L 1361.79 752.6" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.58 752.6 L 1361.79 752.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 708.14 897.72 L 708.14 823 L 624.05 823" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 708.14 897.72 L 708.14 823 L 624.05 823" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 624.95 684.6 L 624.95 651.75" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="127@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 624.95 684.6 L 624.95 651.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 660.08 741.35 L 624.9 741.35" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.08 741.35 L 624.9 741.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 624.05 878.72 L 624.05 785.83" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 624.05 878.72 L 624.05 785.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 624.9 764.59 L 624.9 720.68" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 624.9 764.59 L 624.9 720.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 505.86 919.75 L 505.86 863.18" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="178@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 505.86 919.75 L 505.86 863.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 506.2 827.1 L 506.2 817 L 624.05 817" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 506.2 827.1 L 506.2 817 L 624.05 817" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv10" d="M 541.33 875.1 L 505.86 875.1" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 541.33 875.1 L 505.86 875.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv10" d="M 1010.44 914.46 L 1010.44 895 L 961.86 895" stroke-width="1" zvalue="223"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 1010.44 914.46 L 1010.44 895 L 961.86 895" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv10" d="M 1164.14 897.72 L 1164.14 823 L 1080.05 823" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 1164.14 897.72 L 1164.14 823 L 1080.05 823" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 1080.95 684.6 L 1080.95 651.75" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225@0" LinkObjectIDznd="127@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.95 684.6 L 1080.95 651.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv10" d="M 1116.08 741.35 L 1080.9 741.35" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1116.08 741.35 L 1080.9 741.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 1080.05 878.72 L 1080.05 785.83" stroke-width="1" zvalue="234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="221@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.05 878.72 L 1080.05 785.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 1080.9 764.59 L 1080.9 720.68" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="225@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.9 764.59 L 1080.9 720.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 961.86 919.75 L 961.86 863.18" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="215@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 961.86 919.75 L 961.86 863.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 962.2 827.1 L 962.2 817 L 1080.05 817" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.2 827.1 L 962.2 817 L 1080.05 817" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv10" d="M 997.33 875.1 L 961.86 875.1" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.33 875.1 L 961.86 875.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv110" d="M 758.87 376.87 L 758.87 442.28" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.87 376.87 L 758.87 442.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv110" d="M 783.72 405.46 L 758.87 405.46" stroke-width="1" zvalue="247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="1" MaxPinNum="2"/>
   </metadata>
  <path d="M 783.72 405.46 L 758.87 405.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 1083.12 493.56 L 1083.12 512.4" stroke-width="1" zvalue="257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@1" LinkObjectIDznd="4@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.12 493.56 L 1083.12 512.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 1083.09 536.24 L 1083.04 549.59" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@1" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.09 536.24 L 1083.04 549.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 1083.16 570.83 L 1083.16 604.4" stroke-width="1" zvalue="259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@1" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.16 570.83 L 1083.16 604.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv10" d="M 1083.14 628.24 L 1083.14 651.75" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@1" LinkObjectIDznd="127@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.14 628.24 L 1083.14 651.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 1115.58 591.99 L 1083.16 591.99" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="23" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.58 591.99 L 1083.16 591.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="142">
   <use class="kv110" height="30" transform="rotate(270,1126.19,360) scale(-0.925926,0.740741) translate(-2342.93,122.111)" width="12" x="1120.638899061415" xlink:href="#GroundDisconnector:地刀12_0" y="348.8888888888889" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501265926" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449501265926"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1126.19,360) scale(-0.925926,0.740741) translate(-2342.93,122.111)" width="12" x="1120.638899061415" y="348.8888888888889"/></g>
  <g id="124">
   <use class="kv110" height="30" transform="rotate(270,794.333,405.472) scale(-0.925926,0.740741) translate(-1652.66,138.026)" width="12" x="788.7777845594618" xlink:href="#GroundDisconnector:地刀12_0" y="394.3611008326212" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501069318" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449501069318"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,794.333,405.472) scale(-0.925926,0.740741) translate(-1652.66,138.026)" width="12" x="788.7777845594618" y="394.3611008326212"/></g>
  <g id="123">
   <use class="kv110" height="30" transform="rotate(270,801.833,326.306) scale(-0.925926,0.740741) translate(-1668.26,110.318)" width="12" x="796.2777845594618" xlink:href="#GroundDisconnector:地刀12_0" y="315.1944444444444" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500938246" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449500938246"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,801.833,326.306) scale(-0.925926,0.740741) translate(-1668.26,110.318)" width="12" x="796.2777845594618" y="315.1944444444444"/></g>
  <g id="212">
   <use class="kv110" height="30" transform="rotate(270,1129.86,235.611) scale(-0.925926,0.740741) translate(-2350.56,78.575)" width="12" x="1124.305555555556" xlink:href="#GroundDisconnector:地刀12_0" y="224.5000050862629" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500217350" ObjectName="110kV香柏河一二级线11117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449500217350"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1129.86,235.611) scale(-0.925926,0.740741) translate(-2350.56,78.575)" width="12" x="1124.305555555556" y="224.5000050862629"/></g>
  <g id="211">
   <use class="kv110" height="30" transform="rotate(270,1129.86,186.833) scale(-0.925926,0.740741) translate(-2350.56,61.5028)" width="12" x="1124.305555555556" xlink:href="#GroundDisconnector:地刀12_0" y="175.7222220102947" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500086278" ObjectName="110kV香柏河一二级线11160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449500086278"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1129.86,186.833) scale(-0.925926,0.740741) translate(-2350.56,61.5028)" width="12" x="1124.305555555556" y="175.7222220102947"/></g>
  <g id="210">
   <use class="kv110" height="30" transform="rotate(270,1129.86,132.389) scale(-0.925926,0.740741) translate(-2350.56,42.4472)" width="12" x="1124.305555661519" xlink:href="#GroundDisconnector:地刀12_0" y="121.2777775658501" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499955206" ObjectName="110kV香柏河一二级线11167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449499955206"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1129.86,132.389) scale(-0.925926,0.740741) translate(-2350.56,42.4472)" width="12" x="1124.305555661519" y="121.2777775658501"/></g>
  <g id="177">
   <use class="kv110" height="40" transform="rotate(0,1178.44,494) scale(1.11111,-1.11111) translate(-115.622,-936.378)" width="40" x="1156.222222222222" xlink:href="#GroundDisconnector:中性点地刀12_0" y="471.7777777777778" zvalue="131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499693062" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449499693062"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1178.44,494) scale(1.11111,-1.11111) translate(-115.622,-936.378)" width="40" x="1156.222222222222" y="471.7777777777778"/></g>
  <g id="152">
   <use class="kv10" height="30" transform="rotate(270,1410.36,752.611) scale(-1.11574,0.892592) translate(-2673.73,88.9523)" width="12" x="1403.66667175293" xlink:href="#GroundDisconnector:地刀12_0" y="739.2222136391533" zvalue="186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501528070" ObjectName="#1站用变01317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449501528070"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1410.36,752.611) scale(-1.11574,0.892592) translate(-2673.73,88.9523)" width="12" x="1403.66667175293" y="739.2222136391533"/></g>
  <g id="169">
   <use class="kv10" height="30" transform="rotate(270,672.861,741.361) scale(-1.11574,0.892592) translate(-1275.23,87.5985)" width="12" x="666.1666717529297" xlink:href="#GroundDisconnector:地刀12_0" y="727.9722136391533" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501724678" ObjectName="#1发电机01117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449501724678"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,672.861,741.361) scale(-1.11574,0.892592) translate(-1275.23,87.5985)" width="12" x="666.1666717529297" y="727.9722136391533"/></g>
  <g id="186">
   <use class="kv10" height="30" transform="rotate(270,554.111,875.111) scale(-1.11574,0.892592) translate(-1050.05,103.693)" width="12" x="547.4166717529297" xlink:href="#GroundDisconnector:地刀12_0" y="861.7222136391533" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449501921285" ObjectName="#1发电机09117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449501921285"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,554.111,875.111) scale(-1.11574,0.892592) translate(-1050.05,103.693)" width="12" x="547.4166717529297" y="861.7222136391533"/></g>
  <g id="223">
   <use class="kv10" height="30" transform="rotate(270,1128.86,741.361) scale(-1.11574,0.892592) translate(-2139.93,87.5985)" width="12" x="1122.16667175293" xlink:href="#GroundDisconnector:地刀12_0" y="727.9722136391533" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449502248966" ObjectName="#2发电机01217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449502248966"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1128.86,741.361) scale(-1.11574,0.892592) translate(-2139.93,87.5985)" width="12" x="1122.16667175293" y="727.9722136391533"/></g>
  <g id="208">
   <use class="kv10" height="30" transform="rotate(270,1010.11,875.111) scale(-1.11574,0.892592) translate(-1914.74,103.693)" width="12" x="1003.41667175293" xlink:href="#GroundDisconnector:地刀12_0" y="861.7222136391533" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449502052358" ObjectName="#2发电机09217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449502052358"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1010.11,875.111) scale(-1.11574,0.892592) translate(-1914.74,103.693)" width="12" x="1003.41667175293" y="861.7222136391533"/></g>
  <g id="28">
   <use class="kv10" height="30" transform="rotate(270,1126.19,592) scale(-0.925926,0.740741) translate(-2342.93,203.311)" width="12" x="1120.638899061415" xlink:href="#GroundDisconnector:地刀12_0" y="580.8888888888889" zvalue="262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449668710406" ObjectName="#1主变10kV侧00117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449668710406"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1126.19,592) scale(-0.925926,0.740741) translate(-2342.93,203.311)" width="12" x="1120.638899061415" y="580.8888888888889"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="138">
   <g id="1380">
    <use class="kv110" height="50" transform="rotate(0,1083.12,464.984) scale(1.3448,1.15937) translate(-272.536,-59.9351)" width="30" x="1062.95" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="436" zvalue="46"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418159620" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1381">
    <use class="kv10" height="50" transform="rotate(0,1083.12,464.984) scale(1.3448,1.15937) translate(-272.536,-59.9351)" width="30" x="1062.95" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="436" zvalue="46"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418225156" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399441252356" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399441252356"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1083.12,464.984) scale(1.3448,1.15937) translate(-272.536,-59.9351)" width="30" x="1062.95" y="436"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="115">
   <use class="kv110" height="18" transform="rotate(0,756.974,465.5) scale(-3.33333,2.77778) translate(-966.567,-281.92)" width="15" x="731.9744149131072" xlink:href="#Accessory:PT8_0" y="440.5" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500807174" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,756.974,465.5) scale(-3.33333,2.77778) translate(-966.567,-281.92)" width="15" x="731.9744149131072" y="440.5"/></g>
  <g id="203">
   <use class="kv110" height="26" transform="rotate(90,1006.04,138.026) scale(-1.08054,1.08054) translate(-1936.6,-9.24121)" width="12" x="999.5535673143577" xlink:href="#Accessory:避雷器1_0" y="123.9786682615629" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499824134" ObjectName="避雷器5"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1006.04,138.026) scale(-1.08054,1.08054) translate(-1936.6,-9.24121)" width="12" x="999.5535673143577" y="123.9786682615629"/></g>
  <g id="175">
   <use class="kv110" height="30" transform="rotate(90,989.778,109.389) scale(-1.66667,1.66667) translate(-1573.64,-33.7556)" width="30" x="964.7777777777778" xlink:href="#Accessory:三卷PT带容断器_0" y="84.38888888888891" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499758598" ObjectName="110kV香柏河一二级线线路PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,989.778,109.389) scale(-1.66667,1.66667) translate(-1573.64,-33.7556)" width="30" x="964.7777777777778" y="84.38888888888891"/></g>
  <g id="180">
   <use class="kv10" height="18" transform="rotate(0,1430.6,490.139) scale(-3.33333,-2.77778) translate(-1842.28,-650.589)" width="15" x="1405.603789714917" xlink:href="#Accessory:PT8_0" y="465.1388888888889" zvalue="134"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499561990" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1430.6,490.139) scale(-3.33333,-2.77778) translate(-1842.28,-650.589)" width="15" x="1405.603789714917" y="465.1388888888889"/></g>
  <g id="63">
   <use class="kv10" height="20" transform="rotate(0,505.75,936.667) scale(1.76667,1.76667) translate(-213.726,-398.811)" width="15" x="492.5" xlink:href="#Accessory:PT6_0" y="919" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499496454" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,505.75,936.667) scale(1.76667,1.76667) translate(-213.726,-398.811)" width="15" x="492.5" y="919"/></g>
  <g id="62">
   <use class="kv10" height="30" transform="rotate(0,554.5,933.5) scale(1.3,1.3) translate(-123.462,-210.923)" width="30" x="535" xlink:href="#Accessory:PT789_0" y="914" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499430918" ObjectName="#1发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,554.5,933.5) scale(1.3,1.3) translate(-123.462,-210.923)" width="30" x="535" y="914"/></g>
  <g id="71">
   <use class="kv10" height="30" transform="rotate(0,716.25,925.45) scale(1.17143,-1.91333) translate(-101.817,-1395.43)" width="35" x="695.75" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="896.75" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449499365382" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,716.25,925.45) scale(1.17143,-1.91333) translate(-101.817,-1395.43)" width="35" x="695.75" y="896.75"/></g>
  <g id="230">
   <use class="kv10" height="20" transform="rotate(0,961.75,936.667) scale(1.76667,1.76667) translate(-411.613,-398.811)" width="15" x="948.5" xlink:href="#Accessory:PT6_0" y="919" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449502511110" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,961.75,936.667) scale(1.76667,1.76667) translate(-411.613,-398.811)" width="15" x="948.5" y="919"/></g>
  <g id="229">
   <use class="kv10" height="30" transform="rotate(0,1010.5,933.5) scale(1.3,1.3) translate(-228.692,-210.923)" width="30" x="991" xlink:href="#Accessory:PT789_0" y="914" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449502445574" ObjectName="#2发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1010.5,933.5) scale(1.3,1.3) translate(-228.692,-210.923)" width="30" x="991" y="914"/></g>
  <g id="227">
   <use class="kv10" height="30" transform="rotate(0,1172.25,925.45) scale(1.17143,-1.91333) translate(-168.549,-1395.43)" width="35" x="1151.75" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="896.75" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449502380038" ObjectName="#2发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1172.25,925.45) scale(1.17143,-1.91333) translate(-168.549,-1395.43)" width="35" x="1151.75" y="896.75"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,624.047,906.136) scale(1.85899,1.85899) translate(-275.471,-405.817)" width="30" x="596.1617897505679" xlink:href="#Generator:发电机_0" y="878.2510218856319" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500741638" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449500741638"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,624.047,906.136) scale(1.85899,1.85899) translate(-275.471,-405.817)" width="30" x="596.1617897505679" y="878.2510218856319"/></g>
  <g id="231">
   <use class="kv10" height="30" transform="rotate(0,1080.05,906.136) scale(1.85899,1.85899) translate(-486.176,-405.817)" width="30" x="1052.161789750568" xlink:href="#Generator:发电机_0" y="878.2510218856319" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449502576646" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449502576646"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1080.05,906.136) scale(1.85899,1.85899) translate(-486.176,-405.817)" width="30" x="1052.161789750568" y="878.2510218856319"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,1361.76,859.25) scale(1.69643,1.70833) translate(-549.288,-345.649)" width="28" x="1338.0089697013" xlink:href="#EnergyConsumer:站用变DY接地_0" y="833.625" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500676102" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1361.76,859.25) scale(1.69643,1.70833) translate(-549.288,-345.649)" width="28" x="1338.0089697013" y="833.625"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1691.75,538.5) scale(1.69643,1.70833) translate(-684.758,-212.655)" width="28" x="1668" xlink:href="#EnergyConsumer:站用变DY接地_0" y="512.875" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449500545030" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1691.75,538.5) scale(1.69643,1.70833) translate(-684.758,-212.655)" width="28" x="1668" y="512.875"/></g>
  <g id="232">
   <use class="kv10" height="30" transform="rotate(0,1690.93,354.528) scale(3.7037,1.48148) translate(-1218.16,-107.999)" width="12" x="1668.70981733304" xlink:href="#EnergyConsumer:负荷_0" y="332.3055553436279" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449502642182" ObjectName="#2站用变线"/>
   <cge:TPSR_Ref TObjectID="6192449502642182"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1690.93,354.528) scale(3.7037,1.48148) translate(-1218.16,-107.999)" width="12" x="1668.70981733304" y="332.3055553436279"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="214">
   <use class="kv110" height="30" transform="rotate(0,1082.18,81.2778) scale(6.34921,1.48148) translate(-893.016,-19.1931)" width="7" x="1059.95981733304" xlink:href="#ACLineSegment:线路_0" y="59.05555534362799" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249301319685" ObjectName="110kV香柏河一二级线"/>
   <cge:TPSR_Ref TObjectID="8444249301319685_5066549580922882"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1082.18,81.2778) scale(6.34921,1.48148) translate(-893.016,-19.1931)" width="7" x="1059.95981733304" y="59.05555534362799"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123784949764" ObjectName="F"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123788750852" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123788816388" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,335.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="340.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123781869572" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123788619780" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123788685316" ObjectName="F"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124567973892" ObjectName="F"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124568039428" ObjectName="F"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,402.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="407.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124567908356" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124613062660" ObjectName="装机率用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124602576900" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="105" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1183.43,52.4056) scale(1,1) translate(0,5.01389e-14)" writing-mode="lr" x="1182.96" xml:space="preserve" y="57.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123778068484" ObjectName="P"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="106" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1183.43,76.4028) scale(1,1) translate(0,7.94454e-14)" writing-mode="lr" x="1182.96" xml:space="preserve" y="81.08" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123778134020" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="107" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1183.43,100.4) scale(1,1) translate(0,1.08752e-13)" writing-mode="lr" x="1182.96" xml:space="preserve" y="105.08" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123778199556" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="133" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1000.62,364.594) scale(1,1) translate(0,3.90521e-14)" writing-mode="lr" x="1000.07" xml:space="preserve" y="369.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123782000644" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="137" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1000.62,391.75) scale(1,1) translate(0,4.2067e-14)" writing-mode="lr" x="1000.07" xml:space="preserve" y="396.45" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123782066183" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="141" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,998.121,534.787) scale(1,1) translate(0,5.79779e-14)" writing-mode="lr" x="997.5700000000001" xml:space="preserve" y="539.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123782131719" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="148" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,998.121,561.359) scale(1,1) translate(0,6.09281e-14)" writing-mode="lr" x="997.5700000000001" xml:space="preserve" y="566.0599999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123782197254" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="150" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1000.62,418.906) scale(1,1) translate(0,4.50819e-14)" writing-mode="lr" x="1000.07" xml:space="preserve" y="423.61" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123782262788" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="162" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,998.121,587.932) scale(1,1) translate(0,6.38782e-14)" writing-mode="lr" x="997.5700000000001" xml:space="preserve" y="592.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123782590468" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="164" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,619.047,980.911) scale(1,1) translate(0,-2.15332e-13)" writing-mode="lr" x="618.49" xml:space="preserve" y="985.59" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123780820996" ObjectName="P"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="166" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1076.3,985.503) scale(1,1) translate(0,-2.16352e-13)" writing-mode="lr" x="1075.74" xml:space="preserve" y="990.1799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123786653700" ObjectName="P"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="173" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,619.047,1004.46) scale(1,1) translate(0,-2.20562e-13)" writing-mode="lr" x="618.49" xml:space="preserve" y="1009.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123780886532" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="174">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="174" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1076.3,1009.06) scale(1,1) translate(0,-2.21582e-13)" writing-mode="lr" x="1075.74" xml:space="preserve" y="1013.73" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123786719236" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="176">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="176" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,619.047,1028.02) scale(1,1) translate(0,-2.25792e-13)" writing-mode="lr" x="618.49" xml:space="preserve" y="1032.69" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123780952068" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1076.3,1032.61) scale(1,1) translate(0,-2.26812e-13)" writing-mode="lr" x="1075.74" xml:space="preserve" y="1037.29" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123786784772" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="70" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1433.11,369.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.64" xml:space="preserve" y="374.03" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123781476356" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,786,539.5) scale(1,1) translate(0,0)" writing-mode="lr" x="785.53" xml:space="preserve" y="544.28" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123784556548" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="101" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1433.11,394.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.64" xml:space="preserve" y="399.03" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123781541892" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="102" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,786,564.5) scale(1,1) translate(0,0)" writing-mode="lr" x="785.53" xml:space="preserve" y="569.28" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123784622084" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="103" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1433.11,419.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.64" xml:space="preserve" y="424.03" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123781607428" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,786,589.5) scale(1,1) translate(0,0)" writing-mode="lr" x="785.53" xml:space="preserve" y="594.28" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123784687620" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="108" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,504.111,671.75) scale(1,1) translate(0,0)" writing-mode="lr" x="503.64" xml:space="preserve" y="676.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123781738500" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="109" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,559,311) scale(1,1) translate(0,0)" writing-mode="lr" x="558.53" xml:space="preserve" y="315.78" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123784818692" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="54">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="290"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374883749892" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="291"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950261178376" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>