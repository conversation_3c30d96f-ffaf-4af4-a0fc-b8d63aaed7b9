<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549582823426" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Accessory:勐养主变PT_0" viewBox="0,0,40,28">
   <use terminal-index="0" type="0" x="39.77572331551165" xlink:href="#terminal" y="15.43279152351341"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.66666666666667" x2="20.66666666666667" y1="13.02740325661302" y2="9.027403256613018"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="20" y1="2.500000000000004" y2="6.916666666666669"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.66666666666667" x2="18.66666666666667" y1="9.027403256613018" y2="10.02740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.66666666666667" x2="18.66666666666667" y1="13.02740325661302" y2="12.02740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.5" x2="14.5" y1="15.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="2.5" y2="27.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.75" x2="26.75" y1="15.5" y2="27.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="26.66666666666667" y1="27.5" y2="27.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25" x2="20.25" y1="18.75" y2="27.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="20" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="40" y1="15.5" y2="15.5"/>
   <ellipse cx="20.31" cy="18.74" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.545809518335158" x2="3.545809518335158" y1="0.3812038107576825" y2="4.658019655441528"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.295809518335165" x2="2.295809518335165" y1="1.214537144091015" y2="3.824686322108196"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.045809518335165" x2="1.045809518335165" y1="1.797870477424349" y2="3.24135298877486"/>
   <ellipse cx="26.73" cy="15.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.14" cy="15.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.45074469211364" x2="17.14660664936333" y1="15.31980081353841" y2="15.47418190151245"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44403897429873" x2="14.45074469211363" y1="13.05870275082647" y2="15.31980081353839"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.45074469211364" x2="12.76158845267886" y1="15.31980081353841" y2="17.42651778827629"/>
   <ellipse cx="20.14" cy="11.08" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.20074469211364" x2="18.51158845267886" y1="18.81980081353841" y2="20.92651778827629"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.70074469211364" x2="29.39660664936333" y1="15.56980081353841" y2="15.72418190151245"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.19403897429873" x2="20.20074469211363" y1="16.55870275082647" y2="18.81980081353839"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.70074469211364" x2="25.01158845267886" y1="15.56980081353841" y2="17.67651778827629"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.69403897429873" x2="26.70074469211363" y1="13.30870275082647" y2="15.56980081353839"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.20074469211364" x2="22.89660664936333" y1="18.81980081353841" y2="18.97418190151245"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.25" y2="25.75"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_0" viewBox="0,0,30,50">
   <ellipse cx="14.99" cy="14.99" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.99138374485597" x2="10.08333333333333" y1="11.06149193548387" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01984739368999" x2="20" y1="11.06354954865259" y2="16"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="6.416666666666664" y2="11.06149193548386"/>
   <use terminal-index="0" type="1" x="14.99138374485597" xlink:href="#terminal" y="0.2276829173857209"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_1" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="32" y2="37.04670698924731"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00546553497943" x2="20" y1="37.04651063100137" y2="41.75"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="10" y1="37.04670698924731" y2="41.91666666666666"/>
   <ellipse cx="15.01" cy="35.25" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.01333161865569" xlink:href="#terminal" y="50.00705645161292"/>
  </symbol>
  <symbol id="Coil:光伏接地变中性点_0" viewBox="0,0,25,40">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="8.25" y1="4" y2="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.33333333333333" x2="21.5" y1="27.33333333333333" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.25" x2="8.25" y1="4" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.916666666666664" x2="2.916666666666664" y1="9.83333333333333" y2="9.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.916666666666664" x2="2.916666666666664" y1="19.16666666666667" y2="19.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="16" y1="12.25" y2="0.75"/>
   <path d="M 15.7917 22.425 A 2.54583 4.29167 270 0 0 15.7917 17.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16" x2="16" y1="27.75000000000001" y2="34.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.51666666666667" x2="18.2" y1="36.66302242688344" y2="36.66302242688344"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.63333333333333" x2="17.06666666666667" y1="38.5686533568213" y2="38.5686533568213"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.4" x2="20.08333333333334" y1="34.67405816361222" y2="34.67405816361222"/>
   <path d="M 16.0833 17.2292 A 2.53125 3.83333 270 0 0 16.0833 12.1667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.75 27.7583 A 2.5875 4.5 270 0 0 15.75 22.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 3.91667 16.0083 A 1.62917 2 270 0 0 3.91667 12.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.25" x2="8.25" y1="19.08333333333334" y2="25.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.766666666666666" x2="10.45" y1="27.99635576021678" y2="27.99635576021678"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.883333333333332" x2="9.316666666666666" y1="29.90198669015464" y2="29.90198669015464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.4" x2="12.08333333333333" y1="26.00739149694556" y2="26.00739149694556"/>
   <path d="M 4 12.7292 A 1.44792 1.91667 270 0 0 4 9.83333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 3.91667 19.175 A 1.5875 2 270 0 0 3.91667 16" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 8.41667 16.0917 A 1.62917 2 270 0 1 8.41667 12.8333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 8.33333 12.8125 A 1.44792 1.91667 270 0 1 8.33333 9.91667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 8.41667 19.2583 A 1.5875 2 270 0 1 8.41667 16.0833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":光伏发电2_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="29.91666666666667" y2="1.116666666666665"/>
   <path d="M 4.91667 14.5224 L 3.5 7.60569 L 5 11.1057 L 6.5 7.43903 L 4.95238 14.739" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:SVG2024_0" viewBox="0,0,28,26">
   <use terminal-index="0" type="0" x="14" xlink:href="#terminal" y="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.383333333333331" x2="9.383333333333331" y1="9.416666666666668" y2="12.41666666666667"/>
   <path d="M 3.8 11 L 0.8 11 L 0.8 21 L 12.8 21" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.38333333333333" x2="10.38333333333333" y1="8.416666666666666" y2="13.25"/>
   <path d="M 10.4667 9.41667 L 11.4667 8.41667 L 11.4667 6.41667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.3" x2="18.3" y1="7.25" y2="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.3" x2="18.3" y1="7.25" y2="7.25"/>
   <path d="M 18.3 9.25 L 17.3 12.25 L 19.3 12.25 L 18.3 9.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="13.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.97,10.88) scale(1,1) translate(0,0)" width="20" x="3.97" y="4.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.88333333333333" x2="12.88333333333333" y1="19.5" y2="22.58333333333333"/>
   <path d="M 10.3 12 L 11.3 13 L 11.3 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.3" x2="11.3" y1="14.25" y2="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.300000000000001" x2="7.3" y1="11" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.3" x2="19.3" y1="9.25" y2="9.25"/>
   <path d="M 14.8833 21 L 26.8 21 L 26.8 11 L 23.8833 11" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96666666666667" x2="14.96666666666667" y1="19.5" y2="22.58333333333333"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV环山光伏电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="48" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176,73) scale(1,1) translate(0,0)" writing-mode="lr" x="176" xml:space="preserve" y="76.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.5,72.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="176.5" xml:space="preserve" y="81.69" zvalue="55">35kV环山光伏电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="25" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,69.625,305.75) scale(1,1) translate(0,0)" width="72.88" x="33.19" y="293.75" zvalue="1867"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.625,305.75) scale(1,1) translate(0,0)" writing-mode="lr" x="69.63" xml:space="preserve" y="310.25" zvalue="1867">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.508,444.032) scale(1,1) translate(0,0)" writing-mode="lr" x="593.51" xml:space="preserve" y="448.53" zvalue="7">35kV母线</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="233.05" xml:space="preserve" y="960" zvalue="1080">Huanshan-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="143.05" xml:space="preserve" y="992" zvalue="1081">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="992" zvalue="1082">20201106</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1621.39,399.286) scale(1,1) translate(-3.57492e-13,0)" writing-mode="lr" x="1621.39" xml:space="preserve" y="403.79" zvalue="1715">311</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1654.57,123.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1654.57" xml:space="preserve" y="127.64" zvalue="1716">35kV弄环线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1636.86,237.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1636.86" xml:space="preserve" y="242.36" zvalue="1721">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1698.71,182.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1698.71" xml:space="preserve" y="186.64" zvalue="1724">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1563.83,592.794) scale(1,1) translate(0,0)" writing-mode="lr" x="1563.83" xml:space="preserve" y="597.29" zvalue="1728">35kV计量柜</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1207.35,715) scale(1,1) translate(-2.53652e-13,0)" writing-mode="lr" x="1207.35" xml:space="preserve" y="719.5" zvalue="1730">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1172.57,506.429) scale(1,1) translate(0,0)" writing-mode="lr" x="1172.57" xml:space="preserve" y="510.93" zvalue="1733">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,600.286,821.857) scale(1,1) translate(0,0)" writing-mode="lr" x="600.29" xml:space="preserve" y="826.36" zvalue="1742">35kV SVG</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,571.143,526.429) scale(1,1) translate(0,0)" writing-mode="lr" x="571.14" xml:space="preserve" y="530.9299999999999" zvalue="1745">315</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.143,646.714) scale(1,1) translate(0,0)" writing-mode="lr" x="586.14" xml:space="preserve" y="651.21" zvalue="1749">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,530.571,684.857) scale(1,1) translate(0,0)" writing-mode="lr" x="530.5700000000001" xml:space="preserve" y="689.36" zvalue="1756">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,698.117,527.857) scale(1,1) translate(0,0)" writing-mode="lr" x="698.12" xml:space="preserve" y="532.36" zvalue="1761">314</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,685.857,603.286) scale(1,1) translate(0,0)" writing-mode="lr" x="685.86" xml:space="preserve" y="607.79" zvalue="1766">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,729.402,819.857) scale(1,1) translate(0,0)" writing-mode="lr" x="729.4" xml:space="preserve" y="824.36" zvalue="1770">35kV光伏Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.26,525) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.26" xml:space="preserve" y="529.5" zvalue="1773">312</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1028,600.429) scale(1,1) translate(0,0)" writing-mode="lr" x="1028" xml:space="preserve" y="604.9299999999999" zvalue="1778">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1071.55,821) scale(1,1) translate(0,0)" writing-mode="lr" x="1071.55" xml:space="preserve" y="825.5" zvalue="1782">35kV光伏Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,829.26,526.429) scale(1,1) translate(4.5367e-13,0)" writing-mode="lr" x="829.26" xml:space="preserve" y="530.9299999999999" zvalue="1786">313</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,817,601.857) scale(1,1) translate(0,0)" writing-mode="lr" x="817" xml:space="preserve" y="606.36" zvalue="1790">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,861.648,819.237) scale(1,1) translate(1.83109e-13,0)" writing-mode="lr" x="861.65" xml:space="preserve" y="823.74" zvalue="1795">35kV接地变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.698,656.714) scale(1,1) translate(0,0)" writing-mode="lr" x="956.7" xml:space="preserve" y="661.21" zvalue="1810">3130</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,600.841,837.19) scale(1,1) translate(0,0)" writing-mode="lr" x="600.84" xml:space="preserve" y="841.6900000000001" zvalue="1813">±3Mvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,536.143,607.714) scale(1,1) translate(0,0)" writing-mode="lr" x="536.14" xml:space="preserve" y="612.21" zvalue="1827">60</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="150" y2="150"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="2.928571428571331" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="150" y2="150"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.9285714285713" x2="364.9285714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="2.928571428571331" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.9285714285713" x2="364.9285714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="2.928571428571331" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.9285714285713" x2="364.9285714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="2.928571428571331" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.9285714285713" x2="364.9285714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="183.9285714285713" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.928571428571331" x2="2.928571428571331" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="364.9285714285713" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.9285714285713" x2="183.9285714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.9285714285713" x2="364.9285714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="432.1666435058594" y2="432.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="469.6566435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="432.1666435058594" y2="432.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="469.6566435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="432.1666435058594" y2="432.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="469.6566435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="432.1666435058594" y2="432.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="469.6566435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="432.1666435058594" y2="432.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="469.6566435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="432.1666435058594" y2="469.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="469.6567435058594" y2="469.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="469.6567435058594" y2="469.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="469.6567435058594" y2="469.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="469.6567435058594" y2="469.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="469.6567435058594" y2="469.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="469.6567435058594" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="517.9939435058594" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="517.9939435058594" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="517.9939435058594" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="517.9939435058594" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="493.8253435058595" y2="493.8253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="517.9939435058594" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="493.8253435058595" y2="517.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="517.9939835058594" y2="517.9939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="542.1625835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="517.9939835058594" y2="517.9939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="542.1625835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="517.9939835058594" y2="517.9939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="542.1625835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="517.9939835058594" y2="517.9939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="542.1625835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="517.9939835058594" y2="517.9939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="542.1625835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="517.9939835058594" y2="542.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="542.1627435058595" y2="542.1627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="542.1627435058595" y2="542.1627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="542.1627435058595" y2="542.1627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="542.1627435058595" y2="542.1627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="542.1627435058595" y2="542.1627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="542.1627435058595" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="590.4999435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="566.3313435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="566.3313435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="590.4999435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="566.3313435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="566.3313435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="590.4999435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="566.3313435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="566.3313435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="590.4999435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="566.3313435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="566.3313435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="566.3313435058594" y2="566.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="590.4999435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="566.3313435058594" y2="590.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="566.3313435058594" y2="590.4999435058594"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,157.827,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="157.83" xml:space="preserve" y="308.34" zvalue="1856">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,262.827,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="262.83" xml:space="preserve" y="308.34" zvalue="1857">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,174.595,451.75) scale(1,1) translate(0,0)" writing-mode="lr" x="174.5952380952378" xml:space="preserve" y="456.2500000000001" zvalue="1858">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,479.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="484.0000000000001" zvalue="1859">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,505) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="509.5" zvalue="1860">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,530.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="535" zvalue="1861">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.4286,555) scale(1,1) translate(0,0)" writing-mode="lr" x="55.42857142857133" xml:space="preserve" y="559.5" zvalue="1862">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,581.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="586" zvalue="1863">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,38.4286,163) scale(1,1) translate(0,0)" writing-mode="lr" x="38.43" xml:space="preserve" y="168.5" zvalue="1864">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.429,163) scale(1,1) translate(0,0)" writing-mode="lr" x="218.43" xml:space="preserve" y="168.5" zvalue="1865">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.5536,188.5) scale(1,1) translate(0,0)" writing-mode="lr" x="55.55" xml:space="preserve" y="193" zvalue="1866">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1330,820.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1330" xml:space="preserve" y="825" zvalue="1886">容量：13.54MW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="33.19" y="293.75" zvalue="1867"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv35" d="M 547.43 470.81 L 1380.29 470.81" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674236497924" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674236497924"/></metadata>
  <path d="M 547.43 470.81 L 1380.29 470.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv35" d="M 1456 470.81 L 1726 470.81" stroke-width="4" zvalue="1681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674236563461" ObjectName="35kV计量柜母线"/>
   <cge:TPSR_Ref TObjectID="9288674236563461"/></metadata>
  <path d="M 1456 470.81 L 1726 470.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="12">
   <use class="kv35" height="20" transform="rotate(0,1654.57,400.286) scale(3.14286,3.14286) translate(-1117.4,-251.494)" width="10" x="1638.857142857143" xlink:href="#Breaker:小车断路器_0" y="368.8571428571429" zvalue="1714"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924484595717" ObjectName="35kV弄环线311断路器"/>
   <cge:TPSR_Ref TObjectID="6473924484595717"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1654.57,400.286) scale(3.14286,3.14286) translate(-1117.4,-251.494)" width="10" x="1638.857142857143" y="368.8571428571429"/></g>
  <g id="119">
   <use class="kv35" height="20" transform="rotate(0,603.857,527.429) scale(3.14286,3.14286) translate(-401.006,-338.182)" width="10" x="588.1428571428573" xlink:href="#Breaker:小车断路器_0" y="496" zvalue="1744"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924484661253" ObjectName="35kVSVG315断路器"/>
   <cge:TPSR_Ref TObjectID="6473924484661253"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,603.857,527.429) scale(3.14286,3.14286) translate(-401.006,-338.182)" width="10" x="588.1428571428573" y="496"/></g>
  <g id="146">
   <use class="kv35" height="20" transform="rotate(0,730.831,528.857) scale(3.14286,3.14286) translate(-487.58,-339.156)" width="10" x="715.1166651089873" xlink:href="#Breaker:小车断路器_0" y="497.4285714285714" zvalue="1760"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924484726789" ObjectName="35kV光伏Ⅱ回线314断路器"/>
   <cge:TPSR_Ref TObjectID="6473924484726789"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,730.831,528.857) scale(3.14286,3.14286) translate(-487.58,-339.156)" width="10" x="715.1166651089873" y="497.4285714285714"/></g>
  <g id="164">
   <use class="kv35" height="20" transform="rotate(0,1072.97,526) scale(3.14286,3.14286) translate(-720.859,-337.208)" width="10" x="1057.259522251844" xlink:href="#Breaker:小车断路器_0" y="494.5714285714286" zvalue="1772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924484792325" ObjectName="35kV光伏Ⅰ回线312断路器"/>
   <cge:TPSR_Ref TObjectID="6473924484792325"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1072.97,526) scale(3.14286,3.14286) translate(-720.859,-337.208)" width="10" x="1057.259522251844" y="494.5714285714286"/></g>
  <g id="183">
   <use class="kv35" height="20" transform="rotate(0,861.974,527.429) scale(3.14286,3.14286) translate(-576.995,-338.182)" width="10" x="846.2595222518445" xlink:href="#Breaker:小车断路器_0" y="496" zvalue="1785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924484857861" ObjectName="35kV接地变313断路器"/>
   <cge:TPSR_Ref TObjectID="6473924484857861"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,861.974,527.429) scale(3.14286,3.14286) translate(-576.995,-338.182)" width="10" x="846.2595222518445" y="496"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="18">
   <use class="kv35" height="30" transform="rotate(0,1654.57,151.429) scale(2.65306,0.714286) translate(-1025.14,56.2857)" width="7" x="1645.285714285714" xlink:href="#ACLineSegment:线路_0" y="140.7142857142857" zvalue="1715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249305513990" ObjectName="35kV弄环线"/>
   <cge:TPSR_Ref TObjectID="8444249305513990_5066549582823426"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1654.57,151.429) scale(2.65306,0.714286) translate(-1025.14,56.2857)" width="7" x="1645.285714285714" y="140.7142857142857"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="39">
   <path class="kv35" d="M 1654.57 428.57 L 1654.57 470.81" stroke-width="1" zvalue="1717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@1" LinkObjectIDznd="315@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1654.57 428.57 L 1654.57 470.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1654.57 162.04 L 1654.57 223.66" stroke-width="1" zvalue="1721"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1654.57 162.04 L 1654.57 223.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 1654.66 254.3 L 1654.57 371.21" stroke-width="1" zvalue="1722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1654.66 254.3 L 1654.57 371.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv35" d="M 1666.36 197.36 L 1654.57 197.36" stroke-width="1" zvalue="1724"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 1666.36 197.36 L 1654.57 197.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 1681.71 311.83 L 1681.71 326 L 1654.61 326" stroke-width="1" zvalue="1725"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 1681.71 311.83 L 1681.71 326 L 1654.61 326" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv35" d="M 1307.43 470.81 L 1307.43 570.29 L 1530.29 570.29 L 1530.29 470.81" stroke-width="1" zvalue="1726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@0" LinkObjectIDznd="315@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.43 470.81 L 1307.43 570.29 L 1530.29 570.29 L 1530.29 470.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1562.71 518.57 L 1562.71 507.43 L 1530.29 507.43" stroke-width="1" zvalue="1728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="99" MaxPinNum="2"/>
   </metadata>
  <path d="M 1562.71 518.57 L 1562.71 507.43 L 1530.29 507.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv35" d="M 1203.26 622.14 L 1203.26 525.95" stroke-width="1" zvalue="1733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1203.26 622.14 L 1203.26 525.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 1203.15 488.96 L 1203.15 470.81" stroke-width="1" zvalue="1734"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@1" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1203.15 488.96 L 1203.15 470.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 1172.79 537.75 L 1172.79 531.71 L 1203.26 531.71" stroke-width="1" zvalue="1740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 1172.79 537.75 L 1172.79 531.71 L 1203.26 531.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv35" d="M 603.86 498.36 L 603.86 470.81" stroke-width="1" zvalue="1746"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 603.86 498.36 L 603.86 470.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv35" d="M 604.57 772.57 L 604.57 663.16" stroke-width="1" zvalue="1749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="123@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.57 772.57 L 604.57 663.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv35" d="M 603.98 632.52 L 603.98 555.71" stroke-width="1" zvalue="1750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 603.98 632.52 L 603.98 555.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 625.32 728.62 L 625.32 712 L 604.57 712" stroke-width="1" zvalue="1758"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 625.32 728.62 L 625.32 712 L 604.57 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv35" d="M 730.83 499.79 L 730.83 470.81" stroke-width="1" zvalue="1762"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 730.83 499.79 L 730.83 470.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv35" d="M 730.83 557.14 L 730.83 757.5" stroke-width="1" zvalue="1770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@1" LinkObjectIDznd="148@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 730.83 557.14 L 730.83 757.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 1072.97 496.93 L 1072.97 470.81" stroke-width="1" zvalue="1774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.97 496.93 L 1072.97 470.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv35" d="M 1072.97 758.64 L 1072.97 554.29" stroke-width="1" zvalue="1781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@0" LinkObjectIDznd="164@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.97 758.64 L 1072.97 554.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv35" d="M 861.97 498.36 L 861.97 470.81" stroke-width="1" zvalue="1787"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="6@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 861.97 498.36 L 861.97 470.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv35" d="M 861.97 704.9 L 861.97 555.71" stroke-width="1" zvalue="1796"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="183@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 861.97 704.9 L 861.97 555.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="v400" d="M 861.94 759.29 L 861.99 749.12" stroke-width="1" zvalue="1799"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="191@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 861.94 759.29 L 861.99 749.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv35" d="M 900.91 699.21 L 900.91 714.52" stroke-width="1" zvalue="1806"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="228" MaxPinNum="2"/>
   </metadata>
  <path d="M 900.91 699.21 L 900.91 714.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv35" d="M 861.98 714.52 L 931.17 714.52 L 931.17 674.83" stroke-width="1" zvalue="1810"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@2" LinkObjectIDznd="227@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 861.98 714.52 L 931.17 714.52 L 931.17 674.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv35" d="M 931.2 644.19 L 931.2 625.01" stroke-width="1" zvalue="1811"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.2 644.19 L 931.2 625.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv35" d="M 1049.64 590.36 L 1049.64 575 L 1072.97 575" stroke-width="1" zvalue="1816"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.64 590.36 L 1049.64 575 L 1072.97 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 1097.46 588.33 L 1097.46 574 L 1072.97 574" stroke-width="1" zvalue="1817"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.46 588.33 L 1097.46 574 L 1072.97 574" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv35" d="M 626.04 589.76 L 626.04 571 L 603.98 571" stroke-width="1" zvalue="1819"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.04 589.76 L 626.04 571 L 603.98 571" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 707.5 593.21 L 707.5 575 L 730.83 575" stroke-width="1" zvalue="1820"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.5 593.21 L 707.5 575 L 730.83 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 755.32 591.19 L 755.32 573 L 730.83 573" stroke-width="1" zvalue="1821"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.32 591.19 L 755.32 573 L 730.83 573" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 838.64 591.79 L 838.64 571 L 861.97 571" stroke-width="1" zvalue="1822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="193" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.64 591.79 L 838.64 571 L 861.97 571" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 886.46 589.76 L 886.46 570 L 861.97 570" stroke-width="1" zvalue="1823"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="193" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.46 589.76 L 886.46 570 L 861.97 570" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 566.07 688.64 L 604.57 688.64" stroke-width="1" zvalue="1824"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 566.07 688.64 L 604.57 688.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 564.07 595.79 L 564.07 575 L 603.98 575" stroke-width="1" zvalue="1827"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 564.07 595.79 L 564.07 575 L 603.98 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv35" d="M 1204.24 544.06 L 1203.26 544.06" stroke-width="1" zvalue="1873"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204.24 544.06 L 1203.26 544.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="40">
   <use class="kv35" height="40" transform="rotate(0,1681.71,284.571) scale(1.42857,-1.42857) translate(-495.943,-475.2)" width="40" x="1653.142857142857" xlink:href="#Accessory:线路PT11带避雷器_0" y="256.0000000000001" zvalue="1718"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449643675654" ObjectName="弄环线"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1681.71,284.571) scale(1.42857,-1.42857) translate(-495.943,-475.2)" width="40" x="1653.142857142857" y="256.0000000000001"/></g>
  <g id="100">
   <use class="kv35" height="40" transform="rotate(0,1562.71,545) scale(1.42857,-1.42857) translate(-462.386,-917.929)" width="30" x="1541.285714285714" xlink:href="#Accessory:带熔断器的线路PT1_0" y="516.4285714285714" zvalue="1727"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449643937798" ObjectName="35kV计量柜"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1562.71,545) scale(1.42857,-1.42857) translate(-462.386,-917.929)" width="30" x="1541.285714285714" y="516.4285714285714"/></g>
  <g id="102">
   <use class="kv35" height="28" transform="rotate(270,1200.52,660.044) scale(1.91649,1.91649) translate(-555.773,-302.811)" width="40" x="1162.187182787408" xlink:href="#Accessory:勐养主变PT_0" y="633.213236845654" zvalue="1729"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644003334" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="28" opacity="0" stroke="white" transform="rotate(270,1200.52,660.044) scale(1.91649,1.91649) translate(-555.773,-302.811)" width="40" x="1162.187182787408" y="633.213236845654"/></g>
  <g id="111">
   <use class="kv35" height="18" transform="rotate(0,1204.21,559.143) scale(1.78571,1.90476) translate(-525.926,-257.45)" width="10" x="1195.285714285714" xlink:href="#Accessory:熔断器_0" y="542" zvalue="1736"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644134406" ObjectName="35kV母线熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1204.21,559.143) scale(1.78571,1.90476) translate(-525.926,-257.45)" width="10" x="1195.285714285714" y="542"/></g>
  <g id="114">
   <use class="kv35" height="20" transform="rotate(0,1172.79,561.5) scale(3.39286,3.39286) translate(-803.194,-372.077)" width="20" x="1138.857142857143" xlink:href="#Accessory:线路PT3_0" y="527.5714285714286" zvalue="1739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644199942" ObjectName="35kV母线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1172.79,561.5) scale(3.39286,3.39286) translate(-803.194,-372.077)" width="20" x="1138.857142857143" y="527.5714285714286"/></g>
  <g id="127">
   <use class="kv35" height="26" transform="rotate(0,625.286,746.286) scale(1.07143,1.42857) translate(-41.2571,-218.314)" width="12" x="618.8571428571428" xlink:href="#Accessory:避雷器1_0" y="727.7142857142858" zvalue="1751"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644396550" ObjectName="SVG2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,625.286,746.286) scale(1.07143,1.42857) translate(-41.2571,-218.314)" width="12" x="618.8571428571428" y="727.7142857142858"/></g>
  <g id="128">
   <use class="kv35" height="26" transform="rotate(0,626,607.429) scale(1.07143,1.42857) translate(-41.3048,-176.657)" width="12" x="619.5714285714284" xlink:href="#Accessory:避雷器1_0" y="588.8571428571429" zvalue="1753"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644462086" ObjectName="SVG1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,626,607.429) scale(1.07143,1.42857) translate(-41.3048,-176.657)" width="12" x="619.5714285714284" y="588.8571428571429"/></g>
  <g id="140">
   <use class="kv35" height="26" transform="rotate(0,755.286,608.857) scale(1.07143,1.42857) translate(-49.9238,-177.086)" width="12" x="748.8571428571428" xlink:href="#Accessory:避雷器1_0" y="590.2857142857143" zvalue="1764"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644789766" ObjectName="光伏Ⅱ回线1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,755.286,608.857) scale(1.07143,1.42857) translate(-49.9238,-177.086)" width="12" x="748.8571428571428" y="590.2857142857143"/></g>
  <g id="160">
   <use class="kv35" height="26" transform="rotate(0,1097.43,606) scale(1.07143,1.42857) translate(-72.7333,-176.229)" width="12" x="1091" xlink:href="#Accessory:避雷器1_0" y="587.4285714285714" zvalue="1776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645117446" ObjectName="光伏Ⅰ回线1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1097.43,606) scale(1.07143,1.42857) translate(-72.7333,-176.229)" width="12" x="1091" y="587.4285714285714"/></g>
  <g id="172">
   <use class="kv35" height="26" transform="rotate(0,886.429,607.429) scale(1.07143,1.42857) translate(-58.6667,-176.657)" width="12" x="880" xlink:href="#Accessory:避雷器1_0" y="588.8571428571429" zvalue="1788"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645314054" ObjectName="接地变1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,886.429,607.429) scale(1.07143,1.42857) translate(-58.6667,-176.657)" width="12" x="880" y="588.8571428571429"/></g>
  <g id="224">
   <use class="kv35" height="26" transform="rotate(0,900.873,681.54) scale(1.07143,-1.42857) translate(-59.6296,-1153.05)" width="12" x="894.4444444444445" xlink:href="#Accessory:避雷器1_0" y="662.968253968254" zvalue="1805"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645576198" ObjectName="接地变2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,900.873,681.54) scale(1.07143,-1.42857) translate(-59.6296,-1153.05)" width="12" x="894.4444444444445" y="662.968253968254"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="73">
   <use class="kv35" height="30" transform="rotate(0,1654.57,238.857) scale(1.42857,1.04762) translate(-493.157,-10.1429)" width="15" x="1643.857142857143" xlink:href="#Disconnector:刀闸_0" y="223.1428571428572" zvalue="1720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449643741190" ObjectName="35kV弄环线3116隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449643741190"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1654.57,238.857) scale(1.42857,1.04762) translate(-493.157,-10.1429)" width="15" x="1643.857142857143" y="223.1428571428572"/></g>
  <g id="106">
   <use class="kv35" height="26" transform="rotate(0,1203.14,507.429) scale(1.42857,1.42857) translate(-358.371,-146.657)" width="12" x="1194.571428571428" xlink:href="#Disconnector:小车隔刀熔断器_0" y="488.8571428571429" zvalue="1732"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644068870" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449644068870"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1203.14,507.429) scale(1.42857,1.42857) translate(-358.371,-146.657)" width="12" x="1194.571428571428" y="488.8571428571429"/></g>
  <g id="123">
   <use class="kv35" height="30" transform="rotate(0,603.857,647.714) scale(1.42857,1.04762) translate(-177.943,-28.7273)" width="15" x="593.1428571428571" xlink:href="#Disconnector:刀闸_0" y="632" zvalue="1748"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644331014" ObjectName="35kVSVG3156隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449644331014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,603.857,647.714) scale(1.42857,1.04762) translate(-177.943,-28.7273)" width="15" x="593.1428571428571" y="632"/></g>
  <g id="227">
   <use class="kv35" height="30" transform="rotate(0,931.079,659.381) scale(1.42857,1.04762) translate(-276.11,-29.2576)" width="15" x="920.3650793650793" xlink:href="#Disconnector:刀闸_0" y="643.6666666666667" zvalue="1809"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645641734" ObjectName="35kV接地变3130隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449645641734"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.079,659.381) scale(1.42857,1.04762) translate(-276.11,-29.2576)" width="15" x="920.3650793650793" y="643.6666666666667"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="87">
   <use class="kv35" height="20" transform="rotate(270,1680.29,197.429) scale(1.42857,1.42857) translate(-501.943,-54.9429)" width="10" x="1673.142857142857" xlink:href="#GroundDisconnector:地刀_0" y="183.1428571428571" zvalue="1723"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449643872262" ObjectName="35kV弄环线31167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449643872262"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1680.29,197.429) scale(1.42857,1.42857) translate(-501.943,-54.9429)" width="10" x="1673.142857142857" y="183.1428571428571"/></g>
  <g id="129">
   <use class="kv35" height="20" transform="rotate(90,552.143,688.714) scale(-1.42857,1.42857) translate(-936.5,-202.329)" width="10" x="544.9999999999999" xlink:href="#GroundDisconnector:地刀_0" y="674.4285714285714" zvalue="1755"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644593158" ObjectName="35kVSVG31567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449644593158"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,552.143,688.714) scale(-1.42857,1.42857) translate(-936.5,-202.329)" width="10" x="544.9999999999999" y="674.4285714285714"/></g>
  <g id="139">
   <use class="kv35" height="20" transform="rotate(0,707.429,607.143) scale(1.42857,1.42857) translate(-210.086,-177.857)" width="10" x="700.2857142857141" xlink:href="#GroundDisconnector:地刀_0" y="592.8571428571428" zvalue="1765"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644724230" ObjectName="35kV光伏Ⅱ回线31467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449644724230"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,707.429,607.143) scale(1.42857,1.42857) translate(-210.086,-177.857)" width="10" x="700.2857142857141" y="592.8571428571428"/></g>
  <g id="158">
   <use class="kv35" height="20" transform="rotate(0,1049.57,604.286) scale(1.42857,1.42857) translate(-312.729,-177)" width="10" x="1042.428571428571" xlink:href="#GroundDisconnector:地刀_0" y="590" zvalue="1777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645051910" ObjectName="35kV光伏Ⅰ回线31267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449645051910"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1049.57,604.286) scale(1.42857,1.42857) translate(-312.729,-177)" width="10" x="1042.428571428571" y="590"/></g>
  <g id="171">
   <use class="kv35" height="20" transform="rotate(0,838.571,605.714) scale(1.42857,1.42857) translate(-249.429,-177.429)" width="10" x="831.4285714285713" xlink:href="#GroundDisconnector:地刀_0" y="591.4285714285713" zvalue="1789"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645248518" ObjectName="35kV接地变31367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449645248518"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,838.571,605.714) scale(1.42857,1.42857) translate(-249.429,-177.429)" width="10" x="831.4285714285713" y="591.4285714285713"/></g>
  <g id="16">
   <use class="kv35" height="20" transform="rotate(0,564.143,609.714) scale(-1.42857,1.42857) translate(-956.9,-178.629)" width="10" x="556.9999999999999" xlink:href="#GroundDisconnector:地刀_0" y="595.4285714285713" zvalue="1826"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645772806" ObjectName="35kVSVG31560接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449645772806"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,564.143,609.714) scale(-1.42857,1.42857) translate(-956.9,-178.629)" width="10" x="556.9999999999999" y="595.4285714285713"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="116">
   <use class="kv35" height="26" transform="rotate(0,604.571,785.095) scale(1.93878,1.39194) translate(-279.597,-215.971)" width="28" x="577.4285714285716" xlink:href="#EnergyConsumer:SVG2024_0" y="767" zvalue="1741"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449644265478" ObjectName="35kVSVG"/>
   <cge:TPSR_Ref TObjectID="6192449644265478"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,604.571,785.095) scale(1.93878,1.39194) translate(-279.597,-215.971)" width="28" x="577.4285714285716" y="767"/></g>
  <g id="195">
   <use class="v400" height="30" transform="rotate(0,861.942,778.571) scale(1.42857,-1.42857) translate(-256.011,-1317.14)" width="12" x="853.3706333629555" xlink:href="#EnergyConsumer:负荷_0" y="757.1428570898752" zvalue="1798"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645379590" ObjectName="接地变负荷"/>
   <cge:TPSR_Ref TObjectID="6192449645379590"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,861.942,778.571) scale(1.42857,-1.42857) translate(-256.011,-1317.14)" width="12" x="853.3706333629555" y="757.1428570898752"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="191">
   <g id="1910">
    <use class="kv35" height="50" transform="rotate(0,861.981,726.904) scale(0.888889,0.888293) translate(106.081,88.6193)" width="30" x="848.65" xlink:href="#PowerTransformer2:Y-Y_0" y="704.7" zvalue="1794"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874425630724" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1911">
    <use class="v400" height="50" transform="rotate(0,861.981,726.904) scale(0.888889,0.888293) translate(106.081,88.6193)" width="30" x="848.65" xlink:href="#PowerTransformer2:Y-Y_1" y="704.7" zvalue="1794"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874425696260" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399445053444" ObjectName="35kV接地变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,861.981,726.904) scale(0.888889,0.888293) translate(106.081,88.6193)" width="30" x="848.65" y="704.7"/></g>
 </g>
 <g id="CoilClass">
  <g id="198">
   <use class="kv35" height="40" transform="rotate(0,928.083,599.611) scale(0.944444,-1.31944) translate(53.8987,-1047.66)" width="25" x="916.2777777777778" xlink:href="#Coil:光伏接地变中性点_0" y="573.2222222222223" zvalue="1801"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645510662" ObjectName="接地变消弧线圈"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,928.083,599.611) scale(0.944444,-1.31944) translate(53.8987,-1047.66)" width="25" x="916.2777777777778" y="573.2222222222223"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,537.429,486.81) scale(1,1) translate(0,0)" writing-mode="lr" x="536.96" xml:space="preserve" y="491.59" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124481073156" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1501,445.81) scale(1,1) translate(0,0)" writing-mode="lr" x="1500.53" xml:space="preserve" y="450.59" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124481597444" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="23" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,723.053,854.431) scale(1,1) translate(0,1.87437e-13)" writing-mode="lr" x="722.58" xml:space="preserve" y="859.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124484808708" ObjectName="P"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="24" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1075.2,846.241) scale(1,1) translate(1.14375e-13,-1.85224e-13)" writing-mode="lr" x="1074.73" xml:space="preserve" y="850.9299999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124485726212" ObjectName="P"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="26" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,723.053,876.183) scale(1,1) translate(0,1.92267e-13)" writing-mode="lr" x="722.58" xml:space="preserve" y="880.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124484874244" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="27" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1075.2,871.754) scale(1,1) translate(1.14375e-13,-1.90889e-13)" writing-mode="lr" x="1074.73" xml:space="preserve" y="876.45" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124485791748" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="29" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,723.053,897.934) scale(1,1) translate(0,1.97097e-13)" writing-mode="lr" x="722.58" xml:space="preserve" y="902.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124484939780" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="30" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1075.2,897.267) scale(1,1) translate(1.14375e-13,-1.96554e-13)" writing-mode="lr" x="1074.73" xml:space="preserve" y="901.96" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124485857284" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="32" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,589.72,859.986) scale(1,1) translate(0,1.88671e-13)" writing-mode="lr" x="589.25" xml:space="preserve" y="864.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124491558916" ObjectName="P"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="33" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,588.609,881.738) scale(1,1) translate(0,1.93501e-13)" writing-mode="lr" x="588.14" xml:space="preserve" y="886.4" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124483497988" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="35" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,588.609,903.49) scale(1,1) translate(0,1.98331e-13)" writing-mode="lr" x="588.14" xml:space="preserve" y="908.15" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124483563524" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="36" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1650.13,44.5899) scale(1,1) translate(0,7.75676e-14)" writing-mode="lr" x="1649.66" xml:space="preserve" y="49.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124482449412" ObjectName="P"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="37" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1650.13,68.5238) scale(1,1) translate(0,1.33369e-13)" writing-mode="lr" x="1649.66" xml:space="preserve" y="73.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124482514948" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1650.13,92.4577) scale(1,1) translate(0,1.8917e-13)" writing-mode="lr" x="1649.66" xml:space="preserve" y="97.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124482580484" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,511.167) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="516.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124480876548" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,533.667) scale(1,1) translate(0,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="538.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124480942084" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,558.167) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="563.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124481007622" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,485.667) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="490.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124481138692" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="22" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,109,162) scale(1,1) translate(0,0)" writing-mode="lr" x="109.15" xml:space="preserve" y="168.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124491689988" ObjectName=""/>
   </metadata>
  </g>
  <g id="1035">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="1035" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,292,161) scale(1,1) translate(0,0)" writing-mode="lr" x="292.15" xml:space="preserve" y="167.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124491755524" ObjectName=""/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,140,187.71) scale(1,1) translate(0,0)" writing-mode="lr" x="139.54" xml:space="preserve" y="192.49" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124481269764" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,294.339,302.643) scale(0.708333,0.665547) translate(116.824,147.069)" width="30" x="283.71" xlink:href="#State:红绿圆(方形)_0" y="292.66" zvalue="1871"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374886305795" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,294.339,302.643) scale(0.708333,0.665547) translate(116.824,147.069)" width="30" x="283.71" y="292.66"/></g>
  <g id="21">
   <use height="30" transform="rotate(0,198.714,302.643) scale(0.708333,0.665547) translate(77.4485,147.069)" width="30" x="188.09" xlink:href="#State:红绿圆(方形)_0" y="292.66" zvalue="1872"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950169427972" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,198.714,302.643) scale(0.708333,0.665547) translate(77.4485,147.069)" width="30" x="188.09" y="292.66"/></g>
 </g>
</svg>