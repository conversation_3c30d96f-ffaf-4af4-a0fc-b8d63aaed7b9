<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584527362" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Accessory:PT4_0" viewBox="0,0,18,15">
   <use terminal-index="0" type="0" x="17.15" xlink:href="#terminal" y="9.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.3" x2="5.3" y1="4.9" y2="4.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.33333333333334" x2="13.83333333333334" y1="9.25" y2="9.25"/>
   <rect fill-opacity="0" height="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.96,13.4) scale(1,1) translate(0,0)" width="7.92" x="4" y="11.98"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.499999999999998" x2="2.083333333333331" y1="9.25" y2="9.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="8.449999999999999" y1="7.433333333333334" y2="9.300000000000001"/>
   <path d="M 12 13.35 L 15 13.35 L 15 9.35" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 6 12.45 L 6 14.45 L 8.66667 13.4333 L 6 12.45" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.96666666666667" x2="1.96666666666667" y1="7.75" y2="10.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.716666666666673" x2="4.716666666666673" y1="9.25" y2="9.25"/>
   <ellipse cx="8.5" cy="4.75" fill-opacity="0" rx="2.57" ry="2.57" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="12.41666666666667" x2="4.75" y1="9.25" y2="9.25"/>
   <ellipse cx="4.5" cy="4.75" fill-opacity="0" rx="2.57" ry="2.57" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.5" cy="4.75" fill-opacity="0" rx="2.57" ry="2.57" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="3" fill-opacity="0" rx="2.57" ry="2.57" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.71666666666667" x2="4.71666666666667" y1="7.85" y2="10.68333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.300000000000006" x2="1.300000000000006" y1="8.333333333333336" y2="10.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.8000000000000007" x2="0.8000000000000007" y1="8.750000000000004" y2="9.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.55" x2="9.550000000000001" y1="4.9" y2="4.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.71666666666667" x2="13.71666666666667" y1="9.25" y2="9.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.5" x2="1.916666666666666" y1="13.48333333333333" y2="13.48333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.933333333333335" x2="1.933333333333335" y1="11.98333333333333" y2="14.98333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.71666666666667" x2="13.71666666666667" y1="7.85" y2="10.68333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.46666666666667" x2="3.46666666666667" y1="7.850000000000001" y2="10.68333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.26666666666667" x2="1.26666666666667" y1="12.56666666666667" y2="14.56666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.7666666666666639" x2="0.7666666666666639" y1="12.98333333333334" y2="13.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.8" x2="13.8" y1="4.9" y2="4.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="12.46666666666667" x2="12.46666666666667" y1="7.85" y2="10.68333333333333"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Accessory:线路PT三绕组_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="24" y2="24"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.98040845230975" x2="31.98040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.66666666666667" y2="32.66666666666667"/>
   <ellipse cx="12.01" cy="28.48" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666664" x2="15.16666666666666" y1="28.91666666666667" y2="28.91666666666667"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1334.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1133.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="220kV那邦水电厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="286" x="57" xlink:href="logo.png" y="45"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200,75) scale(1,1) translate(0,0)" writing-mode="lr" x="200" xml:space="preserve" y="78.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,199.5,74.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="199.5" xml:space="preserve" y="83.69" zvalue="3">220kV那邦水电厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="436"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="436">信号一览</text>
  <line fill="none" id="169" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="383" x2="383" y1="13" y2="1043" zvalue="4"/>
  <line fill="none" id="167" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00000000000045" x2="384" y1="148.8704926140824" y2="148.8704926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1077.89,208.556) scale(1,1) translate(4.71573e-13,0)" writing-mode="lr" x="1077.89" xml:space="preserve" y="213.06" zvalue="15">2516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1128.78,238.495) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.78" xml:space="preserve" y="242.99" zvalue="24">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.07,178.495) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.07" xml:space="preserve" y="182.99" zvalue="26">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,560.556,611.667) scale(1,1) translate(0,0)" writing-mode="lr" x="560.5599999999999" xml:space="preserve" y="616.17" zvalue="68">2540</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.244,579.694) scale(1,1) translate(0,0)" writing-mode="lr" x="662.2442838449131" xml:space="preserve" y="584.1944444444445" zvalue="75">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,604.565,938.056) scale(1,1) translate(0,0)" writing-mode="lr" x="604.5700000000001" xml:space="preserve" y="942.5599999999999" zvalue="105">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.361,702.222) scale(1,1) translate(6.46791e-13,0)" writing-mode="lr" x="744.36" xml:space="preserve" y="706.72" zvalue="116">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.722,764.111) scale(1,1) translate(0,0)" writing-mode="lr" x="798.72" xml:space="preserve" y="768.61" zvalue="120">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,661.5,864) scale(1,1) translate(0,0)" writing-mode="lr" x="661.5" xml:space="preserve" y="868.5" zvalue="138">0913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,504.5,861) scale(1,1) translate(0,0)" writing-mode="lr" x="504.5" xml:space="preserve" y="865.5" zvalue="141">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.928,582) scale(1,1) translate(0,0)" writing-mode="lr" x="802.9299999999999" xml:space="preserve" y="586.5" zvalue="151">0911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1043.6,84.2222) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.6" xml:space="preserve" y="88.72" zvalue="193">220kV那盈线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="404" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,867.111,258.194) scale(1,1) translate(0,0)" writing-mode="lr" x="867.11" xml:space="preserve" y="262.69" zvalue="198">254</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="406" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,960.111,254.654) scale(1,1) translate(0,0)" writing-mode="lr" x="960.11" xml:space="preserve" y="259.15" zvalue="200">2542</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="405" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.111,255.444) scale(1,1) translate(0,0)" writing-mode="lr" x="780.11" xml:space="preserve" y="259.94" zvalue="202">2541</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="408" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,923.995,208.869) scale(1,1) translate(0,0)" writing-mode="lr" x="923.99" xml:space="preserve" y="213.37" zvalue="204">25427</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,823.995,208.869) scale(1,1) translate(0,0)" writing-mode="lr" x="823.99" xml:space="preserve" y="213.37" zvalue="206">25417</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="414" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1439.11,253.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1439.11" xml:space="preserve" y="257.94" zvalue="208">251</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="417" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1528.11,246.654) scale(1,1) translate(0,0)" writing-mode="lr" x="1528.11" xml:space="preserve" y="251.15" zvalue="209">2512</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="416" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1352.11,254.684) scale(1,1) translate(0,0)" writing-mode="lr" x="1352.11" xml:space="preserve" y="259.18" zvalue="210">2511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="419" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.99,212.869) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.99" xml:space="preserve" y="217.37" zvalue="211">25127</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="418" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1395.99,212.869) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.99" xml:space="preserve" y="217.37" zvalue="212">25117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="415" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1439.11,389.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1439.11" xml:space="preserve" y="393.94" zvalue="214">252</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="423" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1528.11,378.654) scale(1,1) translate(0,0)" writing-mode="lr" x="1528.11" xml:space="preserve" y="383.15" zvalue="215">2521</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="420" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1352.11,378.684) scale(1,1) translate(0,0)" writing-mode="lr" x="1352.11" xml:space="preserve" y="383.18" zvalue="216">2522</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="422" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1497.43,450.758) scale(1,1) translate(0,0)" writing-mode="lr" x="1497.43" xml:space="preserve" y="455.26" zvalue="217">25217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="421" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1397.93,446.008) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.93" xml:space="preserve" y="450.51" zvalue="218">25227</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="409" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,863.111,385.444) scale(1,1) translate(0,0)" writing-mode="lr" x="863.11" xml:space="preserve" y="389.94" zvalue="220">253</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="411" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,952.111,374.654) scale(1,1) translate(0,0)" writing-mode="lr" x="952.11" xml:space="preserve" y="379.15" zvalue="221">2531</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="410" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,776.111,375.444) scale(1,1) translate(0,0)" writing-mode="lr" x="776.11" xml:space="preserve" y="379.94" zvalue="222">2532</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="413" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,923.176,444.758) scale(1,1) translate(0,0)" writing-mode="lr" x="923.1799999999999" xml:space="preserve" y="449.26" zvalue="223">25317</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="412" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.926,443.646) scale(1,1) translate(0,0)" writing-mode="lr" x="821.9299999999999" xml:space="preserve" y="448.15" zvalue="224">25327</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569,475.25) scale(1,1) translate(0,0)" writing-mode="lr" x="569" xml:space="preserve" y="479.75" zvalue="244">2546</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,505.894,516.939) scale(1,1) translate(0,0)" writing-mode="lr" x="505.89" xml:space="preserve" y="521.4400000000001" zvalue="247">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,508.177,440.939) scale(1,1) translate(0,0)" writing-mode="lr" x="508.18" xml:space="preserve" y="445.44" zvalue="249">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,616.778,659) scale(1,1) translate(0,0)" writing-mode="lr" x="616.78" xml:space="preserve" y="663.5" zvalue="263">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,584.458,716.111) scale(1,1) translate(0,0)" writing-mode="lr" x="584.46" xml:space="preserve" y="720.61" zvalue="265">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,533.106,786.424) scale(1,1) translate(0,0)" writing-mode="lr" x="533.11" xml:space="preserve" y="790.92" zvalue="269">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.498,907.889) scale(1,1) translate(0,0)" writing-mode="lr" x="780.5" xml:space="preserve" y="912.39" zvalue="280">#1厂用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,453.778,936.333) scale(1,1) translate(0,0)" writing-mode="lr" x="453.78" xml:space="preserve" y="940.83" zvalue="281">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.56,611.667) scale(1,1) translate(4.72758e-13,0)" writing-mode="lr" x="1080.56" xml:space="preserve" y="616.17" zvalue="292">2530</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1182.24,579.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1182.244283844913" xml:space="preserve" y="584.1944444444445" zvalue="295">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1128.57,938.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.57" xml:space="preserve" y="942.5599999999999" zvalue="297">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1263.43,702.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1263.43" xml:space="preserve" y="706.72" zvalue="299">0221</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1318.72,764.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.72" xml:space="preserve" y="768.61" zvalue="302">022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1181.5,864) scale(1,1) translate(0,0)" writing-mode="lr" x="1181.5" xml:space="preserve" y="868.5" zvalue="304">0923</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1024.5,861) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.5" xml:space="preserve" y="865.5" zvalue="306">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.93,582) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.93" xml:space="preserve" y="586.5" zvalue="308">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089,479) scale(1,1) translate(0,0)" writing-mode="lr" x="1089" xml:space="preserve" y="483.5" zvalue="310">2536</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.89,516.939) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.89" xml:space="preserve" y="521.4400000000001" zvalue="312">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1028.18,440.939) scale(1,1) translate(0,0)" writing-mode="lr" x="1028.18" xml:space="preserve" y="445.44" zvalue="314">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1136.78,659) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.78" xml:space="preserve" y="663.5" zvalue="323">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1092.48,716.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1092.48" xml:space="preserve" y="720.61" zvalue="325">021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1053.11,786.424) scale(1,1) translate(0,0)" writing-mode="lr" x="1053.11" xml:space="preserve" y="790.92" zvalue="330">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1300.5,907.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1300.5" xml:space="preserve" y="912.39" zvalue="337">#1隔离变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,973.778,936.333) scale(1,1) translate(0,0)" writing-mode="lr" x="973.78" xml:space="preserve" y="940.83" zvalue="339">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="356" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1636.56,611.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1636.56" xml:space="preserve" y="616.17" zvalue="350">2520</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="355" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1738.24,579.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1738.244283844913" xml:space="preserve" y="584.1944444444445" zvalue="353">#3主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="354" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1684.57,938.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1684.57" xml:space="preserve" y="942.5599999999999" zvalue="355">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1820.36,702.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1820.36" xml:space="preserve" y="706.72" zvalue="357">0321</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1874.72,764.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1874.72" xml:space="preserve" y="768.61" zvalue="360">032</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1737.5,864) scale(1,1) translate(0,0)" writing-mode="lr" x="1737.5" xml:space="preserve" y="868.5" zvalue="362">0933</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1580.5,861) scale(1,1) translate(0,0)" writing-mode="lr" x="1580.5" xml:space="preserve" y="865.5" zvalue="364">0932</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1880.98,582) scale(1,1) translate(0,0)" writing-mode="lr" x="1880.98" xml:space="preserve" y="586.5" zvalue="366">0931</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1647,479) scale(1,1) translate(0,0)" writing-mode="lr" x="1647" xml:space="preserve" y="483.5" zvalue="368">2526</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1581.89,516.939) scale(1,1) translate(0,0)" writing-mode="lr" x="1581.89" xml:space="preserve" y="521.4400000000001" zvalue="370">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="346" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1584.18,440.939) scale(1,1) translate(0,0)" writing-mode="lr" x="1584.18" xml:space="preserve" y="445.44" zvalue="372">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1692.78,659) scale(1,1) translate(0,0)" writing-mode="lr" x="1692.78" xml:space="preserve" y="663.5" zvalue="381">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1655.98,716.111) scale(1,1) translate(1.82354e-13,0)" writing-mode="lr" x="1655.98" xml:space="preserve" y="720.61" zvalue="383">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="343" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1609.11,786.424) scale(1,1) translate(0,0)" writing-mode="lr" x="1609.11" xml:space="preserve" y="790.92" zvalue="388">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="342" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1856.5,907.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1856.5" xml:space="preserve" y="912.39" zvalue="395">#2厂用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="341" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.78,936.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.78" xml:space="preserve" y="940.83" zvalue="397">励磁变</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="47" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="414"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="416">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="417">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="418">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="419">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="420">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="422">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="423">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="424">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,956) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="962" zvalue="425">YYKBH12-109</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="428">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="429">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="437">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="438">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="441">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="445">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="449">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="451">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="436"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="DisconnectorClass">
  <g id="160">
   <use class="kv220" height="30" transform="rotate(0,1043.67,205.556) scale(-1.11111,-0.814815) translate(-1982.13,-460.606)" width="15" x="1035.333333359824" xlink:href="#Disconnector:刀闸_0" y="193.3333333333333" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740931077" ObjectName="220kV那盈线2516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449740931077"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1043.67,205.556) scale(-1.11111,-0.814815) translate(-1982.13,-460.606)" width="15" x="1035.333333359824" y="193.3333333333333"/></g>
  <g id="98">
   <use class="kv10" height="30" transform="rotate(0,772.333,703.222) scale(1.11111,0.814815) translate(-76.4,157.045)" width="15" x="764" xlink:href="#Disconnector:刀闸_0" y="691" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740406790" ObjectName="#1厂用变0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449740406790"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772.333,703.222) scale(1.11111,0.814815) translate(-76.4,157.045)" width="15" x="764" y="691"/></g>
  <g id="152">
   <use class="kv10" height="26" transform="rotate(0,684.5,865) scale(1,1) translate(0,0)" width="12" x="678.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="852" zvalue="136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740341254" ObjectName="#1发电机0913隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449740341254"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,684.5,865) scale(1,1) translate(0,0)" width="12" x="678.5" y="852"/></g>
  <g id="159">
   <use class="kv10" height="26" transform="rotate(0,527,862) scale(1,1) translate(0,0)" width="12" x="521" xlink:href="#Disconnector:单手车刀闸1212_0" y="849" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740275718" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449740275718"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,527,862) scale(1,1) translate(0,0)" width="12" x="521" y="849"/></g>
  <g id="171">
   <use class="kv10" height="26" transform="rotate(0,772.428,584) scale(1,-1) translate(0,-1168)" width="12" x="766.4284747632814" xlink:href="#Disconnector:单手车刀闸1212_0" y="571" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740210182" ObjectName="#1厂用变0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449740210182"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,772.428,584) scale(1,-1) translate(0,-1168)" width="12" x="766.4284747632814" y="571"/></g>
  <g id="188">
   <use class="kv220" height="30" transform="rotate(270,956.111,286.988) scale(-1.11111,-0.814815) translate(-1815.78,-641.977)" width="15" x="947.7777778042689" xlink:href="#Disconnector:刀闸_0" y="274.7653728621046" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741127685" ObjectName="#1主变220kV侧2542隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449741127685"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,956.111,286.988) scale(-1.11111,-0.814815) translate(-1815.78,-641.977)" width="15" x="947.7777778042689" y="274.7653728621046"/></g>
  <g id="190">
   <use class="kv220" height="30" transform="rotate(270,780.111,287.778) scale(-1.11111,-0.814815) translate(-1481.38,-643.737)" width="15" x="771.7777778042688" xlink:href="#Disconnector:刀闸_0" y="275.5555555555554" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741193221" ObjectName="#1主变220kV侧2541隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449741193221"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,780.111,287.778) scale(-1.11111,-0.814815) translate(-1481.38,-643.737)" width="15" x="771.7777778042688" y="275.5555555555554"/></g>
  <g id="197">
   <use class="kv220" height="30" transform="rotate(270,1528.11,286.988) scale(-1.11111,-0.814815) translate(-2902.58,-641.977)" width="15" x="1519.777777804269" xlink:href="#Disconnector:刀闸_0" y="274.7653728621046" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741848581" ObjectName="220kV那盈线2512隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449741848581"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1528.11,286.988) scale(-1.11111,-0.814815) translate(-2902.58,-641.977)" width="15" x="1519.777777804269" y="274.7653728621046"/></g>
  <g id="196">
   <use class="kv220" height="30" transform="rotate(270,1352.11,287.017) scale(-1.11111,-0.814815) translate(-2568.18,-642.043)" width="15" x="1343.777777804269" xlink:href="#Disconnector:刀闸_0" y="274.7949424473879" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741783045" ObjectName="220kV那盈线2511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449741783045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1352.11,287.017) scale(-1.11111,-0.814815) translate(-2568.18,-642.043)" width="15" x="1343.777777804269" y="274.7949424473879"/></g>
  <g id="202">
   <use class="kv220" height="30" transform="rotate(270,1528.11,354.988) scale(-1.11111,-0.814815) translate(-2902.58,-793.432)" width="15" x="1519.777777804269" xlink:href="#Disconnector:刀闸_0" y="342.7653728621046" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742241797" ObjectName="#3主变220kV侧2521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449742241797"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1528.11,354.988) scale(-1.11111,-0.814815) translate(-2902.58,-793.432)" width="15" x="1519.777777804269" y="342.7653728621046"/></g>
  <g id="201">
   <use class="kv220" height="30" transform="rotate(270,1352.11,355.017) scale(-1.11111,-0.814815) translate(-2568.18,-793.498)" width="15" x="1343.777777804269" xlink:href="#Disconnector:刀闸_0" y="342.7949424473879" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742176261" ObjectName="#3主变220kV侧2522隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449742176261"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1352.11,355.017) scale(-1.11111,-0.814815) translate(-2568.18,-793.498)" width="15" x="1343.777777804269" y="342.7949424473879"/></g>
  <g id="207">
   <use class="kv220" height="30" transform="rotate(270,952.111,354.988) scale(-1.11111,-0.814815) translate(-1808.18,-793.432)" width="15" x="943.7777778042688" xlink:href="#Disconnector:刀闸_0" y="342.7653728621046" zvalue="220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742635014" ObjectName="#2主变220kV侧2531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449742635014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,952.111,354.988) scale(-1.11111,-0.814815) translate(-1808.18,-793.432)" width="15" x="943.7777778042688" y="342.7653728621046"/></g>
  <g id="206">
   <use class="kv220" height="30" transform="rotate(270,776.111,355.778) scale(-1.11111,-0.814815) translate(-1473.78,-795.192)" width="15" x="767.7777778042688" xlink:href="#Disconnector:刀闸_0" y="343.5555555555554" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742569478" ObjectName="#2主变220kV侧2532隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449742569478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,776.111,355.778) scale(-1.11111,-0.814815) translate(-1473.78,-795.192)" width="15" x="767.7777778042688" y="343.5555555555554"/></g>
  <g id="235">
   <use class="kv220" height="30" transform="rotate(0,604.778,476) scale(-1.11111,-0.814815) translate(-1148.24,-1062.96)" width="15" x="596.4444444709355" xlink:href="#Disconnector:刀闸_0" y="463.7777777777778" zvalue="243"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742962694" ObjectName="#1主变220kV侧2546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449742962694"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,604.778,476) scale(-1.11111,-0.814815) translate(-1148.24,-1062.96)" width="15" x="596.4444444709355" y="463.7777777777778"/></g>
  <g id="245">
   <use class="kv10" height="30" transform="rotate(0,604.778,660) scale(-1.11111,-0.814815) translate(-1148.24,-1472.78)" width="15" x="596.4444444709355" xlink:href="#Disconnector:刀闸_0" y="647.7777777777778" zvalue="262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743159302" ObjectName="#1主变10kV侧0111隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449743159302"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,604.778,660) scale(-1.11111,-0.814815) translate(-1148.24,-1472.78)" width="15" x="596.4444444709355" y="647.7777777777778"/></g>
  <g id="336">
   <use class="kv10" height="30" transform="rotate(0,1292.33,703.222) scale(1.11111,0.814815) translate(-128.4,157.045)" width="15" x="1284" xlink:href="#Disconnector:刀闸_0" y="691" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449746894853" ObjectName="#1隔离变0221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449746894853"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1292.33,703.222) scale(1.11111,0.814815) translate(-128.4,157.045)" width="15" x="1284" y="691"/></g>
  <g id="333">
   <use class="kv10" height="26" transform="rotate(0,1204.5,865) scale(1,1) translate(0,0)" width="12" x="1198.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="852" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744797701" ObjectName="#2发电机0923隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449744797701"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1204.5,865) scale(1,1) translate(0,0)" width="12" x="1198.5" y="852"/></g>
  <g id="332">
   <use class="kv10" height="26" transform="rotate(0,1047,862) scale(1,1) translate(0,0)" width="12" x="1041" xlink:href="#Disconnector:单手车刀闸1212_0" y="849" zvalue="305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744732165" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449744732165"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1047,862) scale(1,1) translate(0,0)" width="12" x="1041" y="849"/></g>
  <g id="331">
   <use class="kv10" height="26" transform="rotate(0,1292.43,584) scale(1,-1) translate(0,-1168)" width="12" x="1286.428474763281" xlink:href="#Disconnector:单手车刀闸1212_0" y="571" zvalue="307"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744666629" ObjectName="#1隔离变0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449744666629"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1292.43,584) scale(1,-1) translate(0,-1168)" width="12" x="1286.428474763281" y="571"/></g>
  <g id="330">
   <use class="kv220" height="30" transform="rotate(0,1124.78,476) scale(-1.11111,-0.814815) translate(-2136.24,-1062.96)" width="15" x="1116.444444470936" xlink:href="#Disconnector:刀闸_0" y="463.7777777777778" zvalue="309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744601093" ObjectName="#2主变220kV侧2536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449744601093"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1124.78,476) scale(-1.11111,-0.814815) translate(-2136.24,-1062.96)" width="15" x="1116.444444470936" y="463.7777777777778"/></g>
  <g id="320">
   <use class="kv10" height="30" transform="rotate(0,1124.78,660) scale(-1.11111,-0.814815) translate(-2136.24,-1472.78)" width="15" x="1116.444444470936" xlink:href="#Disconnector:刀闸_0" y="647.7777777777778" zvalue="322"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744142341" ObjectName="#2主变10kV侧0211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449744142341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1124.78,660) scale(-1.11111,-0.814815) translate(-2136.24,-1472.78)" width="15" x="1116.444444470936" y="647.7777777777778"/></g>
  <g id="393">
   <use class="kv10" height="30" transform="rotate(0,1848.33,703.222) scale(1.11111,0.814815) translate(-184,157.045)" width="15" x="1840" xlink:href="#Disconnector:刀闸_0" y="691" zvalue="356"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449747025925" ObjectName="#2厂用变0321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449747025925"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1848.33,703.222) scale(1.11111,0.814815) translate(-184,157.045)" width="15" x="1840" y="691"/></g>
  <g id="390">
   <use class="kv10" height="26" transform="rotate(0,1760.5,865) scale(1,1) translate(0,0)" width="12" x="1754.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="852" zvalue="361"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449746239493" ObjectName="#3发电机0933隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449746239493"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1760.5,865) scale(1,1) translate(0,0)" width="12" x="1754.5" y="852"/></g>
  <g id="389">
   <use class="kv10" height="26" transform="rotate(0,1603,862) scale(1,1) translate(0,0)" width="12" x="1597" xlink:href="#Disconnector:单手车刀闸1212_0" y="849" zvalue="363"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449746173957" ObjectName="#3发电机0932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449746173957"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1603,862) scale(1,1) translate(0,0)" width="12" x="1597" y="849"/></g>
  <g id="388">
   <use class="kv10" height="26" transform="rotate(0,1848.43,584) scale(1,-1) translate(0,-1168)" width="12" x="1842.428474763281" xlink:href="#Disconnector:单手车刀闸1212_0" y="571" zvalue="365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449746108421" ObjectName="#2厂用变0931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449746108421"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1848.43,584) scale(1,-1) translate(0,-1168)" width="12" x="1842.428474763281" y="571"/></g>
  <g id="387">
   <use class="kv220" height="30" transform="rotate(0,1680.78,476) scale(-1.11111,-0.814815) translate(-3192.64,-1062.96)" width="15" x="1672.444444470936" xlink:href="#Disconnector:刀闸_0" y="463.7777777777778" zvalue="367"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449746042885" ObjectName="#3主变220kV侧2526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449746042885"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1680.78,476) scale(-1.11111,-0.814815) translate(-3192.64,-1062.96)" width="15" x="1672.444444470936" y="463.7777777777778"/></g>
  <g id="377">
   <use class="kv10" height="30" transform="rotate(0,1680.78,660) scale(-1.11111,-0.814815) translate(-3192.64,-1472.78)" width="15" x="1672.444444470936" xlink:href="#Disconnector:刀闸_0" y="647.7777777777778" zvalue="380"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745584133" ObjectName="#3主变10kV侧0311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449745584133"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1680.78,660) scale(-1.11111,-0.814815) translate(-3192.64,-1472.78)" width="15" x="1672.444444470936" y="647.7777777777778"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="148">
   <use class="kv220" height="20" transform="rotate(90,1099.99,235.758) scale(-1.11111,-1.11111) translate(-2089.43,-446.828)" width="10" x="1094.439394376495" xlink:href="#GroundDisconnector:地刀_0" y="224.646464646465" zvalue="23"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740865541" ObjectName="220kV那盈线25160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449740865541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1099.99,235.758) scale(-1.11111,-1.11111) translate(-2089.43,-446.828)" width="10" x="1094.439394376495" y="224.646464646465"/></g>
  <g id="146">
   <use class="kv220" height="20" transform="rotate(90,1100.73,179.495) scale(-1.11111,-1.11111) translate(-2090.84,-339.929)" width="10" x="1095.176767773098" xlink:href="#GroundDisconnector:地刀_0" y="168.3838383838384" zvalue="25"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740734469" ObjectName="220kV那盈线25167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449740734469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1100.73,179.495) scale(-1.11111,-1.11111) translate(-2090.84,-339.929)" width="10" x="1095.176767773098" y="168.3838383838384"/></g>
  <g id="181">
   <use class="kv220" height="40" transform="rotate(0,516.778,612.667) scale(1.11111,-1.11111) translate(-49.4556,-1161.84)" width="40" x="494.5555555555555" xlink:href="#GroundDisconnector:中性点地刀12_0" y="590.4444444444445" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740603398" ObjectName="#1主变220kV侧2540中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449740603398"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,516.778,612.667) scale(1.11111,-1.11111) translate(-49.4556,-1161.84)" width="40" x="494.5555555555555" y="590.4444444444445"/></g>
  <g id="192">
   <use class="kv220" height="20" transform="rotate(0,923.995,239.758) scale(-1.11111,-1.11111) translate(-1755.03,-454.428)" width="10" x="918.4393943764945" xlink:href="#GroundDisconnector:地刀_0" y="228.646464646465" zvalue="203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741324293" ObjectName="#1主变220kV侧25427接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449741324293"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,923.995,239.758) scale(-1.11111,-1.11111) translate(-1755.03,-454.428)" width="10" x="918.4393943764945" y="228.646464646465"/></g>
  <g id="193">
   <use class="kv220" height="20" transform="rotate(0,823.995,239.758) scale(-1.11111,-1.11111) translate(-1565.03,-454.428)" width="10" x="818.4393943764945" xlink:href="#GroundDisconnector:地刀_0" y="228.646464646465" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741455365" ObjectName="#1主变220kV侧25417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449741455365"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,823.995,239.758) scale(-1.11111,-1.11111) translate(-1565.03,-454.428)" width="10" x="818.4393943764945" y="228.646464646465"/></g>
  <g id="195">
   <use class="kv220" height="20" transform="rotate(0,1495.99,239.758) scale(-1.11111,-1.11111) translate(-2841.83,-454.428)" width="10" x="1490.439394376494" xlink:href="#GroundDisconnector:地刀_0" y="228.646464646465" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741717509" ObjectName="220kV那盈线25127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449741717509"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1495.99,239.758) scale(-1.11111,-1.11111) translate(-2841.83,-454.428)" width="10" x="1490.439394376494" y="228.646464646465"/></g>
  <g id="194">
   <use class="kv220" height="20" transform="rotate(0,1395.99,239.758) scale(-1.11111,-1.11111) translate(-2651.83,-454.428)" width="10" x="1390.439394376494" xlink:href="#GroundDisconnector:地刀_0" y="228.646464646465" zvalue="211"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741586437" ObjectName="220kV那盈线25117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449741586437"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1395.99,239.758) scale(-1.11111,-1.11111) translate(-2651.83,-454.428)" width="10" x="1390.439394376494" y="228.646464646465"/></g>
  <g id="200">
   <use class="kv220" height="20" transform="rotate(180,1495.99,415.758) scale(-1.11111,-1.11111) translate(-2841.83,-788.828)" width="10" x="1490.439394376494" xlink:href="#GroundDisconnector:地刀_0" y="404.646464646465" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742110725" ObjectName="#3主变220kV侧25217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449742110725"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1495.99,415.758) scale(-1.11111,-1.11111) translate(-2841.83,-788.828)" width="10" x="1490.439394376494" y="404.646464646465"/></g>
  <g id="199">
   <use class="kv220" height="20" transform="rotate(180,1395.99,415.758) scale(-1.11111,-1.11111) translate(-2651.83,-788.828)" width="10" x="1390.439394376494" xlink:href="#GroundDisconnector:地刀_0" y="404.646464646465" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741979653" ObjectName="#3主变220kV侧25227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449741979653"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1395.99,415.758) scale(-1.11111,-1.11111) translate(-2651.83,-788.828)" width="10" x="1390.439394376494" y="404.646464646465"/></g>
  <g id="205">
   <use class="kv220" height="20" transform="rotate(180,919.995,415.758) scale(-1.11111,-1.11111) translate(-1747.43,-788.828)" width="10" x="914.4393943764945" xlink:href="#GroundDisconnector:地刀_0" y="404.646464646465" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742503942" ObjectName="#2主变220kV侧25317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449742503942"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,919.995,415.758) scale(-1.11111,-1.11111) translate(-1747.43,-788.828)" width="10" x="914.4393943764945" y="404.646464646465"/></g>
  <g id="204">
   <use class="kv220" height="20" transform="rotate(180,819.995,414.646) scale(-1.11111,-1.11111) translate(-1557.43,-786.717)" width="10" x="814.4393943764945" xlink:href="#GroundDisconnector:地刀_0" y="403.5353535353538" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742372869" ObjectName="#2主变220kV侧25327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449742372869"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,819.995,414.646) scale(-1.11111,-1.11111) translate(-1557.43,-786.717)" width="10" x="814.4393943764945" y="403.5353535353538"/></g>
  <g id="233">
   <use class="kv220" height="20" transform="rotate(270,533.106,514.202) scale(1.11111,-1.11111) translate(-52.7551,-975.873)" width="10" x="527.5505054876057" xlink:href="#GroundDisconnector:地刀_0" y="503.0909090909094" zvalue="246"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742897157" ObjectName="#1主变220kV侧25467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449742897157"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,533.106,514.202) scale(1.11111,-1.11111) translate(-52.7551,-975.873)" width="10" x="527.5505054876057" y="503.0909090909094"/></g>
  <g id="232">
   <use class="kv220" height="20" transform="rotate(270,533.843,441.939) scale(1.11111,-1.11111) translate(-52.8288,-838.574)" width="10" x="528.2878788842095" xlink:href="#GroundDisconnector:地刀_0" y="430.8282828282829" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449742766086" ObjectName="#1主变220kV侧25460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449742766086"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,533.843,441.939) scale(1.11111,-1.11111) translate(-52.8288,-838.574)" width="10" x="528.2878788842095" y="430.8282828282829"/></g>
  <g id="249">
   <use class="kv10" height="20" transform="rotate(270,533.106,762.202) scale(1.11111,-1.11111) translate(-52.7551,-1447.07)" width="10" x="527.5505054876057" xlink:href="#GroundDisconnector:地刀_0" y="751.0909090909093" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743290374" ObjectName="#1主变10kV侧01167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449743290374"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,533.106,762.202) scale(1.11111,-1.11111) translate(-52.7551,-1447.07)" width="10" x="527.5505054876057" y="751.0909090909093"/></g>
  <g id="340">
   <use class="kv220" height="40" transform="rotate(0,1036.78,612.667) scale(1.11111,-1.11111) translate(-101.456,-1161.84)" width="40" x="1014.555555555556" xlink:href="#GroundDisconnector:中性点地刀12_0" y="590.4444444444445" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745059845" ObjectName="#2主变220kV侧2530中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449745059845"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1036.78,612.667) scale(1.11111,-1.11111) translate(-101.456,-1161.84)" width="40" x="1014.555555555556" y="590.4444444444445"/></g>
  <g id="329">
   <use class="kv220" height="20" transform="rotate(270,1053.11,514.202) scale(1.11111,-1.11111) translate(-104.755,-975.873)" width="10" x="1047.550505487606" xlink:href="#GroundDisconnector:地刀_0" y="503.0909090909094" zvalue="311"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744535557" ObjectName="#2主变220kV侧25367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449744535557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1053.11,514.202) scale(1.11111,-1.11111) translate(-104.755,-975.873)" width="10" x="1047.550505487606" y="503.0909090909094"/></g>
  <g id="328">
   <use class="kv220" height="20" transform="rotate(270,1053.84,441.939) scale(1.11111,-1.11111) translate(-104.829,-838.574)" width="10" x="1048.287878884209" xlink:href="#GroundDisconnector:地刀_0" y="430.8282828282829" zvalue="313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744404485" ObjectName="#2主变220kV侧25360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449744404485"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1053.84,441.939) scale(1.11111,-1.11111) translate(-104.829,-838.574)" width="10" x="1048.287878884209" y="430.8282828282829"/></g>
  <g id="316">
   <use class="kv10" height="20" transform="rotate(270,1053.11,762.202) scale(1.11111,-1.11111) translate(-104.755,-1447.07)" width="10" x="1047.550505487606" xlink:href="#GroundDisconnector:地刀_0" y="751.0909090909093" zvalue="328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744076805" ObjectName="#2主变10kV侧02167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449744076805"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1053.11,762.202) scale(1.11111,-1.11111) translate(-104.755,-1447.07)" width="10" x="1047.550505487606" y="751.0909090909093"/></g>
  <g id="397">
   <use class="kv220" height="40" transform="rotate(0,1592.78,612.667) scale(1.11111,-1.11111) translate(-157.056,-1161.84)" width="40" x="1570.555555555556" xlink:href="#GroundDisconnector:中性点地刀12_0" y="590.4444444444445" zvalue="349"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449746501637" ObjectName="#3主变220kV侧2520中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449746501637"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1592.78,612.667) scale(1.11111,-1.11111) translate(-157.056,-1161.84)" width="40" x="1570.555555555556" y="590.4444444444445"/></g>
  <g id="386">
   <use class="kv220" height="20" transform="rotate(270,1609.11,514.202) scale(1.11111,-1.11111) translate(-160.355,-975.873)" width="10" x="1603.550505487606" xlink:href="#GroundDisconnector:地刀_0" y="503.0909090909094" zvalue="369"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745977349" ObjectName="#3主变220kV侧25267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449745977349"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1609.11,514.202) scale(1.11111,-1.11111) translate(-160.355,-975.873)" width="10" x="1603.550505487606" y="503.0909090909094"/></g>
  <g id="385">
   <use class="kv220" height="20" transform="rotate(270,1609.84,441.939) scale(1.11111,-1.11111) translate(-160.429,-838.574)" width="10" x="1604.287878884209" xlink:href="#GroundDisconnector:地刀_0" y="430.8282828282829" zvalue="371"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745846277" ObjectName="#3主变220kV侧25260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449745846277"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1609.84,441.939) scale(1.11111,-1.11111) translate(-160.429,-838.574)" width="10" x="1604.287878884209" y="430.8282828282829"/></g>
  <g id="373">
   <use class="kv10" height="20" transform="rotate(270,1609.11,762.202) scale(1.11111,-1.11111) translate(-160.355,-1447.07)" width="10" x="1603.550505487606" xlink:href="#GroundDisconnector:地刀_0" y="751.0909090909093" zvalue="386"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745518597" ObjectName="#3主变10kV侧03167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449745518597"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1609.11,762.202) scale(1.11111,-1.11111) translate(-160.355,-1447.07)" width="10" x="1603.550505487606" y="751.0909090909093"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="124">
   <path class="kv220" d="M 604.66 563.3 L 501.89 563.3 L 501.89 599.11" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@2" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.66 563.3 L 501.89 563.3 L 501.89 599.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 772.4 715.24 L 772.4 753.48" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@1" LinkObjectIDznd="139@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.4 715.24 L 772.4 753.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv220" d="M 1043.6 193.54 L 1043.6 131.39" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@1" LinkObjectIDznd="173@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.6 193.54 L 1043.6 131.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv220" d="M 1089.9 179.44 L 1043.6 179.44" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1089.9 179.44 L 1043.6 179.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv220" d="M 1002.01 154.92 L 1043.6 154.92" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.01 154.92 L 1043.6 154.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv220" d="M 852.79 287.22 L 791.93 287.22" stroke-width="1" zvalue="224"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@1" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 852.79 287.22 L 791.93 287.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv220" d="M 881.46 287.06 L 944.1 287.06" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="188@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 881.46 287.06 L 944.1 287.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv220" d="M 787.93 355.88 L 848.79 355.88" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="208@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 787.93 355.88 L 848.79 355.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv220" d="M 877.46 355.06 L 940.1 355.06" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="207@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 877.46 355.06 L 940.1 355.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv220" d="M 1424.79 287.22 L 1363.93 287.22" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="196@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.79 287.22 L 1363.93 287.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv220" d="M 1453.46 287.06 L 1516.1 287.06" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="197@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.46 287.06 L 1516.1 287.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv220" d="M 1453.46 355.06 L 1516.1 355.06" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.46 355.06 L 1516.1 355.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv220" d="M 1424.79 355.22 L 1363.93 355.22" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@1" LinkObjectIDznd="201@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.79 355.22 L 1363.93 355.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv220" d="M 823.94 250.59 L 823.94 287.22" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="209" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.94 250.59 L 823.94 287.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv220" d="M 923.94 250.59 L 923.94 287.06" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="210" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.94 250.59 L 923.94 287.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv220" d="M 820.05 403.81 L 820.05 355.88" stroke-width="1" zvalue="234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="211" MaxPinNum="2"/>
   </metadata>
  <path d="M 820.05 403.81 L 820.05 355.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv220" d="M 920.05 404.92 L 920.05 355.06" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 920.05 404.92 L 920.05 355.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv220" d="M 1396.05 404.92 L 1396.05 355.22" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.05 404.92 L 1396.05 355.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv220" d="M 1496.05 404.92 L 1496.05 355.06" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 1496.05 404.92 L 1496.05 355.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv220" d="M 1495.94 250.59 L 1495.94 287.06" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="214" MaxPinNum="2"/>
   </metadata>
  <path d="M 1495.94 250.59 L 1495.94 287.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv220" d="M 1395.94 250.59 L 1395.94 287.22" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395.94 250.59 L 1395.94 287.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv220" d="M 967.93 287.09 L 1340.1 287.09" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="196@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 967.93 287.09 L 1340.1 287.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv220" d="M 963.93 355.09 L 1340.1 355.09" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="201@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 963.93 355.09 L 1340.1 355.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv220" d="M 768.1 287.85 L 604.71 287.85 L 604.71 463.99" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@1" LinkObjectIDznd="235@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.1 287.85 L 604.71 287.85 L 604.71 463.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv220" d="M 604.68 487.82 L 604.68 544.6" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.68 487.82 L 604.68 544.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv220" d="M 543.94 514.15 L 604.68 514.15" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="237" MaxPinNum="2"/>
   </metadata>
  <path d="M 543.94 514.15 L 604.68 514.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv220" d="M 544.68 441.88 L 604.71 441.88" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@0" LinkObjectIDznd="236" MaxPinNum="2"/>
   </metadata>
  <path d="M 544.68 441.88 L 604.71 441.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv220" d="M 764.1 355.85 L 604.71 355.85" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="236" MaxPinNum="2"/>
   </metadata>
  <path d="M 764.1 355.85 L 604.71 355.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv220" d="M 545.74 396.7 L 604.71 396.7" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@0" LinkObjectIDznd="236" MaxPinNum="2"/>
   </metadata>
  <path d="M 545.74 396.7 L 604.71 396.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv220" d="M 657.51 396.7 L 604.71 396.7" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="236" MaxPinNum="2"/>
   </metadata>
  <path d="M 657.51 396.7 L 604.71 396.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv10" d="M 604.64 609.47 L 604.71 647.99" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="245@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.64 609.47 L 604.71 647.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv10" d="M 604.68 671.82 L 604.68 702.79" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@0" LinkObjectIDznd="246@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.68 671.82 L 604.68 702.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 604.85 731.46 L 604.85 877.35" stroke-width="1" zvalue="269"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.85 731.46 L 604.85 877.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 543.94 762.15 L 604.85 762.15" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="250" MaxPinNum="2"/>
   </metadata>
  <path d="M 543.94 762.15 L 604.85 762.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv10" d="M 453.72 887.7 L 453.72 813.78 L 604.85 813.78" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@0" LinkObjectIDznd="250" MaxPinNum="2"/>
   </metadata>
  <path d="M 453.72 887.7 L 453.72 813.78 L 604.85 813.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv10" d="M 527.08 894.46 L 527.08 874.97" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 527.08 894.46 L 527.08 874.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv10" d="M 527 849.08 L 527 813.78" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@1" LinkObjectIDznd="272" MaxPinNum="2"/>
   </metadata>
  <path d="M 527 849.08 L 527 813.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv10" d="M 684.58 904.91 L 684.58 877.97" stroke-width="1" zvalue="284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 684.58 904.91 L 684.58 877.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="kv10" d="M 604.85 814.89 L 684.5 814.89 L 684.5 852.08" stroke-width="1" zvalue="285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.85 814.89 L 684.5 814.89 L 684.5 852.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv10" d="M 773.19 774.72 L 773.19 835.91" stroke-width="1" zvalue="286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@1" LinkObjectIDznd="265@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.19 774.72 L 773.19 835.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv10" d="M 772.43 691.4 L 772.43 596.93" stroke-width="1" zvalue="287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="171@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.43 691.4 L 772.43 596.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv10" d="M 772.51 571.03 L 772.51 542.54" stroke-width="1" zvalue="288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="252@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.51 571.03 L 772.51 542.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv10" d="M 604.67 628.22 L 772.43 628.22" stroke-width="1" zvalue="289"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247" LinkObjectIDznd="278" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.67 628.22 L 772.43 628.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv220" d="M 1124.66 563.3 L 1021.89 563.3 L 1021.89 599.11" stroke-width="1" zvalue="293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@2" LinkObjectIDznd="340@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.66 563.3 L 1021.89 563.3 L 1021.89 599.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="335">
   <path class="kv10" d="M 1292.4 715.24 L 1292.4 753.48" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@1" LinkObjectIDznd="334@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.4 715.24 L 1292.4 753.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="kv220" d="M 1124.68 487.82 L 1124.68 544.6" stroke-width="1" zvalue="315"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="338@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.68 487.82 L 1124.68 544.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv220" d="M 1063.94 514.15 L 1124.68 514.15" stroke-width="1" zvalue="316"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@0" LinkObjectIDznd="327" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.94 514.15 L 1124.68 514.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="323">
   <path class="kv220" d="M 1065.74 396.7 L 1124.71 396.7" stroke-width="1" zvalue="319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@0" LinkObjectIDznd="398" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.74 396.7 L 1124.71 396.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="321">
   <path class="kv220" d="M 1177.51 396.7 L 1124.71 396.7" stroke-width="1" zvalue="321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@0" LinkObjectIDznd="323" MaxPinNum="2"/>
   </metadata>
  <path d="M 1177.51 396.7 L 1124.71 396.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv10" d="M 1124.64 609.47 L 1124.71 647.99" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@1" LinkObjectIDznd="320@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.64 609.47 L 1124.71 647.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv10" d="M 1124.68 671.82 L 1124.68 702.79" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="319@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.68 671.82 L 1124.68 702.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv10" d="M 1124.85 731.46 L 1124.85 877.35" stroke-width="1" zvalue="329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="319@0" LinkObjectIDznd="337@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.85 731.46 L 1124.85 877.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv10" d="M 1063.94 762.15 L 1124.85 762.15" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316@0" LinkObjectIDznd="315" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.94 762.15 L 1124.85 762.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="308">
   <path class="kv10" d="M 973.72 887.7 L 973.72 813.78 L 1124.85 813.78" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309@0" LinkObjectIDznd="315" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.72 887.7 L 973.72 813.78 L 1124.85 813.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv10" d="M 1047.08 894.46 L 1047.08 874.97" stroke-width="1" zvalue="340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="332@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1047.08 894.46 L 1047.08 874.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv10" d="M 1047 849.08 L 1047 813.78" stroke-width="1" zvalue="341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@1" LinkObjectIDznd="308" MaxPinNum="2"/>
   </metadata>
  <path d="M 1047 849.08 L 1047 813.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv10" d="M 1204.58 904.91 L 1204.58 877.97" stroke-width="1" zvalue="342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="311@0" LinkObjectIDznd="333@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204.58 904.91 L 1204.58 877.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="kv10" d="M 1124.85 814.89 L 1204.5 814.89 L 1204.5 852.08" stroke-width="1" zvalue="343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315" LinkObjectIDznd="333@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.85 814.89 L 1204.5 814.89 L 1204.5 852.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="kv10" d="M 1293.19 774.72 L 1293.19 835.91" stroke-width="1" zvalue="344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="334@1" LinkObjectIDznd="310@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.19 774.72 L 1293.19 835.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv10" d="M 1292.43 691.4 L 1292.43 596.93" stroke-width="1" zvalue="345"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@0" LinkObjectIDznd="331@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.43 691.4 L 1292.43 596.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv10" d="M 1292.51 571.03 L 1292.51 542.54" stroke-width="1" zvalue="346"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331@0" LinkObjectIDznd="313@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.51 571.03 L 1292.51 542.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv10" d="M 1124.67 628.22 L 1292.43 628.22" stroke-width="1" zvalue="347"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318" LinkObjectIDznd="302" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.67 628.22 L 1292.43 628.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="396">
   <path class="kv220" d="M 1680.66 563.3 L 1577.89 563.3 L 1577.89 599.11" stroke-width="1" zvalue="351"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="395@2" LinkObjectIDznd="397@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1680.66 563.3 L 1577.89 563.3 L 1577.89 599.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="kv10" d="M 1848.4 715.24 L 1848.4 753.48" stroke-width="1" zvalue="358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="393@1" LinkObjectIDznd="391@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1848.4 715.24 L 1848.4 753.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="384">
   <path class="kv220" d="M 1680.68 487.82 L 1680.68 544.6" stroke-width="1" zvalue="373"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="387@0" LinkObjectIDznd="395@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1680.68 487.82 L 1680.68 544.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="383">
   <path class="kv220" d="M 1619.94 514.15 L 1680.68 514.15" stroke-width="1" zvalue="374"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="386@0" LinkObjectIDznd="384" MaxPinNum="2"/>
   </metadata>
  <path d="M 1619.94 514.15 L 1680.68 514.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="382">
   <path class="kv220" d="M 1620.68 441.88 L 1680.71 441.88" stroke-width="1" zvalue="375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="385@0" LinkObjectIDznd="400" MaxPinNum="2"/>
   </metadata>
  <path d="M 1620.68 441.88 L 1680.71 441.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv220" d="M 1621.74 396.7 L 1680.71 396.7" stroke-width="1" zvalue="377"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="381@0" LinkObjectIDznd="400" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621.74 396.7 L 1680.71 396.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="378">
   <path class="kv220" d="M 1733.51 396.7 L 1680.71 396.7" stroke-width="1" zvalue="379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="379@0" LinkObjectIDznd="380" MaxPinNum="2"/>
   </metadata>
  <path d="M 1733.51 396.7 L 1680.71 396.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="375">
   <path class="kv10" d="M 1680.64 609.47 L 1680.71 647.99" stroke-width="1" zvalue="384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="395@1" LinkObjectIDznd="377@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1680.64 609.47 L 1680.71 647.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv10" d="M 1680.68 671.82 L 1680.68 702.79" stroke-width="1" zvalue="385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="377@0" LinkObjectIDznd="376@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1680.68 671.82 L 1680.68 702.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="372">
   <path class="kv10" d="M 1680.85 731.46 L 1680.85 877.35" stroke-width="1" zvalue="387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="376@0" LinkObjectIDznd="394@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1680.85 731.46 L 1680.85 877.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="371">
   <path class="kv10" d="M 1619.94 762.15 L 1680.85 762.15" stroke-width="1" zvalue="389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="373@0" LinkObjectIDznd="372" MaxPinNum="2"/>
   </metadata>
  <path d="M 1619.94 762.15 L 1680.85 762.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="365">
   <path class="kv10" d="M 1529.72 887.7 L 1529.72 813.78 L 1680.85 813.78" stroke-width="1" zvalue="396"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="366@0" LinkObjectIDznd="372" MaxPinNum="2"/>
   </metadata>
  <path d="M 1529.72 887.7 L 1529.72 813.78 L 1680.85 813.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="364">
   <path class="kv10" d="M 1603.08 894.46 L 1603.08 874.97" stroke-width="1" zvalue="398"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@0" LinkObjectIDznd="389@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1603.08 894.46 L 1603.08 874.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="363">
   <path class="kv10" d="M 1603 849.08 L 1603 813.78" stroke-width="1" zvalue="399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@1" LinkObjectIDznd="365" MaxPinNum="2"/>
   </metadata>
  <path d="M 1603 849.08 L 1603 813.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="362">
   <path class="kv10" d="M 1760.58 904.91 L 1760.58 877.97" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@0" LinkObjectIDznd="390@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1760.58 904.91 L 1760.58 877.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="361">
   <path class="kv10" d="M 1680.85 814.89 L 1760.5 814.89 L 1760.5 852.08" stroke-width="1" zvalue="401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="372" LinkObjectIDznd="390@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1680.85 814.89 L 1760.5 814.89 L 1760.5 852.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="360">
   <path class="kv10" d="M 1849.19 774.72 L 1849.19 835.91" stroke-width="1" zvalue="402"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391@1" LinkObjectIDznd="367@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1849.19 774.72 L 1849.19 835.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="359">
   <path class="kv10" d="M 1848.43 691.4 L 1848.43 596.93" stroke-width="1" zvalue="403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="393@0" LinkObjectIDznd="388@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1848.43 691.4 L 1848.43 596.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="358">
   <path class="kv10" d="M 1848.51 571.03 L 1848.51 542.54" stroke-width="1" zvalue="404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@0" LinkObjectIDznd="370@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1848.51 571.03 L 1848.51 542.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="357">
   <path class="kv10" d="M 1680.67 628.22 L 1848.43 628.22" stroke-width="1" zvalue="405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375" LinkObjectIDznd="359" MaxPinNum="2"/>
   </metadata>
  <path d="M 1680.67 628.22 L 1848.43 628.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="398">
   <path class="kv220" d="M 1124.71 463.99 L 1124.71 355.09" stroke-width="1" zvalue="406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@1" LinkObjectIDznd="226" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.71 463.99 L 1124.71 355.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="399">
   <path class="kv220" d="M 1064.68 441.88 L 1124.71 441.88" stroke-width="1" zvalue="407"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="398" MaxPinNum="2"/>
   </metadata>
  <path d="M 1064.68 441.88 L 1124.71 441.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="400">
   <path class="kv220" d="M 1539.93 287.09 L 1680.71 287.09 L 1680.71 463.99" stroke-width="1" zvalue="408"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="387@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1539.93 287.09 L 1680.71 287.09 L 1680.71 463.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="401">
   <path class="kv220" d="M 1539.93 355.09 L 1680.71 355.09" stroke-width="1" zvalue="409"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="400" MaxPinNum="2"/>
   </metadata>
  <path d="M 1539.93 355.09 L 1680.71 355.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="402">
   <path class="kv220" d="M 1043.57 217.37 L 1043.57 287.09" stroke-width="1" zvalue="410"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.57 217.37 L 1043.57 287.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="403">
   <path class="kv220" d="M 1089.16 235.7 L 1043.57 235.7" stroke-width="1" zvalue="411"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="402" MaxPinNum="2"/>
   </metadata>
  <path d="M 1089.16 235.7 L 1043.57 235.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="163">
   <g id="1630">
    <use class="kv220" height="50" transform="rotate(0,604.638,576.966) scale(1.28735,1.31862) translate(-130.651,-131.448)" width="30" x="585.33" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="544" zvalue="74"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430676996" ObjectName="220"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="kv10" height="50" transform="rotate(0,604.638,576.966) scale(1.28735,1.31862) translate(-130.651,-131.448)" width="30" x="585.33" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="544" zvalue="74"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430742532" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447609348" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447609348"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,604.638,576.966) scale(1.28735,1.31862) translate(-130.651,-131.448)" width="30" x="585.33" y="544"/></g>
  <g id="338">
   <g id="3380">
    <use class="kv220" height="50" transform="rotate(0,1124.64,576.966) scale(1.28735,1.31862) translate(-246.721,-131.448)" width="30" x="1105.33" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="544" zvalue="294"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430808068" ObjectName="220"/>
    </metadata>
   </g>
   <g id="3381">
    <use class="kv10" height="50" transform="rotate(0,1124.64,576.966) scale(1.28735,1.31862) translate(-246.721,-131.448)" width="30" x="1105.33" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="544" zvalue="294"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430873604" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447674884" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399447674884"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1124.64,576.966) scale(1.28735,1.31862) translate(-246.721,-131.448)" width="30" x="1105.33" y="544"/></g>
  <g id="395">
   <g id="3950">
    <use class="kv220" height="50" transform="rotate(0,1680.64,576.966) scale(1.28735,1.31862) translate(-370.826,-131.448)" width="30" x="1661.33" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="544" zvalue="352"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430939140" ObjectName="220"/>
    </metadata>
   </g>
   <g id="3951">
    <use class="kv10" height="50" transform="rotate(0,1680.64,576.966) scale(1.28735,1.31862) translate(-370.826,-131.448)" width="30" x="1661.33" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="544" zvalue="352"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431004676" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447740420" ObjectName="#3主变"/>
   <cge:TPSR_Ref TObjectID="6755399447740420"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1680.64,576.966) scale(1.28735,1.31862) translate(-370.826,-131.448)" width="30" x="1661.33" y="544"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,604.847,897.944) scale(1.3963,1.3963) translate(-165.723,-248.91)" width="30" x="583.9024699699792" xlink:href="#Generator:发电机_0" y="877.0000000000001" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449740472326" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449740472326"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,604.847,897.944) scale(1.3963,1.3963) translate(-165.723,-248.91)" width="30" x="583.9024699699792" y="877.0000000000001"/></g>
  <g id="337">
   <use class="kv10" height="30" transform="rotate(0,1124.85,897.944) scale(1.3963,1.3963) translate(-313.309,-248.91)" width="30" x="1103.902469969979" xlink:href="#Generator:发电机_0" y="877.0000000000001" zvalue="296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744928773" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449744928773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1124.85,897.944) scale(1.3963,1.3963) translate(-313.309,-248.91)" width="30" x="1103.902469969979" y="877.0000000000001"/></g>
  <g id="394">
   <use class="kv10" height="30" transform="rotate(0,1680.85,897.944) scale(1.3963,1.3963) translate(-471.113,-248.91)" width="30" x="1659.902469969979" xlink:href="#Generator:发电机_0" y="877.0000000000001" zvalue="354"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449746370565" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449746370565"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1680.85,897.944) scale(1.3963,1.3963) translate(-471.113,-248.91)" width="30" x="1659.902469969979" y="877.0000000000001"/></g>
 </g>
 <g id="BreakerClass">
  <g id="139">
   <use class="kv10" height="20" transform="rotate(0,773.111,764.111) scale(1.22222,1.11111) translate(-139.455,-75.3)" width="10" x="767" xlink:href="#Breaker:开关_0" y="753" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499079173" ObjectName="#1厂用变012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499079173"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,773.111,764.111) scale(1.22222,1.11111) translate(-139.455,-75.3)" width="10" x="767" y="753"/></g>
  <g id="184">
   <use class="kv220" height="20" transform="rotate(90,867.111,287.111) scale(1.66667,1.5) translate(-343.511,-90.7037)" width="10" x="858.7777777777777" xlink:href="#Breaker:开关_0" y="272.1111111111111" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499144709" ObjectName="#1主变220kV侧254断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499144709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,867.111,287.111) scale(1.66667,1.5) translate(-343.511,-90.7037)" width="10" x="858.7777777777777" y="272.1111111111111"/></g>
  <g id="198">
   <use class="kv220" height="20" transform="rotate(90,1439.11,287.111) scale(1.66667,1.5) translate(-572.311,-90.7037)" width="10" x="1430.777777777778" xlink:href="#Breaker:开关_0" y="272.1111111111111" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499210245" ObjectName="220kV那盈线251断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499210245"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1439.11,287.111) scale(1.66667,1.5) translate(-572.311,-90.7037)" width="10" x="1430.777777777778" y="272.1111111111111"/></g>
  <g id="203">
   <use class="kv220" height="20" transform="rotate(90,1439.11,355.111) scale(1.66667,1.5) translate(-572.311,-113.37)" width="10" x="1430.777777777778" xlink:href="#Breaker:开关_0" y="340.1111111111111" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499275781" ObjectName="#3主变220kV侧252断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499275781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1439.11,355.111) scale(1.66667,1.5) translate(-572.311,-113.37)" width="10" x="1430.777777777778" y="340.1111111111111"/></g>
  <g id="208">
   <use class="kv220" height="20" transform="rotate(90,863.111,355.111) scale(1.66667,1.5) translate(-341.911,-113.37)" width="10" x="854.7777777777777" xlink:href="#Breaker:开关_0" y="340.1111111111111" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499341317" ObjectName="#2主变220kV侧253断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499341317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,863.111,355.111) scale(1.66667,1.5) translate(-341.911,-113.37)" width="10" x="854.7777777777777" y="340.1111111111111"/></g>
  <g id="246">
   <use class="kv10" height="20" transform="rotate(180,604.791,717.111) scale(1.66667,1.5) translate(-238.583,-234.037)" width="10" x="596.4580255255346" xlink:href="#Breaker:开关_0" y="702.1111111111111" zvalue="264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499406853" ObjectName="#1主变10kV侧011断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499406853"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,604.791,717.111) scale(1.66667,1.5) translate(-238.583,-234.037)" width="10" x="596.4580255255346" y="702.1111111111111"/></g>
  <g id="334">
   <use class="kv10" height="20" transform="rotate(0,1293.11,764.111) scale(1.22222,1.11111) translate(-234,-75.3)" width="10" x="1287" xlink:href="#Breaker:开关_0" y="753" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499734533" ObjectName="#1隔离变022断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499734533"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1293.11,764.111) scale(1.22222,1.11111) translate(-234,-75.3)" width="10" x="1287" y="753"/></g>
  <g id="319">
   <use class="kv10" height="20" transform="rotate(180,1124.79,717.111) scale(1.66667,1.5) translate(-446.583,-234.037)" width="10" x="1116.458025525535" xlink:href="#Breaker:开关_0" y="702.1111111111111" zvalue="324"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499472390" ObjectName="#2主变10kV侧021断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499472390"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1124.79,717.111) scale(1.66667,1.5) translate(-446.583,-234.037)" width="10" x="1116.458025525535" y="702.1111111111111"/></g>
  <g id="391">
   <use class="kv10" height="20" transform="rotate(0,1849.11,764.111) scale(1.22222,1.11111) translate(-335.091,-75.3)" width="10" x="1843" xlink:href="#Breaker:开关_0" y="753" zvalue="359"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499800069" ObjectName="#2厂用变032断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499800069"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1849.11,764.111) scale(1.22222,1.11111) translate(-335.091,-75.3)" width="10" x="1843" y="753"/></g>
  <g id="376">
   <use class="kv10" height="20" transform="rotate(180,1680.79,717.111) scale(1.66667,1.5) translate(-668.983,-234.037)" width="10" x="1672.458025525535" xlink:href="#Breaker:开关_0" y="702.1111111111111" zvalue="382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924499603461" ObjectName="#3主变10kV侧031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924499603461"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1680.79,717.111) scale(1.66667,1.5) translate(-668.983,-234.037)" width="10" x="1672.458025525535" y="702.1111111111111"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="180">
   <use class="kv220" height="15" transform="rotate(0,982.889,150.815) scale(2.34568,2.34568) translate(-551.757,-76.4275)" width="18" x="961.7777777777778" xlink:href="#Accessory:PT4_0" y="133.2222222222223" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449741062149" ObjectName="220kV那盈线PT"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,982.889,150.815) scale(2.34568,2.34568) translate(-551.757,-76.4275)" width="18" x="961.7777777777778" y="133.2222222222223"/></g>
  <g id="241">
   <use class="kv220" height="26" transform="rotate(90,532,396.667) scale(1.11111,1.11111) translate(-52.5333,-38.2222)" width="12" x="525.3333333333334" xlink:href="#Accessory:避雷器_0" y="382.2222222222223" zvalue="257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743028230" ObjectName="#1主变220kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,532,396.667) scale(1.11111,1.11111) translate(-52.5333,-38.2222)" width="12" x="525.3333333333334" y="382.2222222222223"/></g>
  <g id="243">
   <use class="kv220" height="18" transform="rotate(270,670.444,396.704) scale(1.48148,1.48148) translate(-213.561,-124.595)" width="18" x="657.1111111111111" xlink:href="#Accessory:四卷PT_0" y="383.3703703703703" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743093766" ObjectName="#1主变220kV侧PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,670.444,396.704) scale(1.48148,1.48148) translate(-213.561,-124.595)" width="18" x="657.1111111111111" y="383.3703703703703"/></g>
  <g id="252">
   <use class="kv10" height="40" transform="rotate(0,772.513,521.333) scale(1.11111,-1.11111) translate(-75.029,-988.311)" width="40" x="750.2903197649748" xlink:href="#Accessory:线路PT三绕组_0" y="499.1111111111111" zvalue="271"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743355910" ObjectName="#1厂用变PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,772.513,521.333) scale(1.11111,-1.11111) translate(-75.029,-988.311)" width="40" x="750.2903197649748" y="499.1111111111111"/></g>
  <g id="253">
   <use class="kv10" height="48" transform="rotate(0,524.029,920.889) scale(1.11111,-1.11111) translate(-49.9029,-1747.02)" width="45" x="499.02851166836" xlink:href="#Accessory:母线电压互感器11_0" y="894.2222222222223" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743421446" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,524.029,920.889) scale(1.11111,-1.11111) translate(-49.9029,-1747.02)" width="45" x="499.02851166836" y="894.2222222222223"/></g>
  <g id="254">
   <use class="kv10" height="48" transform="rotate(0,681.529,931.333) scale(1.11111,-1.11111) translate(-65.6529,-1766.87)" width="45" x="656.52851166836" xlink:href="#Accessory:母线电压互感器11_0" y="904.6666666666667" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743486982" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,681.529,931.333) scale(1.11111,-1.11111) translate(-65.6529,-1766.87)" width="45" x="656.52851166836" y="904.6666666666667"/></g>
  <g id="270">
   <use class="kv10" height="20" transform="rotate(0,453.639,901) scale(1.38889,1.38889) translate(-124.102,-248.391)" width="15" x="443.2222222222221" xlink:href="#Accessory:PT6_0" y="887.1111111111111" zvalue="280"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743618054" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,453.639,901) scale(1.38889,1.38889) translate(-124.102,-248.391)" width="15" x="443.2222222222221" y="887.1111111111111"/></g>
  <g id="324">
   <use class="kv220" height="26" transform="rotate(90,1052,396.667) scale(1.11111,1.11111) translate(-104.533,-38.2222)" width="12" x="1045.333333333333" xlink:href="#Accessory:避雷器_0" y="382.2222222222223" zvalue="318"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744273413" ObjectName="#2主变220kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1052,396.667) scale(1.11111,1.11111) translate(-104.533,-38.2222)" width="12" x="1045.333333333333" y="382.2222222222223"/></g>
  <g id="322">
   <use class="kv220" height="18" transform="rotate(270,1190.44,396.704) scale(1.48148,1.48148) translate(-382.561,-124.595)" width="18" x="1177.111111111111" xlink:href="#Accessory:四卷PT_0" y="383.3703703703703" zvalue="320"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449744207877" ObjectName="#2主变220kV侧PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1190.44,396.704) scale(1.48148,1.48148) translate(-382.561,-124.595)" width="18" x="1177.111111111111" y="383.3703703703703"/></g>
  <g id="313">
   <use class="kv10" height="40" transform="rotate(0,1292.51,521.333) scale(1.11111,-1.11111) translate(-127.029,-988.311)" width="40" x="1270.290319764975" xlink:href="#Accessory:线路PT三绕组_0" y="499.1111111111111" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743945733" ObjectName="#1隔离变PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1292.51,521.333) scale(1.11111,-1.11111) translate(-127.029,-988.311)" width="40" x="1270.290319764975" y="499.1111111111111"/></g>
  <g id="312">
   <use class="kv10" height="48" transform="rotate(0,1044.03,920.889) scale(1.11111,-1.11111) translate(-101.903,-1747.02)" width="45" x="1019.02851166836" xlink:href="#Accessory:母线电压互感器11_0" y="894.2222222222223" zvalue="333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743880197" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1044.03,920.889) scale(1.11111,-1.11111) translate(-101.903,-1747.02)" width="45" x="1019.02851166836" y="894.2222222222223"/></g>
  <g id="311">
   <use class="kv10" height="48" transform="rotate(0,1201.53,931.333) scale(1.11111,-1.11111) translate(-117.653,-1766.87)" width="45" x="1176.52851166836" xlink:href="#Accessory:母线电压互感器11_0" y="904.6666666666667" zvalue="334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743814661" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1201.53,931.333) scale(1.11111,-1.11111) translate(-117.653,-1766.87)" width="45" x="1176.52851166836" y="904.6666666666667"/></g>
  <g id="309">
   <use class="kv10" height="20" transform="rotate(0,973.639,901) scale(1.38889,1.38889) translate(-269.702,-248.391)" width="15" x="963.2222222222221" xlink:href="#Accessory:PT6_0" y="887.1111111111111" zvalue="336"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743683589" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,973.639,901) scale(1.38889,1.38889) translate(-269.702,-248.391)" width="15" x="963.2222222222221" y="887.1111111111111"/></g>
  <g id="381">
   <use class="kv220" height="26" transform="rotate(90,1608,396.667) scale(1.11111,1.11111) translate(-160.133,-38.2222)" width="12" x="1601.333333333333" xlink:href="#Accessory:避雷器_0" y="382.2222222222223" zvalue="376"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745715205" ObjectName="#3主变220kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1608,396.667) scale(1.11111,1.11111) translate(-160.133,-38.2222)" width="12" x="1601.333333333333" y="382.2222222222223"/></g>
  <g id="379">
   <use class="kv220" height="18" transform="rotate(270,1746.44,396.704) scale(1.48148,1.48148) translate(-563.261,-124.595)" width="18" x="1733.111111111111" xlink:href="#Accessory:四卷PT_0" y="383.3703703703703" zvalue="378"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745649669" ObjectName="#3主变220kV侧PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1746.44,396.704) scale(1.48148,1.48148) translate(-563.261,-124.595)" width="18" x="1733.111111111111" y="383.3703703703703"/></g>
  <g id="370">
   <use class="kv10" height="40" transform="rotate(0,1848.51,521.333) scale(1.11111,-1.11111) translate(-182.629,-988.311)" width="40" x="1826.290319764975" xlink:href="#Accessory:线路PT三绕组_0" y="499.1111111111111" zvalue="390"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745387525" ObjectName="#2厂用变PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1848.51,521.333) scale(1.11111,-1.11111) translate(-182.629,-988.311)" width="40" x="1826.290319764975" y="499.1111111111111"/></g>
  <g id="369">
   <use class="kv10" height="48" transform="rotate(0,1600.03,920.889) scale(1.11111,-1.11111) translate(-157.503,-1747.02)" width="45" x="1575.02851166836" xlink:href="#Accessory:母线电压互感器11_0" y="894.2222222222223" zvalue="391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745321989" ObjectName="#3发电机PT1"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1600.03,920.889) scale(1.11111,-1.11111) translate(-157.503,-1747.02)" width="45" x="1575.02851166836" y="894.2222222222223"/></g>
  <g id="368">
   <use class="kv10" height="48" transform="rotate(0,1757.53,931.333) scale(1.11111,-1.11111) translate(-173.253,-1766.87)" width="45" x="1732.52851166836" xlink:href="#Accessory:母线电压互感器11_0" y="904.6666666666667" zvalue="392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745256453" ObjectName="#3发电机PT2"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1757.53,931.333) scale(1.11111,-1.11111) translate(-173.253,-1766.87)" width="45" x="1732.52851166836" y="904.6666666666667"/></g>
  <g id="366">
   <use class="kv10" height="20" transform="rotate(0,1529.64,901) scale(1.38889,1.38889) translate(-425.382,-248.391)" width="15" x="1519.222222222222" xlink:href="#Accessory:PT6_0" y="887.1111111111111" zvalue="394"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745125381" ObjectName="#3发电机励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1529.64,901) scale(1.38889,1.38889) translate(-425.382,-248.391)" width="15" x="1519.222222222222" y="887.1111111111111"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="265">
   <use class="kv10" height="30" transform="rotate(0,773.054,857.778) scale(1.50794,1.51852) translate(-253.286,-285.122)" width="28" x="751.9429434376716" xlink:href="#EnergyConsumer:站用变DY接地_0" y="835" zvalue="279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449743552518" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,773.054,857.778) scale(1.50794,1.51852) translate(-253.286,-285.122)" width="28" x="751.9429434376716" y="835"/></g>
  <g id="310">
   <use class="kv10" height="30" transform="rotate(0,1293.05,857.778) scale(1.50794,1.51852) translate(-428.444,-285.122)" width="28" x="1271.942943437672" xlink:href="#EnergyConsumer:站用变DY接地_0" y="835" zvalue="335"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449745190917" ObjectName="#1隔离变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1293.05,857.778) scale(1.50794,1.51852) translate(-428.444,-285.122)" width="28" x="1271.942943437672" y="835"/></g>
  <g id="367">
   <use class="kv10" height="30" transform="rotate(0,1849.05,857.778) scale(1.50794,1.51852) translate(-615.728,-285.122)" width="28" x="1827.942943437672" xlink:href="#EnergyConsumer:站用变DY接地_0" y="835" zvalue="393"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449746960389" ObjectName="#2厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1849.05,857.778) scale(1.50794,1.51852) translate(-615.728,-285.122)" width="28" x="1827.942943437672" y="835"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="25" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124900765701" ObjectName="F"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124900831237" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124900634629" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124900700165" ObjectName="F"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124903649285" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,402.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="407.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124903714821" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124900634629" ObjectName="F"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124900634629" ObjectName="F"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="49" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1138.6,84.5501) scale(1,1) translate(0,1.74407e-13)" writing-mode="lr" x="1138.13" xml:space="preserve" y="89.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124887724037" ObjectName="P"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="77" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1138.6,105.153) scale(1,1) translate(0,2.22441e-13)" writing-mode="lr" x="1138.13" xml:space="preserve" y="109.81" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124887789573" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="78" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1138.6,125.755) scale(1,1) translate(0,2.70476e-13)" writing-mode="lr" x="1138.13" xml:space="preserve" y="130.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124887855109" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="79" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,675.888,456.264) scale(1,1) translate(0,-9.84768e-14)" writing-mode="lr" x="675.34" xml:space="preserve" y="460.97" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124885692421" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="80" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1190.89,461.757) scale(1,1) translate(-2.52662e-13,-1.00142e-13)" writing-mode="lr" x="1190.34" xml:space="preserve" y="466.43" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124894277637" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="81" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1744.39,449.01) scale(1,1) translate(0,-1.94178e-13)" writing-mode="lr" x="1743.84" xml:space="preserve" y="453.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124898603013" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="82" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,675.888,483.25) scale(1,1) translate(0,-1.04469e-13)" writing-mode="lr" x="675.34" xml:space="preserve" y="487.95" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124885757957" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="83" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1190.89,484.5) scale(1,1) translate(-2.52662e-13,-1.05192e-13)" writing-mode="lr" x="1190.34" xml:space="preserve" y="489.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124894343173" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="84" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1744.39,473.875) scale(1,1) translate(0,-1.0261e-13)" writing-mode="lr" x="1743.84" xml:space="preserve" y="478.56" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124898668549" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="85" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,669.638,696.891) scale(1,1) translate(0,-6.09007e-13)" writing-mode="lr" x="669.09" xml:space="preserve" y="701.5700000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124885823493" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="86" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1190.89,694.746) scale(1,1) translate(-2.52662e-13,0)" writing-mode="lr" x="1190.34" xml:space="preserve" y="699.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124894408709" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="87" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1745.64,691.443) scale(1,1) translate(0,-4.52592e-13)" writing-mode="lr" x="1745.09" xml:space="preserve" y="696.13" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124898734085" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="88" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,669.638,720.591) scale(1,1) translate(0,-1.57514e-13)" writing-mode="lr" x="669.09" xml:space="preserve" y="725.27" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124885889029" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="89" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1190.89,721.841) scale(1,1) translate(-2.52662e-13,3.14871e-13)" writing-mode="lr" x="1190.34" xml:space="preserve" y="726.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124894474245" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="90" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1745.64,716.841) scale(1,1) translate(0,-1.56503e-13)" writing-mode="lr" x="1745.09" xml:space="preserve" y="721.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124898799621" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="91" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,675.888,510.236) scale(1,1) translate(0,0)" writing-mode="lr" x="675.34" xml:space="preserve" y="514.9400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124885954565" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="92" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1190.89,507.243) scale(1,1) translate(-2.52662e-13,-1.10242e-13)" writing-mode="lr" x="1190.34" xml:space="preserve" y="511.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124894539781" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="94" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1744.39,498.74) scale(1,1) translate(0,5.40656e-14)" writing-mode="lr" x="1743.84" xml:space="preserve" y="503.43" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124898865157" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,669.638,744.291) scale(1,1) translate(0,-6.51107e-13)" writing-mode="lr" x="669.09" xml:space="preserve" y="748.97" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124886282245" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="96" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1190.89,748.935) scale(1,1) translate(-2.52662e-13,0)" writing-mode="lr" x="1190.34" xml:space="preserve" y="753.64" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124894867461" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="97" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1745.64,742.238) scale(1,1) translate(0,-4.86428e-13)" writing-mode="lr" x="1745.09" xml:space="preserve" y="746.9299999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124899192837" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="99" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,603.597,963.67) scale(1,1) translate(0,-1.26684e-12)" writing-mode="lr" x="603.04" xml:space="preserve" y="968.37" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124885037061" ObjectName="P"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1123.6,963.67) scale(1,1) translate(0,-1.26684e-12)" writing-mode="lr" x="1123.04" xml:space="preserve" y="968.37" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124893622277" ObjectName="P"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="101" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1679.6,963.67) scale(1,1) translate(0,-1.26684e-12)" writing-mode="lr" x="1679.04" xml:space="preserve" y="968.37" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124897947653" ObjectName="P"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="102" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,603.597,990.694) scale(1,1) translate(0,-1.30284e-12)" writing-mode="lr" x="603.04" xml:space="preserve" y="995.4" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124885102597" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="103" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1123.6,990.694) scale(1,1) translate(0,-1.30284e-12)" writing-mode="lr" x="1123.04" xml:space="preserve" y="995.4" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124893687813" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1679.6,990.694) scale(1,1) translate(0,-1.30284e-12)" writing-mode="lr" x="1679.04" xml:space="preserve" y="995.4" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124898013189" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="105" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,603.597,1017.72) scale(1,1) translate(0,-1.33884e-12)" writing-mode="lr" x="603.04" xml:space="preserve" y="1022.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124885168133" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1123.6,1017.72) scale(1,1) translate(0,-1.33884e-12)" writing-mode="lr" x="1123.04" xml:space="preserve" y="1022.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124893753349" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="107" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1679.6,1017.72) scale(1,1) translate(0,-1.33884e-12)" writing-mode="lr" x="1679.04" xml:space="preserve" y="1022.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124898078725" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="22">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="434"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374887747587" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="435"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950411190276" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>