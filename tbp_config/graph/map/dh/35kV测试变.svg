<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1200" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT1_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9.15" xlink:href="#terminal" y="1.15"/>
   <ellipse cx="9.23" cy="6.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.12" cy="11.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.9" cy="11.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <path d="M 6.025 3.025 L 3.05 10 L 6.025 7.85833 L 9.025 10.05 L 6.025 3.025" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="7.877914951989029" y2="28.48902606310013"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV测试变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="37.86" id="3" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,728.429,33.6429) scale(1,1) translate(6.99546e-14,4.08403e-14)" width="196.67" x="630.1" y="14.71" zvalue="495"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728.429,33.6429) scale(1,1) translate(6.99546e-14,4.08403e-14)" writing-mode="lr" x="728.4299999999999" xml:space="preserve" y="41.14" zvalue="495">35kV测试变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,429.25,340) scale(1,1) translate(0,0)" writing-mode="lr" x="429.25" xml:space="preserve" y="344.5" zvalue="2">110kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.948,523.5) scale(1,1) translate(0,0)" writing-mode="lr" x="684.95" xml:space="preserve" y="528" zvalue="129">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.278,453.222) scale(1,1) translate(0,0)" writing-mode="lr" x="711.28" xml:space="preserve" y="457.72" zvalue="131">1013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715.5,412.25) scale(1,1) translate(0,0)" writing-mode="lr" x="715.5" xml:space="preserve" y="416.75" zvalue="133">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.278,368.222) scale(1,1) translate(0,0)" writing-mode="lr" x="711.28" xml:space="preserve" y="372.72" zvalue="136">1011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.278,681.222) scale(1,1) translate(0,0)" writing-mode="lr" x="711.28" xml:space="preserve" y="685.72" zvalue="143">0013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,713.5,643) scale(1,1) translate(0,0)" writing-mode="lr" x="713.5" xml:space="preserve" y="647.5" zvalue="145">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.278,600.222) scale(1,1) translate(0,0)" writing-mode="lr" x="711.28" xml:space="preserve" y="604.72" zvalue="147">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,431.466,712) scale(1,1) translate(0,0)" writing-mode="lr" x="431.47" xml:space="preserve" y="716.5" zvalue="153">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.28,376.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.28" xml:space="preserve" y="380.72" zvalue="246">1091</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709.278,743.222) scale(1,1) translate(0,0)" writing-mode="lr" x="709.28" xml:space="preserve" y="747.72" zvalue="258">0119</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.278,830.222) scale(1,1) translate(0,0)" writing-mode="lr" x="710.28" xml:space="preserve" y="834.72" zvalue="259">0111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715,793) scale(1,1) translate(0,0)" writing-mode="lr" x="715" xml:space="preserve" y="797.5" zvalue="260">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761,780) scale(1,1) translate(0,0)" writing-mode="lr" x="761" xml:space="preserve" y="784.5" zvalue="261">01190</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733,896.5) scale(1,1) translate(0,0)" writing-mode="lr" x="733" xml:space="preserve" y="901" zvalue="266">10kV备用一线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="398" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,515.456,680.222) scale(1,1) translate(0,0)" writing-mode="lr" x="515.46" xml:space="preserve" y="684.72" zvalue="411">0091</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.422,154.667) scale(1,1) translate(2.30456e-13,0)" writing-mode="lr" x="733.42" xml:space="preserve" y="159.17" zvalue="501">35kV测试一线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.722,214.111) scale(1,1) translate(0,0)" writing-mode="lr" x="708.72" xml:space="preserve" y="218.61" zvalue="503">3119</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,707.278,309.222) scale(1,1) translate(0,0)" writing-mode="lr" x="707.28" xml:space="preserve" y="313.72" zvalue="506">3111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715.932,267) scale(1,1) translate(0,0)" writing-mode="lr" x="715.9299999999999" xml:space="preserve" y="271.5" zvalue="507">311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761,299) scale(1,1) translate(0,0)" writing-mode="lr" x="761" xml:space="preserve" y="303.5" zvalue="510">31110</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761,253) scale(1,1) translate(0,0)" writing-mode="lr" x="761" xml:space="preserve" y="257.5" zvalue="516">31190</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="kv35" d="M 462.82 341 L 1073.93 341" stroke-width="6" zvalue="1"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403024899" ObjectName="110kVⅠ母"/>
   </metadata>
  <path d="M 462.82 341 L 1073.93 341" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 463.04 713 L 1074.14 713" stroke-width="6" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403090435" ObjectName="10kVⅠ母"/>
   </metadata>
  <path d="M 463.04 713 L 1074.14 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="127">
   <g id="1270">
    <use class="kv35" height="30" transform="rotate(0,733.426,524.145) scale(2.27083,2.27631) translate(-395.199,-274.739)" width="24" x="706.1799999999999" xlink:href="#PowerTransformer2:可调两卷变_0" y="490" zvalue="128"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1271">
    <use class="kv10" height="30" transform="rotate(0,733.426,524.145) scale(2.27083,2.27631) translate(-395.199,-274.739)" width="24" x="706.1799999999999" xlink:href="#PowerTransformer2:可调两卷变_1" y="490" zvalue="128"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399525793794" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.426,524.145) scale(2.27083,2.27631) translate(-395.199,-274.739)" width="24" x="706.1799999999999" y="490"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="131">
   <use class="kv35" height="30" transform="rotate(0,733.389,454.222) scale(1.01481,0.814815) translate(-10.5953,100.455)" width="15" x="725.7777777777778" xlink:href="#Disconnector:刀闸_0" y="442" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962301443" ObjectName="1013"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.389,454.222) scale(1.01481,0.814815) translate(-10.5953,100.455)" width="15" x="725.7777777777778" y="442"/></g>
  <g id="134">
   <use class="kv35" height="30" transform="rotate(0,733.389,369.222) scale(1.01481,0.814815) translate(-10.5953,81.1364)" width="15" x="725.7777777777778" xlink:href="#Disconnector:刀闸_0" y="357" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962366979" ObjectName="1011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.389,369.222) scale(1.01481,0.814815) translate(-10.5953,81.1364)" width="15" x="725.7777777777778" y="357"/></g>
  <g id="146">
   <use class="kv10" height="30" transform="rotate(0,733.389,682.222) scale(1.01481,0.814815) translate(-10.5953,152.273)" width="15" x="725.7777777777778" xlink:href="#Disconnector:刀闸_0" y="670" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962498051" ObjectName="0013"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.389,682.222) scale(1.01481,0.814815) translate(-10.5953,152.273)" width="15" x="725.7777777777778" y="670"/></g>
  <g id="144">
   <use class="kv10" height="30" transform="rotate(0,733.389,601.222) scale(1.01481,0.814815) translate(-10.5953,133.864)" width="15" x="725.7777777777778" xlink:href="#Disconnector:刀闸_0" y="589" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962432515" ObjectName="0011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.389,601.222) scale(1.01481,0.814815) translate(-10.5953,133.864)" width="15" x="725.7777777777778" y="589"/></g>
  <g id="236">
   <use class="kv35" height="30" transform="rotate(0,1025.39,377.222) scale(1.01481,0.814815) translate(-14.8581,82.9545)" width="15" x="1017.777777777778" xlink:href="#Disconnector:刀闸_0" y="365" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962563587" ObjectName="1091"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1025.39,377.222) scale(1.01481,0.814815) translate(-14.8581,82.9545)" width="15" x="1017.777777777778" y="365"/></g>
  <g id="246">
   <use class="kv10" height="30" transform="rotate(0,733.389,744.222) scale(1.01481,0.814815) translate(-10.5953,166.364)" width="15" x="725.7777777777778" xlink:href="#Disconnector:刀闸_0" y="732" zvalue="257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962694659" ObjectName="0119"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.389,744.222) scale(1.01481,0.814815) translate(-10.5953,166.364)" width="15" x="725.7777777777778" y="732"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(0,733.389,831.222) scale(1.01481,0.814815) translate(-10.5953,186.136)" width="15" x="725.7777777777778" xlink:href="#Disconnector:刀闸_0" y="819" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962760195" ObjectName="0111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.389,831.222) scale(1.01481,0.814815) translate(-10.5953,186.136)" width="15" x="725.7777777777778" y="819"/></g>
  <g id="402">
   <use class="kv10" height="30" transform="rotate(180,537.567,681.222) scale(1.01481,0.814815) translate(-7.73658,152.045)" width="15" x="529.9559326810063" xlink:href="#Disconnector:刀闸_0" y="669" zvalue="410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453963087875" ObjectName="0091"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,537.567,681.222) scale(1.01481,0.814815) translate(-7.73658,152.045)" width="15" x="529.9559326810063" y="669"/></g>
  <g id="111">
   <use class="kv35" height="30" transform="rotate(0,733.333,216.222) scale(1.01481,0.814815) translate(-10.5945,46.3636)" width="15" x="725.7217915535618" xlink:href="#Disconnector:刀闸_0" y="203.9999999999999" zvalue="502"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453963546627" ObjectName="3119"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.333,216.222) scale(1.01481,0.814815) translate(-10.5945,46.3636)" width="15" x="725.7217915535618" y="203.9999999999999"/></g>
  <g id="91">
   <use class="kv35" height="30" transform="rotate(0,733.416,310.222) scale(1.01481,0.814815) translate(-10.5957,67.7273)" width="15" x="725.8047846656699" xlink:href="#Disconnector:刀闸_0" y="297.9999999999999" zvalue="504"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453963481091" ObjectName="3111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.416,310.222) scale(1.01481,0.814815) translate(-10.5957,67.7273)" width="15" x="725.8047846656699" y="297.9999999999999"/></g>
 </g>
 <g id="BreakerClass">
  <g id="132">
   <use class="kv35" height="20" transform="rotate(0,733,412.25) scale(1.1,1) translate(-66.1364,0)" width="10" x="727.5" xlink:href="#Breaker:开关_0" y="402.25" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925115576323" ObjectName="101"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,733,412.25) scale(1.1,1) translate(-66.1364,0)" width="10" x="727.5" y="402.25"/></g>
  <g id="145">
   <use class="kv10" height="20" transform="rotate(0,733,643) scale(1.1,1) translate(-66.1364,0)" width="10" x="727.5" xlink:href="#Breaker:开关_0" y="633" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925115641859" ObjectName="001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,733,643) scale(1.1,1) translate(-66.1364,0)" width="10" x="727.5" y="633"/></g>
  <g id="247">
   <use class="kv10" height="20" transform="rotate(0,734,794) scale(1.1,1) translate(-66.2273,0)" width="10" x="728.5" xlink:href="#Breaker:开关_0" y="784" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925115707395" ObjectName="011"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,734,794) scale(1.1,1) translate(-66.2273,0)" width="10" x="728.5" y="784"/></g>
  <g id="90">
   <use class="kv35" height="20" transform="rotate(0,733.432,267) scale(1.1,1) translate(-66.1756,0)" width="10" x="727.9316398950618" xlink:href="#Breaker:开关_0" y="257" zvalue="505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925115772931" ObjectName="311"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,733.432,267) scale(1.1,1) translate(-66.1756,0)" width="10" x="727.9316398950618" y="257"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="136">
   <path class="kv35" d="M 733.48 442.4 L 733.48 421.8" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.48 442.4 L 733.48 421.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv35" d="M 733.45 381.24 L 733.45 402.68" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@1" LinkObjectIDznd="132@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.45 381.24 L 733.45 402.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv35" d="M 733.48 357.4 L 733.48 341" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.48 357.4 L 733.48 341" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 733.48 670.4 L 733.48 652.55" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="145@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.48 670.4 L 733.48 652.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv10" d="M 733.45 613.24 L 733.45 633.43" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@1" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.45 613.24 L 733.45 633.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 733.48 589.4 L 733.43 556.12" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.48 589.4 L 733.43 556.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 733.45 694.24 L 733.45 713" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@1" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.45 694.24 L 733.45 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 733.45 756.24 L 733.45 784.43" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@1" LinkObjectIDznd="247@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.45 756.24 L 733.45 784.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv10" d="M 733.48 819.4 L 733.48 803.55" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="247@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.48 819.4 L 733.48 803.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv10" d="M 751.25 770.05 L 733.45 770.05" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="251" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.25 770.05 L 733.45 770.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 733 862.2 L 733 843.24" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="248@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 733 862.2 L 733 843.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="404">
   <path class="kv10" d="M 537.5 669.21 L 537.5 653.34" stroke-width="1" zvalue="416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="402@1" LinkObjectIDznd="401@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 537.5 669.21 L 537.5 653.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="412">
   <path class="kv35" d="M 733.45 466.24 L 733.45 492.45" stroke-width="1" zvalue="425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.45 466.24 L 733.45 492.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="413">
   <path class="kv35" d="M 1025.45 404.02 L 1025.45 389.24" stroke-width="1" zvalue="426"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.45 404.02 L 1025.45 389.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv35" d="M 1025.48 365.4 L 1025.48 341" stroke-width="1" zvalue="497"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.48 365.4 L 1025.48 341" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 733.48 322.24 L 733.48 341" stroke-width="1" zvalue="509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@1" LinkObjectIDznd="138" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.48 322.24 L 733.48 341" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 733.5 298.4 L 733.5 276.55" stroke-width="1" zvalue="511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="90@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.5 298.4 L 733.5 276.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 733.39 257.43 L 733.39 228.24" stroke-width="1" zvalue="512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="111@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.39 257.43 L 733.39 228.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 751.25 288.05 L 733.5 288.05" stroke-width="1" zvalue="513"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.25 288.05 L 733.5 288.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 751.25 242.05 L 733.39 242.05" stroke-width="1" zvalue="515"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.25 242.05 L 733.39 242.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 733.42 187.91 L 733.42 204.4" stroke-width="1" zvalue="517"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.42 187.91 L 733.42 204.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 713.37 197.03 L 733.42 197.03" stroke-width="1" zvalue="519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 713.37 197.03 L 733.42 197.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 537.48 693.04 L 537.48 713" stroke-width="1" zvalue="520"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="402@0" LinkObjectIDznd="151@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 537.48 693.04 L 537.48 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv10" d="M 733.48 732.4 L 733.48 713" stroke-width="1" zvalue="521"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="151@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.48 732.4 L 733.48 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="237">
   <use class="kv35" height="18" transform="rotate(0,1025.18,417.82) scale(1.8254,1.75779) translate(-456.13,-173.304)" width="18" x="1008.74857850023" xlink:href="#Accessory:PT1_0" y="402" zvalue="247"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962629123" ObjectName="110kVⅠ母PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1025.18,417.82) scale(1.8254,1.75779) translate(-456.13,-173.304)" width="18" x="1008.74857850023" y="402"/></g>
  <g id="401">
   <use class="kv10" height="18" transform="rotate(180,538.722,642) scale(1.5,1.44444) translate(-175.074,-193.538)" width="18" x="525.2222222222222" xlink:href="#Accessory:PT1_0" y="629" zvalue="412"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453963022339" ObjectName="10kVⅠ母PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(180,538.722,642) scale(1.5,1.44444) translate(-175.074,-193.538)" width="18" x="525.2222222222222" y="629"/></g>
  <g id="19">
   <use class="kv35" height="26" transform="rotate(90,701,197) scale(1,1) translate(0,0)" width="12" x="695" xlink:href="#Accessory:避雷器_0" y="184" zvalue="518"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453963153411" ObjectName="35kV测试一线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,701,197) scale(1,1) translate(0,0)" width="12" x="695" y="184"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="249">
   <use class="kv10" height="20" transform="rotate(90,761,770) scale(1,-1) translate(0,-1540)" width="10" x="756" xlink:href="#GroundDisconnector:地刀_0" y="760" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962891267" ObjectName="01190"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,761,770) scale(1,-1) translate(0,-1540)" width="10" x="756" y="760"/></g>
  <g id="46">
   <use class="kv35" height="20" transform="rotate(90,761,288) scale(1,-1) translate(0,-576)" width="10" x="756" xlink:href="#GroundDisconnector:地刀_0" y="278" zvalue="508"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453963415555" ObjectName="31110"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,761,288) scale(1,-1) translate(0,-576)" width="10" x="756" y="278"/></g>
  <g id="36">
   <use class="kv35" height="20" transform="rotate(90,761,242) scale(1,-1) translate(0,-484)" width="10" x="756" xlink:href="#GroundDisconnector:地刀_0" y="232" zvalue="514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453963284483" ObjectName="31190"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,761,242) scale(1,-1) translate(0,-484)" width="10" x="756" y="232"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="258">
   <use class="kv10" height="30" transform="rotate(180,733,873) scale(1,0.8) translate(0,215.25)" width="12" x="727" xlink:href="#EnergyConsumer:负荷_0" y="861" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453962956803" ObjectName="10kV备用一线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,733,873) scale(1,0.8) translate(0,215.25)" width="12" x="727" y="861"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="50" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,433.607,320) scale(1,1) translate(4.23384e-14,0)" writing-mode="lr" x="433.83" xml:space="preserve" y="325.89" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="53" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,433.537,693.429) scale(1,1) translate(4.23306e-14,0)" writing-mode="lr" x="433.76" xml:space="preserve" y="699.3200000000001" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" SignFlag="down" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="54" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,781.426,388) scale(1,1) translate(0,0)" writing-mode="lr" x="781.6" xml:space="preserve" y="393.89" zvalue="1">P:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" SignFlag="down" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="56" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,781.426,410) scale(1,1) translate(0,0)" writing-mode="lr" x="781.6" xml:space="preserve" y="415.89" zvalue="1">Q:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="58" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,781.426,622.289) scale(1,1) translate(0,1.35956e-13)" writing-mode="lr" x="781.6" xml:space="preserve" y="628.1799999999999" zvalue="1">P:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="60" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,781.426,644.289) scale(1,1) translate(0,0)" writing-mode="lr" x="781.6" xml:space="preserve" y="650.1799999999999" zvalue="1">Q:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="62" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,781.426,432) scale(1,1) translate(0,0)" writing-mode="lr" x="781.6" xml:space="preserve" y="437.89" zvalue="1">Ia:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="64" prefix="油温：" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.562,521.895) scale(1,1) translate(0,0)" writing-mode="lr" x="821.72" xml:space="preserve" y="527.79" zvalue="1">油温：????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="66" prefix="档位：" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.187,501.145) scale(1,1) translate(0,0)" writing-mode="lr" x="821.34" xml:space="preserve" y="507.04" zvalue="1">档位：????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="88" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,781.426,666.289) scale(1,1) translate(0,0)" writing-mode="lr" x="781.6" xml:space="preserve" y="672.1799999999999" zvalue="1">Ia:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,733,914.25) scale(1,1) translate(0,0)" writing-mode="lr" x="733.15" xml:space="preserve" y="920.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="98" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,733,936.25) scale(1,1) translate(0,0)" writing-mode="lr" x="733.15" xml:space="preserve" y="942.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="104" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,733,958.25) scale(1,1) translate(0,0)" writing-mode="lr" x="733.15" xml:space="preserve" y="964.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="115" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,725.422,71.5) scale(1,1) translate(-1.51084e-13,0)" writing-mode="lr" x="725.62" xml:space="preserve" y="76.41" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="116" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,725.422,93) scale(1,1) translate(-1.51084e-13,0)" writing-mode="lr" x="725.62" xml:space="preserve" y="97.91" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="117" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,725.422,115) scale(1,1) translate(-1.51084e-13,0)" writing-mode="lr" x="725.62" xml:space="preserve" y="119.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.422,135.25) scale(1,1) translate(-1.51084e-13,0)" writing-mode="lr" x="725.62" xml:space="preserve" y="140.16" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Cos"/>
   </metadata>
  </g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="112">
   <use class="kv35" height="30" transform="rotate(0,733.422,179.25) scale(3.09524,0.583333) translate(-489.137,121.786)" width="7" x="722.5886467829539" xlink:href="#ACLineSegment:线路_0" y="170.5" zvalue="500"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453963612163" ObjectName="35kV测试一线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,733.422,179.25) scale(3.09524,0.583333) translate(-489.137,121.786)" width="7" x="722.5886467829539" y="170.5"/></g>
 </g>
</svg>