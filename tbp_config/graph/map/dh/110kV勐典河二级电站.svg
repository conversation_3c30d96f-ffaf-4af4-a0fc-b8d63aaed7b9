<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684535297" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_0" viewBox="0,0,50,50">
   <use terminal-index="0" type="1" x="14.75" xlink:href="#terminal" y="0.2499999999999929"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="10.58333333333333" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="10.66666666666667" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="14.16666666666666" y2="18.91666666666666"/>
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="2" x="15.08333333333334" xlink:href="#terminal" y="14.16666666666666"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_1" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66666666666666" x2="34.66666666666666" y1="29.41666666666667" y2="25.11796982167353"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66598079561043" x2="29.75" y1="25.11796982167353" y2="20.66666666666667"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.67329675354367" x2="39.25" y1="25.11065386374029" y2="20.58333333333334"/>
   <ellipse cx="34.78" cy="25.08" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="49.50000000000001" xlink:href="#terminal" y="25.08333333333333"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_2" viewBox="0,0,50,50">
   <path d="M 15.1667 32 L 10.0833 40 L 20.1667 40 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.12" cy="35.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="3" type="1" x="15.08333333333334" xlink:href="#terminal" y="49.91666666666667"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变11_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="9.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0928507596068" x2="10.0928507596068" y1="10.97013412501683" y2="12.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.77292225201073" x2="10.0928507596068" y1="14.31365173613114" y2="12.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀11_0" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="16" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="16" y1="1.75" y2="6.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="5" y1="17.75" y2="17.75"/>
   <path d="M 16 8.75 L 15.5 6.75 L 16.5 6.75 L 16 8.75 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="20.58333333333334" y2="13.36429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666668" x2="8.000000000000002" y1="20.59694619969018" y2="20.59694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333334" x2="7" y1="22.43157590710537" y2="22.43157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="24.18348701738202" y2="24.18348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666661" x2="5.081410256410254" y1="3.516666666666666" y2="13.3642916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.39249548976499" y2="3.39249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.350000000000005" y2="0.6666666666666679"/>
   <path d="M 16.0833 10.9167 L 15.5833 12.9167 L 16.5833 12.9167 L 16.0833 10.9167 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.08333333333334" x2="16.08333333333334" y1="17.91666666666667" y2="12.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀11_1" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="16" y1="1.75" y2="6.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="16" y1="1.75" y2="1.75"/>
   <path d="M 16 8.75 L 15.5 6.75 L 16.5 6.75 L 16 8.75 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="5" y1="17.75" y2="17.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="20.58333333333333" y2="3.333333333333332"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666668" x2="8.000000000000002" y1="20.59694619969018" y2="20.59694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333334" x2="7" y1="22.43157590710537" y2="22.43157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="24.18348701738202" y2="24.18348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.350000000000005" y2="0.6666666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.39249548976499" y2="3.39249548976499"/>
   <path d="M 16.0833 10.9167 L 15.5833 12.9167 L 16.5833 12.9167 L 16.0833 10.9167 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.08333333333334" x2="16.08333333333334" y1="17.91666666666667" y2="12.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀11_2" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="8" y1="4.5" y2="12.5"/>
   <path d="M 16 8.75 L 15.5 6.75 L 16.5 6.75 L 16 8.75 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="16" y1="1.75" y2="6.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="5" y1="17.75" y2="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="16" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="2" y1="4.5" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="20.58333333333334" y2="13.36429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666668" x2="8.000000000000002" y1="20.59694619969018" y2="20.59694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333334" x2="7" y1="22.43157590710537" y2="22.43157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="24.18348701738202" y2="24.18348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.39249548976499" y2="3.39249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.350000000000005" y2="0.6666666666666679"/>
   <path d="M 16.0833 10.9167 L 15.5833 12.9167 L 16.5833 12.9167 L 16.0833 10.9167 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.08333333333334" x2="16.08333333333334" y1="17.91666666666667" y2="12.91666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
  <symbol id="Accessory:pt带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <path d="M 15 12.5 L 15 3.08333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,6.21) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.083333333333332" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV勐典河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="53" xlink:href="logo.png" y="45"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,181,75) scale(1,1) translate(0,0)" writing-mode="lr" x="181" xml:space="preserve" y="78.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.333,74.6903) scale(1,1) translate(6.55032e-15,0)" writing-mode="lr" x="179.33" xml:space="preserve" y="83.69" zvalue="3">110kV勐典河二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="278" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="709"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="709">信号一览</text>
  <line fill="none" id="36" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="387" x2="387" y1="13" y2="1043" zvalue="4"/>
  <line fill="none" id="34" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00000000000045" x2="380" y1="148.8704926140824" y2="148.8704926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,448.556,280) scale(1,1) translate(-3.67114e-13,-3.59046e-13)" writing-mode="lr" x="448.56" xml:space="preserve" y="284.5" zvalue="42">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.61,320.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.61" xml:space="preserve" y="324.72" zvalue="44">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,438.883,608.556) scale(1,1) translate(0,0)" writing-mode="lr" x="438.88" xml:space="preserve" y="613.0599999999999" zvalue="47">10kV Ⅳ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,535.333,222.111) scale(1,1) translate(0,0)" writing-mode="lr" x="535.33" xml:space="preserve" y="226.61" zvalue="48">161</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,526.556,269.667) scale(1,1) translate(0,0)" writing-mode="lr" x="526.5599999999999" xml:space="preserve" y="274.17" zvalue="50">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.222,161.667) scale(1,1) translate(0,0)" writing-mode="lr" x="528.22" xml:space="preserve" y="166.17" zvalue="52">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,513.932,71.4444) scale(1,1) translate(0,0)" writing-mode="lr" x="513.9299999999999" xml:space="preserve" y="75.94" zvalue="57">110kV勐二一线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,563.056,260.778) scale(1,1) translate(0,0)" writing-mode="lr" x="563.0599999999999" xml:space="preserve" y="265.28" zvalue="60">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,563.056,150.15) scale(1,1) translate(0,0)" writing-mode="lr" x="563.0599999999999" xml:space="preserve" y="154.65" zvalue="64">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,854.556,241.667) scale(1,1) translate(0,0)" writing-mode="lr" x="854.5599999999999" xml:space="preserve" y="246.17" zvalue="66">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.333,220.944) scale(1,1) translate(0,0)" writing-mode="lr" x="883.33" xml:space="preserve" y="225.44" zvalue="68">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,578.778,339.222) scale(1,1) translate(0,0)" writing-mode="lr" x="578.78" xml:space="preserve" y="343.72" zvalue="73">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,588.111,395.222) scale(1,1) translate(0,0)" writing-mode="lr" x="588.11" xml:space="preserve" y="399.72" zvalue="74">104</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,610.444,372.222) scale(1,1) translate(0,0)" writing-mode="lr" x="610.4400000000001" xml:space="preserve" y="376.72" zvalue="77">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,456.03,674.111) scale(1,1) translate(0,0)" writing-mode="lr" x="456.03" xml:space="preserve" y="678.61" zvalue="80">067</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827.956,106.75) scale(1,1) translate(0,0)" writing-mode="lr" x="827.9562440537476" xml:space="preserve" y="111.25" zvalue="84">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.333,286.111) scale(1,1) translate(0,0)" writing-mode="lr" x="883.33" xml:space="preserve" y="290.61" zvalue="86">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632.974,458) scale(1,1) translate(4.63748e-13,0)" writing-mode="lr" x="632.9742557276099" xml:space="preserve" y="462.5" zvalue="88">#4主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1499.83,218.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1499.83" xml:space="preserve" y="222.67" zvalue="91">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1491.06,265.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.06" xml:space="preserve" y="270.22" zvalue="92">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1492.72,165.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.72" xml:space="preserve" y="170.22" zvalue="95">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1476.53,79.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1476.53" xml:space="preserve" y="84" zvalue="99">35kV光明硅厂Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1515.56,147.404) scale(1,1) translate(0,0)" writing-mode="lr" x="1515.56" xml:space="preserve" y="151.9" zvalue="101">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1782.98,358.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1782.98" xml:space="preserve" y="363.11" zvalue="104">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,432,860.25) scale(1,1) translate(0,0)" writing-mode="lr" x="432" xml:space="preserve" y="864.75" zvalue="109">#4发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" x="701.328125" xml:space="preserve" y="794.75" zvalue="125">10kVⅣ母电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="701.328125" xml:space="preserve" y="810.75" zvalue="125">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,509,505) scale(1,1) translate(0,0)" writing-mode="lr" x="509" xml:space="preserve" y="509.5" zvalue="201">1040</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,617,810.75) scale(1,1) translate(0,0)" writing-mode="lr" x="617" xml:space="preserve" y="815.25" zvalue="207">#3站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,636.5,478) scale(1,1) translate(-5.33129e-13,0)" writing-mode="lr" x="636.4999999999999" xml:space="preserve" y="482.5" zvalue="212">31.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,726,682.25) scale(1,1) translate(0,0)" writing-mode="lr" x="726" xml:space="preserve" y="686.75" zvalue="214">0904</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,753,606.556) scale(1,1) translate(0,0)" writing-mode="lr" x="753" xml:space="preserve" y="611.0599999999999" zvalue="217">10kVⅢ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.895,339.222) scale(1,1) translate(1.29087e-12,0)" writing-mode="lr" x="896.89" xml:space="preserve" y="343.72" zvalue="220">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,906.228,395.222) scale(1,1) translate(0,0)" writing-mode="lr" x="906.23" xml:space="preserve" y="399.72" zvalue="221">103</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,928.562,372.222) scale(1,1) translate(0,0)" writing-mode="lr" x="928.5599999999999" xml:space="preserve" y="376.72" zvalue="224">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,870.147,674.111) scale(1,1) translate(0,0)" writing-mode="lr" x="870.15" xml:space="preserve" y="678.61" zvalue="226">063</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.789,482.028) scale(1,1) translate(0,0)" writing-mode="lr" x="953.7888681137022" xml:space="preserve" y="486.5277777777778" zvalue="229">#3主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,846.117,903.25) scale(1,1) translate(1.80326e-13,0)" writing-mode="lr" x="846.1170633550773" xml:space="preserve" y="907.75" zvalue="232">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" x="791.4375" xml:space="preserve" y="763.5" zvalue="246">10kVⅢ母电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="791.4375" xml:space="preserve" y="779.5" zvalue="246">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.117,505) scale(1,1) translate(0,0)" writing-mode="lr" x="831.12" xml:space="preserve" y="509.5" zvalue="252">1030</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,946.145,521.75) scale(1,1) translate(0,0)" writing-mode="lr" x="946.144841132855" xml:space="preserve" y="526.25" zvalue="259">21MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.559,657) scale(1,1) translate(0,0)" writing-mode="lr" x="761.5599999999999" xml:space="preserve" y="661.5" zvalue="261">0903</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324.25,603.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1324.25" xml:space="preserve" y="607.5599999999999" zvalue="266">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1390.89,339.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1390.89" xml:space="preserve" y="343.72" zvalue="269">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1400.23,383.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1400.23" xml:space="preserve" y="387.72" zvalue="270">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1422.56,376.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1422.56" xml:space="preserve" y="380.72" zvalue="273">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1476.12,676.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1476.12" xml:space="preserve" y="680.83" zvalue="289">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1329.12,505) scale(1,1) translate(0,0)" writing-mode="lr" x="1329.12" xml:space="preserve" y="509.5" zvalue="298">1020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1449.12,785) scale(1,1) translate(-1.26087e-12,0)" writing-mode="lr" x="1449.12" xml:space="preserve" y="789.5" zvalue="300">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1470.37,473.75) scale(1,1) translate(1.11453e-12,0)" writing-mode="lr" x="1470.367063355077" xml:space="preserve" y="478.25" zvalue="305">21MVW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472,457.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1472" xml:space="preserve" y="462" zvalue="310">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1715.83,219.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1715.83" xml:space="preserve" y="223.67" zvalue="320">362</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1707.06,266.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1707.06" xml:space="preserve" y="271.22" zvalue="321">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1708.72,166.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1708.72" xml:space="preserve" y="171.22" zvalue="324">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1694.43,80.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1694.43" xml:space="preserve" y="85" zvalue="328">35kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1731.56,146.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1731.56" xml:space="preserve" y="151.12" zvalue="330">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1815.03,484.96) scale(1,1) translate(0,0)" writing-mode="lr" x="1815.027777777778" xml:space="preserve" y="489.4595964087737" zvalue="338">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1652.67,339.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1652.67" xml:space="preserve" y="343.72" zvalue="342">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1662.11,395.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1662.11" xml:space="preserve" y="399.72" zvalue="343">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1655.48,445.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1655.48" xml:space="preserve" y="449.72" zvalue="347">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1436.11,269.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1436.11" xml:space="preserve" y="273.62" zvalue="375">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1658.11,268.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1658.11" xml:space="preserve" y="272.87" zvalue="378">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1447.25,169.306) scale(1,1) translate(0,0)" writing-mode="lr" x="1447.25" xml:space="preserve" y="173.81" zvalue="382">3619</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1658,161.526) scale(1,1) translate(0,0)" writing-mode="lr" x="1658" xml:space="preserve" y="166.03" zvalue="389">3629</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,589.194,710.472) scale(1,1) translate(0,0)" writing-mode="lr" x="589.1900000000001" xml:space="preserve" y="714.97" zvalue="399">068</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,587.821,664.472) scale(1,1) translate(0,0)" writing-mode="lr" x="587.8200000000001" xml:space="preserve" y="668.97" zvalue="404">0681</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,540,774.25) scale(1,1) translate(0,0)" writing-mode="lr" x="540" xml:space="preserve" y="778.75" zvalue="414">0942</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,507.25,762.75) scale(1,1) translate(0,0)" writing-mode="lr" x="507.25" xml:space="preserve" y="767.25" zvalue="429">0941</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1008.5,844) scale(1,1) translate(0,0)" writing-mode="lr" x="1008.5" xml:space="preserve" y="848.5" zvalue="435">0932</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.512,840.75) scale(1,1) translate(0,0)" writing-mode="lr" x="924.51" xml:space="preserve" y="845.25" zvalue="437">0931</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="429" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010,607.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1010" xml:space="preserve" y="611.5599999999999" zvalue="446">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="428" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1111.89,336.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1111.89" xml:space="preserve" y="341.22" zvalue="449">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="427" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1121.23,392.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.23" xml:space="preserve" y="397.22" zvalue="450">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="426" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1143.56,369.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.56" xml:space="preserve" y="374.22" zvalue="453">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="425" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1085.15,671.611) scale(1,1) translate(-7.15525e-13,0)" writing-mode="lr" x="1085.15" xml:space="preserve" y="676.11" zvalue="455">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="424" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1164.65,450.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1164.652174012193" xml:space="preserve" y="455" zvalue="458">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="423" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.12,901.75) scale(1,1) translate(-2.28066e-13,0)" writing-mode="lr" x="1061.117063355077" xml:space="preserve" y="906.25" zvalue="461">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="422" stroke="rgb(255,255,255)" text-anchor="middle" x="1201.34375" xml:space="preserve" y="770.5" zvalue="463">10kVⅠ母电压互</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="422" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1201.34375" xml:space="preserve" y="786.5" zvalue="463">感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="421" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.12,502.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.12" xml:space="preserve" y="507" zvalue="469">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="444" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.37,474.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.367063355077" xml:space="preserve" y="478.75" zvalue="470">21MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="420" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.03,656.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.03" xml:space="preserve" y="661" zvalue="472">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1215.5,845.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1215.5" xml:space="preserve" y="850" zvalue="480">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1139.51,842.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.51" xml:space="preserve" y="847" zvalue="481">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1028.37,673.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1028.37" xml:space="preserve" y="678.33" zvalue="493">064</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="466" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1001.37,782.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1001.37" xml:space="preserve" y="787" zvalue="497">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="473" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1569.62,674.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1569.62" xml:space="preserve" y="678.58" zvalue="500">066</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="472" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1541.62,783.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1541.62" xml:space="preserve" y="788.25" zvalue="504">近区变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="481" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1318.9,675.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.9" xml:space="preserve" y="679.86" zvalue="507">062</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="480" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.87,904.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.867063355077" xml:space="preserve" y="909" zvalue="511">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="479" stroke="rgb(255,255,255)" text-anchor="middle" x="1645.09375" xml:space="preserve" y="776.75" zvalue="513">10kVⅡ母电压互</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="479" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1645.09375" xml:space="preserve" y="792.75" zvalue="513">感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="478" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1615.31,662.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1615.31" xml:space="preserve" y="667.25" zvalue="515">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1452.25,846.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1452.25" xml:space="preserve" y="850.75" zvalue="522">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1373.26,846.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1373.26" xml:space="preserve" y="850.75" zvalue="523">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.556,581.444) scale(1,1) translate(0,0)" writing-mode="lr" x="534.5599999999999" xml:space="preserve" y="585.9400000000001" zvalue="550">0041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,857.444,572.556) scale(1,1) translate(0,0)" writing-mode="lr" x="857.4400000000001" xml:space="preserve" y="577.0599999999999" zvalue="554">0031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.22,569.222) scale(1,1) translate(4.71277e-13,0)" writing-mode="lr" x="1076.22" xml:space="preserve" y="573.72" zvalue="558">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1354.56,575.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1354.56" xml:space="preserve" y="580.39" zvalue="562">0021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1716.44,366.041) scale(1,1) translate(0,0)" writing-mode="lr" x="1716.44" xml:space="preserve" y="370.54" zvalue="568">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1715.22,431.597) scale(1,1) translate(0,0)" writing-mode="lr" x="1715.22" xml:space="preserve" y="436.1" zvalue="570">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1598.44,349.819) scale(1,1) translate(0,0)" writing-mode="lr" x="1598.44" xml:space="preserve" y="354.32" zvalue="572">30217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1610,445.041) scale(1,1) translate(0,0)" writing-mode="lr" x="1610" xml:space="preserve" y="449.54" zvalue="574">30267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1365.3,429.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1365.3" xml:space="preserve" y="433.72" zvalue="584">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1426.78,429.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.78" xml:space="preserve" y="433.72" zvalue="588">67</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="248.9999566202091" y2="248.9999566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275.3396566202091" y2="275.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="248.9999566202091" y2="275.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="248.9999566202091" y2="275.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="248.9999566202091" y2="248.9999566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275.3396566202091" y2="275.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="248.9999566202091" y2="275.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="248.9999566202091" y2="275.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275.3397566202091" y2="275.3397566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.9066566202091" y2="299.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275.3397566202091" y2="299.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275.3397566202091" y2="299.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275.3397566202091" y2="275.3397566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.9066566202091" y2="299.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275.3397566202091" y2="299.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275.3397566202091" y2="299.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.9065566202091" y2="299.9065566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322.9538566202091" y2="322.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.9065566202091" y2="322.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.9065566202091" y2="322.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.9065566202091" y2="299.9065566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322.9538566202091" y2="322.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.9065566202091" y2="322.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.9065566202091" y2="322.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322.9538566202091" y2="322.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="346.0011566202091" y2="346.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322.9538566202091" y2="346.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322.9538566202091" y2="346.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322.9538566202091" y2="322.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="346.0011566202091" y2="346.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322.9538566202091" y2="346.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322.9538566202091" y2="346.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="346.0010866202091" y2="346.0010866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="369.0483866202091" y2="369.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="346.0010866202091" y2="369.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="346.0010866202091" y2="369.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="346.0010866202091" y2="346.0010866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="369.0483866202091" y2="369.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="346.0010866202091" y2="369.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="346.0010866202091" y2="369.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="369.0483566202091" y2="369.0483566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="392.0956566202091" y2="392.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="369.0483566202091" y2="392.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="369.0483566202091" y2="392.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="369.0483566202091" y2="369.0483566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="392.0956566202091" y2="392.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="369.0483566202091" y2="392.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="369.0483566202091" y2="392.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="392.0955566202091" y2="392.0955566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="415.1428566202091" y2="415.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="392.0955566202091" y2="415.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="392.0955566202091" y2="415.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="392.0955566202091" y2="392.0955566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="415.1428566202091" y2="415.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="392.0955566202091" y2="415.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="392.0955566202091" y2="415.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="415.1428566202091" y2="415.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="438.1901566202091" y2="438.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="415.1428566202091" y2="438.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="415.1428566202091" y2="438.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="415.1428566202091" y2="415.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="438.1901566202091" y2="438.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="415.1428566202091" y2="438.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="415.1428566202091" y2="438.1901566202091"/>
  <line fill="none" id="298" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="688"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="690">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="691">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="692">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="693">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="694">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="696">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="697">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="698">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="699">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="700">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="701">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.625,334) scale(1,1) translate(0,0)" writing-mode="lr" x="241.63" xml:space="preserve" y="338.5" zvalue="702">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,357.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="361.75" zvalue="703">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="710">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="711">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,405.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="409.75" zvalue="714">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="408.75" zvalue="715">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,428.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="432.75" zvalue="716">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,427.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="431.75" zvalue="717">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="270" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="718">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="720">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.688,357.25) scale(1,1) translate(0,0)" writing-mode="lr" x="239.69" xml:space="preserve" y="361.75" zvalue="723">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.2589,379.821) scale(1,1) translate(0,0)" writing-mode="lr" x="58.26" xml:space="preserve" y="384.32" zvalue="730">10kVⅢ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.688,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="239.69" xml:space="preserve" y="385.75" zvalue="732">10kV Ⅳ母频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,210,952) scale(1,1) translate(0,0)" writing-mode="lr" x="210" xml:space="preserve" y="958" zvalue="742">MengDian2-01-2013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,435.5,877) scale(1,1) translate(0,0)" writing-mode="lr" x="435.5" xml:space="preserve" y="881.5" zvalue="768">24MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,849.5,922) scale(1,1) translate(0,0)" writing-mode="lr" x="849.5" xml:space="preserve" y="926.5" zvalue="770">15MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1296.5,921) scale(1,1) translate(0,0)" writing-mode="lr" x="1296.5" xml:space="preserve" y="925.5" zvalue="772">15MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1062.5,921) scale(1,1) translate(0,0)" writing-mode="lr" x="1062.5" xml:space="preserve" y="925.5" zvalue="774">15MW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="709"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="362">
   <path class="kv110" d="M 448.44 301.67 L 1390 301.67" stroke-width="4" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422161411" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674422161411"/></metadata>
  <path d="M 448.44 301.67 L 1390 301.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="361">
   <path class="kv35" d="M 1454.16 302.67 L 1828.61 302.67" stroke-width="4" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422095875" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674422095875"/></metadata>
  <path d="M 1454.16 302.67 L 1828.61 302.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="360">
   <path class="kv10" d="M 404.75 627.89 L 728.5 627.89" stroke-width="4" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422030339" ObjectName="10kVⅣ母"/>
   <cge:TPSR_Ref TObjectID="9288674422030339"/></metadata>
  <path d="M 404.75 627.89 L 728.5 627.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv10" d="M 775.5 626.89 L 942.5 626.89" stroke-width="4" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421964803" ObjectName="10kVⅢ母"/>
   <cge:TPSR_Ref TObjectID="9288674421964803"/></metadata>
  <path d="M 775.5 626.89 L 942.5 626.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="343">
   <path class="kv10" d="M 1262 626.89 L 1680 626.89" stroke-width="4" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421899267" ObjectName="10kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674421899267"/></metadata>
  <path d="M 1262 626.89 L 1680 626.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="460">
   <path class="kv10" d="M 990.5 624.39 L 1233.75 624.39" stroke-width="4" zvalue="445"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422226947" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674422226947"/></metadata>
  <path d="M 990.5 624.39 L 1233.75 624.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="359">
   <use class="kv110" height="20" transform="rotate(0,514,223.111) scale(1.22222,1.11111) translate(-92.3434,-21.2)" width="10" x="507.8888888888889" xlink:href="#Breaker:开关_0" y="212" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219123203" ObjectName="110kV勐二一线161断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219123203"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,514,223.111) scale(1.22222,1.11111) translate(-92.3434,-21.2)" width="10" x="507.8888888888889" y="212"/></g>
  <g id="334">
   <use class="kv110" height="20" transform="rotate(0,567.333,396.222) scale(1.22222,1.11111) translate(-102.04,-38.5111)" width="10" x="561.2222324079938" xlink:href="#Breaker:开关_0" y="385.1111111111111" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219057668" ObjectName="#4主变110kV侧104断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219057668"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,567.333,396.222) scale(1.22222,1.11111) translate(-102.04,-38.5111)" width="10" x="561.2222324079938" y="385.1111111111111"/></g>
  <g id="341">
   <use class="kv10" height="20" transform="rotate(0,431.03,675.111) scale(2,2) translate(-210.515,-327.556)" width="10" x="421.0295695852833" xlink:href="#Breaker:小车断路器_0" y="655.1111128065321" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925218992131" ObjectName="#4发电机067断路器"/>
   <cge:TPSR_Ref TObjectID="6473925218992131"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,431.03,675.111) scale(2,2) translate(-210.515,-327.556)" width="10" x="421.0295695852833" y="655.1111128065321"/></g>
  <g id="764">
   <use class="kv35" height="20" transform="rotate(0,1478.5,219.167) scale(1.22222,1.11111) translate(-267.707,-20.8056)" width="10" x="1472.388888888889" xlink:href="#Breaker:开关_0" y="208.05554178026" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925218926595" ObjectName="35kV光明硅厂Ⅰ回线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473925218926595"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1478.5,219.167) scale(1.22222,1.11111) translate(-267.707,-20.8056)" width="10" x="1472.388888888889" y="208.05554178026"/></g>
  <g id="233">
   <use class="kv110" height="20" transform="rotate(0,885.45,396.222) scale(1.22222,1.11111) translate(-159.88,-38.5111)" width="10" x="879.3392957630713" xlink:href="#Breaker:开关_0" y="385.1111111111111" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925218861059" ObjectName="#3主变110kV侧103断路器"/>
   <cge:TPSR_Ref TObjectID="6473925218861059"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,885.45,396.222) scale(1.22222,1.11111) translate(-159.88,-38.5111)" width="10" x="879.3392957630713" y="385.1111111111111"/></g>
  <g id="229">
   <use class="kv10" height="20" transform="rotate(0,845.147,675.111) scale(2,2) translate(-417.573,-327.556)" width="10" x="835.1466329403607" xlink:href="#Breaker:小车断路器_0" y="655.1111128065321" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925218795523" ObjectName="#3发电机063断路器"/>
   <cge:TPSR_Ref TObjectID="6473925218795523"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,845.147,675.111) scale(2,2) translate(-417.573,-327.556)" width="10" x="835.1466329403607" y="655.1111128065321"/></g>
  <g id="340">
   <use class="kv110" height="20" transform="rotate(0,1379.45,384.222) scale(1.22222,1.11111) translate(-249.698,-37.3111)" width="10" x="1373.339295763071" xlink:href="#Breaker:开关_0" y="373.1111111111111" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925218729987" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925218729987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1379.45,384.222) scale(1.22222,1.11111) translate(-249.698,-37.3111)" width="10" x="1373.339295763071" y="373.1111111111111"/></g>
  <g id="308">
   <use class="kv10" height="20" transform="rotate(0,1449.62,677.333) scale(2,2) translate(-719.809,-328.667)" width="10" x="1439.617063355078" xlink:href="#Breaker:小车断路器_0" y="657.3333384195963" zvalue="288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925218664451" ObjectName="#2站用变065断路器"/>
   <cge:TPSR_Ref TObjectID="6473925218664451"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1449.62,677.333) scale(2,2) translate(-719.809,-328.667)" width="10" x="1439.617063355078" y="657.3333384195963"/></g>
  <g id="170">
   <use class="kv35" height="20" transform="rotate(0,1694.5,220.167) scale(1.22222,1.11111) translate(-306.98,-20.9056)" width="10" x="1688.388888888889" xlink:href="#Breaker:开关_0" y="209.05554178026" zvalue="318"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925218598915" ObjectName="35kV备用线362断路器"/>
   <cge:TPSR_Ref TObjectID="6473925218598915"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1694.5,220.167) scale(1.22222,1.11111) translate(-306.98,-20.9056)" width="10" x="1688.388888888889" y="209.05554178026"/></g>
  <g id="182">
   <use class="kv35" height="20" transform="rotate(0,1641.33,396.222) scale(1.22222,1.11111) translate(-297.313,-38.5111)" width="10" x="1635.222222235468" xlink:href="#Breaker:开关_0" y="385.1111111111111" zvalue="341"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925218533379" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925218533379"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1641.33,396.222) scale(1.22222,1.11111) translate(-297.313,-38.5111)" width="10" x="1635.222222235468" y="385.1111111111111"/></g>
  <g id="383">
   <use class="kv10" height="20" transform="rotate(0,616.083,709.472) scale(1.22222,1.11111) translate(-110.904,-69.8361)" width="10" x="609.9722324079938" xlink:href="#Breaker:开关_0" y="698.3611111111111" zvalue="398"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219188739" ObjectName="#3站用变068断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219188739"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,616.083,709.472) scale(1.22222,1.11111) translate(-110.904,-69.8361)" width="10" x="609.9722324079938" y="698.3611111111111"/></g>
  <g id="458">
   <use class="kv110" height="20" transform="rotate(0,1100.45,393.722) scale(1.22222,1.11111) translate(-198.971,-38.2611)" width="10" x="1094.339295763071" xlink:href="#Breaker:开关_0" y="382.6111111111111" zvalue="448"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219319811" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219319811"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1100.45,393.722) scale(1.22222,1.11111) translate(-198.971,-38.2611)" width="10" x="1094.339295763071" y="382.6111111111111"/></g>
  <g id="455">
   <use class="kv10" height="20" transform="rotate(0,1060.15,672.611) scale(2,2) translate(-525.073,-326.306)" width="10" x="1050.146632940361" xlink:href="#Breaker:小车断路器_0" y="652.6111128065321" zvalue="454"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219254275" ObjectName="#1发电机061断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219254275"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1060.15,672.611) scale(2,2) translate(-525.073,-326.306)" width="10" x="1050.146632940361" y="652.6111128065321"/></g>
  <g id="471">
   <use class="kv10" height="20" transform="rotate(0,1001.87,674.833) scale(2,2) translate(-495.934,-327.417)" width="10" x="991.8670633550776" xlink:href="#Breaker:小车断路器_0" y="654.8333384195963" zvalue="492"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219385347" ObjectName="#1站用变064断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219385347"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1001.87,674.833) scale(2,2) translate(-495.934,-327.417)" width="10" x="991.8670633550776" y="654.8333384195963"/></g>
  <g id="477">
   <use class="kv10" height="20" transform="rotate(0,1543.12,675.083) scale(2,2) translate(-766.559,-327.542)" width="10" x="1533.117063355078" xlink:href="#Breaker:小车断路器_0" y="655.0833384195963" zvalue="499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219450883" ObjectName="近区变066断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219450883"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1543.12,675.083) scale(2,2) translate(-766.559,-327.542)" width="10" x="1533.117063355078" y="655.0833384195963"/></g>
  <g id="499">
   <use class="kv10" height="20" transform="rotate(0,1293.9,676.361) scale(2,2) translate(-641.948,-328.181)" width="10" x="1283.896632940361" xlink:href="#Breaker:小车断路器_0" y="656.3611128065321" zvalue="506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219516419" ObjectName="#2发电机062断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219516419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1293.9,676.361) scale(2,2) translate(-641.948,-328.181)" width="10" x="1283.896632940361" y="656.3611128065321"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="358">
   <use class="kv110" height="30" transform="rotate(0,514,270.667) scale(-1.11111,-0.814815) translate(-975.767,-605.626)" width="15" x="505.6666666666666" xlink:href="#Disconnector:刀闸_0" y="258.4444580078124" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454804832258" ObjectName="110kV勐二一线1611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454804832258"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,514,270.667) scale(-1.11111,-0.814815) translate(-975.767,-605.626)" width="15" x="505.6666666666666" y="258.4444580078124"/></g>
  <g id="357">
   <use class="kv110" height="30" transform="rotate(0,514,162.667) scale(-1.11111,-0.814815) translate(-975.767,-365.081)" width="15" x="505.6666666931577" xlink:href="#Disconnector:刀闸_0" y="150.4444444444445" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454804766722" ObjectName="110kV勐二一线1616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454804766722"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,514,162.667) scale(-1.11111,-0.814815) translate(-975.767,-365.081)" width="15" x="505.6666666931577" y="150.4444444444445"/></g>
  <g id="345">
   <use class="kv110" height="30" transform="rotate(0,829.222,242.667) scale(-1.11111,-0.814815) translate(-1574.69,-543.263)" width="15" x="820.8888888888888" xlink:href="#Disconnector:刀闸_0" y="230.4444581137762" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454804373506" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454804373506"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,829.222,242.667) scale(-1.11111,-0.814815) translate(-1574.69,-543.263)" width="15" x="820.8888888888888" y="230.4444581137762"/></g>
  <g id="335">
   <use class="kv110" height="30" transform="rotate(0,567.333,340.222) scale(1.11111,0.814815) translate(-55.9,74.5455)" width="15" x="559.0000101725263" xlink:href="#Disconnector:刀闸_0" y="328" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454804176898" ObjectName="#4主变110kV侧1041隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454804176898"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,567.333,340.222) scale(1.11111,0.814815) translate(-55.9,74.5455)" width="15" x="559.0000101725263" y="328"/></g>
  <g id="763">
   <use class="kv35" height="30" transform="rotate(0,1478.5,266.722) scale(-1.11111,-0.814815) translate(-2808.32,-596.841)" width="15" x="1470.166666666667" xlink:href="#Disconnector:刀闸_0" y="254.4999862247043" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803783682" ObjectName="35kV光明硅厂Ⅰ回线3611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454803783682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1478.5,266.722) scale(-1.11111,-0.814815) translate(-2808.32,-596.841)" width="15" x="1470.166666666667" y="254.4999862247043"/></g>
  <g id="762">
   <use class="kv35" height="30" transform="rotate(0,1478.5,166.722) scale(-1.11111,-0.814815) translate(-2808.32,-374.114)" width="15" x="1470.166666693158" xlink:href="#Disconnector:刀闸_0" y="154.4999862247043" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803718146" ObjectName="35kV光明硅厂Ⅰ回线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454803718146"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1478.5,166.722) scale(-1.11111,-0.814815) translate(-2808.32,-374.114)" width="15" x="1470.166666693158" y="154.4999862247043"/></g>
  <g id="848">
   <use class="kv35" height="30" transform="rotate(0,1757.64,359.611) scale(-1.11111,-0.814815) translate(-3338.69,-803.73)" width="15" x="1749.309734114727" xlink:href="#Disconnector:刀闸_0" y="347.3889025582208" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805422082" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454805422082"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1757.64,359.611) scale(-1.11111,-0.814815) translate(-3338.69,-803.73)" width="15" x="1749.309734114727" y="347.3889025582208"/></g>
  <g id="237">
   <use class="kv10" height="33" transform="rotate(0,700,676.25) scale(1,-1) translate(0,-1352.5)" width="14" x="693" xlink:href="#Disconnector:手车隔离开关13_0" y="659.75" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802997250" ObjectName="10kVⅣ母电压互感器0904隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454802997250"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,700,676.25) scale(1,-1) translate(0,-1352.5)" width="14" x="693" y="659.75"/></g>
  <g id="234">
   <use class="kv110" height="30" transform="rotate(0,885.45,340.222) scale(1.11111,0.814815) translate(-87.7117,74.5455)" width="15" x="877.1170735276038" xlink:href="#Disconnector:刀闸_0" y="328" zvalue="218"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802931714" ObjectName="#3主变110kV侧1031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454802931714"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,885.45,340.222) scale(1.11111,0.814815) translate(-87.7117,74.5455)" width="15" x="877.1170735276038" y="328"/></g>
  <g id="196">
   <use class="kv10" height="33" transform="rotate(0,790.117,655) scale(1,-1) translate(0,-1310)" width="14" x="783.1170633550773" xlink:href="#Disconnector:手车隔离开关13_0" y="638.5" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802472962" ObjectName="10kVⅢ母电压互感器0903隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454802472962"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,790.117,655) scale(1,-1) translate(0,-1310)" width="14" x="783.1170633550773" y="638.5"/></g>
  <g id="342">
   <use class="kv110" height="30" transform="rotate(0,1379.45,340.222) scale(1.11111,0.814815) translate(-137.112,74.5455)" width="15" x="1371.117073527604" xlink:href="#Disconnector:刀闸_0" y="328" zvalue="267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802407426" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454802407426"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1379.45,340.222) scale(1.11111,0.814815) translate(-137.112,74.5455)" width="15" x="1371.117073527604" y="328"/></g>
  <g id="169">
   <use class="kv35" height="30" transform="rotate(0,1694.5,267.722) scale(-1.11111,-0.814815) translate(-3218.72,-599.068)" width="15" x="1686.166666666667" xlink:href="#Disconnector:刀闸_0" y="255.4999862247043" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454801948674" ObjectName="35kV备用线3621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454801948674"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1694.5,267.722) scale(-1.11111,-0.814815) translate(-3218.72,-599.068)" width="15" x="1686.166666666667" y="255.4999862247043"/></g>
  <g id="163">
   <use class="kv35" height="30" transform="rotate(0,1694.5,167.722) scale(-1.11111,-0.814815) translate(-3218.72,-376.341)" width="15" x="1686.166666693158" xlink:href="#Disconnector:刀闸_0" y="155.4999862247043" zvalue="322"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454801883138" ObjectName="35kV备用线3626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454801883138"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1694.5,167.722) scale(-1.11111,-0.814815) translate(-3218.72,-376.341)" width="15" x="1686.166666693158" y="155.4999862247043"/></g>
  <g id="183">
   <use class="kv35" height="30" transform="rotate(0,1641.22,340.222) scale(1.11111,0.814815) translate(-163.289,74.5455)" width="15" x="1632.891298801276" xlink:href="#Disconnector:刀闸_0" y="328" zvalue="340"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454801620994" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454801620994"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1641.22,340.222) scale(1.11111,0.814815) translate(-163.289,74.5455)" width="15" x="1632.891298801276" y="328"/></g>
  <g id="184">
   <use class="kv35" height="30" transform="rotate(0,1641.32,446.222) scale(1.11111,0.814815) translate(-163.298,98.6364)" width="15" x="1632.983951438215" xlink:href="#Disconnector:刀闸_0" y="434" zvalue="345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454801555458" ObjectName="#2主变35kV侧3026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454801555458"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1641.32,446.222) scale(1.11111,0.814815) translate(-163.298,98.6364)" width="15" x="1632.983951438215" y="434"/></g>
  <g id="368">
   <use class="kv35" height="30" transform="rotate(90,1447.25,132.972) scale(-1.11111,-0.814815) translate(-2748.94,-298.943)" width="15" x="1438.916666693158" xlink:href="#Disconnector:刀闸_0" y="120.7499862247043" zvalue="381"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805159938" ObjectName="35kV光明硅厂Ⅰ回线3619隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454805159938"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1447.25,132.972) scale(-1.11111,-0.814815) translate(-2748.94,-298.943)" width="15" x="1438.916666693158" y="120.7499862247043"/></g>
  <g id="377">
   <use class="kv35" height="30" transform="rotate(90,1664.5,132.193) scale(-1.11111,-0.814815) translate(-3161.72,-297.207)" width="15" x="1656.166666693158" xlink:href="#Disconnector:刀闸_0" y="119.9704304147168" zvalue="388"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805225474" ObjectName="35kV备用线3629隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454805225474"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1664.5,132.193) scale(-1.11111,-0.814815) translate(-3161.72,-297.207)" width="15" x="1656.166666693158" y="119.9704304147168"/></g>
  <g id="387">
   <use class="kv10" height="30" transform="rotate(0,615.975,665.472) scale(1.11111,0.814815) translate(-60.7641,148.466)" width="15" x="607.6413089738021" xlink:href="#Disconnector:刀闸_0" y="653.25" zvalue="403"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805553154" ObjectName="#3站用变0681隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454805553154"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,615.975,665.472) scale(1.11111,0.814815) translate(-60.7641,148.466)" width="15" x="607.6413089738021" y="653.25"/></g>
  <g id="394">
   <use class="kv10" height="36" transform="rotate(0,564,775.25) scale(1,1) translate(0,0)" width="14" x="557" xlink:href="#Disconnector:手车刀闸_0" y="757.25" zvalue="413"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805749762" ObjectName="#4发电机0942隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454805749762"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,564,775.25) scale(1,1) translate(0,0)" width="14" x="557" y="757.25"/></g>
  <g id="407">
   <use class="kv10" height="33" transform="rotate(0,483.75,763.75) scale(1,-1) translate(0,-1527.5)" width="14" x="476.75" xlink:href="#Disconnector:手车隔离开关13_0" y="747.25" zvalue="428"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805946370" ObjectName="#4发电机0941隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454805946370"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,483.75,763.75) scale(1,-1) translate(0,-1527.5)" width="14" x="476.75" y="747.25"/></g>
  <g id="411">
   <use class="kv10" height="33" transform="rotate(0,985,845) scale(1,-1) translate(0,-1690)" width="14" x="978" xlink:href="#Disconnector:手车隔离开关13_0" y="828.5" zvalue="434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806077442" ObjectName="#3发电机0932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454806077442"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,985,845) scale(1,-1) translate(0,-1690)" width="14" x="978" y="828.5"/></g>
  <g id="412">
   <use class="kv10" height="33" transform="rotate(0,901.512,841.75) scale(1,-1) translate(0,-1683.5)" width="14" x="894.5116559724929" xlink:href="#Disconnector:手车隔离开关13_0" y="825.25" zvalue="436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806142978" ObjectName="#3发电机0931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454806142978"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,901.512,841.75) scale(1,-1) translate(0,-1683.5)" width="14" x="894.5116559724929" y="825.25"/></g>
  <g id="459">
   <use class="kv110" height="30" transform="rotate(0,1100.45,337.722) scale(1.11111,0.814815) translate(-109.212,73.9773)" width="15" x="1092.117073527604" xlink:href="#Disconnector:刀闸_0" y="325.5" zvalue="447"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806994946" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454806994946"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1100.45,337.722) scale(1.11111,0.814815) translate(-109.212,73.9773)" width="15" x="1092.117073527604" y="325.5"/></g>
  <g id="443">
   <use class="kv10" height="33" transform="rotate(0,1200.12,654.5) scale(1,-1) translate(0,-1309)" width="14" x="1193.117063355077" xlink:href="#Disconnector:手车隔离开关13_0" y="638" zvalue="471"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806536194" ObjectName="10kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454806536194"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1200.12,654.5) scale(1,-1) translate(0,-1309)" width="14" x="1193.117063355077" y="638"/></g>
  <g id="436">
   <use class="kv10" height="33" transform="rotate(0,1192,846.5) scale(1,-1) translate(0,-1693)" width="14" x="1185" xlink:href="#Disconnector:手车隔离开关13_0" y="830" zvalue="479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806274050" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454806274050"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1192,846.5) scale(1,-1) translate(0,-1693)" width="14" x="1185" y="830"/></g>
  <g id="435">
   <use class="kv10" height="33" transform="rotate(0,1116.51,843.5) scale(1,-1) translate(0,-1687)" width="14" x="1109.511655972493" xlink:href="#Disconnector:手车隔离开关13_0" y="827" zvalue="480"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806208514" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454806208514"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1116.51,843.5) scale(1,-1) translate(0,-1687)" width="14" x="1109.511655972493" y="827"/></g>
  <g id="494">
   <use class="kv10" height="33" transform="rotate(0,1643.87,660.75) scale(1,-1) translate(0,-1321.5)" width="14" x="1636.867063355077" xlink:href="#Disconnector:手车隔离开关13_0" y="644.25" zvalue="514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807519234" ObjectName="10kVⅡ母电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454807519234"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1643.87,660.75) scale(1,-1) translate(0,-1321.5)" width="14" x="1636.867063355077" y="644.25"/></g>
  <g id="488">
   <use class="kv10" height="33" transform="rotate(0,1425.75,846.25) scale(1,-1) translate(0,-1692.5)" width="14" x="1418.75" xlink:href="#Disconnector:手车隔离开关13_0" y="829.75" zvalue="521"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807257090" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454807257090"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1425.75,846.25) scale(1,-1) translate(0,-1692.5)" width="14" x="1418.75" y="829.75"/></g>
  <g id="487">
   <use class="kv10" height="33" transform="rotate(0,1350.26,847.25) scale(1,-1) translate(0,-1694.5)" width="14" x="1343.261655972493" xlink:href="#Disconnector:手车隔离开关13_0" y="830.75" zvalue="522"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807191554" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454807191554"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1350.26,847.25) scale(1,-1) translate(0,-1694.5)" width="14" x="1343.261655972493" y="830.75"/></g>
  <g id="89">
   <use class="kv10" height="26" transform="rotate(0,564.444,575.778) scale(1.11111,1.62393) translate(-55.6667,-213.109)" width="14" x="556.6666666666665" xlink:href="#Disconnector:联体手车刀闸_0" y="554.6666666666666" zvalue="549"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807715842" ObjectName="#4主变10kV侧0041隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454807715842"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,564.444,575.778) scale(1.11111,1.62393) translate(-55.6667,-213.109)" width="14" x="556.6666666666665" y="554.6666666666666"/></g>
  <g id="93">
   <use class="kv10" height="26" transform="rotate(0,882.778,573.556) scale(1.11111,1.62393) translate(-87.5,-212.255)" width="14" x="874.9999999999999" xlink:href="#Disconnector:联体手车刀闸_0" y="552.4444444444445" zvalue="553"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807781378" ObjectName="#3主变10kV侧0031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454807781378"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,882.778,573.556) scale(1.11111,1.62393) translate(-87.5,-212.255)" width="14" x="874.9999999999999" y="552.4444444444445"/></g>
  <g id="96">
   <use class="kv10" height="26" transform="rotate(0,1100,570.222) scale(1.11111,1.62393) translate(-109.222,-210.974)" width="14" x="1092.222222222222" xlink:href="#Disconnector:联体手车刀闸_0" y="549.1111111111111" zvalue="557"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807846914" ObjectName="#1主变110kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454807846914"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1100,570.222) scale(1.11111,1.62393) translate(-109.222,-210.974)" width="14" x="1092.222222222222" y="549.1111111111111"/></g>
  <g id="99">
   <use class="kv10" height="26" transform="rotate(0,1378.33,576.889) scale(1.11111,1.62393) translate(-137.056,-213.536)" width="14" x="1370.555555555555" xlink:href="#Disconnector:联体手车刀闸_0" y="555.7777777777778" zvalue="561"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807912450" ObjectName="#2主变110kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454807912450"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1378.33,576.889) scale(1.11111,1.62393) translate(-137.056,-213.536)" width="14" x="1370.555555555555" y="555.7777777777778"/></g>
  <g id="143">
   <use class="kv110" height="30" transform="rotate(0,1380.01,430.222) scale(1.11111,0.814815) translate(-137.167,95)" width="15" x="1371.672629083159" xlink:href="#Disconnector:刀闸_0" y="418" zvalue="583"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454808502274" ObjectName="#2主变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454808502274"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1380.01,430.222) scale(1.11111,0.814815) translate(-137.167,95)" width="15" x="1371.672629083159" y="418"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="356">
   <path class="kv110" d="M 513.9 282.48 L 513.9 301.67" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@0" LinkObjectIDznd="362@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.9 282.48 L 513.9 301.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="355">
   <path class="kv110" d="M 513.93 258.65 L 513.93 233.72" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@1" LinkObjectIDznd="359@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.93 258.65 L 513.93 233.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="354">
   <path class="kv110" d="M 513.96 212.48 L 513.9 174.48" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="359@0" LinkObjectIDznd="357@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.96 212.48 L 513.9 174.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="352">
   <path class="kv110" d="M 513.93 150.65 L 513.93 130.22" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="357@1" LinkObjectIDznd="353@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.93 150.65 L 513.93 130.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv110" d="M 829.12 254.48 L 829.12 301.67" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="345@0" LinkObjectIDznd="362@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.12 254.48 L 829.12 301.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="336">
   <path class="kv110" d="M 829.15 230.65 L 829.15 179.37" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="345@1" LinkObjectIDznd="325@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.15 230.65 L 829.15 179.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="333">
   <path class="kv110" d="M 567.4 352.24 L 567.4 385.59" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@1" LinkObjectIDznd="334@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 567.4 352.24 L 567.4 385.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="329">
   <path class="kv110" d="M 567.43 328.4 L 567.43 301.67" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@0" LinkObjectIDznd="362@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 567.43 328.4 L 567.43 301.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="kv10" d="M 431.03 656.61 L 431.03 627.89" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="360@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 431.03 656.61 L 431.03 627.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv35" d="M 1478.4 278.54 L 1478.4 302.67" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="763@0" LinkObjectIDznd="361@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1478.4 278.54 L 1478.4 302.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv35" d="M 1478.43 254.71 L 1478.43 229.78" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="763@1" LinkObjectIDznd="764@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1478.43 254.71 L 1478.43 229.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv35" d="M 1478.46 208.54 L 1478.4 178.54" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="764@0" LinkObjectIDznd="762@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1478.46 208.54 L 1478.4 178.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv35" d="M 1478.43 154.71 L 1478.43 117.88" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="762@1" LinkObjectIDznd="758@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1478.43 154.71 L 1478.43 117.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv10" d="M 431 791.88 L 431.03 693.11" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 431 791.88 L 431.03 693.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv110" d="M 550.78 135.65 L 513.93 135.65" stroke-width="1" zvalue="128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="348@0" LinkObjectIDznd="352" MaxPinNum="2"/>
   </metadata>
  <path d="M 550.78 135.65 L 513.93 135.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv110" d="M 550.78 246.28 L 513.93 246.28" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="351@0" LinkObjectIDznd="355" MaxPinNum="2"/>
   </metadata>
  <path d="M 550.78 246.28 L 513.93 246.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv110" d="M 872.5 204.78 L 829.15 204.78" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="344@0" LinkObjectIDznd="336" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.5 204.78 L 829.15 204.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv110" d="M 872.5 272.61 L 829.12 272.61" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@0" LinkObjectIDznd="339" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.5 272.61 L 829.12 272.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv110" d="M 567.41 406.83 L 567.41 448.84" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="334@1" LinkObjectIDznd="320@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 567.41 406.83 L 567.41 448.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv110" d="M 599.61 360.06 L 567.4 360.06" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="333" MaxPinNum="2"/>
   </metadata>
  <path d="M 599.61 360.06 L 567.4 360.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv110" d="M 567.38 475.12 L 472.07 475.12 L 472.07 489.85" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@2" LinkObjectIDznd="248@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 567.38 475.12 L 472.07 475.12 L 472.07 489.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 699.99 692.55 L 700.07 723.25" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="294@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.99 692.55 L 700.07 723.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv110" d="M 885.52 352.24 L 885.52 385.59" stroke-width="1" zvalue="222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@1" LinkObjectIDznd="233@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.52 352.24 L 885.52 385.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv10" d="M 845.15 656.61 L 845.15 626.89" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="235@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.15 656.61 L 845.15 626.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv10" d="M 845.12 834.88 L 845.15 693.11" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="229@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.12 834.88 L 845.15 693.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv110" d="M 885.53 406.83 L 885.53 448.84" stroke-width="1" zvalue="247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="226@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.53 406.83 L 885.53 448.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv110" d="M 917.73 360.06 L 885.52 360.06" stroke-width="1" zvalue="248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="232" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.73 360.06 L 885.52 360.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv110" d="M 885.5 475.12 L 794.19 475.12 L 794.19 489.85" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@2" LinkObjectIDznd="204@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.5 475.12 L 794.19 475.12 L 794.19 489.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 790.12 639 L 790.12 626.89" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@1" LinkObjectIDznd="235@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 790.12 639 L 790.12 626.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 790.11 671.3 L 790.19 692" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 790.11 671.3 L 790.19 692" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv110" d="M 885.55 328.4 L 885.55 301.67" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="362@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.55 328.4 L 885.55 301.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv110" d="M 1379.52 352.24 L 1379.52 373.59" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342@1" LinkObjectIDznd="340@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.52 352.24 L 1379.52 373.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv10" d="M 1449.62 658.83 L 1449.62 626.89" stroke-width="1" zvalue="290"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="343@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.62 658.83 L 1449.62 626.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv10" d="M 1449.62 714.79 L 1449.62 695.33" stroke-width="1" zvalue="291"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="308@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.62 714.79 L 1449.62 695.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv110" d="M 1411.73 360.06 L 1379.52 360.06" stroke-width="1" zvalue="295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="192" MaxPinNum="2"/>
   </metadata>
  <path d="M 1411.73 360.06 L 1379.52 360.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv110" d="M 1379.15 474.5 L 1296.94 474.5 L 1296.94 492.33" stroke-width="1" zvalue="297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@1" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.15 474.5 L 1296.94 474.5 L 1296.94 492.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv110" d="M 1379.55 328.4 L 1379.55 301.67" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342@0" LinkObjectIDznd="362@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.55 328.4 L 1379.55 301.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv35" d="M 1503.28 132.9 L 1478.43 132.9" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="755@0" LinkObjectIDznd="312" MaxPinNum="2"/>
   </metadata>
  <path d="M 1503.28 132.9 L 1478.43 132.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 1694.4 279.54 L 1694.4 302.67" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="361@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1694.4 279.54 L 1694.4 302.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv35" d="M 1694.43 255.71 L 1694.43 230.78" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@1" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1694.43 255.71 L 1694.43 230.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv35" d="M 1694.46 209.54 L 1694.4 179.54" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1694.46 209.54 L 1694.4 179.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv35" d="M 1694.43 155.71 L 1694.43 117.8" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1694.43 155.71 L 1694.43 117.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 1719.28 132.12 L 1694.43 132.12" stroke-width="1" zvalue="332"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1719.28 132.12 L 1694.43 132.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv35" d="M 1641.29 352.24 L 1641.29 385.59" stroke-width="1" zvalue="344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@1" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1641.29 352.24 L 1641.29 385.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv35" d="M 1641.32 328.4 L 1641.32 302.67" stroke-width="1" zvalue="346"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="361@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1641.32 328.4 L 1641.32 302.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv35" d="M 1641.41 406.83 L 1641.41 434.4" stroke-width="1" zvalue="348"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@1" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1641.41 406.83 L 1641.41 434.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv10" d="M 700 660.25 L 700 627.89" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="360@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 700 660.25 L 700 627.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="364">
   <path class="kv35" d="M 1446.94 244.62 L 1478.43 244.62" stroke-width="1" zvalue="375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="363@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.94 244.62 L 1478.43 244.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="366">
   <path class="kv35" d="M 1670.94 245.87 L 1694.43 245.87" stroke-width="1" zvalue="378"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="365@0" LinkObjectIDznd="161" MaxPinNum="2"/>
   </metadata>
  <path d="M 1670.94 245.87 L 1694.43 245.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="372">
   <path class="kv35" d="M 1459.26 132.9 L 1478.43 132.9" stroke-width="1" zvalue="385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@1" LinkObjectIDznd="312" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.26 132.9 L 1478.43 132.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="379">
   <path class="kv35" d="M 1676.51 132.12 L 1694.43 132.12" stroke-width="1" zvalue="392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="377@1" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1676.51 132.12 L 1694.43 132.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv35" d="M 1441.1 494.15 L 1641.39 494.15 L 1641.39 458.24" stroke-width="1" zvalue="393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@2" LinkObjectIDznd="184@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1441.1 494.15 L 1641.39 494.15 L 1641.39 458.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="381">
   <path class="kv10" d="M 431.02 731.03 L 400.78 731.03 L 400.78 740.63" stroke-width="1" zvalue="394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309" LinkObjectIDznd="382@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 431.02 731.03 L 400.78 731.03 L 400.78 740.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="384">
   <path class="kv10" d="M 617.5 740.54 L 617.5 720.08" stroke-width="1" zvalue="399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="383@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.5 740.54 L 617.5 720.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="388">
   <path class="kv10" d="M 616.04 698.84 L 616.04 677.49" stroke-width="1" zvalue="404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="383@0" LinkObjectIDznd="387@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 616.04 698.84 L 616.04 677.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="389">
   <path class="kv10" d="M 616.07 653.65 L 616.07 627.89" stroke-width="1" zvalue="405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="387@0" LinkObjectIDznd="360@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 616.07 653.65 L 616.07 627.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="kv10" d="M 664.28 675.63 L 664.28 653 L 700 653" stroke-width="1" zvalue="409"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="390@0" LinkObjectIDznd="138" MaxPinNum="2"/>
   </metadata>
  <path d="M 664.28 675.63 L 664.28 653 L 700 653" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="395">
   <path class="kv10" d="M 563.25 828.19 L 563.25 792.25" stroke-width="1" zvalue="414"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="394@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 563.25 828.19 L 563.25 792.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="396">
   <path class="kv10" d="M 564 758.25 L 564 729 L 483.25 729" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="394@0" LinkObjectIDznd="408" MaxPinNum="2"/>
   </metadata>
  <path d="M 564 758.25 L 564 729 L 483.25 729" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="397">
   <path class="kv10" d="M 514.41 828.35 L 514.41 808 L 563.25 808" stroke-width="1" zvalue="416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="393@0" LinkObjectIDznd="395" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.41 828.35 L 514.41 808 L 563.25 808" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="408">
   <path class="kv10" d="M 431.01 729 L 483.75 729 L 483.75 747.75" stroke-width="1" zvalue="429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="381" LinkObjectIDznd="407@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 431.01 729 L 483.75 729 L 483.75 747.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="409">
   <path class="kv10" d="M 483.74 780.05 L 483.74 801.25" stroke-width="1" zvalue="430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="407@0" LinkObjectIDznd="241@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 483.74 780.05 L 483.74 801.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="413">
   <path class="kv10" d="M 985.75 899.69 L 985.75 861.3" stroke-width="1" zvalue="437"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@0" LinkObjectIDznd="411@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 985.75 899.69 L 985.75 861.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="415">
   <path class="kv10" d="M 932.66 907.85 L 932.66 883 L 985.75 883" stroke-width="1" zvalue="439"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="402@0" LinkObjectIDznd="413" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.66 907.85 L 932.66 883 L 985.75 883" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="416">
   <path class="kv10" d="M 901.67 880.75 L 901.67 858.05" stroke-width="1" zvalue="440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403@0" LinkObjectIDznd="412@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.67 880.75 L 901.67 858.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="457">
   <path class="kv110" d="M 1100.52 349.74 L 1100.52 383.09" stroke-width="1" zvalue="451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="459@1" LinkObjectIDznd="458@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.52 349.74 L 1100.52 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="454">
   <path class="kv10" d="M 1060.15 654.11 L 1060.15 624.39" stroke-width="1" zvalue="456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="455@0" LinkObjectIDznd="460@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.15 654.11 L 1060.15 624.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="452">
   <path class="kv10" d="M 1060.12 833.38 L 1060.15 690.61" stroke-width="1" zvalue="459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="451@0" LinkObjectIDznd="455@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.12 833.38 L 1060.15 690.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="449">
   <path class="kv110" d="M 1100.53 404.33 L 1100.53 446.34" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="458@1" LinkObjectIDznd="453@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.53 404.33 L 1100.53 446.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="448">
   <path class="kv110" d="M 1132.73 357.56 L 1100.52 357.56" stroke-width="1" zvalue="465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="456@0" LinkObjectIDznd="457" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.73 357.56 L 1100.52 357.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="445">
   <path class="kv110" d="M 1100.5 472.62 L 1009.19 472.62 L 1009.19 487.35" stroke-width="1" zvalue="468"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@2" LinkObjectIDznd="446@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.5 472.62 L 1009.19 472.62 L 1009.19 487.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="442">
   <path class="kv10" d="M 1200.12 638.5 L 1200.12 624.39" stroke-width="1" zvalue="473"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="443@1" LinkObjectIDznd="460@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1200.12 638.5 L 1200.12 624.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="441">
   <path class="kv10" d="M 1200.11 670.8 L 1200.11 699" stroke-width="1" zvalue="474"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="443@0" LinkObjectIDznd="450@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1200.11 670.8 L 1200.11 699" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv110" d="M 1100.55 325.9 L 1100.55 301.67" stroke-width="1" zvalue="475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="459@0" LinkObjectIDznd="362@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.55 325.9 L 1100.55 301.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="434">
   <path class="kv10" d="M 1192.25 902.44 L 1192.25 862.8" stroke-width="1" zvalue="481"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="437@0" LinkObjectIDznd="436@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.25 902.44 L 1192.25 862.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="432">
   <path class="kv10" d="M 1156.66 910.35 L 1156.66 884.5 L 1192.25 884.5" stroke-width="1" zvalue="483"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="438@0" LinkObjectIDznd="434" MaxPinNum="2"/>
   </metadata>
  <path d="M 1156.66 910.35 L 1156.66 884.5 L 1192.25 884.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="461">
   <path class="kv10" d="M 1116.5 887 L 1116.5 859.8" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="439@0" LinkObjectIDznd="435@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1116.5 887 L 1116.5 859.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="462">
   <path class="kv10" d="M 1192 830.5 L 1192 800.5 L 1060.12 800.5" stroke-width="1" zvalue="487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@1" LinkObjectIDznd="452" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192 830.5 L 1192 800.5 L 1060.12 800.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="463">
   <path class="kv10" d="M 1116.51 827.5 L 1116.51 800.5" stroke-width="1" zvalue="488"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="435@1" LinkObjectIDznd="462" MaxPinNum="2"/>
   </metadata>
  <path d="M 1116.51 827.5 L 1116.51 800.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="464">
   <path class="kv10" d="M 985 829 L 985 798 L 845.12 798" stroke-width="1" zvalue="489"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="411@1" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 985 829 L 985 798 L 845.12 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="465">
   <path class="kv10" d="M 901.51 825.75 L 901.51 798" stroke-width="1" zvalue="490"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="412@1" LinkObjectIDznd="464" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.51 825.75 L 901.51 798" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="470">
   <path class="kv10" d="M 1001.87 656.33 L 1001.87 624.39" stroke-width="1" zvalue="494"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="471@0" LinkObjectIDznd="460@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.87 656.33 L 1001.87 624.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="469">
   <path class="kv10" d="M 1001.87 712.29 L 1001.87 692.83" stroke-width="1" zvalue="495"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="468@0" LinkObjectIDznd="471@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.87 712.29 L 1001.87 692.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="476">
   <path class="kv10" d="M 1543.12 656.58 L 1543.12 626.89" stroke-width="1" zvalue="501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="477@0" LinkObjectIDznd="343@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1543.12 656.58 L 1543.12 626.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="475">
   <path class="kv10" d="M 1542.12 713.54 L 1542.12 693.08" stroke-width="1" zvalue="502"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="474@0" LinkObjectIDznd="477@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1542.12 713.54 L 1542.12 693.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="kv10" d="M 1293.9 657.86 L 1293.9 626.89" stroke-width="1" zvalue="508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="499@0" LinkObjectIDznd="343@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.9 657.86 L 1293.9 626.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="497">
   <path class="kv10" d="M 1292.87 836.13 L 1292.87 694.36" stroke-width="1" zvalue="509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="496@0" LinkObjectIDznd="499@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.87 836.13 L 1292.87 694.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="493">
   <path class="kv10" d="M 1643.87 644.75 L 1643.87 626.89" stroke-width="1" zvalue="516"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@1" LinkObjectIDznd="343@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1643.87 644.75 L 1643.87 626.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="492">
   <path class="kv10" d="M 1643.86 677.05 L 1643.86 705.25" stroke-width="1" zvalue="517"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@0" LinkObjectIDznd="495@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1643.86 677.05 L 1643.86 705.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="486">
   <path class="kv10" d="M 1426 902.19 L 1426 862.55" stroke-width="1" zvalue="523"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@0" LinkObjectIDznd="488@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426 902.19 L 1426 862.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="485">
   <path class="kv10" d="M 1389.41 909.1 L 1389.41 884.25 L 1426 884.25" stroke-width="1" zvalue="524"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="490@0" LinkObjectIDznd="486" MaxPinNum="2"/>
   </metadata>
  <path d="M 1389.41 909.1 L 1389.41 884.25 L 1426 884.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="484">
   <path class="kv10" d="M 1350.25 890.75 L 1350.25 863.55" stroke-width="1" zvalue="525"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="491@0" LinkObjectIDznd="487@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.25 890.75 L 1350.25 863.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="483">
   <path class="kv10" d="M 1425.75 830.25 L 1425.75 804.25 L 1292.87 804.25" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="488@1" LinkObjectIDznd="497" MaxPinNum="2"/>
   </metadata>
  <path d="M 1425.75 830.25 L 1425.75 804.25 L 1292.87 804.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="482">
   <path class="kv10" d="M 1350.26 831.25 L 1350.26 804.25" stroke-width="1" zvalue="527"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="487@1" LinkObjectIDznd="483" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.26 831.25 L 1350.26 804.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 567.35 540.02 L 567.35 548.73 L 564.43 548.73 L 564.43 557.43" stroke-width="1" zvalue="550"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@1" LinkObjectIDznd="89@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 567.35 540.02 L 567.35 548.73 L 564.43 548.73 L 564.43 557.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 564.47 594.16 L 564.47 627.89" stroke-width="1" zvalue="551"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="360@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 564.47 594.16 L 564.47 627.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 885.47 540.02 L 885.47 547.61 L 882.76 547.61 L 882.76 555.21" stroke-width="1" zvalue="554"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@1" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.47 540.02 L 885.47 547.61 L 882.76 547.61 L 882.76 555.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 882.8 591.93 L 882.8 626.89" stroke-width="1" zvalue="555"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@1" LinkObjectIDznd="235@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 882.8 591.93 L 882.8 626.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv10" d="M 1100.47 537.52 L 1100.47 551.87" stroke-width="1" zvalue="558"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@1" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.47 537.52 L 1100.47 551.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 1100.02 588.6 L 1100.02 624.39" stroke-width="1" zvalue="559"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="460@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.02 588.6 L 1100.02 624.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv10" d="M 1378.36 626.89 L 1378.36 595.27" stroke-width="1" zvalue="562"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="343@4" LinkObjectIDznd="99@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1378.36 626.89 L 1378.36 595.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1378.32 558.54 L 1378.32 538.85" stroke-width="1" zvalue="563"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="347@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1378.32 558.54 L 1378.32 538.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv35" d="M 1757.58 347.6 L 1757.58 302.67" stroke-width="1" zvalue="574"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="848@1" LinkObjectIDznd="361@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1757.58 347.6 L 1757.58 302.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1758.73 410.44 L 1758.73 371.43" stroke-width="1" zvalue="575"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="848@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1758.73 410.44 L 1758.73 371.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv35" d="M 1723.28 332.54 L 1757.58 332.54" stroke-width="1" zvalue="576"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 1723.28 332.54 L 1757.58 332.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv35" d="M 1726.06 398.1 L 1758.73 398.1" stroke-width="1" zvalue="577"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 1726.06 398.1 L 1758.73 398.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv35" d="M 1613.28 370.32 L 1641.29 370.32" stroke-width="1" zvalue="578"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1613.28 370.32 L 1641.29 370.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv35" d="M 1613.83 422.54 L 1641.41 422.54" stroke-width="1" zvalue="579"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 1613.83 422.54 L 1641.41 422.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv110" d="M 1379.53 394.83 L 1379.53 418.4" stroke-width="1" zvalue="584"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@1" LinkObjectIDznd="143@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.53 394.83 L 1379.53 418.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv110" d="M 1380.07 442.24 L 1380.07 449.45" stroke-width="1" zvalue="585"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@1" LinkObjectIDznd="347@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1380.07 442.24 L 1380.07 449.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv110" d="M 1412.95 407.83 L 1379.53 407.83" stroke-width="1" zvalue="589"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="144" MaxPinNum="2"/>
   </metadata>
  <path d="M 1412.95 407.83 L 1379.53 407.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv35" d="M 1621.36 132.75 L 1652.68 132.75" stroke-width="1" zvalue="592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@0" LinkObjectIDznd="377@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621.36 132.75 L 1652.68 132.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv35" d="M 1435.43 132.87 L 1413.11 132.87" stroke-width="1" zvalue="593"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@0" LinkObjectIDznd="167@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1435.43 132.87 L 1413.11 132.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="353">
   <use class="kv110" height="30" transform="rotate(0,513.932,108.222) scale(6.34921,1.48148) translate(-414.266,-27.95)" width="7" x="491.7098173330401" xlink:href="#ACLineSegment:线路_0" y="85.99999999999994" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249302499333" ObjectName="110kV勐二一线"/>
   <cge:TPSR_Ref TObjectID="8444249302499333_5066549684535297"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,513.932,108.222) scale(6.34921,1.48148) translate(-414.266,-27.95)" width="7" x="491.7098173330401" y="85.99999999999994"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="351">
   <use class="kv110" height="20" transform="rotate(270,561.611,246.222) scale(-1.11111,1.11111) translate(-1066.51,-23.5111)" width="10" x="556.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="235.1111111111111" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454804635650" ObjectName="110kV勐二一线16117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454804635650"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,561.611,246.222) scale(-1.11111,1.11111) translate(-1066.51,-23.5111)" width="10" x="556.0555555555555" y="235.1111111111111"/></g>
  <g id="348">
   <use class="kv110" height="20" transform="rotate(270,561.611,135.594) scale(-1.11111,1.11111) translate(-1066.51,-12.4483)" width="10" x="556.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="124.4833333333334" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454804504578" ObjectName="110kV勐二一线16167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454804504578"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,561.611,135.594) scale(-1.11111,1.11111) translate(-1066.51,-12.4483)" width="10" x="556.0555556615193" y="124.4833333333334"/></g>
  <g id="344">
   <use class="kv110" height="20" transform="rotate(270,883.333,204.722) scale(-1.11111,1.11111) translate(-1677.78,-19.3611)" width="10" x="877.7777845594618" xlink:href="#GroundDisconnector:地刀_0" y="193.6111008326212" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454804307970" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454804307970"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,883.333,204.722) scale(-1.11111,1.11111) translate(-1677.78,-19.3611)" width="10" x="877.7777845594618" y="193.6111008326212"/></g>
  <g id="330">
   <use class="kv110" height="20" transform="rotate(270,610.444,360) scale(-1.11111,1.11111) translate(-1159.29,-34.8889)" width="10" x="604.888899061415" xlink:href="#GroundDisconnector:地刀_0" y="348.8888888888889" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454804111362" ObjectName="#4主变110kV侧10417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454804111362"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,610.444,360) scale(-1.11111,1.11111) translate(-1159.29,-34.8889)" width="10" x="604.888899061415" y="348.8888888888889"/></g>
  <g id="324">
   <use class="kv110" height="20" transform="rotate(270,883.333,272.556) scale(-1.11111,1.11111) translate(-1677.78,-26.1444)" width="10" x="877.7777845594618" xlink:href="#GroundDisconnector:地刀_0" y="261.4444444444444" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803914754" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454803914754"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,883.333,272.556) scale(-1.11111,1.11111) translate(-1677.78,-26.1444)" width="10" x="877.7777845594618" y="261.4444444444444"/></g>
  <g id="755">
   <use class="kv35" height="20" transform="rotate(270,1514.11,132.849) scale(-1.11111,1.11111) translate(-2876.26,-12.1738)" width="10" x="1508.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="121.7375813090311" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803587074" ObjectName="35kV光明硅厂Ⅰ回线36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454803587074"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1514.11,132.849) scale(-1.11111,1.11111) translate(-2876.26,-12.1738)" width="10" x="1508.555555555556" y="121.7375813090311"/></g>
  <g id="248">
   <use class="kv110" height="25" transform="rotate(0,479,507) scale(1.4,1.4) translate(-132.857,-139.857)" width="20" x="465" xlink:href="#GroundDisconnector:中性点地刀11_0" y="489.5" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803324930" ObjectName="#4主变110kV侧1040中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454803324930"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,479,507) scale(1.4,1.4) translate(-132.857,-139.857)" width="20" x="465" y="489.5"/></g>
  <g id="231">
   <use class="kv110" height="20" transform="rotate(270,928.562,360) scale(-1.11111,1.11111) translate(-1763.71,-34.8889)" width="10" x="923.0059624164924" xlink:href="#GroundDisconnector:地刀_0" y="348.8888888888889" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802866178" ObjectName="#3主变110kV侧10317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454802866178"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,928.562,360) scale(-1.11111,1.11111) translate(-1763.71,-34.8889)" width="10" x="923.0059624164924" y="348.8888888888889"/></g>
  <g id="204">
   <use class="kv110" height="25" transform="rotate(0,801.117,507) scale(1.4,1.4) translate(-224.891,-139.857)" width="20" x="787.1170633550773" xlink:href="#GroundDisconnector:中性点地刀11_0" y="489.5" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802604034" ObjectName="#3主变110kV侧1030中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454802604034"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,801.117,507) scale(1.4,1.4) translate(-224.891,-139.857)" width="20" x="787.1170633550773" y="489.5"/></g>
  <g id="338">
   <use class="kv110" height="20" transform="rotate(270,1422.56,360) scale(-1.11111,1.11111) translate(-2702.31,-34.8889)" width="10" x="1417.005962416492" xlink:href="#GroundDisconnector:地刀_0" y="348.8888888888889" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802341890" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454802341890"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1422.56,360) scale(-1.11111,1.11111) translate(-2702.31,-34.8889)" width="10" x="1417.005962416492" y="348.8888888888889"/></g>
  <g id="230">
   <use class="kv110" height="40" transform="rotate(0,1295.12,503) scale(0.7,-0.875) translate(549.05,-1080.36)" width="40" x="1281.117063355077" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="485.5" zvalue="296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802210818" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454802210818"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1295.12,503) scale(0.7,-0.875) translate(549.05,-1080.36)" width="40" x="1281.117063355077" y="485.5"/></g>
  <g id="158">
   <use class="kv35" height="20" transform="rotate(270,1730.11,132.069) scale(-1.11111,1.11111) translate(-3286.66,-12.0958)" width="10" x="1724.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="120.9580254990436" zvalue="329"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454801752066" ObjectName="35kV备用线36267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454801752066"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1730.11,132.069) scale(-1.11111,1.11111) translate(-3286.66,-12.0958)" width="10" x="1724.555555555556" y="120.9580254990436"/></g>
  <g id="363">
   <use class="kv35" height="20" transform="rotate(90,1436.11,244.569) scale(1.11111,1.11111) translate(-143.056,-23.3458)" width="10" x="1430.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="233.4580254990436" zvalue="374"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454804963330" ObjectName="35kV光明硅厂Ⅰ回线36117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454804963330"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1436.11,244.569) scale(1.11111,1.11111) translate(-143.056,-23.3458)" width="10" x="1430.555555555556" y="233.4580254990436"/></g>
  <g id="365">
   <use class="kv35" height="20" transform="rotate(90,1660.11,245.819) scale(1.11111,1.11111) translate(-165.456,-23.4708)" width="10" x="1654.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="234.7080254990436" zvalue="377"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805094402" ObjectName="35kV备用线36217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454805094402"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1660.11,245.819) scale(1.11111,1.11111) translate(-165.456,-23.4708)" width="10" x="1654.555555555556" y="234.7080254990436"/></g>
  <g id="456">
   <use class="kv110" height="20" transform="rotate(270,1143.56,357.5) scale(-1.11111,1.11111) translate(-2172.21,-34.6389)" width="10" x="1138.005962416492" xlink:href="#GroundDisconnector:地刀_0" y="346.3888888888889" zvalue="452"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806929410" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454806929410"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1143.56,357.5) scale(-1.11111,1.11111) translate(-2172.21,-34.6389)" width="10" x="1138.005962416492" y="346.3888888888889"/></g>
  <g id="446">
   <use class="kv110" height="25" transform="rotate(0,1016.12,504.5) scale(1.4,1.4) translate(-286.319,-139.143)" width="20" x="1002.117063355077" xlink:href="#GroundDisconnector:中性点地刀11_0" y="487" zvalue="467"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806667266" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454806667266"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1016.12,504.5) scale(1.4,1.4) translate(-286.319,-139.143)" width="20" x="1002.117063355077" y="487"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(90,1712.44,332.486) scale(1.11111,1.11111) translate(-170.689,-32.1375)" width="10" x="1706.888888888889" xlink:href="#GroundDisconnector:地刀_0" y="321.3746921657103" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454808043522" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454808043522"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1712.44,332.486) scale(1.11111,1.11111) translate(-170.689,-32.1375)" width="10" x="1706.888888888889" y="321.3746921657103"/></g>
  <g id="105">
   <use class="kv35" height="20" transform="rotate(90,1715.22,398.041) scale(1.11111,1.11111) translate(-170.967,-38.693)" width="10" x="1709.666666666667" xlink:href="#GroundDisconnector:地刀_0" y="386.9302477212658" zvalue="569"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454808174594" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454808174594"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1715.22,398.041) scale(1.11111,1.11111) translate(-170.967,-38.693)" width="10" x="1709.666666666667" y="386.9302477212658"/></g>
  <g id="106">
   <use class="kv35" height="20" transform="rotate(90,1602.44,370.264) scale(1.11111,1.11111) translate(-159.689,-35.9152)" width="10" x="1596.888888888889" xlink:href="#GroundDisconnector:地刀_0" y="359.1524699434881" zvalue="571"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454808305666" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454808305666"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1602.44,370.264) scale(1.11111,1.11111) translate(-159.689,-35.9152)" width="10" x="1596.888888888889" y="359.1524699434881"/></g>
  <g id="107">
   <use class="kv35" height="20" transform="rotate(90,1603,422.486) scale(1.11111,1.11111) translate(-159.744,-41.1375)" width="10" x="1597.444444444444" xlink:href="#GroundDisconnector:地刀_0" y="411.3746921657103" zvalue="573"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454808436738" ObjectName="#2主变35kV侧30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454808436738"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1603,422.486) scale(1.11111,1.11111) translate(-159.744,-41.1375)" width="10" x="1597.444444444444" y="411.3746921657103"/></g>
  <g id="153">
   <use class="kv110" height="20" transform="rotate(270,1423.78,407.778) scale(-1.11111,1.11111) translate(-2704.63,-39.6667)" width="10" x="1418.228184638715" xlink:href="#GroundDisconnector:地刀_0" y="396.6666666666667" zvalue="587"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454808633346" ObjectName="#2主变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454808633346"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1423.78,407.778) scale(-1.11111,1.11111) translate(-2704.63,-39.6667)" width="10" x="1418.228184638715" y="396.6666666666667"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="325">
   <use class="kv110" height="35" transform="rotate(0,826.412,157.25) scale(1.3,1.3) translate(-184.711,-31.0385)" width="40" x="800.4124881074952" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="134.5" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803980290" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,826.412,157.25) scale(1.3,1.3) translate(-184.711,-31.0385)" width="40" x="800.4124881074952" y="134.5"/></g>
  <g id="294">
   <use class="kv10" height="20" transform="rotate(0,700.068,744.125) scale(1.92,2.0875) translate(-323.949,-376.783)" width="25" x="676.068344027507" xlink:href="#Accessory:4绕组母线PT_0" y="723.25" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803390466" ObjectName="10kVⅣ母电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,700.068,744.125) scale(1.92,2.0875) translate(-323.949,-376.783)" width="25" x="676.068344027507" y="723.25"/></g>
  <g id="241">
   <use class="kv10" height="20" transform="rotate(0,483.738,821.25) scale(1.84778,2) translate(-211.346,-400.625)" width="25" x="460.6411218052848" xlink:href="#Accessory:4绕组母线PT_0" y="801.25" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803128322" ObjectName="#4发电机PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,483.738,821.25) scale(1.84778,2) translate(-211.346,-400.625)" width="25" x="460.6411218052848" y="801.25"/></g>
  <g id="240">
   <use class="kv10" height="29" transform="rotate(0,563.25,853.367) scale(1.76667,-1.76667) translate(-232.929,-1325.29)" width="30" x="536.75" xlink:href="#Accessory:PT12321_0" y="827.75" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803062786" ObjectName="#4发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,563.25,853.367) scale(1.76667,-1.76667) translate(-232.929,-1325.29)" width="30" x="536.75" y="827.75"/></g>
  <g id="209">
   <use class="kv10" height="20" transform="rotate(0,790.185,713) scale(1.92,2.1) translate(-367.131,-362.476)" width="25" x="766.1854073825845" xlink:href="#Accessory:4绕组母线PT_0" y="692" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802669570" ObjectName="10kVⅢ母电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,790.185,713) scale(1.92,2.1) translate(-367.131,-362.476)" width="25" x="766.1854073825845" y="692"/></g>
  <g id="167">
   <use class="kv35" height="29" transform="rotate(90,1390.75,132.875) scale(1.51667,-1.56897) translate(-466.022,-209.314)" width="30" x="1368" xlink:href="#Accessory:PT12321_0" y="110.1246783904145" zvalue="314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802014210" ObjectName="35kV光明硅厂Ⅰ回线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,1390.75,132.875) scale(1.51667,-1.56897) translate(-466.022,-209.314)" width="30" x="1368" y="110.1246783904145"/></g>
  <g id="171">
   <use class="kv35" height="35" transform="rotate(0,1756.61,437.743) scale(1.00278,-1.60444) translate(-4.8104,-699.997)" width="40" x="1736.555555555556" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="409.6651519643291" zvalue="337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805356546" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1756.61,437.743) scale(1.00278,-1.60444) translate(-4.8104,-699.997)" width="40" x="1736.555555555556" y="409.6651519643291"/></g>
  <g id="378">
   <use class="kv35" height="29" transform="rotate(90,1599,132.75) scale(1.51667,-1.56897) translate(-536.964,-209.11)" width="30" x="1576.25" xlink:href="#Accessory:PT12321_0" y="110" zvalue="387"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805291010" ObjectName="35kV宏利硅厂线PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,1599,132.75) scale(1.51667,-1.56897) translate(-536.964,-209.11)" width="30" x="1576.25" y="110"/></g>
  <g id="382">
   <use class="kv10" height="26" transform="rotate(0,400.75,753) scale(1,1) translate(0,0)" width="12" x="394.75" xlink:href="#Accessory:避雷器1_0" y="740" zvalue="396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805487618" ObjectName="#4发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,400.75,753) scale(1,1) translate(0,0)" width="12" x="394.75" y="740"/></g>
  <g id="390">
   <use class="kv10" height="26" transform="rotate(0,664.25,688) scale(1,1) translate(0,0)" width="12" x="658.25" xlink:href="#Accessory:避雷器1_0" y="675" zvalue="407"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805618690" ObjectName="电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,664.25,688) scale(1,1) translate(0,0)" width="12" x="658.25" y="675"/></g>
  <g id="393">
   <use class="kv10" height="30" transform="rotate(0,514.5,853.367) scale(1.76667,1.70778) translate(-211.774,-343.056)" width="30" x="488" xlink:href="#Accessory:pt带容断器_0" y="827.75" zvalue="411"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805684226" ObjectName="#4发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,514.5,853.367) scale(1.76667,1.70778) translate(-211.774,-343.056)" width="30" x="488" y="827.75"/></g>
  <g id="403">
   <use class="kv10" height="20" transform="rotate(0,901.667,900.75) scale(1.64444,2) translate(-345.3,-440.375)" width="25" x="881.1111111111111" xlink:href="#Accessory:4绕组母线PT_0" y="880.75" zvalue="420"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805880834" ObjectName="#3发电机PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,901.667,900.75) scale(1.64444,2) translate(-345.3,-440.375)" width="25" x="881.1111111111111" y="880.75"/></g>
  <g id="402">
   <use class="kv10" height="30" transform="rotate(0,932.75,932.867) scale(1.76667,1.70778) translate(-393.278,-376.004)" width="30" x="906.25" xlink:href="#Accessory:pt带容断器_0" y="907.25" zvalue="421"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454805815298" ObjectName="#3发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,932.75,932.867) scale(1.76667,1.70778) translate(-393.278,-376.004)" width="30" x="906.25" y="907.25"/></g>
  <g id="410">
   <use class="kv10" height="29" transform="rotate(0,985.75,924.867) scale(1.76667,-1.76667) translate(-416.278,-1437.26)" width="30" x="959.25" xlink:href="#Accessory:PT12321_0" y="899.25" zvalue="432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806011906" ObjectName="#3发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,985.75,924.867) scale(1.76667,-1.76667) translate(-416.278,-1437.26)" width="30" x="959.25" y="899.25"/></g>
  <g id="450">
   <use class="kv10" height="20" transform="rotate(0,1200.11,723) scale(1.92,2.4) translate(-563.551,-407.75)" width="25" x="1176.105407382584" xlink:href="#Accessory:4绕组母线PT_0" y="699" zvalue="462"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806732802" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1200.11,723) scale(1.92,2.4) translate(-563.551,-407.75)" width="25" x="1176.105407382584" y="699"/></g>
  <g id="439">
   <use class="kv10" height="20" transform="rotate(0,1116.5,907) scale(1.75444,2) translate(-470.686,-443.5)" width="25" x="1094.569444444444" xlink:href="#Accessory:4绕组母线PT_0" y="887" zvalue="476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806470658" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1116.5,907) scale(1.75444,2) translate(-470.686,-443.5)" width="25" x="1094.569444444444" y="887"/></g>
  <g id="438">
   <use class="kv10" height="30" transform="rotate(0,1156.75,935.367) scale(1.76667,1.70778) translate(-490.486,-377.04)" width="30" x="1130.25" xlink:href="#Accessory:pt带容断器_0" y="909.75" zvalue="477"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806405122" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1156.75,935.367) scale(1.76667,1.70778) translate(-490.486,-377.04)" width="30" x="1130.25" y="909.75"/></g>
  <g id="437">
   <use class="kv10" height="29" transform="rotate(0,1192.25,927.617) scale(1.76667,-1.76667) translate(-505.892,-1441.57)" width="30" x="1165.75" xlink:href="#Accessory:PT12321_0" y="902" zvalue="478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806339586" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1192.25,927.617) scale(1.76667,-1.76667) translate(-505.892,-1441.57)" width="30" x="1165.75" y="902"/></g>
  <g id="495">
   <use class="kv10" height="20" transform="rotate(0,1643.23,726.625) scale(1.87,2.1375) translate(-753.623,-375.308)" width="25" x="1619.855407382584" xlink:href="#Accessory:4绕组母线PT_0" y="705.25" zvalue="512"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807584770" ObjectName="10kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1643.23,726.625) scale(1.87,2.1375) translate(-753.623,-375.308)" width="25" x="1619.855407382584" y="705.25"/></g>
  <g id="491">
   <use class="kv10" height="20" transform="rotate(0,1350.25,910.75) scale(1.70111,2) translate(-547.74,-445.375)" width="25" x="1328.986111111111" xlink:href="#Accessory:4绕组母线PT_0" y="890.75" zvalue="518"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807453698" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1350.25,910.75) scale(1.70111,2) translate(-547.74,-445.375)" width="25" x="1328.986111111111" y="890.75"/></g>
  <g id="490">
   <use class="kv10" height="30" transform="rotate(0,1389.5,934.117) scale(1.76667,1.70778) translate(-591.491,-376.522)" width="30" x="1363" xlink:href="#Accessory:pt带容断器_0" y="908.5" zvalue="519"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807388162" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1389.5,934.117) scale(1.76667,1.70778) translate(-591.491,-376.522)" width="30" x="1363" y="908.5"/></g>
  <g id="489">
   <use class="kv10" height="29" transform="rotate(0,1426,927.367) scale(1.76667,-1.76667) translate(-607.33,-1441.17)" width="30" x="1399.5" xlink:href="#Accessory:PT12321_0" y="901.75" zvalue="520"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807322626" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1426,927.367) scale(1.76667,-1.76667) translate(-607.33,-1441.17)" width="30" x="1399.5" y="901.75"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="320">
   <g id="3200">
    <use class="kv110" height="50" transform="rotate(0,567.349,494.339) scale(1.99167,1.85355) translate(-267.613,-206.301)" width="30" x="537.47" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="448" zvalue="87"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593599490" ObjectName="110"/>
    </metadata>
   </g>
   <g id="3201">
    <use class="kv10" height="50" transform="rotate(0,567.349,494.339) scale(1.99167,1.85355) translate(-267.613,-206.301)" width="30" x="537.47" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="448" zvalue="87"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593665026" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533920258" ObjectName="#4主变"/>
   <cge:TPSR_Ref TObjectID="6755399533920258"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,567.349,494.339) scale(1.99167,1.85355) translate(-267.613,-206.301)" width="30" x="537.47" y="448"/></g>
  <g id="226">
   <g id="2260">
    <use class="kv110" height="50" transform="rotate(0,885.466,494.339) scale(1.98776,1.85355) translate(-425.192,-206.301)" width="30" x="855.65" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="448" zvalue="228"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593468418" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2261">
    <use class="kv10" height="50" transform="rotate(0,885.466,494.339) scale(1.98776,1.85355) translate(-425.192,-206.301)" width="30" x="855.65" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="448" zvalue="228"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593533954" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533854722" ObjectName="#3主变"/>
   <cge:TPSR_Ref TObjectID="6755399533854722"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,885.466,494.339) scale(1.98776,1.85355) translate(-425.192,-206.301)" width="30" x="855.65" y="448"/></g>
  <g id="453">
   <g id="4530">
    <use class="kv110" height="50" transform="rotate(0,1100.47,491.839) scale(1.9211,1.85355) translate(-513.819,-205.15)" width="30" x="1071.65" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="445.5" zvalue="457"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593730562" ObjectName="110"/>
    </metadata>
   </g>
   <g id="4531">
    <use class="kv10" height="50" transform="rotate(0,1100.47,491.839) scale(1.9211,1.85355) translate(-513.819,-205.15)" width="30" x="1071.65" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="445.5" zvalue="457"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593796098" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533985794" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399533985794"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1100.47,491.839) scale(1.9211,1.85355) translate(-513.819,-205.15)" width="30" x="1071.65" y="445.5"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,431,814) scale(1.5,1.5) translate(-136.167,-263.833)" width="30" x="408.5" xlink:href="#Generator:发电机_0" y="791.5" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803456002" ObjectName="#4发电机"/>
   <cge:TPSR_Ref TObjectID="6192454803456002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,431,814) scale(1.5,1.5) translate(-136.167,-263.833)" width="30" x="408.5" y="791.5"/></g>
  <g id="224">
   <use class="kv10" height="30" transform="rotate(0,845.117,857) scale(1.5,1.5) translate(-274.206,-278.167)" width="30" x="822.6170633550773" xlink:href="#Generator:发电机_0" y="834.5" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802735106" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192454802735106"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,845.117,857) scale(1.5,1.5) translate(-274.206,-278.167)" width="30" x="822.6170633550773" y="834.5"/></g>
  <g id="451">
   <use class="kv10" height="30" transform="rotate(0,1060.12,855.5) scale(1.5,1.5) translate(-345.872,-277.667)" width="30" x="1037.617063355077" xlink:href="#Generator:发电机_0" y="833" zvalue="460"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454806798338" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454806798338"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1060.12,855.5) scale(1.5,1.5) translate(-345.872,-277.667)" width="30" x="1037.617063355077" y="833"/></g>
  <g id="496">
   <use class="kv10" height="30" transform="rotate(0,1292.87,858.25) scale(1.5,1.5) translate(-423.456,-278.583)" width="30" x="1270.367063355077" xlink:href="#Generator:发电机_0" y="835.75" zvalue="510"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807650306" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454807650306"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1292.87,858.25) scale(1.5,1.5) translate(-423.456,-278.583)" width="30" x="1270.367063355077" y="835.75"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="243">
   <use class="kv10" height="30" transform="rotate(0,617.5,770.25) scale(2.1,2.1) translate(-312.452,-386.964)" width="20" x="596.5000000000001" xlink:href="#EnergyConsumer:站用变11_0" y="738.75" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454803193858" ObjectName="#3站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,617.5,770.25) scale(2.1,2.1) translate(-312.452,-386.964)" width="20" x="596.5000000000001" y="738.75"/></g>
  <g id="228">
   <use class="kv10" height="30" transform="rotate(0,1449.62,744.5) scale(2.1,2.1) translate(-748.323,-373.476)" width="20" x="1428.617063355078" xlink:href="#EnergyConsumer:站用变11_0" y="713" zvalue="299"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454802079746" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1449.62,744.5) scale(2.1,2.1) translate(-748.323,-373.476)" width="20" x="1428.617063355078" y="713"/></g>
  <g id="159">
   <use class="kv35" height="30" transform="rotate(0,1694.43,107.028) scale(2.67747,0.798149) translate(-1051.52,24.0395)" width="12" x="1678.367246944261" xlink:href="#EnergyConsumer:负荷_0" y="95.05554178025977" zvalue="327"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454801817602" ObjectName="35kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192454801817602"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1694.43,107.028) scale(2.67747,0.798149) translate(-1051.52,24.0395)" width="12" x="1678.367246944261" y="95.05554178025977"/></g>
  <g id="468">
   <use class="kv10" height="30" transform="rotate(0,1001.87,742) scale(2.1,2.1) translate(-513.788,-372.167)" width="20" x="980.8670633550776" xlink:href="#EnergyConsumer:站用变11_0" y="710.5" zvalue="496"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807060482" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1001.87,742) scale(2.1,2.1) translate(-513.788,-372.167)" width="20" x="980.8670633550776" y="710.5"/></g>
  <g id="474">
   <use class="kv10" height="30" transform="rotate(0,1542.12,743.25) scale(2.1,2.1) translate(-796.776,-372.821)" width="20" x="1521.117063355078" xlink:href="#EnergyConsumer:站用变11_0" y="711.75" zvalue="503"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454807126018" ObjectName="近区变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1542.12,743.25) scale(2.1,2.1) translate(-796.776,-372.821)" width="20" x="1521.117063355078" y="711.75"/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="347">
   <g id="3470">
    <use class="kv110" height="50" transform="rotate(0,1397,494) scale(1.8,1.8) translate(-600.889,-199.556)" width="50" x="1352" xlink:href="#PowerTransformer3:主变高压侧有中性点_0" y="449" zvalue="308"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593271810" ObjectName="110"/>
    </metadata>
   </g>
   <g id="3471">
    <use class="kv35" height="50" transform="rotate(0,1397,494) scale(1.8,1.8) translate(-600.889,-199.556)" width="50" x="1352" xlink:href="#PowerTransformer3:主变高压侧有中性点_1" y="449" zvalue="308"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593337346" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3472">
    <use class="kv10" height="50" transform="rotate(0,1397,494) scale(1.8,1.8) translate(-600.889,-199.556)" width="50" x="1352" xlink:href="#PowerTransformer3:主变高压侧有中性点_2" y="449" zvalue="308"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593402882" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533789186" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399533789186"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1397,494) scale(1.8,1.8) translate(-600.889,-199.556)" width="50" x="1352" y="449"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="12" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1694.43,26.1667) scale(1,1) translate(0,-3.18264e-14)" writing-mode="lr" x="1693.96" xml:space="preserve" y="30.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136449454082" ObjectName="P"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="13" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1475.1,20.1111) scale(1,1) translate(0,3.33067e-14)" writing-mode="lr" x="1474.63" xml:space="preserve" y="24.75" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136461316098" ObjectName="P"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="14" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,517.265,16.8889) scale(1,1) translate(0,6.41462e-16)" writing-mode="lr" x="516.8" xml:space="preserve" y="21.57" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136465969154" ObjectName="P"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="15" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1694.43,44.1111) scale(1,1) translate(0,-7.08076e-14)" writing-mode="lr" x="1693.96" xml:space="preserve" y="48.81" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136449519618" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="16" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1475.1,41.3889) scale(1,1) translate(0,6.48864e-14)" writing-mode="lr" x="1474.63" xml:space="preserve" y="46.08" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136461381634" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="17" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,517.265,32.0556) scale(1,1) translate(0,5.15637e-14)" writing-mode="lr" x="516.8" xml:space="preserve" y="36.73" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136466034690" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1694.43,60.3889) scale(1,1) translate(0,-1.20768e-13)" writing-mode="lr" x="1693.96" xml:space="preserve" y="65.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136449585154" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="23" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1475.1,57.6667) scale(1,1) translate(0,1.20866e-13)" writing-mode="lr" x="1474.63" xml:space="preserve" y="62.33" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136461447170" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="30" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,517.265,47.7778) scale(1,1) translate(0,8.49691e-14)" writing-mode="lr" x="516.8" xml:space="preserve" y="52.46" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136466100226" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="199" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,833.562,933.139) scale(1,1) translate(8.66597e-14,2.14845e-12)" writing-mode="lr" x="833.01" xml:space="preserve" y="937.8200000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136455811074" ObjectName="P"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,428.556,887.139) scale(1,1) translate(0,-1.54869e-12)" writing-mode="lr" x="428" xml:space="preserve" y="891.88" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136460660738" ObjectName="P"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="201" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1049.01,932.333) scale(1,1) translate(-2.21158e-13,1.83631e-12)" writing-mode="lr" x="1048.45" xml:space="preserve" y="937.05" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136469639170" ObjectName="P"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="202" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1277.31,932.042) scale(1,1) translate(0,-2.03905e-13)" writing-mode="lr" x="1276.76" xml:space="preserve" y="936.76" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136476127234" ObjectName="P"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="205" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,833.562,952.417) scale(1,1) translate(0,1.98251e-12)" writing-mode="lr" x="833.01" xml:space="preserve" y="957.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136455876610" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,428.556,908.528) scale(1,1) translate(0,0)" writing-mode="lr" x="428" xml:space="preserve" y="913.26" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136460726274" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="210" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1049.01,950.833) scale(1,1) translate(1.10579e-13,2.08303e-12)" writing-mode="lr" x="1048.45" xml:space="preserve" y="955.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136469704706" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="211">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="211" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1277.31,950.542) scale(1,1) translate(0,2.29262e-12)" writing-mode="lr" x="1276.76" xml:space="preserve" y="955.23" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136476192770" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="212" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,833.562,968.361) scale(1,1) translate(8.66597e-14,2.12756e-13)" writing-mode="lr" x="833.01" xml:space="preserve" y="973.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136455942146" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="213" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,428.556,927.694) scale(1,1) translate(0,-2.23746e-12)" writing-mode="lr" x="428" xml:space="preserve" y="932.38" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136460791810" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="214" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1049.01,969.889) scale(1,1) translate(-2.21158e-13,-2.12571e-13)" writing-mode="lr" x="1048.45" xml:space="preserve" y="974.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136469770242" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="215">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="215" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1277.31,969.042) scale(1,1) translate(0,2.12935e-13)" writing-mode="lr" x="1276.76" xml:space="preserve" y="973.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136476258306" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="216" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,664.724,375.875) scale(1,1) translate(0,0)" writing-mode="lr" x="664.17" xml:space="preserve" y="380.59" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136462888962" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="217" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,664.724,394.25) scale(1,1) translate(0,0)" writing-mode="lr" x="664.17" xml:space="preserve" y="398.93" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136462954498" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="218" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,654.724,530.964) scale(1,1) translate(0,0)" writing-mode="lr" x="654.17" xml:space="preserve" y="535.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136463020034" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="219" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,652.224,551.964) scale(1,1) translate(0,0)" writing-mode="lr" x="651.67" xml:space="preserve" y="556.62" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136463085570" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="220" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,664.724,417.625) scale(1,1) translate(0,0)" writing-mode="lr" x="664.17" xml:space="preserve" y="422.35" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136463151106" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="221" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,652.224,571.089) scale(1,1) translate(0,6.20812e-14)" writing-mode="lr" x="651.67" xml:space="preserve" y="575.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136463478786" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="222" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,984.15,370.25) scale(1,1) translate(0,3.95517e-14)" writing-mode="lr" x="983.6" xml:space="preserve" y="374.97" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136456466434" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="223" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,984.15,391.25) scale(1,1) translate(0,4.18554e-14)" writing-mode="lr" x="983.6" xml:space="preserve" y="395.98" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136456531970" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="239" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,957.9,549.714) scale(1,1) translate(0,5.96942e-14)" writing-mode="lr" x="957.35" xml:space="preserve" y="554.4" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136456597506" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="242" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,957.9,570.089) scale(1,1) translate(0,-1.23996e-13)" writing-mode="lr" x="957.35" xml:space="preserve" y="574.77" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136456663042" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="244" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,984.15,409.75) scale(1,1) translate(0,4.41591e-14)" writing-mode="lr" x="983.6" xml:space="preserve" y="414.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136456728578" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,957.9,591.714) scale(1,1) translate(0,-1.28603e-13)" writing-mode="lr" x="957.35" xml:space="preserve" y="596.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136457056258" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="246" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1208.9,352.75) scale(1,1) translate(0,3.76088e-14)" writing-mode="lr" x="1208.35" xml:space="preserve" y="357.47" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136470294530" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="249" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1208.9,373.125) scale(1,1) translate(0,4.00513e-14)" writing-mode="lr" x="1208.35" xml:space="preserve" y="377.82" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136470360066" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="252">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="252" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1186.4,526.589) scale(1,1) translate(0,-1.1356e-13)" writing-mode="lr" x="1185.85" xml:space="preserve" y="531.33" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136470425602" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="253">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="253" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1186.4,548.214) scale(1,1) translate(0,5.94999e-14)" writing-mode="lr" x="1185.85" xml:space="preserve" y="552.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136470491138" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="254">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="254" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1211.4,392.25) scale(1,1) translate(0,4.22162e-14)" writing-mode="lr" x="1210.85" xml:space="preserve" y="396.94" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136470556674" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="255">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="255" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1187.65,569.214) scale(1,1) translate(0,-1.23052e-13)" writing-mode="lr" x="1187.1" xml:space="preserve" y="573.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136470884354" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="256" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1323.25,366) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.78" xml:space="preserve" y="370.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136451026946" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1323.25,385.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.78" xml:space="preserve" y="390.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136451092482" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="258">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="258" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1323.25,405) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.78" xml:space="preserve" y="409.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136451158018" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="259">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="259" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1427,596.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.53" xml:space="preserve" y="601.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136451289090" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="260" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1547.75,377.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1547.28" xml:space="preserve" y="382.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136451485698" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="261">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="261" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1547.75,394.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1547.28" xml:space="preserve" y="399.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136451551234" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="262">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="262" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1427,552.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.53" xml:space="preserve" y="557.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136451616770" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="263">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="263" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1427,574.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.53" xml:space="preserve" y="579.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136451682306" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="264" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1547.75,411.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1547.28" xml:space="preserve" y="416.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136451813378" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="279" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136468983810" ObjectName="F"/>
   </metadata>
  </g>
  <g id="376">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="376" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136477962242" ObjectName="F"/>
   </metadata>
  </g>
  <g id="375">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="375" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136478027778" ObjectName="F"/>
   </metadata>
  </g>
  <g id="374">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="374" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,334.056) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="338.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136468459522" ObjectName="F"/>
   </metadata>
  </g>
  <g id="275">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="275" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136477831170" ObjectName="F"/>
   </metadata>
  </g>
  <g id="367">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="367" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136477896706" ObjectName="F"/>
   </metadata>
  </g>
  <g id="269">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="269" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180894213" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="265">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,337.222,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180828677" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,356.917) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="361.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136473767938" ObjectName="F"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,353.917) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="358.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136455680002" ObjectName="F"/>
   </metadata>
  </g>
  <g id="303">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,380.917) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="385.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136459939842" ObjectName="F"/>
   </metadata>
  </g>
  <g id="305">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="305" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,377.917) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="382.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136467935234" ObjectName="F"/>
   </metadata>
  </g>
  <g id="306">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="306" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,402.917) scale(1,1) translate(0,-2.61402e-13)" writing-mode="lr" x="159.77" xml:space="preserve" y="407.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124367368196" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="307">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,425.917) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="430.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124367433732" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="311">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="311" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,402.917) scale(1,1) translate(0,-2.61402e-13)" writing-mode="lr" x="337.38" xml:space="preserve" y="407.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124367499268" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="310">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,425.917) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="430.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124367630340" ObjectName="入库流量"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1366,648.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1365.53" xml:space="preserve" y="653.67" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136455548930" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="33" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,766.5,587.889) scale(1,1) translate(0,0)" writing-mode="lr" x="766.03" xml:space="preserve" y="592.67" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136459808770" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="135" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,444.75,584.889) scale(1,1) translate(0,0)" writing-mode="lr" x="444.28" xml:space="preserve" y="589.67" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136467804162" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="136" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1498.16,335.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1497.69" xml:space="preserve" y="340.44" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136468328450" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="146" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,449.444,258.667) scale(1,1) translate(-2.69414e-13,0)" writing-mode="lr" x="448.98" xml:space="preserve" y="263.44" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136468852738" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="148" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1058.5,591.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1058.03" xml:space="preserve" y="596.17" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136473636866" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="417">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="417" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,624.25,829.375) scale(1,1) translate(6.36158e-14,0)" writing-mode="lr" x="575.14" xml:space="preserve" y="833.3" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136460070914" ObjectName="P"/>
   </metadata>
  </g>
  <g id="418">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="418" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,624.25,850.25) scale(1,1) translate(6.36158e-14,0)" writing-mode="lr" x="575.14" xml:space="preserve" y="854.1799999999999" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136460136450" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="419">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="419" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,624.25,871.25) scale(1,1) translate(6.36158e-14,0)" writing-mode="lr" x="575.14" xml:space="preserve" y="875.1799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136460201986" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="447">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="447" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1014.37,793) scale(1,1) translate(1.06927e-13,0)" writing-mode="lr" x="965.25" xml:space="preserve" y="796.9299999999999" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136473899010" ObjectName="P"/>
   </metadata>
  </g>
  <g id="500">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="500" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1014.37,810) scale(1,1) translate(0,0)" writing-mode="lr" x="965.25" xml:space="preserve" y="813.9299999999999" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136473964546" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="501">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="501" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1014.37,827) scale(1,1) translate(0,0)" writing-mode="lr" x="965.25" xml:space="preserve" y="830.9299999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136474030082" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="505">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="505" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1466.12,799.5) scale(1,1) translate(1.57082e-13,0)" writing-mode="lr" x="1417" xml:space="preserve" y="803.4299999999999" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136453648386" ObjectName="P"/>
   </metadata>
  </g>
  <g id="506">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="506" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1466.12,816.5) scale(1,1) translate(1.57082e-13,0)" writing-mode="lr" x="1417" xml:space="preserve" y="820.4299999999999" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136453713922" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="507">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="507" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1466.12,833.5) scale(1,1) translate(1.57082e-13,0)" writing-mode="lr" x="1417" xml:space="preserve" y="837.4299999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136453779458" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="430">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="430" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,437.556,952) scale(1,1) translate(0,1.04401e-13)" writing-mode="lr" x="386.76" xml:space="preserve" y="956.41" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136461250562" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="431">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="431" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,848.117,990) scale(1,1) translate(8.82757e-14,1.0862e-13)" writing-mode="lr" x="797.33" xml:space="preserve" y="994.41" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136456400898" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="433">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="433" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1060.12,991.5) scale(1,1) translate(-2.23625e-13,1.08786e-13)" writing-mode="lr" x="1009.33" xml:space="preserve" y="995.91" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136470228994" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="502">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="502" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1290.87,987.25) scale(1,1) translate(0,1.08315e-13)" writing-mode="lr" x="1240.08" xml:space="preserve" y="991.66" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136476717058" ObjectName="Ua"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="373">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="708"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374885388292" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="301">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="728"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950115295236" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>