<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549595668482" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器-线路_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.99087893864013" xlink:href="#terminal" y="0.7550975177305101"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="8.997927031509123" y1="10.28767730496455" y2="6.757092198581571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="15.0924543946932" y1="0.9708554964538898" y2="3.22650709219857"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="21.18698175787728" y1="10.28767730496455" y2="6.757092198581571"/>
   <rect fill-opacity="0" height="18.83" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15.04,12.64) scale(1,1) translate(0,0)" width="20.42" x="4.83" y="3.23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="15.0924543946932" y1="3.226507092198597" y2="10.36613475177306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="15.0924543946932" y1="14.99512411347518" y2="22.05629432624114"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.18626773539708" x2="19.32476506357104" y1="29.60782358156029" y2="29.60782358156029"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="8.997927031509123" y1="14.99512411347518" y2="18.52570921985816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.23940482771328" x2="20.62267366869358" y1="27.98189622993656" y2="27.98189622993656"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.643587617468211" x2="23.21849087893864" y1="26.35596887831283" y2="26.35596887831283"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="21.18698175787728" y1="14.99512411347518" y2="18.52570921985816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="15.0924543946932" y1="22.05629432624115" y2="26.29299645390072"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="Disconnector:三相刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="30"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="23.66666666666666" y2="29.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.25" x2="15.08333333333333" y1="5.999999999999998" y2="23.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.46666666666667" x2="16.43333333333333" y1="5.755662181544974" y2="5.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.750000000000002" y2="0.3554618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV测试站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="285" x="45" xlink:href="logo.png" y="117"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.5,147) scale(1,1) translate(0,0)" writing-mode="lr" x="187.5" xml:space="preserve" y="150.5" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,189.5,147.69) scale(1,1) translate(0,0)" writing-mode="lr" x="189.5" xml:space="preserve" y="156.69" zvalue="5">10kV测试站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="3" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,77.4375,436) scale(1,1) translate(0,0)" width="72.88" x="41" y="424" zvalue="32"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.4375,436) scale(1,1) translate(0,0)" writing-mode="lr" x="77.44" xml:space="preserve" y="440.5" zvalue="32">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1585.64,582.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1585.64" xml:space="preserve" y="586.59" zvalue="2">10kVI段母线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="233" y2="233"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="259" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="233" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="233" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="233" y2="233"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="259" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="233" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="233" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="259" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="283.25" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="259" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="259" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="259" y2="259"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="283.25" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="259" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="259" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="283.25" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="306" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="283.25" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="283.25" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="283.25" y2="283.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="306" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="283.25" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="283.25" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="306" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="328.75" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="306" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="306" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="306" y2="306"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="328.75" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="306" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="306" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="328.75" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="199" y1="351.5" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18" x2="18" y1="328.75" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="328.75" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="328.75" y2="328.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="380" y1="351.5" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199" x2="199" y1="328.75" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380" x2="380" y1="328.75" y2="351.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="513.6666435058594" y2="513.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="551.1566435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="513.6666435058594" y2="551.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="551.1567435058594" y2="551.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="551.1567435058594" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="575.3253435058595" y2="575.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="599.4939435058594" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="575.3253435058595" y2="599.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="599.4939835058594" y2="599.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="623.6625835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="599.4939835058594" y2="623.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="623.6627435058595" y2="623.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="623.6627435058595" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="118.5670617373469" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="70.22236173734677" x2="70.22236173734677" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5670617373469" x2="118.5670617373469" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="180.6756617373469" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.5673617373468" x2="118.5673617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6756617373469" x2="180.6756617373469" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="243.9999617373469" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="180.6751617373468" x2="180.6751617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9999617373469" x2="243.9999617373469" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="306.1081617373468" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="243.9998617373468" x2="243.9998617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="647.8313435058594" y2="647.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="368.2164617373469" y1="671.9999435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="306.1081617373468" x2="306.1081617373468" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2164617373469" x2="368.2164617373469" y1="647.8313435058594" y2="671.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1006" y2="1006"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1045.1633" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="17" y1="1006" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1006" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="377" y1="1006" y2="1006"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="377" y1="1045.1633" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1006" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377" x2="377" y1="1006" y2="1045.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1045.16327" y2="1045.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1073.08167" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="17" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="1045.16327" y2="1045.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="1073.08167" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197" x2="197" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="1045.16327" y2="1045.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="1073.08167" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="197.0000000000001" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.0000000000001" x2="287.0000000000001" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="1045.16327" y2="1045.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="1073.08167" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="287" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377" x2="377" y1="1045.16327" y2="1073.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1073.0816" y2="1073.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1101" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="17" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="1073.0816" y2="1073.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="1101" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197" x2="197" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="1073.0816" y2="1073.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="1101" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="197.0000000000001" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.0000000000001" x2="287.0000000000001" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="1073.0816" y2="1073.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="1101" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="287" y1="1073.0816" y2="1101"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377" x2="377" y1="1073.0816" y2="1101"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62,1026) scale(1,1) translate(0,0)" writing-mode="lr" x="62" xml:space="preserve" y="1032" zvalue="10">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59,1060) scale(1,1) translate(0,0)" writing-mode="lr" x="59" xml:space="preserve" y="1066" zvalue="11">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241,1060) scale(1,1) translate(0,0)" writing-mode="lr" x="241" xml:space="preserve" y="1066" zvalue="12">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58,1088) scale(1,1) translate(0,0)" writing-mode="lr" x="58" xml:space="preserve" y="1094" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240,1088) scale(1,1) translate(0,0)" writing-mode="lr" x="240" xml:space="preserve" y="1094" zvalue="14">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" x="145" xml:space="preserve" y="528" zvalue="15">10kVⅠ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="145" xml:space="preserve" y="544" zvalue="15">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" x="205.671875" xml:space="preserve" y="528" zvalue="17">10kVⅡ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="205.671875" xml:space="preserve" y="544" zvalue="17">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94,563.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94" xml:space="preserve" y="568" zvalue="18">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94,589) scale(1,1) translate(0,0)" writing-mode="lr" x="94" xml:space="preserve" y="593.5" zvalue="19">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94,614.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94" xml:space="preserve" y="619" zvalue="20">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93,639) scale(1,1) translate(0,0)" writing-mode="lr" x="93" xml:space="preserve" y="643.5" zvalue="21">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94,665.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94" xml:space="preserve" y="670" zvalue="22">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.054,1028) scale(1,1) translate(0,0)" writing-mode="lr" x="242.05" xml:space="preserve" y="1032" zvalue="23">ceshizhan-01-2021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,152.054,1060) scale(1,1) translate(0,0)" writing-mode="lr" x="152.05" xml:space="preserve" y="1066" zvalue="24">XX</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,332.054,1060) scale(1,1) translate(0,0)" writing-mode="lr" x="332.05" xml:space="preserve" y="1066" zvalue="25">20210220</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56,247) scale(1,1) translate(0,0)" writing-mode="lr" x="56" xml:space="preserve" y="252.5" zvalue="26">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,247) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="252.5" zvalue="27">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,243.125,271.25) scale(1,1) translate(0,0)" writing-mode="lr" x="243.13" xml:space="preserve" y="275.75" zvalue="28">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244.75,317.5) scale(1,1) translate(0,0)" writing-mode="lr" x="244.75" xml:space="preserve" y="322" zvalue="29">110kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,193.211,436.591) scale(1,1) translate(0,0)" writing-mode="lr" x="193.21" xml:space="preserve" y="441.09" zvalue="30">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,298.211,436.591) scale(1,1) translate(0,0)" writing-mode="lr" x="298.21" xml:space="preserve" y="441.09" zvalue="31">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1275.64,670.636) scale(1,1) translate(0,0)" writing-mode="lr" x="1275.64" xml:space="preserve" y="675.14" zvalue="47">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044.73,509) scale(1,1) translate(0,0)" writing-mode="lr" x="1044.73" xml:space="preserve" y="513.5" zvalue="58">010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1048.91,384.455) scale(1,1) translate(0,0)" writing-mode="lr" x="1048.91" xml:space="preserve" y="388.95" zvalue="59">0101</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1002.73,266.818) scale(1,1) translate(0,0)" writing-mode="lr" x="1002.73" xml:space="preserve" y="271.32" zvalue="61">10kV负荷线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1465.45,795.636) scale(1,1) translate(0,0)" writing-mode="lr" x="1465.45" xml:space="preserve" y="800.14" zvalue="81">10kVI段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1488.68,659) scale(1,1) translate(0,0)" writing-mode="lr" x="1488.68" xml:space="preserve" y="663.5" zvalue="82">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1237.73,911.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1237.73" xml:space="preserve" y="915.59" zvalue="95">2号发电机</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="41" y="424" zvalue="32"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="kv10" d="M 488 594 L 1630 594" stroke-width="4" zvalue="1"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674260353028" ObjectName="10kVI段母线"/>
   <cge:TPSR_Ref TObjectID="9288674260353028"/></metadata>
  <path d="M 488 594 L 1630 594" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="54">
   <use class="kv10" height="20" transform="rotate(0,1234.09,671.636) scale(2,2) translate(-612.045,-325.818)" width="10" x="1224.090909090909" xlink:href="#Breaker:小车断路器_0" y="651.6363636363636" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924576477188" ObjectName="2号发电机012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924576477188"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1234.09,671.636) scale(2,2) translate(-612.045,-325.818)" width="10" x="1224.090909090909" y="651.6363636363636"/></g>
  <g id="59">
   <use class="kv10" height="20" transform="rotate(0,1006.36,504.545) scale(2,2) translate(-498.182,-242.273)" width="10" x="996.3636363636364" xlink:href="#Breaker:小车断路器_0" y="484.5454545454546" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924576542724" ObjectName="10kV负荷线010断路器"/>
   <cge:TPSR_Ref TObjectID="6473924576542724"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1006.36,504.545) scale(2,2) translate(-498.182,-242.273)" width="10" x="996.3636363636364" y="484.5454545454546"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="53">
   <path class="kv10" d="M 1234.09 853.52 L 1234.09 689.64" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="54@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.09 853.52 L 1234.09 689.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 1234.09 653.14 L 1234.09 594" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.09 653.14 L 1234.09 594" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 1147.73 751.6 L 1147.73 731.09 L 1234.09 731.09" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="53" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.73 751.6 L 1147.73 731.09 L 1234.09 731.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 1234.09 689.72 L 1234.09 731.09" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.09 689.72 L 1234.09 731.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 1006.36 522.55 L 1006.36 594" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@1" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.36 522.55 L 1006.36 594" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 1004.55 315.68 L 1004.55 367.91" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.55 315.68 L 1004.55 367.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 1005.45 397.27 L 1005.45 486.05" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="59@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.45 397.27 L 1005.45 486.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 1050 463.79 L 1050 448.93 L 1005.45 448.93" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050 463.79 L 1050 448.93 L 1005.45 448.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 1455.68 729.89 L 1455.68 676.36" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="87@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.68 729.89 L 1455.68 676.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1455.68 649.09 L 1455.68 594" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.68 649.09 L 1455.68 594" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 923.91 634.05 L 923.91 594" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="1@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.91 634.05 L 923.91 594" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 631.09 640.36 L 631.09 594" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="1@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 631.09 640.36 L 631.09 594" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="51">
   <use class="kv10" height="20" transform="rotate(0,1147.73,764.727) scale(1.5,1.5) translate(-377.576,-249.909)" width="20" x="1132.727272727273" xlink:href="#Accessory:线路PT3_0" y="749.7272727272727" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450180874245" ObjectName="2号发电机避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1147.73,764.727) scale(1.5,1.5) translate(-377.576,-249.909)" width="20" x="1132.727272727273" y="749.7272727272727"/></g>
  <g id="72">
   <use class="kv10" height="30" transform="rotate(0,1050,470.909) scale(0.5,0.5) translate(1042.5,463.409)" width="30" x="1042.5" xlink:href="#Accessory:避雷器-线路_0" y="463.4090909090909" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450181070853" ObjectName="10kV负荷线避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1050,470.909) scale(0.5,0.5) translate(1042.5,463.409)" width="30" x="1042.5" y="463.4090909090909"/></g>
  <g id="85">
   <use class="kv10" height="40" transform="rotate(0,1468.18,753.636) scale(1.25,1.25) translate(-288.636,-145.727)" width="40" x="1443.181818181818" xlink:href="#Accessory:五卷PT_0" y="728.6363636363637" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450181136389" ObjectName="10kVI段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1468.18,753.636) scale(1.25,1.25) translate(-288.636,-145.727)" width="40" x="1443.181818181818" y="728.6363636363637"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="61">
   <use class="kv10" height="33" transform="rotate(0,1005.45,382.727) scale(0.909091,0.909091) translate(99.9091,36.7727)" width="14" x="999.0909090909091" xlink:href="#Disconnector:手车隔离开关13_0" y="367.7272727272728" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450180939781" ObjectName="10kV负荷线0101刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450180939781"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1005.45,382.727) scale(0.909091,0.909091) translate(99.9091,36.7727)" width="14" x="999.0909090909091" y="367.7272727272728"/></g>
  <g id="87">
   <use class="kv10" height="30" transform="rotate(0,1455.68,662.727) scale(0.909091,0.909091) translate(144.205,64.9091)" width="30" x="1442.045454545455" xlink:href="#Disconnector:三相刀闸_0" y="649.0909090909091" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450181464069" ObjectName="10kVI段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450181464069"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1455.68,662.727) scale(0.909091,0.909091) translate(144.205,64.9091)" width="30" x="1442.045454545455" y="649.0909090909091"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="82" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,996.864,195.773) scale(1,1) translate(0,0)" writing-mode="lr" x="996.4400000000001" xml:space="preserve" y="200.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127514472452" ObjectName="P"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="83" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,996.864,225.136) scale(1,1) translate(0,0)" writing-mode="lr" x="996.4400000000001" xml:space="preserve" y="229.88" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127514537988" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="84" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,998.682,249.045) scale(1,1) translate(0,0)" writing-mode="lr" x="998.26" xml:space="preserve" y="253.79" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127514603524" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="92" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1473.68,843.045) scale(1,1) translate(0,7.35008e-13)" writing-mode="lr" x="1473.26" xml:space="preserve" y="847.79" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127512375300" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="93" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1473.68,877.864) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.26" xml:space="preserve" y="882.61" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127512440836" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="94" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1473.68,909.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.26" xml:space="preserve" y="913.79" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127512506372" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1480.95,942.364) scale(1,1) translate(0,0)" writing-mode="lr" x="1480.53" xml:space="preserve" y="947.11" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127512571908" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(0,170,0)" font-family="SimSun" font-size="13" id="36" prefix="P:" stroke="rgb(0,170,0)" text-anchor="middle" transform="rotate(0,1233.59,941.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1233.01" xml:space="preserve" y="946.37" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127516176388" ObjectName="P"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(170,0,0)" font-family="SimSun" font-size="13" id="37" prefix="Q:" stroke="rgb(170,0,0)" text-anchor="middle" transform="rotate(0,1232.59,977.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1232.01" xml:space="preserve" y="982.37" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127516241924" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1234.59,1014.59) scale(1,1) translate(0,0)" writing-mode="lr" x="1234.01" xml:space="preserve" y="1019.37" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127516307460" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,652.864,343.773) scale(1,1) translate(0,0)" writing-mode="lr" x="652.4400000000001" xml:space="preserve" y="348.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123897802756" ObjectName="德宏地区网供负荷"/>
   </metadata>
  </g>
 </g>
 <g id="GeneratorClass">
  <g id="75">
   <use class="kv10" height="30" transform="rotate(0,1234.09,873.636) scale(1.36364,1.36364) translate(-323.636,-227.515)" width="30" x="1213.636363636364" xlink:href="#Generator:发电机_0" y="853.1818181818182" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450181332997" ObjectName="2号发电机"/>
   <cge:TPSR_Ref TObjectID="6192450181332997"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1234.09,873.636) scale(1.36364,1.36364) translate(-323.636,-227.515)" width="30" x="1213.636363636364" y="853.1818181818182"/></g>
 </g>
</svg>