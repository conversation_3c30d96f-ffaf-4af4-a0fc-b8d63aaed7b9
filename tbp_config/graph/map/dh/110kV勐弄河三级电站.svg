<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549685387265" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:带电显示器_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.41666666666667" x2="8.750000000000002" y1="11.08333333333333" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="1.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="8.5" y2="10.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="6.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="8.5" y2="8.5"/>
   <ellipse cx="10.08" cy="12.75" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.083333333333334" x2="11.08333333333333" y1="19.5" y2="19.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.666666666666668" x2="11.66666666666667" y1="11.08333333333333" y2="14.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333334" x2="11.58333333333333" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="17.5" y2="15.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.083333333333334" x2="12.08333333333333" y1="17.5" y2="17.5"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3" x2="7" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.25" x2="5.083333333333332" y1="7.083333333333336" y2="19.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_1" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.833333333333333" x2="6.916666666666667" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="5.000000000000004" y2="22.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_2" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666668" x2="1.333333333333333" y1="5.083333333333332" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="8.833333333333334" y1="4.999999999999998" y2="24.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="3" y1="23" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="12" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.3333333333333357" y2="12.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:厂用变2020_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <path d="M 13 13.5 L 17 13.5 L 15 10.5 L 13 13.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="20.66666666666666" y2="22.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="17.91666666666666" y2="20.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="25.41666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="20.66666666666666" y2="22.66666666666666"/>
   <ellipse cx="14.95" cy="20.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="12.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0833 3.5 L 14.0833 4.5 L 16.0833 4.5 L 15.0833 3.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="1" y2="7.333333333333331"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:PT2525_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,4.96) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="8.166666666666663" y1="24.91666666666666" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="10.75" y1="21.91666666666666" y2="24.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="13.75" y1="24.91666666666666" y2="26.91666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_0" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="19.75" y1="7" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="8.75" y1="2" y2="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="21.75" y1="2" y2="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.75" y1="4.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.583333333333336" x2="23.41666666666666" y1="3.583333333333333" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333332" x2="19.58333333333333" y1="6.833333333333333" y2="11.83333333333333"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_1" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="17" y1="8" y2="4"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17,10.5) scale(1,1) translate(0,0)" width="14" x="10" y="8"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.33333333333333" x2="6.000000000000004" y1="10.55" y2="10.55"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_2" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="21" y1="13" y2="7"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="21" y1="7" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="6" y2="3"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
  </symbol>
  <symbol id="Accessory:避雷器PT（德宏变）_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="31.00905664884499" xlink:href="#terminal" y="3.977945066432934"/>
   <path d="M 10 25 L 4 25 L 4 32" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 5.11667 17.3167 L 5.11667 21.3167 L 8.11667 19.3167 L 5.11667 17.3167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,31.02,18.92) scale(1,1) translate(0,0)" width="5.87" x="28.08" y="11.92"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="31" y1="12" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="31" y1="8" y2="8"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="31" y1="4" y2="12"/>
   <path d="M 31 20 L 32 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="22.16666666666667" y1="18.54585360194742" y2="18.54585360194742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.15990464876007" x2="22.15990464876007" y1="14.7557301451573" y2="22.82880868476295"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.08490464876007" x2="22.08490464876007" y1="11.65160994962663" y2="7.974999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.17943360505483" x2="22.17943360505483" y1="25.80025144679045" y2="32.48154965466559"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.07943360505483" x2="22.07943360505483" y1="22.78753864640143" y2="22.78753864640143"/>
   <ellipse cx="13.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.54255077401041" x2="18.70251205906045" y1="22.87087197973477" y2="22.87087197973477"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.07943360505483" x2="22.07943360505483" y1="11.64462828879835" y2="11.64462828879835"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067708" x2="18.61917872572711" y1="11.64462828879838" y2="11.64462828879838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067709" x2="18.61917872572711" y1="25.88279152351342" y2="25.88279152351342"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067708" x2="18.61917872572711" y1="14.73988116591035" y2="14.73988116591035"/>
   <ellipse cx="9.890000000000001" cy="25.37" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.29255077401042" x2="20.01573492932657" y1="32.54945819018009" y2="32.54945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.45921744067708" x2="20.84906826265991" y1="33.79945819018008" y2="33.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="22.87588410734375" x2="21.43240159599324" y1="35.29945819018008" y2="35.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <ellipse cx="6.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="21.21252026861409" y2="18.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="11.73744029990987" y2="10.49990031555776"/>
   <ellipse cx="6.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.98240159599324" x2="9.98240159599324" y1="27.74585360194743" y2="25.27077363324322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993235" x2="7.582401595993243" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993244" x2="12.38240159599323" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.042550774010419" x2="1.765734929326573" y1="32.04945819018009" y2="32.04945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.209217440677085" x2="2.599068262659905" y1="33.29945819018008" y2="33.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.625884107343754" x2="3.182401595993237" y1="34.79945819018008" y2="34.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.92943360505483" x2="30.92943360505483" y1="25.80025144679045" y2="32.48154965466559"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.04255077401042" x2="28.76573492932657" y1="32.54945819018009" y2="32.54945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.20921744067708" x2="29.59906826265991" y1="33.79945819018008" y2="33.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.62588410734375" x2="30.18240159599324" y1="35.29945819018008" y2="35.29945819018008"/>
   <path d="M 31 20 L 30 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.416666666666625" x2="14.8889734851558" y1="30.74450652239035" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_1" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.8889734851558" x2="14.8889734851558" y1="32.25" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_2" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.88620689655174" x2="9.700000000000015" y1="19.49501474926254" y2="32.31404129793511"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.699999999999994" x2="20.24999999999999" y1="19.33333333333333" y2="32.38333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="Accessory:空挂线路_0" viewBox="0,0,11,13">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="-0.0833333333333357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="12.91666666666667" y2="0.2500000000000009"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV勐弄河三级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="5">110kV勐弄河三级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="158" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.625,210) scale(1,1) translate(0,0)" width="72.88" x="49.19" y="198" zvalue="208"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.625,210) scale(1,1) translate(0,0)" writing-mode="lr" x="85.63" xml:space="preserve" y="214.5" zvalue="208">信号一览</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line fill="none" id="23" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="621.441921185511" y2="621.441921185511" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.2143,956.571) scale(1,1) translate(0,0)" writing-mode="lr" x="55.21" xml:space="preserve" y="962.5700000000001" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.2143,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="52.21" xml:space="preserve" y="996.5700000000001" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.214,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="234.21" xml:space="preserve" y="996.5700000000001" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.2143,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="51.21" xml:space="preserve" y="1024.57" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.214,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="233.21" xml:space="preserve" y="1024.57" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75.7143,651.071) scale(1,1) translate(0,2.10522e-13)" writing-mode="lr" x="75.71428571428555" xml:space="preserve" y="655.5714285714286" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.268,958.571) scale(1,1) translate(0,0)" writing-mode="lr" x="235.27" xml:space="preserve" y="964.5700000000001" zvalue="27">MengNong3-01-2012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,145.268,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="145.27" xml:space="preserve" y="996.5700000000001" zvalue="28"> 杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,325.268,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="325.27" xml:space="preserve" y="996.5700000000001" zvalue="29">20210222</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,656.316,368.508) scale(1,1) translate(0,0)" writing-mode="lr" x="656.3200000000001" xml:space="preserve" y="373.01" zvalue="34">131</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,550.186,464.602) scale(1,1) translate(-1.16018e-13,4.98792e-14)" writing-mode="lr" x="550.1900000000001" xml:space="preserve" y="469.1" zvalue="36">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,486.837,614.452) scale(1,1) translate(0,0)" writing-mode="lr" x="486.84" xml:space="preserve" y="618.95" zvalue="39">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" x="631.2734375" xml:space="preserve" y="966.2274740134186" zvalue="41">#1发电机     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="631.2734375" xml:space="preserve" y="982.2274740134186" zvalue="41">7.5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1548.25,327.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1548.25" xml:space="preserve" y="332.42" zvalue="43">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1548.01,45.7857) scale(1,1) translate(0,0)" writing-mode="lr" x="1548.01" xml:space="preserve" y="50.29" zvalue="45">T接35kV勐弄变10kV卡场线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,658.554,588) scale(1,1) translate(0,0)" writing-mode="lr" x="658.55" xml:space="preserve" y="592.5" zvalue="48">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.375,461.111) scale(1,1) translate(0,0)" writing-mode="lr" x="831.38" xml:space="preserve" y="465.61" zvalue="51">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709,304) scale(1,1) translate(9.30589e-13,0)" writing-mode="lr" x="709" xml:space="preserve" y="308.5" zvalue="54">-60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,656.902,286.119) scale(1,1) translate(0,0)" writing-mode="lr" x="656.9" xml:space="preserve" y="290.62" zvalue="57">1316</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706.333,215.667) scale(1,1) translate(9.27036e-13,0)" writing-mode="lr" x="706.33" xml:space="preserve" y="220.17" zvalue="59">-67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,582,299.556) scale(1,1) translate(0,0)" writing-mode="lr" x="582" xml:space="preserve" y="304.06" zvalue="61">-97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,589.206,192.786) scale(1,1) translate(0,0)" writing-mode="lr" x="589.21" xml:space="preserve" y="197.29" zvalue="63">1319</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.225,714) scale(1,1) translate(0,0)" writing-mode="lr" x="598.23" xml:space="preserve" y="718.5" zvalue="69">631</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.945,739.277) scale(1,1) translate(0,0)" writing-mode="lr" x="784.95" xml:space="preserve" y="743.78" zvalue="76">6911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.778,675.889) scale(1,1) translate(0,0)" writing-mode="lr" x="800.78" xml:space="preserve" y="680.39" zvalue="78">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,700.083,902.917) scale(1,1) translate(0,0)" writing-mode="lr" x="700.08" xml:space="preserve" y="907.42" zvalue="82">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.75,869.667) scale(1,1) translate(0,0)" writing-mode="lr" x="786.75" xml:space="preserve" y="874.17" zvalue="84">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,806.25,951.25) scale(1,1) translate(0,0)" writing-mode="lr" x="806.25" xml:space="preserve" y="955.75" zvalue="87">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" x="1091.5078125" xml:space="preserve" y="967.4774740134186" zvalue="90">#2发电机          </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1091.5078125" xml:space="preserve" y="983.4774740134186" zvalue="90">7.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1058.48,715.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1058.48" xml:space="preserve" y="719.75" zvalue="92">632</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.2,743.527) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.2" xml:space="preserve" y="748.03" zvalue="98">6921</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.03,677.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1259.03" xml:space="preserve" y="681.64" zvalue="100">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1159.56,906.705) scale(1,1) translate(0,0)" writing-mode="lr" x="1159.56" xml:space="preserve" y="911.21" zvalue="104">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1245,870.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1245" xml:space="preserve" y="875.42" zvalue="106">6922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1264.5,952.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1264.5" xml:space="preserve" y="957" zvalue="108">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1199.07,383.674) scale(1,1) translate(-7.8342e-13,0)" writing-mode="lr" x="1199.07" xml:space="preserve" y="388.17" zvalue="110">母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230.04,496.739) scale(1,1) translate(0,1.08411e-13)" writing-mode="lr" x="1230.04" xml:space="preserve" y="501.24" zvalue="112">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1569.36,707.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1569.36" xml:space="preserve" y="711.64" zvalue="118">633</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406.33,806.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1406.33" xml:space="preserve" y="810.72" zvalue="120">-67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,631.501,115.25) scale(1,1) translate(0,0)" writing-mode="lr" x="631.5" xml:space="preserve" y="119.75" zvalue="128">110kV勐二三T线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="271.75" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="297.75" y2="297.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="12" y1="271.75" y2="297.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="271.75" y2="297.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="271.75" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="297.75" y2="297.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="271.75" y2="297.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374" x2="374" y1="271.75" y2="297.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="297.75" y2="297.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="12" y1="297.75" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="297.75" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="297.75" y2="297.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="297.75" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374" x2="374" y1="297.75" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="12" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374" x2="374" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="12" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374" x2="374" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="12" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374" x2="374" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="12" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374" x2="374" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="193" y1="435.75" y2="435.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12" x2="12" y1="413" y2="435.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="413" y2="435.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="374" y1="435.75" y2="435.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193" x2="193" y1="413" y2="435.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374" x2="374" y1="413" y2="435.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.399,210.591) scale(1,1) translate(0,0)" writing-mode="lr" x="201.4" xml:space="preserve" y="215.09" zvalue="195">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.399,210.591) scale(1,1) translate(0,0)" writing-mode="lr" x="306.4" xml:space="preserve" y="215.09" zvalue="196">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,284.75) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="289.25" zvalue="197">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,284.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="289.25" zvalue="198">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.875,356.75) scale(1,1) translate(0,0)" writing-mode="lr" x="60.88" xml:space="preserve" y="361.25" zvalue="200">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,57.5,310.75) scale(1,1) translate(0,0)" writing-mode="lr" x="15" xml:space="preserve" y="315.25" zvalue="209">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238,310.75) scale(1,1) translate(0,0)" writing-mode="lr" x="195.5" xml:space="preserve" y="315.25" zvalue="210">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.6875,404) scale(1,1) translate(0,0)" writing-mode="lr" x="58.69" xml:space="preserve" y="408.5" zvalue="213">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.688,403) scale(1,1) translate(0,0)" writing-mode="lr" x="226.69" xml:space="preserve" y="407.5" zvalue="215">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.6875,427) scale(1,1) translate(0,0)" writing-mode="lr" x="58.69" xml:space="preserve" y="431.5" zvalue="217">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.688,426) scale(1,1) translate(0,0)" writing-mode="lr" x="226.69" xml:space="preserve" y="430.5" zvalue="218">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,57.5,333.75) scale(1,1) translate(0,0)" writing-mode="lr" x="15" xml:space="preserve" y="338.25" zvalue="219">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,237.5,332.75) scale(1,1) translate(0,0)" writing-mode="lr" x="195" xml:space="preserve" y="337.25" zvalue="221">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="49.19" y="198" zvalue="208"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="418">
   <use class="kv110" height="20" transform="rotate(0,630.894,360.22) scale(1.82119,1.82119) translate(-280.369,-154.214)" width="10" x="621.7881451064065" xlink:href="#Breaker:开关_0" y="342.0079365079364" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226070019" ObjectName="#1主变110kV侧131断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226070019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,630.894,360.22) scale(1.82119,1.82119) translate(-280.369,-154.214)" width="10" x="621.7881451064065" y="342.0079365079364"/></g>
  <g id="453">
   <use class="v6300" height="20" transform="rotate(0,629.679,589) scale(2.75,2.75) translate(-391.955,-357.318)" width="10" x="615.9285714285717" xlink:href="#Breaker:小车断路器_0" y="561.5" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226004483" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226004483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,629.679,589) scale(2.75,2.75) translate(-391.955,-357.318)" width="10" x="615.9285714285717" y="561.5"/></g>
  <g id="198">
   <use class="v6300" height="20" transform="rotate(0,626.475,713) scale(2.75,2.75) translate(-389.916,-436.227)" width="10" x="612.7252143314928" xlink:href="#Breaker:小车断路器_0" y="685.5" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925225938947" ObjectName="#1发电机631断路器"/>
   <cge:TPSR_Ref TObjectID="6473925225938947"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,626.475,713) scale(2.75,2.75) translate(-389.916,-436.227)" width="10" x="612.7252143314928" y="685.5"/></g>
  <g id="181">
   <use class="v6300" height="20" transform="rotate(0,1086.73,714.25) scale(2.75,2.75) translate(-682.802,-437.023)" width="10" x="1072.975214331493" xlink:href="#Breaker:小车断路器_0" y="686.75" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925225873411" ObjectName="#2发电机632断路器"/>
   <cge:TPSR_Ref TObjectID="6473925225873411"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1086.73,714.25) scale(2.75,2.75) translate(-682.802,-437.023)" width="10" x="1072.975214331493" y="686.75"/></g>
  <g id="163">
   <use class="v6300" height="20" transform="rotate(0,1537.95,708.139) scale(2.75,2.75) translate(-969.944,-433.134)" width="10" x="1524.197436553715" xlink:href="#Breaker:小车断路器_0" y="680.6388888888889" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925225807875" ObjectName="#1站用变633断路器"/>
   <cge:TPSR_Ref TObjectID="6473925225807875"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1537.95,708.139) scale(2.75,2.75) translate(-969.944,-433.134)" width="10" x="1524.197436553715" y="680.6388888888889"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv110" height="50" transform="rotate(0,629.679,461.327) scale(2.15,1.85355) translate(-319.555,-191.1)" width="30" x="597.4299999999999" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="414.99" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874596089858" ObjectName="110"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v6300" height="50" transform="rotate(0,629.679,461.327) scale(2.15,1.85355) translate(-319.555,-191.1)" width="30" x="597.4299999999999" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="414.99" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423402500" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399535099906" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399535099906"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,629.679,461.327) scale(2.15,1.85355) translate(-319.555,-191.1)" width="30" x="597.4299999999999" y="414.99"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="104">
   <path class="kv110" d="M 631.02 377.61 L 631.02 415.83" stroke-width="1" zvalue="37"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@1" LinkObjectIDznd="414@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 631.02 377.61 L 631.02 415.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv110" d="M 773.75 442.11 L 629.71 442.11" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="457@0" LinkObjectIDznd="414@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.75 442.11 L 629.71 442.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="v6300" d="M 629.68 613.75 L 629.68 638.79" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@1" LinkObjectIDznd="410@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.68 613.75 L 629.68 638.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv110" d="M 630.83 326.1 L 697.75 326.1" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100" LinkObjectIDznd="207@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.83 326.1 L 697.75 326.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv110" d="M 630.83 342.8 L 630.83 298.94" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@0" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.83 342.8 L 630.83 298.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv110" d="M 582.94 216.11 L 553.9 216.11 L 553.9 284.64" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="203@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 582.94 216.11 L 553.9 216.11 L 553.9 284.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 521.68 280.53 L 553.9 280.53" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="99" MaxPinNum="2"/>
   </metadata>
  <path d="M 521.68 280.53 L 553.9 280.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="v6300" d="M 626.48 638.79 L 626.48 687.56" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@1" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 638.79 L 626.48 687.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="v6300" d="M 629.68 563.56 L 629.68 507.01" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@0" LinkObjectIDznd="414@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.68 563.56 L 629.68 507.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="v6300" d="M 752.81 710.69 L 752.81 718.67" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.81 710.69 L 752.81 718.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="v6300" d="M 806.67 884.83 L 806.67 900.6" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.67 884.83 L 806.67 900.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="v6300" d="M 1086.73 638.79 L 1086.73 688.81" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@0" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.73 638.79 L 1086.73 688.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v6300" d="M 1086.73 739 L 1086.73 885.25" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@1" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.73 739 L 1086.73 885.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="v6300" d="M 1211.06 711.94 L 1211.06 722.92" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="176@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.06 711.94 L 1211.06 722.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="v6300" d="M 1195.31 455.69 L 1195.31 482.17" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="168@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.31 455.69 L 1195.31 482.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v6300" d="M 1537.95 732.89 L 1537.95 849.32" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1537.95 732.89 L 1537.95 849.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv110" d="M 631.5 275.11 L 631.5 176.52" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 631.5 275.11 L 631.5 176.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv110" d="M 606.78 216.08 L 631.5 216.08" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@1" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 606.78 216.08 L 631.5 216.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv110" d="M 693.75 237.21 L 631.5 237.21" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.75 237.21 L 631.5 237.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv6" d="M 1552.28 259.32 L 1552.28 231.68" stroke-width="1" zvalue="131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="183@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1552.28 259.32 L 1552.28 231.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv6" d="M 1552.28 177.6 L 1552.28 118.31" stroke-width="1" zvalue="132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="211@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1552.28 177.6 L 1552.28 118.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="v6300" d="M 626.48 737.75 L 626.48 884" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 737.75 L 626.48 884" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="v6300" d="M 516.66 778.96 L 626.48 778.96" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 516.66 778.96 L 626.48 778.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="v6300" d="M 548.06 805.5 L 548.06 778.96" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 548.06 805.5 L 548.06 778.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="v6300" d="M 753.06 847.5 L 753.06 781 L 626.48 781" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.06 847.5 L 753.06 781 L 626.48 781" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="v6300" d="M 806.67 856.5 L 806.67 823 L 753.06 823" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="75" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.67 856.5 L 806.67 823 L 753.06 823" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="v6300" d="M 702.02 841.96 L 702.02 823 L 753.06 823" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.02 841.96 L 702.02 823 L 753.06 823" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="v6300" d="M 753.71 749.75 L 753.71 781" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.71 749.75 L 753.71 781" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="v6300" d="M 1264.5 901.85 L 1264.5 886.08" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="171@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1264.5 901.85 L 1264.5 886.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v6300" d="M 974.91 780.21 L 1086.73 780.21" stroke-width="1" zvalue="141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 974.91 780.21 L 1086.73 780.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v6300" d="M 1006.31 806.75 L 1006.31 780.21" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.31 806.75 L 1006.31 780.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v6300" d="M 1210.96 848.75 L 1210.96 780.69 L 1086.73 780.69" stroke-width="1" zvalue="143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 1210.96 848.75 L 1210.96 780.69 L 1086.73 780.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="v6300" d="M 1264.92 857.75 L 1264.92 818.38 L 1210.96 818.38" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1264.92 857.75 L 1264.92 818.38 L 1210.96 818.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="v6300" d="M 1160.27 843.21 L 1160.27 819.15 L 1210.96 819.15" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1160.27 843.21 L 1160.27 819.15 L 1210.96 819.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v6300" d="M 1210.96 754 L 1210.96 780.69" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@1" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1210.96 754 L 1210.96 780.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v6300" d="M 1431.68 791.31 L 1431.68 767.62 L 1537.95 767.62" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="86" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.68 791.31 L 1431.68 767.62 L 1537.95 767.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v6300" d="M 1476.31 791.19 L 1476.31 767.62" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.31 791.19 L 1476.31 767.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v6300" d="M 1632.95 801.93 L 1632.95 773.77 L 1537.95 773.77" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="86" MaxPinNum="2"/>
   </metadata>
  <path d="M 1632.95 801.93 L 1632.95 773.77 L 1537.95 773.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v6300" d="M 821.7 545.26 L 821.7 516.08 L 629.68 516.08" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 821.7 545.26 L 821.7 516.08 L 629.68 516.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="v6300" d="M 788.72 538.17 L 788.72 516.08" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 788.72 538.17 L 788.72 516.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="v6300" d="M 1537.95 682.7 L 1537.95 638.79" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="410@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1537.95 682.7 L 1537.95 638.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="v6300" d="M 1195.21 513.25 L 1195.21 638.79" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@1" LinkObjectIDznd="410@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.21 513.25 L 1195.21 638.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="v6300" d="M 1125.06 525.5 L 1125.06 567 L 1195.21 567" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125.06 525.5 L 1125.06 567 L 1195.21 567" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="410">
   <path class="v6300" d="M 463.62 638.79 L 1696.62 638.79" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674423734275" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674423734275"/></metadata>
  <path d="M 463.62 638.79 L 1696.62 638.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v6300" height="30" transform="rotate(0,626.475,911.422) scale(1.85899,1.85899) translate(-276.593,-408.259)" width="30" x="598.5903611791396" xlink:href="#Generator:发电机_0" y="883.5367361713461" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454872793090" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454872793090"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,626.475,911.422) scale(1.85899,1.85899) translate(-276.593,-408.259)" width="30" x="598.5903611791396" y="883.5367361713461"/></g>
  <g id="182">
   <use class="v6300" height="30" transform="rotate(0,1086.73,912.672) scale(1.85899,1.85899) translate(-489.262,-408.837)" width="30" x="1058.84036117914" xlink:href="#Generator:发电机_0" y="884.7867361713461" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871154690" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454871154690"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1086.73,912.672) scale(1.85899,1.85899) translate(-489.262,-408.837)" width="30" x="1058.84036117914" y="884.7867361713461"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="399">
   <use class="kv6" height="30" transform="rotate(0,1552.12,283.925) scale(1.69643,1.70833) translate(-627.437,-107.1)" width="28" x="1528.371922478492" xlink:href="#EnergyConsumer:站用变DY接地_0" y="258.2996031746032" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454872727554" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1552.12,283.925) scale(1.69643,1.70833) translate(-627.437,-107.1)" width="28" x="1528.371922478492" y="258.2996031746032"/></g>
  <g id="159">
   <use class="v6300" height="30" transform="rotate(0,1538.79,873.925) scale(1.69643,1.70833) translate(-621.966,-351.734)" width="28" x="1515.044243907063" xlink:href="#EnergyConsumer:站用变DY接地_0" y="848.2996031746031" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870106114" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1538.79,873.925) scale(1.69643,1.70833) translate(-621.966,-351.734)" width="28" x="1515.044243907063" y="848.2996031746031"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="211">
   <use class="kv6" height="13" transform="rotate(0,1551.76,93.5804) scale(1.25,-3.75687) translate(-308.976,-100.57)" width="11" x="1544.880654761905" xlink:href="#Accessory:空挂线路_0" y="69.16071428571422" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454872662018" ObjectName="10kV外接电源"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1551.76,93.5804) scale(1.25,-3.75687) translate(-308.976,-100.57)" width="11" x="1544.880654761905" y="69.16071428571422"/></g>
  <g id="200">
   <use class="kv110" height="40" transform="rotate(0,509.444,298.333) scale(1.11111,1.11111) translate(-48.7222,-27.6111)" width="40" x="487.2222222222225" xlink:href="#Accessory:避雷器PT（德宏变）_0" y="276.1111111111112" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871941122" ObjectName="#1主变110kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,509.444,298.333) scale(1.11111,1.11111) translate(-48.7222,-27.6111)" width="40" x="487.2222222222225" y="276.1111111111112"/></g>
  <g id="195">
   <use class="v6300" height="20" transform="rotate(0,788.722,555.667) scale(1.94444,1.94444) translate(-373.649,-260.451)" width="20" x="769.2777777777778" xlink:href="#Accessory:带电显示器_0" y="536.2222222222223" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871875586" ObjectName="#1主变显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,788.722,555.667) scale(1.94444,1.94444) translate(-373.649,-260.451)" width="20" x="769.2777777777778" y="536.2222222222223"/></g>
  <g id="194">
   <use class="v6300" height="26" transform="rotate(0,821.667,559) scale(1.11111,1.11111) translate(-81.5,-54.4556)" width="12" x="815.0000000000001" xlink:href="#Accessory:避雷器1_0" y="544.5555555555555" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871810050" ObjectName="#1主变6kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,821.667,559) scale(1.11111,1.11111) translate(-81.5,-54.4556)" width="12" x="815.0000000000001" y="544.5555555555555"/></g>
  <g id="193">
   <use class="v6300" height="26" transform="rotate(90,502.917,779) scale(-1.11111,1.11111) translate(-954.875,-76.4556)" width="12" x="496.2499999999999" xlink:href="#Accessory:避雷器1_0" y="764.5555555555555" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871744514" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,502.917,779) scale(-1.11111,1.11111) translate(-954.875,-76.4556)" width="12" x="496.2499999999999" y="764.5555555555555"/></g>
  <g id="192">
   <use class="v6300" height="20" transform="rotate(0,548.056,823) scale(1.94444,1.94444) translate(-256.754,-390.298)" width="20" x="528.6111111111111" xlink:href="#Accessory:带电显示器_0" y="803.5555555555557" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871678978" ObjectName="#1发电机显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,548.056,823) scale(1.94444,1.94444) translate(-256.754,-390.298)" width="20" x="528.6111111111111" y="803.5555555555557"/></g>
  <g id="190">
   <use class="v6300" height="30" transform="rotate(0,752.889,688.444) scale(1.51852,-1.51852) translate(-249.306,-1134.03)" width="30" x="730.1111111111113" xlink:href="#Accessory:PT789_0" y="665.6666666666665" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871547906" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,752.889,688.444) scale(1.51852,-1.51852) translate(-249.306,-1134.03)" width="30" x="730.1111111111113" y="665.6666666666665"/></g>
  <g id="189">
   <use class="v6300" height="20" transform="rotate(0,753.056,865) scale(1.94444,1.94444) translate(-356.325,-410.698)" width="20" x="733.6111111111111" xlink:href="#Accessory:带电显示器_0" y="845.5555555555557" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871482370" ObjectName="#1发电机显示器2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,753.056,865) scale(1.94444,1.94444) translate(-356.325,-410.698)" width="20" x="733.6111111111111" y="845.5555555555557"/></g>
  <g id="187">
   <use class="v6300" height="30" transform="rotate(0,702.083,861.083) scale(1.30556,1.30556) translate(-159.734,-196.947)" width="30" x="682.5000000000001" xlink:href="#Accessory:PT2525_0" y="841.5000000000001" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871416834" ObjectName="#1发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,702.083,861.083) scale(1.30556,1.30556) translate(-159.734,-196.947)" width="30" x="682.5000000000001" y="841.5000000000001"/></g>
  <g id="184">
   <use class="v6300" height="29" transform="rotate(0,806.25,920.792) scale(1.41667,-1.41667) translate(-230.882,-1564.72)" width="30" x="785.0000000000001" xlink:href="#Accessory:厂用变2020_0" y="900.2499999999999" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871285762" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,806.25,920.792) scale(1.41667,-1.41667) translate(-230.882,-1564.72)" width="30" x="785.0000000000001" y="900.2499999999999"/></g>
  <g id="178">
   <use class="v6300" height="26" transform="rotate(90,961.167,780.25) scale(-1.11111,1.11111) translate(-1825.55,-76.5806)" width="12" x="954.5" xlink:href="#Accessory:避雷器1_0" y="765.8055555555555" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871089154" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,961.167,780.25) scale(-1.11111,1.11111) translate(-1825.55,-76.5806)" width="12" x="954.5" y="765.8055555555555"/></g>
  <g id="177">
   <use class="v6300" height="20" transform="rotate(0,1006.31,824.25) scale(1.94444,1.94444) translate(-479.333,-390.906)" width="20" x="986.8611111111111" xlink:href="#Accessory:带电显示器_0" y="804.8055555555557" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871023618" ObjectName="#2发电机显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1006.31,824.25) scale(1.94444,1.94444) translate(-479.333,-390.906)" width="20" x="986.8611111111111" y="804.8055555555557"/></g>
  <g id="175">
   <use class="v6300" height="30" transform="rotate(0,1211.14,689.694) scale(1.51852,-1.51852) translate(-405.782,-1136.11)" width="30" x="1188.361111111111" xlink:href="#Accessory:PT789_0" y="666.9166666666665" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870892546" ObjectName="#2发电机机组PT电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1211.14,689.694) scale(1.51852,-1.51852) translate(-405.782,-1136.11)" width="30" x="1188.361111111111" y="666.9166666666665"/></g>
  <g id="174">
   <use class="v6300" height="20" transform="rotate(0,1210.96,866.25) scale(1.94444,1.94444) translate(-578.739,-411.306)" width="20" x="1191.520479919691" xlink:href="#Accessory:带电显示器_0" y="846.8055555555557" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870827010" ObjectName="#2发电机显示器2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1210.96,866.25) scale(1.94444,1.94444) translate(-578.739,-411.306)" width="20" x="1191.520479919691" y="846.8055555555557"/></g>
  <g id="172">
   <use class="v6300" height="30" transform="rotate(0,1160.33,862.333) scale(1.30556,1.30556) translate(-266.984,-197.239)" width="30" x="1140.75" xlink:href="#Accessory:PT2525_0" y="842.7500000000001" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870761474" ObjectName="#2发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1160.33,862.333) scale(1.30556,1.30556) translate(-266.984,-197.239)" width="30" x="1140.75" y="842.7500000000001"/></g>
  <g id="170">
   <use class="v6300" height="29" transform="rotate(0,1264.5,922.042) scale(1.41667,-1.41667) translate(-365.662,-1566.85)" width="30" x="1243.25" xlink:href="#Accessory:厂用变2020_0" y="901.4999999999999" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870630402" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1264.5,922.042) scale(1.41667,-1.41667) translate(-365.662,-1566.85)" width="30" x="1243.25" y="901.4999999999999"/></g>
  <g id="169">
   <use class="v6300" height="30" transform="rotate(0,1195.39,433.444) scale(1.51852,-1.51852) translate(-400.404,-711.106)" width="30" x="1172.611111111111" xlink:href="#Accessory:PT789_0" y="410.6666666666665" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870564866" ObjectName="6.3kV母线PT电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1195.39,433.444) scale(1.51852,-1.51852) translate(-400.404,-711.106)" width="30" x="1172.611111111111" y="410.6666666666665"/></g>
  <g id="165">
   <use class="v6300" height="20" transform="rotate(0,1125.06,508) scale(1.94444,-1.94444) translate(-537.011,-759.813)" width="20" x="1105.611111111111" xlink:href="#Accessory:带电显示器_0" y="488.5555555555557" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870433794" ObjectName="母线PT电压互感器显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1125.06,508) scale(1.94444,-1.94444) translate(-537.011,-759.813)" width="20" x="1105.611111111111" y="488.5555555555557"/></g>
  <g id="161">
   <use class="v6300" height="20" transform="rotate(0,1476.31,808.694) scale(1.94444,1.94444) translate(-707.618,-383.35)" width="20" x="1456.861111111111" xlink:href="#Accessory:带电显示器_0" y="789.2500000000002" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870237186" ObjectName="#1站用变显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1476.31,808.694) scale(1.94444,1.94444) translate(-707.618,-383.35)" width="20" x="1456.861111111111" y="789.2500000000002"/></g>
  <g id="160">
   <use class="v6300" height="26" transform="rotate(0,1632.92,815.667) scale(1.11111,1.11111) translate(-162.625,-80.1222)" width="12" x="1626.25" xlink:href="#Accessory:避雷器1_0" y="801.2222222222222" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870171650" ObjectName="#1站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1632.92,815.667) scale(1.11111,1.11111) translate(-162.625,-80.1222)" width="12" x="1626.25" y="801.2222222222222"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="457">
   <use class="kv110" height="45" transform="rotate(0,783.75,462.111) scale(1.25,-1.25) translate(-151.125,-826.175)" width="45" x="755.6250000000001" xlink:href="#GroundDisconnector:12547_0" y="433.9863244035436" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454872596482" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454872596482"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,783.75,462.111) scale(1.25,-1.25) translate(-151.125,-826.175)" width="45" x="755.6250000000001" y="433.9863244035436"/></g>
  <g id="207">
   <use class="kv110" height="30" transform="rotate(270,713.667,326.111) scale(-1.11111,1.11111) translate(-1355.41,-30.9444)" width="10" x="708.1111111111112" xlink:href="#GroundDisconnector:配网地刀_0" y="309.4444444444445" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454872465410" ObjectName="#1主变110kV侧13160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454872465410"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,713.667,326.111) scale(-1.11111,1.11111) translate(-1355.41,-30.9444)" width="10" x="708.1111111111112" y="309.4444444444445"/></g>
  <g id="204">
   <use class="kv110" height="30" transform="rotate(270,709.667,237.222) scale(-1.11111,1.11111) translate(-1347.81,-22.0556)" width="10" x="704.1111111111112" xlink:href="#GroundDisconnector:配网地刀_0" y="220.5555555555555" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454872268802" ObjectName="#1主变110kV侧13167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454872268802"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,709.667,237.222) scale(-1.11111,1.11111) translate(-1347.81,-22.0556)" width="10" x="704.1111111111112" y="220.5555555555555"/></g>
  <g id="203">
   <use class="kv110" height="30" transform="rotate(0,553.889,300.556) scale(-1.33333,1.11111) translate(-967.639,-28.3889)" width="10" x="547.2222222222224" xlink:href="#GroundDisconnector:配网地刀_0" y="283.8888888888888" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454872137730" ObjectName="#1主变110kV侧13197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454872137730"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,553.889,300.556) scale(-1.33333,1.11111) translate(-967.639,-28.3889)" width="10" x="547.2222222222224" y="283.8888888888888"/></g>
  <g id="162">
   <use class="v6300" height="30" transform="rotate(180,1431.67,807.222) scale(1.11111,-1.11111) translate(-142.611,-1532.06)" width="10" x="1426.111111111111" xlink:href="#GroundDisconnector:配网地刀_0" y="790.5555555555554" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870368258" ObjectName="#1站用变63367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454870368258"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1431.67,807.222) scale(1.11111,-1.11111) translate(-142.611,-1532.06)" width="10" x="1426.111111111111" y="790.5555555555554"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="206">
   <use class="kv110" height="30" transform="rotate(0,631.569,287.119) scale(-1.11111,-0.814815) translate(-1199.15,-642.27)" width="15" x="623.2353709301144" xlink:href="#Disconnector:刀闸_0" y="274.8968390661572" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454872334338" ObjectName="#1主变110kV侧1316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454872334338"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,631.569,287.119) scale(-1.11111,-0.814815) translate(-1199.15,-642.27)" width="15" x="623.2353709301144" y="274.8968390661572"/></g>
  <g id="202">
   <use class="kv110" height="30" transform="rotate(270,594.762,216.008) scale(-1.11111,0.814815) translate(-1129.21,46.3149)" width="15" x="586.4285713807476" xlink:href="#Disconnector:刀闸_0" y="203.785727955046" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454872006658" ObjectName="#1主变110kV侧1319隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454872006658"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,594.762,216.008) scale(-1.11111,0.814815) translate(-1129.21,46.3149)" width="15" x="586.4285713807476" y="203.785727955046"/></g>
  <g id="191">
   <use class="v6300" height="26" transform="rotate(0,753.712,734.239) scale(1.2003,-1.2003) translate(-124.572,-1343.35)" width="12" x="746.5102735786277" xlink:href="#Disconnector:手车刀闸2020_0" y="718.6349206349206" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871613442" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454871613442"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,753.712,734.239) scale(1.2003,-1.2003) translate(-124.572,-1343.35)" width="12" x="746.5102735786277" y="718.6349206349206"/></g>
  <g id="186">
   <use class="v6300" height="36" transform="rotate(0,806.667,870.667) scale(0.833333,0.833333) translate(160.167,171.133)" width="14" x="800.8333333333334" xlink:href="#Disconnector:手车刀闸_0" y="855.6666666666665" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871351298" ObjectName="#1发电机励磁变6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454871351298"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,806.667,870.667) scale(0.833333,0.833333) translate(160.167,171.133)" width="14" x="800.8333333333334" y="855.6666666666665"/></g>
  <g id="183">
   <use class="kv6" height="14" transform="rotate(90,1559,204.333) scale(1.83333,1.83333) translate(-696.136,-87.0455)" width="30" x="1531.5" xlink:href="#Disconnector:站外刀闸_0" y="191.5000000000002" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454871220226" ObjectName="T接35kV勐弄变10kV卡场线隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454871220226"/></metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(90,1559,204.333) scale(1.83333,1.83333) translate(-696.136,-87.0455)" width="30" x="1531.5" y="191.5000000000002"/></g>
  <g id="176">
   <use class="v6300" height="26" transform="rotate(0,1210.96,738.489) scale(1.2003,-1.2003) translate(-200.875,-1351.14)" width="12" x="1203.760273578627" xlink:href="#Disconnector:手车刀闸2020_0" y="722.8849206349206" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870958082" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454870958082"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1210.96,738.489) scale(1.2003,-1.2003) translate(-200.875,-1351.14)" width="12" x="1203.760273578627" y="722.8849206349206"/></g>
  <g id="171">
   <use class="v6300" height="36" transform="rotate(0,1264.92,871.917) scale(0.833333,0.833333) translate(251.817,171.383)" width="14" x="1259.083333333333" xlink:href="#Disconnector:手车刀闸_0" y="856.9166666666665" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454870695938" ObjectName="#2发电机励磁变6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454870695938"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1264.92,871.917) scale(0.833333,0.833333) translate(251.817,171.383)" width="14" x="1259.083333333333" y="856.9166666666665"/></g>
  <g id="168">
   <use class="v6300" height="26" transform="rotate(0,1195.21,497.739) scale(1.2003,-1.2003) translate(-198.247,-909.815)" width="12" x="1188.010273578627" xlink:href="#Disconnector:手车刀闸2020_0" y="482.1349206349207" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449602584582" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449602584582"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1195.21,497.739) scale(1.2003,-1.2003) translate(-198.247,-909.815)" width="12" x="1188.010273578627" y="482.1349206349207"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="88" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,626.501,49.9375) scale(1,1) translate(0,0)" writing-mode="lr" x="626.03" xml:space="preserve" y="54.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136646782978" ObjectName="P"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="89" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,626.501,67.1875) scale(1,1) translate(0,0)" writing-mode="lr" x="626.03" xml:space="preserve" y="71.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136646848514" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="107" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,626.501,86.3125) scale(1,1) translate(0,0)" writing-mode="lr" x="626.03" xml:space="preserve" y="91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136646914050" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="108" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,552.179,353.119) scale(1,1) translate(0,3.78296e-14)" writing-mode="lr" x="551.63" xml:space="preserve" y="357.82" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136652943362" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="109" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,552.179,374.744) scale(1,1) translate(0,4.02721e-14)" writing-mode="lr" x="551.63" xml:space="preserve" y="379.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136653008898" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="110" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,545.929,551.333) scale(1,1) translate(0,-1.19552e-13)" writing-mode="lr" x="545.38" xml:space="preserve" y="556.04" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124354719748" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="111" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,545.929,574.208) scale(1,1) translate(0,0)" writing-mode="lr" x="545.38" xml:space="preserve" y="578.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124354785284" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="112" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,552.179,396.369) scale(1,1) translate(0,4.27146e-14)" writing-mode="lr" x="551.63" xml:space="preserve" y="401.05" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136653205506" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="113" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,545.929,594.583) scale(1,1) translate(0,0)" writing-mode="lr" x="545.38" xml:space="preserve" y="599.26" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124354850820" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="114" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1670.37,612.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1669.9" xml:space="preserve" y="617.0599999999999" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136652615682" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="115" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1086.73,988.153) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.17" xml:space="preserve" y="992.9" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136649469954" ObjectName="P"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="116" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,623.975,988.778) scale(1,1) translate(0,0)" writing-mode="lr" x="623.42" xml:space="preserve" y="993.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136651763714" ObjectName="P"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="117" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1086.73,1007.9) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.17" xml:space="preserve" y="1012.62" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136649535490" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="118" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,623.975,1010.4) scale(1,1) translate(0,0)" writing-mode="lr" x="623.42" xml:space="preserve" y="1015.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136651829250" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="119" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1086.73,1030.78) scale(1,1) translate(-2.29533e-13,0)" writing-mode="lr" x="1086.17" xml:space="preserve" y="1035.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136649601026" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="120" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,623.975,1031.4) scale(1,1) translate(0,0)" writing-mode="lr" x="623.42" xml:space="preserve" y="1036.08" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136651894786" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="167" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,158.611,284.917) scale(1,1) translate(0,0)" writing-mode="lr" x="158.77" xml:space="preserve" y="289.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136656351234" ObjectName="F"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,336.222,285.917) scale(1,1) translate(0,0)" writing-mode="lr" x="336.38" xml:space="preserve" y="290.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136656416770" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,157.222,357.917) scale(1,1) translate(0,0)" writing-mode="lr" x="157.38" xml:space="preserve" y="362.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136652812290" ObjectName="F"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="152" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,158.611,309.917) scale(1,1) translate(0,0)" writing-mode="lr" x="158.77" xml:space="preserve" y="314.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136656220162" ObjectName="F"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,336.222,310.917) scale(1,1) translate(0,0)" writing-mode="lr" x="336.38" xml:space="preserve" y="315.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136656285698" ObjectName="F"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,158.611,402.139) scale(1,1) translate(0,0)" writing-mode="lr" x="158.77" xml:space="preserve" y="407.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136655826946" ObjectName="F"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,336.611,402.139) scale(1,1) translate(0,0)" writing-mode="lr" x="336.77" xml:space="preserve" y="407.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136655958018" ObjectName="F"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,158.611,333.917) scale(1,1) translate(0,0)" writing-mode="lr" x="158.77" xml:space="preserve" y="338.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181811717" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,338.611,332.917) scale(1,1) translate(0,0)" writing-mode="lr" x="338.77" xml:space="preserve" y="337.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181746181" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,158.611,422.139) scale(1,1) translate(0,0)" writing-mode="lr" x="158.77" xml:space="preserve" y="427.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136655892482" ObjectName="F"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,336.611,422.139) scale(1,1) translate(0,0)" writing-mode="lr" x="336.77" xml:space="preserve" y="427.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136656089090" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="164">
   <use height="30" transform="rotate(0,333.673,211.107) scale(0.708333,0.665547) translate(133.02,101.07)" width="30" x="323.05" xlink:href="#State:红绿圆(方形)_0" y="201.12" zvalue="206"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374929559553" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,333.673,211.107) scale(0.708333,0.665547) translate(133.02,101.07)" width="30" x="323.05" y="201.12"/></g>
  <g id="1">
   <use height="30" transform="rotate(0,236.625,208.983) scale(0.708333,0.665547) translate(93.0588,100.002)" width="30" x="226" xlink:href="#State:红绿圆(方形)_0" y="199" zvalue="227"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962192924673" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,236.625,208.983) scale(0.708333,0.665547) translate(93.0588,100.002)" width="30" x="226" y="199"/></g>
  <g id="2">
   <use height="30" transform="rotate(0,333.812,131.464) scale(1.22222,1.03092) translate(-50.6932,-3.47934)" width="90" x="278.81" xlink:href="#State:全站检修_0" y="116" zvalue="229"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549685387265" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,333.812,131.464) scale(1.22222,1.03092) translate(-50.6932,-3.47934)" width="90" x="278.81" y="116"/></g>
 </g>
</svg>