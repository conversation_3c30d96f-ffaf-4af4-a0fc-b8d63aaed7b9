<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549683879937" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT99_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="35" xlink:href="#terminal" y="6.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.5" x2="1.5" y1="4" y2="9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.27377700368697" x2="20.43246971500333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.6817302644139" x2="34.98333333333333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.55947355115261" x2="24.55947355115261" y1="6.895129129519741" y2="14.09675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.46102695297582" x2="5.583333333333334" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.47373975336484" x2="20.47373975336484" y1="6.846289710456816" y2="6.846289710456816"/>
   <ellipse cx="24.54" cy="18.35" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="20.47373975336483" x2="20.47373975336483" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.61665011096792" x2="31.61665011096792" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="17.37848687625285" x2="17.37848687625285" y1="3.466505874834558" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="31.6166501109679" x2="31.6166501109679" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="28.52139723385593" x2="28.52139723385593" y1="3.466505874834564" y2="10.30654458978453"/>
   <ellipse cx="27.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:避雷器PT（德宏变）_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="31.00905664884499" xlink:href="#terminal" y="3.977945066432934"/>
   <path d="M 10 25 L 4 25 L 4 32" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 5.11667 17.3167 L 5.11667 21.3167 L 8.11667 19.3167 L 5.11667 17.3167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,31.02,18.92) scale(1,1) translate(0,0)" width="5.87" x="28.08" y="11.92"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="31" y1="12" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="31" y1="8" y2="8"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="31" y1="4" y2="12"/>
   <path d="M 31 20 L 32 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="22.16666666666667" y1="18.54585360194742" y2="18.54585360194742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.15990464876007" x2="22.15990464876007" y1="14.7557301451573" y2="22.82880868476295"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.08490464876007" x2="22.08490464876007" y1="11.65160994962663" y2="7.974999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.17943360505483" x2="22.17943360505483" y1="25.80025144679045" y2="32.48154965466559"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.07943360505483" x2="22.07943360505483" y1="22.78753864640143" y2="22.78753864640143"/>
   <ellipse cx="13.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.54255077401041" x2="18.70251205906045" y1="22.87087197973477" y2="22.87087197973477"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.07943360505483" x2="22.07943360505483" y1="11.64462828879835" y2="11.64462828879835"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067708" x2="18.61917872572711" y1="11.64462828879838" y2="11.64462828879838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067709" x2="18.61917872572711" y1="25.88279152351342" y2="25.88279152351342"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067708" x2="18.61917872572711" y1="14.73988116591035" y2="14.73988116591035"/>
   <ellipse cx="9.890000000000001" cy="25.37" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.29255077401042" x2="20.01573492932657" y1="32.54945819018009" y2="32.54945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.45921744067708" x2="20.84906826265991" y1="33.79945819018008" y2="33.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="22.87588410734375" x2="21.43240159599324" y1="35.29945819018008" y2="35.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <ellipse cx="6.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="21.21252026861409" y2="18.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="11.73744029990987" y2="10.49990031555776"/>
   <ellipse cx="6.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.98240159599324" x2="9.98240159599324" y1="27.74585360194743" y2="25.27077363324322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993235" x2="7.582401595993243" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993244" x2="12.38240159599323" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.042550774010419" x2="1.765734929326573" y1="32.04945819018009" y2="32.04945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.209217440677085" x2="2.599068262659905" y1="33.29945819018008" y2="33.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.625884107343754" x2="3.182401595993237" y1="34.79945819018008" y2="34.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.92943360505483" x2="30.92943360505483" y1="25.80025144679045" y2="32.48154965466559"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.04255077401042" x2="28.76573492932657" y1="32.54945819018009" y2="32.54945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.20921744067708" x2="29.59906826265991" y1="33.79945819018008" y2="33.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.62588410734375" x2="30.18240159599324" y1="35.29945819018008" y2="35.29945819018008"/>
   <path d="M 31 20 L 30 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:厂用变2020_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <path d="M 13 13.5 L 17 13.5 L 15 10.5 L 13 13.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="20.66666666666666" y2="22.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="17.91666666666666" y2="20.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="25.41666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="20.66666666666666" y2="22.66666666666666"/>
   <ellipse cx="14.95" cy="20.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="12.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0833 3.5 L 14.0833 4.5 L 16.0833 4.5 L 15.0833 3.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="1" y2="7.333333333333331"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="3" y1="23" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="12" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.3333333333333357" y2="12.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV濠散卡一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="290.5" x="44.25" xlink:href="logo.png" y="34.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.5,64.25) scale(1,1) translate(0,0)" writing-mode="lr" x="189.5" xml:space="preserve" y="67.75" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,192.625,62.3153) scale(1,1) translate(0,0)" writing-mode="lr" x="192.63" xml:space="preserve" y="71.31999999999999" zvalue="3">110kV濠散卡一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="151" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" width="72.88" x="46.19" y="173.25" zvalue="300"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.63" xml:space="preserve" y="189.75" zvalue="300">信号一览</text>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="378.25" x2="378.25" y1="2.25" y2="1032.25" zvalue="4"/>
  <line fill="none" id="31" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.250000000000455" x2="371.25" y1="138.1204926140824" y2="138.1204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,639.333,240.111) scale(1,1) translate(0,0)" writing-mode="lr" x="639.33" xml:space="preserve" y="244.61" zvalue="52">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.556,287.667) scale(1,1) translate(0,0)" writing-mode="lr" x="630.5599999999999" xml:space="preserve" y="292.17" zvalue="54">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632.222,179.667) scale(1,1) translate(0,0)" writing-mode="lr" x="632.22" xml:space="preserve" y="184.17" zvalue="57">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.932,70.6944) scale(1,1) translate(0,0)" writing-mode="lr" x="613.9299999999999" xml:space="preserve" y="75.19" zvalue="62">110kV濠散卡电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.056,278.778) scale(1,1) translate(0,0)" writing-mode="lr" x="667.0599999999999" xml:space="preserve" y="283.28" zvalue="64">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.056,226.333) scale(1,1) translate(0,0)" writing-mode="lr" x="667.0599999999999" xml:space="preserve" y="230.83" zvalue="66">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.056,171.889) scale(1,1) translate(0,0)" writing-mode="lr" x="667.0599999999999" xml:space="preserve" y="176.39" zvalue="68">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.306,384.417) scale(1,1) translate(0,0)" writing-mode="lr" x="534.3099999999999" xml:space="preserve" y="388.92" zvalue="76">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,544.333,452.944) scale(1,1) translate(0,0)" writing-mode="lr" x="544.33" xml:space="preserve" y="457.44" zvalue="78">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551.833,359.111) scale(1,1) translate(0,0)" writing-mode="lr" x="551.83" xml:space="preserve" y="363.61" zvalue="85">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.833,239.167) scale(1,1) translate(-1.97694e-13,0)" writing-mode="lr" x="901.83" xml:space="preserve" y="243.67" zvalue="88">152</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,893.056,286.722) scale(1,1) translate(1.28533e-12,0)" writing-mode="lr" x="893.0599999999999" xml:space="preserve" y="291.22" zvalue="89">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,894.722,178.722) scale(1,1) translate(7.9112e-13,0)" writing-mode="lr" x="894.72" xml:space="preserve" y="183.22" zvalue="92">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876.432,67.25) scale(1,1) translate(0,0)" writing-mode="lr" x="876.4299999999999" xml:space="preserve" y="71.75" zvalue="96">110kV濠散卡一二级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.556,274.167) scale(1,1) translate(0,0)" writing-mode="lr" x="929.5599999999999" xml:space="preserve" y="278.67" zvalue="99">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.556,225.389) scale(1,1) translate(0,0)" writing-mode="lr" x="929.5599999999999" xml:space="preserve" y="229.89" zvalue="101">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.556,170.944) scale(1,1) translate(-2.04626e-13,0)" writing-mode="lr" x="929.5599999999999" xml:space="preserve" y="175.44" zvalue="103">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1186.89,292.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1186.89" xml:space="preserve" y="297.42" zvalue="112">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516,524.625) scale(1,1) translate(0,0)" writing-mode="lr" x="516" xml:space="preserve" y="529.13" zvalue="123">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089.53,358.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1089.53" xml:space="preserve" y="362.97" zvalue="126">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1098.86,414.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1098.86" xml:space="preserve" y="418.97" zvalue="128">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1121.19,391.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.19" xml:space="preserve" y="395.97" zvalue="131">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1152.62,490.089) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.63" xml:space="preserve" y="494.59" zvalue="134">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.617,543.75) scale(1,1) translate(0,0)" writing-mode="lr" x="891.62" xml:space="preserve" y="548.25" zvalue="142">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1152.62,506.339) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.63" xml:space="preserve" y="510.84" zvalue="143">10MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1580.4,642.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1580.4" xml:space="preserve" y="647.42" zvalue="145">10.5kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,797.31,722.615) scale(1,1) translate(0,0)" writing-mode="lr" x="797.3099999999999" xml:space="preserve" y="727.12" zvalue="147">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.836,958.417) scale(1,1) translate(0,-2.10042e-13)" writing-mode="lr" x="773.8360901404426" xml:space="preserve" y="962.917452363313" zvalue="151">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.024,863.531) scale(1,1) translate(-3.13382e-13,-5.68623e-13)" writing-mode="lr" x="723.02" xml:space="preserve" y="868.03" zvalue="154">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,840.713,876.821) scale(1,1) translate(3.65371e-13,-5.77476e-13)" writing-mode="lr" x="840.71" xml:space="preserve" y="881.3200000000001" zvalue="157">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.964,975.255) scale(1,1) translate(0,2.46175e-12)" writing-mode="lr" x="773.9643058361017" xml:space="preserve" y="979.7551648754704" zvalue="165">4MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1451.19,723.103) scale(1,1) translate(0,0)" writing-mode="lr" x="1451.19" xml:space="preserve" y="727.6" zvalue="193">052</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1435.41,962.527) scale(1,1) translate(0,2.1162e-13)" writing-mode="lr" x="1435.410963253273" xml:space="preserve" y="967.0267086596286" zvalue="197">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1379.86,873.256) scale(1,1) translate(0,2.49277e-12)" writing-mode="lr" x="1379.86" xml:space="preserve" y="877.76" zvalue="200">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1488.21,870.357) scale(1,1) translate(-3.91862e-12,2.48441e-12)" writing-mode="lr" x="1488.21" xml:space="preserve" y="874.86" zvalue="203">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1436.53,980.185) scale(1,1) translate(0,-1.93297e-12)" writing-mode="lr" x="1436.532751510264" xml:space="preserve" y="984.6846262361162" zvalue="209">4MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1100.4,627.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.4" xml:space="preserve" y="632.11" zvalue="231">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1471.48,592.118) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.48" xml:space="preserve" y="596.62" zvalue="236">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444.62,477.438) scale(1,1) translate(-6.04294e-13,-1.03352e-13)" writing-mode="lr" x="1444.63" xml:space="preserve" y="481.94" zvalue="241">10.5kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070.88,897.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1070.88" xml:space="preserve" y="902" zvalue="244">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1100.35,732.696) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.35" xml:space="preserve" y="737.2" zvalue="246">053</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="247" y2="247"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="247" y2="247"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="411" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="411" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="388.25" y2="411"/>
  <line fill="none" id="195" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="492.8704926140824" y2="492.8704926140824" zvalue="279"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="281">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="282">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="283">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="284">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="285">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,566.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="571" zvalue="287">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="198.4" xml:space="preserve" y="190.34" zvalue="288">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="303.4" xml:space="preserve" y="190.34" zvalue="289">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,260) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="264.5" zvalue="290">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,260) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="264.5" zvalue="291">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.6875,333.25) scale(1,1) translate(0,0)" writing-mode="lr" x="58.69" xml:space="preserve" y="337.75" zvalue="292">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.875,332) scale(1,1) translate(0,0)" writing-mode="lr" x="236.88" xml:space="preserve" y="336.5" zvalue="293">10.5kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,286) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="290.5" zvalue="301">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,286) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="290.5" zvalue="302">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,379.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="383.75" zvalue="305">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,378.25) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="382.75" zvalue="307">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,402.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="406.75" zvalue="309">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,401.25) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="405.75" zvalue="310">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,309) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="313.5" zvalue="311">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234.5,308) scale(1,1) translate(0,0)" writing-mode="lr" x="192" xml:space="preserve" y="312.5" zvalue="313">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,203,950) scale(1,1) translate(0,0)" writing-mode="lr" x="203" xml:space="preserve" y="956" zvalue="320">HaoSanKa1-01-2018</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="46.19" y="173.25" zvalue="300"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="58">
   <use class="kv110" height="20" transform="rotate(0,618,241.111) scale(1.22222,1.11111) translate(-111.253,-23)" width="10" x="611.8888888888889" xlink:href="#Breaker:开关_0" y="230" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211783171" ObjectName="110kV濠散卡电站线151断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211783171"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,618,241.111) scale(1.22222,1.11111) translate(-111.253,-23)" width="10" x="611.8888888888889" y="230"/></g>
  <g id="273">
   <use class="kv110" height="20" transform="rotate(0,880.5,240.167) scale(1.22222,1.11111) translate(-158.98,-22.9056)" width="10" x="874.3888888888889" xlink:href="#Breaker:开关_0" y="229.055555343628" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211848707" ObjectName="110kV濠散卡一二级线152断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211848707"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,880.5,240.167) scale(1.22222,1.11111) translate(-158.98,-22.9056)" width="10" x="874.3888888888889" y="229.055555343628"/></g>
  <g id="111">
   <use class="kv110" height="20" transform="rotate(0,1078.08,415.472) scale(1.22222,1.11111) translate(-194.904,-40.4361)" width="10" x="1071.972232407994" xlink:href="#Breaker:开关_0" y="404.3611111111111" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211914243" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211914243"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1078.08,415.472) scale(1.22222,1.11111) translate(-194.904,-40.4361)" width="10" x="1071.972232407994" y="404.3611111111111"/></g>
  <g id="341">
   <use class="v10500" height="20" transform="rotate(0,770.297,723.696) scale(2.16108,2.16108) translate(-408.051,-377.209)" width="10" x="759.4912202553933" xlink:href="#Breaker:手车开关_0" y="702.084974364315" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211979779" ObjectName="#1发电机051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211979779"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,770.297,723.696) scale(2.16108,2.16108) translate(-408.051,-377.209)" width="10" x="759.4912202553933" y="702.084974364315"/></g>
  <g id="179">
   <use class="v10500" height="20" transform="rotate(0,1424.81,724.159) scale(2.11059,2.11059) translate(-744.182,-369.946)" width="10" x="1414.258543837723" xlink:href="#Breaker:手车开关_0" y="703.0526151501956" zvalue="192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212045315" ObjectName="#2发电机052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212045315"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1424.81,724.159) scale(2.11059,2.11059) translate(-744.182,-369.946)" width="10" x="1414.258543837723" y="703.0526151501956"/></g>
  <g id="191">
   <use class="v10500" height="20" transform="rotate(0,1075.28,628.611) scale(2,2) translate(-532.64,-304.306)" width="10" x="1065.279569585283" xlink:href="#Breaker:手车开关_0" y="608.6111128065321" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212110851" ObjectName="#1主变10.5kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212110851"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1075.28,628.611) scale(2,2) translate(-532.64,-304.306)" width="10" x="1065.279569585283" y="608.6111128065321"/></g>
  <g id="206">
   <use class="v10500" height="20" transform="rotate(0,1073.8,733.696) scale(2.16108,2.16108) translate(-571.113,-382.581)" width="10" x="1062.991220255393" xlink:href="#Breaker:手车开关_0" y="712.084974364315" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212176387" ObjectName="#1站用变053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212176387"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1073.8,733.696) scale(2.16108,2.16108) translate(-571.113,-382.581)" width="10" x="1062.991220255393" y="712.084974364315"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="57">
   <use class="kv110" height="30" transform="rotate(0,618,288.667) scale(-1.11111,-0.814815) translate(-1173.37,-645.717)" width="15" x="609.6666666666666" xlink:href="#Disconnector:刀闸_0" y="276.4444580078125" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454753320962" ObjectName="110kV濠散卡电站线1511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454753320962"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,618,288.667) scale(-1.11111,-0.814815) translate(-1173.37,-645.717)" width="15" x="609.6666666666666" y="276.4444580078125"/></g>
  <g id="56">
   <use class="kv110" height="30" transform="rotate(0,618,180.667) scale(-1.11111,-0.814815) translate(-1173.37,-405.172)" width="15" x="609.6666666931577" xlink:href="#Disconnector:刀闸_0" y="168.4444444444445" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454753255426" ObjectName="110kV濠散卡电站线1516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454753255426"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,618,180.667) scale(-1.11111,-0.814815) translate(-1173.37,-405.172)" width="15" x="609.6666666931577" y="168.4444444444445"/></g>
  <g id="89">
   <use class="kv110" height="30" transform="rotate(0,508.972,385.417) scale(-1.11111,-0.814815) translate(-966.214,-861.206)" width="15" x="500.6388888888888" xlink:href="#Disconnector:刀闸_0" y="373.1944581137762" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754304002" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454754304002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,508.972,385.417) scale(-1.11111,-0.814815) translate(-966.214,-861.206)" width="15" x="500.6388888888888" y="373.1944581137762"/></g>
  <g id="272">
   <use class="kv110" height="30" transform="rotate(0,880.5,287.722) scale(-1.11111,-0.814815) translate(-1672.12,-643.614)" width="15" x="872.1666666666666" xlink:href="#Disconnector:刀闸_0" y="275.5000133514404" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454753976322" ObjectName="110kV濠散卡一二级线1521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454753976322"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,880.5,287.722) scale(-1.11111,-0.814815) translate(-1672.12,-643.614)" width="15" x="872.1666666666666" y="275.5000133514404"/></g>
  <g id="271">
   <use class="kv110" height="30" transform="rotate(0,880.5,179.722) scale(-1.11111,-0.814815) translate(-1672.12,-403.068)" width="15" x="872.1666666931577" xlink:href="#Disconnector:刀闸_0" y="167.4999997880724" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454753910786" ObjectName="110kV濠散卡一二级线1526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454753910786"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,880.5,179.722) scale(-1.11111,-0.814815) translate(-1672.12,-403.068)" width="15" x="872.1666666931577" y="167.4999997880724"/></g>
  <g id="112">
   <use class="kv110" height="30" transform="rotate(0,1078.08,359.472) scale(1.11111,0.814815) translate(-106.975,78.9205)" width="15" x="1069.750010172526" xlink:href="#Disconnector:刀闸_0" y="347.25" zvalue="125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754697218" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454754697218"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1078.08,359.472) scale(1.11111,0.814815) translate(-106.975,78.9205)" width="15" x="1069.750010172526" y="347.25"/></g>
  <g id="132">
   <use class="v10500" height="26" transform="rotate(0,695.759,864.77) scale(1.23933,1.23933) translate(-132.922,-163.885)" width="12" x="688.3225404732252" xlink:href="#Disconnector:手车刀闸2020_0" y="848.6588615061935" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754959362" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454754959362"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,695.759,864.77) scale(1.23933,1.23933) translate(-132.922,-163.885)" width="12" x="688.3225404732252" y="848.6588615061935"/></g>
  <g id="129">
   <use class="v10500" height="26" transform="rotate(0,866.681,877.155) scale(1.23933,1.23933) translate(-165.929,-166.277)" width="12" x="859.2454530434896" xlink:href="#Disconnector:手车刀闸2020_0" y="861.044054079156" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754893826" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454754893826"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,866.681,877.155) scale(1.23933,1.23933) translate(-165.929,-166.277)" width="12" x="859.2454530434896" y="861.044054079156"/></g>
  <g id="175">
   <use class="v10500" height="26" transform="rotate(0,1353.24,874.466) scale(1.21037,1.21037) translate(-233.939,-149.253)" width="12" x="1345.973478069158" xlink:href="#Disconnector:手车刀闸2020_0" y="858.7310843681984" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755155970" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454755155970"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1353.24,874.466) scale(1.21037,1.21037) translate(-233.939,-149.253)" width="12" x="1345.973478069158" y="858.7310843681984"/></g>
  <g id="172">
   <use class="v10500" height="26" transform="rotate(0,1520.17,868.045) scale(1.21037,1.21037) translate(-262.953,-148.137)" width="12" x="1512.902864644789" xlink:href="#Disconnector:手车刀闸2020_0" y="852.3105481427269" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755090434" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454755090434"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1520.17,868.045) scale(1.21037,1.21037) translate(-262.953,-148.137)" width="12" x="1512.902864644789" y="852.3105481427269"/></g>
  <g id="196">
   <use class="v10500" height="26" transform="rotate(0,1442.2,593.118) scale(1.52661,-1.52661) translate(-494.335,-974.791)" width="12" x="1433.039242237126" xlink:href="#Disconnector:手车刀闸2020_0" y="573.2721947576339" zvalue="235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755811330" ObjectName="10.5kV母线0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454755811330"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1442.2,593.118) scale(1.52661,-1.52661) translate(-494.335,-974.791)" width="12" x="1433.039242237126" y="573.2721947576339"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="55">
   <path class="kv110" d="M 617.9 300.48 L 617.9 319.75" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="85@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.9 300.48 L 617.9 319.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv110" d="M 617.93 276.65 L 617.93 251.72" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@1" LinkObjectIDznd="58@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.93 276.65 L 617.93 251.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv110" d="M 617.96 230.48 L 617.9 192.48" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.96 230.48 L 617.9 192.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv110" d="M 617.93 168.65 L 617.93 129.47" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="52@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.93 168.65 L 617.93 129.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv110" d="M 599.5 153.65 L 617.93 153.65" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="51" MaxPinNum="2"/>
   </metadata>
  <path d="M 599.5 153.65 L 617.93 153.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv110" d="M 654.78 157.39 L 617.93 157.39" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="51" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.78 157.39 L 617.93 157.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv110" d="M 654.78 211.83 L 617.93 211.83" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="53" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.78 211.83 L 617.93 211.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv110" d="M 654.78 264.28 L 617.93 264.28" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="54" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.78 264.28 L 617.93 264.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv110" d="M 880.4 299.54 L 880.4 319.75" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="85@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.4 299.54 L 880.4 319.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 880.43 275.71 L 880.43 250.78" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.43 275.71 L 880.43 250.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv110" d="M 880.46 229.54 L 880.4 191.54" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.46 229.54 L 880.4 191.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv110" d="M 880.43 167.71 L 880.43 126.03" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.43 167.71 L 880.43 126.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv110" d="M 862 152.71 L 880.43 152.71" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 862 152.71 L 880.43 152.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv110" d="M 917.28 156.44 L 880.43 156.44" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.28 156.44 L 880.43 156.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 917.28 210.89 L 880.43 210.89" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.28 210.89 L 880.43 210.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv110" d="M 917.28 259.67 L 880.43 259.67" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.28 259.67 L 880.43 259.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv110" d="M 656.88 134.97 L 617.93 134.97" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375@0" LinkObjectIDznd="51" MaxPinNum="2"/>
   </metadata>
  <path d="M 656.88 134.97 L 617.93 134.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 921.88 134.97 L 880.43 134.97" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 921.88 134.97 L 880.43 134.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv110" d="M 508.9 373.4 L 508.9 319.75" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 508.9 373.4 L 508.9 319.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv110" d="M 541 345.61 L 508.9 345.61" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 541 345.61 L 508.9 345.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv110" d="M 533.5 436.78 L 508.87 436.78 L 508.87 397.23" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="89@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 533.5 436.78 L 508.87 436.78 L 508.87 397.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 509.74 471.97 L 509.74 436.78" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="94" MaxPinNum="2"/>
   </metadata>
  <path d="M 509.74 471.97 L 509.74 436.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv110" d="M 1078.15 371.49 L 1078.15 404.84" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@1" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1078.15 371.49 L 1078.15 404.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv110" d="M 1078.18 347.65 L 1078.18 319.75" stroke-width="1" zvalue="132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="85@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1078.18 347.65 L 1078.18 319.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv110" d="M 1078.16 426.08 L 1078.16 468.09" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@1" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1078.16 426.08 L 1078.16 468.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv110" d="M 1110.36 379.31 L 1078.15 379.31" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 1110.36 379.31 L 1078.15 379.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv110" d="M 1076.29 494.37 L 931.45 494.37 L 931.45 522.15" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@2" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.29 494.37 L 931.45 494.37 L 931.45 522.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="v10500" d="M 770.3 703.71 L 770.3 666" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="117@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 770.3 703.71 L 770.3 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="v10500" d="M 769.05 881.97 L 769.05 743.15" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.05 881.97 L 769.05 743.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="v10500" d="M 695.76 848.75 L 695.76 835.21 L 769.05 835.21" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@1" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.76 848.75 L 695.76 835.21 L 769.05 835.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="v10500" d="M 866.79 893.23 L 866.79 902.11" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="186@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.79 893.23 L 866.79 902.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="v10500" d="M 1424.81 704.64 L 1424.81 666" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="117@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.81 704.64 L 1424.81 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="v10500" d="M 1424.81 877.51 L 1424.81 743.15" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="179@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.81 877.51 L 1424.81 743.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="v10500" d="M 1520.27 883.74 L 1520.27 897.18" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="188@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1520.27 883.74 L 1520.27 897.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="v10500" d="M 819.67 784.49 L 769.05 784.49" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.67 784.49 L 769.05 784.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="v10500" d="M 1460.54 776.37 L 1424.81 776.28" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="177" MaxPinNum="2"/>
   </metadata>
  <path d="M 1460.54 776.37 L 1424.81 776.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="v10500" d="M 1076.25 559.27 L 1076.25 610.11" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.25 559.27 L 1076.25 610.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="v10500" d="M 1075.28 646.61 L 1075.28 666" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="117@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.28 646.61 L 1075.28 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="v10500" d="M 1442.2 612.85 L 1442.2 666" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@1" LinkObjectIDznd="117@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.2 612.85 L 1442.2 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="v10500" d="M 1474.65 627.05 L 1442.2 627.05" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 1474.65 627.05 L 1442.2 627.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="v10500" d="M 1442.13 541.45 L 1442.13 573.32" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="196@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.13 541.45 L 1442.13 573.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="v10500" d="M 1073.8 666 L 1073.8 713.71" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@3" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.8 666 L 1073.8 713.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="v10500" d="M 1073.8 753.15 L 1073.8 828.89" stroke-width="1" zvalue="247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.8 753.15 L 1073.8 828.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="v10500" d="M 769.05 834.75 L 912.8 834.75 L 912.8 891.67" stroke-width="1" zvalue="248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133" LinkObjectIDznd="189@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.05 834.75 L 912.8 834.75 L 912.8 891.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="v10500" d="M 866.68 834.75 L 866.68 861.14" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211" LinkObjectIDznd="129@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.68 834.75 L 866.68 861.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="v10500" d="M 1424.81 834.09 L 1556.43 834.09 L 1556.43 896.56" stroke-width="1" zvalue="250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.81 834.09 L 1556.43 834.09 L 1556.43 896.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="v10500" d="M 1520.17 852.4 L 1520.17 834.09" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@1" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 1520.17 852.4 L 1520.17 834.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="v10500" d="M 695.25 920.07 L 695.25 880.84" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="132@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.25 920.07 L 695.25 880.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="v10500" d="M 1353.34 912.93 L 1353.34 890.16" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1353.34 912.93 L 1353.34 890.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="v10500" d="M 1424.81 834.75 L 1353.24 834.75 L 1353.24 858.82" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177" LinkObjectIDznd="175@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.81 834.75 L 1353.24 834.75 L 1353.24 858.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="50">
   <use class="kv110" height="20" transform="rotate(270,665.611,264.222) scale(-1.11111,1.11111) translate(-1264.11,-25.3111)" width="10" x="660.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="253.1111111111111" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454753124354" ObjectName="110kV濠散卡电站线15117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454753124354"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,665.611,264.222) scale(-1.11111,1.11111) translate(-1264.11,-25.3111)" width="10" x="660.0555555555555" y="253.1111111111111"/></g>
  <g id="49">
   <use class="kv110" height="20" transform="rotate(270,665.611,211.778) scale(-1.11111,1.11111) translate(-1264.11,-20.0667)" width="10" x="660.0555555555557" xlink:href="#GroundDisconnector:地刀_0" y="200.6666666666667" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454752993282" ObjectName="110kV濠散卡电站线15160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454752993282"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,665.611,211.778) scale(-1.11111,1.11111) translate(-1264.11,-20.0667)" width="10" x="660.0555555555557" y="200.6666666666667"/></g>
  <g id="48">
   <use class="kv110" height="20" transform="rotate(270,665.611,157.333) scale(-1.11111,1.11111) translate(-1264.11,-14.6222)" width="10" x="660.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="146.2222222222221" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454752862210" ObjectName="110kV濠散卡电站线15167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454752862210"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,665.611,157.333) scale(-1.11111,1.11111) translate(-1264.11,-14.6222)" width="10" x="660.0555556615193" y="146.2222222222221"/></g>
  <g id="91">
   <use class="kv110" height="20" transform="rotate(270,544.333,436.722) scale(-1.11111,1.11111) translate(-1033.68,-42.5611)" width="10" x="538.7777845594617" xlink:href="#GroundDisconnector:地刀_0" y="425.6111008326212" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754238466" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454754238466"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,544.333,436.722) scale(-1.11111,1.11111) translate(-1033.68,-42.5611)" width="10" x="538.7777845594617" y="425.6111008326212"/></g>
  <g id="80">
   <use class="kv110" height="20" transform="rotate(270,551.833,345.556) scale(-1.11111,1.11111) translate(-1047.93,-33.4444)" width="10" x="546.2777845594617" xlink:href="#GroundDisconnector:地刀_0" y="334.4444444444444" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754107394" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454754107394"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,551.833,345.556) scale(-1.11111,1.11111) translate(-1047.93,-33.4444)" width="10" x="546.2777845594617" y="334.4444444444444"/></g>
  <g id="265">
   <use class="kv110" height="20" transform="rotate(270,928.111,259.611) scale(-1.11111,1.11111) translate(-1762.86,-24.85)" width="10" x="922.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="248.500005086263" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454753779714" ObjectName="110kV濠散卡一二级线15217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454753779714"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,928.111,259.611) scale(-1.11111,1.11111) translate(-1762.86,-24.85)" width="10" x="922.5555555555555" y="248.500005086263"/></g>
  <g id="264">
   <use class="kv110" height="20" transform="rotate(270,928.111,210.833) scale(-1.11111,1.11111) translate(-1762.86,-19.9722)" width="10" x="922.5555555555557" xlink:href="#GroundDisconnector:地刀_0" y="199.7222220102947" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454753648642" ObjectName="110kV濠散卡一二级线15260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454753648642"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,928.111,210.833) scale(-1.11111,1.11111) translate(-1762.86,-19.9722)" width="10" x="922.5555555555557" y="199.7222220102947"/></g>
  <g id="263">
   <use class="kv110" height="20" transform="rotate(270,928.111,156.389) scale(-1.11111,1.11111) translate(-1762.86,-14.5278)" width="10" x="922.5555556615193" xlink:href="#GroundDisconnector:地刀_0" y="145.2777775658501" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454753517570" ObjectName="110kV濠散卡一二级线15267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454753517570"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,928.111,156.389) scale(-1.11111,1.11111) translate(-1762.86,-14.5278)" width="10" x="922.5555556615193" y="145.2777775658501"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(270,1121.19,379.25) scale(-1.11111,1.11111) translate(-2129.71,-36.8139)" width="10" x="1115.638899061415" xlink:href="#GroundDisconnector:地刀_0" y="368.1388888888889" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754631682" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454754631682"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1121.19,379.25) scale(-1.11111,1.11111) translate(-2129.71,-36.8139)" width="10" x="1115.638899061415" y="368.1388888888889"/></g>
  <g id="230">
   <use class="kv110" height="40" transform="rotate(0,928.809,537.636) scale(1.01543,-1.26928) translate(-13.8021,-955.824)" width="40" x="908.5" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="512.25" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754828290" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454754828290"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,928.809,537.636) scale(1.01543,-1.26928) translate(-13.8021,-955.824)" width="40" x="908.5" y="512.25"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="47">
   <use class="kv110" height="30" transform="rotate(0,582,162) scale(1,1) translate(0,0)" width="35" x="564.5" xlink:href="#Accessory:线路PT99_0" y="147" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454752731138" ObjectName="110kV濠散卡电站线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,582,162) scale(1,1) translate(0,0)" width="35" x="564.5" y="147"/></g>
  <g id="262">
   <use class="kv110" height="30" transform="rotate(0,844.5,161.056) scale(1,1) translate(0,0)" width="35" x="827" xlink:href="#Accessory:线路PT99_0" y="146.055555343628" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454753386498" ObjectName="110kV濠散卡一二级线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,844.5,161.056) scale(1,1) translate(0,0)" width="35" x="827" y="146.055555343628"/></g>
  <g id="375">
   <use class="kv110" height="26" transform="rotate(270,669.25,135) scale(1,1) translate(0,0)" width="12" x="663.25" xlink:href="#Accessory:避雷器1_0" y="122" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754369538" ObjectName="避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,669.25,135) scale(1,1) translate(0,0)" width="12" x="663.25" y="122"/></g>
  <g id="88">
   <use class="kv110" height="26" transform="rotate(270,934.25,135) scale(1,1) translate(0,0)" width="12" x="928.25" xlink:href="#Accessory:避雷器1_0" y="122" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754435074" ObjectName="避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,934.25,135) scale(1,1) translate(0,0)" width="12" x="928.25" y="122"/></g>
  <g id="96">
   <use class="kv110" height="40" transform="rotate(0,523.5,492) scale(-1.25,1.25) translate(-937.3,-93.4)" width="40" x="498.5" xlink:href="#Accessory:避雷器PT（德宏变）_0" y="467" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454754500610" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,523.5,492) scale(-1.25,1.25) translate(-937.3,-93.4)" width="40" x="498.5" y="467"/></g>
  <g id="181">
   <use class="v10500" height="26" transform="rotate(270,833.037,784.526) scale(1.08054,1.08054) translate(-61.6103,-57.4305)" width="12" x="826.5535673143577" xlink:href="#Accessory:避雷器1_0" y="770.4786682615629" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755287042" ObjectName="避雷器3"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,833.037,784.526) scale(1.08054,1.08054) translate(-61.6103,-57.4305)" width="12" x="826.5535673143577" y="770.4786682615629"/></g>
  <g id="182">
   <use class="v10500" height="26" transform="rotate(270,1473.59,776.407) scale(1.0553,1.0553) translate(-76.882,-39.9637)" width="12" x="1467.255960669453" xlink:href="#Accessory:避雷器1_0" y="762.6881452672186" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755352578" ObjectName="避雷器4"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1473.59,776.407) scale(1.0553,1.0553) translate(-76.882,-39.9637)" width="12" x="1467.255960669453" y="762.6881452672186"/></g>
  <g id="185">
   <use class="v10500" height="29" transform="rotate(0,695.25,937.883) scale(1.25,-1.25) translate(-135.3,-1684.56)" width="30" x="676.5" xlink:href="#Accessory:PT12321_0" y="919.7579345703125" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755418114" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,695.25,937.883) scale(1.25,-1.25) translate(-135.3,-1684.56)" width="30" x="676.5" y="919.7579345703125"/></g>
  <g id="186">
   <use class="v10500" height="29" transform="rotate(0,866.786,921.358) scale(1.35068,-1.35068) translate(-219.784,-1598.42)" width="30" x="846.5254331841718" xlink:href="#Accessory:PT12321_0" y="901.773127067602" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755483650" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,866.786,921.358) scale(1.35068,-1.35068) translate(-219.784,-1598.42)" width="30" x="846.5254331841718" y="901.773127067602"/></g>
  <g id="187">
   <use class="v10500" height="29" transform="rotate(0,1353.34,931.732) scale(1.31912,-1.31912) translate(-322.611,-1633.43)" width="30" x="1333.550654935525" xlink:href="#Accessory:PT12321_0" y="912.6043624164899" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755549186" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1353.34,931.732) scale(1.31912,-1.31912) translate(-322.611,-1633.43)" width="30" x="1333.550654935525" y="912.6043624164899"/></g>
  <g id="188">
   <use class="v10500" height="29" transform="rotate(0,1520.27,915.982) scale(1.31912,-1.31912) translate(-362.994,-1605.74)" width="30" x="1500.480041511157" xlink:href="#Accessory:PT12321_0" y="896.8543624164899" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755614722" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1520.27,915.982) scale(1.31912,-1.31912) translate(-362.994,-1605.74)" width="30" x="1500.480041511157" y="896.8543624164899"/></g>
  <g id="189">
   <use class="v10500" height="29" transform="rotate(0,912.796,913.487) scale(1.53077,-1.53077) translate(-308.535,-1502.54)" width="30" x="889.8340697067018" xlink:href="#Accessory:厂用变2020_0" y="891.2905701754385" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755680258" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,912.796,913.487) scale(1.53077,-1.53077) translate(-308.535,-1502.54)" width="30" x="889.8340697067018" y="891.2905701754385"/></g>
  <g id="190">
   <use class="v10500" height="29" transform="rotate(0,1556.43,917.864) scale(1.495,-1.495) translate(-507.916,-1524.64)" width="30" x="1534.003429516805" xlink:href="#Accessory:厂用变2020_0" y="896.1864291277257" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755745794" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1556.43,917.864) scale(1.495,-1.495) translate(-507.916,-1524.64)" width="30" x="1534.003429516805" y="896.1864291277257"/></g>
  <g id="198">
   <use class="v10500" height="26" transform="rotate(270,1491.11,627.098) scale(1.33102,1.33102) translate(-368.849,-151.655)" width="12" x="1483.124059415795" xlink:href="#Accessory:避雷器1_0" y="609.7951042750069" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755876866" ObjectName="避雷器5"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1491.11,627.098) scale(1.33102,1.33102) translate(-368.849,-151.655)" width="12" x="1483.124059415795" y="609.7951042750069"/></g>
  <g id="200">
   <use class="v10500" height="20" transform="rotate(0,1442.12,525.683) scale(1.5766,-1.5766) translate(-520.211,-853.345)" width="25" x="1422.417528195489" xlink:href="#Accessory:4绕组母线PT_0" y="509.9168233082708" zvalue="240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755942402" ObjectName="10.5kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1442.12,525.683) scale(1.5766,-1.5766) translate(-520.211,-853.345)" width="25" x="1422.417528195489" y="509.9168233082708"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="85">
   <path class="kv110" d="M 448.5 319.75 L 1208.5 319.75" stroke-width="6" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420195331" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420195331"/></metadata>
  <path d="M 448.5 319.75 L 1208.5 319.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="v10500" d="M 650.3 666 L 1593.03 666" stroke-width="6" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420260867" ObjectName="10.5kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420260867"/></metadata>
  <path d="M 650.3 666 L 1593.03 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="108">
   <g id="1080">
    <use class="kv110" height="50" transform="rotate(0,1076.25,513.589) scale(2.15,1.85355) translate(-558.419,-215.166)" width="30" x="1044" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="467.25" zvalue="133"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590519298" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1081">
    <use class="v10500" height="50" transform="rotate(0,1076.25,513.589) scale(2.15,1.85355) translate(-558.419,-215.166)" width="30" x="1044" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="467.25" zvalue="133"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590584834" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532544002" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399532544002"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1076.25,513.589) scale(2.15,1.85355) translate(-558.419,-215.166)" width="30" x="1044" y="467.25"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v10500" height="30" transform="rotate(0,769.047,909.386) scale(1.85899,1.85899) translate(-342.471,-407.318)" width="30" x="741.161789750568" xlink:href="#Generator:发电机_0" y="881.5010218856319" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755024898" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454755024898"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,769.047,909.386) scale(1.85899,1.85899) translate(-342.471,-407.318)" width="30" x="741.161789750568" y="881.5010218856319"/></g>
  <g id="176">
   <use class="v10500" height="30" transform="rotate(0,1424.81,904.289) scale(1.81556,1.81556) translate(-627.798,-393.977)" width="30" x="1397.578165447497" xlink:href="#Generator:发电机_0" y="877.0559045518551" zvalue="196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454755221506" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454755221506"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1424.81,904.289) scale(1.81556,1.81556) translate(-627.798,-393.977)" width="30" x="1397.578165447497" y="877.0559045518551"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="v10500" height="30" transform="rotate(0,1074.75,853.5) scale(1.69643,1.70833) translate(-431.463,-343.265)" width="28" x="1051" xlink:href="#EnergyConsumer:站用变DY接地_0" y="827.875" zvalue="243"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756007938" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1074.75,853.5) scale(1.69643,1.70833) translate(-431.463,-343.265)" width="28" x="1051" y="827.875"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="71" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,613.488,15.2361) scale(1,1) translate(0,0)" writing-mode="lr" x="613.02" xml:space="preserve" y="19.92" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136274669570" ObjectName="P"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="81" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,613.488,33.1806) scale(1,1) translate(0,0)" writing-mode="lr" x="613.02" xml:space="preserve" y="37.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136274735106" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="82" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,613.488,50.0139) scale(1,1) translate(0,0)" writing-mode="lr" x="613.02" xml:space="preserve" y="54.68" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136274800642" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="83" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,878.21,11.7917) scale(1,1) translate(0,7.70988e-17)" writing-mode="lr" x="877.74" xml:space="preserve" y="16.47" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136276242434" ObjectName="P"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="84" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,878.21,29.625) scale(1,1) translate(0,-4.43041e-14)" writing-mode="lr" x="877.74" xml:space="preserve" y="34.31" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136276307970" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,878.21,48.0139) scale(1,1) translate(0,-7.90109e-15)" writing-mode="lr" x="877.74" xml:space="preserve" y="52.71" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136276373506" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="99" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1005.14,386.792) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.59" xml:space="preserve" y="391.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136278339586" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="104" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1005.14,407.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.59" xml:space="preserve" y="412.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136278405122" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="105" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1000.69,581.186) scale(1,1) translate(0,-1.26253e-13)" writing-mode="lr" x="1000.14" xml:space="preserve" y="585.89" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136278470658" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="123" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1000.69,603.797) scale(1,1) translate(0,6.55321e-14)" writing-mode="lr" x="1000.14" xml:space="preserve" y="608.51" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136278536194" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="124" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1005.14,426.569) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.59" xml:space="preserve" y="431.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136278601730" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="125" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1000.69,624.741) scale(1,1) translate(0,-1.35875e-13)" writing-mode="lr" x="1000.14" xml:space="preserve" y="629.4400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136278929410" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="126" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,763.491,990.413) scale(1,1) translate(0,2.50058e-12)" writing-mode="lr" x="762.9400000000001" xml:space="preserve" y="995.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136281419778" ObjectName="P"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="128" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1424.81,999.761) scale(1,1) translate(-6.09207e-13,-2.4131e-12)" writing-mode="lr" x="1424.26" xml:space="preserve" y="1004.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136282599426" ObjectName="P"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="130" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,763.491,1011.14) scale(1,1) translate(0,-2.66496e-12)" writing-mode="lr" x="762.9400000000001" xml:space="preserve" y="1015.81" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136281485314" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="136" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1425.92,1018.82) scale(1,1) translate(-6.097e-13,2.34823e-12)" writing-mode="lr" x="1425.37" xml:space="preserve" y="1023.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136282664962" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="137" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,763.491,1032.97) scale(1,1) translate(0,1.13359e-13)" writing-mode="lr" x="762.9400000000001" xml:space="preserve" y="1037.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136281550850" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1427.03,1037.87) scale(1,1) translate(-6.10194e-13,2.16045e-12)" writing-mode="lr" x="1426.48" xml:space="preserve" y="1042.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136282730498" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="222" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,260.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="265.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136285548546" ObjectName="F"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="221" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,261.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="266.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136285614082" ObjectName="F"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="220" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.222,333.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="338.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136281288706" ObjectName="F"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="148" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,285.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="290.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136285417474" ObjectName="F"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,286.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="291.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136285483010" ObjectName="F"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,377.389) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="382.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136555753474" ObjectName="F"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.611,377.389) scale(1,1) translate(0,0)" writing-mode="lr" x="333.77" xml:space="preserve" y="382.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136555884546" ObjectName="F"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,155.611,309.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="314.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179583493" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,335.611,308.167) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="313.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179517957" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,401.389) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="406.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136555819010" ObjectName="F"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,157.222,333.167) scale(1,1) translate(0,0)" writing-mode="lr" x="157.38" xml:space="preserve" y="338.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136278208514" ObjectName="F"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,515.5,569.25) scale(1,1) translate(0,0)" writing-mode="lr" x="515.03" xml:space="preserve" y="574.03" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136277815298" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1441.3,399.5) scale(1,1) translate(-9.30123e-13,0)" writing-mode="lr" x="1440.83" xml:space="preserve" y="404.28" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136280895490" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,515.5,594.25) scale(1,1) translate(0,0)" writing-mode="lr" x="515.03" xml:space="preserve" y="599.03" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136277880834" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1441.3,421.5) scale(1,1) translate(9.30123e-13,0)" writing-mode="lr" x="1440.83" xml:space="preserve" y="426.28" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136280961026" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,515.5,619.25) scale(1,1) translate(0,0)" writing-mode="lr" x="515.03" xml:space="preserve" y="624.03" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136277946370" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1441.3,442.5) scale(1,1) translate(9.30123e-13,0)" writing-mode="lr" x="1440.83" xml:space="preserve" y="447.28" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136281026562" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,442.5,346.75) scale(1,1) translate(0,0)" writing-mode="lr" x="442.03" xml:space="preserve" y="351.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136278077442" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,633.301,689) scale(1,1) translate(0,0)" writing-mode="lr" x="632.83" xml:space="preserve" y="693.78" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136281157634" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="152">
   <use height="30" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="298"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374928379905" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" y="176.37"/></g>
  <g id="215">
   <use height="30" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="299"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962179096577" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" y="176.37"/></g>
  <g id="12">
   <use height="30" transform="rotate(0,307.812,120.464) scale(1.22222,1.03092) translate(-45.9659,-3.14941)" width="90" x="252.81" xlink:href="#State:全站检修_0" y="105" zvalue="330"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549683879937" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,307.812,120.464) scale(1.22222,1.03092) translate(-45.9659,-3.14941)" width="90" x="252.81" y="105"/></g>
 </g>
</svg>