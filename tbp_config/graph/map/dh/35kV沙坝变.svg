<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549680209921" height="1055" id="thSvg" source="NR-PCS9000" viewBox="0 0 1918 1055" width="1918">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:线路PT带避雷器0904_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,11) scale(1,1) translate(0,0)" width="6" x="7" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.988213005145578" x2="9.988213005145578" y1="1.029523490692871" y2="18.75"/>
   <ellipse cx="9.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1752.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2332.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Accessory:电压互感器带电显示_0" viewBox="0,0,70,75">
   <use terminal-index="0" type="0" x="33.8" xlink:href="#terminal" y="70.3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.13333333333334" x2="64.60000000000001" y1="45.23333333333333" y2="54.7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55" x2="64.59999999999999" y1="54.7" y2="45.1"/>
   <ellipse cx="60.07" cy="50.03" fill-opacity="0" rx="6.74" ry="6.74" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.2 43.5 L 8.2 65.9 L 33.8 65.9" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.93333333333334" x2="63.66666666666667" y1="63.1" y2="63.1"/>
   <path d="M 55 21.1 L 55 25.9 L 49.4 25.9" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 47.8 19.5 L 47.8 17.9 L 62.2 6.7 L 62.2 5.1" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.80000000000001" x2="59.80000000000001" y1="60.16666666666666" y2="56.43333333333334"/>
   <rect fill-opacity="0" height="14.4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,33.8,51.5) scale(1,1) translate(0,0)" width="8" x="29.8" y="44.3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.66666666666667" x2="59.8" y1="65.90000000000001" y2="65.90000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.8" x2="33.8" y1="40.3" y2="69.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="42.41333333333334" x2="42.41333333333334" y1="31.14384521058083" y2="24.74384521058083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="42.41333333333334" x2="45.61333333333334" y1="31.14384521058083" y2="29.54384521058083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="42.41333333333334" x2="45.61333333333334" y1="24.74384521058083" y2="26.34384521058083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.27999999999999" x2="11.26666666666667" y1="46.34384521058083" y2="54.3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.8" x2="59.8" y1="65.90000000000001" y2="63.23333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.146666666666651" x2="5.399999999999995" y1="46.34384521058083" y2="54.3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.93333333333334" x2="63.66666666666667" y1="60.3" y2="60.3"/>
   <rect fill-opacity="0" height="16.53" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,8.2,50.83) scale(1,-1) translate(0,-1388.33)" width="11.2" x="2.6" y="42.57"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.087093768087719" x2="8.087093768087719" y1="42.47717854391416" y2="37.44003242978255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="60.08709376808772" x2="60.08709376808772" y1="43.14384521058084" y2="38.10669909644922"/>
   <ellipse cx="33.89" cy="33.89" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33474790508334" x2="4.491842553589187" y1="37.33137877295937" y2="37.33137877295937"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="63.33474790508334" x2="56.49184255358919" y1="37.99804543962603" y2="37.99804543962603"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.00141457175" x2="5.825175886922519" y1="35.33137877295938" y2="35.33137877295938"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="62.00141457175" x2="57.82517588692252" y1="35.99804543962604" y2="35.99804543962604"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.068081238416674" x2="6.758509220255849" y1="33.33137877295938" y2="33.33137877295938"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.8518425535892" x2="37.69184255358919" y1="34.23060739739171" y2="36.21067137235507"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.85184255358918" x2="30.01184255358919" y1="34.23060739739171" y2="36.21067137235507"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="61.06808123841667" x2="58.75850922025585" y1="33.99804543962604" y2="33.99804543962604"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.85184255358919" x2="33.85184255358919" y1="30.27047944746495" y2="34.23060739739167"/>
   <ellipse cx="33.36" cy="22.56" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="23.36" cy="28.29" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.31850922025586" x2="23.31850922025586" y1="24.67047944746495" y2="28.63060739739167"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.31850922025585" x2="29.47850922025586" y1="22.89727406405837" y2="24.87733803902174"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.31850922025586" x2="33.31850922025586" y1="18.93714611413161" y2="22.89727406405834"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.31850922025586" x2="27.15850922025585" y1="28.63060739739171" y2="30.61067137235507"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.31850922025586" x2="37.15850922025585" y1="22.89727406405837" y2="24.87733803902174"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.31850922025585" x2="19.47850922025586" y1="28.63060739739171" y2="30.61067137235507"/>
   <ellipse cx="43.23" cy="28.02" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="14.21" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,54.93,14.03) scale(1,1) translate(0,0)" width="8.4" x="50.73" y="6.92"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.44865365939912" x2="54.44865365939912" y1="4.566666666666663" y2="6.966666666666651"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="56.0125128711384" x2="52.09076330619942" y1="4.75335271024386" y2="4.75335271024386"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="55.24836385090142" x2="52.85491232643641" y1="3.499576240599325" y2="3.499576240599325"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="54.71345953673553" x2="53.38981664060229" y1="2.24579977095479" y2="2.24579977095479"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666666" x2="27.83333333333334" y1="6.416666666666666" y2="22.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.5" x2="28.5" y1="3.833333333333332" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.16666666666667" x2="35.5" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.5" x2="21.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV沙坝变" InitShowingPlane="" fill="rgb(0,0,0)" height="1055" width="1918" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="59.4" id="1" preserveAspectRatio="xMidYMid slice" width="189.48" x="85.61" xlink:href="logo.png" y="28.18"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180.347,57.8772) scale(1,1) translate(9.50428e-15,-2.50264e-14)" writing-mode="lr" x="180.35" xml:space="preserve" y="62.38" zvalue="1402"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,191.8,56.1394) scale(1,1) translate(0,3.78926e-15)" writing-mode="lr" x="191.8" xml:space="preserve" y="63.64" zvalue="1403">    35kV沙坝变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="232" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,278.438,406.75) scale(1,1) translate(0,-2.62956e-13)" width="72.88" x="242" y="394.75" zvalue="1546"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,278.438,406.75) scale(1,1) translate(0,-2.62956e-13)" writing-mode="lr" x="278.44" xml:space="preserve" y="411.25" zvalue="1546">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="222" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.625,325.75) scale(1,1) translate(0,0)" width="72.88" x="38.19" y="313.75" zvalue="1547"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.625,325.75) scale(1,1) translate(0,0)" writing-mode="lr" x="74.63" xml:space="preserve" y="330.25" zvalue="1547">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="231" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,176.531,406.75) scale(1,1) translate(0,0)" width="72.88" x="140.09" y="394.75" zvalue="1548"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176.531,406.75) scale(1,1) translate(0,0)" writing-mode="lr" x="176.53" xml:space="preserve" y="411.25" zvalue="1548">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="224" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.625,406.75) scale(1,1) translate(0,0)" width="72.88" x="38.19" y="394.75" zvalue="1549"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.625,406.75) scale(1,1) translate(0,0)" writing-mode="lr" x="74.63" xml:space="preserve" y="411.25" zvalue="1549">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="223" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.625,366.25) scale(1,1) translate(0,0)" width="72.88" x="38.19" y="354.25" zvalue="1550"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.625,366.25) scale(1,1) translate(0,0)" writing-mode="lr" x="74.63" xml:space="preserve" y="370.75" zvalue="1550">信号一览</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="176.5061138428261" y2="176.5061138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="202.0955138428261" y2="202.0955138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="28.20615013404824" y1="176.5061138428261" y2="202.0955138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="176.5061138428261" y2="202.0955138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="176.5061138428261" y2="176.5061138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="202.0955138428261" y2="202.0955138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="176.5061138428261" y2="202.0955138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="327.8501501340482" x2="327.8501501340482" y1="176.5061138428261" y2="202.0955138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="202.0954138428261" y2="202.0954138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="225.9624138428261" y2="225.9624138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="28.20615013404824" y1="202.0954138428261" y2="225.9624138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="202.0954138428261" y2="225.9624138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="202.0954138428261" y2="202.0954138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="225.9624138428261" y2="225.9624138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="202.0954138428261" y2="225.9624138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="327.8501501340482" x2="327.8501501340482" y1="202.0954138428261" y2="225.9624138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="225.9624538428261" y2="225.9624538428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="249.8294538428261" y2="249.8294538428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="28.20615013404824" y1="225.9624538428261" y2="249.8294538428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="225.9624538428261" y2="249.8294538428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="225.9624538428261" y2="225.9624538428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="249.8294538428261" y2="249.8294538428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="225.9624538428261" y2="249.8294538428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="327.8501501340482" x2="327.8501501340482" y1="225.9624538428261" y2="249.8294538428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="249.8295138428261" y2="249.8295138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="272.2202138428261" y2="272.2202138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="28.20615013404824" y1="249.8295138428261" y2="272.2202138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="249.8295138428261" y2="272.2202138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="249.8295138428261" y2="249.8295138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="272.2202138428261" y2="272.2202138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="249.8295138428261" y2="272.2202138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="327.8501501340482" x2="327.8501501340482" y1="249.8295138428261" y2="272.2202138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="272.2202138428261" y2="272.2202138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="178.0281501340482" y1="294.6109138428261" y2="294.6109138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.20615013404824" x2="28.20615013404824" y1="272.2202138428261" y2="294.6109138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="272.2202138428261" y2="294.6109138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="272.2202138428261" y2="272.2202138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="327.8501501340482" y1="294.6109138428261" y2="294.6109138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="178.0281501340482" x2="178.0281501340482" y1="272.2202138428261" y2="294.6109138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="327.8501501340482" x2="327.8501501340482" y1="272.2202138428261" y2="294.6109138428261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="440.5195627885682" y2="440.5195627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="472.6703627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="26.55026809651417" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="440.5195627885682" y2="440.5195627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="472.6703627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0502680965142" x2="146.0502680965142" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="440.5195627885682" y2="440.5195627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="472.6703627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="146.0499680965141" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.7999680965141" x2="205.7999680965141" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="440.5195627885682" y2="440.5195627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="472.6703627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="205.8000680965142" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5500680965142" x2="265.5500680965142" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="440.5195627885682" y2="440.5195627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="472.6703627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="265.5502680965142" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="325.3002680965142" x2="325.3002680965142" y1="440.5195627885682" y2="472.6703627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="472.6702627885682" y2="472.6702627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="498.9157627885681" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="26.55026809651417" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="472.6702627885682" y2="472.6702627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="498.9157627885681" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0502680965142" x2="146.0502680965142" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="472.6702627885682" y2="472.6702627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="498.9157627885681" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="146.0499680965141" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.7999680965141" x2="205.7999680965141" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="472.6702627885682" y2="472.6702627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="498.9157627885681" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="205.8000680965142" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5500680965142" x2="265.5500680965142" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="472.6702627885682" y2="472.6702627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="498.9157627885681" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="265.5502680965142" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="325.3002680965142" x2="325.3002680965142" y1="472.6702627885682" y2="498.9157627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="498.9157627885682" y2="498.9157627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="525.1612627885681" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="26.55026809651417" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="498.9157627885682" y2="498.9157627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="525.1612627885681" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0502680965142" x2="146.0502680965142" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="498.9157627885682" y2="498.9157627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="525.1612627885681" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="146.0499680965141" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.7999680965141" x2="205.7999680965141" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="498.9157627885682" y2="498.9157627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="525.1612627885681" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="205.8000680965142" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5500680965142" x2="265.5500680965142" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="498.9157627885682" y2="498.9157627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="525.1612627885681" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="265.5502680965142" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="325.3002680965142" x2="325.3002680965142" y1="498.9157627885682" y2="525.1612627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="525.1612827885682" y2="525.1612827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="551.4067827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="26.55026809651417" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="525.1612827885682" y2="525.1612827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="551.4067827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0502680965142" x2="146.0502680965142" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="525.1612827885682" y2="525.1612827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="551.4067827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="146.0499680965141" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.7999680965141" x2="205.7999680965141" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="525.1612827885682" y2="525.1612827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="551.4067827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="205.8000680965142" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5500680965142" x2="265.5500680965142" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="525.1612827885682" y2="525.1612827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="551.4067827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="265.5502680965142" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="325.3002680965142" x2="325.3002680965142" y1="525.1612827885682" y2="551.4067827885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="551.4068627885682" y2="551.4068627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="577.6523627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="26.55026809651417" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="551.4068627885682" y2="551.4068627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="577.6523627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0502680965142" x2="146.0502680965142" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="551.4068627885682" y2="551.4068627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="577.6523627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="146.0499680965141" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.7999680965141" x2="205.7999680965141" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="551.4068627885682" y2="551.4068627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="577.6523627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="205.8000680965142" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5500680965142" x2="265.5500680965142" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="551.4068627885682" y2="551.4068627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="577.6523627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="265.5502680965142" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="325.3002680965142" x2="325.3002680965142" y1="551.4068627885682" y2="577.6523627885682"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="577.6522627885681" y2="577.6522627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="86.30026809651417" y1="603.8977627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="26.55026809651417" x2="26.55026809651417" y1="577.6522627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="577.6522627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="577.6522627885681" y2="577.6522627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="146.0502680965142" y1="603.8977627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="86.30026809651417" x2="86.30026809651417" y1="577.6522627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0502680965142" x2="146.0502680965142" y1="577.6522627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="577.6522627885681" y2="577.6522627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="205.7999680965141" y1="603.8977627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.0499680965141" x2="146.0499680965141" y1="577.6522627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.7999680965141" x2="205.7999680965141" y1="577.6522627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="577.6522627885681" y2="577.6522627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="265.5500680965142" y1="603.8977627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="205.8000680965142" x2="205.8000680965142" y1="577.6522627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5500680965142" x2="265.5500680965142" y1="577.6522627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="577.6522627885681" y2="577.6522627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="325.3002680965142" y1="603.8977627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="265.5502680965142" x2="265.5502680965142" y1="577.6522627885681" y2="603.8977627885681"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="325.3002680965142" x2="325.3002680965142" y1="577.6522627885681" y2="603.8977627885681"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.371,305.413) scale(1,1) translate(0,0)" writing-mode="lr" x="628.37" xml:space="preserve" y="309.91" zvalue="21">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1687.91,305.052) scale(1,1) translate(0,0)" writing-mode="lr" x="1687.91" xml:space="preserve" y="309.55" zvalue="23">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.523,290.889) scale(1,1) translate(0,0)" writing-mode="lr" x="788.52" xml:space="preserve" y="295.39" zvalue="42">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.58,194.205) scale(1,1) translate(0,0)" writing-mode="lr" x="786.58" xml:space="preserve" y="198.7" zvalue="45">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1047.12,290.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.13" xml:space="preserve" y="295.39" zvalue="93">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.88,290.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1196.88" xml:space="preserve" y="295.39" zvalue="96">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,664.341,370.041) scale(1,1) translate(0,0)" writing-mode="lr" x="664.34" xml:space="preserve" y="374.54" zvalue="107">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,515.006,703.417) scale(1,1) translate(0,0)" writing-mode="lr" x="515.01" xml:space="preserve" y="707.92" zvalue="130">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1677.79,701.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1677.79" xml:space="preserve" y="705.83" zvalue="131">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1253.86,686.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1253.86" xml:space="preserve" y="691.11" zvalue="386">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.14,636.431) scale(1,1) translate(0,2.05373e-13)" writing-mode="lr" x="1167.138888888889" xml:space="preserve" y="640.9305555555555" zvalue="387">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1106,282.389) scale(1,1) translate(0,-2.35614e-13)" writing-mode="lr" x="1106" xml:space="preserve" y="286.8888888888889" zvalue="450">312</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1101.83,242.32) scale(1,1) translate(0,-3.50045e-13)" writing-mode="lr" x="1101.827586206897" xml:space="preserve" y="246.8199233716475" zvalue="451">35kV分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="540" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,740.992,184.515) scale(1,1) translate(8.08141e-13,0)" writing-mode="lr" x="740.99" xml:space="preserve" y="189.02" zvalue="572">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="550" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897.071,509.744) scale(1,1) translate(0,0)" writing-mode="lr" x="897.0700000000001" xml:space="preserve" y="514.24" zvalue="576">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="553" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.172,253.146) scale(1,1) translate(0,0)" writing-mode="lr" x="798.17" xml:space="preserve" y="257.65" zvalue="578">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="558" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931.806,290.889) scale(1,1) translate(0,0)" writing-mode="lr" x="931.8099999999999" xml:space="preserve" y="295.39" zvalue="580">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="557" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.681,194.205) scale(1,1) translate(0,0)" writing-mode="lr" x="927.6799999999999" xml:space="preserve" y="198.7" zvalue="583">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="555" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.639,184.473) scale(1,1) translate(0,0)" writing-mode="lr" x="880.64" xml:space="preserve" y="188.97" zvalue="590">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="554" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.774,254.181) scale(1,1) translate(0,0)" writing-mode="lr" x="937.77" xml:space="preserve" y="258.68" zvalue="593">362</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="575" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1104.03,686.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1104.03" xml:space="preserve" y="691.36" zvalue="597">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="546" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1171.17,673.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1171.17" xml:space="preserve" y="678.4400000000001" zvalue="599">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="588" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1305.03,290.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1305.03" xml:space="preserve" y="295.39" zvalue="603">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="583" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1304.9,194.205) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.9" xml:space="preserve" y="198.7" zvalue="606">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="581" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1257.86,189.339) scale(1,1) translate(-1.38198e-12,0)" writing-mode="lr" x="1257.86" xml:space="preserve" y="193.84" zvalue="613">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="580" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1313.95,249.146) scale(1,1) translate(0,0)" writing-mode="lr" x="1313.95" xml:space="preserve" y="253.65" zvalue="616">363</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="615" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1457.45,290.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1457.45" xml:space="preserve" y="295.39" zvalue="619">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="614" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1453.24,194.205) scale(1,1) translate(0,0)" writing-mode="lr" x="1453.24" xml:space="preserve" y="198.7" zvalue="622">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="612" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406.19,190.45) scale(1,1) translate(-1.54666e-12,0)" writing-mode="lr" x="1406.19" xml:space="preserve" y="194.95" zvalue="629">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1463.19,253.146) scale(1,1) translate(-3.86942e-12,0)" writing-mode="lr" x="1463.19" xml:space="preserve" y="257.65" zvalue="632">364</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="636" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,861.278,389.167) scale(1,1) translate(0,0)" writing-mode="lr" x="861.28" xml:space="preserve" y="393.67" zvalue="635">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="637" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.278,351.194) scale(1,1) translate(0,0)" writing-mode="lr" x="853.28" xml:space="preserve" y="355.69" zvalue="639">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="644" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,861.278,633.167) scale(1,1) translate(0,0)" writing-mode="lr" x="861.28" xml:space="preserve" y="637.67" zvalue="643">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="645" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.278,678.25) scale(1,1) translate(0,0)" writing-mode="lr" x="853.28" xml:space="preserve" y="682.75" zvalue="647">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="670" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,641.521,465.902) scale(1,1) translate(0,0)" writing-mode="lr" x="641.52" xml:space="preserve" y="470.4" zvalue="673">35kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="672" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1566.71,364.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.71" xml:space="preserve" y="369.36" zvalue="675">3902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="671" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1541.97,462.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1541.97" xml:space="preserve" y="467.22" zvalue="680">35kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,775.889,96.5707) scale(1,1) translate(0,0)" writing-mode="lr" x="775.89" xml:space="preserve" y="101.07" zvalue="690">35kV西沙Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,587.283,694.722) scale(1,1) translate(0,0)" writing-mode="lr" x="587.28" xml:space="preserve" y="699.22" zvalue="711">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,617.222,666.278) scale(1,1) translate(0,0)" writing-mode="lr" x="617.22" xml:space="preserve" y="670.78" zvalue="715">7</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,567.019,583.389) scale(1,1) translate(0,-2.54858e-13)" writing-mode="lr" x="567.02" xml:space="preserve" y="587.89" zvalue="718">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1609.08,691.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1609.08" xml:space="preserve" y="696.22" zvalue="720">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1643,666.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1643" xml:space="preserve" y="670.78" zvalue="724">7</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1588.8,582.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1588.8" xml:space="preserve" y="586.89" zvalue="727">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,926.934,96.5707) scale(1,1) translate(0,0)" writing-mode="lr" x="926.9299999999999" xml:space="preserve" y="101.07" zvalue="730">35kV沙锡线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292.56,100.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.56" xml:space="preserve" y="105.07" zvalue="733">35kV西沙Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1442.85,96.5707) scale(1,1) translate(0,0)" writing-mode="lr" x="1442.85" xml:space="preserve" y="101.07" zvalue="736">35kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1617.45,290.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1617.45" xml:space="preserve" y="295.39" zvalue="1122">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1613.24,194.205) scale(1,1) translate(0,0)" writing-mode="lr" x="1613.24" xml:space="preserve" y="198.7" zvalue="1125">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1566.19,190.45) scale(1,1) translate(-1.7243e-12,0)" writing-mode="lr" x="1566.19" xml:space="preserve" y="194.95" zvalue="1130">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.19,253.146) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.19" xml:space="preserve" y="257.65" zvalue="1132">381</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1599.41,98.569) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.41" xml:space="preserve" y="103.07" zvalue="1139">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.278,436.902) scale(1,1) translate(0,0)" writing-mode="lr" x="853.28" xml:space="preserve" y="441.4" zvalue="1147">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.756,462.313) scale(1,1) translate(-3.94909e-13,0)" writing-mode="lr" x="896.76" xml:space="preserve" y="466.81" zvalue="1158">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.278,592.152) scale(1,1) translate(0,0)" writing-mode="lr" x="853.28" xml:space="preserve" y="596.65" zvalue="1160">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.269,530.077) scale(1,1) translate(9.65638e-14,0)" writing-mode="lr" x="898.27" xml:space="preserve" y="534.58" zvalue="1164">5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.15,507.437) scale(1,1) translate(0,0)" writing-mode="lr" x="1410.15" xml:space="preserve" y="511.94" zvalue="1167">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1374.35,386.859) scale(1,1) translate(0,0)" writing-mode="lr" x="1374.35" xml:space="preserve" y="391.36" zvalue="1169">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.35,348.887) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.35" xml:space="preserve" y="353.39" zvalue="1171">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1374.35,630.859) scale(1,1) translate(0,0)" writing-mode="lr" x="1374.35" xml:space="preserve" y="635.36" zvalue="1174">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.35,675.942) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.35" xml:space="preserve" y="680.4400000000001" zvalue="1177">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.35,434.595) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.35" xml:space="preserve" y="439.09" zvalue="1180">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1409.83,460.005) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.83" xml:space="preserve" y="464.51" zvalue="1185">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.35,589.845) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.35" xml:space="preserve" y="594.34" zvalue="1187">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1411.35,527.769) scale(1,1) translate(0,0)" writing-mode="lr" x="1411.35" xml:space="preserve" y="532.27" zvalue="1191">6300kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557.992,859.545) scale(1,1) translate(0,0)" writing-mode="lr" x="557.99" xml:space="preserve" y="864.05" zvalue="1201">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,527.3,782.571) scale(1,1) translate(0,1.37591e-12)" writing-mode="lr" x="527.3" xml:space="preserve" y="787.0700000000001" zvalue="1202">081</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,520.667,931.043) scale(1,1) translate(0,0)" writing-mode="lr" x="520.67" xml:space="preserve" y="935.54" zvalue="1204">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,515.194,824.5) scale(1,1) translate(0,0)" writing-mode="lr" x="515.1900000000001" xml:space="preserve" y="829" zvalue="1206">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,677.992,859.545) scale(1,1) translate(0,0)" writing-mode="lr" x="677.99" xml:space="preserve" y="864.05" zvalue="1231">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,647.3,782.571) scale(1,1) translate(0,1.37591e-12)" writing-mode="lr" x="647.3" xml:space="preserve" y="787.0700000000001" zvalue="1233">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.694,824.5) scale(1,1) translate(0,0)" writing-mode="lr" x="638.6900000000001" xml:space="preserve" y="829" zvalue="1237">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,529.581,746.5) scale(1,1) translate(0,0)" writing-mode="lr" x="529.58" xml:space="preserve" y="751" zvalue="1245">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="381" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,637.536,748.5) scale(1,1) translate(0,0)" writing-mode="lr" x="637.54" xml:space="preserve" y="753" zvalue="1249">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="385" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,641,886.25) scale(1,1) translate(0,0)" writing-mode="lr" x="641" xml:space="preserve" y="890.75" zvalue="1253">8</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="462" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,748.94,960) scale(1,1) translate(0,0)" writing-mode="lr" x="748.9400000000001" xml:space="preserve" y="964.5" zvalue="1257">10kV沙横线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="390" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,797.992,859.545) scale(1,1) translate(0,0)" writing-mode="lr" x="797.99" xml:space="preserve" y="864.05" zvalue="1258">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="389" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,767.3,782.571) scale(1,1) translate(0,1.37591e-12)" writing-mode="lr" x="767.3" xml:space="preserve" y="787.0700000000001" zvalue="1260">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="388" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,758.694,824.5) scale(1,1) translate(0,0)" writing-mode="lr" x="758.6900000000001" xml:space="preserve" y="829" zvalue="1262">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="387" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,757.536,748.5) scale(1,1) translate(0,0)" writing-mode="lr" x="757.54" xml:space="preserve" y="753" zvalue="1266">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="386" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761,886.25) scale(1,1) translate(0,0)" writing-mode="lr" x="761" xml:space="preserve" y="890.75" zvalue="1270">8</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="463" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,868.94,960) scale(1,1) translate(0,0)" writing-mode="lr" x="868.9400000000001" xml:space="preserve" y="964.5" zvalue="1275">10kV沙勐线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="422" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.992,859.545) scale(1,1) translate(0,0)" writing-mode="lr" x="917.99" xml:space="preserve" y="864.05" zvalue="1276">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="421" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.3,782.571) scale(1,1) translate(0,1.37591e-12)" writing-mode="lr" x="887.3" xml:space="preserve" y="787.0700000000001" zvalue="1278">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="420" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,878.694,824.5) scale(1,1) translate(0,0)" writing-mode="lr" x="878.6900000000001" xml:space="preserve" y="829" zvalue="1280">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="419" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,877.536,748.5) scale(1,1) translate(0,0)" writing-mode="lr" x="877.54" xml:space="preserve" y="753" zvalue="1284">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="418" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,881,886.25) scale(1,1) translate(0,0)" writing-mode="lr" x="881" xml:space="preserve" y="890.75" zvalue="1288">8</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="442" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037.99,859.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.99" xml:space="preserve" y="864.05" zvalue="1294">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="441" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.3,782.571) scale(1,1) translate(0,1.37591e-12)" writing-mode="lr" x="1007.3" xml:space="preserve" y="787.0700000000001" zvalue="1296">064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="440" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.694,824.5) scale(1,1) translate(0,0)" writing-mode="lr" x="998.6900000000001" xml:space="preserve" y="829" zvalue="1298">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="439" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.536,748.5) scale(1,1) translate(0,0)" writing-mode="lr" x="997.54" xml:space="preserve" y="753" zvalue="1302">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="438" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1001,886.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1001" xml:space="preserve" y="890.75" zvalue="1306">8</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="482" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1308.94,962) scale(1,1) translate(0,0)" writing-mode="lr" x="1308.94" xml:space="preserve" y="966.5" zvalue="1312">10kV沙磨线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="481" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1357.99,859.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1357.99" xml:space="preserve" y="864.05" zvalue="1313">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="480" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.3,782.571) scale(1,1) translate(0,1.37591e-12)" writing-mode="lr" x="1327.3" xml:space="preserve" y="787.0700000000001" zvalue="1315">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="479" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1318.69,824.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.69" xml:space="preserve" y="829" zvalue="1317">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="478" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317.54,748.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1317.54" xml:space="preserve" y="753" zvalue="1321">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="477" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.53,886.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.53" xml:space="preserve" y="890.75" zvalue="1325">8</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="475" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1477.99,859.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.99" xml:space="preserve" y="864.05" zvalue="1331">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="474" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1447.3,782.571) scale(1,1) translate(0,1.37591e-12)" writing-mode="lr" x="1447.3" xml:space="preserve" y="787.0700000000001" zvalue="1333">066</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="473" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1438.69,824.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1438.69" xml:space="preserve" y="829" zvalue="1335">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="472" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437.54,747.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.54" xml:space="preserve" y="752" zvalue="1339">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="471" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1442.53,886.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1442.53" xml:space="preserve" y="890.75" zvalue="1343">8</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="470" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1548.94,960) scale(1,1) translate(0,0)" writing-mode="lr" x="1548.94" xml:space="preserve" y="964.5" zvalue="1348">10kV沙丝线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="469" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1597.99,859.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.99" xml:space="preserve" y="864.05" zvalue="1349">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="468" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1567.3,782.571) scale(1,1) translate(0,1.37591e-12)" writing-mode="lr" x="1567.3" xml:space="preserve" y="787.0700000000001" zvalue="1351">067</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1558.69,824.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1558.69" xml:space="preserve" y="829" zvalue="1353">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="466" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1557.54,748.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1557.54" xml:space="preserve" y="753" zvalue="1357">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="465" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1562.53,886.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1562.53" xml:space="preserve" y="890.75" zvalue="1361">8</text>
  <line fill="none" id="94" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="348.3002680965151" x2="348.3002680965151" y1="13.75" y2="1003.75" zvalue="1404"/>
  <line fill="none" id="79" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.20576407506621" x2="332.8170241286854" y1="167.5208030256257" y2="167.5208030256257" zvalue="1419"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.30026809651508" x2="91.74026809651514" y1="928.4556480819297" y2="928.4556480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.30026809651508" x2="91.74026809651514" y1="979.7951480819296" y2="979.7951480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.30026809651508" x2="20.30026809651508" y1="928.4556480819297" y2="979.7951480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74026809651514" x2="91.74026809651514" y1="928.4556480819297" y2="979.7951480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74076809651513" x2="326.3007680965151" y1="928.4556480819297" y2="928.4556480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74076809651513" x2="326.3007680965151" y1="979.7951480819296" y2="979.7951480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74076809651513" x2="91.74076809651513" y1="928.4556480819297" y2="979.7951480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="326.3007680965151" x2="326.3007680965151" y1="928.4556480819297" y2="979.7951480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.30026809651508" x2="91.74026809651514" y1="979.7951280819295" y2="979.7951280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.30026809651508" x2="91.74026809651514" y1="1007.27262808193" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.30026809651508" x2="20.30026809651508" y1="979.7951280819295" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74026809651514" x2="91.74026809651514" y1="979.7951280819295" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74076809651513" x2="162.7760680965151" y1="979.7951280819295" y2="979.7951280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74076809651513" x2="162.7760680965151" y1="1007.27262808193" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74076809651513" x2="91.74076809651513" y1="979.7951280819295" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="162.7760680965151" x2="162.7760680965151" y1="979.7951280819295" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="162.7760680965151" x2="244.5381680965151" y1="979.7951280819295" y2="979.7951280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="162.7760680965151" x2="244.5381680965151" y1="1007.27262808193" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="162.7760680965151" x2="162.7760680965151" y1="979.7951280819295" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="244.5381680965151" x2="244.5381680965151" y1="979.7951280819295" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="244.5380680965151" x2="326.3001680965151" y1="979.7951280819295" y2="979.7951280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="244.5380680965151" x2="326.3001680965151" y1="1007.27262808193" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="244.5380680965151" x2="244.5380680965151" y1="979.7951280819295" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="326.3001680965151" x2="326.3001680965151" y1="979.7951280819295" y2="1007.27262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.30026809651508" x2="91.74026809651514" y1="1007.27254808193" y2="1007.27254808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.30026809651508" x2="91.74026809651514" y1="1034.75004808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.30026809651508" x2="20.30026809651508" y1="1007.27254808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74026809651514" x2="91.74026809651514" y1="1007.27254808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74076809651513" x2="162.7760680965151" y1="1007.27254808193" y2="1007.27254808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74076809651513" x2="162.7760680965151" y1="1034.75004808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.74076809651513" x2="91.74076809651513" y1="1007.27254808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="162.7760680965151" x2="162.7760680965151" y1="1007.27254808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="162.7760680965151" x2="244.5381680965151" y1="1007.27254808193" y2="1007.27254808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="162.7760680965151" x2="244.5381680965151" y1="1034.75004808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="162.7760680965151" x2="162.7760680965151" y1="1007.27254808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="244.5381680965151" x2="244.5381680965151" y1="1007.27254808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="244.5380680965151" x2="326.3001680965151" y1="1007.27254808193" y2="1007.27254808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="244.5380680965151" x2="326.3001680965151" y1="1034.75004808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="244.5380680965151" x2="244.5380680965151" y1="1007.27254808193" y2="1034.75004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="326.3001680965151" x2="326.3001680965151" y1="1007.27254808193" y2="1034.75004808193"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="75" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.1914,994.398) scale(1,1) translate(0,1.09198e-13)" writing-mode="lr" x="34.38" xml:space="preserve" y="1000.4" zvalue="1423">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240.181,995.398) scale(1,1) translate(1.14227e-13,-1.53033e-12)" writing-mode="lr" x="171.48" xml:space="preserve" y="1001.4" zvalue="1424">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.9712,1023.96) scale(1,1) translate(-2.10942e-14,-1.57472e-12)" writing-mode="lr" x="47.97" xml:space="preserve" y="1029.96" zvalue="1425">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="72" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,248.475,1021.96) scale(1,1) translate(0,1.12258e-13)" writing-mode="lr" x="171.65" xml:space="preserve" y="1027.96" zvalue="1426">更新日期    20231205</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6602,190.285) scale(1,1) translate(-3.40716e-14,-2.78935e-13)" writing-mode="lr" x="59.66" xml:space="preserve" y="195.79" zvalue="1428">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.655,190.285) scale(1,1) translate(-1.99489e-13,-2.78935e-13)" writing-mode="lr" x="208.65" xml:space="preserve" y="195.79" zvalue="1429">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.2903,216.12) scale(1,1) translate(0,0)" writing-mode="lr" x="69.29000000000001" xml:space="preserve" y="220.62" zvalue="1430">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.82,217.124) scale(1,1) translate(0,0)" writing-mode="lr" x="220.82" xml:space="preserve" y="221.62" zvalue="1431">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.5403,240.991) scale(1,1) translate(0,0)" writing-mode="lr" x="70.54000000000001" xml:space="preserve" y="245.49" zvalue="1432">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,219.164,240.745) scale(1,1) translate(0,0)" writing-mode="lr" x="219.16" xml:space="preserve" y="245.25" zvalue="1433">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.358,344.157) scale(1,1) translate(0,0)" writing-mode="lr" x="178.36" xml:space="preserve" y="348.66" zvalue="1434">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,265.272,344.157) scale(1,1) translate(0,0)" writing-mode="lr" x="265.27" xml:space="preserve" y="348.66" zvalue="1435">通道</text>
  <line fill="none" id="62" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.58495308311001" x2="332.1962131367292" y1="618.2873878122265" y2="618.2873878122265" zvalue="1436"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.0717,489.238) scale(1,1) translate(5.77518e-14,-1.05464e-13)" writing-mode="lr" x="48.07171581769535" xml:space="preserve" y="493.7377444690242" zvalue="1438">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.0717,514.335) scale(1,1) translate(5.77518e-14,0)" writing-mode="lr" x="48.07171581769535" xml:space="preserve" y="518.8350106525576" zvalue="1439">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.0717,539.432) scale(1,1) translate(5.77518e-14,0)" writing-mode="lr" x="48.07171581769535" xml:space="preserve" y="543.9322768360911" zvalue="1440">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.0717,564.53) scale(1,1) translate(5.77518e-14,-1.22182e-13)" writing-mode="lr" x="48.07171581769535" xml:space="preserve" y="569.0295430196245" zvalue="1441">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.0717,589.627) scale(1,1) translate(5.77518e-14,6.38773e-14)" writing-mode="lr" x="48.07171581769535" xml:space="preserve" y="594.1268092031579" zvalue="1442">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" x="120.671875" xml:space="preserve" y="454.7634347210171" zvalue="1443">35kV   Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="120.671875" xml:space="preserve" y="470.7634347210171" zvalue="1443">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" x="177.296875" xml:space="preserve" y="455.2709944965191" zvalue="1444">35kV   Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="177.296875" xml:space="preserve" y="471.2709944965191" zvalue="1444">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" x="235.90625" xml:space="preserve" y="454.7634347210171" zvalue="1445">10kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="235.90625" xml:space="preserve" y="470.7634347210171" zvalue="1445">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" x="292.5625" xml:space="preserve" y="456.0053694965191" zvalue="1446">10kV      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="292.5625" xml:space="preserve" y="472.0053694965191" zvalue="1446">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98.6495,639.329) scale(1,1) translate(6.21699e-15,1.37807e-13)" writing-mode="lr" x="98.64954758713156" xml:space="preserve" y="643.8292383117242" zvalue="1448">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.9816,262.14) scale(1,1) translate(0,0)" writing-mode="lr" x="67.98" xml:space="preserve" y="266.64" zvalue="1449">#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,216.175,260.398) scale(1,1) translate(0,0)" writing-mode="lr" x="216.18" xml:space="preserve" y="264.9" zvalue="1450">#2主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.9816,284.64) scale(1,1) translate(0,0)" writing-mode="lr" x="67.98" xml:space="preserve" y="289.14" zvalue="1451">#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,216.175,282.898) scale(1,1) translate(0,0)" writing-mode="lr" x="216.18" xml:space="preserve" y="287.4" zvalue="1452">#2主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="78" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.1914,955.398) scale(1,1) translate(0,1.04868e-13)" writing-mode="lr" x="22.38" xml:space="preserve" y="961.4" zvalue="1552">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="122" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,206.691,955.398) scale(1,1) translate(0,1.04868e-13)" writing-mode="lr" x="148.38" xml:space="preserve" y="961.4" zvalue="1554">ShaBa-01-2023</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="234" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,124.191,1023.4) scale(1,1) translate(0,1.12418e-13)" writing-mode="lr" x="107.38" xml:space="preserve" y="1029.4" zvalue="1556">段勇</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,625.19,960) scale(1,1) translate(0,0)" writing-mode="lr" x="625.1900000000001" xml:space="preserve" y="964.5" zvalue="1558">10kV沙糖Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1426,962) scale(1,1) translate(0,0)" writing-mode="lr" x="1426" xml:space="preserve" y="966.5" zvalue="1560">10kV沙糖Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978,960) scale(1,1) translate(0,0)" writing-mode="lr" x="978" xml:space="preserve" y="964.5" zvalue="1567">10kV沙九线</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="242" y="394.75" zvalue="1546"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="38.19" y="313.75" zvalue="1547"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="140.09" y="394.75" zvalue="1548"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="38.19" y="394.75" zvalue="1549"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="38.19" y="354.25" zvalue="1550"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="508">
   <path class="kv35" d="M 595.9 322.11 L 1080.25 322.11" stroke-width="6" zvalue="20"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403352579" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674403352579"/></metadata>
  <path d="M 595.9 322.11 L 1080.25 322.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="506">
   <path class="kv35" d="M 1135.88 321.25 L 1717.28 321.25" stroke-width="6" zvalue="22"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403287043" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674403287043"/></metadata>
  <path d="M 1135.88 321.25 L 1717.28 321.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="429">
   <path class="kv10" d="M 488.2 720 L 1132.87 720" stroke-width="6" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403221507" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674403221507"/></metadata>
  <path d="M 488.2 720 L 1132.87 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="426">
   <path class="kv10" d="M 1210.33 720.25 L 1709 720.25" stroke-width="6" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403155971" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674403155971"/></metadata>
  <path d="M 1210.33 720.25 L 1709 720.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="492">
   <use class="kv35" height="30" transform="rotate(0,775.75,291.25) scale(0.833333,0.833333) translate(153.9,55.75)" width="15" x="769.5" xlink:href="#Disconnector:刀闸_0" y="278.75" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453964791811" ObjectName="35kV西沙Ⅰ回线3611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453964791811"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,775.75,291.25) scale(0.833333,0.833333) translate(153.9,55.75)" width="15" x="769.5" y="278.75"/></g>
  <g id="489">
   <use class="kv35" height="30" transform="rotate(0,775.75,197.75) scale(0.833333,0.833333) translate(153.9,37.05)" width="15" x="769.5" xlink:href="#Disconnector:刀闸_0" y="185.25" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453964726275" ObjectName="35kV西沙Ⅰ回线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453964726275"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,775.75,197.75) scale(0.833333,0.833333) translate(153.9,37.05)" width="15" x="769.5" y="185.25"/></g>
  <g id="458">
   <use class="kv35" height="30" transform="rotate(0,1035.5,292.75) scale(0.833333,0.833333) translate(205.85,56.05)" width="15" x="1029.25" xlink:href="#Disconnector:刀闸_0" y="280.25" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453964660739" ObjectName="35kV分段3121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453964660739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1035.5,292.75) scale(0.833333,0.833333) translate(205.85,56.05)" width="15" x="1029.25" y="280.25"/></g>
  <g id="457">
   <use class="kv35" height="30" transform="rotate(0,1185.5,292.75) scale(0.833333,0.833333) translate(235.85,56.05)" width="15" x="1179.25" xlink:href="#Disconnector:刀闸_0" y="280.25" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453964595203" ObjectName="35kV分段3122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453964595203"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1185.5,292.75) scale(0.833333,0.833333) translate(235.85,56.05)" width="15" x="1179.25" y="280.25"/></g>
  <g id="449">
   <use class="kv35" height="30" transform="rotate(0,641.966,367.263) scale(0.833333,0.833333) translate(127.143,70.9527)" width="15" x="635.7155172413792" xlink:href="#Disconnector:刀闸_0" y="354.7634085046834" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453964529667" ObjectName="35kVⅠ段母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453964529667"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,641.966,367.263) scale(0.833333,0.833333) translate(127.143,70.9527)" width="15" x="635.7155172413792" y="354.7634085046834"/></g>
  <g id="436">
   <use class="kv10" height="30" transform="rotate(0,1241.44,687.611) scale(0.833333,0.833333) translate(247.039,135.022)" width="15" x="1235.194444444445" xlink:href="#Disconnector:刀闸_0" y="675.1111111111111" zvalue="385"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453964464131" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453964464131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1241.44,687.611) scale(0.833333,0.833333) translate(247.039,135.022)" width="15" x="1235.194444444445" y="675.1111111111111"/></g>
  <g id="572">
   <use class="kv35" height="30" transform="rotate(0,916.306,289.028) scale(0.833333,0.833333) translate(182.011,55.3056)" width="15" x="910.0555555555555" xlink:href="#Disconnector:刀闸_0" y="276.5277777777778" zvalue="579"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965185027" ObjectName="35kV沙锡线3621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453965185027"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,916.306,289.028) scale(0.833333,0.833333) translate(182.011,55.3056)" width="15" x="910.0555555555555" y="276.5277777777778"/></g>
  <g id="570">
   <use class="kv35" height="30" transform="rotate(0,916.306,195.528) scale(0.833333,0.833333) translate(182.011,36.6056)" width="15" x="910.0555555555555" xlink:href="#Disconnector:刀闸_0" y="183.0277777777778" zvalue="582"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965119491" ObjectName="35kV沙锡线3626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453965119491"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,916.306,195.528) scale(0.833333,0.833333) translate(182.011,36.6056)" width="15" x="910.0555555555555" y="183.0277777777778"/></g>
  <g id="574">
   <use class="kv10" height="30" transform="rotate(0,1094.5,687.861) scale(0.833333,0.833333) translate(217.65,135.072)" width="15" x="1088.25" xlink:href="#Disconnector:刀闸_0" y="675.3611111111112" zvalue="596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965250563" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453965250563"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1094.5,687.861) scale(0.833333,0.833333) translate(217.65,135.072)" width="15" x="1088.25" y="675.3611111111112"/></g>
  <g id="606">
   <use class="kv35" height="30" transform="rotate(0,1293.53,286.806) scale(0.833333,0.833333) translate(257.456,54.8611)" width="15" x="1287.277777777778" xlink:href="#Disconnector:刀闸_0" y="274.3055555555555" zvalue="602"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965512707" ObjectName="35kV西沙Ⅱ回线3632隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453965512707"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1293.53,286.806) scale(0.833333,0.833333) translate(257.456,54.8611)" width="15" x="1287.277777777778" y="274.3055555555555"/></g>
  <g id="604">
   <use class="kv35" height="30" transform="rotate(0,1293.53,193.306) scale(0.833333,0.833333) translate(257.456,36.1611)" width="15" x="1287.277777777778" xlink:href="#Disconnector:刀闸_0" y="180.8055555555555" zvalue="605"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965447171" ObjectName="35kV西沙Ⅱ回线3636隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453965447171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1293.53,193.306) scale(0.833333,0.833333) translate(257.456,36.1611)" width="15" x="1287.277777777778" y="180.8055555555555"/></g>
  <g id="629">
   <use class="kv35" height="30" transform="rotate(0,1441.95,290.139) scale(0.833333,0.833333) translate(287.139,55.5278)" width="15" x="1435.696852457616" xlink:href="#Disconnector:刀闸_0" y="277.6388888888888" zvalue="618"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965774851" ObjectName="35kV备用线3642隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453965774851"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1441.95,290.139) scale(0.833333,0.833333) translate(287.139,55.5278)" width="15" x="1435.696852457616" y="277.6388888888888"/></g>
  <g id="627">
   <use class="kv35" height="30" transform="rotate(0,1441.86,192.194) scale(0.833333,0.833333) translate(287.122,35.9389)" width="15" x="1435.611111111111" xlink:href="#Disconnector:刀闸_0" y="179.6944444444443" zvalue="621"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965709315" ObjectName="35kV备用线3646隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453965709315"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1441.86,192.194) scale(0.833333,0.833333) translate(287.122,35.9389)" width="15" x="1435.611111111111" y="179.6944444444443"/></g>
  <g id="633">
   <use class="kv35" height="30" transform="rotate(0,835.75,346.194) scale(0.833333,0.833333) translate(165.9,66.7389)" width="15" x="829.5" xlink:href="#Disconnector:刀闸_0" y="333.6944428814782" zvalue="638"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965840387" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453965840387"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.75,346.194) scale(0.833333,0.833333) translate(165.9,66.7389)" width="15" x="829.5" y="333.6944428814782"/></g>
  <g id="641">
   <use class="kv10" height="30" transform="rotate(0,836.306,675.25) scale(0.833333,0.833333) translate(166.011,132.55)" width="15" x="830.0555555555557" xlink:href="#Disconnector:刀闸_0" y="662.75" zvalue="646"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965905923" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453965905923"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,836.306,675.25) scale(0.833333,0.833333) translate(166.011,132.55)" width="15" x="830.0555555555557" y="662.75"/></g>
  <g id="676">
   <use class="kv35" height="30" transform="rotate(0,1542.33,365.083) scale(0.833333,0.833333) translate(307.217,70.5167)" width="15" x="1536.083333333333" xlink:href="#Disconnector:刀闸_0" y="352.5833318763309" zvalue="674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453966102531" ObjectName="35kVⅡ段母线电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453966102531"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1542.33,365.083) scale(0.833333,0.833333) translate(307.217,70.5167)" width="15" x="1536.083333333333" y="352.5833318763309"/></g>
  <g id="28">
   <use class="kv10" height="30" transform="rotate(0,564.978,692.944) scale(0.833333,-0.833333) translate(111.746,-1526.98)" width="15" x="558.7278228110376" xlink:href="#Disconnector:刀闸_0" y="680.4444507757823" zvalue="710"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453966364675" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453966364675"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,564.978,692.944) scale(0.833333,-0.833333) translate(111.746,-1526.98)" width="15" x="558.7278228110376" y="680.4444507757823"/></g>
  <g id="40">
   <use class="kv10" height="30" transform="rotate(0,1586.78,691.944) scale(0.833333,-0.833333) translate(316.106,-1524.78)" width="15" x="1580.527777777778" xlink:href="#Disconnector:刀闸_0" y="679.4444507757823" zvalue="719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453966692355" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453966692355"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1586.78,691.944) scale(0.833333,-0.833333) translate(316.106,-1524.78)" width="15" x="1580.527777777778" y="679.4444507757823"/></g>
  <g id="228">
   <use class="kv35" height="30" transform="rotate(0,1601.95,290.139) scale(0.833333,0.833333) translate(319.139,55.5278)" width="15" x="1595.696852457616" xlink:href="#Disconnector:刀闸_0" y="277.6388888888888" zvalue="1121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967675395" ObjectName="35kV1号站用变3812隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453967675395"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1601.95,290.139) scale(0.833333,0.833333) translate(319.139,55.5278)" width="15" x="1595.696852457616" y="277.6388888888888"/></g>
  <g id="226">
   <use class="kv35" height="30" transform="rotate(0,1601.86,192.194) scale(0.833333,0.833333) translate(319.122,35.9389)" width="15" x="1595.611111111111" xlink:href="#Disconnector:刀闸_0" y="179.6944444444443" zvalue="1124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967609859" ObjectName="35kV1号站用变3816隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453967609859"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1601.86,192.194) scale(0.833333,0.833333) translate(319.122,35.9389)" width="15" x="1595.611111111111" y="179.6944444444443"/></g>
  <g id="237">
   <use class="kv35" height="30" transform="rotate(0,836.215,433.902) scale(0.833333,0.833333) translate(165.993,84.2805)" width="15" x="829.9651451405429" xlink:href="#Disconnector:刀闸_0" y="421.4024354909338" zvalue="1146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967806467" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453967806467"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,836.215,433.902) scale(0.833333,0.833333) translate(165.993,84.2805)" width="15" x="829.9651451405429" y="421.4024354909338"/></g>
  <g id="250">
   <use class="kv10" height="30" transform="rotate(0,836.793,589.152) scale(0.833333,0.833333) translate(166.109,115.33)" width="15" x="830.5433604336044" xlink:href="#Disconnector:刀闸_0" y="576.6524390243903" zvalue="1159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968003075" ObjectName="#1主变10kV侧0016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453968003075"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,836.793,589.152) scale(0.833333,0.833333) translate(166.109,115.33)" width="15" x="830.5433604336044" y="576.6524390243903"/></g>
  <g id="280">
   <use class="kv35" height="30" transform="rotate(0,1348.83,343.887) scale(0.833333,0.833333) translate(268.515,66.2774)" width="15" x="1342.576923076923" xlink:href="#Disconnector:刀闸_0" y="331.3867505737859" zvalue="1170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968396291" ObjectName="#2主变35kV侧3022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453968396291"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1348.83,343.887) scale(0.833333,0.833333) translate(268.515,66.2774)" width="15" x="1342.576923076923" y="331.3867505737859"/></g>
  <g id="277">
   <use class="kv10" height="30" transform="rotate(0,1349.38,672.942) scale(0.833333,0.833333) translate(268.626,132.088)" width="15" x="1343.132478632479" xlink:href="#Disconnector:刀闸_0" y="660.4423076923077" zvalue="1175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968330755" ObjectName="#2主变10kV侧0022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453968330755"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1349.38,672.942) scale(0.833333,0.833333) translate(268.626,132.088)" width="15" x="1343.132478632479" y="660.4423076923077"/></g>
  <g id="274">
   <use class="kv35" height="30" transform="rotate(0,1349.29,431.595) scale(0.833333,0.833333) translate(268.608,83.8189)" width="15" x="1343.042068217466" xlink:href="#Disconnector:刀闸_0" y="419.0947431832415" zvalue="1179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968265219" ObjectName="#2主变35kV侧3026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453968265219"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1349.29,431.595) scale(0.833333,0.833333) translate(268.608,83.8189)" width="15" x="1343.042068217466" y="419.0947431832415"/></g>
  <g id="269">
   <use class="kv10" height="30" transform="rotate(0,1349.87,586.845) scale(0.833333,0.833333) translate(268.724,114.869)" width="15" x="1343.620283510527" xlink:href="#Disconnector:刀闸_0" y="574.344746716698" zvalue="1186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968068611" ObjectName="#2主变10kV侧0026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453968068611"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1349.87,586.845) scale(0.833333,0.833333) translate(268.724,114.869)" width="15" x="1343.620283510527" y="574.344746716698"/></g>
  <g id="175">
   <use class="kv10" height="25" transform="rotate(0,503.694,825.5) scale(0.531325,1) translate(433.758,0)" width="45" x="491.7396863239683" xlink:href="#Disconnector:特殊刀闸_0" y="813" zvalue="1205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968658435" ObjectName="10kV2号站用变0816隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453968658435"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,503.694,825.5) scale(0.531325,1) translate(433.758,0)" width="45" x="491.7396863239683" y="813"/></g>
  <g id="320">
   <use class="kv10" height="25" transform="rotate(0,623.694,825.5) scale(0.531325,1) translate(539.609,0)" width="45" x="611.7396863239683" xlink:href="#Disconnector:特殊刀闸_0" y="813" zvalue="1236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968723971" ObjectName="10kV沙糖Ⅰ回线0616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453968723971"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,623.694,825.5) scale(0.531325,1) translate(539.609,0)" width="45" x="611.7396863239683" y="813"/></g>
  <g id="329">
   <use class="kv10" height="25" transform="rotate(0,503.536,747.5) scale(0.531325,1) translate(433.618,0)" width="45" x="491.5807974350794" xlink:href="#Disconnector:特殊刀闸_0" y="735" zvalue="1244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968920579" ObjectName="10kV2号站用变0811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453968920579"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,503.536,747.5) scale(0.531325,1) translate(433.618,0)" width="45" x="491.5807974350794" y="735"/></g>
  <g id="333">
   <use class="kv10" height="25" transform="rotate(0,623.536,747.5) scale(0.531325,1) translate(539.468,0)" width="45" x="611.5807974350794" xlink:href="#Disconnector:特殊刀闸_0" y="735" zvalue="1248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968986115" ObjectName="10kV沙糖Ⅰ回线0611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453968986115"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,623.536,747.5) scale(0.531325,1) translate(539.468,0)" width="45" x="611.5807974350794" y="735"/></g>
  <g id="382">
   <use class="kv10" height="30" transform="rotate(0,625.306,885.25) scale(0.833333,0.833333) translate(123.811,174.55)" width="15" x="619.0555555555557" xlink:href="#Disconnector:刀闸_0" y="872.75" zvalue="1252"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969051651" ObjectName="10kV沙糖Ⅰ回线0618隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453969051651"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,625.306,885.25) scale(0.833333,0.833333) translate(123.811,174.55)" width="15" x="619.0555555555557" y="872.75"/></g>
  <g id="414">
   <use class="kv10" height="25" transform="rotate(0,743.694,825.5) scale(0.531325,1) translate(645.459,0)" width="45" x="731.7396863239684" xlink:href="#Disconnector:特殊刀闸_0" y="813" zvalue="1261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969248259" ObjectName="10kV沙横线0626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453969248259"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,743.694,825.5) scale(0.531325,1) translate(645.459,0)" width="45" x="731.7396863239684" y="813"/></g>
  <g id="411">
   <use class="kv10" height="25" transform="rotate(0,743.536,747.5) scale(0.531325,1) translate(645.319,0)" width="45" x="731.5807974350795" xlink:href="#Disconnector:特殊刀闸_0" y="735" zvalue="1265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969182723" ObjectName="10kV沙横线0621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453969182723"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,743.536,747.5) scale(0.531325,1) translate(645.319,0)" width="45" x="731.5807974350795" y="735"/></g>
  <g id="393">
   <use class="kv10" height="30" transform="rotate(0,745.306,885.25) scale(0.833333,0.833333) translate(147.811,174.55)" width="15" x="739.0555555555557" xlink:href="#Disconnector:刀闸_0" y="872.75" zvalue="1269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969117187" ObjectName="10kV沙横线0628隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453969117187"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,745.306,885.25) scale(0.833333,0.833333) translate(147.811,174.55)" width="15" x="739.0555555555557" y="872.75"/></g>
  <g id="433">
   <use class="kv10" height="25" transform="rotate(0,863.694,825.5) scale(0.531325,1) translate(751.31,0)" width="45" x="851.7396863239684" xlink:href="#Disconnector:特殊刀闸_0" y="813" zvalue="1279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969641475" ObjectName="10kV沙勐线0636隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453969641475"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,863.694,825.5) scale(0.531325,1) translate(751.31,0)" width="45" x="851.7396863239684" y="813"/></g>
  <g id="430">
   <use class="kv10" height="25" transform="rotate(0,863.536,747.5) scale(0.531325,1) translate(751.17,0)" width="45" x="851.5807974350795" xlink:href="#Disconnector:特殊刀闸_0" y="735" zvalue="1283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969575939" ObjectName="10kV沙勐线0631隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453969575939"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,863.536,747.5) scale(0.531325,1) translate(751.17,0)" width="45" x="851.5807974350795" y="735"/></g>
  <g id="425">
   <use class="kv10" height="30" transform="rotate(0,865.306,885.25) scale(0.833333,0.833333) translate(171.811,174.55)" width="15" x="859.0555555555557" xlink:href="#Disconnector:刀闸_0" y="872.75" zvalue="1287"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969510403" ObjectName="10kV沙勐线0638隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453969510403"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,865.306,885.25) scale(0.833333,0.833333) translate(171.811,174.55)" width="15" x="859.0555555555557" y="872.75"/></g>
  <g id="454">
   <use class="kv10" height="25" transform="rotate(0,983.694,825.5) scale(0.531325,1) translate(857.161,0)" width="45" x="971.7396863239684" xlink:href="#Disconnector:特殊刀闸_0" y="813" zvalue="1297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970034691" ObjectName="10kV沙九线0646隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453970034691"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,983.694,825.5) scale(0.531325,1) translate(857.161,0)" width="45" x="971.7396863239684" y="813"/></g>
  <g id="451">
   <use class="kv10" height="25" transform="rotate(0,983.536,747.5) scale(0.531325,1) translate(857.02,0)" width="45" x="971.5807974350795" xlink:href="#Disconnector:特殊刀闸_0" y="735" zvalue="1301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969969155" ObjectName="10kV沙九线0641隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453969969155"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,983.536,747.5) scale(0.531325,1) translate(857.02,0)" width="45" x="971.5807974350795" y="735"/></g>
  <g id="447">
   <use class="kv10" height="30" transform="rotate(0,985.306,885.25) scale(0.833333,0.833333) translate(195.811,174.55)" width="15" x="979.0555555555557" xlink:href="#Disconnector:刀闸_0" y="872.75" zvalue="1305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969903619" ObjectName="10kV沙九线0648隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453969903619"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,985.306,885.25) scale(0.833333,0.833333) translate(195.811,174.55)" width="15" x="979.0555555555557" y="872.75"/></g>
  <g id="542">
   <use class="kv10" height="25" transform="rotate(0,1303.69,825.5) scale(0.531325,1) translate(1139.43,0)" width="45" x="1291.739686323968" xlink:href="#Disconnector:特殊刀闸_0" y="813" zvalue="1316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453971214339" ObjectName="10kV沙磨线0656隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453971214339"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1303.69,825.5) scale(0.531325,1) translate(1139.43,0)" width="45" x="1291.739686323968" y="813"/></g>
  <g id="536">
   <use class="kv10" height="25" transform="rotate(0,1303.54,747.5) scale(0.531325,1) translate(1139.29,0)" width="45" x="1291.580797435079" xlink:href="#Disconnector:特殊刀闸_0" y="735" zvalue="1320"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453971148803" ObjectName="10kV沙磨线0652隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453971148803"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1303.54,747.5) scale(0.531325,1) translate(1139.29,0)" width="45" x="1291.580797435079" y="735"/></g>
  <g id="531">
   <use class="kv10" height="30" transform="rotate(0,1306.84,885.25) scale(0.833333,0.833333) translate(260.117,174.55)" width="15" x="1300.585856289034" xlink:href="#Disconnector:刀闸_0" y="872.75" zvalue="1324"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453971083267" ObjectName="10kV沙磨线0658隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453971083267"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1306.84,885.25) scale(0.833333,0.833333) translate(260.117,174.55)" width="15" x="1300.585856289034" y="872.75"/></g>
  <g id="512">
   <use class="kv10" height="25" transform="rotate(0,1423.69,825.5) scale(0.531325,1) translate(1245.28,0)" width="45" x="1411.739686323968" xlink:href="#Disconnector:特殊刀闸_0" y="813" zvalue="1334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970821123" ObjectName="10kV沙糖Ⅱ回线0666隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453970821123"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1423.69,825.5) scale(0.531325,1) translate(1245.28,0)" width="45" x="1411.739686323968" y="813"/></g>
  <g id="504">
   <use class="kv10" height="25" transform="rotate(0,1423.54,746.5) scale(0.531325,1) translate(1245.14,0)" width="45" x="1411.580797435079" xlink:href="#Disconnector:特殊刀闸_0" y="734" zvalue="1338"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970755587" ObjectName="10kV沙糖Ⅱ回线0662隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453970755587"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1423.54,746.5) scale(0.531325,1) translate(1245.14,0)" width="45" x="1411.580797435079" y="734"/></g>
  <g id="500">
   <use class="kv10" height="30" transform="rotate(0,1426.84,885.25) scale(0.833333,0.833333) translate(284.117,174.55)" width="15" x="1420.585856289034" xlink:href="#Disconnector:刀闸_0" y="872.75" zvalue="1342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970690051" ObjectName="10kV沙糖Ⅱ回线0668隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453970690051"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1426.84,885.25) scale(0.833333,0.833333) translate(284.117,174.55)" width="15" x="1420.585856289034" y="872.75"/></g>
  <g id="494">
   <use class="kv10" height="25" transform="rotate(0,1543.69,825.5) scale(0.531325,1) translate(1351.13,0)" width="45" x="1531.739686323968" xlink:href="#Disconnector:特殊刀闸_0" y="813" zvalue="1352"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970427907" ObjectName="10kV沙丝线0676隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453970427907"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1543.69,825.5) scale(0.531325,1) translate(1351.13,0)" width="45" x="1531.739686323968" y="813"/></g>
  <g id="488">
   <use class="kv10" height="25" transform="rotate(0,1543.54,747.5) scale(0.531325,1) translate(1350.99,0)" width="45" x="1531.58079743508" xlink:href="#Disconnector:特殊刀闸_0" y="735" zvalue="1356"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970362371" ObjectName="10kV沙丝线0672隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453970362371"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1543.54,747.5) scale(0.531325,1) translate(1350.99,0)" width="45" x="1531.58079743508" y="735"/></g>
  <g id="485">
   <use class="kv10" height="30" transform="rotate(0,1546.84,885.25) scale(0.833333,0.833333) translate(308.117,174.55)" width="15" x="1540.585856289034" xlink:href="#Disconnector:刀闸_0" y="872.75" zvalue="1360"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970296835" ObjectName="10kV沙丝线0678隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453970296835"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1546.84,885.25) scale(0.833333,0.833333) translate(308.117,174.55)" width="15" x="1540.585856289034" y="872.75"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="490">
   <path class="kv35" d="M 775.8 303.54 L 775.8 322.11" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="492@1" LinkObjectIDznd="508@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.8 303.54 L 775.8 322.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="456">
   <path class="kv35" d="M 1035.55 305.04 L 1035.55 322.11" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="458@1" LinkObjectIDznd="508@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.55 305.04 L 1035.55 322.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv35" d="M 1185.55 305.04 L 1185.55 321.25" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="457@1" LinkObjectIDznd="506@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1185.55 305.04 L 1185.55 321.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="445">
   <path class="kv35" d="M 641.93 406.41 L 642.02 379.55" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="669@0" LinkObjectIDznd="449@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 641.93 406.41 L 642.02 379.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="443">
   <path class="kv35" d="M 642.04 355.18 L 642.04 322.11" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="449@0" LinkObjectIDznd="508@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 642.04 355.18 L 642.04 322.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv10" d="M 1094.55 700.15 L 1094.55 720" stroke-width="1" zvalue="388"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@1" LinkObjectIDznd="429@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.55 700.15 L 1094.55 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv35" d="M 775.8 210.04 L 775.75 239.9" stroke-width="1" zvalue="444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@1" LinkObjectIDznd="552@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.8 210.04 L 775.75 239.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv35" d="M 775.91 267.52 L 775.82 279.16" stroke-width="1" zvalue="445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="552@1" LinkObjectIDznd="492@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.91 267.52 L 775.82 279.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="534">
   <path class="kv35" d="M 1185.57 280.66 L 1185.57 264.36 L 1119.87 264.36" stroke-width="1" zvalue="567"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="457@0" LinkObjectIDznd="502@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1185.57 280.66 L 1185.57 264.36 L 1119.87 264.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="535">
   <path class="kv35" d="M 1094.24 264.43 L 1035.57 264.43 L 1035.57 280.66" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="502@1" LinkObjectIDznd="458@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.24 264.43 L 1035.57 264.43 L 1035.57 280.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="538">
   <path class="kv35" d="M 775.89 132.72 L 775.82 185.66" stroke-width="1" zvalue="570"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@0" LinkObjectIDznd="489@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.89 132.72 L 775.82 185.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="571">
   <path class="kv35" d="M 916.36 301.31 L 916.36 322.11" stroke-width="1" zvalue="581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="572@1" LinkObjectIDznd="508@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.36 301.31 L 916.36 322.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="566">
   <path class="kv35" d="M 916.38 132.72 L 916.38 183.44" stroke-width="1" zvalue="587"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="570@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.38 132.72 L 916.38 183.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="573">
   <path class="kv10" d="M 1241.5 699.9 L 1241.5 720.25" stroke-width="1" zvalue="594"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@1" LinkObjectIDznd="426@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.5 699.9 L 1241.5 720.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="577">
   <path class="kv10" d="M 1094.57 675.77 L 1094.57 656.94 L 1155.9 656.87" stroke-width="1" zvalue="599"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@0" LinkObjectIDznd="576@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.57 675.77 L 1094.57 656.94 L 1155.9 656.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="578">
   <path class="kv10" d="M 1181.53 656.81 L 1241.52 656.81 L 1241.52 675.52" stroke-width="1" zvalue="600"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="576@0" LinkObjectIDznd="436@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1181.53 656.81 L 1241.52 656.81 L 1241.52 675.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="605">
   <path class="kv35" d="M 1293.58 299.09 L 1293.58 321.25" stroke-width="1" zvalue="604"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="606@1" LinkObjectIDznd="506@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.58 299.09 L 1293.58 321.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="599">
   <path class="kv35" d="M 1293.58 205.59 L 1293.53 237.37" stroke-width="1" zvalue="607"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="604@1" LinkObjectIDznd="589@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.58 205.59 L 1293.53 237.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="598">
   <path class="kv35" d="M 1293.69 264.98 L 1293.6 274.72" stroke-width="1" zvalue="608"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="589@1" LinkObjectIDznd="606@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.69 264.98 L 1293.6 274.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="596">
   <path class="kv35" d="M 1292.56 136.72 L 1292.56 181.22" stroke-width="1" zvalue="610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="604@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.56 136.72 L 1292.56 181.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="590">
   <path class="kv35" d="M 1272.28 167.49 L 1292.56 167.49" stroke-width="1" zvalue="614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="591@0" LinkObjectIDznd="596" MaxPinNum="2"/>
   </metadata>
  <path d="M 1272.28 167.49 L 1292.56 167.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="628">
   <path class="kv35" d="M 1442 302.43 L 1442 321.25" stroke-width="1" zvalue="620"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="629@1" LinkObjectIDznd="506@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442 302.43 L 1442 321.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="626">
   <path class="kv35" d="M 1441.91 204.48 L 1441.86 238.79" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="627@1" LinkObjectIDznd="620@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1441.91 204.48 L 1441.86 238.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="625">
   <path class="kv35" d="M 1442.02 266.41 L 1442.02 278.05" stroke-width="1" zvalue="624"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="620@1" LinkObjectIDznd="629@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.02 266.41 L 1442.02 278.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="623">
   <path class="kv35" d="M 1441.93 131.68 L 1441.93 180.11" stroke-width="1" zvalue="626"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="627@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1441.93 131.68 L 1441.93 180.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="635">
   <path class="kv35" d="M 835.82 334.11 L 835.82 322.11" stroke-width="1" zvalue="640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="633@0" LinkObjectIDznd="508@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.82 334.11 L 835.82 322.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="642">
   <path class="kv10" d="M 836.46 643.96 L 836.38 663.16" stroke-width="1" zvalue="647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="638@1" LinkObjectIDznd="641@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.46 643.96 L 836.38 663.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="643">
   <path class="kv10" d="M 836.36 687.54 L 836.36 720" stroke-width="1" zvalue="648"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="641@1" LinkObjectIDznd="429@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.36 687.54 L 836.36 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="675">
   <path class="kv35" d="M 1542.38 403.23 L 1542.38 377.37" stroke-width="1" zvalue="676"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="673@0" LinkObjectIDznd="676@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1542.38 403.23 L 1542.38 377.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="677">
   <path class="kv35" d="M 1542.41 353 L 1542.41 321.25" stroke-width="1" zvalue="677"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="676@0" LinkObjectIDznd="506@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1542.41 353 L 1542.41 321.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 565.05 705.03 L 565.05 720" stroke-width="1" zvalue="712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="429@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.05 705.03 L 565.05 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 565.05 649.71 L 565.03 680.66" stroke-width="1" zvalue="713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="28@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.05 649.71 L 565.03 680.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv10" d="M 586.17 666.33 L 565.04 666.33" stroke-width="1" zvalue="716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 586.17 666.33 L 565.04 666.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 1586.85 704.03 L 1586.85 720.25" stroke-width="1" zvalue="721"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="426@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.85 704.03 L 1586.85 720.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 1586.83 648.71 L 1586.83 679.66" stroke-width="1" zvalue="722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="40@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.83 648.71 L 1586.83 679.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 1609.94 665.33 L 1586.83 665.33" stroke-width="1" zvalue="725"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="38" MaxPinNum="2"/>
   </metadata>
  <path d="M 1609.94 665.33 L 1586.83 665.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 755.41 167.49 L 775.85 167.49" stroke-width="1" zvalue="1072"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="539@0" LinkObjectIDznd="538" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.41 167.49 L 775.85 167.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 757.62 143.14 L 775.88 143.16" stroke-width="1" zvalue="1074"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="538" MaxPinNum="2"/>
   </metadata>
  <path d="M 757.62 143.14 L 775.88 143.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv35" d="M 895.06 164.85 L 916.38 164.85" stroke-width="1" zvalue="1075"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="565@0" LinkObjectIDznd="566" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.06 164.85 L 916.38 164.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv35" d="M 1420.61 167.49 L 1441.93 167.49" stroke-width="1" zvalue="1076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="622@0" LinkObjectIDznd="623" MaxPinNum="2"/>
   </metadata>
  <path d="M 1420.61 167.49 L 1441.93 167.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv35" d="M 903.53 146.32 L 916.38 146.32" stroke-width="1" zvalue="1079"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="566" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.53 146.32 L 916.38 146.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 1278.53 143.14 L 1292.56 143.14" stroke-width="1" zvalue="1082"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@0" LinkObjectIDznd="596" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.53 143.14 L 1292.56 143.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv35" d="M 791.94 218.91 L 775.79 218.91" stroke-width="1" zvalue="1085"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 791.94 218.91 L 775.79 218.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv35" d="M 1311.83 219.83 L 1293.56 219.83" stroke-width="1" zvalue="1092"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="599" MaxPinNum="2"/>
   </metadata>
  <path d="M 1311.83 219.83 L 1293.56 219.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv35" d="M 1458.01 216.02 L 1441.89 216.02" stroke-width="1" zvalue="1095"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@0" LinkObjectIDznd="626" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.01 216.02 L 1441.89 216.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv35" d="M 1602 302.43 L 1602 321.25" stroke-width="1" zvalue="1123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@1" LinkObjectIDznd="506@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602 302.43 L 1602 321.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv35" d="M 1601.91 204.48 L 1601.86 240.26" stroke-width="1" zvalue="1126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@1" LinkObjectIDznd="196@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1601.91 204.48 L 1601.86 240.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv35" d="M 1602.02 267.87 L 1602.02 278.05" stroke-width="1" zvalue="1127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@1" LinkObjectIDznd="228@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602.02 267.87 L 1602.02 278.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv35" d="M 1602.32 150.97 L 1602.32 180.11" stroke-width="1" zvalue="1128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="226@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602.32 150.97 L 1602.32 180.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv35" d="M 1580.61 167.49 L 1602.32 167.49" stroke-width="1" zvalue="1135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 1580.61 167.49 L 1602.32 167.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv35" d="M 916.38 276.94 L 916.46 269.95" stroke-width="1" zvalue="1141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="572@0" LinkObjectIDznd="559@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.38 276.94 L 916.46 269.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv35" d="M 916.36 207.81 L 916.36 242.34" stroke-width="1" zvalue="1143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="570@1" LinkObjectIDznd="559@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.36 207.81 L 916.36 242.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv35" d="M 930.61 221.47 L 916.36 221.47" stroke-width="1" zvalue="1144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="235" MaxPinNum="2"/>
   </metadata>
  <path d="M 930.61 221.47 L 916.36 221.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 836.29 421.82 L 836.29 399.96" stroke-width="1" zvalue="1149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="630@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.29 421.82 L 836.29 399.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv35" d="M 835.8 372.35 L 835.8 358.48" stroke-width="1" zvalue="1150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="630@0" LinkObjectIDznd="633@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.8 372.35 L 835.8 358.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv35" d="M 836.27 446.19 L 836.34 484.73" stroke-width="1" zvalue="1151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="549@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.27 446.19 L 836.34 484.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 836.31 562.45 L 836.31 577.07" stroke-width="1" zvalue="1160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@1" LinkObjectIDznd="250@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.31 562.45 L 836.31 577.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv10" d="M 836.84 601.44 L 836.84 616.35" stroke-width="1" zvalue="1161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@1" LinkObjectIDznd="638@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.84 601.44 L 836.84 616.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv35" d="M 863.24 462.11 L 836.29 462.11" stroke-width="1" zvalue="1162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="245" MaxPinNum="2"/>
   </metadata>
  <path d="M 863.24 462.11 L 836.29 462.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv35" d="M 1348.9 331.8 L 1348.9 321.25" stroke-width="1" zvalue="1172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="506@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.9 331.8 L 1348.9 321.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="kv10" d="M 1349.54 641.65 L 1349.46 660.86" stroke-width="1" zvalue="1176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="278@1" LinkObjectIDznd="277@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.54 641.65 L 1349.46 660.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv10" d="M 1349.43 685.23 L 1349.43 720.25" stroke-width="1" zvalue="1178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@1" LinkObjectIDznd="426@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.43 685.23 L 1349.43 720.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv35" d="M 1349.37 419.51 L 1349.37 397.65" stroke-width="1" zvalue="1181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="281@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.37 419.51 L 1349.37 397.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv35" d="M 1348.88 370.04 L 1348.88 356.17" stroke-width="1" zvalue="1182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="280@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.88 370.04 L 1348.88 356.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv35" d="M 1349.34 443.88 L 1349.41 482.42" stroke-width="1" zvalue="1183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@1" LinkObjectIDznd="282@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.34 443.88 L 1349.41 482.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv10" d="M 1349.38 560.14 L 1349.38 574.76" stroke-width="1" zvalue="1188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@1" LinkObjectIDznd="269@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.38 560.14 L 1349.38 574.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv10" d="M 1349.92 599.13 L 1349.92 614.04" stroke-width="1" zvalue="1189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@1" LinkObjectIDznd="278@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.92 599.13 L 1349.92 614.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv35" d="M 1376.32 459.8 L 1349.37 459.8" stroke-width="1" zvalue="1190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@0" LinkObjectIDznd="271" MaxPinNum="2"/>
   </metadata>
  <path d="M 1376.32 459.8 L 1349.37 459.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 506.91 813.75 L 506.91 797.39" stroke-width="1" zvalue="1206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 506.91 813.75 L 506.91 797.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 506.91 837.45 L 506.91 882.68" stroke-width="1" zvalue="1207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@1" LinkObjectIDznd="168@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 506.91 837.45 L 506.91 882.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 517.16 857.16 L 506.91 857.16" stroke-width="1" zvalue="1208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="178" MaxPinNum="2"/>
   </metadata>
  <path d="M 517.16 857.16 L 506.91 857.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv10" d="M 626.91 813.75 L 626.91 797.39" stroke-width="1" zvalue="1238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="322@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.91 813.75 L 626.91 797.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv10" d="M 637.16 857.16 L 626.91 857.16" stroke-width="1" zvalue="1240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="383" MaxPinNum="2"/>
   </metadata>
  <path d="M 637.16 857.16 L 626.91 857.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="kv10" d="M 506.75 769.78 L 506.75 759.45" stroke-width="1" zvalue="1245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="329@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 506.75 769.78 L 506.75 759.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="331">
   <path class="kv10" d="M 506.75 735.75 L 506.75 720" stroke-width="1" zvalue="1246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@0" LinkObjectIDznd="429@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 506.75 735.75 L 506.75 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="379">
   <path class="kv10" d="M 626.75 769.78 L 626.75 759.45" stroke-width="1" zvalue="1249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@1" LinkObjectIDznd="333@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.75 769.78 L 626.75 759.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv10" d="M 626.75 735.75 L 626.75 720" stroke-width="1" zvalue="1250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333@0" LinkObjectIDznd="429@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.75 735.75 L 626.75 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="383">
   <path class="kv10" d="M 626.91 837.45 L 626.91 873.16" stroke-width="1" zvalue="1253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@1" LinkObjectIDznd="382@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.91 837.45 L 626.91 873.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="384">
   <path class="kv10" d="M 625.36 897.54 L 625.36 902.83" stroke-width="1" zvalue="1254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@1" LinkObjectIDznd="238@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 625.36 897.54 L 625.36 902.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="413">
   <path class="kv10" d="M 746.91 813.75 L 746.91 797.39" stroke-width="1" zvalue="1263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="415@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.91 813.75 L 746.91 797.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="412">
   <path class="kv10" d="M 757.16 857.16 L 746.91 857.16" stroke-width="1" zvalue="1264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="416@0" LinkObjectIDznd="392" MaxPinNum="2"/>
   </metadata>
  <path d="M 757.16 857.16 L 746.91 857.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="410">
   <path class="kv10" d="M 746.75 769.78 L 746.75 759.45" stroke-width="1" zvalue="1267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="415@1" LinkObjectIDznd="411@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.75 769.78 L 746.75 759.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="409">
   <path class="kv10" d="M 746.75 735.75 L 746.75 720" stroke-width="1" zvalue="1268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="411@0" LinkObjectIDznd="429@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.75 735.75 L 746.75 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="kv10" d="M 746.91 837.45 L 746.91 873.16" stroke-width="1" zvalue="1271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@1" LinkObjectIDznd="393@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.91 837.45 L 746.91 873.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="391">
   <path class="kv10" d="M 745.36 897.54 L 745.36 908.01" stroke-width="1" zvalue="1272"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="393@1" LinkObjectIDznd="417@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.36 897.54 L 745.36 908.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="432">
   <path class="kv10" d="M 866.91 813.75 L 866.91 797.39" stroke-width="1" zvalue="1281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="433@0" LinkObjectIDznd="434@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.91 813.75 L 866.91 797.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="431">
   <path class="kv10" d="M 877.16 857.16 L 866.91 857.16" stroke-width="1" zvalue="1282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="435@0" LinkObjectIDznd="424" MaxPinNum="2"/>
   </metadata>
  <path d="M 877.16 857.16 L 866.91 857.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="428">
   <path class="kv10" d="M 866.75 769.78 L 866.75 759.45" stroke-width="1" zvalue="1285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="434@1" LinkObjectIDznd="430@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.75 769.78 L 866.75 759.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="427">
   <path class="kv10" d="M 866.75 735.75 L 866.75 720" stroke-width="1" zvalue="1286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="430@0" LinkObjectIDznd="429@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.75 735.75 L 866.75 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="424">
   <path class="kv10" d="M 866.91 837.45 L 866.91 873.16" stroke-width="1" zvalue="1289"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="433@1" LinkObjectIDznd="425@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.91 837.45 L 866.91 873.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="423">
   <path class="kv10" d="M 865.36 897.54 L 865.36 908.01" stroke-width="1" zvalue="1290"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="425@1" LinkObjectIDznd="437@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 865.36 897.54 L 865.36 908.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="453">
   <path class="kv10" d="M 986.91 813.75 L 986.91 797.39" stroke-width="1" zvalue="1299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="454@0" LinkObjectIDznd="459@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.91 813.75 L 986.91 797.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="452">
   <path class="kv10" d="M 997.16 857.16 L 986.91 857.16" stroke-width="1" zvalue="1300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="460@0" LinkObjectIDznd="446" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.16 857.16 L 986.91 857.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="450">
   <path class="kv10" d="M 986.75 769.78 L 986.75 759.45" stroke-width="1" zvalue="1303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="459@1" LinkObjectIDznd="451@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.75 769.78 L 986.75 759.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="448">
   <path class="kv10" d="M 986.75 735.75 L 986.75 720" stroke-width="1" zvalue="1304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="451@0" LinkObjectIDznd="429@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.75 735.75 L 986.75 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="446">
   <path class="kv10" d="M 986.91 837.45 L 986.91 873.16" stroke-width="1" zvalue="1307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="454@1" LinkObjectIDznd="447@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.91 837.45 L 986.91 873.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="444">
   <path class="kv10" d="M 985.36 897.54 L 985.36 906.83" stroke-width="1" zvalue="1308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="447@1" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 985.36 897.54 L 985.36 906.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="541">
   <path class="kv10" d="M 1306.91 813.75 L 1306.91 797.39" stroke-width="1" zvalue="1318"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="542@0" LinkObjectIDznd="543@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.91 813.75 L 1306.91 797.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="537">
   <path class="kv10" d="M 1317.16 857.16 L 1306.91 857.16" stroke-width="1" zvalue="1319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="544@0" LinkObjectIDznd="530" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.16 857.16 L 1306.91 857.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="533">
   <path class="kv10" d="M 1306.75 769.78 L 1306.75 759.45" stroke-width="1" zvalue="1322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="543@1" LinkObjectIDznd="536@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.75 769.78 L 1306.75 759.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="532">
   <path class="kv10" d="M 1306.75 735.75 L 1306.75 720.25" stroke-width="1" zvalue="1323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="536@0" LinkObjectIDznd="426@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.75 735.75 L 1306.75 720.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="530">
   <path class="kv10" d="M 1306.91 837.45 L 1306.91 873.16" stroke-width="1" zvalue="1326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="542@1" LinkObjectIDznd="531@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.91 837.45 L 1306.91 873.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="521">
   <path class="kv10" d="M 1306.89 897.54 L 1306.89 910.01" stroke-width="1" zvalue="1327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="531@1" LinkObjectIDznd="545@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.89 897.54 L 1306.89 910.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="507">
   <path class="kv10" d="M 1426.91 813.75 L 1426.91 797.39" stroke-width="1" zvalue="1336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@0" LinkObjectIDznd="518@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.91 813.75 L 1426.91 797.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="505">
   <path class="kv10" d="M 1437.16 857.16 L 1426.91 857.16" stroke-width="1" zvalue="1337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="519@0" LinkObjectIDznd="499" MaxPinNum="2"/>
   </metadata>
  <path d="M 1437.16 857.16 L 1426.91 857.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="503">
   <path class="kv10" d="M 1426.75 769.78 L 1426.75 758.45" stroke-width="1" zvalue="1340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="518@1" LinkObjectIDznd="504@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.75 769.78 L 1426.75 758.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="501">
   <path class="kv10" d="M 1426.75 734.75 L 1426.75 720.25" stroke-width="1" zvalue="1341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="504@0" LinkObjectIDznd="426@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.75 734.75 L 1426.75 720.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="499">
   <path class="kv10" d="M 1426.91 837.45 L 1426.91 873.16" stroke-width="1" zvalue="1344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@1" LinkObjectIDznd="500@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.91 837.45 L 1426.91 873.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="kv10" d="M 1426.89 897.54 L 1426.89 905.83" stroke-width="1" zvalue="1345"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="500@1" LinkObjectIDznd="239@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.89 897.54 L 1426.89 905.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="493">
   <path class="kv10" d="M 1546.91 813.75 L 1546.91 797.39" stroke-width="1" zvalue="1354"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@0" LinkObjectIDznd="495@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.91 813.75 L 1546.91 797.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="491">
   <path class="kv10" d="M 1557.16 857.16 L 1546.91 857.16" stroke-width="1" zvalue="1355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="496@0" LinkObjectIDznd="484" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.16 857.16 L 1546.91 857.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="487">
   <path class="kv10" d="M 1546.75 769.78 L 1546.75 759.45" stroke-width="1" zvalue="1358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@1" LinkObjectIDznd="488@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.75 769.78 L 1546.75 759.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="486">
   <path class="kv10" d="M 1546.75 735.75 L 1546.75 720.25" stroke-width="1" zvalue="1359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="488@0" LinkObjectIDznd="426@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.75 735.75 L 1546.75 720.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="484">
   <path class="kv10" d="M 1546.91 837.45 L 1546.91 873.16" stroke-width="1" zvalue="1362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@1" LinkObjectIDznd="485@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.91 837.45 L 1546.91 873.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="483">
   <path class="kv10" d="M 1546.89 897.54 L 1546.89 908.01" stroke-width="1" zvalue="1363"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="485@1" LinkObjectIDznd="497@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.89 897.54 L 1546.89 908.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="502">
   <use class="kv35" height="20" transform="rotate(270,1107,264.5) scale(1.4,1.3) translate(-314.286,-58.0385)" width="10" x="1100" xlink:href="#Breaker:母联开关_0" y="251.5" zvalue="452"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925115904003" ObjectName="35kV分段312断路器"/>
   <cge:TPSR_Ref TObjectID="6473925115904003"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1107,264.5) scale(1.4,1.3) translate(-314.286,-58.0385)" width="10" x="1100" y="251.5"/></g>
  <g id="552">
   <use class="kv35" height="20" transform="rotate(0,775.803,253.722) scale(1.58889,1.44444) translate(-284.591,-73.6239)" width="10" x="767.8585185185186" xlink:href="#Breaker:开关_0" y="239.2777676052514" zvalue="577"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925115969539" ObjectName="35kV西沙Ⅰ回线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473925115969539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,775.803,253.722) scale(1.58889,1.44444) translate(-284.591,-73.6239)" width="10" x="767.8585185185186" y="239.2777676052514"/></g>
  <g id="559">
   <use class="kv35" height="20" transform="rotate(0,916.359,256.159) scale(1.58889,1.44444) translate(-336.685,-74.3737)" width="10" x="908.4140740740741" xlink:href="#Breaker:开关_0" y="241.7145492144471" zvalue="592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116035075" ObjectName="35kV沙锡线362断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116035075"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,916.359,256.159) scale(1.58889,1.44444) translate(-336.685,-74.3737)" width="10" x="908.4140740740741" y="241.7145492144471"/></g>
  <g id="576">
   <use class="kv10" height="20" transform="rotate(270,1168.67,656.944) scale(1.4,1.3) translate(-331.905,-148.603)" width="10" x="1161.666666666667" xlink:href="#Breaker:母联开关_0" y="643.9444444444443" zvalue="598"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116100611" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116100611"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1168.67,656.944) scale(1.4,1.3) translate(-331.905,-148.603)" width="10" x="1161.666666666667" y="643.9444444444443"/></g>
  <g id="589">
   <use class="kv35" height="20" transform="rotate(0,1293.58,251.187) scale(1.58889,1.44444) translate(-476.495,-72.8438)" width="10" x="1285.636296296296" xlink:href="#Breaker:开关_0" y="236.7424248589409" zvalue="615"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116166147" ObjectName="35kV西沙Ⅱ回线363断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116166147"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1293.58,251.187) scale(1.58889,1.44444) translate(-476.495,-72.8438)" width="10" x="1285.636296296296" y="236.7424248589409"/></g>
  <g id="620">
   <use class="kv35" height="20" transform="rotate(0,1441.91,252.611) scale(1.58889,1.44444) translate(-531.471,-73.282)" width="10" x="1433.96962962963" xlink:href="#Breaker:开关_0" y="238.1666564411585" zvalue="631"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116231683" ObjectName="35kV备用线364断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116231683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1441.91,252.611) scale(1.58889,1.44444) translate(-531.471,-73.282)" width="10" x="1433.96962962963" y="238.1666564411585"/></g>
  <g id="630">
   <use class="kv35" height="20" transform="rotate(0,835.854,386.167) scale(1.58889,1.44444) translate(-306.848,-114.376)" width="10" x="827.9094888719401" xlink:href="#Breaker:开关_0" y="371.7222256130642" zvalue="634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116297219" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116297219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,835.854,386.167) scale(1.58889,1.44444) translate(-306.848,-114.376)" width="10" x="827.9094888719401" y="371.7222256130642"/></g>
  <g id="638">
   <use class="kv10" height="20" transform="rotate(0,836.359,630.167) scale(1.58889,1.44444) translate(-307.035,-189.453)" width="10" x="828.4140740740741" xlink:href="#Breaker:开关_0" y="615.7222222222223" zvalue="642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116362755" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116362755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,836.359,630.167) scale(1.58889,1.44444) translate(-307.035,-189.453)" width="10" x="828.4140740740741" y="615.7222222222223"/></g>
  <g id="196">
   <use class="kv35" height="20" transform="rotate(0,1601.91,254.076) scale(1.58889,1.44444) translate(-590.772,-73.7327)" width="10" x="1593.96962962963" xlink:href="#Breaker:开关_0" y="239.6313137478297" zvalue="1131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116428291" ObjectName="35kV1号站用变381断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116428291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1601.91,254.076) scale(1.58889,1.44444) translate(-590.772,-73.7327)" width="10" x="1593.96962962963" y="239.6313137478297"/></g>
  <g id="281">
   <use class="kv35" height="20" transform="rotate(0,1348.93,383.859) scale(1.58889,1.44444) translate(-497.009,-113.666)" width="10" x="1340.986411948863" xlink:href="#Breaker:开关_0" y="369.4145333053719" zvalue="1168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116559363" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116559363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1348.93,383.859) scale(1.58889,1.44444) translate(-497.009,-113.666)" width="10" x="1340.986411948863" y="369.4145333053719"/></g>
  <g id="278">
   <use class="kv10" height="20" transform="rotate(0,1349.44,627.859) scale(1.58889,1.44444) translate(-497.196,-188.743)" width="10" x="1341.490997150997" xlink:href="#Breaker:开关_0" y="613.4145299145299" zvalue="1173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116493827" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116493827"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1349.44,627.859) scale(1.58889,1.44444) translate(-497.196,-188.743)" width="10" x="1341.490997150997" y="613.4145299145299"/></g>
  <g id="96">
   <use class="kv10" height="20" transform="rotate(180,506.856,783.571) scale(1.58889,1.44444) translate(-184.911,-236.654)" width="10" x="498.9115964240107" xlink:href="#Breaker:开关_0" y="769.1263490874193" zvalue="1201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116624899" ObjectName="10kV2号站用变081断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116624899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,506.856,783.571) scale(1.58889,1.44444) translate(-184.911,-236.654)" width="10" x="498.9115964240107" y="769.1263490874193"/></g>
  <g id="322">
   <use class="kv10" height="20" transform="rotate(180,626.856,783.571) scale(1.58889,1.44444) translate(-229.387,-236.654)" width="10" x="618.9115964240107" xlink:href="#Breaker:开关_0" y="769.1263490874193" zvalue="1232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116690435" ObjectName="10kV沙糖Ⅰ回线061断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116690435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,626.856,783.571) scale(1.58889,1.44444) translate(-229.387,-236.654)" width="10" x="618.9115964240107" y="769.1263490874193"/></g>
  <g id="415">
   <use class="kv10" height="20" transform="rotate(180,746.856,783.571) scale(1.58889,1.44444) translate(-273.862,-236.654)" width="10" x="738.9115964240107" xlink:href="#Breaker:开关_0" y="769.1263490874193" zvalue="1259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116755971" ObjectName="10kV沙横线062断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116755971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,746.856,783.571) scale(1.58889,1.44444) translate(-273.862,-236.654)" width="10" x="738.9115964240107" y="769.1263490874193"/></g>
  <g id="434">
   <use class="kv10" height="20" transform="rotate(180,866.856,783.571) scale(1.58889,1.44444) translate(-318.338,-236.654)" width="10" x="858.9115964240107" xlink:href="#Breaker:开关_0" y="769.1263490874193" zvalue="1277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116821507" ObjectName="10kV沙勐线063断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116821507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,866.856,783.571) scale(1.58889,1.44444) translate(-318.338,-236.654)" width="10" x="858.9115964240107" y="769.1263490874193"/></g>
  <g id="459">
   <use class="kv10" height="20" transform="rotate(180,986.856,783.571) scale(1.58889,1.44444) translate(-362.813,-236.654)" width="10" x="978.9115964240107" xlink:href="#Breaker:开关_0" y="769.1263490874193" zvalue="1295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116887043" ObjectName="10kV沙九线064断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116887043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,986.856,783.571) scale(1.58889,1.44444) translate(-362.813,-236.654)" width="10" x="978.9115964240107" y="769.1263490874193"/></g>
  <g id="543">
   <use class="kv10" height="20" transform="rotate(180,1306.86,783.571) scale(1.58889,1.44444) translate(-481.415,-236.654)" width="10" x="1298.911596424011" xlink:href="#Breaker:开关_0" y="769.1263490874193" zvalue="1314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925117083651" ObjectName="10kV沙磨线065断路器"/>
   <cge:TPSR_Ref TObjectID="6473925117083651"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1306.86,783.571) scale(1.58889,1.44444) translate(-481.415,-236.654)" width="10" x="1298.911596424011" y="769.1263490874193"/></g>
  <g id="518">
   <use class="kv10" height="20" transform="rotate(180,1426.86,783.571) scale(1.58889,1.44444) translate(-525.89,-236.654)" width="10" x="1418.911596424011" xlink:href="#Breaker:开关_0" y="769.1263490874193" zvalue="1332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925117018115" ObjectName="10kV沙糖Ⅱ回线066断路器"/>
   <cge:TPSR_Ref TObjectID="6473925117018115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1426.86,783.571) scale(1.58889,1.44444) translate(-525.89,-236.654)" width="10" x="1418.911596424011" y="769.1263490874193"/></g>
  <g id="495">
   <use class="kv10" height="20" transform="rotate(180,1546.86,783.571) scale(1.58889,1.44444) translate(-570.366,-236.654)" width="10" x="1538.911596424011" xlink:href="#Breaker:开关_0" y="769.1263490874193" zvalue="1350"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925116952579" ObjectName="10kV沙丝线067断路器"/>
   <cge:TPSR_Ref TObjectID="6473925116952579"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1546.86,783.571) scale(1.58889,1.44444) translate(-570.366,-236.654)" width="10" x="1538.911596424011" y="769.1263490874193"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="539">
   <use class="kv35" height="20" transform="rotate(90,744.576,167.435) scale(1.11111,1.11111) translate(-73.902,-15.6323)" width="10" x="739.0202020202021" xlink:href="#GroundDisconnector:地刀_0" y="156.3234795464414" zvalue="571"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453964922883" ObjectName="35kV西沙Ⅰ回线36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453964922883"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,744.576,167.435) scale(1.11111,1.11111) translate(-73.902,-15.6323)" width="10" x="739.0202020202021" y="156.3234795464414"/></g>
  <g id="565">
   <use class="kv35" height="20" transform="rotate(90,884.222,164.791) scale(1.11111,1.11111) translate(-87.8667,-15.368)" width="10" x="878.6666666666667" xlink:href="#GroundDisconnector:地刀_0" y="153.6797592558764" zvalue="589"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965053955" ObjectName="35kV沙锡线36267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453965053955"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,884.222,164.791) scale(1.11111,1.11111) translate(-87.8667,-15.368)" width="10" x="878.6666666666667" y="153.6797592558764"/></g>
  <g id="591">
   <use class="kv35" height="20" transform="rotate(90,1261.44,167.435) scale(1.11111,1.11111) translate(-125.589,-15.6323)" width="10" x="1255.888888888889" xlink:href="#GroundDisconnector:地刀_0" y="156.3234796524047" zvalue="612"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965381635" ObjectName="35kV西沙Ⅱ回线36367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453965381635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1261.44,167.435) scale(1.11111,1.11111) translate(-125.589,-15.6323)" width="10" x="1255.888888888889" y="156.3234796524047"/></g>
  <g id="622">
   <use class="kv35" height="20" transform="rotate(90,1409.78,167.435) scale(1.11111,1.11111) translate(-140.422,-15.6323)" width="10" x="1404.222222222222" xlink:href="#GroundDisconnector:地刀_0" y="156.3234797053865" zvalue="628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965643779" ObjectName="35kV备用线36467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453965643779"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1409.78,167.435) scale(1.11111,1.11111) translate(-140.422,-15.6323)" width="10" x="1404.222222222222" y="156.3234797053865"/></g>
  <g id="25">
   <use class="kv10" height="20" transform="rotate(270,597,666.389) scale(1.11111,1.11111) translate(-59.1444,-65.5278)" width="10" x="591.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="655.2777777777778" zvalue="714"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453966299139" ObjectName="10kVⅠ段母线电压互感器09017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453966299139"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,597,666.389) scale(1.11111,1.11111) translate(-59.1444,-65.5278)" width="10" x="591.4444444444445" y="655.2777777777778"/></g>
  <g id="37">
   <use class="kv10" height="20" transform="rotate(270,1620.78,665.389) scale(1.11111,1.11111) translate(-161.522,-65.4278)" width="10" x="1615.222222222222" xlink:href="#GroundDisconnector:地刀_0" y="654.2777777777778" zvalue="723"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453966626819" ObjectName="10kVⅡ段母线电压互感器09027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453966626819"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1620.78,665.389) scale(1.11111,1.11111) translate(-161.522,-65.4278)" width="10" x="1615.222222222222" y="654.2777777777778"/></g>
  <g id="197">
   <use class="kv35" height="20" transform="rotate(90,1569.78,167.435) scale(1.11111,1.11111) translate(-156.422,-15.6323)" width="10" x="1564.222222222222" xlink:href="#GroundDisconnector:地刀_0" y="156.3234797053864" zvalue="1129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967544323" ObjectName="35kV1号站用变38167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453967544323"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1569.78,167.435) scale(1.11111,1.11111) translate(-156.422,-15.6323)" width="10" x="1564.222222222222" y="156.3234797053864"/></g>
  <g id="249">
   <use class="kv35" height="20" transform="rotate(270,874.073,462.161) scale(1.11111,1.11111) translate(-86.8518,-45.105)" width="10" x="868.5176151761518" xlink:href="#GroundDisconnector:地刀_0" y="451.050135501355" zvalue="1157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967937539" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453967937539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,874.073,462.161) scale(1.11111,1.11111) translate(-86.8518,-45.105)" width="10" x="868.5176151761518" y="451.050135501355"/></g>
  <g id="270">
   <use class="kv35" height="20" transform="rotate(270,1387.15,459.854) scale(1.11111,1.11111) translate(-138.159,-44.8742)" width="10" x="1381.594538253075" xlink:href="#GroundDisconnector:地刀_0" y="448.7424431936627" zvalue="1184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968199683" ObjectName="#2主变35kV侧30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453968199683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1387.15,459.854) scale(1.11111,1.11111) translate(-138.159,-44.8742)" width="10" x="1381.594538253075" y="448.7424431936627"/></g>
  <g id="105">
   <use class="kv10" height="20" transform="rotate(270,527.992,857.212) scale(1.11111,1.11111) translate(-52.2437,-84.6101)" width="10" x="522.4367816091958" xlink:href="#GroundDisconnector:地刀_0" y="846.1008497965292" zvalue="1200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968527363" ObjectName="10kV2号站用变08167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453968527363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,527.992,857.212) scale(1.11111,1.11111) translate(-52.2437,-84.6101)" width="10" x="522.4367816091958" y="846.1008497965292"/></g>
  <g id="323">
   <use class="kv10" height="20" transform="rotate(270,647.992,857.212) scale(1.11111,1.11111) translate(-64.2437,-84.6101)" width="10" x="642.4367816091958" xlink:href="#GroundDisconnector:地刀_0" y="846.1008497965292" zvalue="1230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968855043" ObjectName="10kV沙糖Ⅰ回线06167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453968855043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,647.992,857.212) scale(1.11111,1.11111) translate(-64.2437,-84.6101)" width="10" x="642.4367816091958" y="846.1008497965292"/></g>
  <g id="416">
   <use class="kv10" height="20" transform="rotate(270,767.992,857.212) scale(1.11111,1.11111) translate(-76.2437,-84.6101)" width="10" x="762.4367816091958" xlink:href="#GroundDisconnector:地刀_0" y="846.1008497965291" zvalue="1257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969379331" ObjectName="10kV沙横线06267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453969379331"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,767.992,857.212) scale(1.11111,1.11111) translate(-76.2437,-84.6101)" width="10" x="762.4367816091958" y="846.1008497965291"/></g>
  <g id="435">
   <use class="kv10" height="20" transform="rotate(270,887.992,857.212) scale(1.11111,1.11111) translate(-88.2437,-84.6101)" width="10" x="882.4367816091958" xlink:href="#GroundDisconnector:地刀_0" y="846.1008497965291" zvalue="1275"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969772547" ObjectName="10kV沙勐线06367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453969772547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,887.992,857.212) scale(1.11111,1.11111) translate(-88.2437,-84.6101)" width="10" x="882.4367816091958" y="846.1008497965291"/></g>
  <g id="460">
   <use class="kv10" height="20" transform="rotate(270,1007.99,857.212) scale(1.11111,1.11111) translate(-100.244,-84.6101)" width="10" x="1002.436781609196" xlink:href="#GroundDisconnector:地刀_0" y="846.1008497965291" zvalue="1293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970165763" ObjectName="10kV沙九线06467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453970165763"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1007.99,857.212) scale(1.11111,1.11111) translate(-100.244,-84.6101)" width="10" x="1002.436781609196" y="846.1008497965291"/></g>
  <g id="544">
   <use class="kv10" height="20" transform="rotate(270,1327.99,857.212) scale(1.11111,1.11111) translate(-132.244,-84.6101)" width="10" x="1322.436781609196" xlink:href="#GroundDisconnector:地刀_0" y="846.1008497965292" zvalue="1311"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453971345411" ObjectName="10kV沙磨线06567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453971345411"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1327.99,857.212) scale(1.11111,1.11111) translate(-132.244,-84.6101)" width="10" x="1322.436781609196" y="846.1008497965292"/></g>
  <g id="519">
   <use class="kv10" height="20" transform="rotate(270,1447.99,857.212) scale(1.11111,1.11111) translate(-144.244,-84.6101)" width="10" x="1442.436781609196" xlink:href="#GroundDisconnector:地刀_0" y="846.1008497965292" zvalue="1330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970952195" ObjectName="10kV沙糖Ⅱ回线06667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453970952195"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1447.99,857.212) scale(1.11111,1.11111) translate(-144.244,-84.6101)" width="10" x="1442.436781609196" y="846.1008497965292"/></g>
  <g id="496">
   <use class="kv10" height="20" transform="rotate(270,1567.99,857.212) scale(1.11111,1.11111) translate(-156.244,-84.6101)" width="10" x="1562.436781609196" xlink:href="#GroundDisconnector:地刀_0" y="846.1008497965291" zvalue="1347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970558979" ObjectName="10kV沙丝线06767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453970558979"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1567.99,857.212) scale(1.11111,1.11111) translate(-156.244,-84.6101)" width="10" x="1562.436781609196" y="846.1008497965291"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="549">
   <g id="5490">
    <use class="kv35" height="30" transform="rotate(0,836.306,523.42) scale(2.77878,2.77878) translate(-513.998,-308.375)" width="24" x="802.96" xlink:href="#PowerTransformer2:可调不带中性点_0" y="481.74" zvalue="575"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874572693506" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5491">
    <use class="kv10" height="30" transform="rotate(0,836.306,523.42) scale(2.77878,2.77878) translate(-513.998,-308.375)" width="24" x="802.96" xlink:href="#PowerTransformer2:可调不带中性点_1" y="481.74" zvalue="575"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874572759042" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399525859330" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399525859330"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,836.306,523.42) scale(2.77878,2.77878) translate(-513.998,-308.375)" width="24" x="802.96" y="481.74"/></g>
  <g id="282">
   <g id="2820">
    <use class="kv35" height="30" transform="rotate(0,1349.38,521.112) scale(2.77878,2.77878) translate(-842.434,-306.897)" width="24" x="1316.04" xlink:href="#PowerTransformer2:可调不带中性点_0" y="479.43" zvalue="1166"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874572824578" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2821">
    <use class="kv10" height="30" transform="rotate(0,1349.38,521.112) scale(2.77878,2.77878) translate(-842.434,-306.897)" width="24" x="1316.04" xlink:href="#PowerTransformer2:可调不带中性点_1" y="479.43" zvalue="1166"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874572890114" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399525924866" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399525924866"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1349.38,521.112) scale(2.77878,2.77878) translate(-842.434,-306.897)" width="24" x="1316.04" y="479.43"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="669">
   <use class="kv35" height="30" transform="rotate(0,643.466,429.013) scale(1.35185,1.62222) translate(-162.2,-155.22)" width="30" x="623.1877394636014" xlink:href="#Accessory:避雷器PT带熔断器_0" y="404.6800766283527" zvalue="672"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453965971459" ObjectName="35kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,643.466,429.013) scale(1.35185,1.62222) translate(-162.2,-155.22)" width="30" x="623.1877394636014" y="404.6800766283527"/></g>
  <g id="673">
   <use class="kv35" height="30" transform="rotate(0,1543.92,425.833) scale(1.35185,1.62222) translate(-396.563,-154)" width="30" x="1523.638624674409" xlink:href="#Accessory:避雷器PT带熔断器_0" y="401.5000000000002" zvalue="679"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453966036995" ObjectName="35kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1543.92,425.833) scale(1.35185,1.62222) translate(-396.563,-154)" width="30" x="1523.638624674409" y="401.5000000000002"/></g>
  <g id="29">
   <use class="kv10" height="75" transform="rotate(0,565.908,626.389) scale(0.714286,0.711111) translate(216.363,243.637)" width="70" x="540.9081132105644" xlink:href="#Accessory:电压互感器带电显示_0" y="599.7222222222223" zvalue="717"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453966430211" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="75" opacity="0" stroke="white" transform="rotate(0,565.908,626.389) scale(0.714286,0.711111) translate(216.363,243.637)" width="70" x="540.9081132105644" y="599.7222222222223"/></g>
  <g id="35">
   <use class="kv10" height="75" transform="rotate(0,1587.69,625.389) scale(0.714286,0.711111) translate(625.074,243.231)" width="70" x="1562.685890988342" xlink:href="#Accessory:电压互感器带电显示_0" y="598.7222222222223" zvalue="726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453966495747" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="75" opacity="0" stroke="white" transform="rotate(0,1587.69,625.389) scale(0.714286,0.711111) translate(625.074,243.231)" width="70" x="1562.685890988342" y="598.7222222222223"/></g>
  <g id="2">
   <use class="kv35" height="40" transform="rotate(90,740.273,143.136) scale(0.909091,0.909091) translate(73.1182,12.4955)" width="20" x="731.181818181818" xlink:href="#Accessory:线路PT带避雷器0904_0" y="124.9545371315693" zvalue="1073"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967020035" ObjectName="35kV西沙Ⅰ回线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,740.273,143.136) scale(0.909091,0.909091) translate(73.1182,12.4955)" width="20" x="731.181818181818" y="124.9545371315693"/></g>
  <g id="8">
   <use class="kv35" height="40" transform="rotate(90,886.182,146.321) scale(0.909091,0.909091) translate(87.7091,12.8139)" width="20" x="877.090909090909" xlink:href="#Accessory:线路PT带避雷器0904_0" y="128.1391501305289" zvalue="1078"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967085571" ObjectName="35kV沙锡线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,886.182,146.321) scale(0.909091,0.909091) translate(87.7091,12.8139)" width="20" x="877.090909090909" y="128.1391501305289"/></g>
  <g id="12">
   <use class="kv35" height="40" transform="rotate(90,1261.18,143.136) scale(0.909091,0.909091) translate(125.209,12.4955)" width="20" x="1252.090909090909" xlink:href="#Accessory:线路PT带避雷器0904_0" y="124.9545371749184" zvalue="1081"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967151107" ObjectName="35kV西沙Ⅱ回线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1261.18,143.136) scale(0.909091,0.909091) translate(125.209,12.4955)" width="20" x="1252.090909090909" y="124.9545371749184"/></g>
  <g id="18">
   <use class="kv35" height="26" transform="rotate(270,803.182,218.942) scale(0.909091,0.909091) translate(79.7727,20.7123)" width="12" x="797.7272727272727" xlink:href="#Accessory:避雷器_0" y="207.1234270886876" zvalue="1084"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967216643" ObjectName="35kV西沙Ⅰ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,803.182,218.942) scale(0.909091,0.909091) translate(79.7727,20.7123)" width="12" x="797.7272727272727" y="207.1234270886876"/></g>
  <g id="61">
   <use class="kv35" height="26" transform="rotate(270,941.856,221.5) scale(0.909091,0.909091) translate(93.6402,20.9682)" width="12" x="936.4015331967644" xlink:href="#Accessory:避雷器_0" y="209.6818181818182" zvalue="1087"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967282179" ObjectName="35kV沙锡线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,941.856,221.5) scale(0.909091,0.909091) translate(93.6402,20.9682)" width="12" x="936.4015331967644" y="209.6818181818182"/></g>
  <g id="104">
   <use class="kv35" height="26" transform="rotate(270,1323.07,219.864) scale(0.909091,0.909091) translate(131.762,20.8045)" width="12" x="1317.619833168676" xlink:href="#Accessory:避雷器_0" y="208.0454545454546" zvalue="1091"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967347715" ObjectName="35kV西沙Ⅱ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1323.07,219.864) scale(0.909091,0.909091) translate(131.762,20.8045)" width="12" x="1317.619833168676" y="208.0454545454546"/></g>
  <g id="156">
   <use class="kv35" height="26" transform="rotate(270,1469.26,216.045) scale(0.909091,0.909091) translate(146.38,20.4227)" width="12" x="1463.801651350495" xlink:href="#Accessory:避雷器_0" y="204.2272727272728" zvalue="1094"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967413251" ObjectName="35kV备用线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1469.26,216.045) scale(0.909091,0.909091) translate(146.38,20.4227)" width="12" x="1463.801651350495" y="204.2272727272728"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="3">
   <use class="kv35" height="30" transform="rotate(0,775.889,121.348) scale(4.7619,0.765656) translate(-599.786,33.6258)" width="7" x="759.2222222222223" xlink:href="#ACLineSegment:线路_0" y="109.8636499270046" zvalue="689"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249329631235" ObjectName="35kV西沙Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="8444249329631235_5066549680209921"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,775.889,121.348) scale(4.7619,0.765656) translate(-599.786,33.6258)" width="7" x="759.2222222222223" y="109.8636499270046"/></g>
  <g id="45">
   <use class="kv35" height="30" transform="rotate(0,1292.56,125.348) scale(4.7619,0.765656) translate(-1007.95,34.8501)" width="7" x="1275.888888888889" xlink:href="#ACLineSegment:线路_0" y="113.8636495151904" zvalue="732"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249329696771" ObjectName="35kV西沙Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="8444249329696771_5066549680209921"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1292.56,125.348) scale(4.7619,0.765656) translate(-1007.95,34.8501)" width="7" x="1275.888888888889" y="113.8636495151904"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="47">
   <use class="kv35" height="30" transform="rotate(0,1441.93,121.348) scale(1.01852,0.765656) translate(-26.1059,33.6258)" width="12" x="1435.823147542384" xlink:href="#EnergyConsumer:负荷_0" y="109.8636474224049" zvalue="735"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453966888963" ObjectName="35kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192453966888963"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1441.93,121.348) scale(1.01852,0.765656) translate(-26.1059,33.6258)" width="12" x="1435.823147542384" y="109.8636474224049"/></g>
  <g id="229">
   <use class="kv35" height="30" transform="rotate(180,1602.45,130.603) scale(1.40394,1.41379) translate(-455.4,-32.0185)" width="28" x="1582.793103448276" xlink:href="#EnergyConsumer:站用变DY接地_0" y="109.3965517241379" zvalue="1138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453967740931" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1602.45,130.603) scale(1.40394,1.41379) translate(-455.4,-32.0185)" width="28" x="1582.793103448276" y="109.3965517241379"/></g>
  <g id="168">
   <use class="kv10" height="30" transform="rotate(0,506.322,903.043) scale(1.40394,1.41379) translate(-140.023,-258.098)" width="28" x="486.6666666666669" xlink:href="#EnergyConsumer:站用变DY接地_0" y="881.8362068965517" zvalue="1203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453968592899" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,506.322,903.043) scale(1.40394,1.41379) translate(-140.023,-258.098)" width="28" x="486.6666666666669" y="881.8362068965517"/></g>
  <g id="417">
   <use class="kv10" height="30" transform="rotate(0,748.94,927) scale(1.43333,-1.43333) translate(-223.174,-1567.24)" width="15" x="738.1898592423105" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="905.5" zvalue="1256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969444867" ObjectName="10kV沙横线"/>
   <cge:TPSR_Ref TObjectID="6192453969444867"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,748.94,927) scale(1.43333,-1.43333) translate(-223.174,-1567.24)" width="15" x="738.1898592423105" y="905.5"/></g>
  <g id="437">
   <use class="kv10" height="30" transform="rotate(0,868.94,927) scale(1.43333,-1.43333) translate(-259.453,-1567.24)" width="15" x="858.1898592423105" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="905.5" zvalue="1274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453969838083" ObjectName="10kV沙勐线"/>
   <cge:TPSR_Ref TObjectID="6192453969838083"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,868.94,927) scale(1.43333,-1.43333) translate(-259.453,-1567.24)" width="15" x="858.1898592423105" y="905.5"/></g>
  <g id="545">
   <use class="kv10" height="30" transform="rotate(0,1308.94,929) scale(1.43333,-1.43333) translate(-392.476,-1570.64)" width="15" x="1298.18985924231" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="907.5" zvalue="1310"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453971410947" ObjectName="10kV沙磨线"/>
   <cge:TPSR_Ref TObjectID="6192453971410947"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1308.94,929) scale(1.43333,-1.43333) translate(-392.476,-1570.64)" width="15" x="1298.18985924231" y="907.5"/></g>
  <g id="497">
   <use class="kv10" height="30" transform="rotate(0,1548.94,927) scale(1.43333,-1.43333) translate(-465.034,-1567.24)" width="15" x="1538.18985924231" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="905.5" zvalue="1346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453970624515" ObjectName="10kV沙丝线"/>
   <cge:TPSR_Ref TObjectID="6192453970624515"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1548.94,927) scale(1.43333,-1.43333) translate(-465.034,-1567.24)" width="15" x="1538.18985924231" y="905.5"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="158" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,775.889,14.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="776.0700000000001" xml:space="preserve" y="19.23" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133766934531" ObjectName="P"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="159" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,775.889,35.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="776.0700000000001" xml:space="preserve" y="40.23" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133767000067" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="160" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,775.889,56.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="776.0700000000001" xml:space="preserve" y="61.23" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133767065603" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="547">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="547" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,926.934,13.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="927.13" xml:space="preserve" y="18.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133767983109" ObjectName="P"/>
   </metadata>
  </g>
  <g id="548">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="548" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1292.56,13.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.75" xml:space="preserve" y="18.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133769031685" ObjectName="P"/>
   </metadata>
  </g>
  <g id="551">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="551" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1442.85,9.36365) scale(1,1) translate(0,0)" writing-mode="lr" x="1443.05" xml:space="preserve" y="14.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133770080259" ObjectName="P"/>
   </metadata>
  </g>
  <g id="556">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="556" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,926.934,36.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="927.13" xml:space="preserve" y="41.27" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133768048645" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="560">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="560" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1292.56,36.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.75" xml:space="preserve" y="41.27" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133769097221" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="561">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="561" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1442.85,32.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="1443.05" xml:space="preserve" y="37.27" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133770145795" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="562">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="562" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,926.934,59.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="927.13" xml:space="preserve" y="64.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133768114181" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="563">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="563" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292.56,59.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.75" xml:space="preserve" y="64.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133769162757" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="564">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="564" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1442.85,55.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="1443.05" xml:space="preserve" y="60.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133770211331" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="587">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="587" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,747.94,978) scale(1,1) translate(0,0)" writing-mode="lr" x="748.14" xml:space="preserve" y="982.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133778403333" ObjectName="P"/>
   </metadata>
  </g>
  <g id="592">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="592" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,867.94,978) scale(1,1) translate(9.13647e-14,0)" writing-mode="lr" x="868.14" xml:space="preserve" y="982.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133779845125" ObjectName="P"/>
   </metadata>
  </g>
  <g id="594">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="594" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1548.94,973) scale(1,1) translate(0,0)" writing-mode="lr" x="1549.14" xml:space="preserve" y="977.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133782728709" ObjectName="P"/>
   </metadata>
  </g>
  <g id="597">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="597" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1308.94,975) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.14" xml:space="preserve" y="979.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133785612293" ObjectName="P"/>
   </metadata>
  </g>
  <g id="601">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="601" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,747.94,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="748.14" xml:space="preserve" y="1005.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133778468869" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="602">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="602" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,867.94,1001) scale(1,1) translate(9.13647e-14,0)" writing-mode="lr" x="868.14" xml:space="preserve" y="1005.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133779910661" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="608">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="608" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1548.94,996) scale(1,1) translate(0,0)" writing-mode="lr" x="1549.14" xml:space="preserve" y="1000.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133782794245" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="610">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="610" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1308.94,998) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.14" xml:space="preserve" y="1002.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133785677829" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="613">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="613" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,747.94,1024) scale(1,1) translate(0,0)" writing-mode="lr" x="748.14" xml:space="preserve" y="1028.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133778534405" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="616">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="616" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,868.94,1023) scale(1,1) translate(9.14757e-14,0)" writing-mode="lr" x="869.14" xml:space="preserve" y="1027.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133779976197" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="618">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="618" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1548.94,1019) scale(1,1) translate(0,0)" writing-mode="lr" x="1549.14" xml:space="preserve" y="1023.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133782859781" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="621">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="621" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1308.94,1021) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.14" xml:space="preserve" y="1025.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133785743365" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,237.065,485.5) scale(1,1) translate(0,0)" writing-mode="lr" x="237.19" xml:space="preserve" y="490.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759528965" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,237.065,511.341) scale(1,1) translate(0,0)" writing-mode="lr" x="237.19" xml:space="preserve" y="516.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759266821" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="91" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,237.065,538.341) scale(1,1) translate(0,0)" writing-mode="lr" x="237.19" xml:space="preserve" y="543.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759332357" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,294.3,485.5) scale(1,1) translate(0,0)" writing-mode="lr" x="294.43" xml:space="preserve" y="490.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759004677" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,294.3,511.341) scale(1,1) translate(0,0)" writing-mode="lr" x="294.43" xml:space="preserve" y="516.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133758742531" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="88" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,294.3,538.341) scale(1,1) translate(0,0)" writing-mode="lr" x="294.43" xml:space="preserve" y="543.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133758808067" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,175.3,485.5) scale(1,1) translate(0,0)" writing-mode="lr" x="175.43" xml:space="preserve" y="490.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760053253" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,175.3,511.341) scale(1,1) translate(0,0)" writing-mode="lr" x="175.43" xml:space="preserve" y="516.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759791107" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="85" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,175.3,538.341) scale(1,1) translate(0,0)" writing-mode="lr" x="175.43" xml:space="preserve" y="543.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759856643" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,146.762,284.675) scale(1,1) translate(0,0)" writing-mode="lr" x="146.94" xml:space="preserve" y="291.16" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133761363971" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,287.236,283.825) scale(1,1) translate(0,3.01231e-13)" writing-mode="lr" x="287.41" xml:space="preserve" y="290.31" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133774733317" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f4.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139.15,261.675) scale(1,1) translate(0,0)" writing-mode="lr" x="139.32" xml:space="preserve" y="268.16" zvalue="1">ddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133761298435" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f4.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,288.3,261.675) scale(1,1) translate(0,0)" writing-mode="lr" x="288.47" xml:space="preserve" y="268.16" zvalue="1">ddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133774667779" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,116.5,511.341) scale(1,1) translate(0,0)" writing-mode="lr" x="116.63" xml:space="preserve" y="516.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760315397" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="15" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,116.5,538.341) scale(1,1) translate(0,0)" writing-mode="lr" x="116.63" xml:space="preserve" y="543.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760380931" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,116.5,566.341) scale(1,1) translate(0,0)" writing-mode="lr" x="116.63" xml:space="preserve" y="571.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760446467" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,116.5,485.5) scale(1,1) translate(0,0)" writing-mode="lr" x="116.63" xml:space="preserve" y="490.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760577539" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,116.5,590.341) scale(1,1) translate(0,0)" writing-mode="lr" x="116.63" xml:space="preserve" y="595.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760774147" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,140.678,190.25) scale(1,1) translate(0,0)" writing-mode="lr" x="140.83" xml:space="preserve" y="195.16" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133791379459" ObjectName="P"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,289.678,189.25) scale(1,1) translate(0,0)" writing-mode="lr" x="289.83" xml:space="preserve" y="194.16" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133791444995" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,141,215.841) scale(1,1) translate(0,0)" writing-mode="lr" x="141.15" xml:space="preserve" y="220.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760708611" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,141.967,238.841) scale(1,1) translate(0,0)" writing-mode="lr" x="142.12" xml:space="preserve" y="243.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759660035" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,292.967,215.841) scale(1,1) translate(0,0)" writing-mode="lr" x="293.12" xml:space="preserve" y="220.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760184325" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,291.967,238.841) scale(1,1) translate(0,0)" writing-mode="lr" x="292.12" xml:space="preserve" y="243.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759135749" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,175.3,566.341) scale(1,1) translate(0,0)" writing-mode="lr" x="175.43" xml:space="preserve" y="571.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759922179" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,175.3,590.341) scale(1,1) translate(0,-2.57723e-13)" writing-mode="lr" x="175.43" xml:space="preserve" y="595.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760249861" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,236.065,566.341) scale(1,1) translate(0,0)" writing-mode="lr" x="236.19" xml:space="preserve" y="571.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759397893" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,238.065,590.341) scale(1,1) translate(0,-2.57723e-13)" writing-mode="lr" x="238.19" xml:space="preserve" y="595.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759725571" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,294.3,566.341) scale(1,1) translate(0,0)" writing-mode="lr" x="294.43" xml:space="preserve" y="571.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133758873605" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="164" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,296.3,590.341) scale(1,1) translate(0,-2.57723e-13)" writing-mode="lr" x="296.43" xml:space="preserve" y="595.23" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759201285" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="76" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,767.806,397.238) scale(1,1) translate(0,0)" writing-mode="lr" x="768.05" xml:space="preserve" y="403.73" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760839683" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="98" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,767.806,424.238) scale(1,1) translate(0,0)" writing-mode="lr" x="768.05" xml:space="preserve" y="430.73" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760905219" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="99" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,767.806,451.238) scale(1,1) translate(0,0)" writing-mode="lr" x="768.05" xml:space="preserve" y="457.73" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133761101827" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="100" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,765.806,606.601) scale(1,1) translate(0,0)" writing-mode="lr" x="766.05" xml:space="preserve" y="613.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760970755" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="101" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,765.806,633.601) scale(1,1) translate(0,0)" writing-mode="lr" x="766.05" xml:space="preserve" y="640.09" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133761036291" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="102" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,765.806,660.601) scale(1,1) translate(0,0)" writing-mode="lr" x="766.05" xml:space="preserve" y="667.09" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133761429507" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="103" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1276.88,398.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.12" xml:space="preserve" y="405.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133774209027" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="107" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1277.88,424.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1278.12" xml:space="preserve" y="431.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133774274563" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="108" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1277.88,451.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1278.12" xml:space="preserve" y="458.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133774471171" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="109" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1271.88,570.294) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.12" xml:space="preserve" y="576.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133774340099" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="110" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1271.88,597.294) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.12" xml:space="preserve" y="603.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133774405635" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="111" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1271.88,624.294) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.12" xml:space="preserve" y="630.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133774798853" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="114" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1105,181) scale(1,1) translate(0,0)" writing-mode="lr" x="1105.2" xml:space="preserve" y="185.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133757825028" ObjectName="P"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="115" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1105,204) scale(1,1) translate(0,0)" writing-mode="lr" x="1105.2" xml:space="preserve" y="208.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133757890564" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="116" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1105,223) scale(1,1) translate(0,0)" writing-mode="lr" x="1105.2" xml:space="preserve" y="227.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133757956100" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="117" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1168.67,577.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.86" xml:space="preserve" y="582.35" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133763919875" ObjectName="P"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="119" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1168.67,600.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.86" xml:space="preserve" y="605.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133763985411" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="121" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1168.67,619.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.86" xml:space="preserve" y="624.35" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133764050947" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="283">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="283" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,625.19,978) scale(1,1) translate(0,0)" writing-mode="lr" x="624.72" xml:space="preserve" y="982.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133771128835" ObjectName="P"/>
   </metadata>
  </g>
  <g id="286">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="286" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,625.19,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="624.72" xml:space="preserve" y="1005.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133771194371" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="287" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,625.19,1024) scale(1,1) translate(0,0)" writing-mode="lr" x="624.72" xml:space="preserve" y="1028.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133771259907" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="288" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1426,978) scale(1,1) translate(0,0)" writing-mode="lr" x="1425.53" xml:space="preserve" y="982.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133784170501" ObjectName="P"/>
   </metadata>
  </g>
  <g id="289">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="289" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1426,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="1425.53" xml:space="preserve" y="1005.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133784236037" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="290">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="290" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1426,1024) scale(1,1) translate(0,0)" writing-mode="lr" x="1425.53" xml:space="preserve" y="1028.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133784301573" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="190" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,985,978) scale(1,1) translate(-2.08722e-13,0)" writing-mode="lr" x="984.53" xml:space="preserve" y="982.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133781286915" ObjectName="P"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="202" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,985,1001) scale(1,1) translate(-2.08722e-13,0)" writing-mode="lr" x="984.53" xml:space="preserve" y="1005.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133781352451" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,985,1023) scale(1,1) translate(-2.08722e-13,0)" writing-mode="lr" x="984.53" xml:space="preserve" y="1027.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133781417987" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="297">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="297" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1602.95,9.36364) scale(1,1) translate(0,0)" writing-mode="lr" x="1602.48" xml:space="preserve" y="14.03" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133772570627" ObjectName="P"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="298" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1602.95,32.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="1602.48" xml:space="preserve" y="37.03" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133772636163" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="299">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="299" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1602.95,55.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="1602.48" xml:space="preserve" y="60.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133772701699" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="300" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,506.822,978) scale(1,1) translate(0,0)" writing-mode="lr" x="506.35" xml:space="preserve" y="982.67" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133776764931" ObjectName="P"/>
   </metadata>
  </g>
  <g id="301">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="301" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,506.822,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="506.35" xml:space="preserve" y="1005.67" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133776830467" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="302">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="302" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,506.822,1024) scale(1,1) translate(0,0)" writing-mode="lr" x="506.35" xml:space="preserve" y="1028.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133776896003" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="294">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,636.5,489.5) scale(1,1) translate(0,0)" writing-mode="lr" x="636.63" xml:space="preserve" y="494.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760577539" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="295">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1537.5,485.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1537.63" xml:space="preserve" y="490.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133760053253" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="296" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,566.5,560.5) scale(1,1) translate(0,0)" writing-mode="lr" x="566.63" xml:space="preserve" y="565.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759528965" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="309">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1588.5,558.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1588.63" xml:space="preserve" y="563.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133759004677" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="169">
   <use height="30" transform="rotate(0,298.925,345.75) scale(0.8,0.8) translate(71.7313,83.4375)" width="30" x="286.93" xlink:href="#State:红绿圆(方形)_0" y="333.75" zvalue="1470"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374923857921" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,298.925,345.75) scale(0.8,0.8) translate(71.7313,83.4375)" width="30" x="286.93" y="333.75"/></g>
  <g id="310">
   <use height="30" transform="rotate(0,219.925,344.75) scale(0.8,0.8) translate(51.9813,83.1875)" width="30" x="207.93" xlink:href="#State:红绿圆(方形)_0" y="332.75" zvalue="1603"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950716325892" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,219.925,344.75) scale(0.8,0.8) translate(51.9813,83.1875)" width="30" x="207.93" y="332.75"/></g>
 </g>
</svg>